app_name: op-cloudman-takumi
app_group: ys
gateway:
  pts:
    entry: api/op-cloudman-takumi/op-cloudman-takumi.proto
    config: api/op-cloudman-takumi.yml
  public:
    entry: api/op-cloudman-takumi/op-cloudman-takumi.proto
    config: api/op-cloudman-takumi.yml
    imports:
      - path: api/include/google/protobuf/struct.proto
      - path: api/include/google/protobuf/wrappers.proto
      - path: api/op-cloudman-takumi/account.proto
      - path: api/op-cloudman-takumi/cloudman_common.proto
      - path: api/op-cloudman-takumi/region.proto
      - path: api/op-cloudman-takumi/task.proto
      - path: api/op-cloudman-takumi/cmdb.proto
      - path: api/op-cloudman-takumi/dashBoard.proto
      - path: api/op-cloudman-takumi/inner.proto
      - path: api/op-cloudman-takumi/resourceGroup.proto
      - path: api/op-cloudman-takumi/resRecycle.proto
      - path: api/op-cloudman-takumi/resTemplate.proto
      - path: api/op-cloudman-takumi/tags.proto
      - path: api/op-cloudman-takumi/task.proto
      - path: api/op-cloudman-takumi/taskLog.proto
      - path: api/op-cloudman-takumi/region.proto
      - path: api/op-cloudman-takumi/cloudGateway.proto
      - path: api/op-cloudman-takumi/experimental.proto
      - path: api/op-cloudman-takumi/cloudAgent.proto
      - path: api/op-cloudman-takumi/agentRunner.proto
      - path: api/op-cloudman-takumi/permission.proto
      - path: api/op-cloudman-takumi/servicetree.proto
      - path: api/op-cloudman-takumi/securityGroup.proto
      - path: api/op-cloudman-takumi/loadBalancer.proto
      - path: api/op-cloudman-takumi/eip.proto
      - path: api/op-cloudman-takumi/domain.proto
      - path: api/op-cloudman-takumi/ip.proto
      - path: api/op-cloudman-takumi/ram.proto
      - path: api/op-cloudman-takumi/initResource.proto
      - path: api/op-cloudman-takumi/ddos.proto
zest:
  enable_env:
  - testing
  - pre
  - prod
  - testing-os
  - pre-os
  - prod-os
