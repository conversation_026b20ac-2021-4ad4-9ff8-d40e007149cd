app_name: op-cloudman-takumi
app_group: ys
environment: dev

registry:
  provider: consul
  args:
    address: localhost:8500

# Settings for config logging.
logging:
  directory: stdout

sentry:
  #  sentry的接入地址
#  dsn: https://8bfeb728f7fa4a0980d8b1aa57163d5e:<EMAIL>/6
#   对warning事件的上报采样率
#  warning_rate: 0.3

# Other extra configs should put here.
extra:
  zest:
    apps:
      - app_group: ys
        app_name: op-cloudman-takumi
    token: dcebe8fa-f8dc-11ef-b92d-00155da0d06c
    zest_api: http://127.0.0.1:6000

#serve_as_http: yes