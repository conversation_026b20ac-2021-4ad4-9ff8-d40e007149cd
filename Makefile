INCLUDE = api/include
SERVICE_OUT = api/pb

PACKAGES = $(shell find . -type d -not -path './.git*' -not -path './vendor*')
VERSION := $(shell git describe --tags --dirty 2>/dev/null || echo "v1.0.0")
COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null)
TIME := $(shell date +"%Y-%m-%d.%H:%M:%S")
CONSTANTS := constants
MODULE := op-cloudman
PLATFORM = $(platform)
# GOPROXY := http://gopkgproxy.ysops.mihoyo.com:3000,goproxy.mihoyo.com,direct

ifeq ("$(PLATFORM)", "")
	PLATFORM := "linux"
endif
DOCKERVER := latest

GOOS ?= $(shell uname -s | tr [:upper:] [:lower:])
GOARCH ?= amd64

FLAGS = -ldflags "-X $(CONSTANTS).BuildVersion=$(VERSION) -X $(CONSTANTS).BuildCommit=$(COMMIT) -X $(CONSTANTS).BuildDate=$(TIME)"

proto_set := $(wildcard $(SERVICE_IDL)/*.proto)
pb_set := $(patsubst %.proto, %.pb.go, $(proto_set))

#all: pb app

app:
	@export GOOS=$(GOOS) GOARCH=$(GOARCH)
	go build -o op-cloudman $(FLAGS) main.go

dlv-debug:
	@export GOOS=$(GOOS) GOARCH=$(GOARCH)
	go build -gcflags "all=-N -l" $(FLAGS)

lint: $(PACKAGES:=.lint)

$(PACKAGES:=.lint):
	golint -set_exit_status $(subst .lint,,$@)

unittest:
	go test ./...

.PHONY: docker
docker:
	echo "build $(MODULE) docker images"
	docker login --username=mihoyo_nap --password=swF3kJdQda156kwlHH5M registery-nap-registry.cn-shanghai.cr.aliyuncs.com
	docker build -t registery-nap-registry.cn-shanghai.cr.aliyuncs.com/nap-ops/$(MODULE)-${PLATFORM}:$(DOCKERVER) .

docker-push:
	echo "push $(MODULE) docker images"
	docker login --username=mihoyo_nap --password=swF3kJdQda156kwlHH5M registery-nap-registry.cn-shanghai.cr.aliyuncs.com
	docker push registery-nap-registry.cn-shanghai.cr.aliyuncs.com/nap-ops/$(MODULE)-$(PLATFORM):$(DOCKERVER)


.PHONY: app

hz:
	hz update -idl idl/op-cloudman.proto --unset_omitempty