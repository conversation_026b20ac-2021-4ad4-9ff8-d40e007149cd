syntax = "proto3";

package cloudman;
import "api.proto";

option go_package = "cloudman";

message cloudAgentQueryReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string statue = 5;
  string account_id = 6;
  string inner_ip = 7;
}

message cloudAgentListRes {
  uint32 total = 1;
  repeated cloudAgentDetail list = 2;
}

message cloudAgentDetail {
  string id = 1;
  string account_id = 2;
  string inner_ip = 3;
  int32 status = 4;
  bool disabled = 5;
  uint64 created_time = 6;
  uint64 updated_time = 7;
  string create_user = 8;
  string update_user = 9;
  string agent_id = 10;
}

message createCloudAgentReq {
  string account_id = 1;
  string inner_ip = 2;
}

message updateCloudAgentReq {
  string id = 1;
  bool disabled = 2;
}

message GetInstallScriptResp {
  string script = 1;
}

message IDReq {
  string id = 1[(api.path)="id", (api.query)="id"];
}

message cloudAgentTaskListReq{
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string account_id = 11;
  string risky = 12;
  string result = 13;
  string resource_type = 14;
  string search_key = 21;
  string search_value = 22;
  int32 start = 31;
  int32 end = 32;
}

message cloudAgentTaskListResp{
  repeated cloudAgentTaskBrief data = 1;
  int32 count = 2;
}

message cloudAgentTaskBrief{
  string id = 1;
  string account_id = 2;
  string region_name = 3;

  string resource_type = 11;
  string method_name = 12;
  int32 run_type = 13;
  int32 risky = 14;
  string operator = 15;

  int64 submit_time = 21;
  int32 result = 25;
  bool business_result = 28;

  string agent_ip = 35;
}

message cloudAgentTaskDetailResp{
  string id = 1;
  string account_id = 2;
  string region_name = 3;

  string resource_type = 11;
  string method_name = 12;
  int32 run_type = 13;
  int32 risky = 14;
  string operator = 15;

  int64 submit_time = 21;
  int64 start_time = 22;
  int64 end_time = 23;
  int64 callback_time = 24;
  int32 result = 25;
  string error_msg = 26;
  string business_error_msg = 27;
  bool business_result = 28;

  string request_dump = 31;
  string response_dump = 32;
  string request_schema = 33;
  string response_schema = 34;
  string agent_ip = 35;
  string agent_id = 36;

  string access_key = 41;
  string access_secret = 42;
  string access_token = 43;
  int64 key_expire_time = 44;
  string key_policy = 45;
}

message resChangelistReq{
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string account_id = 11;
  string resource_type = 12;
  string instance_id = 13;
  string instance_name = 14;
  string field = 15;
  int32 start = 21;
  int32 end = 22;
}

message resChangelistResp{
  repeated resChangelist data = 1;
  int32 count = 2;
}

message resChangelist{
  string id = 1;
  int32 created_time = 2;
  string account_id = 11;
  string resource_type = 12;
  string instance_id = 13;
  string instance_name = 14;
  string field = 15;
  string before = 21;
  string after = 22;
}
