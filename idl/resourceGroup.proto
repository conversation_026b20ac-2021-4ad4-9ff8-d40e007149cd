syntax = "proto3";

//import "google/protobuf/wrappers.proto";
import "resTemplate.proto";
import "api.proto";

package cloudman;

option go_package = "cloudman";

message groupQueryReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
}

message groupListResp {
  repeated groupInfo list = 1;
  uint32 total = 2;
}

message groupCreateReq {
  string id = 1[(api.path)="id"];;
  string name = 2;
  string desc = 3;
}

message groupInfo {
  string id = 1;
  string name = 2;
  string desc = 3;
}

// -------------------------

message resGroupPolicyQueryReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
}

message groupPolicyInfoReq {
  string id = 1;
  string resource_type = 2;
}

message resGroupPolicyListResp {
  repeated resGroupPolicyShort list = 1;
  uint32 total = 2;
}

message resGroupPolicyShort {
  string id = 1;
  string name = 2;
  string desc = 3;
}

message resGroupPolicyCreateReq {
  //  string id = 1;
  //  string name = 2;
  //  string desc = 3;
  string resource_type = 3;
  string bind_group_id = 4;
  string condition = 5;
}

message groupPolicy {
  string related = 1; // 条件间关系
  repeated groupPolicyInfo list = 2;
}

message groupPolicyInfo {
  string key = 1;
  string related = 2;
  string value = 3;
}

message resGroupPolicyInfo {
  string id = 1;
  string bind_group_name = 2;
  string bind_group_desc = 3;
  string bind_group_id = 4;
  //  groupPolicy group_conditions = 5;
  string resource_type = 5;
  string condition = 6;
}

message findResReq {
  uint64 page = 1;
  uint64 size = 2;
  string resource_type = 3;
  string bind_res_group_id = 4;
  repeated string fields = 5;
  string condition = 6;
  repeated string ordering = 7;
}

message countResReq {
  string bind_res_group_id = 1;
}

message countResResp {
  uint32 host_total = 1;
  uint32 mysql_total = 2;
  uint32 redis_total = 3;
}

message findResResp {
  repeated HostResDetail host_list = 1;
  repeated MysqlClusterDetail mysql_list = 2;
  repeated RedisDetail redis_list = 3;
  uint32 total = 4;
}
// -------------------------

message listCloudReq {
  string isp_id = 1;
  string region_id = 2;
}

message listCloudResp {
  repeated listCloudData list = 1;
  int32 total = 2;
}

message listCloudData {
  string display_name = 1;
  string id = 2;
  string name = 3;
}