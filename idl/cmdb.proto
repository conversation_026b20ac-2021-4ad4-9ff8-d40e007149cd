syntax = "proto3";

//import "google/protobuf/wrappers.proto";
package cloudman;

option go_package = "cloudman";

message cmdbQueryRequest {
  string env = 1;
  string inst_type = 11;
  repeated string conds = 12;
  int32 limit = 21;
  int32 start = 22;
  string sort = 23;
}

message cmdbQueryResponse {
  int32 bk_error_code = 1;
  string bk_error_msg = 2;
  int32 count = 11;
  repeated string info = 12;
}

message cmdbQueryAssociateRequest {
  string env = 1;
  string body = 11;
}

message cmdbUpdateRequest {
  int32 update_count = 1;
  string resource_type = 11;
  string isp_id = 12;
  string region_id = 13;
  repeated string resource_id = 14;
  string cust_cond = 21;
}

message cmdbUpdateResponse {
  string result = 1;
}

message cmdbDeleteRequest {
  string env = 1;
  string inst_type = 11;
  repeated int32 ids = 12;
}

message cmdbDeleteResponse {
  int32 bk_error_code = 1;
  string bk_error_msg = 2;
}

message cmdbPullInfoRequest {
  string resource_type = 1;
  string region_id = 2;
  repeated string instances = 11;
}

message cmdbDiffRequest {
  string resource_type = 1;
  string env = 2;
  string isp_id = 11;
  string region_id = 12;
  repeated string instances = 21;
}

message cmdbDiffProcessRequest {
  string order_id = 1;
  repeated cmdbDiffProcessItem list = 11;
}

message cmdbDiffProcessItem {
  string resource_type = 1;
  string action_type = 2;
  string instance_id = 11;
}

message cmdbDiffDeleteReq {
  bool dry_run = 1;
}