syntax = "proto3";

//import "google/protobuf/wrappers.proto";
package cloudman;

option go_package = "cloudman";

message TreeNode{
  string path = 1;
  repeated string owner = 2;
  repeated string owner_admin = 3;
  repeated TreeNode list = 4;
  bool leaf = 5;
  int32 sort = 6;
  string path_name = 11;
  string display = 12;
  string detail = 13;
}

message TreeEntity{
  string entity_id = 1;
  string display = 2;
  string catalog = 3;
  string detail = 4;
  repeated string owner = 5;
  map<string, string> tag = 6;
  repeated string paths = 11;
  string row_path = 12;
}

message GetTreeResponse{
  repeated TreeNode list = 1;
}

message GetEntityResponse {
    repeated TreeEntity list = 1;
    int32 count = 2;
}

message NodeEntityCountResponse {
    map<string, int32> path_count_map = 1;
}

message AddNodeRequest {
  string path = 1;
  string display = 11;
  string detail = 12;
  bool is_leaf = 13;
  int32 sort = 14;
  repeated string owner = 21;
}

message AddNodeResponse{
}

message VerifyRequest {
  string search_key = 1;
  repeated string list = 2;
}

message VerifyResponse {
  repeated string pass = 1;
  repeated string deny = 2;
}