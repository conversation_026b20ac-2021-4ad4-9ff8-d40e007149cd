syntax = "proto3";

package cloudman;

option go_package = "cloudman";

message dashBoardAccountRequest {}

message dashBoardAccountResponse {
  repeated dashBoardAccountDetail list = 1;
}

message dashBoardAccountDetail {
  string account_id = 1;
  string account_name = 2;
  string account_type = 3;
  repeated dashBoardAccountRegion regions = 4;
}

message dashBoardAccountRegion {
  string region_id = 1;
  string region_name = 2;
}

message dashBoardUserAuthRequest {}

message dashBoardUserAuthResponse {
  string auth = 1;
}

message dashboardIfWorkOrderUsedResponse {
  bool isUsed = 1;
}

message dashboardBackendVersionResponse {
  string buildVersion = 1;
  string buildCommit = 2;
  string buildDate = 3;
}

message dashboardHostBriefReq {
  string account_id = 1;
  repeated string region_ids = 2;
}

message dashboardHostBriefResp {
  repeated dashboardHostBrief list = 1;
}

message dashboardHostBrief {
  string account_id = 1;
  string region_id = 2;
  int32 host_total = 11;
  int32 host_running = 12;
  int32 host_agent_running = 13;
  int32 host_7day_created = 21;
  int32 host_7day_deleted = 22;
  int32 host_7day_expire = 23;
  int32 host_need_cleanup = 31;
  int32 host_in_recycle = 32;
}

message dashboardResBriefReq {
  string account_id = 1;
  repeated string region_ids = 2;
  string res_type = 3;
}

message dashboardResBriefResp {
  repeated dashboardResBrief list = 1;
}

message dashboardResBrief {
  string account_id = 1;
  string region_id = 2;
  int32 res_total = 11;
  int32 res_running = 12;
  int32 res_agent_running = 13;
  int32 res_7day_created = 21;
  int32 res_7day_deleted = 22;
  int32 res_7day_expire = 23;
  int32 res_need_cleanup = 31;
  int32 res_in_recycle = 32;
}

message dashboardHostDailyCountReq {
  int32 day_before = 1;
  string account_id = 2;
  string region_id = 3;
}

message dashboardHostDailyCountResp {
  repeated dashboardHostDailyCount list = 1;
}

message dashboardHostDailyCount {
  string day_name = 1;
  int32 state_count = 2;
  int32 created_count = 3;
  int32 deleted_count = 4;
}

message dashboardResDailyCountReq {
  string account_id = 1;
  string region_id = 2;
  string res_type = 3;
  int32 start = 11;
  int32 end = 12;
  int32 day_before = 21;
}

message dashboardResDailyCountResp {
  repeated dashboardResDailyCount list = 1;
}

message dashboardResDailyCount {
  string day_name = 1;
  int32 state_count = 2;
  int32 created_count = 3;
  int32 deleted_count = 4;
}

message autoFillSnapshotReq {
  int32 day_before = 1;
  string account_id = 2;
}

message snapshot {
  string day_name = 1;
  string account_id = 2;
  string region_id = 3;
  string res_type = 4;
  int32 total_count = 5;
  string action = 6;
}

message fillSnapshotReq {
  repeated snapshot data = 1;
}

message fillSnapshotRes {
  repeated snapshot failures = 1;
}
