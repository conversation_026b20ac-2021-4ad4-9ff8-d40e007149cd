syntax = "proto3";

//import "google/protobuf/wrappers.proto";
import "api.proto";

package cloudman;

option go_package = "cloudman";

import "resTemplate.proto";

message SecurityGroupEntity {
  string CreationTime = 2;
  string VpcId = 3;
  bool ServiceManaged = 4;
  string Description = 5;
  string SecurityGroupId = 6;
  string ResourceGroupId = 7;
  string SecurityGroupName = 8;
  int32 EcsCount = 9;
  int64 ServiceID = 10;
  string SecurityGroupType = 11;
  int32 AvailableInstanceAmount = 12;
  string RegionID = 13;
  repeated resource_tag Tags = 14;
  repeated SecurityGroupPermission Permissions = 15;
  string isp_id = 16;
  string isp_type = 17;
  string isp_name = 18;
  repeated instance_info bind_instance_infos = 19;
  string custom_tag = 20;
  relate_instance_count count = 21;
  RuleTag level_tag = 22;
  bool NeedCleanup = 23;
}

message relate_instance_count {
  int32 host = 1;
  int32 mysql = 2;
  int32 redis = 3;
}

message instance_info {
  string InstanceID = 1;
  string InstanceName = 2;
}

message SecurityGroupPermission {
  string CreateTime = 1;
  string Description = 2;
  string DestCidrIp = 3;
  string DestGroupId = 4;
  string DestGroupName = 5;
  string DestGroupOwnerAccount = 6;
  string DestPrefixListId = 7;
  string DestPrefixListName = 8;
  string Direction = 9;
  string IpProtocol = 10;
  string Ipv6DestCidrIp = 11;
  string Ipv6SourceCidrIp = 12;
  string NicType = 13;
  string Policy = 14;
  string PortRange = 15;
  int32 Priority = 16;
  string SourceCidrIp = 17;
  string SourceGroupId = 18;
  string SourceGroupName = 19;
  string SourceGroupOwnerAccount = 20;
  string SourcePortRange = 21;
  string SourcePrefixListId = 22;
  string SourcePrefixListName = 23;
  string SecurityGroupRuleId = 24;
  repeated RuleTag tags = 25;
}

message DescribeSecurityGroupReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
  string search_key = 5;
  string search_value = 6;
  string custom_tag = 7;
  string level = 8;
  bool display_need_clean_up = 9;
  string type = 10;
}

message DescribeSecurityGroupRes {
  int32 total = 1;
  repeated SecurityGroupEntity list = 2;
}

message DescribeSecurityGroupByInstancesReq {
  uint64 page = 1;
  uint64 size = 2;
  string instanceType = 3;
  repeated string instance_ids = 4;
}

message DescribeSecurityGroupByInstancesRes {
  int32 total = 1;
  repeated SecurityGroupEntity list = 2;
}

message DescribeRulesReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
  string ip_type = 5;
  string portRange = 6;
  int32 priority = 7;
  string ip_str = 8;
}

message DescribeRulesRes {
  repeated SecurityGroupPermission list = 1;
  int32 total = 2;
}

message UpdateSecurityGroupRuleReq {
  string sg_id = 1;
  repeated SecurityGroupPermission permissions = 2;
  RuleTag level_tag = 3;
}

message RuleTag {
  string level = 1;
  string value = 2;
}

message UpdateSecurityGroupsReq {
  repeated SecurityGroupEntity sgs = 1;
}

message GetAccountRegionTagsReq {
  string isp_id = 1;
  string region_id = 2;
}

message GetAccountRegionTagsRes {
  repeated string customTags = 1;
}

message GetCustomTagSecurityGroupsReq {
  string isp_id = 1;
  string region_id = 2;
  string custom_tag = 3;
}

message GetCustomTagSecurityGroupsRes {
  int32 total = 1;
  repeated SecurityGroupEntity list = 2;
}

message UpdateSecurityGroupCustomTagReq {
  repeated string sg_ids = 1;
  string custom_tag = 2;
}

message ExportSecurityGroupReq {
  repeated string keys = 1;
}

message ExportSecurityGroupRuleReq {
  string sg_id = 1;
}

message ExportRelateInstancesReq {
  repeated string sg_ids = 1;
  bool and_search_security_group_ids = 2;
  string instance_type = 3;
}

message DescribeIPWhitelistsReq {
  uint64 page = 1;
  uint64 size = 2;
  string whitelist_id = 3;
  string name = 4;
}

message DescribeIPWhitelistsResp {
  repeated IPWhitelist list = 1;
  int32 total = 2;
}

message IPWhitelist {
  string name = 1;
  string whitelist_id = 2;
  string security_ip_type = 3;
  repeated string ips = 4;
  repeated string db_instance_ids = 5;
  string region_id = 6;
  string isp_id = 7;
  string isp_type = 8;
  string isp_name = 9;
}

message ModifyIPWhitelistsReq {
  string security_group_id = 1[(api.path)="security_group_id"];
  repeated string ips = 2;
}

message DescribeALBAclsReq {
  uint64 page = 1;
  uint64 size = 2;
  string acl_id = 3;
  string name = 4;
}

message DescribeALBAclsResp {
  repeated ALBAclEntity list = 1;
  int32 total = 2;
}

message ALBAclEntity {
  string acl_id = 1;
  string name = 2;
  string status = 3;
  string address_ip_version = 4;
  string create_time = 5;
  repeated AclEntry acl_entries = 6;
  repeated RelatedListener related_listeners = 7;
  string region_id = 8;
  string isp_id = 9;
  string isp_type = 10;
  string isp_name = 11;}

message AclEntry {
  string status = 1;
  string ip = 2;
  string description = 3;
}

message RelatedListener {
  string status = 1;
  string listener_port = 2;
  string listener_protocol = 3;
  string lb_id = 4;
}

message BatchDeleteSecurityGroupRuleReq {
  string security_group_id = 1;
  repeated string ids = 2;
}

message CreateSecurityGroupRuleReq {
  string security_group_id = 1;
  repeated CreateSecurityGroupRuleEntity permissions = 2;
}

message CreateSecurityGroupRuleEntity {
  string ip_protocol = 1;
  string source_cidr_ip = 2;
  string port_range = 3;
  string policy = 4;
  string description = 5;
  int32 priority = 6;
}

message AddALBAclsEntriesReq {
  string acl_id = 1[(api.path)="acl_id"];
  repeated AddALBAclsEntry entries = 2;
}

message AddALBAclsEntry {
  string entry = 1;
  string description = 2;
}

message RemoveALBAclsEntriesReq {
  string acl_id = 1[(api.path)="acl_id"];
  repeated string entries = 2;
}


message JoinOrLeaveSecurityGroupReq {
    string region_id = 1;
    string instance_type = 2;
    repeated InstanceIdNamePair instance_info = 3;
    string security_group_name = 4; 
}

message FailedInfo {
    string instance_id = 1;
    string message = 2;
}

message ModifySecurityGroupResult {
    repeated string success_instance_ids = 1;
    repeated FailedInfo failed_infos = 2;
}