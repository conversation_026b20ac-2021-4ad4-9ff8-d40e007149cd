syntax = "proto3";

//import "google/protobuf/wrappers.proto";
import "api.proto";

package cloudman;

option go_package = "cloudman";

message taskListReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string status = 5;
  string name = 6;
}

message taskListResp {
  uint32 total = 1;
  repeated taskDetail list = 2;
}

message taskDetail {
  string account_name = 1;
  int32 status = 2;
  string name = 3;
  string account_id = 4;
  string bind_zone_id = 5;
  string bind_zone_name = 6;
  string bind_zone_source_id = 18;
  string task_type = 7;
  uint32 policy = 8;
  uint32 rate = 9;
  uint32 timeout = 10;
  string latest_run_id = 11;
  int32 latest_run_status = 12;
  int64 latest_run_start_time = 13;
  int64 latest_run_end_time = 14;
  string id = 15;
  uint32 retry = 16;
  string account_type = 17;
}

message createTaskReq {
  int32 status = 1;
  string name = 2;
  string account_id = 3;
  string bind_zone_id = 4;
  string task_type = 5;
  uint32 policy = 6;
  uint32 rate = 7;
  uint32 timeout = 8;
  uint32 retry = 9;
}

message updateTaskReq {
  string id = 1[(api.path)="id"];;
  int32 status = 2;
  string name = 3;
  string account_id = 4;
  string bind_zone_id = 5;
  string task_type = 6;
  uint32 policy = 7;
  uint32 rate = 8;
  uint32 timeout = 9;
  uint32 retry = 10;
}

message runTaskReq {
  string oid = 1[(api.path)="id"];;
}
