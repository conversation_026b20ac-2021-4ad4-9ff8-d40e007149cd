syntax = "proto3";

package cloudman;

option go_package = "cloudman";

message create_gateway_req {
  string name = 1;
  string isp = 2;
  string region = 3;
  string ssh_host_id = 4;
  uint32 ssh_port = 5;
  string auth_type = 6;
  string ssh_username = 7;
  string ssh_password = 8;
  string ssh_pem = 9;
}

message list_gateway_resp {
  repeated gateway_resp list = 1;
}

message gateway_resp {
  string status = 1;
  string hostname = 2;
  string address = 3;
  string name = 4;
}

message script {
  string script = 1;
}

message gateway_address_req {
  string public_protoc = 1;
  string public_address = 2;
  string private_protoc = 3;
  string private_address = 4;
}

// 云管任务agent详情
message agent_resp {
  int32 status = 1; // 连接状态
  uint32 lost_total = 2; // 掉线统计
  string agent_id = 3; // agent-id
  string region_id = 4; // 地域id
  string connect_gate = 5; // 来源网关
  string os_name = 6; // 系统类型
  string node_type = 7; // agent类型
  int64 update_time = 8; // 最后一次状态变化时间
  string latest_ip = 9; // 最后一次上报ip
  string latest_hostname = 10; // 最后一次上报主机名
  string proxy_id = 11;
}

message topo_result {
  repeated gateway_resp gateway = 1;
  repeated agent_resp agent = 2;
}

message gatewayTopoReq {
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string type = 6;
  uint64 page = 7;
  uint64 size = 8;
  string search_key = 13;
  string search_value = 14;
}

message topo {
  repeated agent_callback_req proxy_list = 1;
  repeated agent_callback_req normal_list = 2;
}

message gatewayTopoResult {
  topo prod_topo = 1;
  topo test_topo = 2;
  topo no_env_topo = 3;
}

message create_agent_req {
  string isp = 1; // 服务提供商id
  string region = 2; // 连接地域
  string ssh_host_id = 3; // ssh主机id
  uint32 ssh_port = 4; // ssh端口
  string auth_type = 5; // ssh认证类型
  string ssh_username = 6; // ssh username
  string ssh_password = 7; // ssh密码
  string ssh_pem = 8; // ssh证书内容
  string node_type = 9; // 节点类型
  string network_type = 10; // 网络类型
}

message list_agent_req {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string host_name = 5;
  string node_type = 6;
  int32 status = 7;
  string env = 8;
  string version = 9;
  string host_ip = 10;
  string agent_id = 11;
  repeated string search_param = 12;
}

message list_agent_resp {
  repeated agent_callback_req list = 1;
  uint32 total = 2;
  uint32 running = 3;
  uint32 outdated = 4;
  uint32 current = 5;
}

message agent_callback_req {
  string agentId = 1;
  int32 status = 2;
  repeated string real_ips = 3;
  string version = 4;
  string system_version = 5;
  string node_type = 6;
  string proxy_id = 7;
  string hostname = 8;
  string instance_id = 9;
  string region = 10;
  string host_ip = 11;
  uint32 updated_time = 12;
  uint32 created_time = 13;
  string cpu_model = 14;
  int32  cpu_core = 15;
  repeated string gpu_list = 16;
  int64 mem_total = 17;
  string env = 18;
  repeated string net_speed = 19;
}

message get_agent_req {
  string hostname = 1;
  string inner_ipaddress = 2;
  string region = 3;
  string account_id = 4;
}

message agent_install_config {
  string isp_id = 1;
  string region_id = 2;
  string cmdb_env = 3;
  string binary_url = 4;
  string reg_url = 5;
}

message list_agent_install_config_resp {
  repeated agent_install_config list = 1;
}

message ReinstallAgentReq {
  repeated string ids = 1;
  string username = 2;
  string password = 3;
  string port = 4;
}

message UpdateAgentReq {
  repeated string ids = 1;
}

message ChangeAgentStatusReq {
  repeated string ids = 1;
  int32 status = 2;
}

message ListAgentVersionResp{
  repeated string versions = 1;
}
