syntax = "proto3";

package cloudman;
option go_package = "cloudman";

message GetJumpserverReq {
  string region_id = 1; // 区域数据库id
  string isp = 2; // 云商数据库id
}

message GetJumpserverResp {
  repeated string hosts = 1; // 堡垒机主机名列表
}

message GetPipelineReq {
  string region_id = 1; // 区域数据库id
  string isp = 2; // 云商数据库id
}

message GetPipelineResp {
  repeated PipelineData pipelines = 1; // 流水线列表
}

message PipelineData {
  string id = 1;
  string name = 2;
}

message PipelineParam {
  string key = 1;
  string value = 2;
  bool enable_gzip_base64 = 3;
}

message PreviewParamReq {
  repeated PipelineParam params = 1;
  repeated string builtin_params = 2;
  string form_json = 3;
}

message PipelineParamPreviewData {
  string key = 1;
  string value = 2;
  string rendered_value = 3;
}

message PreviewParamResp {
  repeated PipelineParamPreviewData preview_params = 1;
}

message InitOption {
  bool lock = 1;
  bool enable_init = 2;
  string jumpserver_host = 3;
  string pipeline_id = 4;
  repeated PipelineParam pipeline_params = 5;
  repeated string builtin_params = 6;
}