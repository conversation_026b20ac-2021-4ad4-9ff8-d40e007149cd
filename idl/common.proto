syntax = "proto3";

import "api.proto";

package cloudman;

option go_package = "cloudman";

message Result {
  string message = 1;
}

message ResultOK {
  bool ok = 1;
}

message BatchResult {
  repeated string Errors = 1;
  repeated string Success = 2;
  repeated string SuccessCreated = 3;
  repeated string SuccessUpdated = 4;
  repeated string UpdateErrors = 5;
}

message ObjectID {
  string id = 1[(api.path)="id"];
}

message ObjectIDs {
  repeated string list = 1;
}

message Empty {}

message OptionResp {
  repeated FieldOption list = 1;
}

message FieldOption {
  string name = 1;
  string type = 2;
  string value = 3;
  bool required = 4;
  bool indexed = 5;
  bool extra = 6;
}

message AgentIDResp {
  string agent_id = 1;
}

enum ConditionType {
  AND = 0;
  OR = 1;
}

enum QueryOperator {
  equal = 0;
  not_equal = 1;
  in = 2;
  not_in = 3;
  less = 4;
  less_or_equal = 5;
  greater = 6;
  greater_or_equal = 7;
  datetime_less = 8;
  datetime_less_or_equal = 9;
  datetime_greater = 10;
  datetime_greater_or_equal = 11;
  begins_with = 12;
  not_begins_with = 13;
  contains = 14;
  not_contains = 15;
  ends_with = 16;
  not_ends_with = 17;
  is_empty = 18;
  is_not_empty = 19;
  is_null = 20;
  is_not_null = 21;
  exist = 22;
  not_exist = 23;
}

message QueryRule {
  string field = 1;
  QueryOperator operator = 2;
  string value = 3;
  repeated string values = 4;
}

message CustomQuery {
  ConditionType condition = 1;
  repeated QueryRule rules = 2;
  int64 page = 3;
  int64 size = 4;
  repeated string ordering = 5;
}

message timeFilter {
  string ref = 1;
  int32 value = 2;
}

message searchColumn {
  string key = 1;
  string value = 2;
}

message ExportXlsxResp {
  bytes file = 1;
}

message ObjIDReq {
  string id = 1[(api.path)="id"];
}

message OrderID {
  string order_id = 1[(api.path)="order_id"];
}

message Raw {
  bytes raw_body = 2;
}