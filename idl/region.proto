syntax = "proto3";

package cloudman;

option go_package = "cloudman";

message regionQueryRequest {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string type = 6;
}

message regionListResponse {
  uint32 total = 1;
  repeated regionDetail list = 2;

}

message regionDetail {
  string name = 1;
  string ispType = 2;
  string region_id = 3;
  string id = 4;
}
