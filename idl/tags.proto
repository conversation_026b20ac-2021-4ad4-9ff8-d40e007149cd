syntax = "proto3";

//import "google/protobuf/wrappers.proto";
package cloudman;

option go_package = "cloudman";

message tagQueryReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
}

message tagListResp {
  repeated tagInfo list = 1;
  uint32 total = 2;
}

message tagCreateReq{
  string id = 1;
  string key = 2;
  string value = 3;
  string desc = 4;
  string type = 5;
}

message tagInfo {
  string id = 1;
  string key = 2;
  string value = 3;
  string desc = 4;
  string type = 5;
  string kv = 6;
}
// ----------------------
