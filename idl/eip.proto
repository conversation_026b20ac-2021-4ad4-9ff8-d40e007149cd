syntax = "proto3";

//import "google/protobuf/wrappers.proto";

package cloudman;

option go_package = "cloudman";

import "resTemplate.proto";

message EipEntity {
  string allocation_id = 1;
  string allocation_time = 2;
  string bandwidth = 3;
  string bandwidth_package_bandwidth = 4;
  string bandwidth_package_id = 5;
  string bandwidth_package_type = 6;
  string biz_type = 7;
  string business_status = 8;
  string charge_type = 9;
  bool deletion_protection = 10;
  string description = 11;
  string eip_bandwidth = 12;
  string expired_time = 13;
  string hd_monitor_status = 14;
  string has_reservation_data = 15;
  string isp = 16;
  string instance_id = 17;
  string instance_region_id = 18;
  string instance_type = 19;
  string internet_charge_type = 20;
  string ip_address = 21;
  string name = 22;
  string netmode = 23;
  repeated EipAddressOperationLocks operation_locks = 24;
  string public_ip_address_pool_id = 25;
  string region_id = 26;
  string reservation_active_time = 27;
  string reservation_bandwidth = 28;
  string reservation_internet_charge_type = 29;
  string reservation_order_type = 30;
  string resource_group_id = 31;
  bool second_limited = 32;
  repeated string security_protection_types = 33;
  string segment_instance_id = 34;
  int32 service_managed = 35;
  string status = 36;
  repeated resource_tag tags = 37;
  string vpc_id = 38;
  string zone = 39;
  string isp_id = 40;
  string isp_type = 41;
  string isp_name = 42;
}

message EipAddressOperationLocks {
  string lock_reason = 1;
}

message DescribeEipReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
  string search_key = 5;
  string search_value = 6;
  string ip_address = 7;
}

message DescribeEipRes {
  int32 total = 1;
  repeated EipEntity list = 2;
}

message DescribeEipByInstancesReq {
  uint64 page = 1;
  uint64 size = 2;
  string instanceType = 3;
  repeated string instance_ids = 4;
}

message DescribeEipSegmentReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
}

message DescribeEipSegmentRes {
  int32 total = 1;
  repeated EipSegment list = 2;
}

message EipSegment {
  string status = 1;
  string segment = 2;
  string ip_count = 3;
  string instance_id = 4;
}

message DescribeIpamReq {
  string isp = 1;
  string region_id = 2;
}

message DescribeIpamRes {
  int32 total = 1;
  repeated Ipam list = 2;
}

message Ipam {
  string id = 1;
  string state = 2;
  repeated string cidrs = 3;
  int32 total_count = 4;
  float assigned_percent = 5;
}
