syntax = "proto3";

//import "google/protobuf/wrappers.proto";
import "initResource.proto";
import "api.proto";

package cloudman;

option go_package = "cloudman";

message resTemQueryReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string type = 6;
  string isp = 7;
}

message resTemListResp {
  repeated resTemShort list = 1;
  uint32 total = 2;
}

message resTemShort {
  string id = 1;
  string name = 2;
  string isp_id = 3;
  string version = 4;
  int64 updated_time = 5;
  string type = 6;
  string isp_name = 7;
  string isp_type = 8;
  string bind_region = 9;
  string desc = 10;
}

message resTemCreateReq {
  string id = 1[(api.path)="id", (api.query)="id"];
  string name = 2;
  string desc = 3;
  string type = 4;
  string isp = 5;
  string info = 6;
  string rid = 7;

  // 初始化配置
  InitOption init_option = 8;
}

message AliEcsTemplate {
  string ZoneId = 1;
  string InstanceChargeType = 2;
  repeated string ImageId = 3;
  repeated string InstanceType = 4;
  string LoginType = 5;
  string Password = 6;
  string KeyPairName = 7;

  message SYSTEMDISK {
    string PerformanceLevel = 1;
    repeated string Category = 2;
    int32 min = 3;
    int32 max = 4;
  }

  SYSTEMDISK SystemDisk = 8;

  message DATADISK {
    string PerformanceLevel = 1;
    repeated string Category = 2;
    double min = 3;
    double max = 4;
    double limit = 5;
  }

  DATADISK DataDisk = 9;
  string VpcId = 10;
  repeated string VSwitchId = 11;
  repeated string SecurityGroupId = 12;
  string InstanceName = 13;
  string HostName = 14;
  repeated string default_tags = 15;
}

// ---------------------------

message ZoneReq {
  string isp_id = 1;
  string InstanceChargeType = 2;
  string rid = 3[(api.path)="rid", (api.query)="rid"];
}

message ZoneRespList {
  repeated ZoneRespInfo list = 1;
}

message ZoneRespInfo {
  string ZoneId = 1;
  string LocalName = 2;
  repeated string DataDiskCategories = 3;
  repeated string SystemDiskCategory = 4;
  repeated string InstanceType = 5;
}

message CheckInstanceNameSeqReq {
  string resource_type = 1;
  string prefix = 11;
  string suffix = 12;
}
message CheckInstanceNameSeqResp {
  int32 min_unused = 1;
  int32 max_unused = 2;
}

enum GenInstanceNameSysType {
  windows = 0;
  linux = 1;
}

message GenInstanceNameWithRuleReq {
  string resource_type = 1;
  string rule = 2;
  int32 count = 3;
  bool is_unique = 4;
  GenInstanceNameSysType sys_type = 5;
}

message GenInstanceNameWithRuleResp {
  repeated string instance_names = 1;
}

message HostResReq {
  string isp = 1;
  string ZoneId = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string type = 6;
  uint64 page = 7;
  uint64 size = 8;
  string region_id = 9;
  string tag_key = 10;
  repeated string tag_values = 11;
  string search_key = 13;
  string search_value = 14;
  string agent_status = 15;
  string monitor_status = 16;
  string Status = 17;
  string update_time_ref = 18;
  int32 update_time_val = 19;
  int32 expireTimeBefore = 20;
  bool releaseResource = 21;
  bool display_recyclable = 22;
  bool display_need_cleanup = 23;
  string agent_env = 24;
  string os_type = 25;
  repeated string batch_os_name = 26;
  repeated string batch_agent_status = 27;
  repeated string security_group_ids = 28;
  bool and_search_security_group_ids = 29;
  repeated string batch_monitor_status = 30;
}

message HostResResp{
  repeated HostResDetail list = 1;
  int32 total = 2;
}

message HostResDetail {
  string instance_id = 1;
  string instance_type = 2;
  string isp = 3;
  string status = 4;
  repeated InstanceNetworkInterface network_interface = 5;
  string ops_agent_status = 6;
  string monitor_status = 7;
  string filebeat_status = 8;
  string host_name = 9;
  int32 cpu = 10;
  int32 memory = 11;
  int32 CreationTime = 12;
  bool islock = 13; // 锁定资源
  string bk_inst_name = 14; // 资源唯一标识
  string region_id = 15;
  string isp_name = 16;
  string isp_type = 17;
  string id = 18;
  repeated string public_address = 19;
  repeated string private_address = 20;
  string ops_agent_id = 21;
  repeated resource_tag tags = 22;
  string os_name = 23;
  repeated SecurityGroupInfo SecurityGroupInfos = 24; // 安全组ID
  int32 ExpiredTime = 25;
  int32 InternetMaxBandwidthIn = 26;
  int32 InternetMaxBandwidthOut = 27;
  string ZoneId = 28;
  string InstanceChargeType = 29;
  string Description = 30;
  InstanceVpcAttribute VpcAttr = 31;
  string ImageId = 32;
  string EipAddress = 33;
  string InstanceName = 34;
  string OSType = 35;
  string GPUSpec = 36;
  string isp_id = 37;
  bool allow_change_data = 38;
  int64 updated_time = 39;
  int64 StartTime = 40;
  bool Recyclable = 41;
  string AutoReleaseTime = 42;
  bool NeedCleanup = 43;
  bool installAgent = 44;
  string username = 45;
  string password = 46;
  string port = 47;
  string ops_agent_env = 48;
  repeated string serviceTreeNodes = 49;
  CmdbInfo cmdb_info = 50;
  repeated string ipv6_address = 51;
}

message OpsStatus {
  string agent_status = 1;
  string monitor_status = 2;
}

message HostOpsStatusResp {
  map<string, OpsStatus> hostMap = 1;
}

message CmdbInfo{
  int32 inst_id = 1;
  repeated string game_region = 2;
  repeated string process = 3;
}

message InstanceNetworkInterface{
  string type = 1;
  string mac_address = 2;
  string network_interface_id = 3;
  repeated string ipv6_sets = 4;
  string primary_ip_address = 5;
  repeated private_ip_set private_ip_sets = 6;
  string bandwidth = 7;
}

message private_ip_set {
  bool primary = 1;
  string private_ip_address = 2;
}

message HostDiskInfoResp{
  repeated DiskInfo disks = 1;
}

message DiskInfo{
  string DiskId = 1;
  string Status = 2;
  string DiskName = 3;
  string CreationTime = 4;
  int32 Size = 5;
  string ImageId = 6;
  string DiskType = 11;
  string Device = 12;
  string Category = 13;
  string PerformanceLevel = 21;
  int32 IOPSReadWrite = 22;
  string RawData = 91;
}

message SyncInstancesReq {
  repeated string ids = 1;
}

// vpc
message InstanceVpcAttribute {
  string VpcId = 1;
  string NatIpAddress = 2;
  string VSwitchId = 3;
}

// 标签
message resource_tag {
  string key = 1;
  string value = 2;
  string type = 3;
}


enum Datasource {
  raw = 0;
  custom = 1;
}

// 查询密钥对
message KeyPairReq {
  string isp = 1;
  string name = 2;
  string rid = 3[(api.path)="rid", (api.query)="rid"];
  bool raw = 4;
}

message KeyPairResp {
  repeated string list = 1;
  Datasource source = 2;
}

// 查询安全组
message SecurityGroupReq {
  string isp = 1;
  int32 size = 2;
  string vpc_id = 3;
  string rid = 4[(api.path)="rid", (api.query)="rid"];
  bool raw = 5; // 是否返回原始数据
}

message SecurityGroupResp {
  repeated SecurityGroupInfo list = 1;
  Datasource source = 2;
}

message SecurityGroupInfo {
  string Description = 1;
  string VpcId = 2;
  string SecurityGroupId = 3;
  string SecurityGroupName = 4;
  string SecurityGroupType = 5;
}

// 查询ip白名单
message IPWhiteListReq {
  string isp = 1;
  int32 size = 2;
  string rid = 3[(api.path)="rid", (api.query)="rid"];
}

message IPWhiteListResp{
  repeated IPWhiteListInfo list = 1;
}


message IPWhiteListInfo{
  string TemplateId = 1;
  string TemplateName = 2;
}

// ----------------
message NetworkReq {
  string isp = 1;
  string rid = 2[(api.path)="rid", (api.query)="rid"];
  bool raw = 3;
}

message NetworkResp {
  repeated NetworkInfo list = 1;
  Datasource source = 2;
}

message NetworkInfo {
  string name = 1;
  string vpc_id = 2;
  string status = 3;
  string description = 4;
  bool isDefault = 5;
}
//-----------------

// ---------------
message VSwitchReq {
  string isp = 1;
  string zone_id = 2;
  string vpc_id = 3;
  string rid = 4[(api.path)="rid", (api.query)="rid"];
  bool raw = 5;
}

message VSwitchResp {
  repeated VSwitchInfo list = 1;
  Datasource source = 2;
}

message VSwitchInfo {
  string status = 1;
  string vSwitch_id = 2;
  string vSwitch_name = 3;
}
//---------------


message CreateResReq {
  string isp_id = 1; // 云厂商
  string template_id = 2; // 模板id
  string data = 3; // 值
  string type = 4; // 云资源类型
  string rid = 5; // 绑定地域
  string reason = 6; // 申请理由

  // 初始化配置
  InitOption init_option = 8;
}

message Ids {
  repeated string ids = 1;
}

message bkInstNames {
  repeated string bk_inst_name = 1;
}


message ResCommonReq {
  string isp_id = 1; // 云厂商
  string template_id = 2; // 模板id
  string data = 3; // 值
  string type = 4; // 云资源类型
  string rid = 5;
  string region_id = 6;
  string extend_type = 7;
  string reason = 8; // 资源申请理由
}

message BulkUpdateReq {
  repeated BulkUpdateCommon list = 1;
  string status = 2;
}

message BulkUpdateCommon {
  string isp_id = 1; // 云厂商
  string template_id = 2; // 模板id
  string type = 3; // 云资源类型
  string rid = 4;
  string region_id = 5;
}

message ImagesReq {
  string isp_id = 1; // 云厂商id
  string region_id = 2; // 地域id
  string rid = 3[(api.path)="rid", (api.query)="rid"]; // 地域pk
  string os_type = 4; // 系统类型
  string image_name = 5; // 镜像名称
  bool raw = 6;
  bool is_public = 7; // 是否为公共镜像
}

message ImagesResp {
  repeated ImageResp list = 1;
  Datasource source = 2;
}

message ImageResp {
  string os_name = 1;
  string image_name = 2;
  string image_id = 3;
  string os_type = 4;
  bool isPublic = 5;
  int32 size = 6;
}

message ChargeTypeReq {
  string isp = 1[(api.path)="isp", (api.query)="isp"]; // 云厂商类别
  bool raw = 2;
  string rid = 3[(api.path)="rid", (api.query)="rid"]; // 地域id
}

message ChargeTypesResp {
  repeated ValueLabelResp list = 1; // 计费模式列表
  Datasource source = 2;
}

message InstanceTypeReq {
  string isp_id = 1; // 云厂商ID
  string region_id = 2; // 地域ID
  string rid = 3[(api.path)="rid", (api.query)="rid"]; // 地域pk
  bool raw = 4;
  string images_id = 5; // 根据实例镜像筛选实例规格,仅阿里云可用
  string zone_id = 6;
}

message InstanceTypeResp {
  string value = 1; // 实例值
  string label = 2; // 实例标签
  float MemorySize = 3;        // 内存大小
  int32 CPUCoreCount = 4;      // vpc内核数目
  string InstanceTypeFamily = 5; // 实例规格组
  string InstanceTypeID = 6;     // 实例规格ID
  string Status = 7; // Available/SoldOut
}

message InstanceTypesResp {
  repeated InstanceTypeResp list = 1;
  Datasource source = 2;
}

message VolumeTypeReq {
  string isp_id = 1;    // 云厂商ID
  string region_id = 2; // 地域ID
  string rid = 3[(api.path)="rid", (api.query)="rid"];       // 地域PK
  string image_id = 4; // 镜像ID
  bool raw = 5;
}

message ValueLabelResp {
  string value = 1; // key
  string label = 2; // 解释说明文字
}

enum VolumeTypeMounted {
  system = 0;
  data = 1;
}

message VolumeType {
  repeated ValueLabelResp CateGoryOptions = 1;
  uint32 min_size = 2;
  uint32 max_size = 3;
  uint32 min_count = 4;
  uint32 max_count = 5;
}

message VolumeTypesResp {
  //  repeated VolumeType list = 1;
  VolumeType system_disk = 1;
  VolumeType data_disk = 2;
  Datasource source = 3;
}

message ExportXlsxReq {
  string export_type = 1;
  string isp = 2;
  string region_id = 3;
  repeated string export_fields = 4;
  repeated selectValue selected = 5;
  string ZoneId = 6;
  string name = 7;
  string type = 8;
  string tag_key = 9;
  repeated string tag_values = 10;
  string search_key = 11;
  string search_value = 12;
  string agent_status = 13;
  string monitor_status = 14;
  string Status = 15;
  string update_time_ref = 16;
  int32 update_time_val = 17;
  int32 expireTimeBefore = 18;
  bool releaseResource = 19;
  bool display_recyclable = 20;
}

message selectValue {
  string id = 1;
}

message ImportXlsxReq {
  bytes file = 1;
  string file_name = 2;
  string isp_id = 3;
}

message ImportXlsxResp {

}

// mysql==================
message MysqlClusterReq {
  string isp = 1;
  string ZoneId = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string type = 6;
  uint64 page = 7;
  uint64 size = 8;
  string region_id = 9;
  string tag_key = 10;
  repeated string tag_values = 11;
  string search_key = 13;
  string search_value = 14;
  bool display_recyclable = 15;
  bool display_need_cleanup = 17;
  string DBClusterStatus = 16;
  string update_time_ref = 18;
  int32 update_time_val = 19;
  repeated string security_group_ids = 20;
  bool and_search_security_group_ids = 21;
}

message MysqlClusterResp {
  repeated MysqlClusterDetail list = 1;
  int32 total = 2;
}

message MysqlClusterDetail {
  string id = 1;
  bool is_lock = 2;
  string isp_id = 3;
  string isp_name = 4;
  string isp_type = 5;
  string VpcId = 6;
  string ExpireTime = 7;
  string Expired = 8;
  int32 DBNodeNumber = 9;
  string CreateTime = 10;
  string Status = 11;
  string DBNodeClass = 12;
  repeated resource_tag Tags = 13;
  string DBType = 14;
  string LockMode = 15;
  string RegionId = 16;
  string DBVersion = 17;
  string DBClusterId = 18;
  string DBClusterStatus = 19;
  string ResourceGroupId = 20;
  int64 StorageUsed = 21;
  string DBClusterNetworkType = 22;
  string DBClusterDescription = 23;
  string ZoneId = 24;
  string Engine = 25;
  string PayType = 26;
  int64 updated_time = 39;
  bool Recyclable = 40;
  bool NeedCleanup = 41;
  repeated SecurityGroupInfo SecurityGroupInfos = 42; // 安全组ID
}

message MysqlDatabases {
  repeated MysqlDatabase list = 1;
}

message MysqlDatabase {
  string id = 1;
  repeated resource_tag Tags = 2;
  string charactersetname = 3;
  string cluster_id = 4;
  string dbname = 5;
  string dbstatus = 6;
  string dbdescription = 7;
  bool is_lock = 8;
  repeated MysqlAccount accounts = 9;
  int64  updated_time = 39;
  CmdbInfo cmdb_info = 10;
}

message MysqlAccounts {
  repeated MysqlAccount list = 1;
}

message MysqlAccount {
  string PrivilegeStatus = 1;
  string AccountStatus = 2;
  string AccountPrivilege = 3;
  string AccountName = 4;
}

message MysqlAccountsInfo {
  repeated MysqlAccountInfo list = 1;
}

message MysqlAccountInfo {
  string id = 1;
  bool is_lock = 2;
  string cluster_id = 3;
  string AccountStatus = 4;
  repeated MysqlAccountsPrivileges DatabasePrivileges = 5;
  string AccountDescription = 6;
  string AccountPasswordValidTime = 7;
  string AccountType = 8;
  string AccountLockState = 9;
  string AccountName = 10;
  int64  updated_time = 39;
}

message WhitelistInfo {
  string cluster_id = 1;
  InstanceSecurityGroupIds security_group_ids = 2;
  repeated InstanceIPGroup ip_groups = 3;
}

message InstanceSecurityGroupIds {
  repeated string SecurityGroupID = 1; // 安全组集合
}

message InstanceIPGroup {
  string SecurityIpGroupAttribute = 1;
  string SecurityIpGroupName = 2;
  repeated string SecurityIpList = 3;
}

message MysqlAccountsPrivileges {
  string DBName = 1;
  string AccountPrivilege = 2;
}

message MysqlEndpoints {
  repeated MysqlEndpoint list = 1;
}

message MysqlEndpoint {
  bool is_lock = 2;
  string DBClusterID = 3;
  string NodeWithRoles = 4;
  string Nodes = 5;
  string ReadWriteMode = 6;
  repeated MysqlClusterAddressItems AddressItems = 7;
  string DBEndpointId = 8;
  string EndpointConfig = 9;
  string DBEndpointDescription = 10;
  string EndpointType = 11;
  string AutoAddNewNodes = 12;
  int64  updated_time = 39;
}

message MysqlClusterAddressItems {
  string VSwitchId = 1;
  string PrivateZoneConnectionString = 2;
  string ConnectionString = 3;
  string NetType = 4;
  string Port = 5;
  string VpcInstanceId = 6;
  string VPCId = 7;
  string IPAddress = 8;
}
// mysql==================

// redis==================
message RedisReq {
  string isp = 1;
  string ZoneId = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string type = 6;
  uint64 page = 7;
  uint64 size = 8;
  string region_id = 9;
  string tag_key = 10;
  repeated string tag_values = 11;
  string InstanceId = 12;
  string search_key = 13;
  string search_value = 14;
  bool display_recyclable = 15;
  bool display_need_cleanup = 16;
  string InstanceStatus = 17;
  string update_time_ref = 18;
  int32 update_time_val = 19;
  repeated string security_group_ids = 20;
  bool and_search_security_group_ids = 21;
}

message RedisResp {
  repeated RedisDetail list = 1;
  int32 total = 2;
}

message RedisDetail {
  string id = 1;
  bool is_lock = 2;
  string isp_id = 3;
  string isp_name = 4;
  string isp_type = 5;
  string ReplacateId = 6;
  string InstanceId = 7;
  string InstanceName = 8;
  string SearchKey = 9;
  string ConnectionDomain = 10;
  int64 Port = 11;
  string UserName = 12;
  repeated resource_tag Tags = 13;
  string InstanceStatus = 14;
  string RegionID = 15;
  int64 Capacity = 16;
  string InstanceClass = 17;
  int64 QPS = 18;
  int64 Bandwidth = 19;
  int64 Connections = 20;
  string ZoneId = 21;
  string Config = 22;
  string ChargeType = 23;
  string NetworkType = 24;
  string VpcId = 25;
  string VSwitchId = 26;
  string PrivateIp = 27;
  string CreateTime = 28;
  string EndTime = 29;
  bool HasRenewChangeOrder = 30;
  bool IsRds = 31;
  string InstanceType = 32;
  string ArchitectureType = 33;
  string NodeType = 34;
  string PackageType = 35;
  string EngineVersion = 36;
  string DestroyTime = 37;
  string ConnectionMode = 38;
  string ResourceGroupId = 39;
  int32 ShardCount = 40;
  int64 updated_time = 41;
  bool Recyclable = 42;
  bool NeedCleanup = 43;
  repeated SecurityGroupInfo SecurityGroupInfos = 44; // 安全组ID
}

message RedisAccounts{
  repeated RedisAccount list = 1;
  uint32 total = 2;
}

message RedisAccount {
  string id = 1;
  bool is_lock = 2;
  string isp_id = 3;
  string isp_name = 4;
  string isp_type = 5;
  string InstanceId = 6;
  string AccountName = 7;
  string AccountStatus = 8;
  string AccountType = 9;
  string AccountDescription = 10;
  repeated DatabasePrivilege DatabasePrivileges = 11;
}

message DatabasePrivilege {
  string AccountPrivilege = 1;
}

// redis===================

// monitor==============
message MonitorReq {
  string InstanceName = 1;
  string os_type = 2;
}


message MonitorResp {
  repeated MonitorResult results = 1;
}

message MonitorResult {
  repeated MonitorSeries series = 1;
}

message MonitorSeries {
  string name = 1;
  repeated string columns = 2;
  repeated MonitorValue values = 3;
  MonitorTags tags = 4;
}

message MonitorTags {
  string host = 1;
  string path = 2;
  string instance = 3;
}

message MonitorValue {
  repeated float value = 1;
}

message GraphView {
  string address = 1;
};
//

message GaiaViewReq {
  string endpoint = 1;
}

message GaiaMonitReq {
  string consolFuc = 1;
  int32 count = 2;
  string dstype = 3;
  int32 end = 4;
  repeated string endpoints = 5;
  repeated string groupKey = 6;
  string metric = 7;
  int32 start = 8;
  int32  step = 10;
};

message GaiaMonitResp {
  repeated GaiaMonitDat dat = 1;
  string err = 2;
};

message GaiaMonitDat {
  int32 comparison = 1;
  string counter = 2;
  string dstype = 3;
  int32 end = 4;
  string endpoint = 5;
  string nid = 6;
  int32 start = 7;
  int32 step = 8;
  repeated GaiaMonitValue values = 9;
}

message GaiaMonitValue {
  int32 timestamp = 1;
  float value = 2;
}

message UpdateMonitorStatusReq {
  repeated string ips = 1;
  int32 status = 2;
}

message CommonResourceResp {
  string message = 1;
  string order_id = 2;
  string ferry_url = 3;
}

message BatchModifyReq {
  repeated string change_attrs = 1;
  repeated string instances = 2;
  bool allow_change_data = 3;
  string Description = 4;
  repeated resource_tag tags = 5;
  string tag_rule = 6;
}

message AutoSnapshotPolicyEXReq {
  string rid = 1;
  string isp_id = 2;
  int32 page = 3;
  int32 size = 4;
}

message AutoSnapshotPolicyEXResp {
  int32 total = 1;
  repeated AutoSnapshotPolicyEXInfo list = 2;
  Datasource source = 3;
}

message AutoSnapshotPolicyEXInfo {
  string status = 1; // 自动快照策略状态
  string name = 2; // 策略名称
  string time_points = 3; // 策略执行时间点
  string id = 4; // 策略名称ID
  string repeat_week_days = 5; // 每周执行策略
}

message DBTypeReq {
  string isp_id = 1;
  string rid = 2[(api.path)="rid", (api.query)="rid"];
  string zone_id = 3;
  string engine = 4;
}

message DBTypeResp {
  repeated DBType list = 1;
  Datasource source = 2;
}

message DBType {
  string engine = 1; // 数据库类型
  string engine_name = 2; // 数据库类型
  repeated EngineVersion engine_version = 3; // 数据库版本
}

message EngineVersion {
  string version = 1;
  repeated DBCategory category_list = 2;
}

message DBCategory {
  string category = 1;
  repeated string class_code = 2;
  repeated string storage_type_list = 3;
}

message DBClassesReq {
  //  获取支持的实例规格
  string isp_id = 1;
  string rid = 2[(api.path)="rid", (api.query)="rid"];
  string DBType = 3;
  string DBVersion = 4;
}

message DBClassesResp {
  repeated DBClasses list = 1;
  Datasource source = 2;
}

message DBClasses {
  string id = 1;
  string cpu = 2;
  string memory = 3;
  string group = 4;
}

message DBInstanceFamilyReq {
  // 获取实例规格列表
  string isp_id = 1;
  string rid = 2;
  string engine = 3; // 数据类型, Mysql/PolarDB
  string engine_version = 4; // 数据库版本
  string charge_type = 5; // 付费类型
}

message DBInstanceFamilyResp {

}

message DBZoneReq {
  string isp_id = 1;
  string rid = 2[(api.path)="rid", (api.query)="rid"];
}

message DBZoneResp {
  repeated AvailableZone list = 1;
}

message DBParamsGroupsReq {
  string isp_id = 1[(api.path)="isp_id", (api.query)="isp_id"];
  string rid = 2[(api.path)="rid", (api.query)="rid"];
  string engine = 3; // 引擎类型
  string engine_version = 4; // 引擎版本
}

message DBParamsGroup {
  string id = 1;
  string name = 2;
  string desc = 3;
}

message DBParamsGroupsResp {
  repeated DBParamsGroup list = 1;
}

message AvailableZone {
  string region_id = 1;
  string zone_id = 2;
  string zone_name = 3;
}

message RunOrderReq {
  string id = 1;
  string status = 2;
}

message RunOrderRetryReq {
  string id = 1 [(api.path)="id", (api.query)="id"];
  repeated string retry_list = 11;
}

message CacheParamsGroupsReq {
  string isp_id = 1 [(api.path)="isp_id", (api.query)="isp_id"];
  string rid = 2 [(api.path)="rid", (api.query)="rid"];
  string engine = 3; // 引擎类型
  string engine_version = 4; // 引擎版本
  string group_name = 5; // 参数组名称
}

message CacheParamsGroupsResp {
  repeated CacheParamsGroup list = 1;
  Datasource source = 2;
}

message CacheParamsGroup {
  string name = 1;
  string desc = 2;
}

message CacheClassReq {
  string isp_id = 1;
  string rid = 2[(api.path)="rid", (api.query)="rid"];
  string DBType = 3;
  string DBVersion = 4;
  string zone_id = 5;
  string product_type = 6;
}

message CacheClassesResp {
  repeated CacheClasses list = 1;
  Datasource source = 2;
}

message CacheClasses {
  string id = 1;
  string remark = 2;
  string shardNum = 3;
  string memory = 4;
  string group = 5;
  string zoneId = 6;
  string nodeType = 7;
}

message HostDashBoardReq {
  bool displayRecyclable = 1;
  string isp_id = 2;
  string region_id = 3;
}

//message HostDashBoardResp {
//  repeated HostDashBoardStatus list= 1;
//}

message HostDashBoardResp {
  //  string isp_id = 1;
  HostStatus host_status = 2;
  ResourceStatus res_status = 3;
  AgentStatus ops_agent_status = 4;
  AgentStatus monitor_agent_status = 5;
}

message HostStatus {
  int32 running = 1;
  int32 un_running = 2;
}

message ResourceStatus {
  int32 expire = 1;
  int32 release = 2;
  int32 needCleanup = 3;
}

message AgentStatus {
  int32 running = 1;
  int32 pending = 2;
  int32 stopped = 3;
}

message hostname {
  string hostname = 1;
}

message SalePrice {
  float price = 1; // 订单实际价格
}

message OSNames {
  repeated string list = 1;
}


message InstanceIdNamePair {
    string instance_id = 1;
    string instance_name = 2;
}

message UpdateResReq {
    repeated InstanceIdNamePair instance_infos = 1;
    string instance_type = 2;
    string reason = 3;
    string action = 4;
    string actionData = 5;
}

message UpdateResourcesResp {
    string message = 1;
    repeated string order_id = 2;
}

message UploadBackupSetToOSSReq {
    string backup_order_id = 1;
}

message UploadBackupSetToOSSResp {
    string upload_order_id = 1;
}

message GetBackupDetailResp {
    repeated BackupDetailInfo backup_detail_infos = 1;
}

message BackupDetailInfo {
    string instance_id = 1;
    string instance_name = 2;
    repeated NodeInstanceDownloadUrl node_download_urls = 3;
}

message NodeInstanceDownloadUrl {
    string node_id = 1; // 集群版是分片id，非集群版为实例id
    string intranet_download_url = 2;
}

message AssignIpv6AddressReq {
    string instance_id = 1; // 实例ID
    bool enable_public_access = 2; // 是否允许公网访问
    string public_paytype = 3; // 公网收费类型
    int32 public_bandwidth = 4; // 公网带宽
}


message UnassignIpv6AddressesReq {
    string instance_id = 1; // 实例ID
    string ipv6_address=  2; // ipv6地址
}