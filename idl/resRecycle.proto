syntax = "proto3";

//import "google/protobuf/wrappers.proto";
import "api.proto";

package cloudman;

option go_package = "cloudman";

message recycleQueryReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string type = 6;
  string instance_id = 7;
  string instance_name = 8;
  string instance_type = 9;
  string isp_id = 10;
  string region_id = 11;
  string create_user = 12;
  string status = 13;
}

message recycleListResp {
  repeated recycleShort list = 1;
  uint32 total = 2;
}

message recycleShort {
  string id = 1;
  string name = 2;
  string type = 3;
  string isp = 4;
  int32 status = 5;
  int64 created_time = 6;
  int64 destroy_time = 7;
  string update_user = 8;
  string instance_id = 9;
  string instance_type = 10;
  string create_user = 11;
  repeated string inner_ip_address = 12;
}

message recycleInfo {
  string id = 1;
  string name = 2;
  string type = 3;
  string isp = 4;
  uint32 status = 5;
  int64 created_time = 6;
  int64 destroy_time = 7;
  string update_user = 8;
}

message recyclePolicyReq {
  string instance_type = 1;
  string name = 2;
  uint64 page = 3;
  uint64 size = 4;
}

message recyclePolicyResp {
  repeated recyclePolicy list = 1;
  int32 total = 2;
}

message recyclePolicy {
  string id = 1;
  string name = 2;
  string instance_type = 3;
  bool enable = 4;
  string policy_info = 5;
  int32 release_time = 6; // 释放时间
}

message RecyclePolicyInfo {
  string related = 1; // 条件间关系
  string list = 2;
}

message RecyclePolicyList {
  string key = 1;
  string related = 2;
  //  google.protobuf.Value value = 3;
  string value = 3;
}

message policyOption {
  string name = 1;
  string instance_type = 2;
}

message RecoverReq {
  string id = 1[(api.path)="id", (api.query)="id"];
  string reason = 2;
}

message BatchRecoverReq {
  repeated string ids = 1;
  string reason = 2;
}
// ------------------------

message RecycleReq {
    string id = 1[(api.path)="id", (api.query)="id"];
    bool is_force = 2; 
}