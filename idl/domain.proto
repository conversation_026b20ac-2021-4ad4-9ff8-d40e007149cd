syntax = "proto3";

//import "google/protobuf/wrappers.proto";

package cloudman;

option go_package = "cloudman";

import "resTemplate.proto";

message DomainEntity {
  int32 DomainId = 1;
  string Name = 2;
  string Status = 3;
  int32 TTL = 4;
  string CNAMESpeedup = 5;
  string DNSStatus = 6;
  string Grade = 7;
  int32 GroupId = 8;
  string SearchEnginePush = 9;
  string Remark = 10;
  string Punycode = 11;
  repeated string EffectiveDNS = 12;
  int32 GradeLevel = 13;
  string GradeTitle = 14;
  string IsVip = 15;
  string VipStartAt = 16;
  string VipEndAt = 17;
  string VipAutoRenew = 18;
  int32 RecordCount = 19;
  string CreatedOn = 20;
  string UpdatedOn = 21;
  string Owner = 22;
  repeated resource_tag TagList = 23;
  string isp_id = 24;
  string isp_type = 25;
  string isp_name = 26;
  repeated Record record_list = 27;
  string region_id = 28;
}

message Record {
  uint64 RecordId = 1;
  string Value = 2;
  string Status = 3;
  string UpdatedOn = 4;
  string Name = 5;
  string Line = 6;
  string LineId = 7;
  string Type = 8;
  uint64 Weight = 9;
  string MonitorStatus = 10;
  string Remark = 11;
  uint64 TTL = 12;
  uint64 MX = 13;
  bool DefaultNS = 14;
}

message DomainAddressOperationLocks {
  string lock_reason = 1;
}

message DescribeDomainReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
  string search_key = 5;
  string search_value = 6;
}

message DescribeDomainRes {
  int32 total = 1;
  repeated DomainEntity list = 2;
}

message DescribeDomainRecordsReq {
  uint64 page = 1;
  uint64 size = 2;
  string record_value = 3;
}

message CreateDomainReq {
  string name = 1;
}