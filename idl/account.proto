syntax = "proto3";

import "api.proto";
package cloudman;

option go_package = "cloudman";

message accountId {
  string id = 1;
}

message accountQueryReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string statue = 5;
  string name = 6;
  string type = 7;
}

message accountListRes {
  uint32 total = 1;
  repeated accountDetail list = 2;
}

message createAccountRequest {
  int32 status = 1;
  string name = 2;
  string a_type = 3;
  string access_key = 4;
  string access_secret = 5;
  repeated string region_ids = 6;
  repeated user_id user_ids = 7;
  uint32 index_id = 8;
  bool high_level_permission = 9;
  bool use_agent = 10;
  string arn = 11;
  string host = 12;
  string kms_name = 13;
  int32 project_id = 14;
}

message updateAccountRequest {
  string id = 1[(api.path)="id"];
  int32 status = 2;
  string name = 3;
  string a_type = 4;
  string access_key = 5;
  string access_secret = 6;
  repeated string region_ids = 7;
  repeated user_id user_ids = 8;
  uint32 index_id = 9;
  bool high_level_permission = 10;
  bool use_agent = 11;
  string host = 12;
  string kms_name = 13;
  int32 project_id = 14;
}

message accountDetail {
  uint32 status = 1;
  string name = 2;
  string a_type = 3;
  string access_key = 4;
  string access_secret = 5;
  repeated region region = 7;
  string id = 8;
  int32 ping_status = 9;
  repeated user_id user_ids = 10;
  uint32 index_id = 11;
  bool high_level_permission = 12;
  bool use_agent = 13;
  int32 agent_status = 14;
  string update_user = 15;
  int64 updated_time = 16;
  string host = 17;
  string kms_name = 18;
  int32 project_id = 19;
}

//message region_info {
//  string id = 1;
//  string name = 2;
//}

message pingResult {
  enum Res {
    PongSKFromDB = 0;
    NoReply = 1;
    PongSKFromReq = 2;
  }
  Res result = 1;
}

message updateAccountStatusReq {
  string id = 1[(api.path)="id"];
  int64 status = 2;
}

message regionResult {
  repeated region regions = 1;
}

message region {
  string local_name = 1;
  string region_id = 2;
  string isp_type = 3;
  string id = 4;
}

message user_id {
  string id = 1;
}


message listAccountParamReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string paramType = 5;
  string bind_region_id = 6;
  string bind_account_id = 7[(api.path)="accountId"];
}

message listAccountParamResp {
  uint32 total = 1;
  repeated accountParamInfo list = 2;
}

enum accountParamsType {
  host = 0;
  mysql = 1;
  redis = 2;
}

enum status {
  enabled = 0;
  disabled = 1;
}

message accountParamInfo {
  string id = 1[(api.path)="id"];
  accountParamsType param_type = 2;
  string bind_region_id = 3;
  string params = 4;
  string bind_account_id = 5[(api.path)="accountId"];
  status status = 6;
  int32 update_time = 7;
  string update_user = 8;
  int32 create_time = 9;
  string create_user = 10;
}

message SysConfig {
  bool enable_work_order = 1;
  bool show_tag_value = 2;
  bool auto_refresh_ops_status = 3;
}
