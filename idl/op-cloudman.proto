syntax = "proto3";

import "common.proto";
import "account.proto";
import "cmdb.proto";
import "dashBoard.proto";
import "inner.proto";
import "resourceGroup.proto";
import "resRecycle.proto";
import "resTemplate.proto";
import "tags.proto";
import "task.proto";
import "taskLog.proto";
import "region.proto";
import "cloudGateway.proto";
import "cloudAgent.proto";
import "permission.proto";
import "servicetree.proto";
import "securityGroup.proto";
import "loadBalancer.proto";
import "eip.proto";
import "domain.proto";
import "ip.proto";
import "ram.proto";
import "initResource.proto";
import "ddos.proto";
import "bandwidthPackage.proto";
import "api.proto";
package cloudman;

option go_package = "cloudman";

service OpCloudmanTakumi {
  rpc OpCloudmanTakumiInitStatus(Empty) returns (Result) {
    option (api.get) = "/api/v2/init/status";
  };
}

// Account
service Account {
  rpc AccountList(accountQueryReq) returns (accountListRes) {
    option (api.get) = "/api/v1/account";
  };
  rpc AccountListAll(accountQueryReq) returns (accountListRes) {
    option (api.get) = "/api/v1/all/account";
  };
  rpc AccountCreate(createAccountRequest) returns (Result) {
    option (api.post) = "/api/v1/account";
  };
  rpc AccountGetAccountRegion(updateAccountRequest) returns (regionResult) {
    option (api.post) = "/api/v1/refresh-region";
  };
  rpc AccountUpdateStatus(updateAccountStatusReq) returns(Result) {
    option (api.put) = "/api/v1/account/:id/status";
  };
  rpc AccountRetrieve(ObjectID) returns(accountDetail) {
    option (api.get) = "/api/v1/account/:id";
  };
  rpc AccountUpdate(updateAccountRequest) returns(Result) {
    option (api.put) = "/api/v1/account/:id";
  };
  rpc AccountDestroy(ObjectID) returns(Result) {
    option (api.delete) = "/api/v1/account/:id";
  };
  rpc AccountGetBindRegion(ObjectID) returns(regionListResponse) {
    option (api.get) = "/api/v1/account/:id/bind-region";
  };
  rpc AccountTestRawPing(updateAccountRequest) returns(pingResult) {
    option (api.post) = "/api/v1/account-ping";
  };
  rpc AccountPing(ObjectID) returns(pingResult) {
    option (api.post) = "/api/v1/account/:id/ping";
  };
  rpc AccountSync(ObjectID) returns(Result) {
    option (api.post) = "/api/v1/account/:id/sync";
  };
  rpc AccountCreateParam(accountParamInfo) returns(Result) {
    option (api.post) = "/api/v2/account/:accountId/params";
  };
  rpc AccountParamInfo(ObjectID) returns(accountParamInfo) {
    option (api.get) = "/api/v2/account/:accountId/params/:id";
  };
  rpc AccountListParam(listAccountParamReq) returns(listAccountParamResp) {
    option (api.get) = "/api/v2/account/:accountId/params";
  };
  rpc AccountUpdateParam(accountParamInfo) returns(Result) {
    option (api.put) = "/api/v2/account/:accountId/params/:id";
  };
  rpc AccountDelParam(ObjectID) returns(Result) {
    option (api.delete) = "/api/v2/account/:accountId/params/:id";
  };
}

// CloudAgent
service CloudAgent {
  rpc CloudAgentTaskList(cloudAgentTaskListReq) returns(cloudAgentTaskListResp) {
    option (api.post) = "/api/v1/cloud_agent/task/list";
  };
  rpc CloudAgentTaskDetail(IDReq) returns(cloudAgentTaskDetailResp) {
    option (api.get) = "/api/v1/cloud_agent/task/:id/detail";
  };
  rpc CloudAgentResChangelist(resChangelistReq) returns(resChangelistResp) {
    option (api.post) = "/api/v1/res_changelist/query";
  };
}

// inner
service Inner {
  rpc InnerRunTaskAPI(runTaskReq) returns(runTaskResponse) {
    option (api.post) = "/api/v1/inner";
  };
}

// 资源回收站--------------------
service Recycle {
  rpc RecycleList(recycleQueryReq) returns (recycleListResp) {
    option (api.get) = "/api/v2/resource/recycle";
  };
  rpc RecycleInfo(ObjectID) returns (recycleInfo) {
    option (api.get) = "/api/v2/resource/recycle/:id";
  };
  rpc RecycleRecover(RecoverReq) returns (CommonResourceResp) {
    option (api.post) = "/api/v2/resource/recycle/:id/recover";
  };
  rpc BatchRecover(ObjectIDs) returns (Result);
  rpc RecycleDestroy(RecycleReq) returns (Result) {
    option (api.put) = "/api/v2/resource/recycle/:id/destroy";
  };
  rpc RecycleCreatePolicy(recyclePolicy) returns (Result) {
    option (api.post) = "/api/v2/resource/recycle-policy";
  };
  rpc RecycleGetPolicy(ObjectID) returns (recyclePolicy) {
    option (api.get) = "/api/v2/resource/recycle-policy/:id";
  };
  rpc RecycleGetPolicyList(recyclePolicyReq) returns(recyclePolicyResp) {
    option (api.get) = "/api/v2/resource/recycle-policy";
  };
  rpc RecycleChangePolicy(recyclePolicy) returns(Result) {
    option (api.put) = "/api/v2/resource/recycle-policy/:id";
  };
  rpc RecycleDelPolicy(ObjectID) returns(Result) {
    option (api.delete) = "/api/v2/resource/recycle-policy/:id";
  };

  rpc RecycleRunPolicy(Empty) returns(Result) {
    option (api.post) = "/api/v2/resource/run-policy";
  };
}

// 资源组-----------------
service ResourceGroup {
  rpc ResourceGroupList(groupQueryReq) returns (groupListResp) {
    option (api.get) = "/api/v2/resource/group";
  };
  rpc ResourceGroupCreate(groupCreateReq) returns (Result) {
    option (api.post) = "/api/v2/resource/group";
  };
  rpc ResourceGroupInfo(ObjectID) returns (groupInfo) {
    option (api.get) = "/api/v2/resource/group/:id";
  };
  rpc ResourceGroupUpdate(groupCreateReq) returns (Result) {
    option (api.put) = "/api/v2/resource/group/:id";
  };
  rpc ResourceGroupDel(ObjectID) returns (Result) {
    option (api.delete) = "/api/v2/resource/group/:id";
  };
  rpc ResourceGroupListCloud(listCloudReq) returns (listCloudResp) {
    option (api.get) = "/api/v2/resource/cloud_group";
  };
}

// DashBoard
service DashBoard {
  rpc DashBoardGetAccount(dashBoardAccountRequest) returns (dashBoardAccountResponse) {
    option (api.get) = "/api/v1/dashboard/account";
  };
  rpc DashBoardGetUserAuth(dashBoardUserAuthRequest) returns (dashBoardUserAuthResponse) {
    option (api.get) = "/api/v1/dashboard/userauth";
  };
  rpc DashBoardGetIfWorkOrderUsed(Empty) returns (dashboardIfWorkOrderUsedResponse) {
    option (api.get) = "/api/v1/dashboard/ifworkorderused";
  };
  rpc DashBoardGetBackendVersion(Empty) returns(dashboardBackendVersionResponse) {
    option (api.get) = "/api/v1/dashboard/backendVersion";
  };
  rpc DashBoardGetHostBrief(dashboardHostBriefReq) returns(dashboardHostBriefResp) {
    option (api.post) = "/api/v1/dashboard/host_brief";
  };
  rpc DashBoardGetResBrief(dashboardResBriefReq) returns(dashboardResBriefResp) {
    option (api.post) = "/api/v1/dashboard/res_brief";
  };
  rpc DashBoardGetHostDailyChart(dashboardHostDailyCountReq) returns(dashboardHostDailyCountResp) {
    option (api.post) = "/api/v1/dashboard/host_daily_chart";
  };
  rpc DashBoardGetResDailyChart(dashboardResDailyCountReq) returns(dashboardResDailyCountResp) {
    option (api.post) = "/api/v1/dashboard/res_daily_chart";
  };
  rpc DashBoardAutoFillSnapshot(autoFillSnapshotReq) returns(Result) {
    option (api.post) = "/api/v1/dashboard/auto_fill_snapshot";
  };
  rpc DashBoardFillSnapshot(fillSnapshotReq) returns(fillSnapshotRes) {
    option (api.post) = "/api/v1/dashboard/fill_snapshot";
  };
}

// 资源组分组策略---------------
service ResGroupPolicy {
  rpc ResGroupPolicyInfo(groupPolicyInfoReq) returns (resGroupPolicyInfo) {
    option (api.get) = "/api/v2/resource/group-policy/:id";
  };
  rpc ResGroupPolicyUpdate(resGroupPolicyCreateReq) returns (Result) {
    option (api.put) = "/api/v2/resource/group-policy/:id";
  };
  rpc ResGroupPolicyFindResource(findResReq) returns(findResResp) {
    option (api.post) = "/api/v2/resource/group-policy-preview/query";
  };
  rpc ResGroupPolicyCountResource(countResReq) returns(countResResp) {
    option (api.get) = "/api/v2/resource/group-policy-preview/count";
  };
}

// 标签管理--------------
service Tags {
  rpc TagsList(tagQueryReq) returns (tagListResp) {
    option (api.get) = "/api/v2/resource/tags";
  };
  rpc TagsListSystem(tagQueryReq) returns (tagListResp) {
    option (api.get) = "/api/v2/resource/system/tags";
  };
  rpc TagsCreate(tagCreateReq) returns (Result) {
    option (api.post) = "/api/v2/resource/tags";
  };
  rpc TagsInfo(ObjectID) returns (tagInfo) {
    option (api.get) = "/api/v2/resource/tags/:id";
  };
  rpc TagsUpdate(tagCreateReq) returns (Result) {
    option (api.put) = "/api/v2/resource/tags/:id";
  };
  rpc TagsDel(ObjectID) returns (Result) {
    option (api.delete) = "/api/v2/resource/tags/:id";
  };
}

// 主机资源类信息
service HostResTemplate {
  rpc HostResTemplateOption(Empty) returns(OptionResp) {
    option (api.get) = "/api/v2/resource/host/option";
  };
  rpc HostResTemplateDescribe(HostResReq) returns(HostResResp) {
    option (api.post) = "/api/v2/resource/host";
  };
  rpc HostResTemplateDescribeOpsStatus(Ids) returns(HostOpsStatusResp) {
    option (api.post) = "/api/v2/resource/host-ops-status";
  };
  rpc HostResTemplateLockResource(Ids) returns (Result) {
    option (api.post) = "/api/v2/resource/host-lock";
  };
  rpc HostResTemplateUnlockResource(Ids) returns (Result) {
    option (api.delete) = "/api/v2/resource/host-lock";
  };
  rpc HostResTemplateExportXlsx(ExportXlsxReq) returns(ExportXlsxResp) {
    option (api.post) = "/api/v2/resource/host-export";
  };
  rpc HostResTemplateImportXlsx(ImportXlsxReq) returns(BatchResult) {
    option (api.post) = "/api/v2/resource/host-import";
  };
  rpc HostResTemplateInputResource(HostResDetail) returns(Result) {
    option (api.post) = "/api/v2/resource/host-create";
  };
  rpc HostResTemplateGetHostInfo(ObjectID) returns(HostResDetail) {
    option (api.get) = "/api/v2/resource/host-info/:id";
  };
  rpc HostResTemplateGetHostDiskInfo(ObjectID) returns(HostDiskInfoResp) {
    option (api.get) = "/api/v2/resource/host-info/:id/disk";
  };
  rpc HostResTemplateGetCPUMonitor(MonitorReq) returns(MonitorResp) {
    option (api.post) = "/api/v2/resource/host-monitor/cpu";
  };
  rpc HostResTemplateGetMemMonitor(MonitorReq) returns(MonitorResp) {
    option (api.post) = "/api/v2/resource/host-monitor/mem";
  };
  rpc HostResTemplateGetIOMonitor(MonitorReq) returns(MonitorResp) {
    option (api.post) = "/api/v2/resource/host-monitor/io";
  };
  rpc HostResTemplateGetNetMonitor(MonitorReq) returns(MonitorResp) {
    option (api.post) = "/api/v2/resource/host-monitor/net";
  };
  rpc HostResTemplateGetDiskMonitor(MonitorReq) returns(MonitorResp) {
    option (api.post) = "/api/v2/resource/host-monitor/disk";
  };
  rpc HostResTemplateGetUPTimeMonitor(MonitorReq) returns(MonitorResp) {
    option (api.post) = "/api/v2/resource/host-monitor/uptime";
  };
  rpc HostResTemplateGetGraphView(MonitorReq) returns(GraphView) {
    option (api.post) = "/api/v2/resource/host-monitor/view";
  };
  rpc HostResTemplateGetGaiaMonitor(GaiaMonitReq) returns(GaiaMonitResp) {
    option (api.post) = "/api/v2/resource/host-monitor/gaia";
  };
  rpc HostResTemplateGetGaiaView(GaiaViewReq) returns (GraphView) {
    option (api.post) = "/api/v2/resource/host-monitor/gaia-view";
  };

  rpc HostResTemplateGetZone(ZoneReq) returns(ZoneRespList) {
    option (api.get) = "/api/v2/resource/template-hostinfo/zone/:rid";
  };
  rpc HostResTemplateGetKeyPairs(KeyPairReq) returns(KeyPairResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/keypairs/:rid";
  };
  rpc HostResTemplateGetSecurityGroup(SecurityGroupReq) returns(SecurityGroupResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/security-group/:rid";
  };
  rpc HostResTemplateGetNetwork(NetworkReq) returns(NetworkResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/network/:rid";
  };
  rpc HostResTemplateGetVSwitch(VSwitchReq) returns(VSwitchResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/vswitch/:rid";
  };
  rpc HostResTemplateGetImages(ImagesReq) returns(ImagesResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/images";
  };
  rpc HostResTemplateGetInstanceTypes(InstanceTypeReq) returns(InstanceTypesResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/instance-types";
  };
  rpc HostResTemplateGetVolumeTypes(VolumeTypeReq) returns(VolumeTypesResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/volume-types/:rid";
  };
  rpc HostResTemplateListChargeType(ChargeTypeReq) returns(ChargeTypesResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/charge-types/:isp";
  };
  rpc HostResTemplateDescribeAutoSnapshotPolicyEX(AutoSnapshotPolicyEXReq) returns(AutoSnapshotPolicyEXResp) {
    option (api.get) = "/api/v2/resource/template-hostinfo/auto-snapshot-policyex/:isp_id";
  };
  rpc HostResTemplateGetExistOSNames(Empty) returns(OSNames) {
    option (api.get) = "/api/v2/resource/host/exist_os_names";
  };

  rpc HostResTemplateUpdateMonitorStatus(UpdateMonitorStatusReq) returns(Result) {
    option (api.post) = "/api/v2/resource/host-update/monitor-status";
  };
  rpc HostResTemplateQuery(CustomQuery) returns(HostResResp) {
    option (api.post) = "/api/v2/resource/host-query";
  };
  rpc HostResTemplateDashboard(HostDashBoardReq)returns(HostDashBoardResp){
    option (api.get) = "/api/v2/resource/host/dashboard";
  };
  rpc HostResTemplateGetRemoteToken(ObjectID) returns(Result) {
    option (api.get) = "/api/v2/resource/host/remote_token";
  };
  rpc HostResTemplateSyncHostInstances(SyncInstancesReq) returns(Result) {
    option (api.post) = "/api/v2/resource/host/sync";
  };

  rpc HostResTemplateAssignIpv6Address(AssignIpv6AddressReq) returns(Result) {
    option (api.post) = "/api/v2/resource/host/ipv6/assign";
  };
  rpc HostResTemplateUnassignIpv6Address(UnassignIpv6AddressesReq) returns(Result) {
    option (api.post) = "/api/v2/resource/host/ipv6/unassign";
  };
}

// ali-polarDB
service Mysql {
  rpc MysqlOption(Empty) returns(OptionResp) {
    option (api.get) = "/api/v2/resource/mysql/option";
  };
  rpc MysqlDescribeCluster(MysqlClusterReq) returns(MysqlClusterResp) {
    option (api.post) = "/api/v2/resource/mysql";
  };
  rpc MysqlLockResource(Ids) returns (Result) {
    option (api.post) = "/api/v2/resource/mysql-lock";
  };
  rpc MysqlUnlockResource(Ids) returns (Result) {
    option (api.delete) = "/api/v2/resource/mysql-lock";
  };
  rpc MysqlExportXlsx(ExportXlsxReq) returns(ExportXlsxResp) {
    option (api.post) = "/api/v2/resource/mysql-export";
  };
  rpc MysqlImportXlsx(ImportXlsxReq) returns(BatchResult) {
    option (api.post) = "/api/v2/resource/mysql-import";
  };
  rpc MysqlClusterInfo(ObjectID) returns(MysqlClusterDetail) {
    option (api.get) = "/api/v2/resource/mysql-info/:id";
  };
  rpc MysqlGetDatabases(ObjectID) returns(MysqlDatabases) {
    option (api.get) = "/api/v2/resource/mysql-info-database/:id";
  };
  rpc MysqlGetAccounts(ObjectID) returns(MysqlAccountsInfo) {
    option (api.get) = "/api/v2/resource/mysql-info-account/:id";
  };
  rpc MysqlGetWhitelists(ObjectID) returns(WhitelistInfo) {
    option (api.get) = "/api/v2/resource/mysql-info-whitelist/:id";
  };
  rpc MysqlGetEndpoints(ObjectID) returns(MysqlEndpoints) {
    option (api.get) = "/api/v2/resource/mysql-info-endpoint/:id";
  };
  rpc MysqlGetDBTypes(DBTypeReq) returns(DBTypeResp) {
    option (api.get) = "/api/v2/resource/mysql-ref/db_type/:rid";
  };
  rpc MysqlDescribeAvailableZone(DBZoneReq) returns(DBZoneResp) {
    option (api.get) = "/api/v2/resource/mysql-ref/available_zone/:rid";
  };
  rpc MysqlDescribeDBClasses(DBClassesReq) returns(DBClassesResp) {
    option (api.get) = "/api/v2/resource/mysql-ref/classes/:rid";
  };
  rpc MysqlGetSecurityGroup(SecurityGroupReq) returns(SecurityGroupResp) {
    option (api.get) = "/api/v2/resource/mysql-network/security-group/:rid";
  };
  rpc MysqlGetNetwork(NetworkReq) returns(NetworkResp) {
    option (api.get) = "/api/v2/resource/mysql-network/network/:rid";
  };
  rpc MysqlGetVSwitch(VSwitchReq) returns(VSwitchResp) {
    option (api.get) = "/api/v2/resource/mysql-network/vswitch/:rid";
  };
  rpc MysqlSyncMysqlInstances(SyncInstancesReq) returns(Result) {
    option (api.post) = "/api/v2/resource/mysql/sync";
  };
  rpc MysqlDescribeParamsGroups(DBParamsGroupsReq)returns(DBParamsGroupsResp) {
    option (api.post) = "/api/v2/resource/mysql/parameter-groups/:isp_id/:rid";
  };
  rpc MysqlDescribeIPWhiteList(IPWhiteListReq) returns(IPWhiteListResp) {
    option (api.post) = "/api/v2/resource/mysql-network/ip-whitelist/:rid";
  };
  rpc MysqlUploadBackupSetToOSS(UploadBackupSetToOSSReq) returns(UploadBackupSetToOSSResp) {
    option (api.post) = "/api/v2/resource/mysql/backup/upload";
  };
}

// ali-redis
service Redis {
  rpc RedisOption(Empty) returns(OptionResp) {
    option (api.get) = "/api/v2/resource/redis/option";
  };
  rpc RedisDescribe(RedisReq) returns(RedisResp) {
    option (api.post) = "/api/v2/resource/redis";
  };
  rpc RedisLockResource(Ids) returns (Result) {
    option (api.post) = "/api/v2/resource/redis-lock";
  };
  rpc RedisUnlockResource(Ids) returns (Result) {
    option (api.delete) = "/api/v2/resource/redis-lock";
  };
  rpc RedisExportXlsx(ExportXlsxReq) returns(ExportXlsxResp) {
    option (api.post) = "/api/v2/resource/redis-export";
  };
  rpc RedisImportXlsx(ImportXlsxReq) returns(BatchResult) {
    option (api.post) = "/api/v2/resource/redis-import";
  };
  rpc RedisInfo(ObjectID) returns(RedisDetail) {
    option (api.get) = "/api/v2/resource/redis-info/:id";
  };
  rpc RedisGetAccounts(ObjectID) returns(RedisAccounts) {
    option (api.get) = "/api/v2/resource/redis-info-account/:id";
  };
  rpc RedisGetWhitelists(ObjectID) returns(WhitelistInfo) {
    option (api.get) = "/api/v2/resource/redis-info-whitelist/:id";
  };

  rpc RedisGetCacheTypes(DBTypeReq) returns(DBTypeResp) {
    option (api.get) = "/api/v2/resource/redis-ref/type/:rid";
  };
  rpc RedisDescribeAvailableZone(DBZoneReq) returns(DBZoneResp) {
    option (api.get) = "/api/v2/resource/redis-ref/available_zone/:rid";
  };
  rpc RedisDescribeCacheClasses(CacheClassReq) returns(CacheClassesResp) {
    option (api.get) = "/api/v2/resource/redis-ref/classes/:rid";
  };
  rpc RedisDescribeParamsGroups(CacheParamsGroupsReq) returns(CacheParamsGroupsResp) {
    option (api.get) = "/api/v2/resource/redis-ref/parameter-groups/:isp_id/:rid";
  };
  rpc RedisGetSecurityGroup(SecurityGroupReq) returns(SecurityGroupResp) {
    option (api.get) = "/api/v2/resource/redis-network/security-group/:rid";
  };
  rpc RedisGetNetwork(NetworkReq) returns(NetworkResp) {
    option (api.get) = "/api/v2/resource/redis-network/network/:rid";
  };
  rpc RedisGetVSwitch(VSwitchReq) returns(VSwitchResp) {
    option (api.get) = "/api/v2/resource/redis-network/vswitch/:rid";
  };
  rpc RedisSyncRedisInstances(SyncInstancesReq) returns(Result) {
    option (api.post) = "/api/v2/resource/redis/sync";
  };

  rpc RedisGetBackupDetail(OrderID) returns(GetBackupDetailResp) {
    option (api.get) = "/api/v2/resource/redis/backup/:order_id";
  };
}

service SecurityGroup {
  rpc SecurityGroupDescribe(DescribeSecurityGroupReq) returns (DescribeSecurityGroupRes) {
    option (api.post) = "/api/v2/resource/securitygroup";
  };
  rpc SecurityGroupDescribeByInstances(DescribeSecurityGroupByInstancesReq) returns (DescribeSecurityGroupRes) {
    option (api.post) = "/api/v2/resource/securitygroup/instances";
  };
  rpc SecurityGroupDescribeRules(DescribeRulesReq) returns(DescribeSecurityGroupRes) {
    option (api.post) = "/api/v2/resource/securitygroup/rules";
  };
  rpc SecurityGroupUpdateSecurityGroupRule(UpdateSecurityGroupRuleReq) returns (Result) {
    option (api.put) = "/api/v2/resource/securitygroup/rule";
  };
  rpc SecurityGroupGetAccountRegionTags(GetAccountRegionTagsReq) returns(GetAccountRegionTagsRes) {
    option (api.get) = "/api/v2/resource/securitygroup/custom-tags";
  };
  rpc SecurityGroupGetCustomTagSecurityGroups(GetCustomTagSecurityGroupsReq) returns(GetCustomTagSecurityGroupsRes) {
    option (api.get) = "/api/v2/resource/securitygroup/custom-tags-sg";
  };
  rpc SecurityGroupUpdateSecurityGroupCustomTag(UpdateSecurityGroupCustomTagReq) returns(Result) {
    option (api.post) = "/api/v2/resource/securitygroup/custom-tags/update";
  };
  rpc SecurityGroupUpdateSecurityGroups(UpdateSecurityGroupsReq) returns(Result) {
    option (api.post) = "/api/v2/resource/securitygroup/update";
  };
  rpc SecurityGroupCleanupSecurityGroups(UpdateSecurityGroupCustomTagReq) returns(Result) {
    option (api.post) = "/api/v2/resource/securitygroup/cleanup";
  };
  rpc SecurityGroupExportSecurityGroup(ExportSecurityGroupReq) returns(ExportXlsxResp) {
    option (api.post) = "/api/v2/resource/securitygroup/export";
  };
  rpc SecurityGroupExportSecurityGroupRule(ExportSecurityGroupRuleReq) returns(ExportXlsxResp) {
    option (api.post) = "/api/v2/resource/securitygroup/rule/export";
  };
  rpc SecurityGroupExportRelateInstances(ExportRelateInstancesReq) returns(ExportXlsxResp) {
    option (api.post) = "/api/v2/resource/securitygroup/relate-instances/export";
  };
  rpc SecurityGroupBatchDeleteSecurityGroupRule(BatchDeleteSecurityGroupRuleReq) returns (Result) {
    option (api.post) = "/api/v2/resource/securitygroup/rule/batch-delete";
  };
  rpc SecurityGroupCreateSecurityGroupRule(CreateSecurityGroupRuleReq) returns (Result) {
    option (api.post) = "/api/v2/resource/securitygroup/rule";
  };

  rpc SecurityGroupDescribeIPWhitelists(DescribeIPWhitelistsReq) returns (DescribeIPWhitelistsResp) {
    option (api.get) = "/api/v2/resource/securitygroup/whitelists";
  };
  rpc SecurityGroupModifyIPWhitelists(ModifyIPWhitelistsReq) returns (Result) {
    option (api.put) = "/api/v2/resource/securitygroup/whitelist/:security_group_id";
  };

  rpc SecurityGroupDescribeALBAcls(DescribeALBAclsReq) returns (DescribeALBAclsResp) {
    option (api.get) = "/api/v2/resource/securitygroup/alb_acls";
  };
  rpc SecurityGroupAddALBAclsEntries(AddALBAclsEntriesReq) returns (Result) {
    option (api.post) = "/api/v2/resource/securitygroup/alb_acl/:acl_id/entry";
  };
  rpc SecurityGroupRemoveALBAclsEntries(RemoveALBAclsEntriesReq) returns (Result) {
    option (api.post) = "/api/v2/resource/securitygroup/alb_acl/:acl_id/entry/batch-delete";
  };

  rpc SecurityGroupJoinSecurityGroup(JoinOrLeaveSecurityGroupReq) returns (ModifySecurityGroupResult) {
    option (api.post) = "/api/v2/resource/securitygroup/join";
  };
  rpc SecurityGroupLeaveSecurityGroup(JoinOrLeaveSecurityGroupReq) returns (ModifySecurityGroupResult) {
    option (api.post) = "/api/v2/resource/securitygroup/leave";
  };
}

service LoadBalancer {
  rpc LoadBalancerDescribe(DescribeLoadBalancerReq) returns (DescribeLoadBalancerRes) {
    option (api.post) = "/api/v2/resource/loadbalancer";
  };
  rpc LoadBalancerDescribeDetail(ObjIDReq) returns (DescribeLoadBalancerDetailRes) {
    option (api.get) = "/api/v2/resource/loadbalancer/:id";
  };
  rpc LoadBalancerDescribeServerGroup(IDReq) returns (DescribeLoadBalancerServerGroupRes) {
    option (api.get) = "/api/v2/resource/loadbalancer-server-groups/:id";
  };
  rpc LoadBalancerDescribeZones(DescribeZonesReq) returns (DescribeZonesRes) {
    option (api.post) = "/api/v2/resource/loadbalancer-network/zones";
  };
  rpc LoadBalancerListACLs(ListACLsReq) returns (ListAClsRes) {
    option (api.post) = "/api/v2/resource/loadbalancer-network/acls";
  };
  rpc LoadBalancerListCertificates(ListCertificatesReq) returns (ListCertificatesRes) {
    option (api.post) = "/api/v2/resource/loadbalancer-network/certs";
  };
  rpc LoadBalancerCleanupLoadBalancer(LoadBalancerIDsReq) returns (Result) {
    option (api.post) = "/api/v2/resource/loadbalancer/cleanup";
  };
}

service Eip {
  rpc EipDescribe(DescribeEipReq) returns (DescribeEipRes) {
    option (api.post) = "/api/v2/resource/eip";
  };
  rpc EipDescribeSegment(DescribeEipSegmentReq) returns (DescribeEipSegmentRes) {
    option (api.post) = "/api/v2/resource/eip/segment";
  };
  rpc EipDescribeIpam(DescribeIpamReq) returns (DescribeIpamRes) {
    option (api.post) = "/api/v2/resource/eip/ipam";
  };
}

service IP {
  rpc IPDescribe(DescribeIPReq) returns (DescribeIPRes) {
    option (api.post) = "/api/v2/resource/ip";
  };
  rpc IPCreateCustomIP(IPEntity) returns (Result) {
    option (api.post) = "/api/v2/resource/ip/create";
  };
  rpc IPDeleteIP(DeleteIPReq) returns (Result) {
    option (api.delete) = "/api/v2/resource/ip/:id";
  };
  rpc IPDescribeByIPs(DescribeByIPsReq) returns (DescribeByIPsRes) {
    option (api.post) = "/api/v2/resource/ip/ips";
  };
  rpc IPDescribeIPGroup(DescribeIPGroupReq) returns (DescribeIPGroupResp) {
    option (api.get) = "/api/v2/resource/ip_groups";
  };
  rpc IPCreateIPGroup(CreateIPGroupReq) returns (Result) {
    option (api.post) = "/api/v2/resource/ip_group";
  };
  rpc IPModifyIPGroup(ModifyIPGroupReq) returns (Result) {
    option (api.put) = "/api/v2/resource/ip_group/:id";
  };
  rpc IPDeleteIPGroup(DeleteIPGroupReq) returns (Result) {
    option (api.delete) = "/api/v2/resource/ip_group/:id";
  };
}

service RAM {
  rpc RAMDescribeRAMPolicy(DescribeRAMPolicyReq) returns (DescribeRAMPolicyResp) {
    option (api.get) = "/api/v2/resource/rams";
  };
  rpc RAMModifyRAMPolicySourceIPGroup(ModifyRAMPolicySourceIPGroupReq) returns (Result) {
    option (api.put) = "/api/v2/resource/ram/:id/ip_group";
  };

  rpc RAMSyncRAMPolicy(SyncRamPolicyReq) returns (Result) {
    option (api.post) = "/api/v2/resource/ram/sync";
  };
  rpc RAMPreviewRAMPolicyDeploy(PreviewRAMPolicyDeployReq) returns (PreviewRAMPolicyDeployResp) {
    option (api.post) = "/api/v2/resource/ram/deploy/preview";
  };
  rpc RAMRAMPolicyDeploy(RAMPolicyDeployReq) returns (Result) {
    option (api.post) = "/api/v2/resource/ram/deploy";
  };

  rpc RAMSyncNewRAMPolicy(SyncNewRAMPolicyReq) returns (Result) {
    option (api.post) = "/api/v2/resource/ram/new/sync";
  };
  rpc RAMGetCloudRamPolicy(GetCloudRamPolicyReq) returns (GetCloudRamPolicyResp) {
    option (api.get) = "/api/v2/resource/ram/cloud";
  };
}

service Domain {
  rpc DomainDescribe(DescribeDomainReq) returns (DescribeDomainRes) {
    option (api.post) = "/api/v2/resource/domain";
  };
  rpc DomainDescribeRecords(DescribeDomainRecordsReq) returns (DescribeDomainRes) {
    option (api.post) = "/api/v2/resource/domain/records";
  };
  rpc DomainSyncDnspod(Empty) returns(Result) {
    option (api.post) = "/api/v2/resource/domain/sync-dnspod";
  };
  rpc DomainCreateDomain(CreateDomainReq) returns(Result) {
    option (api.post) = "/api/v2/resource/domain/create";
  };
}


// task
service Task {
  rpc TaskList(taskListReq) returns(taskListResp) {
    option (api.get) = "/api/v1/task";
  };
  rpc TaskRetrieve(runTaskReq) returns(taskDetail) {
    option (api.get) = "/api/v1/task/:id";
  };
  rpc TaskCreate(createTaskReq) returns(Result) {
    option (api.post) = "/api/v1/task";
  };
  rpc TaskRunTaskAPI(runTaskReq) returns(Result) {
    option (api.post) = "/api/v1/task/:id/run";
  };
  rpc TaskUpdate(updateTaskReq) returns(Result) {
    option (api.put) = "/api/v1/task/:id";
  };
  rpc TaskDelete(ObjectID) returns(Result) {
    option (api.delete) = "/api/v1/task/:id";
  };
}

// task log
service TaskLog {
  rpc TaskLogListTaskLog(TaskLogQueryParams) returns(TaskLogListResponse) {
    option (api.get) = "/api/v1/log";
  };
  rpc TaskLogRetrieveTaskLog(OrderLogReq) returns(TaskLogResultList) {
    option (api.get) = "/api/v1/log/:id";
  };
}

// Region
service Region {
  rpc RegionList(regionQueryRequest) returns (regionListResponse) {
    option (api.get) = "/api/v1/region";
  };
  rpc RegionInfo(ObjectID) returns (regionDetail) {
    option (api.get) = "/api/v1/region/:id";
  };
  rpc RegionCreate(regionDetail) returns (Result) {
    option (api.post) = "/api/v1/region";
  };
}


// 资源组模板-----------------
service ResTemplate {
  rpc ResTemplateList(resTemQueryReq) returns (resTemListResp) {
    option (api.get) = "/api/v2/resource/template";
  };
  rpc ResTemplateCreate(resTemCreateReq) returns (Result) {
    option (api.post) = "/api/v2/resource/template";
  };
  rpc ResTemplateInfo(ObjectID) returns (resTemCreateReq) {
    option (api.get) = "/api/v2/resource/template/:id";
  };
  rpc ResTemplateUpdate(resTemCreateReq) returns (Result) {
    option (api.put) = "/api/v2/resource/template/:id";
  };
  rpc ResTemplateDel(ObjectID) returns (Result) {
    option (api.delete) = "/api/v2/resource/template/:id";
  };

  rpc ResTemplateCreateResource(CreateResReq) returns(CommonResourceResp) {
    option (api.post) = "/api/v2/resource/create";
  };
  rpc ResTemplateUpdateResource(ResCommonReq) returns(CommonResourceResp) {
    option (api.post) = "/api/v2/resource/update";
  };
  rpc ResTemplateUpdateResources(UpdateResReq) returns(UpdateResourcesResp) {
    option (api.post) = "/api/v2/resource/bulk-update";
  };
  rpc ResTemplateRunOrder(RunOrderReq) returns(Result) {
    option (api.post) = "/api/v2/resource/run/:id";
  };
  rpc ResTemplateRunOrderRetry(RunOrderRetryReq) returns(Result) {
    option (api.post) = "/api/v2/resource/run/:id/retry";
  };
  rpc ResTemplateGetSalePrice(resTemCreateReq) returns(SalePrice) {
    option (api.post) = "/api/v2/resource/order-price";
  };
  rpc ResTemplateCheckInstanceNameSeq(CheckInstanceNameSeqReq) returns(CheckInstanceNameSeqResp) {
    option (api.post) = "/api/v2/resource/check_instancename_seq";
  };
  rpc ResTemplateGenInstanceNameWithRule(GenInstanceNameWithRuleReq) returns (GenInstanceNameWithRuleResp) {
    option (api.post) = "/api/v2/resource/gen_instancename_with_rule";
  };
}

// 操作与审计---------------
service ResAudit {
  rpc ResAuditGetOrderList(OrderTaskReq) returns(OrderTaskListResp) {
    option (api.post) = "/api/v2/audit/order";
  };
  rpc ResAuditGetOrderLog(OrderLogReq) returns(TaskLogResultList) {
    option (api.get) = "/api/v2/audit/order-log/:id";
  };
  rpc ResAuditGetOrder(IDReq) returns(OrderTaskListResult) {
    option (api.get) = "/api/v2/audit/order/:id";
  };
}

// 云管网关
service CloudGateway {
  rpc CloudGatewayCreateGateway(create_gateway_req) returns (ObjectID) {
    option (api.post) = "/api/v2/gateway";
  };
  rpc CloudGatewayGetGatewayScript(create_gateway_req) returns(script) {
    option (api.post) = "/api/v2/gateway/script";
  };
  rpc CreateAgent(create_gateway_req) returns (ObjectID);
  rpc CloudGatewayGettopo(Empty) returns(topo_result) {
    option (api.get) = "/api/v2/gateway-topo";
  };
  rpc CloudGatewayGetGatewayTopo(gatewayTopoReq) returns(gatewayTopoResult) {
    option (api.post) = "/api/v2/gateway/topo";
  };
  rpc CloudGatewayListGateway(Empty) returns(list_gateway_resp) {
    option (api.get) = "/api/v2/gateway";
  };
  rpc CreateAgentScript(create_agent_req) returns(ObjectID);
  rpc CloudGatewayGetAgentScript(create_agent_req) returns(script) {
    option (api.post) = "/api/v2/gateway/agent/script";
  };
  rpc CloudGatewayInsertGateAddress(gateway_address_req) returns(Result) {
    option (api.post) = "/api/v2/gateway/config";
  };
  rpc CloudGatewayGetGateAddress(Empty) returns(gateway_address_req) {
    option (api.get) = "/api/v2/gateway/config";
  };

  rpc CloudGatewayChangeAgent(agent_callback_req) returns(Result) {
    option (api.post) = "/api/v2/gateway/agent";
  };
  rpc CloudGatewayChangeAgents(Raw) returns(Result) {
    option (api.post) = "/api/v2/gateway/agents";
  };
  rpc CloudGatewayChangeAgentStatus(ChangeAgentStatusReq) returns(Result) {
    option (api.post) = "/api/v2/gateway/agent/status";
  };
  rpc CloudGatewayDeleteAgent(ChangeAgentStatusReq) returns(Result) {
    option (api.post) = "/api/v2/gateway/agent/delete";
  };
  rpc CloudGatewayListAgent(list_agent_req) returns(list_agent_resp) {
    option (api.get) = "/api/v2/gateway/agent";
  };
  rpc CloudGatewayRawAgentInfo(ObjectID) returns(Result) {
    option (api.get) = "/api/v2/gateway/agent/info";
  };
  rpc CloudGatewayExportXlsx(list_agent_req) returns(ExportXlsxResp) {
    option (api.post) = "/api/v2/gateway/agent/export";
  };

  rpc CloudGatewayListAgentInstallConfig(Empty) returns(list_agent_install_config_resp) {
    option (api.get) = "/api/v2/gateway/install_config";
  };
  rpc CloudGatewayUpdateAgentInstallConfig(agent_install_config) returns(Result) {
    option (api.post) = "/api/v2/gateway/install_config";
  };

  rpc CloudGatewayReinstallAgent(ReinstallAgentReq) returns(Result) {
    option (api.post) = "/api/v2/gateway/reinstall_agent";
  };
  rpc CloudGatewayUpdateAgent(UpdateAgentReq) returns(Result) {
    option (api.post) = "/api/v2/gateway/update_agent";
  };
  rpc CloudGatewayGetHostAgentID(get_agent_req) returns(AgentIDResp) {
    option (api.post) = "/api/v2/common/get_agent_id";
  };
  rpc CloudGatewayListAgentVersion(Empty) returns(ListAgentVersionResp) {
    option (api.get) = "/api/v2/gateway/list_version";
  };
}

service System {
  rpc SystemGetConfig(Empty) returns(SysConfig) {
    option (api.get) = "/api/v2/config";
  };
  rpc SystemWecomDailyNotify(Empty) returns(Empty) {
    option (api.get) = "/api/v2/wecom_daily_notify";
  };
  rpc SystemSecurityGroupRuleDailyNotify(Empty) returns(Empty) {
    option (api.get) = "/api/v2/securitygroup_rule_daily_notify";
  };
}

service Cmdb {
  rpc CmdbQuery(cmdbQueryRequest) returns(cmdbQueryResponse) {
    option (api.post) = "/api/v2/cmdb/query";
  };
  rpc CmdbQueryAssociate(cmdbQueryAssociateRequest) returns(cmdbQueryResponse) {
    option (api.post) = "/api/v2/cmdb/query_associate";
  };
  rpc CmdbManualUpdate(cmdbUpdateRequest) returns(cmdbUpdateResponse) {
    option (api.post) = "/api/v2/cmdb/manual_update";
  };
  rpc CmdbDelete(cmdbDeleteRequest) returns(cmdbDeleteResponse) {
    option (api.post) = "/api/v2/cmdb/delete";
  };
  rpc CmdbPullInfo(cmdbPullInfoRequest) returns(Empty) {
    option (api.post) = "/api/v2/cmdb/pull_info";
  };
  rpc CmdbDiff(cmdbDiffRequest) returns(CommonResourceResp) {
    option (api.post) = "/api/v2/cmdb/diff";
  };
  rpc CmdbDiffProcess(cmdbDiffProcessRequest) returns(Empty) {
    option (api.post) = "/api/v2/cmdb/process";
  };
  rpc CmdbDiffDelete(cmdbDiffDeleteReq) returns(CommonResourceResp) {
    option (api.post) = "/api/v2/cmdb/diff/delete";
  };
  rpc CmdbDiffUpdate(cmdbDiffDeleteReq) returns(CommonResourceResp) {
    option (api.post) = "/api/v2/cmdb/diff/update";
  };
}

service ServiceTree {
  rpc ServiceTreeGetTree(Raw) returns(GetTreeResponse) {
    option (api.post) = "/api/v1/servicetree/get_tree";
  };
  rpc ServiceTreeGetEntity(Raw) returns(GetEntityResponse) {
    option (api.post) = "/api/v1/servicetree/get_entity";
  };
  rpc ServiceTreeNodeEntityCount(Raw) returns(NodeEntityCountResponse) {
    option (api.post) = "/api/v1/servicetree/node/entity_count";
  };
  rpc ServiceTreeAddNode(AddNodeRequest) returns(AddNodeResponse) {
    option (api.post) = "/api/v1/servicetree/add_node";
  };
  rpc ServiceTreeVerify(VerifyRequest) returns(VerifyResponse) {
    option (api.post) = "/api/v1/servicetree/verify";
  };
}

service Ddos {
    rpc DdosDescribeDdosBgp(DescribeDdosBgpReq) returns(DescribeDdosBgpRes) {
    option (api.get) = "/api/v2/resource/ddos/bgp";
  };
}

service BandwidthPackage {
  rpc BandwidthPackageDescribeCbwp(DescribeBandwidthPackageCbwpReq) returns(DescribeBandwidthPackageCbwpRes) {
    option (api.get) = "/api/v2/resource/bandwidth-package/cbwp";
  };
}

service Debug {
  rpc DebugFixResTagBind(Empty) returns(Result) {
    option (api.get) = "/api/v1/debug/fix-tag";
  };
  rpc DebugTest(Empty) returns(Result) {
    option (api.get) = "/api/v1/debug/test";
  };
}

service Permission {
  rpc PermissionCheckInstancePermission(CheckInstancePermissionReq) returns(CheckInstancePermissionResp) {
    option (api.post) = "/api/v1/perm/instance";
  };
}

// 初始化跑标准运维流程使用的接口
service InitResource {
  rpc InitResourceGetJumpserver(GetJumpserverReq) returns (GetJumpserverResp) {
    option (api.get) = "/api/v2/resource_init/jumpserver";
  };
  rpc InitResourceGetPipeline(GetPipelineReq) returns (GetPipelineResp) {
    option (api.get) = "/api/v2/resource_init/pipeline";
  };
  rpc InitResourcePreviewParam(PreviewParamReq) returns (PreviewParamResp) {
    option (api.post) = "/api/v2/resource_init/param/preview";
  };
}
