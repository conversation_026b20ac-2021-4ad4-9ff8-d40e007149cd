syntax = "proto3";

//import "google/protobuf/wrappers.proto";

package cloudman;

option go_package = "cloudman";

message IPEntity {
  string id = 1;
  string type = 2; // 区分网段还是IP地址
  string create_type = 3; // "sync" or "custom"
  string address = 4;
  string bind_instance_type = 5;
  string instance_id = 6;
  string desc = 7;
  string isp_id = 8; // 云厂商ID
  string isp_type = 9; // 云厂商类型
  string isp_name = 10;
  string region_id = 11;
  string created_time = 12;
  string updated_time = 13;
}

message IPAddressOperationLocks {
  string lock_reason = 1;
}

message DescribeIPReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
  string search_key = 5;
  string search_value = 6;
  string address = 7;
}

message DescribeIPRes {
  int32 total = 1;
  repeated IPEntity list = 2;
}

message IPMap {
  string address = 1;
  repeated IPEntity list = 2;
}

message DescribeByIPsReq {
  repeated string addresses = 1;
  string bind_instance_type = 2;
}

message DescribeByIPsRes {
  repeated IPMap ipMap = 1;
}

message DescribeIPGroupReq {
  uint64 page = 1;
  uint64 size = 2;
  string name = 3;
  string description = 4;
}

message DescribeIPGroupResp {
  int32 total = 1;
  repeated IPGroup list = 2;
}

message IPGroup {
  string id = 1;
  string name = 2;
  string description = 3;
  string created_time = 4;
  string updated_time = 5;
  string create_user = 6;
  string update_user = 7;
  repeated string ips = 8;
  repeated string ip_ids = 9;
}

message CreateIPGroupReq {
  string name = 1;
  string description = 2;
  repeated string ip_ids = 3;
}

message ModifyIPGroupReq {
  string id = 1;
  string description = 2;
  repeated string ip_ids = 3;
}

message DeleteIPGroupReq {
  string id = 1;
}

message DeleteIPReq {
  string id = 1;
}