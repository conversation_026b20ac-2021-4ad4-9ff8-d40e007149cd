syntax = "proto3";

//import "google/protobuf/wrappers.proto";

package cloudman;

option go_package = "cloudman";

import "resTemplate.proto";

message LoadBalancerEntity {
  string ID = 1;
  string Address = 2;
  string AddressIPVersion = 3;
  string AddressType = 4;
  int64 AutoReleaseTime = 5;
  repeated LoadBalancerBackendServer BackendServers = 6;
  int32 Bandwidth = 7;
  string CreateTime = 8;
  int64 CreateTimeStamp = 9;
  string DeleteProtection = 10;
  string EndTime = 11;
  int64 EndTimeStamp = 12;
  string InstanceChargeType = 13;
  string InternetChargeType = 14;
  repeated LoadBalancerListener Listeners = 15;
  string LoadBalancerID = 18;
  string LoadBalancerName = 19;
  string LoadBalancerSpec = 20;
  string LoadBalancerStatus = 21;
  string MasterZoneID = 22;
  string ModificationProtectionReason = 23;
  string ModificationProtectionStatus = 24;
  string NetworkType = 25;
  string PayType = 26;
  string RegionID = 27;
  string RegionIDAlias = 28;
  string RenewalCycUnit = 29;
  int32 RenewalDuration = 30;
  string RenewalStatus = 31;
  string ResourceGroupID = 33;
  string SlaveZoneID = 34;
  repeated resource_tag Tags = 35;
  string VSwitchID = 36;
  string VpcID = 37;
  string isp_id = 38;
  string isp_type = 39;
  string isp_name = 40;
  string LoadBalancerType = 41;
  bool NeedCleanup = 42;
  repeated string SecurityGroupIds = 43;
  string Ipv6AddressType = 44;
}

message LoadBalancerBackendServer {
  string Description = 1;
  string ServerID = 2;
  string ServerIP = 3;
  string Type = 4;
  int32 Weight = 5;
}

message LoadBalancerListener {
  string AclId = 1;
  string AclStatus = 2;
  string AclType = 3;
  int32 BackendServerPort = 4;
  int32 Bandwidth = 5;
  string Description = 6;
  int32 ListenerPort = 7;
  string ListenerProtocol = 8;
  string LoadBalancerId = 9;
  string Scheduler = 10;
  string Status = 11;
  string VServerGroupId = 12;
}

message LoadBalancerListenerPortsAndProtocal {
  int32 ListenerPort = 1;
  string ListenerProtocal = 2;
}

message LoadBalancerListenerPortAndProtocol {
  string Description = 1;
  int32 ForwardPort = 2;
  string ListenerForward = 3;
  int32 ListenerPort = 4;
  string ListenerProtocol = 5;
}

message DescribeLoadBalancerReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
  string search_key = 5;
  string search_value = 6;
  string address = 7;
  string lb_type = 8;
  bool need_cleanup = 9;
}

message DescribeLoadBalancerRes {
  int32 total = 1;
  repeated LoadBalancerEntity list = 2;
}

message DescribeLoadBalancerDetailRes {
  string LoadBalancerName = 1;
  string LoadBalancerID = 2;
  string VpcID = 3;
  string isp_id = 4;
  string isp_type = 5;
  string isp_name = 6;
  string LoadBalancerType = 7;
  string Address = 8;
  string RegionID = 9;
  repeated ALBListener Listeners = 10;
  repeated ZoneMappings ZoneMappings = 11;
}

message ZoneMappings {
  repeated LoadBalancerAddresses LoadBalancerAddresses = 1;
  string ZoneId = 2;
  string VSwitchId = 3;
}

message LoadBalancerAddresses {
  string Address = 1;
  string Ipv6Address = 2;
  string IntranetAddress = 3;
}

message ALBListener {
  string ListenerID = 1;
  string ListenerProtocol = 2;
  int32 ListenerPort = 3;
  string DefaultServerGroupID = 4;
  int32 IdleTimeout = 5;
  int32 RequestTimeout = 6;
  bool GzipEnabled = 7;
  string ListenerStatus = 8;
  string ListenerDescription = 9;
}

message DescribeLoadBalancerServerGroupRes {
  string ServerGroupID = 1;
  string ServerGroupName = 2;
  string Scheduler = 3;
  string CreateTime = 4;
  repeated LBServerGroupServer Servers = 5;
  string ServerGroupType = 6;
  string ServerGroupStatus = 7;
  string Protocol = 8;
}

message LBServerGroupServer {
  string ServerID = 1;
  string ServerIP = 2;
  string Type = 3;
  int32 Weight = 4;
  int32 Port = 5;
}

message DescribeLoadBalancerByInstancesReq {
  uint64 page = 1;
  uint64 size = 2;
  string instanceType = 3;
  repeated string instance_ids = 4;
}

message DescribeZonesReq {
  string isp = 1;
  string region_id = 2;
}

message DescribeZonesRes {
  int32 total = 1;
  repeated ALBZone list = 2;
}

message ALBZone {
  string local_name = 1;
  string zone_id = 2;
}

message ListACLsReq {
  string isp = 1;
  string region_id = 2;
}

message ListAClsRes {
  int32 total = 1;
  repeated ALBAcl list = 2;
}

message ALBAcl {
  string acl_id  = 1;
  string acl_name = 2;
} 

message ListCertificatesReq {
  string isp = 1;
  string region_id = 2;
}

message ListCertificatesRes {
  int32 total = 1;
  repeated Certificate list = 2;
}

message Certificate {
  string cert_identifier = 1;
  string cert_name = 2;
  string common_name = 3;
  string domain = 4;
  int64 after_date = 5;
  int64 before_date = 6;
}

message LoadBalancerIDsReq {
  repeated string ids = 1;
}