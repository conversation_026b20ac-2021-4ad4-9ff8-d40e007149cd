syntax = "proto3";
import "api.proto";

package cloudman;
option go_package = "cloudman";

message DescribeRAMPolicyReq {
  uint64 page = 1;
  uint64 size = 2;
  string isp = 3;
  string region_id = 4;
  string name = 5;
  string desc = 6;
  bool need_cleanup = 7;
}

message DescribeRAMPolicyResp {
  int32 total = 1;
  repeated RAMPolicy list = 2;
}

message RAMPolicy {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated AttachUser attach_users = 4;
  string policy_document = 5;
  string create_time = 6;
  string update_time = 7;
  repeated string related_ip_groups = 8;
  repeated string related_ip_groups_ids = 9;
  string region_id = 10;
  string isp_id = 11;
  string isp_type = 12;
  string isp_name = 13;
}

message AttachUser {
  string display_name = 1;
  string user_id = 2;
  string user_name = 3;
  string attach_date = 4;
}

message ModifyRAMPolicySourceIPGroupReq {
  string id = 1[(api.path)="id"];
  repeated string ip_group_ids = 2;
}

message PreviewRAMPolicyDeployReq {
  repeated string ids = 1;
  bool is_all = 2;
}

message PreviewRAMPolicyDeployResp {
  repeated PreviewRamPolicyDeployRes results = 1;
}

message PreviewRamPolicyDeployRes {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated AttachUser attach_users = 4;
  repeated string add_ips = 5;
  repeated string remove_ips = 6;
  string oldPolicyDocument = 7;
  string newPolicyDocument = 8;
}

message RAMPolicyDeployReq {
  repeated string ids = 1;
  bool is_all = 2;
}

message SyncRamPolicyReq {
  string region_id = 1;
  string isp_id = 2;
}

message SyncNewRAMPolicyReq {
  repeated string names = 1;
  string region_id = 2;
  string isp_id = 3;
}

message GetCloudRamPolicyReq {
  string region_id = 1;
  string isp_id = 2;
}

message GetCloudRamPolicyResp {
  repeated GetCloudRamPolicyRes list = 1;
}

message GetCloudRamPolicyRes {
  string name = 1;
  string description = 2;
}