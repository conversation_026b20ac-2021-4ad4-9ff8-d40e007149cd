syntax = "proto3";

//import "google/protobuf/wrappers.proto";
import "common.proto";
import "api.proto";

package cloudman;

option go_package = "cloudman";

message TaskLogQueryParams {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
}

message TaskLogListResponse {
  uint32 total = 1;
  repeated TaskLogDetail list = 2;
}

message TaskLogDetail {
  string name = 1;
  string sync_task_id = 2;
  string account_id = 3;
  string account_name = 4;
  string bind_region_id = 5;
  string bind_region_name = 6;
  string task_type = 7;
  uint32 status = 8;
  int64 end_time = 9;
  int64 created_time = 10;
  string id = 11;
  string account_type = 12;
  string create_user = 13;
}

message OrderTaskReq {
  uint64 page = 1;
  uint64 size = 2;
  repeated string ordering = 3;
  string keywords = 4;
  string name = 5;
  string isp = 6;
  string type = 7;
  string status = 8;
  timeFilter created_time = 9;
  string action = 10;
  string region_id = 11;
  string search_key = 12;
  string search_value = 13;
}

message OrderTaskListResp {
  int32 total = 1;
  repeated OrderTaskListResult list = 2;
}

message OrderTaskListResult {
  string id = 1;
  int64 created_time = 2;
  string create_user = 3;
  int32 status = 4;
  string isp_name = 5;
  string type = 6;
  string action = 7;
  string before_info = 8;
  string after_info = 9;
  string error_msg = 10;
  string isp_type = 11;
  string region_name = 12;
  string region_id = 13;
  int64 start_time = 14;
  int64 over_time = 15;
  string raw_data = 16;
  string reason = 17;
  string status_detail = 18;
  string ticket_url = 19;
  string isp_id = 20;
  string template_id = 21;
  string template_name = 22;
}

message OrderLogReq {
  uint64 start = 1;
  string id = 2[(api.path)="id"];;
}

message TaskLogResultList {
  repeated TaskLogResult list = 1;
}

message TaskLogResult {
  int64 created_time = 1;
  string task_log_id = 2;
  bool stopped = 3;
  string task_result = 4;
}
