[Unit]
Description=nginx-service
After=network.target

[Service]
Type=forking
PIDFile=/home/<USER>/ops/nginx/nginx.pid
ExecStartPre=/home/<USER>/ops/nginx/sbin/nginx -p /home/<USER>/ops/nginx -c conf/nginx.conf -t -q -g 'daemon on; master_process on;'
ExecStart=/home/<USER>/ops/nginx/sbin/nginx -p /home/<USER>/ops/nginx -c conf/nginx.conf -g 'daemon on; master_process on;'
ExecReload=/home/<USER>/ops/nginx/sbin/nginx -p /home/<USER>/ops/nginx -c conf/nginx.conf -g 'daemon on; master_process on;' -s reload
ExecStop=-/sbin/start-stop-daemon --quiet --stop --retry QUIT/5 --pidfile /home/<USER>/ops/nginx/nginx.pid
TimeoutStopSec=5
KillMode=mixed

[Install]
WantedBy=multi-user.target