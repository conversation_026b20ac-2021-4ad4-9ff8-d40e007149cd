[Unit]
Description=ops-gateway-service
After=consul
Requires=consul

[Service]
Type=forking
PIDFile=/home/<USER>/ops/op-gateway.pid
#ExecStartPre=/home/<USER>/ops/op-gateway -c /home/<USER>/ops/op-gateway-conf.yml -t
ExecStart=/home/<USER>/ops/op-gateway -c /home/<USER>/ops/op-gateway-conf.yml -d
ExecStop=/home/<USER>/ops/op-gateway -c /home/<USER>/ops/op-gateway-conf.yml stop

[Install]
WantedBy=multi-user.target