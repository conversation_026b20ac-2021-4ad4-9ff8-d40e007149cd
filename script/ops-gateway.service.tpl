[Unit]
Description=ops-gateway-service
After=ops-consul.service
Requires=ops-consul.service

[Service]
Type=forking
PIDFile={{work_path}}/ops-gateway.pid
#ExecStartPre=/home/<USER>/ops/op-gateway -c /home/<USER>/ops/op-gateway-conf.yml -t
ExecStart={{work_path}}/ops-gateway -c {{work_path}}/ops-gateway-conf.yml -d
ExecStop={{work_path}}/ops-gateway -c {{work_path}}/ops-gateway-conf.yml stop

[Install]
WantedBy=multi-user.target