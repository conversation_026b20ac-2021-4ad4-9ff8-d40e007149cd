import sys
import subprocess
import json
import time
import base64
from datetime import datetime

aliyuncli_path = 'aliyun'
instance_id = 'i-m5e4oli6nq39bcpy0lrb'
region_id = 'cn-qingdao'

process = subprocess.Popen([aliyuncli_path, 'ecs', 'RunCommand', '--InstanceId.1', instance_id, '--Type', 'RunShellScript', '--RegionId', region_id, '--CommandContent', sys.argv[1]], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
output, err = process.communicate()
if err.decode('utf-8') != '':
    print(err.decode('utf-8'))
    exit()

out_json = json.loads(output)
invoke_id = out_json['InvokeId']

# define retry time and sleep time
max_retry_time = 60 # seconds
sleep_time = 0.1

start_time = datetime.now()
now_time = start_time
while True:
    time.sleep(sleep_time)
    sleep_time *= 2

    process = subprocess.Popen([aliyuncli_path, 'ecs', 'DescribeInvocationResults', '--RegionId', region_id, '--InvokeId', invoke_id], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    output, err = process.communicate()

    out_json = json.loads(output)
    command_res = out_json['Invocation']['InvocationResults']['InvocationResult'][0]
    if command_res['InvokeRecordStatus'] == 'Finished':
        break

    now_time = datetime.now()
    if datetime.timestamp(now_time) - datetime.timestamp(start_time) > max_retry_time:
        print('error: max retry time achieved')
        break

if command_res['InvocationStatus'] == 'Failed':
    print('[error] ExitCode: {code}, Output: {output}'.format(code = command_res['ExitCode'], output=base64.b64decode(command_res['Output']).decode('utf-8')))
else:
    print(base64.b64decode(command_res['Output']).decode('utf-8'))