#!/bin/bash
#==============================
# @title: 安装Gateway
# @owner: <EMAIL>
# @buildDate: 2021.4.13
# @describe: <<<<
# 安装前先检测对应主机有没有安装ossutil命令,没有先安装ossutil工具
# 提供安装/更新/卸载gateway,同步agent资源等功能
# ***Gateway应与多云管理平台,作业平台等在同一个运维管理集群内
# 安装前确保以下端口未被占用:
# - 8500(consul;提供服务注册发现)
# - 38080(nginx;提供agent安装文件下载)
# - 8000(gateway;agent注册端口)
# - 8020(gateway-dispatch服务)
# - 10086(gateway管理端口)
# 推荐端口管理策略
# 8000通过nginx/阿里云slb服务对外提供服务
# 38080允许所有流量访问
# 其余端口仅限制运维系统集群环境访问
# >>>
#==============================
set -e

CONST_DEFAULT_GATEWAY="ops-gateway"
bindAddress=`ip add|grep -e ens33 -e eth0|grep inet|grep -v '127'|head -n1|awk '{print $2}'|awk -F"/" '{print $1}'`

accessKeyID=$2
accessKeySecret=$3
consulAddress=${4:-"${bindAddress}:8500"}
version=$5
filename=${6:-${CONST_DEFAULT_GATEWAY}}
ossPre="oss://hk4e-ops-prod"
workDir="/home/<USER>/ops" # 不要改变workdir地址

ossutilFile="${workDir}/ossutil64"
gatewayTarFile="ops-gateway.tar.gz"
gatewayDownloadFile="${ossPre}/${gatewayTarFile}"
gatewayFile="${workDir}/${filename}"
gatewayBakTagFile="${gatewayFile}.bak.tar.gz"

consulDownload="${ossPre}/tools/consul.tar.gz" # consul文件下载地址,文件中包含consul(二进制文件,system service文件)
consulBinFile="${workDir}/consul"

nginxFile="${workDir}/nginx"
nginxDownload="${ossPre}/"
nginxPath="/home/<USER>/ops/nginx/html/"


# 切换工作区
mkdir -p ${workDir}
cd ${workDir}

# Getossutil 获取ossutil
function Getossutil() {
  if [ ! -x ${ossutilFile} ]; then
    echo "目录${ossutilFile}未发现ossutil工具,准备下载"
    curl -o "${ossutilFile}" https://gosspublic.alicdn.com/ossutil/1.7.2/ossutil64
    chmod +x ${ossutilFile}
    echo "ossutil工具准备下载初始化完成"
  fi
}


# GetFile 通过oss获取文件
function GetFile() {
  ${ossutilFile} cp -f --endpoint="https://oss-cn-shanghai.aliyuncs.com" --access-key-id="${accessKeyID}" --access-key-secret="${accessKeySecret}" $1 $2
}

# buildService 根据tpl文件生产service
# @params $1 tpl文件路径
function buildService() {
#  echo $@ ${workDir} ${bindAddress}
  sed -i "s#{{work_path}}#${workDir}#g" $1
  sed -i "s#{{bind_address}}#${bindAddress}#g" $1
}

# GetConsul 获取consul
function GetConsul() {
  Getossutil
  check=`ss -tnlp|grep 8500|wc -l`
  if [ ${check} -gt 0 ]; then
    echo "consul检查通过"
  else
    echo "未发现consul,准备安装consul"
    tarfile="consul.tar.gz"
    GetFile "${consulDownload}" "${tarfile}"
    tar zxf ${tarfile}
    chmod +x "${consulBinFile}"
    cp -f "ops-consul.service.tpl" "ops-consul.service"
    buildService "ops-consul.service"
    cp -f "ops-consul.service" "/lib/systemd/system/"
    systemctl daemon-reload
  fi
}

# startConsul 启动consul
function startConsul() {
  echo "启动consul中"
  systemctl start ops-consul
  echo "consul启动成功"
}

# stopConsul 停止consul
function stopConsul() {
  echo "停止consul..."
  systemctl stop ops-consul
  echo "consul已停止"
}

# GetGateway 获取运维网关,强制更新,仅保留最后一次备份文件
function GetGateway() {
  check=`ps -ef|grep ${gatewayFile}|wc -l`
  if [ ${check} -gt 1 ];then
    echo "检测到ops-gateway已在运行中,执行退出动作"
    stopGateway
  fi

  if [ -x ${gatewayFile} ]; then
      tar czf "${gatewayBakTagFile}" "${gatewayFile}" "${gatewayFile}-conf.yml"
      mv "${gatewayFile}" "${gatewayFile}.back"
  fi
  GetFile "${gatewayDownloadFile}" ${gatewayTarFile}
}

# 仅执行动作
function installGateway() {
  echo "开始安装gateway..."
  tarFile="$1.tar.gz"
  tar zxf "${tarFile}"
  chmod +x "$1"
  initGatewayConf
  cp -f "ops-gateway.service.tpl" "ops-gateway.service"
  buildService "ops-gateway.service"
  cp -f "$1.service" "/lib/systemd/system/"
  systemctl daemon-reload
}

# 初始化Gateway配置文件
function initGatewayConf() {
  echo "执行初始化gateway配置文件..."
  ${gatewayFile} init -c "${workDir}/ops-gateway-conf.yml" -r "${consulAddress}" --pid-path="${workDir}/ops-gateway.pid"
}

# startGateway 启动运维网关
function startGateway() {
  echo "启动ops-gateway..."
  systemctl start ops-gateway
  echo "ops-gateway已启动"
}

# stopGateway 停止运维网关
function stopGateway() {
  echo "停止ops-gateway..."
  systemctl stop ops-gateway
  echo "ops-gateway已停止"
}

# upgradeGateway 更新gateway
function upgradeGateway() {
  echo "更新ops-gateway..."
  GetGateway
  startGateway
  sleep 10;
  check=`ps -ef|grep ${gatewayFile}|wc -l`
  if [ ${check} -gt 1 ];then
    echo "ops-gateway更新成功"
  else
    echo "ops-gateway更新成功,开始回滚版本..."
    installGateway ${gatewayBakTagFile}
  fi
}

function installNginx() {
  set sh -e
  set sh -x
  test ! -d "${nginxPath}/agent" && mkdir -p "${nginxPath}/agent" # 无相关权限时将执行失败
  nginxTar="${workDir}/nginx.tar.gz"
  ${GetFile} ${nginxDownload} ${workDir}/
  tar zxf nginxTar -C "${workDir}/nginx"
  sed -i "s#/usr/share/nginx/html/op-agent/#${nginxPath}/agent/#g" "${workDir}/nginx/conf/nginx.conf"
  ${ToolPath}/nginx/sbin/nginx -p ${ToolPath}/nginx/ -c conf/nginx.conf
}

# updateResource 更新资源
function updateResource() {
  GetFile ${nginxPath}/agent
}

# install 安装gateway,将执行完整安装流程
function install() {
  Getossutil
#  GetConsul
#  startConsul
  GetGateway
  installGateway "${gatewayFile}"
  startGateway
}

function installAll() {
  Getossutil
  GetConsul
  startConsul
  GetGateway
  installGateway "${gatewayFile}"
  startGateway
}

# update 更新gateway
function update() {
  pass
}

# uninstall 卸载gateway
function uninstall() {
  pass
}

# help 帮助信息
function help() {
  pass
}

if [ $# == 0 ];then
  install
else
  $1 $2
fi