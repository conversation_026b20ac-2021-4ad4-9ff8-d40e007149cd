apiVersion: v1
kind: Service
metadata:
  name: op-cloudman-agent
  namespace: hk4e-ops
spec:
  ports:
    - port: 80
      protocol: TCP
      targetPort: 7464
  selector:
    component: op-cloudman-takumi
  sessionAffinity: None
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: op-cloudman-agent
  namespace: hk4e-ops
spec:
  rules:
    - host: op-cloudman.yuanshen.com
      http:
        paths:
          - backend:
              service:
                name: op-cloudman-agent
                port:
                  number: 80
            path: /
            pathType: Prefix
---
### nginx_config
# server {
#    server_tokens off;
#    listen 8898;
#
#    location / {
#        proxy_pass http://***********;
#        proxy_set_header Host op-cloudman.yuanshen.com;
#    }
#    location /ws {
#        proxy_pass http://***********;
#        proxy_set_header Host op-cloudman.yuanshen.com;
#        proxy_set_header Upgrade $http_upgrade;
#        proxy_set_header Connection "upgrade";
#    }
#}
