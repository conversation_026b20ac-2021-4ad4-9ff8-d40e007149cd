use op-cloudman-takumi;
db.cloudman_takumi_SyncTask.updateMany({"$or": [{"task_type": "同步阿里云ECS信息"}, {"task_type": "同步AWS EC2信息"}]}, {"$set": {"task_type": "SyncHost"}});
db.cloudman_takumi_SyncTask.updateMany({"$or": [{"task_type": "同步阿里云PolarDB信息"}, {"task_type": "同步AWS Mysql信息"}]}, {"$set": {"task_type": "SyncMysql"}});
db.cloudman_takumi_SyncTask.updateMany({"$or": [{"task_type": "同步阿里云KvStore信息"}, {"task_type": "同步AWS Redis信息"}]}, {"$set": {"task_type": "SyncRedis"}});

db.cloudman_takumi_res_bind_tags.dropIndexes()