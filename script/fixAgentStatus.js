use op-cloudman-takumi;
// 将匹配的agent表的status和agentid同步回host表
db.getCollection("cloudman_takumi_ops-agent").find({"is_delete": 0}).forEach( function(item) {db.cloudman_takumi_host_resource.updateMany({"RegionID": item.region, "HostName": item.hostname, "InnerIpAddress": item.host_ip}, {$set: {"agent_id": item.agent_id, "agent_status": item.status}});})
// 寻找host中未匹配到agent且status为running的记录置为lost
db.cloudman_takumi_host_resource.aggregate([{$match: {is_delete: {$eq: 0}}}, {$project: {"RegionID": 1, "HostName": 1, "is_delete": 1, "agent_status": 1, "host_ip": {$arrayElemAt: ["$InnerIpAddress", 0]}}},{$lookup: {from:"cloudman_takumi_ops-agent", let: {a_region: "$RegionID", a_hostname: "$HostName", a_host_ip: "$host_ip"}, pipeline: [{$match: {$expr: {$and: [{$eq: ["$region", "$$a_region"]},{$eq:["$hostname", "$$a_hostname"]}, {$eq:["$host_ip", "$$a_host_ip"]}, {$eq: ["$is_delete", 0]}]}}}], as: "result"}}, {$match: {result: {$size: 0}, agent_status: 1}}]).forEach(function(item){db.cloudman_takumi_host_resource.updateMany({"_id": item._id}, {$set: {agent_status: 0}})})