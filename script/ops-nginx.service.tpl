[Unit]
Description=nginx-service
After=network.target

[Service]
Type=forking
PIDFile={{work_path}}/nginx/nginx.pid
ExecStartPre={{work_path}}/nginx/sbin/nginx -p {{work_path}}/nginx -c conf/nginx.conf -t -q -g 'daemon on; master_process on;'
ExecStart={{work_path}}/nginx/sbin/nginx -p {{work_path}}/nginx -c conf/nginx.conf -g 'daemon on; master_process on;'
ExecReload={{work_path}}/nginx/sbin/nginx -p {{work_path}}/nginx -c conf/nginx.conf -g 'daemon on; master_process on;' -s reload
ExecStop=-/sbin/start-stop-daemon --quiet --stop --retry QUIT/5 --pidfile {{work_path}}/nginx/nginx.pid
TimeoutStopSec=5
KillMode=mixed

[Install]
WantedBy=multi-user.target