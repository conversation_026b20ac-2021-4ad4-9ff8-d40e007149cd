package webhook

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

var hookQueue = make(chan *http.Request, 1024) // hook传送队列，避免阻塞

// ResourceMsg 统一资源类请求格式
type ResourceMsg struct {
	HookName hooks.HookName `json:"hook_name"`
	Data     interface{}    `json:"data"`
}

// RegHook 统一注册
func RegHook() {
	hooks.RegOrderStartHandler(orderStartHook)
	hooks.RegOrderOrderEndHandler(orderEndHook)

	// 以下与cmdb有关的webhook转至原生cmdb模块

	hooks.RegUpdateHostResourceHandler(updateHostResourceHook)
	hooks.RegCreateHostResourceHandler(createHostResourceHook)
	hooks.RegDeleteHostResourceHandler(func(ctx context.Context, status string, instanceID ...string) {
		deleteHook(hooks.DeleteHostResource, status, instanceID...)
	})

	hooks.RegChangeMysqlClusterResourceHandler(changeMysqlClusterHook)
	hooks.RegDeleteMysqlClusterResourceHandler(func(ctx context.Context, instanceID ...string) {
		deleteHook(hooks.DeleteMysqlClusterResource, "", instanceID...)
	})

	hooks.RegChangeDatabaseResourceHandler(changeMysqlDatabaseHook)
	hooks.RegDeleteDatabaseResourceHandler(func(ctx context.Context, instanceID ...string) {
		deleteHook(hooks.DeleteDatabaseResource, "", instanceID...)
	})

	hooks.RegChangeCacheResourceHandler(changeRedisClusterHook)
	hooks.RegDeleteCacheResourceHandler(func(ctx context.Context, instanceID ...string) {
		deleteHook(hooks.DeleteCacheResource, "", instanceID...)
	})

	hooks.RegMysqlEPHandler(changeMysqlEPClusterHook)

	hooks.RegSyncTaskHandler(syncTaskHook)
	// 采用队列结束,避免夯住
	go func() {
		for {
			req := <-hookQueue
			httpRequest(req)
		}
	}()
}

func httpRequest(req *http.Request) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	req.Header.Set("content-type", "application/json")
	client := &http.Client{
		Timeout: time.Second * 10, // 设置超时时间，根据需要调整
	}
	resp, err := client.Do(req.WithContext(ctx))
	if err != nil {
		logger.Errorf("webhook post error:", req.URL.String(), err.Error())
		return
	}

	if resp.StatusCode >= 400 {
		buf, _ := io.ReadAll(req.Body)
		logger.Errorf("webhook post error(400):", string(buf), req.URL.String())
		return
	}
}

func orderStartHook(data entity.ResOrder) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	buf, err := json.Marshal(data)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.OrderStart, err.Error())
		return
	}

	for _, v := range option.OrderStart {
		req, err := http.NewRequest("post", v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "orderStartHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func orderEndHook(data entity.ResOrder) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	buf, err := json.Marshal(data)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.OrderEnd, err.Error())
		return
	}

	for _, v := range option.OrderEnd {
		req, err := http.NewRequest("post", v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "orderEndHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func createHostResourceHook(ctx context.Context, data ...entity.HostResource) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	var hostPB []*cloudman.HostResDetail
	for _, v := range data {
		hostPB = append(hostPB, service.HostEntityToPb(v))
	}

	msg := ResourceMsg{
		HookName: hooks.CreateHostResource,
		Data:     hostPB,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.CreateHostResource, err.Error())
		return
	}

	for _, v := range option.ChangeHostResource {
		req, err := http.NewRequest(http.MethodPost, v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "createHostResourceHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func updateHostResourceHook(ctx context.Context, data ...entity.HostResource) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	var hostPB []*cloudman.HostResDetail
	for _, v := range data {
		hostPB = append(hostPB, service.HostEntityToPb(v))
	}

	msg := ResourceMsg{
		HookName: hooks.UpdateHostResource,
		Data:     hostPB,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.UpdateHostResource, err.Error())
		return
	}

	for _, v := range option.ChangeHostResource {
		req, err := http.NewRequest(http.MethodPost, v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "updateHostResourceHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func syncTaskHook(data entity.SyncTask) {

	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}
	msg := ResourceMsg{
		HookName: hooks.UpdateHostResource,
		Data:     data,
	}
	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.UpdateHostResource, err.Error())
		return
	}
	byteFile := bytes.NewBuffer(buf)

	for _, v := range option.SyncTask {
		req, err := http.NewRequest("post", v, byteFile)
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "syncTaskHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func changeMysqlClusterHook(ctx context.Context, name hooks.HookName, data ...entity.MysqlClusterResource) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	var hostPB []*cloudman.MysqlClusterDetail
	for _, v := range data {
		hostPB = append(hostPB, models.DBClusterEntityToPB(v))
	}

	msg := ResourceMsg{
		HookName: name,
		Data:     hostPB,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.UpdateHostResource, err.Error())
		return
	}
	// byteFile := bytes.NewBuffer(buf) // 不可复用

	for _, v := range option.ChangeMysqlResource {
		req, err := http.NewRequest(http.MethodPost, v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "changeMysqlClusterHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func changeMysqlDatabaseHook(ctx context.Context, name hooks.HookName, data ...entity.MysqlDatabaseResource) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	var hostPB []*cloudman.MysqlDatabase
	for _, v := range data {
		hostPB = append(hostPB, service.DatabaseEntityToPB(v))
	}

	msg := ResourceMsg{
		HookName: name,
		Data:     hostPB,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.UpdateHostResource, err.Error())
		return
	}

	for _, v := range option.ChangeMysqlDBResource {
		req, err := http.NewRequest(http.MethodPost, v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "changeMysqlDatabaseHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func changeRedisClusterHook(ctx context.Context, name hooks.HookName, data ...entity.RedisResource) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	var hostPB []*cloudman.RedisDetail
	for _, v := range data {
		hostPB = append(hostPB, models.RedisEntityToPb(v))
	}

	msg := ResourceMsg{
		HookName: name,
		Data:     hostPB,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.UpdateHostResource, err.Error())
		return
	}

	for _, v := range option.ChangeRedisResource {
		req, err := http.NewRequest(http.MethodPost, v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "hooks.UpdateHostResource", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func changeMysqlEPClusterHook(ctx context.Context, name hooks.HookName, data ...entity.MysqlClusterEndpointResource) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	var list []*cloudman.MysqlEndpoint
	str, _ := json.Marshal(data)
	err = json.Unmarshal(str, &list)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", "changeRedisClusterHook", err.Error())
		return
	}

	msg := ResourceMsg{
		HookName: name,
		Data:     list,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", "changeRedisClusterHook", err.Error())
		return
	}

	for _, v := range option.ChangeMysqlEPResource {
		req, err := http.NewRequest(http.MethodPost, v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", "changeMysqlEPClusterHook", err.Error())
			continue
		}
		hookQueue <- req
	}
}

func deleteHook(name hooks.HookName, status string, instanceID ...string) {
	option, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("GetHookError", err.Error())
		return
	}

	msg := ResourceMsg{
		HookName: name,
		Data:     instanceID,
	}

	buf, err := json.Marshal(msg)
	if err != nil {
		logger.Errorf("send %s webhook post error: %s", hooks.DeleteHostResource, err.Error())
		return
	}

	for _, v := range option.DeleteResource {
		req, err := http.NewRequest(http.MethodPost, v, bytes.NewBuffer(buf))
		req.Header.Add("x-rpc-token", option.GlobalToken)
		if err != nil {
			logger.Errorf("gen %s webhook post req error: %s", hooks.DeleteHostResource, err.Error())
			continue
		}
		hookQueue <- req
	}
}
