package hooks

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// HookName 钩子名称
type HookName string

const (
	// OrderStart 开始一个订单
	OrderStart HookName = "order_start"
	// OrderEnd 订单执行结束
	OrderEnd HookName = "order_end"
	// CreateHostResource 新增主机资源
	CreateHostResource HookName = "create_host_resource"
	// UpdateHostResource 更新主机资源
	UpdateHostResource HookName = "update_host_resource"
	// DeleteHostResource 删除主机资源
	DeleteHostResource HookName = "delete_host_resource"
	// CreateMysqlClusterResource 创建mysql资源
	CreateMysqlClusterResource HookName = "create_mysql_resource"
	// UpdateMysqlClusterResource 更新mysql资源
	UpdateMysqlClusterResource HookName = "update_mysql_resource"
	// DeleteMysqlClusterResource 删除mysql资源
	DeleteMysqlClusterResource HookName = "delete_mysql_resource"
	// CreateDatabaseResource 创建database
	CreateDatabaseResource HookName = "create_Database_resource"
	// UpdateDatabaseResource 更新数据库资源
	UpdateDatabaseResource HookName = "update_Database_resource"
	// DeleteDatabaseResource 删除database
	DeleteDatabaseResource HookName = "delete_Database_resource"
	// CreateCacheResource 创建缓存类资源
	CreateCacheResource HookName = "create_cache_resource"
	// UpdateCacheResource 更新缓存类资源
	UpdateCacheResource HookName = "update_cache_resource"
	// DeleteCacheResource 删除缓存类资源
	DeleteCacheResource HookName = "delete_cache_resource"
	// CreateMysqlEP 更新mysql-endpoint
	CreateMysqlEP HookName = "create_mysql_ep"
	// UpdateMysqlEP 更新mysql-endpoint
	UpdateMysqlEP HookName = "update_mysql_ep"
)

// OrderStartHandler 订单开始时钩子
type OrderStartHandler func(data entity.ResOrder)

// OrderEndHandler 订单结束时钩子
type OrderEndHandler func(order entity.ResOrder)

// CreateHostResourceHandler 创建主机资源时钩子
type CreateHostResourceHandler func(ctx context.Context, data ...entity.HostResource) // 提供的是完整的创建信息

// UpdateHostResourceHandler 更新主机资源时钩子
type UpdateHostResourceHandler func(ctx context.Context, data ...entity.HostResource) // 注意:提供的并非完整信息

// DeleteHostResourceHandler 删除主机资源时钩子
type DeleteHostResourceHandler func(ctx context.Context, status string, instanceID ...string) // 根据实例ID删除

// ChangeMysqlClusterResourceHandler 新增数据库资源
type ChangeMysqlClusterResourceHandler func(ctx context.Context, hookType HookName, data ...entity.MysqlClusterResource)

// DeleteMysqlClusterResourceHandler 删除cluster
type DeleteMysqlClusterResourceHandler func(ctx context.Context, instanceID ...string) // 根据实例ID删除

// ChangeDatabaseResourceHandler 新增数据库资源
type ChangeDatabaseResourceHandler func(ctx context.Context, hookType HookName, data ...entity.MysqlDatabaseResource)

// ChangeMysqlEPResourceHandler 更新mysql-endpoint
type ChangeMysqlEPResourceHandler func(ctx context.Context, hookType HookName, data ...entity.MysqlClusterEndpointResource)

// DeleteDatabaseResourceHandler 删除数据库资源时钩子
type DeleteDatabaseResourceHandler func(ctx context.Context, instanceID ...string) // 根据实例ID删除

// ChangeCacheResourceHandler 创建缓存类资源
type ChangeCacheResourceHandler func(ctx context.Context, hookType HookName, data ...entity.RedisResource)

// DeleteCacheResourceHandler 删除缓存类资源
type DeleteCacheResourceHandler func(ctx context.Context, instanceID ...string)

// ResourceChangeListHandler 资源变更详单钩子
type ResourceChangeListHandler func(ctx context.Context, changeList ...utils.ResourceChangeList)

// SyncTaskHandler 同步任务执行hook
type SyncTaskHandler func(data entity.SyncTask)

var (
	orderStartHandlers []OrderStartHandler
	orderEndHandlers   []OrderEndHandler

	createHostResourceHandlers []CreateHostResourceHandler
	updateHostResourceHandlers []UpdateHostResourceHandler
	deleteHostResourceHandlers []DeleteHostResourceHandler

	changeMysqlClusterResourceHandlers []ChangeMysqlClusterResourceHandler
	deleteMysqlClusterResourceHandlers []DeleteMysqlClusterResourceHandler

	changeDatabaseResourceHandlers []ChangeDatabaseResourceHandler
	deleteDatabaseResourceHandlers []DeleteDatabaseResourceHandler

	changeCacheResourceHandlers []ChangeCacheResourceHandler
	deleteCacheResourceHandlers []DeleteCacheResourceHandler

	changeMysqlEPHandlers []ChangeMysqlEPResourceHandler

	syncTaskHandlers           []SyncTaskHandler
	resourceChangeListHandlers []ResourceChangeListHandler
)

// RegOrderStartHandler 注册订单开始时钩子
func RegOrderStartHandler(handlers ...OrderStartHandler) {
	orderStartHandlers = append(orderStartHandlers, handlers...)
}

// PubOrderStartHandler 执行订单开始时钩子
func PubOrderStartHandler(data entity.ResOrder) {
	for _, orderStartHandler := range orderStartHandlers {
		orderStartHandler(data)
	}
}

// RegOrderOrderEndHandler 注册订单结束时钩子
func RegOrderOrderEndHandler(handler ...OrderEndHandler) {
	orderEndHandlers = append(orderEndHandlers, handler...)
}

// PubOrderEndHandler 执行订单结束时钩子
func PubOrderEndHandler(data entity.ResOrder) {
	for _, orderEndHandler := range orderEndHandlers {
		orderEndHandler(data)
	}
}

// RegCreateHostResourceHandler 注册创建资源时钩子
func RegCreateHostResourceHandler(handler ...CreateHostResourceHandler) {
	createHostResourceHandlers = append(createHostResourceHandlers, handler...)
}

// PubCreateHostResourceHandler 执行创建资源时钩子
func PubCreateHostResourceHandler(ctx context.Context, data ...entity.HostResource) {
	if len(data) == 0 {
		return
	}
	for _, createResourceHandler := range createHostResourceHandlers {
		createResourceHandler(ctx, data...)
	}
}

// RegUpdateHostResourceHandler 注册资源更新时钩子
func RegUpdateHostResourceHandler(handler ...UpdateHostResourceHandler) {
	updateHostResourceHandlers = append(updateHostResourceHandlers, handler...)
}

// PubUpdateHostResourceHandler 执行资源更新时钩子
func PubUpdateHostResourceHandler(ctx context.Context, data ...entity.HostResource) {
	if len(data) == 0 {
		return
	}

	for _, updateResourceHandler := range updateHostResourceHandlers {
		updateResourceHandler(ctx, data...)
	}
}

// RegDeleteHostResourceHandler 注册删除资源时钩子
func RegDeleteHostResourceHandler(handler ...DeleteHostResourceHandler) {
	deleteHostResourceHandlers = append(deleteHostResourceHandlers, handler...)
}

// PubDeleteHostResourceHandler 执行删除资源时钩子
func PubDeleteHostResourceHandler(ctx context.Context, status string, instances []string) {
	for _, deleteHostResourceHandler := range deleteHostResourceHandlers {
		deleteHostResourceHandler(ctx, status, instances...)
	}
}

// RegChangeMysqlClusterResourceHandler 注册删除资源时钩子
func RegChangeMysqlClusterResourceHandler(handler ...ChangeMysqlClusterResourceHandler) {
	changeMysqlClusterResourceHandlers = append(changeMysqlClusterResourceHandlers, handler...)
}

// PubChangeMysqlClusterResourceHandler 执行删除资源时钩子
func PubChangeMysqlClusterResourceHandler(ctx context.Context, name HookName, data ...entity.MysqlClusterResource) {
	for _, changeMysqlClusterResourceHandler := range changeMysqlClusterResourceHandlers {
		changeMysqlClusterResourceHandler(ctx, name, data...)
	}
}

// RegDeleteMysqlClusterResourceHandler 注册删除资源时钩子
func RegDeleteMysqlClusterResourceHandler(handler ...DeleteMysqlClusterResourceHandler) {
	deleteMysqlClusterResourceHandlers = append(deleteMysqlClusterResourceHandlers, handler...)
}

// PubDeleteMysqlClusterResourceHandler 执行删除资源时钩子
func PubDeleteMysqlClusterResourceHandler(ctx context.Context, instanceID ...string) {
	for _, deleteMysqlClusterResourceHandler := range deleteMysqlClusterResourceHandlers {
		deleteMysqlClusterResourceHandler(ctx, instanceID...)
	}
}

// RegChangeDatabaseResourceHandler 注册删除资源时钩子
func RegChangeDatabaseResourceHandler(handler ...ChangeDatabaseResourceHandler) {
	changeDatabaseResourceHandlers = append(changeDatabaseResourceHandlers, handler...)
}

// PubChangeDatabaseResourceHandler 执行删除资源时钩子
func PubChangeDatabaseResourceHandler(ctx context.Context, name HookName, data ...entity.MysqlDatabaseResource) {
	for _, changeDatabaseResourceHandler := range changeDatabaseResourceHandlers {
		changeDatabaseResourceHandler(ctx, name, data...)
	}
}

// RegDeleteDatabaseResourceHandler 注册删除资源时钩子
func RegDeleteDatabaseResourceHandler(handler ...DeleteDatabaseResourceHandler) {
	deleteDatabaseResourceHandlers = append(deleteDatabaseResourceHandlers, handler...)
}

// PubDeleteDatabaseResourceHandler 执行删除资源时钩子
func PubDeleteDatabaseResourceHandler(ctx context.Context, instanceID ...string) {
	for _, deleteDatabaseResourceHandler := range deleteDatabaseResourceHandlers {
		deleteDatabaseResourceHandler(ctx, instanceID...)
	}
}

// RegChangeCacheResourceHandler 注册删除资源时钩子
func RegChangeCacheResourceHandler(handler ...ChangeCacheResourceHandler) {
	changeCacheResourceHandlers = append(changeCacheResourceHandlers, handler...)
}

// PubChangeCacheResourceHandler 执行删除资源时钩子
func PubChangeCacheResourceHandler(ctx context.Context, name HookName, data ...entity.RedisResource) {
	for _, changeCacheResourceHandler := range changeCacheResourceHandlers {
		changeCacheResourceHandler(ctx, name, data...)
	}
}

// RegDeleteCacheResourceHandler 注册删除资源时钩子
func RegDeleteCacheResourceHandler(handler ...DeleteCacheResourceHandler) {
	deleteCacheResourceHandlers = append(deleteCacheResourceHandlers, handler...)
}

// PubDeleteCacheResourceHandler 执行删除资源时钩子
func PubDeleteCacheResourceHandler(ctx context.Context, instanceID ...string) {
	for _, deleteCacheResourceHandler := range deleteCacheResourceHandlers {
		deleteCacheResourceHandler(ctx, instanceID...)
	}
}

// RegResourceChangeListHandler 注册资源变更时详单钩子
func RegResourceChangeListHandler(handler ...ResourceChangeListHandler) {
	resourceChangeListHandlers = append(resourceChangeListHandlers, handler...)
}

// PubResourceChangeListHandler 执行资源变更时详单钩子
func PubResourceChangeListHandler(ctx context.Context, changeList ...utils.ResourceChangeList) {
	for _, resourceChangeListHandler := range resourceChangeListHandlers {
		resourceChangeListHandler(ctx, changeList...)
	}
}

// RegSyncTaskHandler 注册同步任务hook
func RegSyncTaskHandler(handler ...SyncTaskHandler) {
	syncTaskHandlers = append(syncTaskHandlers, handler...)
}

// PubSyncTaskHandler 执行同步任务hook
func PubSyncTaskHandler(data entity.SyncTask) {
	for _, syncTaskHandler := range syncTaskHandlers {
		syncTaskHandler(data)
	}
}

// RegMysqlEPHandler 更新mysql-endpoint
func RegMysqlEPHandler(handler ...ChangeMysqlEPResourceHandler) {
	changeMysqlEPHandlers = append(changeMysqlEPHandlers, handler...)
}

// PubMysqlEPHandler 更新mysql-endpoint
func PubMysqlEPHandler(ctx context.Context, name HookName, data ...entity.MysqlClusterEndpointResource) {
	for _, changeMysqlEPHandler := range changeMysqlEPHandlers {
		changeMysqlEPHandler(ctx, name, data...)
	}
}
