package notice

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// RegHook 统一注册
func RegHook() {
	hooks.RegOrderStartHandler(orderStartSendNotice)
	hooks.RegOrderOrderEndHandler(orderEndSendNotice)
	hooks.RegResourceChangeListHandler(resourceChangeNotice)
	go sendChangeListMessage()
}

// orderStartSendNotice 订单开始时发送通知
func orderStartSendNotice(data entity.ResOrder) {
	logger.Infof("orderStartSendNotice,订单开始时发送通知", data)
}

// orderEndSendNotice 订单完成时发送通知
func orderEndSendNotice(data entity.ResOrder) {
	logger.Infof("orderEndSendNotice,订单完成时发送通知", data)
}

// resourceChangeNotice -
func resourceChangeNotice(ctx context.Context, changeList ...utils.ResourceChangeList) {
	for _, c := range changeList {
		changeListQueue <- c
	}
}

var changeListQueue = make(chan utils.ResourceChangeList, 50)

func sendChangeListMessage() {
	for {
		select {
		case l := <-changeListQueue:
			for f, d := range l.Data {
				if changeListIgnoreField[f] {
					continue
				}
				_, _ = models.ResChangelistModel.Create(context.Background(), &entity.ResChangelist{
					AccountID:    l.AccountID,
					ResourceType: l.Type,
					InstanceID:   l.InstanceID,
					InstanceName: l.InstanceName,
					Field:        f,
					Before:       d.Before,
					After:        d.After,
				})
			}
		}
	}
}

var changeListIgnoreField = map[string]bool{
	"StorageUsed":  true,
	"ExpiredTime":  true,
	"ExpireTime":   true,
	"EndTime":      true,
	"initTreeNode": true,
}
