package bkcmdb

import (
	"context"
	"encoding/json"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent_runner/task"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

func initHookTask(ctx context.Context, methodName string, data interface{}) (*logrus.Logger, string) {
	var err error
	taskID := ""
	logger := logrus.StandardLogger()
	m, ok := ctx.Value(constant.HookCtxMeta).(map[string]string)
	if ok {
		username := m["username"]
		if username == "" {
			username = permission.GetUsername(ctx)
		}
		dataBin, _ := json.Marshal(data)
		atEntity := &entity.AgentTask{
			AccountID:    m["isp_id"],
			ResourceType: "cmdb",
			MethodName:   methodName,
			Status:       task.StatusCreate,
			RunType:      task.RunTypeLocal,
			Operator:     username,
			RequestDump:  string(dataBin),
			Extra: entity.AgentTaskExtra{
				SubmitTime: time.Now().UnixNano(),
				StartTime:  time.Now().UnixNano(),
				Risky:      1,
			},
		}
		if m["task_id"] != "" {
			atEntity.ID, _ = primitive.ObjectIDFromHex(m["task_id"])
		}
		taskID, err = models.AgentTaskModel.Create(ctx, atEntity)
		if err != nil {
			logger.Errorf("initHookTask.db.error(%s): %s", taskID, err.Error())
			return logger, taskID
		}
		logger = service.BuildLogger(taskID)
	}
	return logger, taskID
}

func finishHookTask(ctx context.Context, logger *logrus.Logger, taskID string, methodName string, resultData interface{}, resultError error) {
	if resultError != nil {
		logger.Errorf("%s.execute.error(%s): %s", methodName, taskID, resultError.Error())
		if taskID != "" {
			_ = models.AgentTaskModel.Update(ctx, taskID, map[string]interface{}{
				"status":              task.StatusFailed,
				"err_msg":             resultError.Error(),
				"extra.end_time":      time.Now().UnixNano(),
				"extra.callback_time": time.Now().UnixNano(),
			})
		}
	} else {
		logger.Infof("%s.execute.done(%s)", methodName, taskID)
		if taskID != "" {
			dataBin, _ := json.Marshal(resultData)
			_ = models.AgentTaskModel.Update(ctx, taskID, map[string]interface{}{
				"status":              task.StatusSuccess,
				"response_dump":       string(dataBin),
				"extra.end_time":      time.Now().UnixNano(),
				"extra.callback_time": time.Now().UnixNano(),
			})
		}
	}
}
