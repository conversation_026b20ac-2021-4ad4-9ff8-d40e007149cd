package bkcmdb

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/servicetree"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cmdb"
)

// RegHook -
func RegHook() {
	hooks.RegCreateHostResourceHandler(createHostResourceHook)
	hooks.RegCreateHostResourceHandler(addServiceTreeHostResourceHook)
	hooks.RegUpdateHostResourceHandler(updateHostResourceHook)
	hooks.RegDeleteHostResourceHandler(deleteHostResourceHook)
	hooks.RegChangeMysqlClusterResourceHandler(updateMysqlResourceHook)
	hooks.RegChangeMysqlClusterResourceHandler(addServiceTreeMysqlResourceHook)
	hooks.RegDeleteMysqlClusterResourceHandler(deleteMysqlResourceHook)
	hooks.RegChangeDatabaseResourceHandler(updateMysqlDatabaseResourceHook)
	hooks.RegDeleteDatabaseResourceHandler(deleteMysqlDatabaseResourceHook)
	hooks.RegChangeCacheResourceHandler(updateRedisResourceHook)
	hooks.RegChangeCacheResourceHandler(addServiceTreeCacheResourceHook)
	hooks.RegDeleteCacheResourceHandler(deleteRedisResourceHook)
}

func createHostResourceHook(ctx context.Context, data ...entity.HostResource) {
	if len(data) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.CreateHostResource), data)
	result, err := cmdb.CreateCmdbHosts(logger, data)
	finishHookTask(ctx, logger, taskID, string(hooks.CreateHostResource), result, err)
}

func addServiceTreeHostResourceHook(ctx context.Context, data ...entity.HostResource) {
	if len(data) == 0 {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.CreateHostResource), data)
	result, err := servicetree.PushServiceTreeHost(ctx, logger, data)
	finishHookTask(ctx, logger, taskID, string(hooks.CreateHostResource), result, err)
}

func updateHostResourceHook(ctx context.Context, data ...entity.HostResource) {
	if len(data) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.UpdateHostResource), data)
	result, err := cmdb.UpdateCmdbHosts(logger, data)
	finishHookTask(ctx, logger, taskID, string(hooks.UpdateHostResource), result, err)
}

func deleteHostResourceHook(ctx context.Context, status string, instanceID ...string) {
	if len(instanceID) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.DeleteHostResource), instanceID)
	result, err := cmdb.DeleteBkCmdbNoEnv(logger, cmdb.ALIEcs, instanceID)
	finishHookTask(ctx, logger, taskID, string(hooks.DeleteHostResource), result, err)
}

func updateMysqlResourceHook(ctx context.Context, hookType hooks.HookName, data ...entity.MysqlClusterResource) {
	if len(data) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hookType), data)
	result, err := cmdb.PushBkCmdbMysql(logger, data)
	finishHookTask(ctx, logger, taskID, string(hookType), result, err)
}

func addServiceTreeMysqlResourceHook(ctx context.Context, hookType hooks.HookName, data ...entity.MysqlClusterResource) {
	if len(data) == 0 || hookType != hooks.CreateMysqlClusterResource {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.CreateMysqlClusterResource), data)
	result, err := servicetree.PushServiceTreeMysql(ctx, logger, data)
	finishHookTask(ctx, logger, taskID, string(hooks.CreateMysqlClusterResource), result, err)
}

func deleteMysqlResourceHook(ctx context.Context, instanceID ...string) {
	if len(instanceID) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.DeleteMysqlClusterResource), instanceID)
	result, err := cmdb.DeleteBkCmdbNoEnv(logger, cmdb.ALIPolarDB, instanceID)
	finishHookTask(ctx, logger, taskID, string(hooks.DeleteMysqlClusterResource), result, err)
}

func updateMysqlDatabaseResourceHook(ctx context.Context, hookType hooks.HookName, data ...entity.MysqlDatabaseResource) {
	if len(data) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hookType), data)
	result, err := cmdb.PushBkCmdbMysqlDatabase(logger, data)
	finishHookTask(ctx, logger, taskID, string(hookType), result, err)
}
func deleteMysqlDatabaseResourceHook(ctx context.Context, instanceID ...string) {
	if len(instanceID) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.DeleteDatabaseResource), instanceID)
	result, err := cmdb.DeleteBkCmdbNoEnv(logger, cmdb.ALIDatabase, instanceID)
	finishHookTask(ctx, logger, taskID, string(hooks.DeleteDatabaseResource), result, err)
}

func updateRedisResourceHook(ctx context.Context, hookType hooks.HookName, data ...entity.RedisResource) {
	if len(data) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hookType), data)
	result, err := cmdb.PushBkCmdbRedis(logger, data)
	finishHookTask(ctx, logger, taskID, string(hookType), result, err)
}

func addServiceTreeCacheResourceHook(ctx context.Context, hookType hooks.HookName, data ...entity.RedisResource) {
	if len(data) == 0 || hookType != hooks.CreateCacheResource {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.CreateCacheResource), data)
	result, err := servicetree.PushServiceTreeRedis(ctx, logger, data)
	finishHookTask(ctx, logger, taskID, string(hooks.CreateCacheResource), result, err)
}

func deleteRedisResourceHook(ctx context.Context, instanceID ...string) {
	if len(instanceID) == 0 || !cfg.GetCmdbConfig().Enable {
		return
	}
	logger, taskID := initHookTask(ctx, string(hooks.DeleteCacheResource), instanceID)
	result, err := cmdb.DeleteBkCmdbNoEnv(logger, cmdb.ALIKVStore, instanceID)
	finishHookTask(ctx, logger, taskID, string(hooks.DeleteCacheResource), result, err)
}
