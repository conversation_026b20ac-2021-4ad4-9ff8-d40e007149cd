package innerhook

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// RegHook 统一注册hook
func RegHook() {
	//hooks.RegOrderOrderEndHandler(OrderEndTriggerSyncResource)
	hooks.RegUpdateHostResourceHandler(UpdateResourceTriggerSendNotice)
	hooks.RegCreateHostResourceHandler(UpdateResourceTriggerSendNotice)
	hooks.RegDeleteHostResourceHandler(deleteResourceTriggerHotWheel)
}

// UpdateResourceTriggerSendNotice 资源更新时触发通知
func UpdateResourceTriggerSendNotice(ctx context.Context, data ...entity.HostResource) {
	logger.Infof("UpdateResourceTriggerSendNotice,资源更新时触发通知", data)
}

func httpRequest(req *http.Request) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	req.Header.Set("content-type", "application/json")

	client := &http.Client{
		Timeout: time.Second * 10, // 设置超时时间，根据需要调整
	}
	resp, err := client.Do(req.WithContext(ctx))
	if err != nil {
		logger.Errorf("inner-hook post error:", req.URL.String(), err.Error())
		return
	}

	if resp.StatusCode >= 400 {
		buf, _ := io.ReadAll(req.Body)
		logger.Errorf("inner-hook post error(400):", string(buf), req.URL.String())
		return
	}
}

// HotWheelSt 标准运维流程传参
type HotWheelSt struct {
	Name       string `json:"name"`
	PipelineID int    `json:"pipelineID"`
	Constants  string `json:"constants"`
}

// deleteResourceTriggerHotWheel 资源删除时触发标准运维
// 这个hook暂时不用
func deleteResourceTriggerHotWheel(ctx context.Context, status string, instancesID ...string) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	logger.Infof("资源删除时触发标准运维", instancesID)

	hotWheelOption, err := cfg.GetHookOption()
	if err != nil {
		logger.Errorf("获取wheel-option error", err.Error())
		return
	}
	if hotWheelOption.HotWheel.ReleaseHostResource == nil && hotWheelOption.HotWheel.UnReleaseHostResource == nil {
		return
	}

	res, err := models.HostResourceModel.FindManyIncludeDeleted(ctx, map[string]interface{}{"InstanceID": map[string][]string{"$in": instancesID}})
	if err != nil {
		logger.Errorf("deleteResourceTriggerHotWheel res: %v", res)
		return
	}
	if len(res) == 0 {
		logger.Errorf("获取wheel-option 未获取到InstanceID%v信息", instancesID)
		return
	}

	// ctx := ctxhelper.AddSoaMeta(context.Background(), "username", "system")
	var hosts []map[string]interface{}
	for _, host := range res {
		hosts = append(hosts, map[string]interface{}{"Host": host.HostName})
	}
	m := map[string]interface{}{
		"${uninstall_gaia}": "0",
		"${instance}": map[string]interface{}{
			"value": hosts,
			"type":  "manual",
		},
	}
	data, _ := json.Marshal(m)

	reqOption := hotWheelOption.HotWheel.ReleaseHostResource
	rawData := HotWheelSt{
		Name:      "主机下线流程",
		Constants: string(data),
	}

	if status == "unRecycle" {
		rawData.Name = "主机从回收站移出"
		reqOption = hotWheelOption.HotWheel.UnReleaseHostResource
	}

	for _, v := range reqOption {
		rawData.PipelineID = v.PipelineID
		rawDataBuf, _ := json.Marshal(rawData)

		req, err := http.NewRequest(http.MethodPost, v.URI, bytes.NewReader(rawDataBuf))
		req.Header.Add("x-rpc-token", v.Token)
		req.Header.Add("Authorization", v.Token)
		logger.Debugf("post-hotwheel:", rawDataBuf, v.URI, v.Token)
		if err != nil {
			logger.Errorf("post-hotwheel-error", err.Error())
			continue
		}
		httpRequest(req)
	}
}
