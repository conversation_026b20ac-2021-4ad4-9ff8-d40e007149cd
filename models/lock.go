package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// LockModel lock model
var LockModel = new(Lock)

// Lock lock
type Lock struct{}

// AcquireLock ...
func (a Lock) AcquireLock(ctx context.Context, lockKey string, lockExpire time.Duration) (bool, error) {
	c := entity.GetLockCollection(GetEngine())
	filter := bson.M{"key": lockKey, "expireAt": bson.M{"$lt": time.Now()}}
	update := bson.M{"$set": bson.M{"expireAt": time.Now().Add(lockExpire)}}
	opts := options.FindOneAndUpdate().SetUpsert(true)
	result := c.FindOneAndUpdate(ctx, filter, update, opts)
	err := result.Err()
	// 没有获取到锁
	if err == mongo.ErrNoDocuments {
		return false, nil
	}
	if result.Err() != nil {
		return false, result.Err()
	}
	return true, nil
}

// ReleaseLock ...
func (a Lock) ReleaseLock(ctx context.Context, lockKey string) error {
	c := entity.GetLockCollection(GetEngine())
	filter := bson.M{"key": lockKey}
	_, err := c.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}
