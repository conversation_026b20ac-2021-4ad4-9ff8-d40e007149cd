package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
)

// ResourcePolicy resource-group-model
var ResourcePolicy = new(resourcePolicy)

// ResourcePolicy resource-group
type resourcePolicy struct {
}

// // Query query
// func (r resourcePolicy) Query(ctx context.Context, params *schema.ResGroupPolicyQueryParams) ([]entity.ResGroupPolicy, uint64, error) {
// Get with git-history
// }

// Get get
// func (r resourcePolicy) Get(ctx context.Context, oid string) (*entity.ResGroupPolicyInfo, error) {
// Get with git-history
// }

func (r resourcePolicy) FindPolicy(ctx context.Context, filter map[string]interface{}) ([]*entity.ResGroupPolicy, error) {
	var data []*entity.ResGroupPolicy
	c := entity.GetResGroupPolicyCollection(GetEngine())
	_, err := FindMany(ctx, c, filter, &data)
	return data, err
}

func (r resourcePolicy) GetPolicy(ctx context.Context, bindResID string, resourceType string) (*entity.ResGroupPolicy, error) {
	var data *entity.ResGroupPolicy
	c := entity.GetResGroupPolicyCollection(GetEngine())
	_, err := FindOne(ctx, c, map[string]interface{}{
		"bind_res_group_id": bindResID,
		"resource_type":     resourceType,
	}, &data)
	if err == mongo.ErrNoDocuments {
		return nil, nil
	}
	return data, err
}

// GetInfo 获取详情
func (r resourcePolicy) GetInfo(ctx context.Context, oid string, resourceType string) (*entity.ResGroupPolicyInfo, error) {
	id, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}
	groupEntity := entity.GetResGroupCollection(GetEngine())
	var group entity.ResGroup
	_, err = FindOne(ctx, groupEntity, map[string]interface{}{
		"_id": id,
	}, &group)
	if err != nil {
		return nil, err
	}

	var resp entity.ResGroupPolicyInfo
	resp.ResGroupInfo = group
	resp.ResGroupPolicy.BindResGroupID = group.ID.Hex()

	c := entity.GetResGroupPolicyCollection(GetEngine())
	var groupPolicy entity.ResGroupPolicy

	_, err = FindOne(ctx, c, map[string]interface{}{
		"bind_res_group_id": oid,
		"resource_type":     resourceType,
	}, &groupPolicy)
	if err != nil {
		logger.Errorf(err.Error())
	}

	return &entity.ResGroupPolicyInfo{
		ResGroupPolicy: groupPolicy,
		ResGroupInfo:   group,
	}, nil
}

// Create create
func (r resourcePolicy) Create(ctx context.Context, data *entity.ResGroupPolicy) error {
	// t := time.Now()
	data.ID = primitive.NewObjectID()
	data.CreatedTime = time.Now().Unix()
	c := entity.GetResGroupPolicyCollection(GetEngine())

	return Insert(ctx, c, data)
}

// Update xxx
func (r resourcePolicy) Update(ctx context.Context, id string, updateData interface{}, queryOptions ...map[string]interface{}) error {
	data, err := mapstruct.Struct2Map(updateData)
	if err != nil {
		return err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	if len(queryOptions) != 0 {
		filter = append(filter, mapToFilter(queryOptions[0])...)
	}

	c := entity.GetResGroupPolicyCollection(GetEngine())
	return UpdateMany(ctx, c, filter, data)
}

// CreateOrUpdate 创建/更新
func (r resourcePolicy) CreateOrUpdate(ctx context.Context, policy *entity.ResGroupPolicy) error {
	c := entity.GetResGroupPolicyCollection(GetEngine())
	data, err := mapstruct.Struct2Map(policy)
	if err != nil {
		return err
	}

	isUpsert := true
	_, err = c.UpdateOne(ctx, map[string]interface{}{
		"bind_res_group_id": policy.BindResGroupID,
		"resource_type":     policy.ResourceType,
	}, map[string]interface{}{"$set": data}, &options.UpdateOptions{Upsert: &isUpsert})
	return err
}
