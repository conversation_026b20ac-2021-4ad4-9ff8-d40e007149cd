package models

import (
	"encoding/json"
	"fmt"

	"github.com/tealeg/xlsx"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// ExcelToMap excelToMap
func ExcelToMap(row *xlsx.Row, rowList []*entity.FiledStruct) (map[string]interface{}, error) {
	var err error
	m := make(map[string]interface{})
	for index, cellStr := range rowList {
		if cellStr == nil {
			continue
		}
		if len(row.Cells) == index {
			break
		}
		cell := row.Cells[index]
		value := row.Cells[index].Value
		switch cellStr.Type {
		case "float32", "float64":
			if value != "" {
				val, err := cell.Float()
				if err != nil {
					return nil, fmt.Errorf("value: %s isnt't int32", value)
				}
				m[cellStr.Value] = val
			}
			m[cellStr.Value] = 0
			break
		case "int64":
			if value != "" {
				val, err := cell.Int64()
				if err != nil {
					return nil, fmt.Errorf("value: %s isnt't int32", value)
				}
				m[cellStr.Value] = val
			} else {
				m[cellStr.Value] = 0
			}
			break
		case "int32":
			if value != "" {
				val, err := cell.Int()
				if err != nil {
					return nil, fmt.Errorf("value: %s isnt't int32", value)
				}
				m[cellStr.Value] = val
			} else {
				m[cellStr.Value] = 0
			}
			break
		case "bool":
			m[cellStr.Value] = cell.Bool()
			break
		case "struct":
			if value == "" {
				m[cellStr.Value] = nil
				break
			}
			var rawMap map[string]interface{}
			err = json.Unmarshal([]byte(value), &rawMap)
			if err != nil {
				return nil, fmt.Errorf("value: %s isnt't struct", value)
			}

			m[cellStr.Value] = rawMap
			break
		case "slice":
			if value == "" {
				m[cellStr.Value] = nil
				break
			}
			var list []interface{}
			err = json.Unmarshal([]byte(value), &list)
			if err != nil {
				return nil, fmt.Errorf("value: %s isnt't slice", value)
			}

			m[cellStr.Value] = list
			break
		case "SliceString":
			if value == "" {
				m[cellStr.Value] = nil
				break
			}
			var list []string
			// 数组导出不符合规范
			err = json.Unmarshal([]byte(value), &list)
			if err != nil {
				return nil, fmt.Errorf("value: %s isn't slice-string", value)
			}
			m[cellStr.Value] = list
			break
		default:
			m[cellStr.Value] = value
		}
	}

	return m, nil
}
