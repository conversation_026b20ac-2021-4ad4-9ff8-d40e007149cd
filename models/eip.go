package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// EipModel zone-model
var EipModel = new(Eip)

// Eip zone
type Eip struct {
}

// Query query
func (r Eip) Query(ctx context.Context, params *schema.EipQueryParams) ([]*entity.Eip, uint64, error) {
	c := entity.GetEipCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.EipColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.Eip, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.Eip{}.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	logger.Info(fmt.Sprintf("Filter:%+v", filter.Map()))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get xx
func (r Eip) Get(ctx context.Context, oid string) (*entity.Eip, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetEipCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var zoneEntity entity.Eip
	_, err = FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// FindManyWithPK find-many
func (r Eip) FindManyWithPK(ctx context.Context, ids []string) ([]entity.Eip, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetEipCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.Eip
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindMany find-many
func (r Eip) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.Eip, error) {
	c := entity.GetEipCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
		if option[0].WithoutIsp {
			filters = DefaultFilter(ctx, mapToFilter(filter)...)
		}
	}

	var resource []*entity.Eip

	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// FindManyWithIDToMap to map
func (r Eip) FindManyWithIDToMap(ctx context.Context, eipIDs []string) (map[string]entity.Eip, error) {
	if len(eipIDs) == 0 {
		return nil, nil
	}

	c := entity.GetEipCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "AllocationId", Value: map[string]interface{}{"$in": eipIDs},
	})

	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	m := map[string]entity.Eip{}
	for cur.Next(ctx) {
		var eip entity.Eip
		if err = cur.Decode(&eip); err != nil {
			return nil, err
		}
		m[eip.AllocationID] = eip
	}

	return m, nil
}

// CreateOrUpdate 创建/更新
func (r Eip) CreateOrUpdate(ctx context.Context, data *entity.Eip) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	action := "create"
	c := entity.GetEipCollection(GetEngine())
	filter := map[string]interface{}{
		"AllocationId": data.AllocationID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.Eip
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	_, err = IPModel.CreateOrUpdate(ctx, EipToIPEntity(data), true)
	if err != nil {
		return "", err
	}

	return action, err
}

// BatchCreateOrUpdate 批量创建或更新
func (r Eip) BatchCreateOrUpdate(ctx context.Context, data []*entity.Eip) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.AllocationID, v.Name)

		if v.AllocationID != "" && v.Name != "" {
			action, err := r.CreateOrUpdate(ctx, v)
			if err != nil {
				errText := fmt.Sprintf("allocation_id: %s, name: %s, errors: %v", v.AllocationID, v.Name, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// EipModelToPb ...
func EipModelToPb(ctx context.Context, eipEntities []*entity.Eip) ([]*cloudman.EipEntity, error) {
	var accountIds []string
	for _, a := range eipEntities {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(ctx, accountIds)
		if err != nil {
			return nil, err
		}
	}

	var eipPbs []*cloudman.EipEntity
	for _, eipEntity := range eipEntities {
		var tags []*cloudman.ResourceTag
		for _, tag := range eipEntity.Tags {
			tags = append(tags, &cloudman.ResourceTag{
				Key:   tag.TagKey,
				Value: tag.TagValue,
			})
		}

		var operationLocks []*cloudman.EipAddressOperationLocks
		for _, operationLock := range eipEntity.OperationLocks {
			operationLocks = append(operationLocks, &cloudman.EipAddressOperationLocks{
				LockReason: operationLock.LockReason,
			})
		}

		eipPb := &cloudman.EipEntity{
			AllocationId:                  eipEntity.AllocationID,
			AllocationTime:                eipEntity.AllocationTime,
			Bandwidth:                     eipEntity.Bandwidth,
			BandwidthPackageBandwidth:     eipEntity.BandwidthPackageBandwidth,
			BandwidthPackageId:            eipEntity.BandwidthPackageID,
			BandwidthPackageType:          eipEntity.BandwidthPackageType,
			BizType:                       eipEntity.BizType,
			BusinessStatus:                eipEntity.BusinessStatus,
			ChargeType:                    eipEntity.ChargeType,
			DeletionProtection:            eipEntity.DeletionProtection,
			Description:                   eipEntity.Description,
			EipBandwidth:                  eipEntity.EipBandwidth,
			ExpiredTime:                   eipEntity.ExpiredTime,
			HdMonitorStatus:               eipEntity.HDMonitorStatus,
			HasReservationData:            eipEntity.HasReservationData,
			Isp:                           eipEntity.ISP,
			InstanceId:                    eipEntity.InstanceID,
			InstanceRegionId:              eipEntity.InstanceRegionID,
			InstanceType:                  eipEntity.InstanceType,
			InternetChargeType:            eipEntity.InternetChargeType,
			IpAddress:                     eipEntity.IPAddress,
			Name:                          eipEntity.Name,
			Netmode:                       eipEntity.Netmode,
			OperationLocks:                operationLocks,
			PublicIpAddressPoolId:         eipEntity.PublicIPAddressPoolID,
			RegionId:                      eipEntity.RegionID,
			ReservationActiveTime:         eipEntity.ReservationActiveTime,
			ReservationBandwidth:          eipEntity.ReservationBandwidth,
			ReservationInternetChargeType: eipEntity.ReservationInternetChargeType,
			ReservationOrderType:          eipEntity.ReservationOrderType,
			ResourceGroupId:               eipEntity.ResourceGroupID,
			SecondLimited:                 eipEntity.SecondLimited,
			SecurityProtectionTypes:       eipEntity.SecurityProtectionTypes,
			SegmentInstanceId:             eipEntity.SegmentInstanceID,
			ServiceManaged:                eipEntity.ServiceManaged,
			Status:                        eipEntity.Status,
			VpcId:                         eipEntity.VpcID,
			Zone:                          eipEntity.Zone,
			IspId:                         eipEntity.IspID,
			IspType:                       eipEntity.IspType,
			IspName:                       ac[eipEntity.IspID].Name,
		}
		eipPbs = append(eipPbs, eipPb)
	}

	return eipPbs, nil
}

// EipToIPEntity ...
func EipToIPEntity(eip *entity.Eip) *entity.IP {
	createType := "sync"
	if eip.IspType == "custom" {
		createType = eip.IspType
	}
	return &entity.IP{
		Type:             "ip",
		CreateType:       createType,
		Address:          eip.IPAddress,
		BindInstanceType: "eip",
		InstanceID:       eip.AllocationID,
		Desc:             "",
		RegionID:         eip.RegionID,
		IspID:            eip.IspID,
		IspType:          eip.IspType,
	}
}
