package models

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// AccountModel account model
var AccountModel = new(Account)

// Account account
type Account struct{}

// Query 查询列表
func (a Account) Query(ctx context.Context, params *schema.AccountQueryParams) ([]entity.Account, uint64, error) {
	c := entity.GetAccountCollection(GetEngine())
	filter := AccountDefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.AccountColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.Account, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{new(entity.Account).Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, err
}

// FindAll ...
func (a Account) FindAll(ctx context.Context) ([]entity.Account, error) {
	c := entity.GetAccountCollection(GetEngine())
	var accounts []entity.Account
	_, err := FindMany(ctx, c, DefaultFilter(ctx), &accounts)
	if err != nil {
		return nil, err
	}
	return accounts, nil
}

// FindByAType ...
func (a Account) FindByAType(ctx context.Context, aType string) ([]entity.Account, error) {
	c := entity.GetAccountCollection(GetEngine())
	var accounts []entity.Account
	filter := DefaultFilter(ctx)
	filter = append(filter, bson.E{Key: "a_type", Value: aType})
	_, err := FindMany(ctx, c, filter, &accounts)
	if err != nil {
		return nil, err
	}
	return accounts, nil
}

// Get 获取详情
func (a Account) Get(ctx context.Context, id string, showSecret ...bool) (*entity.Account, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	c := entity.GetAccountCollection(GetEngine())
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)

	var accountEntity entity.Account
	_, err = FindOne(ctx, c, filter, &accountEntity)
	if err != nil {
		return nil, err
	}

	if len(showSecret) != 0 && !showSecret[0] {
		accountEntity.AccessSecret = "666666"
	}

	return &accountEntity, err
}

// FindManyWithPK xxx
func (a Account) FindManyWithPK(ctx context.Context, ids []string) ([]*entity.Account, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetAccountCollection(GetEngine())

	filter := DefaultFilter(ctx, bson.E{
		"_id", map[string]interface{}{"$in": obj},
	})

	var list []*entity.Account
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindPK 根据主键查询
func (a Account) FindPK(ctx context.Context, id string) (*entity.Account, error) {
	obdID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	c := entity.GetAccountCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "_id", Value: obdID})

	var ac *entity.Account
	_, err = FindOne(ctx, c, filter, &ac)
	if err != nil {
		return nil, err
	}

	return ac, nil
}

// FindManyWithPkToMap to map
func (a Account) FindManyWithPkToMap(ctx context.Context, ids []string) (map[string]entity.Account, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetAccountCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		"_id", map[string]interface{}{"$in": obj},
	})

	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	m := map[string]entity.Account{}
	for cur.Next(ctx) {
		var r entity.Account
		if err = cur.Decode(&r); err != nil {
			return nil, err
		}
		m[r.ID.Hex()] = r
	}

	return m, nil
}

// Create 创建
func (a Account) Create(ctx context.Context, account *entity.Account) (string, error) {
	account.ID = primitive.NewObjectID()
	d, err := mapstruct.Struct2Map(account)
	if err != nil {
		return "", err
	}

	BeforeInsert(d)
	c := entity.GetAccountCollection(GetEngine())
	result, err := c.InsertOne(ctx, d)
	if err != nil {
		return "", err
	}
	if s, ok := result.InsertedID.(primitive.ObjectID); ok {
		return s.Hex(), nil
	}
	return "", nil
}

// IspExist 判断云厂商是否已存在
func (a Account) IspExist(ctx context.Context, ispType string) bool {
	c := entity.GetAccountCollection(GetEngine())
	err := c.FindOne(ctx, DefaultFilter(ctx, bson.E{Key: "a_type", Value: ispType})).Err()
	return err == nil
}

// Update update
func (a Account) Update(ctx context.Context, id string, data map[string]interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetAccountCollection(GetEngine())
	return UpdateMany(ctx, c, filter, data)
}

// Delete delete
func (a Account) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetAccountCollection(GetEngine())

	return Delete(ctx, c, filter)
}

// FiledOptions 表描述信息
func (a Account) FiledOptions(ctx context.Context) []*entity.FiledStruct {
	return entity.GetFieldMeta(entity.HostResourceEntity)
}

// accountDecode account decode
type accountDecode struct {
	Account  entity.Account           `bson:",inline"`
	SyncTask []map[string]interface{} `bson:"tasks"`
}

// ToDashBoard to dashboard
func (a Account) ToDashBoard(ctx context.Context) ([]*cloudman.DashBoardAccountDetail, error) {
	c := entity.GetAccountCollection(GetEngine())
	accountList := []entity.Account{}
	_, err := FindMany(ctx, c, bson.M{"status": 1, "is_delete": 0}, &accountList)
	if err != nil {
		return nil, err
	}

	rc := entity.GetRegionCollection(GetEngine())
	res := make([]*cloudman.DashBoardAccountDetail, 0)
	for _, x := range accountList {
		regionList := []entity.Region{}
		regionObjList := []primitive.ObjectID{}
		for _, rid := range x.RegionIDs {
			o, _ := primitive.ObjectIDFromHex(rid)
			regionObjList = append(regionObjList, o)
		}
		_, err := FindMany(ctx, rc, bson.M{"_id": bson.M{"$in": regionObjList}}, &regionList)
		if err != nil {
			return nil, err
		}
		rres := []*cloudman.DashBoardAccountRegion{}
		for _, r := range regionList {
			rres = append(rres, &cloudman.DashBoardAccountRegion{
				RegionId:   r.RegionID,
				RegionName: r.Name,
			})
		}
		res = append(res, &cloudman.DashBoardAccountDetail{
			AccountId:   x.ID.Hex(),
			AccountName: x.Name,
			AccountType: x.AType,
			Regions:     rres,
		})
	}
	return res, nil
}

// FindUserAccounts 查询用户所属云厂商
func (a Account) FindUserAccounts(ctx context.Context) ([]string, error) {
	isAdmin := permission.IsAdminFromCtx(ctx)

	c := entity.GetAccountCollection(GetEngine())

	filter := DefaultFilter(ctx)
	if !isAdmin {
		// filter = append(filter, bson.E{
		//	"user_ids", map[string]interface{}{"$in": []string{username}},
		// })
	}

	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	ispIDs := make([]string, 0)
	for cur.Next(ctx) {
		var r entity.Account
		if err = cur.Decode(&r); err != nil {
			return nil, err
		}
		ispIDs = append(ispIDs, r.ID.Hex())
	}
	return ispIDs, nil
}

// HasAccountPermission 检查用户是否有该云厂商权限
func (a Account) HasAccountPermission(ctx context.Context, id string) (bool, error) {
	accounts, err := a.FindUserAccounts(ctx)
	if err != nil {
		return false, nil
	}
	for _, v := range accounts {
		if v == id {
			return true, nil
		}
	}
	return false, nil
}
