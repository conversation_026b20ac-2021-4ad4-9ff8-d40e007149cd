package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// PolicyModel zone-model
var PolicyModel = new(Policy)

// Policy zone
type Policy struct {
}

func (r Policy) Query(ctx context.Context, params *schema.PolicyQueryParam, customFilter ...map[string]interface{}) ([]*entity.Policy, uint64, error) {
	c := entity.GetPolicyCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))
	if len(customFilter) != 0 {
		filter = append(filter, mapToFilter(customFilter[0])...)
	}
	if columnFilter := ColumnFilter(params.PolicyColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.Policy, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.Policy{}.Ordering()} // 加入默认排序
	OrderFields = append(OrderFields, params.BuildToOrderFields()...)

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// CreateOrUpdate 创建/更新
func (r Policy) CreateOrUpdate(ctx context.Context, data *entity.Policy, isSync bool) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	action := "create"
	c := entity.GetPolicyCollection(GetEngine())
	filter := map[string]interface{}{
		"PolicyName": data.PolicyName,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.Policy
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		// 同步时使用之前的ip组信息
		if isSync {
			data.RelatedIpGroups = m.RelatedIpGroups
		}
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	if err != nil {
		return "", err
	}

	return action, err
}

// BatchCreateOrUpdate 批量创建或更新
func (r Policy) BatchCreateOrUpdate(ctx context.Context, data []*entity.Policy, isSync bool) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := v.PolicyName

		if v.PolicyName == "" {
			continue
		}
		action, err := r.CreateOrUpdate(ctx, v, isSync)
		if err != nil {
			errText := fmt.Sprintf("name: %s, errors: %v", v.PolicyName, err.Error())
			if action == "update" {
				result.UpdateErrors = append(result.UpdateErrors, errText)
			} else {
				result.Errors = append(result.Errors, errText)
			}
			continue
		}

		if action == "update" {
			result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
		} else {
			result.SuccessCreated = append(result.SuccessCreated, dbFlag)
		}
		result.Success = append(result.Success, dbFlag)
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

func PolicyToPb(ctx context.Context, data []*entity.Policy) ([]*cloudman.RAMPolicy, error) {
	res := make([]*cloudman.RAMPolicy, 0)
	var accountIds []string
	for _, a := range data {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(context.Background(), accountIds)
		if err != nil {
			return nil, err
		}
	}

	for _, e := range data {
		p := &cloudman.RAMPolicy{
			Id:                 e.ID.Hex(),
			Name:               e.PolicyName,
			Description:        e.Description,
			AttachUsers:        []*cloudman.AttachUser{},
			PolicyDocument:     e.DefaultPolicyDocument,
			CreateTime:         e.CreateDate,
			UpdateTime:         e.UpdateDate,
			RelatedIpGroups:    []string{},
			RelatedIpGroupsIds: e.RelatedIpGroups,
			RegionId:           e.RegionID,
			IspId:              e.IspID,
			IspType:            e.IspType,
			IspName:            ac[e.IspID].Name,
		}
		for _, u := range e.AttachUsers {
			p.AttachUsers = append(p.AttachUsers, &cloudman.AttachUser{
				DisplayName: u.DisplayName,
				UserId:      u.UserId,
				UserName:    u.UserName,
				AttachDate:  u.AttachDate,
			})
		}

		groups, err := IPGroupModel.FindManyWithPK(ctx, e.RelatedIpGroups)
		if err != nil {
			return nil, err
		}
		for _, g := range groups {
			p.RelatedIpGroups = append(p.RelatedIpGroups, g.Name)
		}

		res = append(res, p)
	}
	return res, nil
}

// Get xx
func (r Policy) Get(ctx context.Context, oid string) (*entity.Policy, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetPolicyCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var entity entity.Policy
	_, err = FindOne(ctx, c, filter, &entity)
	return &entity, err
}

type PolicyDiff struct {
	OrignalPolicy *entity.Policy
	AddIps        []string
	RemoveIps     []string
	NewDocument   string
}

// GetPolicyDiff ...
func (r Policy) GetPolicyDiff(ctx context.Context, ids []string, isAll bool) ([]*PolicyDiff, error) {
	var entities []entity.Policy
	var err error
	if isAll {
		entities, err = r.FindAll(ctx)
	} else {
		entities, err = r.FindManyWithPK(ctx, ids)
	}
	if err != nil {
		return nil, err
	}
	res := make([]*PolicyDiff, 0)
	for _, e := range entities {
		e := e
		ipGroups, err := IPGroupModel.FindManyWithPK(ctx, e.RelatedIpGroups)
		if err != nil {
			return nil, err
		}
		ipIDs := make([]string, 0)
		for _, group := range ipGroups {
			// 这里不做去重，下面查询的时候直接去重
			ipIDs = append(ipIDs, group.RelatedIPs...)
		}

		ips, err := IPModel.FindManyWithPK(ctx, ipIDs)
		if err != nil {
			return nil, err
		}
		sourceIPs := make([]string, 0)
		for _, ip := range ips {
			sourceIPs = append(sourceIPs, ip.Address)
		}

		oldIps, err := getSourceIPFromDocument(e.DefaultPolicyDocument)
		if err != nil {
			return nil, err
		}
		addIps, removeIps := diffSourceIPs(oldIps, sourceIPs)
		newDocument, err := generateNewDocument(sourceIPs, e.DefaultPolicyDocument)
		if err != nil {
			return nil, err
		}
		res = append(res, &PolicyDiff{
			OrignalPolicy: &e,
			AddIps:        addIps,
			RemoveIps:     removeIps,
			NewDocument:   newDocument,
		})
	}

	return res, err
}

type PolicyDocument struct {
	Version   string       `json:"Version"`
	Statement []*Statement `json:"Statement"`
}

type Statement struct {
	Action    interface{} `json:"Action"`
	Resource  interface{} `json:"Resource"`
	Effect    string      `json:"Effect"`
	Condition *Condition  `json:"Condition"`
}

type Condition struct {
	IpAddress IpAddress `json:"IpAddress"`
}

type IpAddress struct {
	SourceIP []string `json:"acs:SourceIp"`
}

func getSourceIPFromDocument(document string) ([]string, error) {
	pDocument := &PolicyDocument{}
	err := json.Unmarshal([]byte(document), pDocument)
	if err != nil {
		return nil, err
	}

	if len(pDocument.Statement) == 0 {
		return nil, fmt.Errorf("原策略为空")
	}

	// 这里现在document没有statement会panic
	return pDocument.Statement[0].Condition.IpAddress.SourceIP, nil
}

func generateNewDocument(newIps []string, oldDocument string) (string, error) {
	pDocument := PolicyDocument{}
	err := json.Unmarshal([]byte(oldDocument), &pDocument)
	if err != nil {
		return "", err
	}

	if len(pDocument.Statement) == 0 {
		return "", fmt.Errorf("原策略为空")
	}

	pDocument.Statement[0].Condition.IpAddress.SourceIP = newIps

	res, err := json.Marshal(pDocument)
	if err != nil {
		return "", err
	}
	return string(res), nil
}

// diffSourceIPs 以previous为基准删选增加和删除的ip
func diffSourceIPs(previous []string, next []string) ([]string, []string) {
	adds := make([]string, 0)
	removes := make([]string, 0)

	previousSet := make(map[string]interface{})
	nextSet := make(map[string]interface{})
	for _, i := range previous {
		previousSet[i] = true
	}

	for _, i := range next {
		nextSet[i] = true
		if _, ok := previousSet[i]; !ok {
			adds = append(adds, i)
		}
	}
	for _, i := range previous {
		if _, ok := nextSet[i]; !ok {
			removes = append(removes, i)
		}
	}

	return adds, removes
}

// FindManyWithPK find-many
func (r Policy) FindManyWithPK(ctx context.Context, ids []string) ([]entity.Policy, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetPolicyCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.Policy
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindALL
func (r Policy) FindAll(ctx context.Context) ([]entity.Policy, error) {
	c := entity.GetPolicyCollection(GetEngine())

	var list []entity.Policy
	filter := DefaultFilter(ctx)
	_, err := FindMany(ctx, c, filter, &list)
	return list, err
}

func (r Policy) MarkCleanup(ctx context.Context, ispID, regionID string, latestUpdateVersion string) (int64, error) {
	e := entity.GetPolicyCollection(GetEngine())
	_, err := e.UpdateMany(ctx,
		bson.D{
			{Key: "IspID", Value: ispID},
			{Key: "RegionID", Value: regionID},
			{Key: "is_delete", Value: 0},
		},
		bson.D{{Key: "$set", Value: bson.M{"NeedCleanup": false}}})
	if err != nil {
		return 0, err
	}
	res, err := e.UpdateMany(ctx,
		bson.D{
			{Key: "IspID", Value: ispID},
			{Key: "RegionID", Value: regionID},
			{Key: "UpdateVersion", Value: bson.D{{Key: "$ne", Value: latestUpdateVersion}}},
			{Key: "is_delete", Value: 0},
		},
		bson.D{{Key: "$set", Value: bson.M{"NeedCleanup": true}}})
	if err != nil {
		return 0, err
	}
	return res.MatchedCount, nil
}
