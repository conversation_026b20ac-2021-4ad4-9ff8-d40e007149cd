package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// ResourceGroup resource-group-model
var ResourceGroup = new(resourceGroup)

// ResourceGroup resource-group
type resourceGroup struct {
}

// Query query
func (r resourceGroup) Query(ctx context.Context, params *schema.ResourceGroupQueryParams) ([]entity.ResGroup, uint64, error) {
	c := entity.GetResGroupCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.ResourceGroupColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.ResGroup, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.ResGroupEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get get
func (r resourceGroup) Get(ctx context.Context, oid string) (*entity.ResGroup, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetResGroupCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var resGroupEntity entity.ResGroup
	_, err = FindOne(ctx, c, filter, &resGroupEntity)
	return &resGroupEntity, err
}

// Create create
func (r resourceGroup) Create(ctx context.Context, data *entity.ResGroup) error {
	// t := time.Now()
	data.ID = primitive.NewObjectID()
	data.CreatedTime = time.Now().Unix()
	c := entity.GetResGroupCollection(GetEngine())

	return Insert(ctx, c, data)
}

// Update xxx
func (r resourceGroup) Update(ctx context.Context, id string, updateData interface{}, queryOptions ...map[string]interface{}) error {
	data, err := mapstruct.Struct2Map(updateData)
	if err != nil {
		return err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	if len(queryOptions) != 0 {
		filter = append(filter, mapToFilter(queryOptions[0])...)
	}

	c := entity.GetResGroupCollection(GetEngine())
	return UpdateMany(ctx, c, filter, data)
}
