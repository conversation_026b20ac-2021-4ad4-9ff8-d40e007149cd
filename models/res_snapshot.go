package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// ResSnapshotModel zone-model
var ResSnapshotModel = new(ResSnapshot)

// ResSnapshot zone
type ResSnapshot struct {
}

// Get get
func (r ResSnapshot) Get(ctx context.Context, oid string) (*entity.ResSnapshot, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetResSnapshotCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var zoneEntity entity.ResSnapshot
	_, err = FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// FindManyWithPK find-many
func (r ResSnapshot) FindManyWithPK(ctx context.Context, ids []string) ([]entity.ResSnapshot, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetResSnapshotCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.ResSnapshot
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindOrCreate 不存在则创建
func (r ResSnapshot) FindOrCreate(ctx context.Context, data *entity.ResSnapshot) error {
	c := entity.GetResSnapshotCollection(GetEngine())
	err := c.FindOne(ctx, bson.D{
		{"account_id", data.AccountID},
		{"res_type", data.ResType},
		{"region", data.Region},
		{"day_time", data.DayTime}}).Err()
	data.CreatedTime = time.Now().Unix()
	data.UpdatedTime = data.CreatedTime
	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		err = Insert(ctx, c, data)
	}
	return err
}

// FindOne 不存在则创建
func (r ResSnapshot) FindOne(ctx context.Context, data *entity.ResSnapshot) (*entity.ResSnapshot, error) {
	var res *entity.ResSnapshot
	c := entity.GetResSnapshotCollection(GetEngine())
	filter := DefaultFilter(ctx,
		bson.E{Key: "account_id", Value: data.AccountID},
		bson.E{Key: "res_type", Value: data.ResType},
		bson.E{Key: "region", Value: data.Region},
		bson.E{Key: "day_time", Value: data.DayTime})
	_, err := FindOne(ctx, c, filter, res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// CreateOrUpdateDailyStatus 创建或更新
func (r ResSnapshot) CreateOrUpdateDailyStatus(ctx context.Context, searchFilter bson.D, data *entity.ResSnapshot) error {
	c := entity.GetResSnapshotCollection(GetEngine())
	findData := &entity.ResSnapshot{}
	_, err := FindOne(ctx, c, searchFilter, findData)
	if err == mongo.ErrNoDocuments {
		data.CreatedTime = data.UpdatedTime
		data.ID = primitive.NewObjectID()
		err = Insert(ctx, c, data)
	} else if err == nil {
		findData.Region = data.Region
		findData.ResType = data.ResType
		findData.TotalCount = data.TotalCount
		err = UpdateOne(ctx, c, searchFilter, findData)
	}
	return err
}

// BatchFindOrCreate 批量更新或创建
func (r ResSnapshot) BatchFindOrCreate(ctx context.Context, ispType string, data []*entity.ResSnapshot) error {
	for _, v := range data {
		err := r.FindOrCreate(ctx, v)
		if err != nil {
			return err
		}
	}

	return nil
}

// Delete ...
func (r ResSnapshot) Delete(ctx context.Context, searchFilter bson.D) error {
	c := entity.GetResSnapshotCollection(GetEngine())
	return Delete(ctx, c, searchFilter)
}

// GetDailyCount ...
func (r ResSnapshot) GetDailyCount(ctx context.Context, accountID, regionID, resType string, start, end int32) ([]DailyCount, error) {
	c := entity.GetResSnapshotCollection(GetEngine())
	filter := DefaultFilter(ctx,
		bson.E{Key: "day_time", Value: map[string]interface{}{"$gt": start, "$lt": end}},
		bson.E{Key: "account_id", Value: accountID},
	)
	if regionID != "all" {
		filter = append(filter, bson.E{Key: "region", Value: regionID})
	}
	if resType != "all" {
		filter = append(filter, bson.E{Key: "res_type", Value: resType})
	}
	var list []*entity.ResSnapshot
	var dailyCounts []DailyCount
	options := &options.FindOptions{}
	options.SetSort(bson.D{{"day_time", 1}})
	_, err := FindMany(ctx, c, filter, &list, options)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return dailyCounts, nil
		}
		return nil, err
	}

	var lastCount = 0
	var lastDay int64 = 0
	totalCount := 0
	var firstDay = true
	for k, v := range list {
		if k == 0 {
			lastDay = v.DayTime
		}
		if lastDay != v.DayTime {
			if firstDay {
				lastCount = totalCount
				firstDay = false
			}
			dailyCounts = addDailyCount(dailyCounts, lastDay, lastCount, totalCount)
			lastCount = totalCount
			totalCount = 0
			lastDay = v.DayTime
		}
		totalCount += v.TotalCount
		if k == len(list)-1 {
			dailyCounts = addDailyCount(dailyCounts, v.DayTime, lastCount, totalCount)
		}
	}
	return dailyCounts, nil
}

func addDailyCount(d []DailyCount, day int64, lastCount, totalCount int) []DailyCount {
	created, deleted := 0, 0
	if lastCount > totalCount {
		deleted = lastCount - totalCount
	} else {
		created = totalCount - lastCount
	}
	return append(d, DailyCount{
		DayName: time.Unix(day, 0).Format("2006-01-02"),
		State:   totalCount,
		Created: created,
		Deleted: deleted,
	})
}
