package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetMysqlClusterEndpointResCollection 获取Mysql资源collection
func GetMysqlClusterEndpointResCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(MysqlClusterEndpointDBResourceEntity.CollectionName())
	return collection
}

// MysqlClusterEndpointDBResourceEntity entity
var MysqlClusterEndpointDBResourceEntity = new(MysqlClusterEndpointResource)

// MysqlClusterEndpointResource 连接地址配置
type MysqlClusterEndpointResource struct {
	Model                 `bson:",inline"`
	IsLock                bool                       `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	IspID                 string                     `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
	IspType               string                     `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`
	DBClusterID           string                     `bson:"DBClusterID" json:"DBClusterID,omitempty" xml:"DBClusterID,omitempty" field:"所属集群ID"`
	NodeWithRoles         string                     `bson:"NodeWithRoles" json:"NodeWithRoles,omitempty" xml:"NodeWithRoles,omitempty" field:"角色名称"`
	Nodes                 string                     `bson:"Nodes" json:"Nodes,omitempty" xml:"Nodes,omitempty" field:"节点列表"`
	ReadWriteMode         string                     `bson:"ReadWriteMode" json:"ReadWriteMode,omitempty" xml:"ReadWriteMode,omitempty" field:"读写模式"`
	AddressItems          []MysqlClusterAddressItems `bson:"AddressItems" json:"AddressItems,omitempty" xml:"AddressItems,omitempty" type:"Repeated" field:"集群item"`
	DBEndpointID          string                     `bson:"DBEndpointId" json:"DBEndpointId,omitempty" xml:"DBEndpointId,omitempty" field:"连接地址ID"`
	EndpointConfig        string                     `bson:"EndpointConfig" json:"EndpointConfig,omitempty" xml:"EndpointConfig,omitempty" field:"高级配置"`
	DBEndpointDescription string                     `bson:"DBEndpointDescription" json:"DBEndpointDescription,omitempty" xml:"DBEndpointDescription,omitempty" field:"自定义集群地址名称"`
	EndpointType          string                     `bson:"EndpointType" json:"EndpointType,omitempty" xml:"EndpointType,omitempty" field:"集群地址类型"`
	AutoAddNewNodes       string                     `bson:"AutoAddNewNodes" json:"AutoAddNewNodes,omitempty" xml:"AutoAddNewNodes,omitempty" field:"是否自动加入默认集群地址"`
}

// MysqlClusterAddressItems 集群item
type MysqlClusterAddressItems struct {
	VSwitchID                   string `bson:"VSwitchId" json:"VSwitchId,omitempty" xml:"VSwitchId,omitempty" field:"虚拟交换机ID"`
	PrivateZoneConnectionString string `bson:"PrivateZoneConnectionString" json:"PrivateZoneConnectionString,omitempty" xml:"PrivateZoneConnectionString,omitempty" field:"私有域名"`
	ConnectionString            string `bson:"ConnectionString" json:"ConnectionString,omitempty" xml:"ConnectionString,omitempty" field:"连接串"`
	NetType                     string `bson:"NetType" json:"NetType,omitempty" xml:"NetType,omitempty" field:"网络类型"`
	Port                        string `bson:"Port" json:"Port,omitempty" xml:"Port,omitempty" field:"端口"`
	VpcInstanceID               string `bson:"VpcInstanceId" json:"VpcInstanceId,omitempty" xml:"VpcInstanceId,omitempty" field:"VPC实例ID"`
	VPCID                       string `bson:"VPCId" json:"VPCId,omitempty" xml:"VPCId,omitempty" field:"专有网络ID"`
	IPAddress                   string `bson:"IPAddress" json:"IPAddress,omitempty" xml:"IPAddress,omitempty" field:"IP地址"`
}

// CollectionName 定义collection name
func (h MysqlClusterEndpointResource) CollectionName() string {
	return h.Model.CollectionName("mysql_cluster_endpoint_resource")
}

// CreateIndexes 创建索引
func (h MysqlClusterEndpointResource) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return h.Model.CreateIndexes(ctx, cli, h, []mongo.IndexModel{
		{Keys: bson.M{"DBClusterID": 1}},
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"isp_type": 1}},
		{Keys: bson.M{"is_lock": 1}},
		{Keys: bson.M{"DBEndpointId": 1}},
	})
}

// Meta 描述信息
func (h MysqlClusterEndpointResource) Meta() []*FiledStruct {
	return GetFieldMeta(h)
}

// Ordering 默认排序
func (h MysqlClusterEndpointResource) Ordering() *schema.OrderField {
	return h.Model.Ordering()
}
