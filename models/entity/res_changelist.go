package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResChangelistCollection ResChangelist collection
func GetResChangelistCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResChangelistEntity.CollectionName())
	return collection
}

// ResChangelistEntity entity
var ResChangelistEntity = new(ResChangelist)

// ResChangelist 云管agent任务记录
// 部分字段与meta中的重复，方便可读性
// 重复的包括：runType/methodName/agentID，以extra内数据为准
type ResChangelist struct {
	Model        `bson:",inline"`
	AccountID    string `bson:"account_id" json:"account_id"`
	ResourceType string `bson:"resource_type" json:"resource_type"`
	InstanceID   string `bson:"instance_id" json:"instance_id"`
	InstanceName string `bson:"instance_name" json:"instance_name"`
	Field        string `bson:"field" json:"field"`
	Before       string `bson:"before" json:"before"`
	After        string `bson:"after" json:"after"`
}

// CollectionName 定义collection name
func (a ResChangelist) CollectionName() string {
	return a.Model.CollectionName("res_changelist")
}

// CreateIndexes 创建索引
func (a ResChangelist) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"instance_id": 1}},
		{Keys: bson.M{"account_id": 1}},
		{Keys: bson.M{"field": 1}},
	})
}

// Ordering 默认排序
func (a ResChangelist) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
