package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
)

// GetLBServerGroupCollection 获取collection
func GetLBServerGroupCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(LBServerGroupEntity.CollectionName())
	return collection
}

// LBServerGroupEntity entity
var LBServerGroupEntity = new(LBServerGroup)

type LBServerGroup struct {
	UserModel         `bson:",inline"`
	ServerGroupID     string                 `bson:"ServerGroupId" json:"ServerGroupId"`
	ServerGroupName   string                 `bson:"ServerGroupName" json:"ServerGroupName"`
	ResourceGroupID   string                 `bson:"ResourceGroupId" json:"ResourceGroupId"`
	Scheduler         string                 `bson:"Scheduler" json:"Scheduler"`
	CreateTime        string                 `bson:"CreateTime" json:"CreateTime"`
	Servers           []*LBServerGroupServer `bson:"Servers" json:"Servers"`
	ServerGroupType   string                 `bson:"ServerGroupType" json:"ServerGroupType"`
	ServerGroupStatus string                 `bson:"ServerGroupStatus" json:"ServerGroupStatus"`
	VpcID             string                 `bson:"VpcId" json:"VpcId"`
	Protocol          string                 `json:"Protocol"`
	IspID             string                 `bson:"isp_id" json:"isp_id" field:"云厂商ID"`
	IspType           string                 `bson:"isp_type" json:"isp_type" field:"云厂商类型"`
}

type LBServerGroupServer struct {
	ServerID   string `bson:"ServerId" json:"ServerId"`
	ServerType string `bson:"ServerType" json:"ServerType"`
	Status     string `bson:"Status" json:"Status"`
	ServerIP   string `bson:"ServerIp" json:"ServerIp"`
	Port       int32  `bson:"Port" json:"Port"`
	Weight     int32  `bson:"Weight" json:"Weight"`
}

// CollectionName 定义collection name
func (a LBServerGroup) CollectionName() string {
	return a.UserModel.CollectionName("load_balancer_server_group")
}

// CreateIndexes 创建索引
func (a LBServerGroup) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"ServerGroupID": 1}},
	})
}
