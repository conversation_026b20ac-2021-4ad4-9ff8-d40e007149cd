package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetMysqlClusterResCollection 获取Mysql资源collection
func GetMysqlClusterResCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(MysqlClusterDBResourceEntity.CollectionName())
	return collection
}

// MysqlClusterDBResourceEntity entity
var MysqlClusterDBResourceEntity = new(MysqlClusterResource)

// MysqlClusterResource 主机资源
type MysqlClusterResource struct {
	Model                `bson:",inline"`
	IsLock               bool                     `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	IspID                string                   `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
	IspType              string                   `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`
	Recyclable           bool                     `bson:"Recyclable" json:"Recyclable,omitempty" xml:"Recyclable,omitempty" field:"实例是否可以回收"`
	VpcID                string                   `bson:"VpcId" json:"VpcId,omitempty" xml:"VpcId,omitempty" field:"专有网络ID"`
	ExpireTime           string                   `bson:"ExpireTime" json:"ExpireTime,omitempty" xml:"ExpireTime,omitempty" field:"集群到期时间"`
	Expired              string                   `bson:"Expired" json:"Expired,omitempty" xml:"Expired,omitempty" field:"集群是否到期"`
	DBNodeNumber         int32                    `bson:"DBNodeNumber" json:"DBNodeNumber,omitempty" xml:"DBNodeNumber,omitempty" field:"节点数量"`
	CreateTime           string                   `bson:"CreateTime" json:"CreateTime,omitempty" xml:"CreateTime,omitempty" field:"创建时间"`
	PayType              string                   `bson:"PayType" json:"PayType,omitempty" xml:"PayType,omitempty" field:"付费类型"`
	DBNodeClass          string                   `bson:"DBNodeClass" json:"DBNodeClass,omitempty" xml:"DBNodeClass,omitempty" field:"节点规格"`
	Tags                 MysqlClusterTags         `bson:"Tags" json:"Tags,omitempty" xml:"Tags,omitempty" type:"Struct" field:"标签集合"`
	DBType               string                   `bson:"DBType" json:"DBType,omitempty" xml:"DBType,omitempty" field:"数据库类型" validate:"required"`
	LockMode             string                   `bson:"LockMode" json:"LockMode,omitempty" xml:"LockMode,omitempty" field:"集群的锁定状态"`
	DBNodes              MysqlClusterDBNodes      `bson:"DBNodes" json:"DBNodes,omitempty" xml:"DBNodes,omitempty" type:"Struct" field:"集群节点详情"`
	RegionID             string                   `bson:"RegionID" json:"RegionID,omitempty" xml:"RegionID,omitempty" field:"地域ID"`
	DeletionLock         int32                    `bson:"DeletionLock" json:"DeletionLock,omitempty" xml:"DeletionLock,omitempty" field:"集群删除的保护状态"`
	DBVersion            string                   `bson:"DBVersion" json:"DBVersion,omitempty" xml:"DBVersion,omitempty" field:"数据库版本" validate:"required"`
	DBClusterID          string                   `bson:"DBClusterId" json:"DBClusterId,omitempty" xml:"DBClusterId,omitempty" field:"集群ID" validate:"required" indexed:"1"`
	DBClusterStatus      string                   `bson:"DBClusterStatus" json:"DBClusterStatus,omitempty" xml:"DBClusterStatus,omitempty" field:"集群状态"`
	ResourceGroupID      string                   `bson:"ResourceGroupId" json:"ResourceGroupId,omitempty" xml:"ResourceGroupId,omitempty" field:"资源组ID"`
	StorageUsed          int64                    `bson:"StorageUsed" json:"StorageUsed,omitempty" xml:"StorageUsed,omitempty" field:"存储用量"`
	DBClusterNetworkType string                   `bson:"DBClusterNetworkType" json:"DBClusterNetworkType,omitempty" xml:"DBClusterNetworkType,omitempty" field:"集群的网络类型"`
	DBClusterDescription string                   `bson:"DBClusterDescription" json:"DBClusterDescription,omitempty" xml:"DBClusterDescription,omitempty" field:"集群描述"`
	ZoneID               string                   `bson:"ZoneID" json:"ZoneID,omitempty" xml:"ZoneID,omitempty" field:"可用区ID"`
	Engine               string                   `bson:"Engine" json:"Engine,omitempty" xml:"Engine,omitempty" field:"集群引擎" validate:"required"`
	Description          string                   `bson:"Description" json:"Description" xml:"Description,omitempty" field:"实例描述" indexed:"1"`
	UpdateVersion        string                   `bson:"UpdateVersion" json:"UpdateVersion,omitempty"`
	NeedCleanup          bool                     `bson:"NeedCleanup" json:"NeedCleanup,omitempty"`
	InitTreeNode         string                   `bson:"initTreeNode" json:"initTreeNode,omitempty"`
	SecurityGroupIds     InstanceSecurityGroupIds `bson:"SecurityGroupIds" json:"SecurityGroupIds,omitempty" xml:"SecurityGroupIds,omitempty" type:"Struct" field:"安全组ID"`
	IPGroups             []InstanceIPGroup        `bson:"IPGroups" json:"IPGroups,omitempty" xml:"IPGroups,omitempty"`
	IPWhitelists         []InstanceIPWhitelist    `bson:"IPWhitelists" json:"IPWhitelists,omitempty" xml:"IPWhitelists,omitempty"`
}

type InstanceIPWhitelist struct {
	GIpList               string `bson:"GIpList" json:"GIpList,omitempty" xml:"GIpList,omitempty"`
	GlobalIgName          string `bson:"GlobalIgName" json:"GlobalIgName,omitempty" xml:"GlobalIgName,omitempty"`
	GlobalSecurityGroupId string `bson:"GlobalSecurityGroupId" json:"GlobalSecurityGroupId,omitempty" xml:"GlobalSecurityGroupId,omitempty"`
}

// MysqlClusterTags 集群标签
type MysqlClusterTags struct {
	Tag []Tag `bson:"Tag" json:"Tag,omitempty" xml:"Tag,omitempty" type:"Repeated" field:"标签集合"`
}

// MysqlClusterDBNodes mysql节点详情
type MysqlClusterDBNodes struct {
	DBNode []MysqlClusterDBNode `bson:"DBNode" json:"DBNode,omitempty" xml:"DBNode,omitempty" type:"Repeated"`
}

// MysqlClusterDBNode mysql节点详情
type MysqlClusterDBNode struct {
	DBNodeClass string `bson:"DBNodeClass" json:"DBNodeClass,omitempty" xml:"DBNodeClass,omitempty" field:"节点规格"`
	ZoneID      string `bson:"ZoneID" json:"ZoneID,omitempty" xml:"ZoneID,omitempty" field:"可用区ID"`
	DBNodeRole  string `bson:"DBNodeRole" json:"DBNodeRole,omitempty" xml:"DBNodeRole,omitempty" field:"节点角色"`
	DBNodeID    string `bson:"DBNodeId" json:"DBNodeId,omitempty" xml:"DBNodeId,omitempty" field:"节点ID"`
	RegionID    string `bson:"RegionID" json:"RegionID,omitempty" xml:"RegionID,omitempty" field:"地域ID"`
}

// CollectionName 定义collection name
func (h MysqlClusterResource) CollectionName() string {
	return h.Model.CollectionName("mysql_cluster_resource")
}

// CreateIndexes 创建索引
func (h MysqlClusterResource) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return h.Model.CreateIndexes(ctx, cli, h, []mongo.IndexModel{
		{Keys: bson.M{"DBClusterId": 1}},
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"isp_type": 1}},
		{Keys: bson.M{"is_lock": 1}},
		{Keys: bson.M{"DBClusterStatus": 1}},
	})
}

// Meta 描述信息
func (h MysqlClusterResource) Meta() []*FiledStruct {
	extraFields := []*FiledStruct{
		{
			Name:    "标签键",
			Type:    "string",
			Value:   "Tags.Tag.TagKey",
			Extra:   true,
			Indexed: true,
		},
		{
			Name:    "标签值",
			Type:    "string",
			Value:   "Tags.Tag.TagValue",
			Extra:   true,
			Indexed: true,
		},
	}
	options := GetFieldMeta(h)
	options = append(options, extraFields...)

	return options
}

// Ordering 默认排序
func (h MysqlClusterResource) Ordering() *schema.OrderField {
	return h.Model.Ordering()
}

// GetInstanceID 获取实例ID
func (h MysqlClusterResource) GetInstanceID() string {
	return h.DBClusterID
}

// GetResourceIsp 获取厂商信息
func (h MysqlClusterResource) GetResourceIsp() ResourceBase {
	return ResourceBase{
		IspID:    h.IspID,
		IspType:  h.IspType,
		RegionID: h.RegionID,
	}
}

// TransStatus ...
func (h MysqlClusterResource) TransStatus() string {
	if h.DBClusterStatus == "available" {
		return "Running"
	}
	return h.DBClusterStatus
}
