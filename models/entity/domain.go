package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetDomainCollection 获取loadBalancer collection
func GetDomainCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(DomainEntity.CollectionName())
	return collection
}

// DomainEntity entity
var DomainEntity = new(Domain)

// Domain ...
type Domain struct {
	UserModel        `bson:",inline"`
	DomainID         int       `json:"DomainId" bson:"DomainId"`
	Name             string    `json:"Name" bson:"Name"`
	Status           string    `json:"Status" bson:"Status"`
	TTL              int       `json:"TTL" bson:"TTL"`
	CNAMESpeedup     string    `json:"CNAMESpeedup" bson:"CNAMESpeedup"`
	DNSStatus        string    `json:"DNSStatus" bson:"DNSStatus"`
	Grade            string    `json:"Grade" bson:"Grade"`
	GroupID          int       `json:"GroupId" bson:"GroupId"`
	SearchEnginePush string    `json:"SearchEnginePush" bson:"SearchEnginePush"`
	Remark           string    `json:"Remark" bson:"Remark"`
	Punycode         string    `json:"Punycode" bson:"Punycode"`
	EffectiveDNS     []string  `json:"EffectiveDNS" bson:"EffectiveDNS"`
	GradeLevel       int       `json:"GradeLevel" bson:"GradeLevel"`
	GradeTitle       string    `json:"GradeTitle" bson:"GradeTitle"`
	IsVip            string    `json:"IsVip" bson:"IsVip"`
	VipStartAt       string    `json:"VipStartAt" bson:"VipStartAt"`
	VipEndAt         string    `json:"VipEndAt" bson:"VipEndAt"`
	VipAutoRenew     string    `json:"VipAutoRenew" bson:"VipAutoRenew"`
	RecordCount      int       `json:"RecordCount" bson:"RecordCount"`
	CreatedOn        string    `json:"CreatedOn" bson:"CreatedOn"`
	UpdatedOn        string    `json:"UpdatedOn" bson:"UpdatedOn"`
	Owner            string    `json:"Owner" bson:"Owner"`
	TagList          []Tag     `json:"TagList" bson:"TagList"`
	IspID            string    `bson:"isp_id" json:"isp_id" field:"云厂商ID"`
	IspType          string    `bson:"isp_type" json:"isp_type" field:"云厂商类型"`
	RecordList       []*Record `bson:"record_list" json:"record_list"`
	RegionID         string    `bson:"region_id" json:"region_id"`
}

// Record ...
type Record struct {
	RecordID      uint64 `json:"RecordId" bson:"recordId"`
	Value         string `json:"Value" bson:"value"`
	Status        string `json:"Status" bson:"status"`
	UpdatedOn     string `json:"UpdatedOn" bson:"updatedOn"`
	Name          string `json:"Name" bson:"name"`
	Line          string `json:"Line" bson:"line"`
	LineID        string `json:"LineId" bson:"lineId"`
	Type          string `json:"Type" bson:"type"`
	Weight        uint64 `json:"Weight" bson:"weight"`
	MonitorStatus string `json:"MonitorStatus" bson:"monitorStatus"`
	Remark        string `json:"Remark" bson:"remark"`
	TTL           uint64 `json:"TTL" bson:"ttl"`
	MX            uint64 `json:"MX" bson:"mx"`
	DefaultNS     bool   `json:"DefaultNS" bson:"defaultNS"`
}

// DomainAddressOperationLocks ...
type DomainAddressOperationLocks struct {
	LockReason string `json:"LockReason" bson:"LockReason"`
}

// CollectionName 定义collection name
func (a Domain) CollectionName() string {
	return a.UserModel.CollectionName("domain")
}

// CreateIndexes 创建索引
func (a Domain) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"DomainId": 1}},
	})
}

// Ordering 默认排序
func (a Domain) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
