package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetAlbACLCollection 获取ip whitelist collection
func GetAlbACLCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(AlbACLEntity.CollectionName())
	return collection
}

// AlbACLEntity entity
var AlbACLEntity = new(AlbACL)

type AlbACL struct {
	UserModel        `bson:",inline"`
	ACLStatus        string             `json:"AclStatus" bson:"AclStatus"`
	ResourceGroupID  string             `json:"ResourceGroupId" bson:"ResourceGroupId"`
	ACLID            string             `json:"AclId" bson:"AclId"`
	AddressIPVersion string             `json:"AddressIPVersion" bson:"AddressIPVersion"`
	CreateTime       string             `json:"CreateTime" bson:"CreateTime"`
	ACLName          string             `json:"AclName" bson:"AclName"`
	AclEntries       []*ACLEntry        `json:"AclEntries" bson:"AclEntries"`
	RelatedListeners []*RelatedListener `json:"RelatedListeners" bson:"RelatedListeners"`
	RegionID         string             `json:"RegionId" bson:"RegionId"`
	IspID            string             `json:"IspID" bson:"IspID"`
	IspType          string             `json:"IspType" bson:"IspType"`
}

type ACLEntry struct {
	Status      string `json:"Status" bson:"Status"`
	Entry       string `json:"Entry" bson:"Entry"`
	Description string `json:"Description" bson:"Description"`
}

type RelatedListener struct {
	Status           string `json:"Status" bson:"Status"`
	ListenerPort     int32  `json:"ListenerPort" bson:"ListenerPort"`
	LoadBalancerID   string `json:"LoadBalancerId" bson:"LoadBalancerId"`
	ListenerProtocol string `json:"ListenerProtocol" bson:"ListenerProtocol"`
	ListenerID       string `json:"ListenerId" bson:"ListenerId"`
}

func (a AlbACL) CollectionName() string {
	return a.UserModel.CollectionName("alb_acl")
}

// CreateIndexes 创建索引
func (a AlbACL) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"AclId": 1}},
	})
}

// Ordering 默认排序
func (a AlbACL) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
