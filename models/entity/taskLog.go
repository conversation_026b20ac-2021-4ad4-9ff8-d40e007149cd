package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetTaskLogCollection 获取account collection
func GetTaskLogCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(TaskLogEntity.CollectionName())
	return collection
}

// TaskLogEntity 任务执行日志entity
var TaskLogEntity = new(TaskLog)

// TaskLog 任务执行日志
type TaskLog struct {
	UserModel      `bson:",inline"`
	Name           string `bson:"name" json:"name"`                 // 任务名称
	SyncTaskID     string `bson:"sync_task_id" json:"sync_task_id"` // 任务来源
	AccountID      string `bson:"account_id" json:"account_id"`     // 云账户ID
	AccountName    string `bson:"account_name" json:"account_name"` // 执行对象名称
	BindRegionID   string `bson:"bind_region_id" json:"bind_region_id"`
	BindRegionName string `bson:"bind_region_name" json:"bind_region_name"`
	TaskType       string `bson:"task_type" json:"task_type"` // 任务执行状态
	Status         uint   `bson:"status" json:"status"`       // 任务执行状态
	EndTime        int64  `bson:"end_time" json:"end_time"`   // 执行结束时间
}

// CollectionName 定义collection name
func (a TaskLog) CollectionName() string {
	return a.UserModel.CollectionName("taskLog")
}

// CreateIndexes 创建索引
func (a TaskLog) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"name": 1}},
		{Keys: bson.M{"status": 1}},
	})
}

// Ordering 默认排序
func (a TaskLog) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
