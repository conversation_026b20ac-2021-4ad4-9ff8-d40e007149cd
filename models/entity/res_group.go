package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResGroupCollection 获取account collection
func GetResGroupCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResGroupEntity.CollectionName())
	return collection
}

// ResGroupEntity 资源组entity
var ResGroupEntity = new(ResGroup)

// ResGroup 资源组
type ResGroup struct {
	UserModel `bson:",inline"`
	Name      string `bson:"name" json:"name,omitempty" validate:"required"` // 组名称
	Desc      string `bson:"desc" json:"desc,omitempty"`                     // 组描述
}

// CollectionName 定义collection name
func (a ResGroup) CollectionName() string {
	return a.Model.CollectionName("res_group")
}

// CreateIndexes 创建索引
func (a ResGroup) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	// unique := true
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"name": 1}},
		// {Keys: bson.M{"name": 1}, Options: &options.IndexOptions{
		// 	Unique: &unique, // 唯一索引
		// }},
	})
}

// Ordering 默认排序
func (a ResGroup) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
