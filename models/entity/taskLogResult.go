package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetTaskLogResultCollection 获取account collection
func GetTaskLogResultCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(TaskLogResultEntity.CollectionName())
	return collection
}

// TaskLogResultEntity 任务执行日志entity
var TaskLogResultEntity = new(TaskLogResult)

// TaskLogResult 任务执行日志
type TaskLogResult struct {
	Model      `bson:",inline"`
	TaskLogID  string `bson:"task_log_id" json:"task_log_id"` // 任务执行ID
	TaskResult string `bson:"task_result" json:"task_result"` // 任务详细
	Stopped    bool   `bson:"stopped" json:"stopped"`         // 任务执行结束
}

// CollectionName 定义collection name
func (a TaskLogResult) CollectionName() string {
	return a.Model.CollectionName("taskLogResult")
}

// CreateIndexes 创建索引
func (a TaskLogResult) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"task_log_id": 1}},
	})
}

// Ordering 默认排序
func (a TaskLogResult) Ordering() *schema.OrderField {
	return &schema.OrderField{
		Key:       "created_time",
		Direction: 1,
	}
}
