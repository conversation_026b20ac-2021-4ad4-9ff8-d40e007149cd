package entity

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetOpsAgentCollection 获取ops-agent collection
func GetOpsAgentCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(OpsAgentEntity.CollectionName())
	return collection
}

// OpsAgentEntity ops-agent entity
var OpsAgentEntity = new(OpsAgent)

// OpsAgent 运维agent
type OpsAgent struct {
	Model         `bson:",inline"`
	AgentID       string           `bson:"agent_id" json:"agent_id,omitempty"`             // agent唯一标识
	Env           string           `bson:"env" json:"env,omitempty"`                       // agent上报的env
	Hostname      string           `bson:"hostname" json:"hostname,omitempty"`             // agent上报的系统hostname
	HostIP        string           `bson:"host_ip" json:"host_ip,omitempty"`               // agent-id中编码的ip(可作为与主机的关联标识)
	RealIP        []string         `bson:"real_ip" json:"real_ip,omitempty"`               // agent上报的实际ip
	Region        string           `bson:"region" json:"region,omitempty"`                 // agent-id中编码的地域名称(可作为与主机的关联标识)
	Status        int32            `bson:"status" json:"status,omitempty"`                 // agent运行状态
	Version       string           `bson:"version" json:"version,omitempty"`               // 当前运行的agent版本号
	SystemVersion string           `bson:"system_version" json:"system_version,omitempty"` // 主机所在的操作系统类型及版本
	NodeType      string           `bson:"node_type" json:"node_type,omitempty"`           // 节点类型
	ProxyID       string           `bson:"proxy_id" json:"proxy_id,omitempty"`             // 代理节点ID
	SyncVersion   string           `bson:"sync_version" json:"sync_version,omitempty"`     // 代理节点上已同步的资源版本
	Hardware      OpsAgentHardware `bson:"hardware" json:"hardware,omitempty"`
}

// OpsAgentHardware ...
type OpsAgentHardware struct {
	GPUList  []string `bson:"gpu_list" json:"gpu_list"`
	CPUModel string   `bson:"cpu_model" json:"cpu_model"`
	CPUCore  int32    `bson:"cpu_core" json:"cpu_core"`
	MemTotal int64    `bson:"mem_total" json:"mem_total"`
}

// TranslateStatus 状态转换
func (a OpsAgent) TranslateStatus() string {
	return TranslateStatus(a.Status, a.UpdatedTime)
}

// TranslateStatus agent状态转换
func TranslateStatus(status int32, updateTime int64) string {
	switch status {
	case 1:
		if time.Now().Unix()-updateTime < cfg.GetOpsAgentConf().LostWait {
			return "Running"
		}
		// 状态Running但是24小时未上报
		return "Lost"
	case 2:
		return "Lost"
	case 6:
		return "Updating"
	}
	return "NotFound"
}

// CollectionName 定义collection name
func (a OpsAgent) CollectionName() string {
	return a.Model.CollectionName("ops-agent")
}

// CreateIndexes 创建索引
func (a OpsAgent) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"agent_id": 1}},
		{Keys: bson.M{"hostname": 1}},
		{Keys: bson.M{"region": 1}},
		{Keys: bson.M{"verion": 1}},
		{Keys: bson.M{"status": 1}},
		{Keys: bson.M{"host_ip": 1}},
	})
}

// Ordering 默认排序
func (a OpsAgent) Ordering() *schema.OrderField {
	return &schema.OrderField{
		Key:       "updated_time",
		Direction: 2,
	}
}
