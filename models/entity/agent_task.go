package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetAgentTaskCollection AgentTask collection
func GetAgentTaskCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(AgentTaskEntity.CollectionName())
	return collection
}

// AgentTaskEntity entity
var AgentTaskEntity = new(AgentTask)

// AgentTask 云管agent任务记录
// 部分字段与meta中的重复，方便可读性
// 重复的包括：runType/methodName/agentID，以extra内数据为准
type AgentTask struct {
	Model        `bson:",inline"`
	AccountID    string         `bson:"account_id" json:"account_id"`
	IspName      string         `bson:"isp_name" json:"isp_name"`
	ResourceType string         `bson:"resource_type" json:"resource_type"`
	RunType      int32          `bson:"run_type" json:"run_type"`
	Status       int32          `bson:"status" json:"status"`
	Key          STSKey         `bson:"key" json:"key"`
	MethodName   string         `bson:"method_name" json:"method_name"`
	RequestDump  string         `bson:"request_dump" json:"request_dump"`
	ResponseDump string         `bson:"response_dump" json:"response_dump"`
	ErrMsg       string         `bson:"err_msg" json:"err_msg"`
	Extra        AgentTaskExtra `bson:"extra" json:"extra"`
	Operator     string         `bson:"operator" json:"operator"`
}

// CollectionName 定义collection name
func (a AgentTask) CollectionName() string {
	return a.Model.CollectionName("agent_task")
}

// CreateIndexes 创建索引
func (a AgentTask) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"isp": 1}},
		{Keys: bson.M{"task_id": 1}},
	})
}

// Ordering 默认排序
func (a AgentTask) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}

// STSKey 凭据存储类
type STSKey struct {
	STSApply  STSApply  `bson:"stsApply" json:"stsApply"`
	STSResult STSResult `bson:"stsResult" json:"stsResult"`
}

// STSApply ...
type STSApply struct {
	ResourceType string `bson:"resourceType" json:"resourceType"`
	MethodName   string `bson:"methodName" json:"methodName"`
	AccountID    string `bson:"accountID" json:"accountID"`
	Region       string `bson:"region" json:"region"`
	Policy       string `bson:"policy" json:"policy"`
}

// STSResult ...
type STSResult struct {
	Ak         string `bson:"ak" json:"ak"`
	Sk         string `bson:"sk" json:"sk"`
	Expiration int64  `bson:"expiration" json:"expiration"`
	Token      string `bson:"token" json:"token"`
	IsCache    bool   `bson:"isCache" json:"isCache"`
}

// AgentTaskExtra ...
type AgentTaskExtra struct {
	Timeout        int64  `bson:"timeout" json:"timeout"`
	Risky          int32  `bson:"risky" json:"risky"`
	SubmitTime     int64  `bson:"submit_time" json:"submit_time"`
	StartTime      int64  `bson:"start_time" json:"start_time"`
	EndTime        int64  `bson:"end_time" json:"end_time"`
	CallbackTime   int64  `bson:"callback_time" json:"callback_time"`
	AgentID        string `bson:"agent_id" json:"agent_id"`
	AgentIP        string `bson:"agent_ip" json:"agent_ip"`
	ServerHost     string `bson:"server_host" json:"server_host"`
	ServerAddr     string `bson:"server_addr" json:"server_addr"`
	BusinessErrMsg string `bson:"business_err_msg" json:"business_err_msg"`
}
