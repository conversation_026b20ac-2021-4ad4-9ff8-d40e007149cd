package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/querybuilder"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetRecyclePolicyCollection 获取account collection
func GetRecyclePolicyCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(RecyclePolicyEntity.CollectionName())
	return collection
}

// RecyclePolicyEntity 资源回收策略entity
var RecyclePolicyEntity = new(RecyclePolicy)

// RecyclePolicy 资源回收策略
type RecyclePolicy struct {
	UserModel    `bson:",inline"`
	InstanceType string `bson:"instance_type" json:"instance_type,omitempty" validate:"required"` // 实例类型
	Name         string `bson:"name" json:"name,omitempty" validate:"required"`                   // 策略名称
	Enabled      bool   `bson:"enabled" json:"enabled,omitempty" validate:"required"`             // 是否激活自动销毁策略
	PolicyInfo   string `bson:"policy_info" json:"policy_info" validate:"required"`
	// ConditionRelated string        `bson:"condition_related" json:"condition_related,omitempty" validate:"required"` // 条件间关系
	// Condition        []RecycleCond `bson:"condition" json:"condition,omitempty" validate:"required"`                 // 自定义销毁策略
	ReleaseTime int32 `bson:"release_time" json:"release_time"` // 释放时间
}

// RecycleCond 资源销毁判断条件
type RecycleCond struct {
	Field    string      `bson:"field" json:"field" validate:"required"`       // 判断条件key
	Operator string      `bson:"operator" json:"operator" validate:"required"` // 关系: 大于/小于/包含等
	Value    interface{} `bson:"value" json:"value" validate:"required"`       // 值
}

// CollectionName 定义collection name
func (a RecyclePolicy) CollectionName() string {
	return a.Model.CollectionName("res_recycle_policy")
}

// CreateIndexes 创建索引
func (a RecyclePolicy) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.D{{"instance_type", 1}, {"name", 1}}},
		{Keys: bson.M{"status": 1}},
	})
}

// Ordering 默认排序
func (a RecyclePolicy) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}

// ToFilter to-mongo-filter
func (a RecyclePolicy) ToFilter() (map[string]interface{}, string, error) {
	// condition := map[string]interface{}{}
	// if policy.ConditionRelated == "and" {
	// 	for _, v := range policy.Condition {
	// 		condition[v.Field] = map[string]interface{}{
	// 			v.Operator: v.Value,
	// 		}
	// 	}
	// 	return condition
	// }

	// condition["condition"] = "AND"
	// if a.ConditionRelated != "and" {
	// 	condition["condition"] = "OR"
	// }
	// condition["rules"] = a.Condition

	filter, errKey, err := querybuilder.ParseRuleFromBytes([]byte(a.PolicyInfo))
	if err != nil {
		return nil, errKey, err
	}
	return filter.ToMgo()
}
