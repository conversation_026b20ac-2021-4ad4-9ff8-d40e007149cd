package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetOpsAgentInstallConfigCollection 获取ops-agent collection
func GetOpsAgentInstallConfigCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(OpsAgentInstallConfigEntity.CollectionName())
	return collection
}

// OpsAgentInstallConfigEntity ops-agent entity
var OpsAgentInstallConfigEntity = new(OpsAgentInstallConfig)

// OpsAgentInstallConfig 运维agent安装配置
type OpsAgentInstallConfig struct {
	Model     `bson:",inline"`
	IspID     string `bson:"isp_id" json:"isp_id,omitempty"`
	RegionID  string `bson:"region_id" json:"region_id,omitempty"`
	CmdbEnv   string `bson:"cmdb_env" json:"cmdb_env,omitempty"`
	BinaryURL string `bson:"binary_url" json:"binary_url,omitempty"`
	RegURL    string `bson:"reg_url" json:"reg_url,omitempty"`
}

// CollectionName 定义collection name
func (a OpsAgentInstallConfig) CollectionName() string {
	return a.Model.CollectionName("ops_agent_install_config")
}

// CreateIndexes 创建索引
func (a OpsAgentInstallConfig) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"region_id": 1}},
	})
}

// Ordering 默认排序
func (a OpsAgentInstallConfig) Ordering() *schema.OrderField {
	return &schema.OrderField{
		Key:       "updated_time",
		Direction: 2,
	}
}
