package entity

import (
	"context"
	"fmt"
	"reflect"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// Model base model
type Model struct {
	ID          primitive.ObjectID `bson:"_id" json:"-"`
	CreatedTime int64              `json:"created_time,omitempty" bson:"created_time" field_type:"UnixTime" field:"创建时间"`
	UpdatedTime int64              `json:"updated_time,omitempty" bson:"updated_time" field_type:"UnixTime" field:"更新时间"`
	DeletedTime int64              `json:"deleted_time,omitempty" bson:"deleted_time" field_type:"UnixTime" field:"删除时间"`
	IsDelete    int                `bson:"is_delete" json:"is_delete"` // 因mongo使用$exists查询不走索引,使用IsDelete作为筛选条件,加快查询
	// CreatedAt int64              `bson:"created_time" json:"created_time,omitempty"`
	// UpdatedAt int64              `bson:"updated_time" json:"updated_time,omitempty"`
	// DeletedAt int64              `bson:"deleted_time" json:"deleted_time,omitempty"`
}

// CollectionName 定义collection name
func (Model) CollectionName(name string) string {
	return fmt.Sprintf("%s_%s", cfg.GetCollectionPrefix(), name)
}

// CreateIndexes 创建索引
func (Model) CreateIndexes(ctx context.Context, cli *mongo.Client, m Collectioner, indexes []mongo.IndexModel) error {
	models := []mongo.IndexModel{
		// {Keys: bson.M{"created_time": 1}},
		// {Keys: bson.M{"updated_time": 1}},
		{Keys: bson.M{"is_delete": 1}},
		{Keys: bson.M{"created_time": 1}},
		{Keys: bson.M{"updated_time": 1}},
	}
	if len(indexes) > 0 {
		models = append(models, indexes...)
	}

	_, err := GetCollection(cli, m).Indexes().CreateMany(ctx, models)
	return err
}

// Ordering 默认排序规则: created_time 降序
func (Model) Ordering() *schema.OrderField {
	return &schema.OrderField{
		Key:       "created_time",
		Direction: 2,
	}
}

// PkFilter 使用mongo-objectID作为主键时查询十分不遍,且不具有通用性
func (m Model) PkFilter(pk string) map[string]interface{} {
	raw := make(map[string]interface{})
	objectID, err := primitive.ObjectIDFromHex(pk)
	if err != nil {
		return raw
	}
	raw["_id"] = objectID
	return raw
}

// Collectioner collect interface
type Collectioner interface {
	// CreateIndexes(ctx context.Context, cli *mongo.Client) error
	CollectionName() string       // 集合名称
	Ordering() *schema.OrderField // 默认排序字段
}

// GetCollection 定义collection name
func GetCollection(cli *mongo.Client, m Collectioner) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(m.CollectionName())
	return collection
}

// AddUser add user model
type AddUser struct {
	CreateUser string `bson:"create_user" json:"create_user,omitempty"`
	UpdateUser string `bson:"update_user" json:"update_user,omitempty"`
}

// UserModel user model
type UserModel struct {
	Model   `bson:",inline"`
	AddUser `bson:",inline"`
}

// FiledStruct 表描述信息结构体
type FiledStruct struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Value    string `json:"value"`
	Extra    bool   `json:"export"` // 额外字段，不参与导出导入操作
	Required bool   `json:"required"`
	Indexed  bool   `json:"indexed"`
}

// GetFieldMeta 或者表描述信息
func GetFieldMeta(val interface{}) (st []*FiledStruct) {
	var elem reflect.Value
	value := reflect.ValueOf(val)
	kind := value.Kind()
	switch kind {
	case reflect.Struct:
		elem = value
	case reflect.Ptr:
		elem = value.Elem()
	default:
		return nil
	}

	var required bool
	var indexed bool

	for i := 0; i < elem.NumField(); i++ {
		fieldInfo := elem.Type().Field(i)
		tag := fieldInfo.Tag
		name := tag.Get("field")
		bsonName := tag.Get("bson")
		validate := tag.Get("validate")
		fieldType := tag.Get("field_type")
		indexField := tag.Get("indexed")

		if fieldType == "" {
			fieldType = fieldInfo.Type.Kind().String()
		}
		if strings.Index(bsonName, ",inline") != -1 {
			tmp := elem.Field(i).Interface()
			st = append(st, GetFieldMeta(tmp)...)
		}
		if utils.StrNotIn(name, []string{"", "-"}) && bsonName != "" {
			indexed = indexField != ""
			required = strings.Index(validate, "required") != -1

			st = append(st, &FiledStruct{
				Name:     name,
				Type:     fieldType,
				Value:    bsonName,
				Required: required,
				Indexed:  indexed,
			})
		}
	}

	return
}

// MetaToFieldMap 根据field返回map类型描述信息
func MetaToFieldMap(st []*FiledStruct) (stm map[string]*FiledStruct) {
	m := map[string]*FiledStruct{}

	for _, v := range st {
		m[v.Name] = v
	}

	return m
}
