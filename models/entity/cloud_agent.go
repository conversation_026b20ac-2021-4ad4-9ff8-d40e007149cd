package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetCloudAgentCollection 获取CloudAgent collection
func GetCloudAgentCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(CloudAgentEntity.CollectionName())
	return collection
}

// CloudAgentEntity entity
var CloudAgentEntity = new(CloudAgent)

// CloudAgent 账户model
type CloudAgent struct {
	UserModel   `bson:",inline"`
	AccountID   string `bson:"account_id" json:"account_id"`     // 厂商账号
	InnerIP     string `bson:"inner_ip" json:"inner_ip"`         // agent所在机器的ip
	Disabled    bool   `bson:"disabled" json:"disabled"`         // 是否已禁用
	AgentID     string `bson:"agent_id" json:"agent_id"`         // 实例名，与证书中common_name字段相同，变更视为吊销已有证书
	InstallCode string `bson:"install_code" json:"install_code"` // 安装码，安装时证书签发后清空，重复获取安装脚本吊销之前的
	ExpireAt    int64  `bson:"expire_at" json:"expire_at"`
}

// CloudAgentStatusRunning ...
var CloudAgentStatusRunning int32 = 1

// CloudAgentStatusStopped ...
var CloudAgentStatusStopped int32 = 0

// CollectionName 定义collection name
func (a CloudAgent) CollectionName() string {
	return a.UserModel.CollectionName("cloud_agent")
}

// CreateIndexes 创建索引
func (a CloudAgent) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	unique := true
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"account_id": 1, "inner_ip": 1, "agent_id": 1, "install_code": 1}, Options: &options.IndexOptions{Unique: &unique}},
	})
}

// Ordering 默认排序
func (a CloudAgent) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
