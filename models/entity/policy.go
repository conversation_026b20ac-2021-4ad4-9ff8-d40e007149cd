package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetPolicyCollection 获取policy collection
func GetPolicyCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(PolicyEntity.CollectionName())
	return collection
}

// PolicyEntity entity
var PolicyEntity = new(Policy)

type Policy struct {
	UserModel             `bson:",inline"`
	UpdateDate            string        `json:"UpdateDate" bson:"UpdateDate"`
	PolicyType            string        `json:"PolicyType" bson:"PolicyType"`
	Description           string        `json:"Description" bson:"Description"`
	AttachUsers           []*AttachUser `json:"AttachUsers" bson:"AttachUsers"`
	DefaultVersion        string        `json:"DefaultVersion" bson:"DefaultVersion"`
	PolicyName            string        `json:"PolicyName" bson:"PolicyName"`
	CreateDate            string        `json:"CreateDate" bson:"CreateDate"`
	DefaultPolicyVersion  string        `json:"DefaultPolicyVersion" bson:"DefaultPolicyVersion"`
	DefaultPolicyDocument string        `json:"DefaultPolicyDocument" bson:"DefaultPolicyDocument"`
	RegionID              string        `json:"RegionID" bson:"RegionID"`
	IspID                 string        `json:"IspID" bson:"IspID"`
	IspType               string        `json:"IspType" bson:"IspType"`
	RelatedIpGroups       []string      `json:"RelatedIpGroups" bson:"RelatedIpGroups"`
	NeedCleanup           bool          `bson:"NeedCleanup" json:"NeedCleanup,omitempty"`
	UpdateVersion         string        `bson:"UpdateVersion" json:"UpdateVersion,omitempty"`
}

type AttachUser struct {
	DisplayName string `json:"DisplayName" bson:"DisplayName"`
	UserId      string `json:"UserId" bson:"UserId"`
	UserName    string `json:"UserName" bson:"UserName"`
	AttachDate  string `json:"AttachDate" bson:"AttachDate"`
}

func (a Policy) CollectionName() string {
	return a.UserModel.CollectionName("policy")
}

// CreateIndexes 创建索引
func (a Policy) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"PolicyName": 1}},
	})
}

// Ordering 默认排序
func (a Policy) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
