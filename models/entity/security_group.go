package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetSecurityGroupCollection 获取securityGroup collection
func GetSecurityGroupCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(SecurityGroupEntity.CollectionName())
	return collection
}

// SecurityGroupEntity entity
var SecurityGroupEntity = new(SecurityGroup)

// SecurityGroup 账户model
type SecurityGroup struct {
	UserModel               `bson:",inline"`
	CreationTime            string                    `bson:"CreationTime" json:"CreationTime"`
	VpcID                   string                    `bson:"VpcId" json:"VpcId"`
	ServiceManaged          bool                      `bson:"ServiceManaged" json:"ServiceManaged"`
	Description             string                    `bson:"Description" json:"Description"`
	SecurityGroupID         string                    `bson:"SecurityGroupId" json:"SecurityGroupId"`
	ResourceGroupID         string                    `bson:"ResourceGroupId" json:"ResourceGroupId"`
	SecurityGroupName       string                    `bson:"SecurityGroupName" json:"SecurityGroupName"`
	EcsCount                int                       `bson:"EcsCount" json:"EcsCount"`
	ServiceID               int64                     `bson:"ServiceID" json:"ServiceID"`
	SecurityGroupType       string                    `bson:"SecurityGroupType" json:"SecurityGroupType"`
	AvailableInstanceAmount int                       `bson:"AvailableInstanceAmount" json:"AvailableInstanceAmount"`
	RegionID                string                    `bson:"RegionID" json:"RegionID"`
	Tags                    []Tag                     `bson:"Tags" json:"Tags"`
	Permissions             []SecurityGroupPermission `bson:"SecurityGroupPermission" json:"SecurityGroupPermission"`
	IspID                   string                    `bson:"isp_id" json:"isp_id" field:"云厂商ID"`
	IspType                 string                    `bson:"isp_type" json:"isp_type" field:"云厂商类型"`
	CustomTag               string                    `bson:"custom_tag" json:"custom_tag"` //页签
	LevelTag                RuleTag                   `bson:"level_tag" json:"level_tag"`
	UpdateVersion           string                    `bson:"UpdateVersion" json:"UpdateVersion,omitempty"`
	NeedCleanup             bool                      `bson:"NeedCleanup" json:"NeedCleanup,omitempty"`
}

// SecurityGroupPermission ...
type SecurityGroupPermission struct {
	CreateTime              string    `bson:"CreateTime" json:"CreateTime"`
	Description             string    `bson:"Description" json:"Description"`
	DestCidrIP              string    `bson:"DestCidrIp" json:"DestCidrIp"`
	DestGroupID             string    `bson:"DestGroupId" json:"DestGroupId"`
	DestGroupName           string    `bson:"DestGroupName" json:"DestGroupName"`
	DestGroupOwnerAccount   string    `bson:"DestGroupOwnerAccount" json:"DestGroupOwnerAccount"`
	DestPrefixListID        string    `bson:"DestPrefixListId" json:"DestPrefixListId"`
	DestPrefixListName      string    `bson:"DestPrefixListName" json:"DestPrefixListName"`
	Direction               string    `bson:"Direction" json:"Direction"`
	IPProtocol              string    `bson:"IpProtocol" json:"IpProtocol"`
	Ipv6DestCidrIP          string    `bson:"Ipv6DestCidrIp" json:"Ipv6DestCidrIp"`
	Ipv6SourceCidrIP        string    `bson:"Ipv6SourceCidrIp" json:"Ipv6SourceCidrIp"`
	NicType                 string    `bson:"NicType" json:"NicType"`
	Policy                  string    `bson:"Policy" json:"Policy"`
	PortRange               string    `bson:"PortRange" json:"PortRange"`
	Priority                string    `bson:"Priority" json:"Priority"`
	SourceCidrIP            string    `bson:"SourceCidrIp" json:"SourceCidrIp"`
	SourceGroupID           string    `bson:"SourceGroupId" json:"SourceGroupId"`
	SourceGroupName         string    `bson:"SourceGroupName" json:"SourceGroupName"`
	SourceGroupOwnerAccount string    `bson:"SourceGroupOwnerAccount" json:"SourceGroupOwnerAccount"`
	SourcePortRange         string    `bson:"SourcePortRange" json:"SourcePortRange"`
	SourcePrefixListID      string    `bson:"SourcePrefixListId" json:"SourcePrefixListId"`
	SourcePrefixListName    string    `bson:"SourcePrefixListName" json:"SourcePrefixListName"`
	SecurityGroupRuleID     string    `bson:"SecurityGroupRuleID" json:"SecurityGroupRuleID"`
	Tags                    []RuleTag `bson:"tags" json:"tags"`
}

// RuleTag ...
type RuleTag struct {
	Level string `bson:"level" json:"level"`
	Value string `bson:"value" json:"value"`
}

// CollectionName 定义collection name
func (a SecurityGroup) CollectionName() string {
	return a.UserModel.CollectionName("security_group")
}

// CreateIndexes 创建索引
func (a SecurityGroup) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"SecurityGroupId": 1}},
	})
}

// Ordering 默认排序
func (a SecurityGroup) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
