package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetMysqlDatabaseResCollection 获取Mysql资源collection
func GetMysqlDatabaseResCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(MysqlDatabaseResourceEntity.CollectionName())
	return collection
}

// MysqlDatabaseResourceEntity entity
var MysqlDatabaseResourceEntity = new(MysqlDatabaseResource)

// MysqlDatabaseResource Mysql数据库详情
type MysqlDatabaseResource struct {
	Model            `bson:",inline"`
	IsLock           bool              `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	IspID            string            `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
	IspType          string            `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`
	RegionID         string            `bson:"region_id" json:"region_id,omitempty" field:"地域ID"`
	ClusterID        string            `bson:"cluster_id" json:"cluster_id" field:"数据库集群ID"`
	DBDescription    string            `bson:"DBDescription" json:"DBDescription,omitempty" xml:"DBDescription,omitempty" field:"数据库备注"`
	DBStatus         string            `bson:"DBStatus" json:"DBStatus,omitempty" xml:"DBStatus,omitempty" field:"数据库状态"`
	DBName           string            `bson:"DBName" json:"DBName,omitempty" xml:"DBName,omitempty" field:"数据库名称"`
	Engine           string            `bson:"Engine" json:"Engine,omitempty" xml:"Engine,omitempty" field:"数据库引擎类型"`
	CharacterSetName string            `bson:"CharacterSetName" json:"CharacterSetName,omitempty" xml:"CharacterSetName,omitempty" field:"字符集"`
	Accounts         DatabaseAccounts  `bson:"Accounts" json:"Accounts,omitempty" xml:"Accounts,omitempty" type:"Struct" field:"数据库账号信息详情"`
	DatabaseTags     MysqlDatabaseTags `bson:"DatabaseTags" json:"DatabaseTags,omitempty" xml:"DatabaseTags,omitempty" field:"标签集合"`
	BkCmdb           *BkCmdbInfo       `bson:"BkCmdb" json:"BkCmdb,omitempty"`
	UpdateVersion    string            `bson:"UpdateVersion" json:"UpdateVersion,omitempty"`
}

// MysqlDatabaseTags 集群标签
type MysqlDatabaseTags struct {
	Tag []Tag `bson:"Tag" json:"Tag,omitempty" xml:"Tag,omitempty" type:"Repeated" field:"标签集合"`
}

// DatabaseAccounts 账户详情
type DatabaseAccounts struct {
	Account []*DatabaseAccount `bson:"Account" json:"Account,omitempty" xml:"Account,omitempty" type:"Repeated" field:"数据库账号信息集合"`
}

// DatabaseAccount 账户详情
type DatabaseAccount struct {
	PrivilegeStatus  string `bson:"PrivilegeStatus" json:"PrivilegeStatus,omitempty" xml:"PrivilegeStatus,omitempty" field:"授权状态"`
	AccountStatus    string `bson:"AccountStatus" json:"AccountStatus,omitempty" xml:"AccountStatus,omitempty" field:"账号状态"`
	AccountPrivilege string `bson:"AccountPrivilege" json:"AccountPrivilege,omitempty" xml:"AccountPrivilege,omitempty" field:"账号权限"`
	AccountName      string `bson:"AccountName" json:"AccountName,omitempty" xml:"AccountName,omitempty" field:"账号名称"`
}

// CollectionName 定义collection name
func (h MysqlDatabaseResource) CollectionName() string {
	return h.Model.CollectionName("mysql_database_resource")
}

// CreateIndexes 创建索引
func (h MysqlDatabaseResource) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return h.Model.CreateIndexes(ctx, cli, h, []mongo.IndexModel{
		{Keys: bson.M{"DBName": 1}},
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"isp_type": 1}},
		{Keys: bson.M{"is_lock": 1}},
		{Keys: bson.M{"DBStatus": 1}},
		{Keys: bson.M{"cluster_id": 1}},
	})
}

// Meta 描述信息
func (h MysqlDatabaseResource) Meta() []*FiledStruct {
	return GetFieldMeta(h)
}

// Ordering 默认排序
func (h MysqlDatabaseResource) Ordering() *schema.OrderField {
	return h.Model.Ordering()
}
