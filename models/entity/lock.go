package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetLockCollection 获取lock collection
func GetLockCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(LockEntity.CollectionName())
	return collection
}

// LockEntity entity
var LockEntity = new(Lock)

// Lock 账户model
type Lock struct {
	UserModel `bson:",inline"`
	Key       string `bson:"key" json:"key"`             // key
	Value     string `bson:"value" json:"value"`         // 值
	ExpireAt  int64  `bson:"expire_at" json:"expire_at"` // 过期时间
}

// CollectionName 定义collection name
func (a Lock) CollectionName() string {
	return a.UserModel.CollectionName("lock")
}

// CreateIndexes 创建索引
func (a Lock) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	unique := true
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"key": 1}, Options: &options.IndexOptions{Unique: &unique}},
	})
}

// Ordering 默认排序
func (a Lock) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
