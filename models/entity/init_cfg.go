package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetInitCfgCollection 获取collection
func GetInitCfgCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(InitCfgEntity.CollectionName())
	return collection
}

// InitCfgEntity entity
var InitCfgEntity = new(InitCfg)

type InitCfg struct {
	UserModel      `bson:",inline"`
	RegionID       string         `bson:"region_id"`       // 区域id, e.g. cn-shanghai, cn-hangzhou
	HotwheelOption HotWheelOption `bson:"hotwheel_option"` // 标准运维配置
	Jumpservers    []Jumpserver   `bson:"jumpservers"`     // 堡垒机列表
}

type HotWheelOption struct {
	URI    string `bson:"uri"`     // 标准运维地址
	Token  string `bson:"token"`   // 请求hotwheel使用的token
	CateID int64  `bson:"cate_id"` // 标准运维流程分类
}

type Jumpserver struct {
	Host    string `bson:"host"`
	IP      string `bson:"ip"`
	Port    string `bson:"port"`
	AgentID string `bson:"agent_id"`
}

// CollectionName 定义collection name
func (a InitCfg) CollectionName() string {
	return a.UserModel.CollectionName("init_cfg")
}

// CreateIndexes 创建索引
func (a InitCfg) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"region_id": 1}},
	})
}

// Ordering 默认排序
func (a InitCfg) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
