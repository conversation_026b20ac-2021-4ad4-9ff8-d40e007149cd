package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetRecycleCfgCollection 获取collection
func GetRecycleCfgCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(RecycleCfgEntity.CollectionName())
	return collection
}

// RecycleCfgEntity entity
var RecycleCfgEntity = new(RecycleCfg)

type RecycleCfg struct {
	UserModel      `bson:",inline"`
	RegionID       string                `bson:"region_id"`       // 区域id, e.g. cn-shanghai, cn-hangzhou
	HotwheelOption RecycleHotWheelOption `bson:"hotwheel_option"` // 标准运维配置
	Jumpservers    []Jumpserver          `bson:"jumpservers"`     // 堡垒机列表
}

type RecycleHotWheelOption struct {
	URI        string `bson:"uri"`         // 标准运维地址
	Token      string `bson:"token"`       // 请求hotwheel使用的token
	PipelineID string `bson:"pipeline_id"` // 流程id
}

// CollectionName 定义collection name
func (a RecycleCfg) CollectionName() string {
	return a.UserModel.CollectionName("recycle_cfg")
}

// CreateIndexes 创建索引
func (a RecycleCfg) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"region_id": 1}},
	})
}

// Ordering 默认排序
func (a RecycleCfg) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
