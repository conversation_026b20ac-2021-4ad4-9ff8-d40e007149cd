package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResTagsCollection 获取account collection
func GetResTagsCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResTagsEntity.CollectionName())
	return collection
}

// ResTagsEntity 资源模板entity
var ResTagsEntity = new(ResTags)

// ResTags 资源模板
type ResTags struct {
	UserModel `bson:",inline"`
	Key       string `bson:"key" json:"key,omitempty"`     // 标签键
	Value     string `bson:"value" json:"value,omitempty"` // 标签值
	Source    string `bson:"source" json:"source"`         // 标签来源
	Desc      string `bson:"desc" json:"desc,omitempty"`   // 标签描述
	Type      string `bson:"type" json:"type"`             // 是否系统标签
}

// CollectionName 定义collection name
func (a ResTags) CollectionName() string {
	return a.Model.CollectionName("res_tags")
}

// CreateIndexes 创建索引
func (a ResTags) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"key": 1}},
		{Keys: bson.M{"value": 1}},
		{Keys: bson.M{"source": 1}},
	})
}

// Ordering 默认排序
func (a ResTags) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
