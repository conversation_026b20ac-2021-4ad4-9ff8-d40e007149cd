package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetIPCollection 获取ip collection
func GetIPCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(IPEntity.CollectionName())
	return collection
}

// IPEntity entity
var IPEntity = new(IP)

// IP ...
type IP struct {
	UserModel        `bson:",inline"`
	Type             string `bson:"type"`        // 区分网段还是IP地址
	CreateType       string `bson:"create_type"` // "sync" or "custom"
	Address          string `bson:"address"`
	BindInstanceType string `bson:"bind_instance_type"`
	InstanceID       string `bson:"instance_id"`
	Desc             string `bson:"desc"`
	RegionID         string `bson:"RegionID"`
	IspID            string `bson:"isp_id" json:"isp_id" field:"云厂商ID"`
	IspType          string `bson:"isp_type" json:"isp_type" field:"云厂商类型"`
}

// CollectionName 定义collection name
func (a IP) CollectionName() string {
	return a.UserModel.CollectionName("ip")
}

// CreateIndexes 创建索引
func (a IP) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"address": 1, "isp_id": 1, "bind_instance_type": 1}},
	})
}

// Ordering 默认排序
func (a IP) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
