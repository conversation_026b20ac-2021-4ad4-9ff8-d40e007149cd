package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetAccountCollection 获取account collection
func GetAccountCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(AccountEntity.CollectionName())
	return collection
}

// AccountEntity entity
var AccountEntity = new(Account)

// Account 账户model
type Account struct {
	UserModel           `bson:",inline"`
	Name                string   `bson:"name" json:"name"`                               // 环境名称
	AType               string   `bson:"a_type" json:"a_type"`                           // 账户类型
	Status              int32    `bson:"status" json:"status"`                           // 账户状态
	TestStatus          int64    `bson:"test_status" json:"test_status"`                 // 连通性测试
	TestResult          string   `bson:"test_result" json:"test_result"`                 // 连通性测试结果
	AccessKey           string   `bson:"access_key" json:"access_key"`                   // 账户key
	AccessSecret        string   `bson:"access_secret" json:"access_secret"`             // 账户secret
	KMSName             string   `bson:"kms_name" json:"kms_name"`                       // ak类型:原生(null)或kms(kms)
	RegionIDs           []string `bson:"region_ids" json:"region_ids"`                   // 纳管地域
	UserIDs             []string `bson:"user_ids" json:"user_ids"`                       // 授权用户列表
	IndexID             uint32   `bson:"index_id" json:"index_id"`                       // 管理编号
	HighLevelPermission bool     `bson:"HighLevelPermission" json:"HighLevelPermission"` // 开启高级权限控制
	Arn                 string   `bson:"arn" json:"arn"`                                 // RAM角色arn
	// DefaultRegionName string   `bson:"default_region_name" json:"default_region_name"` // 默认地域名称
	// DefaultRegionID   string   `bson:"default_region_id" json:"default_region_id"`     // 默认地域名称
	PingStatus int32  `bson:"ping_status" json:"ping_status"`
	PingResult string `bson:"ping_result" json:"ping_result"`
	UseAgent   bool   `bson:"use_agent" json:"use_agent"`
	Host       string `bson:"host" json:"host"`             // Jumpserver的Host
	ProjectID  int32  `bson:"project_id" json:"project_id"` // 计算平台的project_id
}

// CollectionName 定义collection name
func (a Account) CollectionName() string {
	return a.UserModel.CollectionName("account")
}

// CreateIndexes 创建索引
func (a Account) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"name": 1}},
		{Keys: bson.M{"a_type": 1}},
		{Keys: bson.M{"status": 1}},
	})
}

// Ordering 默认排序
func (a Account) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
