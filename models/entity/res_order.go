package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResOrderCollection 获取order collection
func GetResOrderCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResOrderEntity.CollectionName())
	return collection
}

// ResOrderEntity 资源订单entity
var ResOrderEntity = new(ResOrder)

// ActionType 操作类型
type ActionType string

// ResOrder 资源订单entity
type ResOrder struct {
	UserModel    `bson:",inline"`
	StartTime    int64   `bson:"start_time" json:"start_time,omitempty"`             // 开始时间
	OverTime     int64   `bson:"over_time" json:"over_time,omitempty"`               // 结束时间
	Status       int32   `bson:"status" json:"status,omitempty" validate:"required"` // 订单状态
	IspID        string  `bson:"isp_id" json:"isp_id,omitempty"`                     // 服务提供商
	IspName      string  `bson:"isp_name" json:"isp_name,omitempty"`                 // 服务提供商名称(审计日志不记录变更后的名称)
	IspType      string  `bson:"isp_type" json:"isp_type,omitempty"`                 // 服务提供商类型
	RegionID     string  `bson:"region_id" json:"region_id,omitempty"`               // 所在地域
	RegionName   string  `bson:"region_name" json:"region_name,omitempty"`           // 所在地域
	Type         string  `bson:"type" json:"type,omitempty"`                         // 资源类型
	Action       string  `bson:"action" json:"action,omitempty"`                     // 资源操作,创建/变更/删除
	BeforeInfo   string  `bson:"before_info" json:"before_info,omitempty"`           // 资源变更前信息
	AfterInfo    string  `bson:"after_info" json:"after_info,omitempty"`             // 资源变更后信息,如为初次创建则仅存变更后信息
	ErrorMsg     string  `json:"error_msg" bson:"error_msg,omitempty"`               // 任务执行出错日志
	RawData      string  `json:"raw_data" bson:"raw_data,omitempty"`                 // 原始数据
	Reason       string  `json:"reason" bson:"reason,omitempty"`                     // 资源创建请求原因
	StatusDetail string  `json:"status_detail" bson:"status_detail,omitempty"`       // 执行的实时详细状态
	TicketID     int32   `json:"ticket_id" bson:"ticket_id,omitempty"`               // 对应的工单ID
	TemplateId   *string `json:"template_id" bson:"template_id,omitempty"`           // 对应的模板ID
	TemplateName *string `json:"template_name" bson:"template_name,omitempty"`       // 对应的模板名称
	InitOption   *string `json:"init_option" bson:"init_option,omitempty"`           // 初始化选项,创建资源的订单才有
}

var actionType = map[string]string{"create": "创建", "modify": "变更", "recycle": "回收", "recover": "恢复", "delete": "销毁", "cleanup": "清理", "backup": "备份"}
var orderType = map[string]string{"host": "主机", "mysql": "数据库", "redis": "Redis"}

// GetAction 获取操作类型
func (r ResOrder) GetAction() string {
	v, ok := actionType[r.Action]
	if !ok {
		return r.Action
	}

	return v
}

// GetOrderType 获取订单类型
func (r ResOrder) GetOrderType() string {
	v, ok := orderType[r.Type]
	if !ok {
		return r.Type
	}

	return v
}

// CollectionName 定义collection name
func (r ResOrder) CollectionName() string {
	return r.Model.CollectionName("res_order")
}

// CreateIndexes 创建索引
func (r ResOrder) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	// unique := true
	return r.Model.CreateIndexes(ctx, cli, r, []mongo.IndexModel{
		{Keys: bson.M{"status": 1}},
		{Keys: bson.M{"isp": 1}},
		{Keys: bson.M{"type": 1}},
		// {Keys: bson.M{"name": 1}, Options: &options.IndexOptions{
		// 	Unique: &unique, // 唯一索引
		// }},
	})
}

// Ordering 默认排序
func (r ResOrder) Ordering() *schema.OrderField {
	return r.Model.Ordering()
}
