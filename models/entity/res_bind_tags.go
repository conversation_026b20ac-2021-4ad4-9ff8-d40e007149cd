package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResBindTagsCollection 获取资源绑定collection
func GetResBindTagsCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResBindTagsEntity.CollectionName())
	return collection
}

// ResBindTagsEntity 资源绑定entity
var ResBindTagsEntity = new(ResBindTags)

// ResBindTags 资源模板
type ResBindTags struct {
	UserModel   `bson:",inline"`
	TagsID      primitive.ObjectID `bson:"tags_id" json:"tags_id"`             // 标签ID
	BindResType string             `bson:"bind_res_type" json:"bind_res_type"` // 资源类型
	BindResID   primitive.ObjectID `bson:"bind_res_id" json:"bind_res_id"`     // 资源ID
	Source      string             `bson:"source" json:"source"`               // 来源: 默认为用户创建,可能来源:云资源同步任务
}

// CollectionName 定义collection name
func (a ResBindTags) CollectionName() string {
	return a.Model.CollectionName("res_bind_tags")
}

// CreateIndexes 创建索引
func (a ResBindTags) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	// unique := true
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.D{{"tags_id", 1}, {"bind_res_type", 1}, {"bind_res_id", 1}}, Options: &options.IndexOptions{
			// Unique: &unique, // 唯一索引
		}}, // 联合索引
		{Keys: bson.M{"bind_res_type": 1, "bind_res_id": 1}},
	})
}

// Ordering 默认排序
func (a ResBindTags) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
