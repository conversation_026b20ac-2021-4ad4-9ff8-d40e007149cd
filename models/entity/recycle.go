package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetRecycleCollection 获取account collection
func GetRecycleCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(RecycleEntity.CollectionName())
	return collection
}

// RecycleEntity entity
var RecycleEntity = new(Recycle)

// Recycle 资源回收站
type Recycle struct {
	UserModel      `bson:",inline"`
	InstanceName   string   `bson:"instance_name" json:"instance_name,omitempty"`       // 实例名称
	InstanceType   string   `bson:"instance_type" json:"instance_type,omitempty"`       // 实例类型
	InstanceID     string   `bson:"instance_id" json:"instance_id,omitempty"`           // 实例ID
	IspID          string   `bson:"isp_id" json:"isp_id,omitempty"`                     // 服务提供商
	IspType        string   `bson:"isp_type" json:"isp_type,omitempty"`                 // 服务提供商类型
	RegionID       string   `bson:"region_id" json:"region_id,omitempty"`               // region ID非region表中的ID
	Status         int32    `bson:"status" json:"status,omitempty"`                     // 状态
	DestroyTime    int64    `bson:"destroy_time" json:"destroy_time,omitempty"`         // 实例预销毁时间
	InnerIPAddress []string `bson:"inner_ip_address" json:"inner_ip_address,omitempty"` // 内网IP
}

// CollectionName 定义collection name
func (a Recycle) CollectionName() string {
	return a.Model.CollectionName("res_recycle")
}

// CreateIndexes 创建索引
func (a Recycle) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"instance_name": 1}},
		{Keys: bson.M{"instance_type": 1}},
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"status": 1}},
	})
}

// Ordering 默认排序
func (a Recycle) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
