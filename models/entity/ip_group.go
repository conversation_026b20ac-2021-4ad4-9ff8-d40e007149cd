package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetIPGroupCollection 获取ip group collection
func GetIPGroupCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(IPGroupEntity.CollectionName())
	return collection
}

// IPGroupEntity entity
var IPGroupEntity = new(IPGroup)

type IPGroup struct {
	UserModel   `bson:",inline"`
	Name        string   `json:"Name" bson:"Name"`
	Description string   `json:"Description" bson:"Description"`
	RelatedIPs  []string `json:"RelatedIPs" bson:"RelatedIPs"` // 数据库id
}

func (a IPGroup) CollectionName() string {
	return a.UserModel.CollectionName("ip_group")
}

// CreateIndexes 创建索引
func (a IPGroup) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"Name": 1}},
	})
}

// Ordering 默认排序
func (a IPGroup) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
