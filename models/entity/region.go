package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetRegionCollection 获取account collection
func GetRegionCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(RegionEntity.CollectionName())
	return collection
}

// RegionEntity region entity
var RegionEntity = new(Region)

// Region zone
type Region struct {
	Model    `bson:",inline"`
	Name     string `bson:"name"`      // 地域名
	IspType  string `bson:"isp_type"`  // 地域类型:阿里云/腾讯云
	Endpoint string `bson:"endpoint"`  // api接入点
	RegionID string `bson:"region_id"` // 区服id
}

// CollectionName 定义collection name
func (a Region) CollectionName() string {
	return a.Model.CollectionName("region")
}

// CreateIndexes 创建索引
func (a Region) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"name": 1}},
		{Keys: bson.M{"z_type": 1}},
	})
}

// Ordering 默认排序
func (a Region) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
