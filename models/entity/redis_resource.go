package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetRedisResCollection 获取Mysql资源collection
func GetRedisResCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(RedisResourceEntity.CollectionName())
	return collection
}

// RedisResourceEntity entity
var RedisResourceEntity = new(RedisResource)

// RedisResource Mysql数据库详情
type RedisResource struct {
	// 平台元信息
	Model        `bson:",inline"`
	IsLock       bool   `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	IspID        string `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
	IspType      string `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`
	ReplacateID  string `bson:"ReplacateId" json:"ReplacateId,omitempty" xml:"ReplacateId,omitempty" field:"多活实例的逻辑ID"`
	InstanceID   string `bson:"InstanceID" json:"InstanceID,omitempty" xml:"InstanceID,omitempty" field:"实例ID" indexed:"1"`
	InstanceName string `bson:"InstanceName" json:"InstanceName,omitempty" xml:"InstanceName,omitempty" field:"实例名称" indexed:"1"`
	Recyclable   bool   `bson:"Recyclable" json:"Recyclable,omitempty" xml:"Recyclable,omitempty" field:"实例是否可以回收"`
	CreateTime   string `bson:"CreateTime" json:"CreateTime,omitempty" xml:"CreateTime,omitempty" field:"创建时间"`
	UpdateTime   string `bson:"UpdateTime" json:"UpdateTime,omitempty" xml:"UpdateTime,omitempty" field:"修改时间"`

	// 实例元信息
	SearchKey           string                   `bson:"SearchKey" json:"SearchKey,omitempty" xml:"SearchKey,omitempty" field:"搜索关键字"`
	ConnectionDomain    string                   `bson:"ConnectionDomain" json:"ConnectionDomain,omitempty" xml:"ConnectionDomain,omitempty" field:"内网连接地址"`
	Port                int64                    `bson:"Port" json:"Port,omitempty" xml:"Port,omitempty" field:"服务端口"`
	UserName            string                   `bson:"UserName" json:"UserName,omitempty" xml:"UserName,omitempty" field:"连接用户名"`
	InstanceStatus      string                   `bson:"InstanceStatus" json:"InstanceStatus,omitempty" xml:"InstanceStatus,omitempty" field:"运行状态"`
	RegionID            string                   `bson:"RegionID" json:"RegionID,omitempty" xml:"RegionID,omitempty" field:"地域ID"`
	Capacity            int64                    `bson:"Capacity" json:"Capacity,omitempty" xml:"Capacity,omitempty" field:"实例容量"`
	InstanceClass       string                   `bson:"InstanceClass" json:"InstanceClass,omitempty" xml:"InstanceClass,omitempty" field:"实例的规格"`
	QPS                 int64                    `bson:"QPS" json:"QPS,omitempty" xml:"QPS,omitempty" field:"每秒请求数"`
	Bandwidth           int64                    `bson:"Bandwidth" json:"Bandwidth,omitempty" xml:"Bandwidth,omitempty" field:"实例带宽"`
	Connections         int64                    `bson:"Connections" json:"Connections,omitempty" xml:"Connections,omitempty" field:"连接数限制"`
	ZoneID              string                   `bson:"ZoneID" json:"ZoneID,omitempty" xml:"ZoneID,omitempty" field:"可用区ID"`
	Config              string                   `bson:"Config" json:"Config,omitempty" xml:"Config,omitempty" field:"实例参数设置"`
	ChargeType          string                   `bson:"ChargeType" json:"ChargeType,omitempty" xml:"ChargeType,omitempty" field:"付费类型"`
	NetworkType         string                   `bson:"NetworkType" json:"NetworkType,omitempty" xml:"NetworkType,omitempty" field:"网络类型"`
	VpcID               string                   `bson:"VpcId" json:"VpcId,omitempty" xml:"VpcId,omitempty" field:"专有网络ID"`
	VSwitchID           string                   `bson:"VSwitchId" json:"VSwitchId,omitempty" xml:"VSwitchId,omitempty" field:"虚拟交换机ID"`
	PrivateIP           string                   `bson:"PrivateIp" json:"PrivateIp,omitempty" xml:"PrivateIp,omitempty" field:"内网IP地址"`
	EndTime             string                   `bson:"EndTime" json:"EndTime,omitempty" xml:"EndTime,omitempty" field:"到期时间"`
	HasRenewChangeOrder bool                     `bson:"HasRenewChangeOrder" json:"HasRenewChangeOrder,omitempty" xml:"HasRenewChangeOrder,omitempty" field:"是否有未生效订单"`
	IsRds               bool                     `bson:"IsRds" json:"IsRds,omitempty" xml:"IsRds,omitempty" field:"是否属RDS管控"`
	InstanceType        string                   `bson:"InstanceType" json:"InstanceType,omitempty" xml:"InstanceType,omitempty" field:"实例类型"`
	ArchitectureType    string                   `bson:"ArchitectureType" json:"ArchitectureType,omitempty" xml:"ArchitectureType,omitempty" field:"架构类型"` // cluster集群模式/standard标准模式/rwsplit读写分离模式
	NodeType            string                   `bson:"NodeType" json:"NodeType,omitempty" xml:"NodeType,omitempty" field:"节点类型"`
	PackageType         string                   `bson:"PackageType" json:"PackageType,omitempty" xml:"PackageType,omitempty" field:"套餐类型"`
	EngineVersion       string                   `bson:"EngineVersion" json:"EngineVersion,omitempty" xml:"EngineVersion,omitempty" field:"引擎版本"`
	DestroyTime         string                   `bson:"DestroyTime" json:"DestroyTime,omitempty" xml:"DestroyTime,omitempty" field:"销毁时间"`
	ConnectionMode      string                   `bson:"ConnectionMode" json:"ConnectionMode,omitempty" xml:"ConnectionMode,omitempty" field:"访问模式"`
	ResourceGroupID     string                   `bson:"ResourceGroupId" json:"ResourceGroupId,omitempty" xml:"ResourceGroupId,omitempty" field:"资源组ID"`
	ShardCount          int32                    `bson:"ShardCount" json:"ShardCount,omitempty" xml:"ShardCount,omitempty" field:"数据节点数量"`
	Tags                RedisInstanceTags        `bson:"Tags" json:"Tags,omitempty" xml:"Tags,omitempty" type:"Struct" field:"标签集合"`
	Description         string                   `bson:"Description" json:"Description" xml:"Description,omitempty" field:"实例描述" indexed:"1"`
	UpdateVersion       string                   `bson:"UpdateVersion" json:"UpdateVersion,omitempty"`
	NeedCleanup         bool                     `bson:"NeedCleanup" json:"NeedCleanup,omitempty"`
	InitTreeNode        string                   `bson:"initTreeNode" json:"initTreeNode,omitempty"`
	SecurityGroupIds    InstanceSecurityGroupIds `bson:"SecurityGroupIds" json:"SecurityGroupIds,omitempty" xml:"SecurityGroupIds,omitempty" type:"Struct" field:"安全组ID"`
	IPGroups            []InstanceIPGroup        `bson:"IPGroups" json:"IPGroups,omitempty" xml:"IPGroups,omitempty"`
}

// RedisInstanceTags redis-标签信息
type RedisInstanceTags struct {
	Tag []RedisInstanceTag `bson:"Tag" json:"Tag,omitempty" xml:"Tag,omitempty" type:"Repeated" field:"标签信息"`
}

// RedisInstanceTag redis-标签信息
type RedisInstanceTag struct {
	Key   string `bson:"Key" json:"Key,omitempty" xml:"Key,omitempty" field:"Tag的Key"`
	Value string `bson:"Value" json:"Value,omitempty" xml:"Value,omitempty" field:"Tag的Value"`
}

// CollectionName 定义collection name
func (h RedisResource) CollectionName() string {
	return h.Model.CollectionName("redis_resource")
}

// CreateIndexes 创建索引
func (h RedisResource) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return h.Model.CreateIndexes(ctx, cli, h, []mongo.IndexModel{
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"isp_type": 1}},
		{Keys: bson.M{"is_lock": 1}},
		{Keys: bson.M{"InstanceID": 1}},
	})
}

// Meta 描述信息
func (h RedisResource) Meta() []*FiledStruct {
	extraFields := []*FiledStruct{
		{
			Name:    "标签键",
			Type:    "string",
			Value:   "Tags.Tag.Key",
			Extra:   true,
			Indexed: true,
		},
		{
			Name:    "标签值",
			Type:    "string",
			Value:   "Tags.Tag.Value",
			Extra:   true,
			Indexed: true,
		},
	}
	options := GetFieldMeta(h)
	options = append(options, extraFields...)

	return options
}

// Ordering 默认排序
func (h RedisResource) Ordering() *schema.OrderField {
	return h.Model.Ordering()
}

// TransStatus ...
func (h RedisResource) TransStatus() string {
	if h.InstanceStatus == "Normal" || h.InstanceStatus == "available" {
		return "Running"
	}
	return h.InstanceStatus
}
