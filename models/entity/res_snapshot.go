package entity

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResSnapshotCollection 获取account collection
func GetResSnapshotCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResSnapshotEntity.CollectionName())
	return collection
}

// ResSnapshotEntity resSnapshot entity
var ResSnapshotEntity = new(ResSnapshot)

// ResSnapshot zone
type ResSnapshot struct {
	Model      `bson:",inline"`
	AccountID  string `bson:"account_id"`
	ResType    string `bson:"res_type"`
	Region     string `bson:"region"`
	DayTime    int64  `bson:"day_time"`
	TotalCount int    `bson:"total_count"`
}

// CollectionName 定义collection name
func (a ResSnapshot) CollectionName() string {
	return a.Model.CollectionName("res_snapshot")
}

// CreateIndexes 创建索引
func (a ResSnapshot) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	unique := true
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"account_id": 1, "res_type": 1, "region": 1, "day_time": 1}, Options: &options.IndexOptions{Unique: &unique}},
		{Keys: bson.M{"day_time": 1}},
	})
}

// Ordering 默认排序
func (a ResSnapshot) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
