package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetMysqlDatabaseAccountResCollection 获取Mysql资源collection
func GetMysqlDatabaseAccountResCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(MysqlDatabaseAccountResourceEntity.CollectionName())
	return collection
}

// MysqlDatabaseAccountResourceEntity entity
var MysqlDatabaseAccountResourceEntity = new(MysqlDatabaseAccountResource)

// MysqlDatabaseAccountResource Mysql数据库详情
type MysqlDatabaseAccountResource struct {
	Model                    `bson:",inline"`
	IsLock                   bool                              `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	IspID                    string                            `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
	IspType                  string                            `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`
	ClusterID                string                            `bson:"cluster_id" bson:"cluster_id" json:"cluster_id" field:"数据库集群ID"`
	AccountStatus            string                            `bson:"AccountStatus" json:"AccountStatus,omitempty" xml:"AccountStatus,omitempty" field:"账号状态"`
	DatabasePrivileges       []MysqlAccountsDatabasePrivileges `bson:"DatabasePrivileges" json:"DatabasePrivileges,omitempty" xml:"DatabasePrivileges,omitempty" type:"Repeated" field:"数据库权限详情"`
	AccountDescription       string                            `bson:"AccountDescription" json:"AccountDescription,omitempty" xml:"AccountDescription,omitempty" field:"账号备注"`
	AccountPasswordValidTime string                            `bson:"AccountPasswordValidTime" json:"AccountPasswordValidTime,omitempty" xml:"AccountPasswordValidTime,omitempty" field:"密码过期时间"`
	AccountType              string                            `bson:"AccountType" json:"AccountType,omitempty" xml:"AccountType,omitempty" field:"账户类型"`
	AccountLockState         string                            `bson:"AccountLockState" json:"AccountLockState,omitempty" xml:"AccountLockState,omitempty" field:"账号锁定状态"`
	AccountName              string                            `bson:"AccountName" json:"AccountName,omitempty" xml:"AccountName,omitempty" field:"账号名"`
}

// MysqlAccountsDatabasePrivileges mysql账户权限
type MysqlAccountsDatabasePrivileges struct {
	DBName           string `bson:"DBName" json:"DBName,omitempty" xml:"DBName,omitempty" field:"数据库名称"`
	AccountPrivilege string `bson:"AccountPrivilege" json:"AccountPrivilege,omitempty" xml:"AccountPrivilege,omitempty" field:"账号权限"`
}

// CollectionName 定义collection name
func (h MysqlDatabaseAccountResource) CollectionName() string {
	return h.Model.CollectionName("mysql_database_account")
}

// CreateIndexes 创建索引
func (h MysqlDatabaseAccountResource) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return h.Model.CreateIndexes(ctx, cli, h, []mongo.IndexModel{
		{Keys: bson.M{"DBName": 1}},
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"isp_type": 1}},
		{Keys: bson.M{"is_lock": 1}},
		{Keys: bson.M{"DBStatus": 1}},
		{Keys: bson.M{"cluster_id": 1}},
	})
}

// Meta 描述信息
func (h MysqlDatabaseAccountResource) Meta() []*FiledStruct {
	return GetFieldMeta(h)
}

// Ordering 默认排序
func (h MysqlDatabaseAccountResource) Ordering() *schema.OrderField {
	return h.Model.Ordering()
}
