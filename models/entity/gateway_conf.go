package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetGatewayConfCollection 获取gateway conf collection
func GetGatewayConfCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(GatewayConfEntity.CollectionName())
	return collection
}

// GatewayConfEntity gateway-conf-entity
var GatewayConfEntity = new(GatewayConf)

// GatewayConf 网关连接配置
type GatewayConf struct {
	UserModel      `bson:",inline"`
	PublicProtoc   string `bson:"public_protoc" json:"public_protoc,omitempty"`     // 公有云访问协议
	PublicAddress  string `bson:"public_address" json:"public_address,omitempty"`   // 公有云访问地址
	PrivateProtoc  string `bson:"private_protoc" json:"private_protoc,omitempty"`   // 私有云访问协议
	PrivateAddress string `bson:"private_address" json:"private_address,omitempty"` // 私有云访问地址
}

// CollectionName 定义collection name
func (a GatewayConf) CollectionName() string {
	return a.Model.CollectionName("gateway_conf")
}

// CreateIndexes 创建索引
func (a GatewayConf) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{})
}

// Ordering 默认排序
func (a GatewayConf) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
