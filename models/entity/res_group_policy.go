package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/querybuilder"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResGroupPolicyCollection 获取account collection
func GetResGroupPolicyCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResGroupPolicyEntity.CollectionName())
	return collection
}

// ResGroupPolicyEntity 资源组-自动分组策略entity
var ResGroupPolicyEntity = new(ResGroupPolicy)

// ResGroupPolicy 资源组-自动分组策略
type ResGroupPolicy struct {
	UserModel      `bson:",inline"`
	BindResGroupID string `bson:"bind_res_group_id" json:"bind_res_group_id,omitempty" validate:"required"` // 绑定资源组列表
	ResourceType   string `bson:"resource_type" json:"resource_type,omitempty" validate:"required"`         // 资源类型
	Condition      string `bson:"condition" json:"condition"`                                               // 自定义分组策略,允许为空
	// BindResGroupIDS []string `bson:"bind_res_group_ids" json:"bind_res_group_ids,omitempty" validate:"required"` // 绑定资源组列表
	// Name            string   `bson:"name" json:"name,omitempty" validate:"required"`                             // 策略名称
	// Desc            string   `bson:"desc" json:"desc,omitempty"`                                                 // 策略描述
	// ConditionRelated string         `bson:"condition_related" json:"condition_related,omitempty" validate:"required"`   // 条件间关系
}

// // ResGroupCond 资源销毁判断条件
// type ResGroupCond struct {
// 	Field    string      `bson:"field" json:"field" validate:"required"`       // 判断条件key
// 	Operator string      `bson:"operator" json:"operator" validate:"required"` // 关系: 大于/小于/包含等
// 	Value    interface{} `bson:"value" json:"value" validate:"required"`       // 值
// }

// ResGroupPolicyInfo 资源组分组详情
type ResGroupPolicyInfo struct {
	ResGroupPolicy
	ResGroupInfo ResGroup
}

// CollectionName 定义collection name
func (a ResGroupPolicy) CollectionName() string {
	return a.Model.CollectionName("res_group_policy")
}

// CreateIndexes 创建索引
func (a ResGroupPolicy) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	unique := true
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.D{{"bind_res_group_id", 1}, {"resource_type", 1}}, Options: &options.IndexOptions{
			Unique: &unique, // 唯一索引
		}},
	})
}

// Ordering 默认排序
func (a ResGroupPolicy) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}

// ToFilter to-mongo-filter
func (a ResGroupPolicy) ToFilter() (map[string]interface{}, string, error) {
	filter, errKey, err := querybuilder.ParseRuleFromBytes([]byte(a.Condition))
	if err != nil {
		return nil, errKey, err
	}
	return filter.ToMgo()
}
