package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResTemplateCollection 获取account collection
func GetResTemplateCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResTemplateEntity.CollectionName())
	return collection
}

// ResTemplateEntity 资源模板entity
var ResTemplateEntity = new(ResTemplate)

// ResTemplate 资源模板
type ResTemplate struct {
	UserModel  `bson:",inline"`
	Name       string      `bson:"name" json:"name,omitempty" validate:"required"` // 模板名称
	Desc       string      `bson:"desc" json:"desc,omitempty"`                     // 模板描述
	Type       string      `bson:"type" json:"type,omitempty" validate:"required"` // 模板类型
	ISP        string      `bson:"isp" json:"isp,omitempty" validate:"required"`   // 服务提供商
	RId        string      `bson:"rid" json:"rid" validate:"required"`             // 对应region model id
	Info       string      `bson:"info" json:"info,omitempty"`                     // 模板详情
	InitOption *InitOption `bson:"init_option" json:"init_option"`                 // 初始化配置
}

type InitOption struct {
	Lock           bool             `bson:"lock" json:"lock"`
	EnableInit     bool             `bson:"enable_init" json:"enable_init"`
	JumpserverHost string           `bson:"jumpserver_host" json:"jumpserver_host"`
	PipelineId     string           `bson:"pipeline_id" json:"pipeline_id"`
	PipelineParams []*PipelineParam `bson:"pipeline_params" json:"pipeline_params"`
	BuiltinParams  []string         `bson:"builtin_params" json:"builtin_params"`
}

type PipelineParam struct {
	Key              string `bson:"key" json:"key"`
	Value            string `bson:"value" json:"value"`
	EnableGzipBase64 bool   `bson:"enable_gzip_base64" json:"enable_gzip_base64"`
}

// CollectionName 定义collection name
func (a ResTemplate) CollectionName() string {
	return a.Model.CollectionName("res_template")
}

// CreateIndexes 创建索引
func (a ResTemplate) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"name": 1}},
		{Keys: bson.M{"isp": 1}},
		{Keys: bson.M{"type": 1}},
	})
}

// Ordering 默认排序
func (a ResTemplate) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
