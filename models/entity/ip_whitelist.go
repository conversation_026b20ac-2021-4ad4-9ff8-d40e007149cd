package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetIPWhitelistCollection 获取ip whitelist collection
func GetIPWhitelistCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(IPWhitelistEntity.CollectionName())
	return collection
}

// IPWhitelistEntity entity
var IPWhitelistEntity = new(IPWhitelist)

type IPWhitelist struct {
	UserModel             `bson:",inline"`
	SecurityIPType        string   `json:"SecurityIPType" bson:"SecurityIPType"`
	GlobalIgName          string   `json:"GlobalIgName" bson:"GlobalIgName"`
	GIPList               string   `json:"GIpList" bson:"GIPList"`
	GlobalSecurityGroupID string   `json:"GlobalSecurityGroupId" bson:"GlobalSecurityGroupId"`
	DBInstances           []string `json:"DBInstances" bson:"DBInstances"`
	WhitelistNetType      string   `json:"WhitelistNetType" bson:"WhitelistNetType"`
	RegionID              string   `json:"RegionId" bson:"RegionId"`
	IspID                 string   `json:"IspID" bson:"IspID"`
	IspType               string   `json:"IspType" bson:"IspType"`
}

func (a IPWhitelist) CollectionName() string {
	return a.UserModel.CollectionName("ip_whitelist")
}

// CreateIndexes 创建索引
func (a IPWhitelist) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"GlobalIgName": 1}},
	})
}

// Ordering 默认排序
func (a IPWhitelist) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
