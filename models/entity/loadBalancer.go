package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetLoadBalancerCollection 获取loadBalancer collection
func GetLoadBalancerCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(LoadBalancerEntity.CollectionName())
	return collection
}

// LoadBalancerEntity entity
var LoadBalancerEntity = new(LoadBalancer)

// LoadBalancer ...
type LoadBalancer struct {
	UserModel          `bson:",inline"`
	Address            string                      `json:"Address" bson:"Address"`
	AddressIPVersion   string                      `json:"AddressIPVersion" bson:"AddressIPVersion"`
	AddressType        string                      `json:"AddressType" bson:"AddressType"`
	AutoReleaseTime    int64                       `json:"AutoReleaseTime" bson:"AutoReleaseTime"`
	BackendServers     []LoadBalancerBackendServer `json:"BackendServers" bson:"BackendServers"`
	Bandwidth          int32                       `json:"Bandwidth" bson:"Bandwidth"`
	CreateTime         string                      `json:"CreateTime" bson:"CreateTime"`
	CreateTimeStamp    int64                       `json:"CreateTimeStamp" bson:"CreateTimeStamp"`
	DeleteProtection   string                      `json:"DeleteProtection" bson:"DeleteProtection"`
	EndTime            string                      `json:"EndTime" bson:"EndTime"`
	EndTimeStamp       int64                       `json:"EndTimeStamp" bson:"EndTimeStamp"`
	InstanceChargeType string                      `json:"InstanceChargeType" bson:"InstanceChargeType"`
	InternetChargeType string                      `json:"InternetChargeType" bson:"InternetChargeType"`
	Listeners          []LoadBalancerListener      `json:"listeners" bson:"listeners"`
	LoadBalancerID     string                      `json:"LoadBalancerID" bson:"LoadBalancerID"`
	LoadBalancerName   string                      `json:"LoadBalancerName" bson:"LoadBalancerName"`
	LoadBalancerSpec   string                      `json:"LoadBalancerSpec" bson:"LoadBalancerSpec"`
	LoadBalancerStatus string                      `json:"LoadBalancerStatus" bson:"LoadBalancerStatus"`
	LoadBalancerType   string                      `json:"LoadBalancerType" bson:"LoadBalancerType"` // LB类型, aliyun: ALB、SLB, aws: application, network, gateway

	// alb字段
	AddressAllocatedMode string         `json:"AddressAllocatedMode" bson:"AddressAllocatedMode"` // 地址取值 Fixed: 固定IP, Dynamic: 动态IP模式
	ALBListeners         []*ALBListener `json:"ALBListeners" bson:"ALBListeners"`                 // alb 监听

	MasterZoneID                 string         `json:"MasterZoneID" bson:"MasterZoneID"`
	ModificationProtectionReason string         `json:"ModificationProtectionReason" bson:"ModificationProtectionReason"`
	ModificationProtectionStatus string         `json:"ModificationProtectionStatus" bson:"ModificationProtectionStatus"`
	NetworkType                  string         `json:"NetworkType" bson:"NetworkType"`
	PayType                      string         `json:"PayType" bson:"PayType"`
	RegionID                     string         `json:"RegionID" bson:"RegionID"`
	RegionIDAlias                string         `json:"RegionIDAlias" bson:"RegionIDAlias"`
	RenewalCycUnit               string         `json:"RenewalCycUnit" bson:"RenewalCycUnit"`
	RenewalDuration              int32          `json:"RenewalDuration" bson:"RenewalDuration"`
	RenewalStatus                string         `json:"RenewalStatus" bson:"RenewalStatus"`
	ResourceGroupID              string         `json:"ResourceGroupID" bson:"ResourceGroupID"`
	SlaveZoneID                  string         `json:"SlaveZoneID" bson:"SlaveZoneID"`
	Tags                         []Tag          `json:"Tags" bson:"Tags"`
	VSwitchID                    string         `json:"VSwitchID" bson:"VSwitchID"`
	VpcID                        string         `json:"VpcID" bson:"VpcID"`
	IspID                        string         `bson:"isp_id" json:"isp_id" field:"云厂商ID"`
	IspType                      string         `bson:"isp_type" json:"isp_type" field:"云厂商类型"`
	NeedCleanup                  bool           `bson:"NeedCleanup" json:"NeedCleanup,omitempty"`
	UpdateVersion                string         `bson:"UpdateVersion" json:"UpdateVersion,omitempty"`
	SecurityGroupIds             *[]string      `json:"SecurityGroupIds" bson:"SecurityGroupIds"`
	Ipv6AddressType              string         `json:"Ipv6AddressType" bson:"Ipv6AddressType"`
	ZoneMappings                 []ZoneMappings `json:"ZoneMappings" bson:"ZoneMappings"`
}

type ZoneMappings struct {
	// The ID of the vSwitch in the zone. Each zone can contain only one vSwitch and one subnet.
	LoadBalancerAddresses []ZoneMappingsLoadBalancerAddresses `json:"LoadBalancerAddresses" bson:"LoadBalancerAddresses"`
	// The type of IPv6 address that is used by the ALB instance. Valid values:
	//
	// *   **Internet:** The ALB instance uses a public IP address. The domain name of the ALB instance is resolved to the public IP address. Therefore, the ALB instance can be accessed over the Internet.
	// *   **Intranet:** The ALB instance uses a private IP address. The domain name of the ALB instance is resolved to the private IP address. Therefore, the ALB instance can be accessed over the VPC in which the ALB instance is deployed.
	VSwitchId string `json:"VSwitchId" bson:"VSwitchId"`
	ZoneId    string `json:"ZoneId" bson:"ZoneId"`
}

type ZoneMappingsLoadBalancerAddresses struct {
	Address         string `json:"Address" bson:"Address"`
	AllocationId    string `json:"AllocationId" bson:"AllocationId"`
	EipType         string `json:"EipType" bson:"EipType"`
	IntranetAddress string `json:"IntranetAddress" bson:"IntranetAddress"`
	// The protocol version. Valid values:
	//
	// *   **IPv4:** IPv4.
	// *   **DualStack:** dual stack.
	Ipv6Address string `json:"Ipv6Address" bson:"Ipv6Address"`
}

type ALBListener struct {
	ListenerID           string `json:"ListenerId"`
	ListenerProtocol     string `json:"ListenerProtocol"`
	ListenerPort         int32  `json:"ListenerPort"`
	DefaultServerGroupID string `json:"DefaultServerGroupID"` // 默认转发的服务器组ID
	IdleTimeout          int32  `json:"IdleTimeout"`
	RequestTimeout       int32  `json:"RequestTimeout"`
	GzipEnabled          bool   `json:"GzipEnabled"`
	ListenerStatus       string `json:"ListenerStatus"`
	SecurityPolicyID     string `json:"SecurityPolicyId"` // 仅HTTPs监听有用
	ListenerDescription  string `json:"ListenerDescription"`
}

// LoadBalancerListener ...
type LoadBalancerListener struct {
	ACLID             string `json:"AclId" bson:"AclId"`
	ACLStatus         string `json:"AclStatus" bson:"AclStatus"`
	ACLType           string `json:"AclType" bson:"AclType"`
	BackendServerPort int32  `json:"BackendServerPort" bson:"BackendServerPort"`
	Bandwidth         int32  `json:"Bandwidth" bson:"Bandwidth"`
	Description       string `json:"Description" bson:"Description"`
	ListenerPort      int32  `json:"ListenerPort" bson:"ListenerPort"`
	ListenerProtocol  string `json:"ListenerProtocol" bson:"ListenerProtocol"`
	LoadBalancerID    string `json:"LoadBalancerId" bson:"LoadBalancerId"`
	Scheduler         string `json:"Scheduler" bson:"Scheduler"`
	Status            string `json:"Status" bson:"Status"`
	VServerGroupID    string `json:"VServerGroupId" bson:"VServerGroupId"`
}

// LoadBalancerBackendServer ...
type LoadBalancerBackendServer struct {
	Description string `json:"Description" bson:"Description"`
	ServerID    string `json:"ServerID" bson:"ServerID"`
	ServerIP    string `json:"ServerIP" bson:"ServerIP"`
	Type        string `json:"Type" bson:"Type"`
	Weight      int32  `json:"Weight" bson:"Weight"`
}

// LoadBalancerListenerPortsAndProtocal ...
type LoadBalancerListenerPortsAndProtocal struct {
	ListenerPort     int32  `json:"ListenerPort" bson:"ListenerPort"`
	ListenerProtocal string `json:"ListenerProtocal" bson:"ListenerProtocal"`
}

// LoadBalancerListenerPortAndProtocol ...
type LoadBalancerListenerPortAndProtocol struct {
	Description      string `json:"Description" bson:"Description"`
	ForwardPort      int32  `json:"ForwardPort" bson:"ForwardPort"`
	ListenerForward  string `json:"ListenerForward" bson:"ListenerForward"`
	ListenerPort     int32  `json:"ListenerPort" bson:"ListenerPort"`
	ListenerProtocol string `json:"ListenerProtocol" bson:"ListenerProtocol"`
}

// CollectionName 定义collection name
func (a LoadBalancer) CollectionName() string {
	return a.UserModel.CollectionName("load_balancer")
}

// CreateIndexes 创建索引
func (a LoadBalancer) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"LoadBalancerID": 1}},
	})
}

// Ordering 默认排序
func (a LoadBalancer) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
