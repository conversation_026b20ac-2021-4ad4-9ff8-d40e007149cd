package entity

import (
	"context"
	"encoding/json"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetSyncTaskCollection 获取account collection
func GetSyncTaskCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(SyncTaskEntity.CollectionName())
	return collection
}

// SyncTaskEntity entity
var SyncTaskEntity = new(SyncTask)

// SyncTask sync task model
type SyncTask struct {
	UserModel            `bson:",inline"`
	Name                 string `bson:"name" json:"name,omitempty"`                                   // 任务名称
	AccountID            string `bson:"account_id" json:"account_id,omitempty"`                       // 执行对象ID
	BindRegionID         string `bson:"bind_region_id" json:"bind_region_id,omitempty"`               // 绑定地域ID
	BindRegionName       string `bson:"bind_region_name" json:"bind_region_name,omitempty"`           // 绑定地域
	BindRegionSourceID   string `bson:"bind_region_source_id" json:"bind_region_source_id"`           // region原始ID
	BindJobmanTaskID     string `bson:"bind_jobman_task_id" json:"bind_jobman_task_id"`               // 绑定作业平台定时任务id
	BindJobmanTemplateID string `bson:"bind_jobman_template_id" json:"bind_jobman_template_id"`       // 绑定作业平台定时任务作业模板id
	TaskType             string `bson:"task_type" json:"task_type,omitempty"`                         // 任务类型
	Policy               uint   `bson:"policy" json:"policy,omitempty"`                               // 同步策略, 0: 周期同步
	Rate                 uint   `bson:"rate" json:"rate,omitempty"`                                   // 同步频率
	Retry                uint   `bson:"retry" json:"retry,omitempty"`                                 // 重试次数
	Timeout              uint   `bson:"timeout" json:"timeout,omitempty"`                             // 超时时间
	Status               uint   `bson:"status" json:"status"`                                         // 是否启用
	LatestRunID          string `bson:"latest_run_id" json:"latest_run_id,omitempty"`                 // 任务最后运行id
	LatestRunStatus      int    `bson:"latest_run_status" json:"latest_run_status,omitempty"`         // 任务最后运行状态
	LatestRunStartTime   int64  `bson:"latest_run_start_time" json:"latest_run_start_time,omitempty"` // 任务最后运行开始时间
	LatestRunEndTime     int64  `bson:"latest_run_end_time" json:"latest_run_end_time,omitempty"`     // 任务最后运行结束时间
}

// CollectionName 定义collection name
func (a SyncTask) CollectionName() string {
	return a.UserModel.CollectionName("SyncTask")
}

// CreateIndexes 创建索引
func (a SyncTask) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"name": 1}},
		{Keys: bson.M{"status": 1}},
	})
}

// Ordering 默认排序
func (a SyncTask) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}

func (a SyncTask) String() string {
	buf, _ := json.Marshal(a)
	return string(buf)
}
