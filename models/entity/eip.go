package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetEipCollection 获取eip collection
func GetEipCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(EipEntity.CollectionName())
	return collection
}

// EipEntity entity
var EipEntity = new(Eip)

// Eip ...
type Eip struct {
	UserModel                     `bson:",inline"`
	AllocationID                  string                     `json:"AllocationId" bson:"AllocationId"`
	AllocationTime                string                     `json:"AllocationTime" bson:"AllocationTime"`
	Bandwidth                     string                     `json:"Bandwidth" bson:"Bandwidth"`
	BandwidthPackageBandwidth     string                     `json:"BandwidthPackageBandwidth" bson:"BandwidthPackageBandwidth"`
	BandwidthPackageID            string                     `json:"BandwidthPackageId" bson:"BandwidthPackageId"`
	BandwidthPackageType          string                     `json:"BandwidthPackageType" bson:"BandwidthPackageType"`
	BizType                       string                     `json:"BizType" bson:"BizType"`
	BusinessStatus                string                     `json:"BusinessStatus" bson:"BusinessStatus"`
	ChargeType                    string                     `json:"ChargeType" bson:"ChargeType"`
	DeletionProtection            bool                       `json:"DeletionProtection" bson:"DeletionProtection"`
	Description                   string                     `json:"Description" bson:"Description"`
	EipBandwidth                  string                     `json:"EipBandwidth" bson:"EipBandwidth"`
	ExpiredTime                   string                     `json:"ExpiredTime" bson:"ExpiredTime"`
	HDMonitorStatus               string                     `json:"HDMonitorStatus" bson:"HDMonitorStatus"`
	HasReservationData            string                     `json:"HasReservationData" bson:"HasReservationData"`
	ISP                           string                     `json:"ISP" bson:"ISP"`
	InstanceID                    string                     `json:"InstanceId" bson:"InstanceId"`
	InstanceRegionID              string                     `json:"InstanceRegionId" bson:"InstanceRegionId"`
	InstanceType                  string                     `json:"InstanceType" bson:"InstanceType"`
	InternetChargeType            string                     `json:"InternetChargeType" bson:"InternetChargeType"`
	IPAddress                     string                     `json:"IpAddress" bson:"IpAddress"`
	Name                          string                     `json:"Name" bson:"Name"`
	Netmode                       string                     `json:"Netmode" bson:"Netmode"`
	OperationLocks                []EipAddressOperationLocks `json:"OperationLocks" bson:"OperationLocks"`
	PublicIPAddressPoolID         string                     `json:"PublicIpAddressPoolId" bson:"PublicIpAddressPoolId"`
	RegionID                      string                     `json:"RegionId" bson:"RegionId"`
	ReservationActiveTime         string                     `json:"ReservationActiveTime" bson:"ReservationActiveTime"`
	ReservationBandwidth          string                     `json:"ReservationBandwidth" bson:"ReservationBandwidth"`
	ReservationInternetChargeType string                     `json:"ReservationInternetChargeType" bson:"ReservationInternetChargeType"`
	ReservationOrderType          string                     `json:"ReservationOrderType" bson:"ReservationOrderType"`
	ResourceGroupID               string                     `json:"ResourceGroupId" bson:"ResourceGroupId"`
	SecondLimited                 bool                       `json:"SecondLimited" bson:"SecondLimited"`
	SecurityProtectionTypes       []string                   `json:"SecurityProtectionTypes" bson:"SecurityProtectionTypes"`
	SegmentInstanceID             string                     `json:"SegmentInstanceId" bson:"SegmentInstanceId"`
	ServiceManaged                int32                      `json:"ServiceManaged" bson:"ServiceManaged"`
	Status                        string                     `json:"Status" bson:"Status"`
	Tags                          []Tag                      `json:"Tags" bson:"Tags"`
	VpcID                         string                     `json:"VpcId" bson:"VpcId"`
	Zone                          string                     `json:"Zone" bson:"Zone"`
	IspID                         string                     `bson:"isp_id" json:"isp_id" field:"云厂商ID"`
	IspType                       string                     `bson:"isp_type" json:"isp_type" field:"云厂商类型"`
}

// EipAddressOperationLocks ...
type EipAddressOperationLocks struct {
	LockReason string `json:"LockReason" bson:"LockReason"`
}

// CollectionName 定义collection name
func (a Eip) CollectionName() string {
	return a.UserModel.CollectionName("eip")
}

// CreateIndexes 创建索引
func (a Eip) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"AllocationId": 1}},
	})
}

// Ordering 默认排序
func (a Eip) Ordering() *schema.OrderField {
	return a.UserModel.Ordering()
}
