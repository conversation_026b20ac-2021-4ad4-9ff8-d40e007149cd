package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetAccountParamCollection 获取collection
func GetAccountParamCollection(cli *mongo.Client) *mongo.Collection {

	collection := cli.Database(cfg.GetDBName()).Collection(AccountParamEntity.CollectionName())
	return collection
}

// AccountParamEntity region-param entity
var AccountParamEntity = new(AccountParam)

// AccountParam AccountParam-model
type AccountParam struct {
	UserModel     `bson:",inline"`
	BindAccountID string `bson:"bind_account_id" json:"bind_account_id" validate:"required"`
	ParamType     string `bson:"param_type" json:"param_type" validate:"required"`         // 参数类型
	BindRegionID  string `bson:"bind_region_id" json:"bind_region_id" validate:"required"` // 绑定地域名称
	Params        string `bson:"params" json:"params" validate:"required"`                 // 绑定的参数
	Status        string `bson:"status" json:"status" validate:"oneof=enabled disabled"`   // 是否激活模板
}

// CollectionName 定义collection name
func (a AccountParam) CollectionName() string {
	return a.Model.CollectionName("account_param")
}

// CreateIndexes 创建索引
func (a AccountParam) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"bind_account_id": 1}},
		{Keys: bson.D{{"bind_account_id", 1}, {"param_type", 1}, {"bind_region_id", 1}, {"is_delete", 1}}},
	})
}

// Ordering 默认排序
func (a AccountParam) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
