package entity

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetHostResCollection 获取主机资源collection
func GetHostResCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(HostResourceEntity.CollectionName())
	return collection
}

// HostResourceEntity entity
var HostResourceEntity = new(HostResource)

// HostResource 主机资源
type HostResource struct {
	Model                      `bson:",inline"`
	HostAgent                  `bson:",inline"`
	HostMonitor                `bson:",inline"`
	InstanceName               string                             `bson:"InstanceName" json:"InstanceName,omitempty" xml:"InstanceName,omitempty" field:"实例名称" indexed:"1"`
	InstanceID                 string                             `bson:"InstanceID" json:"InstanceID,omitempty" xml:"InstanceID,omitempty" field:"实例ID" indexed:"1"`
	AllowChangeData            bool                               `bson:"allow_change_data" json:"allow_change_data,omitempty" field:"允许修正数据(限自定义厂商)" indexed:"1"` // 是否允许agent修正数据
	IsLock                     bool                               `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	IspID                      string                             `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
	IspType                    string                             `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`
	CreationTime               int64                              `bson:"CreationTime" json:"CreationTime,omitempty" xml:"CreationTime,omitempty" field_type:"UnixTime" field:"实例创建时间"`
	SerialNumber               string                             `bson:"SerialNumber" json:"SerialNumber,omitempty" xml:"SerialNumber,omitempty" field:"实例序列号"`
	DedicatedHostAttribute     InstanceDedicatedHostAttribute     `bson:"DedicatedHostAttribute" json:"DedicatedHostAttribute,omitempty" xml:"DedicatedHostAttribute,omitempty" type:"Struct"`
	OperationLocks             InstanceOperationLocks             `bson:"OperationLocks" json:"OperationLocks,omitempty" xml:"OperationLocks,omitempty" type:"Struct"`
	Tags                       InstanceTags                       `bson:"Tags" json:"Tags,omitempty" xml:"Tags,omitempty" type:"Struct" field:"标签集合"`
	SaleCycle                  string                             `bson:"SaleCycle" json:"SaleCycle,omitempty" xml:"SaleCycle,omitempty" field:"实例计费周期"`
	PublicIPAddress            []string                           `bson:"PublicIpAddress" json:"PublicIpAddress,omitempty" xml:"PublicIpAddress,omitempty" field_type:"SliceString" type:"Repeated" field:"公网IP列表" indexed:"1"`
	SpotStrategy               string                             `bson:"SpotStrategy" json:"SpotStrategy,omitempty" xml:"SpotStrategy,omitempty" field:"抢占策略"`
	DeviceAvailable            bool                               `bson:"DeviceAvailable" json:"DeviceAvailable,omitempty" xml:"DeviceAvailable,omitempty" field:"是否可以挂载数据盘"`
	EipAddress                 InstanceEipAddress                 `bson:"EipAddress" json:"EipAddress,omitempty" xml:"EipAddress,omitempty" type:"Struct" field:"弹性公网IP列表"`
	InstanceNetworkType        string                             `bson:"InstanceNetworkType" json:"InstanceNetworkType,omitempty" xml:"InstanceNetworkType,omitempty" field:"实例网络类型"`
	SpotDuration               int32                              `bson:"SpotDuration" json:"SpotDuration,omitempty" xml:"SpotDuration,omitempty" field:"实例的保留时长"`
	OSNameEn                   string                             `bson:"OSNameEn" json:"OSNameEn,omitempty" xml:"OSNameEn,omitempty" field:"操作系统名称(en)"`
	SpotPriceLimit             float32                            `bson:"SpotPriceLimit" json:"SpotPriceLimit,omitempty" xml:"SpotPriceLimit,omitempty" field:"每小时最高价格"`
	OSName                     string                             `bson:"OSName" json:"OSName,omitempty" xml:"OSName,omitempty" field:"操作系统名称" validate:"required"`
	ImageID                    string                             `bson:"ImageID" json:"ImageID,omitempty" xml:"ImageID,omitempty" field:"实例运行的镜像ID"`
	DeploymentSetGroupNo       int32                              `bson:"DeploymentSetGroupNo" json:"DeploymentSetGroupNo,omitempty" xml:"DeploymentSetGroupNo,omitempty" field:"实例部署位置"`
	ClusterID                  string                             `bson:"ClusterId" json:"ClusterId,omitempty" xml:"ClusterId,omitempty" field:"实例所在的集群ID"`
	DedicatedInstanceAttribute InstanceDedicatedInstanceAttribute `bson:"DedicatedInstanceAttribute" json:"DedicatedInstanceAttribute,omitempty" xml:"DedicatedInstanceAttribute,omitempty" type:"Struct"`
	GPUSpec                    string                             `bson:"GPUSpec" json:"GPUSpec,omitempty" xml:"GPUSpec,omitempty" field:"实例GPU类型"`
	AutoReleaseTime            string                             `bson:"AutoReleaseTime" json:"AutoReleaseTime,omitempty" xml:"AutoReleaseTime,omitempty" field:"实例自动释放时间"`
	InnerIPAddress             []string                           `bson:"InnerIpAddress" json:"InnerIpAddress,omitempty" xml:"InnerIpAddress,omitempty" field_type:"SliceString" type:"Repeated" field:"内网IP列表" validate:"required" indexed:"1"` // 内网ip,阿里云经典网络内网ip取值为inneripaddress,vpc网络为vpc-PrivateIPAddress,同步时将自动合并该选项
	StoppedMode                string                             `bson:"StoppedMode" json:"StoppedMode,omitempty" xml:"StoppedMode,omitempty" field:"实例停机后是否继续收费"`
	GPUAmount                  int32                              `bson:"GPUAmount" json:"GPUAmount,omitempty" xml:"GPUAmount,omitempty" field:"实例附带的GPU数量"`
	HostName                   string                             `bson:"HostName" json:"HostName,omitempty" xml:"HostName,omitempty" field:"实例主机名" validate:"required" indexed:"1"`
	InstanceType               string                             `bson:"InstanceType" json:"InstanceType,omitempty" xml:"InstanceType,omitempty" field:"实例规格"`
	InstanceChargeType         string                             `bson:"InstanceChargeType" json:"InstanceChargeType,omitempty" xml:"InstanceChargeType,omitempty" field:"实例计费方式"`
	StartTime                  string                             `bson:"StartTime" json:"StartTime,omitempty" xml:"StartTime,omitempty" field:"实例启动时间"`
	InternetChargeType         string                             `bson:"InternetChargeType" json:"InternetChargeType,omitempty" xml:"InternetChargeType,omitempty" field:"网络计费类型"`
	ZoneID                     string                             `bson:"ZoneID" json:"ZoneID,omitempty" xml:"ZoneID,omitempty" field:"实例所属可用区"`
	Recyclable                 bool                               `bson:"Recyclable" json:"Recyclable,omitempty" xml:"Recyclable,omitempty" field:"实例是否可以回收"`
	CreditSpecification        string                             `bson:"CreditSpecification" json:"CreditSpecification,omitempty" xml:"CreditSpecification,omitempty" field:"实例运行模式"`
	OSType                     string                             `bson:"OSType" json:"OSType,omitempty" xml:"OSType,omitempty" field:"实例操作系统类型"`
	Status                     string                             `bson:"Status" json:"Status,omitempty" xml:"Status,omitempty" field:"实例状态"`
	DeploymentSetID            string                             `bson:"DeploymentSetId" json:"DeploymentSetId,omitempty" xml:"DeploymentSetId,omitempty" field:"部署集ID"`
	KeyPairName                string                             `bson:"KeyPairName" json:"KeyPairName,omitempty" xml:"KeyPairName,omitempty" field:"密钥对名称"`
	CPUOptions                 InstanceCPUOptions                 `bson:"CpuOptions" json:"CpuOptions,omitempty" xml:"CpuOptions,omitempty" type:"Struct" field:"CPU配置详情"`
	LocalStorageCapacity       int64                              `bson:"LocalStorageCapacity" json:"LocalStorageCapacity,omitempty" xml:"LocalStorageCapacity,omitempty" field:"实例挂载的本地存储容量"`
	Description                string                             `bson:"Description" json:"Description" xml:"Description,omitempty" field:"实例描述" indexed:"1"`
	HpcClusterID               string                             `bson:"HpcClusterId" json:"HpcClusterId,omitempty" xml:"HpcClusterId,omitempty" field:"实例所属的HPC集群ID"`
	Memory                     int32                              `bson:"Memory" json:"Memory,omitempty" xml:"Memory,omitempty" field:"内存大小(MiB)" validate:"required"`
	VlanID                     string                             `bson:"VlanId" json:"VlanId,omitempty" xml:"VlanId,omitempty" field:"实例的VLAN-ID"`
	VpcAttributes              InstanceVpcAttributes              `bson:"VpcAttributes" json:"VpcAttributes,omitempty" xml:"VpcAttributes,omitempty" type:"Struct" field:"专有网络VPC属性"`
	DeletionProtection         bool                               `bson:"DeletionProtection" json:"DeletionProtection,omitempty" xml:"DeletionProtection,omitempty" field:"开启实例释放保护"`
	EcsCapacityReservationAttr InstanceEcsCapacityReservationAttr `bson:"EcsCapacityReservationAttr" json:"EcsCapacityReservationAttr,omitempty" xml:"EcsCapacityReservationAttr,omitempty" type:"Struct" field:"预留容量参数"`
	InternetMaxBandwidthIn     int32                              `bson:"InternetMaxBandwidthIn" json:"InternetMaxBandwidthIn,omitempty" xml:"InternetMaxBandwidthIn,omitempty" field:"公网入带宽最大值(M/s)"`
	InternetMaxBandwidthOut    int32                              `bson:"InternetMaxBandwidthOut" json:"InternetMaxBandwidthOut,omitempty" xml:"InternetMaxBandwidthOut,omitempty" field:"公网出带宽最大值(M/s)"`
	SecurityGroupIds           InstanceSecurityGroupIds           `bson:"SecurityGroupIds" json:"SecurityGroupIds,omitempty" xml:"SecurityGroupIds,omitempty" type:"Struct" field:"安全组ID"`
	RegionID                   string                             `bson:"RegionID" json:"RegionID,omitempty" xml:"RegionID,omitempty" field:"实例所属地域ID"`
	IoOptimized                bool                               `bson:"IoOptimized" json:"IoOptimized,omitempty" xml:"IoOptimized,omitempty" field:"是否为I/O优化型实例"`
	CPU                        int32                              `bson:"CPU" json:"CPU,omitempty" xml:"CPU,omitempty" field:"vCPU数" validate:"required"`
	RdmaIPAddress              []string                           `bson:"RdmaIpAddress" json:"RdmaIpAddress,omitempty" xml:"RdmaIpAddress,omitempty" field_type:"SliceString" type:"Repeated" field:"RdmaIpAddress"`
	ExpiredTime                int64                              `bson:"ExpiredTime" json:"ExpiredTime,omitempty" xml:"ExpiredTime,omitempty" field_type:"UnixTime" field:"实例过期时间"`
	LocalStorageAmount         int32                              `bson:"LocalStorageAmount" json:"LocalStorageAmount,omitempty" xml:"LocalStorageAmount,omitempty" field:"实例挂载的本地存储数量"`
	ResourceGroupID            string                             `bson:"ResourceGroupId" json:"ResourceGroupId,omitempty" xml:"ResourceGroupId,omitempty" field:"实例所属的企业资源组ID"`
	InstanceTypeFamily         string                             `bson:"InstanceTypeFamily" json:"InstanceTypeFamily,omitempty" xml:"InstanceTypeFamily,omitempty" field:"实例规格族"`
	ISP                        string                             `bson:"ISP" json:"ISP,omitempty" xml:"ISP,omitempty" field:"运营商(beta)"`
	NetworkInterfaces          InstanceNetworkInterfaces          `bson:"NetworkInterfaces" json:"NetworkInterfaces,omitempty" xml:"NetworkInterfaces,omitempty" type:"Struct" field:"实例弹性网卡属性"`
	UpdateVersion              string                             `bson:"UpdateVersion" json:"UpdateVersion,omitempty"`
	NeedCleanup                bool                               `bson:"NeedCleanup" json:"NeedCleanup,omitempty"`
	InitTreeNode               string                             `bson:"initTreeNode" json:"initTreeNode,omitempty"`
	BkCmdb                     *BkCmdbInfo                        `bson:"BkCmdb" json:"BkCmdb,omitempty"`
}

// HostAgent 主机agent
type HostAgent struct {
	AgentStatus  int32  `bson:"agent_status" json:"agent_status,omitempty" field:"运维Agent状态" field_type:"AgentStatus"`
	AgentType    string `bson:"agent_type" json:"agent_type,omitempty" field:"运维agent类型"`
	AgentVersion string `bson:"agent_version" json:"agent_version,omitempty" field:"运维agent运行版本"`
	AgentID      string `bson:"agent_id" json:"agent_id,omitempty" field:"运维agentID"`
	AgentEnv     string `bson:"agent_env" json:"agent_env,omitempty" field:"运维agent环境"`
	AgentUpdate  int64  `bson:"agent_update" json:"agent_update,omitempty" field:"运维agent上次更新时间"`
}

// HostMonitor 主机Monitor
type HostMonitor struct {
	MonitorStatus int32 `bson:"MonitorStatus" json:"MonitorStatus,omitempty" field:"运维Monitor状态"`
}

// MonitorStatusToString 将监控状态转化为字符串
func (h *HostResource) MonitorStatusToString() string {
	if h.HostMonitor.MonitorStatus == 0 {
		return "Lost"
	} else if h.HostMonitor.MonitorStatus == 1 {
		return "Running"
	}
	return ""
}

// InstanceDedicatedHostAttribute 专有宿主机属性
type InstanceDedicatedHostAttribute struct {
	// DedicatedHostName 专有宿主机名称
	DedicatedHostName string `bson:"DedicatedHostName" json:"DedicatedHostName,omitempty" xml:"DedicatedHostName,omitempty" field:"专有宿主机名称"`
	// DedicatedHostClusterId 专有宿主机集群ID
	DedicatedHostClusterID string `bson:"DedicatedHostClusterId" json:"DedicatedHostClusterId,omitempty" xml:"DedicatedHostClusterId,omitempty" field:"专有宿主机集群ID"`
	// DedicatedHostID 专有宿主机ID
	DedicatedHostID string `bson:"DedicatedHostId" json:"DedicatedHostId,omitempty" xml:"DedicatedHostId,omitempty" field:"专有宿主机ID"`
}

// InstanceOperationLocks 锁定原因集合
type InstanceOperationLocks struct {
	// LockReason 锁定原因集合
	LockReason []LockReason `bson:"LockReason" json:"LockReason,omitempty" xml:"LockReason,omitempty" type:"Repeated" field:"锁定原因集合"`
}

// LockReason 锁定原因
type LockReason struct {
	// LockReason 锁定原因
	LockReason string `bson:"LockReason" json:"LockReason,omitempty" xml:"LockReason,omitempty" field:"锁定原因"`
	// LockMsg 锁定原因描述
	LockMsg string `bson:"LockMsg" json:"LockMsg,omitempty" xml:"LockMsg,omitempty" field:"锁定原因描述"`
}

// InstanceTags 实例标签列表
type InstanceTags struct {
	// Tag 标签集合
	Tag []Tag `bson:"Tag" json:"Tag,omitempty" xml:"Tag,omitempty" type:"Repeated" field:"标签集合"`
}

// InstanceEipAddress eip
type InstanceEipAddress struct {
	// IPAddress 弹性公网IP
	IPAddress string `bson:"IpAddress" json:"IpAddress,omitempty" xml:"IpAddress,omitempty" field:"弹性公网IP"`
	// Bandwidth 弹性公网带宽限速(M/s)
	Bandwidth int32 `bson:"Bandwidth" json:"Bandwidth,omitempty" xml:"Bandwidth,omitempty" field:"弹性公网带宽限速(M/s)"`
	// AllocationID 弹性公网IP的ID
	AllocationID string `bson:"AllocationId" json:"AllocationId,omitempty" xml:"AllocationId,omitempty" field:"弹性公网IP的ID"`
	// IsSupportUnassociate 是否可以解绑弹性公网IP
	IsSupportUnassociate bool `bson:"IsSupportUnassociate" json:"IsSupportUnassociate,omitempty" xml:"IsSupportUnassociate,omitempty" field:"是否可以解绑弹性公网IP"`
	// InternetChargeType 弹性公网IP的计费方式
	InternetChargeType string `bson:"InternetChargeType" json:"InternetChargeType,omitempty" xml:"InternetChargeType,omitempty" field:"弹性公网IP的计费方式"`
}

// InstanceDedicatedInstanceAttribute 专有宿主机
type InstanceDedicatedInstanceAttribute struct {
	// Affinity 是否与专有宿主机关联
	Affinity string `bson:"Affinity" json:"Affinity,omitempty" xml:"Affinity,omitempty" field:"是否与专有宿主机关联"`
	// Tenancy 是否为专有宿主机
	Tenancy string `bson:"Tenancy" json:"Tenancy,omitempty" xml:"Tenancy,omitempty" field:"是否为专有宿主机"`
}

// IPAddress ip-address
type IPAddress struct {
	// IPAddress ip-address
	IPAddress []string `bson:"IpAddress" json:"IpAddress,omitempty" xml:"IpAddress,omitempty" type:"Repeated" field:"ip-address"`
}

// InstanceCPUOptions CPU option
type InstanceCPUOptions struct {
	// Numa 分配的线程数
	Numa string `bson:"Numa" json:"Numa,omitempty" xml:"Numa,omitempty" field:"分配的线程数"`
	// CoreCount 物理CPU核心数
	CoreCount int32 `bson:"CoreCount" json:"CoreCount,omitempty" xml:"CoreCount,omitempty" field:"物理CPU核心数"`
	// ThreadsPerCore CPU线程数
	ThreadsPerCore int32 `bson:"ThreadsPerCore" json:"ThreadsPerCore,omitempty" xml:"ThreadsPerCore,omitempty" field:"CPU线程数"`
}

// InstanceVpcAttributes vpc 属性
type InstanceVpcAttributes struct {
	// VpcID 专有网络VPC-ID
	VpcID string `bson:"VpcId" json:"VpcId,omitempty" xml:"VpcId,omitempty" field:"专有网络VPC-ID"`
	// NatIPAddress 云产品的IP
	NatIPAddress string `bson:"NatIpAddress" json:"NatIpAddress,omitempty" xml:"NatIpAddress,omitempty" field:"云产品的IP"`
	// VSwitchID 虚拟交换机ID
	VSwitchID string `bson:"VSwitchId" json:"VSwitchId,omitempty" xml:"VSwitchId,omitempty" field:"虚拟交换机ID"`
	// PrivateIPAddress 私有ip地址
	PrivateIPAddress IPAddress `bson:"PrivateIpAddress" json:"PrivateIpAddress,omitempty" xml:"PrivateIpAddress,omitempty" type:"Struct" field:"私有ip地址"`
}

// InstanceEcsCapacityReservationAttr 容量属性
type InstanceEcsCapacityReservationAttr struct {
	// CapacityReservationPreference 容量预留偏好
	CapacityReservationPreference string `bson:"CapacityReservationPreference" json:"CapacityReservationPreference,omitempty" xml:"CapacityReservationPreference,omitempty" field:"容量预留偏好"`
	// CapacityReservationID 容量预留ID
	CapacityReservationID string `bson:"CapacityReservationId" json:"CapacityReservationId,omitempty" xml:"CapacityReservationId,omitempty" field:"容量预留ID"`
}

// InstanceSecurityGroupIds 实例安全组
type InstanceSecurityGroupIds struct {
	// SecurityGroupID 实例安全组
	SecurityGroupID []string `bson:"SecurityGroupId" json:"SecurityGroupId,omitempty" xml:"SecurityGroupId,omitempty" type:"Repeated" field:"安全组集合"`
}

// InstanceIPGroup ...
type InstanceIPGroup struct {
	SecurityIPGroupAttribute string   `bson:"DBClusterIPArrayAttribute" json:"DBClusterIPArrayAttribute,omitempty" xml:"DBClusterIPArrayAttribute,omitempty"`
	SecurityIPGroupName      string   `bson:"DBClusterIPArrayName" json:"DBClusterIPArrayName,omitempty" xml:"DBClusterIPArrayName,omitempty"`
	SecurityIPList           []string `bson:"SecurityIps" json:"SecurityIps,omitempty" xml:"SecurityIps,omitempty"`
}

// InstanceNetworkInterfaces 弹性网卡接口
type InstanceNetworkInterfaces struct {
	NetworkInterface []InstanceNetworkInterface `bson:"NetworkInterface" json:"NetworkInterface,omitempty" xml:"NetworkInterface,omitempty" type:"Repeated" field:"网络集合"`
}

// InstanceNetworkInterface 弹性网卡参数
type InstanceNetworkInterface struct {
	Type               string                 `bson:"Type" json:"Type,omitempty" xml:"Type,omitempty" field:"弹性网卡类型"`
	MacAddress         string                 `bson:"MacAddress" json:"MacAddress,omitempty" xml:"MacAddress,omitempty" field:"弹性网卡的MAC地址"`
	NetworkInterfaceID string                 `bson:"NetworkInterfaceId" json:"NetworkInterfaceId,omitempty" xml:"NetworkInterfaceId,omitempty" field:"弹性网卡的ID"`
	Ipv6Sets           InterfaceIpv6Sets      `bson:"Ipv6Sets" json:"Ipv6Sets,omitempty" xml:"Ipv6Sets,omitempty" type:"Struct" field:"IPV6集合"`
	PrimaryIPAddress   string                 `bson:"PrimaryIpAddress" json:"PrimaryIpAddress,omitempty" xml:"PrimaryIpAddress,omitempty" field:"弹性网卡主私有IP地址"`
	PrivateIPSets      InterfacePrivateIPSets `bson:"PrivateIpSets" json:"PrivateIpSets,omitempty" xml:"PrivateIpSets,omitempty" type:"Struct" field:"PrivateIpSet组成的集合"`
	Bandwidth          string                 `bson:"Bandwidth" json:"Bandwidth,omitempty" xml:"Bandwidth,omitempty"`
}

// InterfaceIpv6Sets IPV6集合
type InterfaceIpv6Sets struct {
	// Ipv6Set IPV6集合
	Ipv6Set []InterfaceIpv6SetsIpv6Set `bson:"Ipv6Set" json:"Ipv6Set,omitempty" xml:"Ipv6Set,omitempty" type:"Repeated" field:"IPV6集合"`
}

// InterfaceIpv6SetsIpv6Set IPv6地址
type InterfaceIpv6SetsIpv6Set struct {
	// Ipv6Address IPv6地址
	Ipv6Address string `bson:"Ipv6Address" json:"Ipv6Address,omitempty" xml:"Ipv6Address,omitempty" field:"IPv6地址"`
}

// InterfacePrivateIPSets 私有IP集合
type InterfacePrivateIPSets struct {
	// PrivateIpSet 私有IP集合
	PrivateIPSet []InterfacePrivateIPSetsPrivateIPSet `bson:"PrivateIpSet" json:"PrivateIpSet,omitempty" xml:"PrivateIpSet,omitempty" type:"Repeated" field:"私有IP集合"`
}

// InterfacePrivateIPSetsPrivateIPSet 私有IP集合
type InterfacePrivateIPSetsPrivateIPSet struct {
	// Primary 是否是主私网IP地址
	Primary bool `bson:"Primary" json:"Primary,omitempty" xml:"Primary,omitempty" field:"是否是主私网IP地址"`
	// PrivateIpAddress 实例的私网IP地址
	PrivateIPAddress string `bson:"PrivateIpAddress" json:"PrivateIpAddress,omitempty" xml:"PrivateIpAddress,omitempty" field:"实例的私网IP地址"`
}

// BkCmdbInfo cmdb记录
type BkCmdbInfo struct {
	HostID         int32    `json:"HostID" bson:"HostID" `
	InstID         int32    `json:"InstId" bson:"InstId" `
	GameRegionName []string `json:"GameRegionName" bson:"GameRegionName" `
	ProcessName    []string `json:"ProcessName" bson:"ProcessName" `
}

// CollectionName 定义collection name
func (h HostResource) CollectionName() string {
	return h.Model.CollectionName("host_resource")
}

// CreateIndexes 创建索引
func (h HostResource) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return h.Model.CreateIndexes(ctx, cli, h, []mongo.IndexModel{
		{Keys: bson.M{"InstanceID": 1}},
		{Keys: bson.M{"InstanceType": 1}},
		{Keys: bson.M{"InstanceName": 1}},
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"isp_type": 1}},
		{Keys: bson.M{"Status": 1}},
		{Keys: bson.M{"Description": 1}},
		{Keys: bson.M{"InnerIpAddress": 1}},
	})
}

// Meta 描述信息
func (h HostResource) Meta() []*FiledStruct {
	extraFields := []*FiledStruct{
		{
			Name:    "标签键",
			Type:    "string",
			Value:   "Tags.Tag.TagKey",
			Extra:   true,
			Indexed: true,
		},
		{
			Name:    "标签值",
			Type:    "string",
			Value:   "Tags.Tag.TagValue",
			Extra:   true,
			Indexed: true,
		},
	}
	options := GetFieldMeta(h)
	options = append(options, extraFields...)

	return options
}

// Ordering 默认排序
func (h HostResource) Ordering() *schema.OrderField {
	return h.Model.Ordering()
}

// GetInstanceID 获取实例ID
func (h HostResource) GetInstanceID() string {
	return h.InstanceID
}

// GetResourceIsp 获取厂商信息
func (h HostResource) GetResourceIsp() ResourceBase {
	return ResourceBase{
		IspID:    h.IspID,
		IspType:  h.IspType,
		RegionID: h.RegionID,
	}
}

// SetRegionID 设置地区ID
func (h *HostResource) SetRegionID(regionID string) {
	h.RegionID = regionID
}

// SetMemory 设置内存信息
func (h *HostResource) SetMemory(mem int32) {
	h.Memory = mem
}

// CheckCmdbEnvFromTagsAndInstancename -
func (h *HostResource) CheckCmdbEnvFromTagsAndInstancename() string {
	for _, v := range h.Tags.Tag {
		if v.TagKey == "env" && (v.TagValue == "prod" || v.TagValue == "test") {
			return v.TagValue
		}
	}
	result := "test"
	if utils.ResourceIsProd(h.InstanceName) {
		result = "prod"
	}
	return result
}
