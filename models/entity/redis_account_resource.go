package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetRedisAccountResCollection 获取Mysql资源collection
func GetRedisAccountResCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(RedisAccountResourceEntity.CollectionName())
	return collection
}

// RedisAccountResourceEntity entity
var RedisAccountResourceEntity = new(RedisAccountResource)

// RedisAccountResource Mysql数据库详情
type RedisAccountResource struct {
	Model              `bson:",inline"`
	IsLock             bool                 `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	IspID              string               `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
	IspType            string               `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`
	InstanceID         string               `bson:"InstanceID" json:"InstanceID,omitempty" xml:"InstanceID,omitempty" field:"实例ID" validate:"required"`
	AccountName        string               `bson:"AccountName" json:"AccountName,omitempty" xml:"AccountName,omitempty" field:"账号名称"`
	AccountStatus      string               `bson:"AccountStatus" json:"AccountStatus,omitempty" xml:"AccountStatus,omitempty" field:"账号状态"`
	AccountType        string               `bson:"AccountType" json:"AccountType,omitempty" xml:"AccountType,omitempty" field:"账号类型"`
	AccountDescription string               `bson:"AccountDescription" json:"AccountDescription,omitempty" xml:"AccountDescription,omitempty" field:"账号备注"`
	DatabasePrivileges []DatabasePrivileges `bson:"DatabasePrivileges" json:"DatabasePrivileges,omitempty" xml:"DatabasePrivileges,omitempty" type:"Struct" field:"账号权限列表"`
}

// DatabasePrivileges RedisAccount-权限列表
type DatabasePrivileges struct {
	AccountPrivilege string `bson:"AccountPrivilege" json:"AccountPrivilege,omitempty" xml:"AccountPrivilege,omitempty" field:"账号权限"`
}

// CollectionName 定义collection name
func (h RedisAccountResource) CollectionName() string {
	return h.Model.CollectionName("redis_account_resource")
}

// CreateIndexes 创建索引
func (h RedisAccountResource) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return h.Model.CreateIndexes(ctx, cli, h, []mongo.IndexModel{
		{Keys: bson.M{"isp_id": 1}},
		{Keys: bson.M{"isp_type": 1}},
		{Keys: bson.M{"is_lock": 1}},
		{Keys: bson.M{"InstanceID": 1}},
	})
}

// Meta 描述信息
func (h RedisAccountResource) Meta() []*FiledStruct {
	return GetFieldMeta(h)
}

// Ordering 默认排序
func (h RedisAccountResource) Ordering() *schema.OrderField {
	return h.Model.Ordering()
}
