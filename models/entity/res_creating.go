package entity

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// GetResCreatingCollection ResCreating collection
func GetResCreatingCollection(cli *mongo.Client) *mongo.Collection {
	collection := cli.Database(cfg.GetDBName()).Collection(ResCreatingEntity.CollectionName())
	return collection
}

// ResCreatingEntity entity
var ResCreatingEntity = new(ResCreating)

// ResCreating 云管agent任务记录
// 部分字段与meta中的重复，方便可读性
// 重复的包括：runType/methodName/agentID，以extra内数据为准
type ResCreating struct {
	Model        `bson:",inline"`
	OrderID      string `bson:"order_id" json:"order_id"`
	ResourceType string `bson:"resource_type" json:"resource_type"`
	InstanceName string `bson:"instance_name" json:"instance_name"`
	TreeNode     string `bson:"tree_node" json:"tree_node"`
	Tag          string `bson:"tag" json:"tag"`
}

// CollectionName 定义collection name
func (a ResCreating) CollectionName() string {
	return a.Model.CollectionName("res_creating")
}

// CreateIndexes 创建索引
func (a ResCreating) CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return a.Model.CreateIndexes(ctx, cli, a, []mongo.IndexModel{
		{Keys: bson.M{"instance_name": 1}},
		{Keys: bson.M{"tree_node": 1}},
	})
}

// Ordering 默认排序
func (a ResCreating) Ordering() *schema.OrderField {
	return a.Model.Ordering()
}
