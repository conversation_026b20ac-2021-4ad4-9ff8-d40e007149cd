package entity

// Tag 标签
type Tag struct {
	// TagValue 标签值
	TagValue string `bson:"TagValue" json:"TagValue,omitempty" xml:"TagValue,omitempty" field:"标签值"`
	// TagKey 标签键
	TagKey string `bson:"TagKey" json:"TagKey,omitempty" xml:"TagKey,omitempty" field:"标签键"`
}

// ResourceBase 厂商及相关通用属性
type ResourceBase struct {
	IspID    string
	IspType  string
	RegionID string
}

// ResourceEntity 资源entity
type ResourceEntity interface {
	GetInstanceID() string        // 获取实例ID
	GetResourceIsp() ResourceBase // 获取厂商及相关通用属性
}
