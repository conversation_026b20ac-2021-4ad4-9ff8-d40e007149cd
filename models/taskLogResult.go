package models

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
)

// TaskLogResultModel task-log-result-model
var TaskLogResultModel = new(TaskLogResult)

// TaskLogResult task-log-resule
type TaskLogResult struct {
}

// Create create
func (t TaskLogResult) Create(ctx context.Context, data *entity.TaskLogResult) error {
	d, err := mapstruct.Struct2Map(data)
	if err != nil {
		return err
	}

	BeforeInsert(d)
	c := entity.GetTaskLogResultCollection(GetEngine())
	return Insert(ctx, c, d)
}
