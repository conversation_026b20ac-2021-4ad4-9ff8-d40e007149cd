package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

var LBServerGroupModel = new(LBServerGroup)

type LBServerGroup struct {
}

func (r LBServerGroup) GetByServerGroupID(ctx context.Context, id string) (*entity.LBServerGroup, error) {
	c := entity.GetLBServerGroupCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("ServerGroupId", id))
	var zoneEntity entity.LBServerGroup
	_, err := FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// CreateOrUpdate 创建/更新
func (r LBServerGroup) CreateOrUpdate(ctx context.Context, data *entity.LBServerGroup) (string, error) {
	action := "create"
	c := entity.GetLBServerGroupCollection(GetEngine())
	filter := map[string]interface{}{
		"ServerGroupId": data.ServerGroupID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.LoadBalancer
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}
	return action, err
}

// BatchCreateOrUpdate 批量创建或更新
func (r LBServerGroup) BatchCreateOrUpdate(ctx context.Context, data []*entity.LBServerGroup) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.ServerGroupID, v.ServerGroupName)

		if v.ServerGroupID != "" && v.ServerGroupName != "" {
			action, err := r.CreateOrUpdate(ctx, v)
			if err != nil {
				errText := fmt.Sprintf("ServerGroupId: %s, ServerGroupName: %s, errors: %v", v.ServerGroupID, v.ServerGroupName, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

func LBServerGroupModelToPB(data *entity.LBServerGroup) *cloudman.DescribeLoadBalancerServerGroupRes {
	res := &cloudman.DescribeLoadBalancerServerGroupRes{
		ServerGroupID:     data.ServerGroupID,
		ServerGroupName:   data.ServerGroupName,
		Scheduler:         data.Scheduler,
		CreateTime:        data.CreateTime,
		Servers:           []*cloudman.LBServerGroupServer{},
		ServerGroupType:   data.ServerGroupType,
		ServerGroupStatus: data.ServerGroupStatus,
		Protocol:          data.Protocol,
	}

	for _, server := range data.Servers {
		res.Servers = append(res.Servers, &cloudman.LBServerGroupServer{
			ServerID: server.ServerID,
			ServerIP: server.ServerIP,
			Type:     server.ServerType,
			Weight:   server.Weight,
			Port:     server.Port,
		})
	}
	return res
}
