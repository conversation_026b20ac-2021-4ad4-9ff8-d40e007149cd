package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// RedisAccountResourceModel 主机资源model
var RedisAccountResourceModel = new(RedisAccountResource)

// RedisAccountResource 主机资源
type RedisAccountResource struct{}

// BatchCreateOrUpdate 批量创建或更新
func (h RedisAccountResource) BatchCreateOrUpdate(ctx context.Context, data []*entity.RedisAccountResource) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.InstanceID, v.AccountName)

		if v.InstanceID != "" && v.AccountName != "" {
			action, err := h.CreateOrUpdate(ctx, v)
			if err != nil {
				errText := fmt.Sprintf("instance_id: %s, account_name: %s, errors: %v", v.InstanceID, v.AccountName, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// CreateOrUpdate 创建/更新,clusterID和db_name才能确定唯一
func (h RedisAccountResource) CreateOrUpdate(ctx context.Context, data *entity.RedisAccountResource) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	action := "create"
	c := entity.GetRedisAccountResCollection(GetEngine())
	filter := map[string]interface{}{
		"InstanceID":  data.InstanceID,
		"AccountName": data.AccountName,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.RedisAccountResource
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	return action, err
}

// Query 查询
func (h RedisAccountResource) Query(ctx context.Context, params *schema.RedisAccountResourceQueryParams) ([]*entity.RedisAccountResource, uint64, error) {
	c := entity.GetMysqlClusterResCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.RedisAccountResourceColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]*entity.RedisAccountResource, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.MysqlClusterDBResourceEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	// oids, _ := getFilterWithPK([]string{"608aea025ac12fa358b0eb47"})
	// cur, err := c.Find(ctx,
	// 	bson.M{
	// 	"is_delete":0,
	// 	"_id": bson.M{"$in": oids}}, findOption)
	// if err != nil {
	// 	return nil, 0, err
	// }
	// err = cur.All(ctx, &list)

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter.Map(), &list, findOption)
	if err != nil {
		return list, 0, err
	}

	return list, pr.Total, err
}

// FindMany find many
func (h RedisAccountResource) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.RedisAccountResource, error) {
	c := entity.GetRedisAccountResCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)

	cursor, err := c.Find(ctx, filters)
	if err != nil {
		return nil, err
	}

	var resource []*entity.RedisAccountResource
	err = cursor.All(ctx, &resource)
	return resource, err
}
