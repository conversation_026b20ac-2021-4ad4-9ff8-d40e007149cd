package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// RecyclePolicyModel recycle-policy-model
var RecyclePolicyModel = new(RecyclePolicy)

// RecyclePolicy recycle
type RecyclePolicy struct {
}

// Query query
func (r RecyclePolicy) Query(ctx context.Context, params *schema.RecyclePolicyQueryParams) ([]entity.RecyclePolicy, uint64, error) {
	c := entity.GetRecyclePolicyCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))
	findOption := &options.FindOptions{}

	// if params != nil {
	{
		if columnFilter := ColumnFilter(params.RecyclePolicyColumnParam); len(columnFilter) != 0 {
			filter = append(filter, columnFilter...)
		}

		OrderFields := []*schema.OrderField{entity.RecyclePolicyEntity.Ordering()} // 加入默认排序
		if len(OrderFields) == 0 {
			OrderFields = append(OrderFields, params.BuildToOrderFields()...)
		}

		findOption.SetSort(ParseOrder(OrderFields))
	}
	// }

	var list = make([]entity.RecyclePolicy, 0)

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get get
func (r RecyclePolicy) Get(ctx context.Context, oid string) (*entity.RecyclePolicy, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetRecyclePolicyCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var recyclePolicyEntity entity.RecyclePolicy
	_, err = FindOne(ctx, c, filter, &recyclePolicyEntity)
	return &recyclePolicyEntity, err
}

// CreateOrUpdatePolicy 更新/创建销毁规则
// Cannot increment with non-numeric argument
func (r RecyclePolicy) CreateOrUpdatePolicy(ctx context.Context, policy *entity.RecyclePolicy) error {
	// TODO: 对传入数据进行验证
	policy.UpdatedTime = time.Now().Unix()
	c := entity.GetRecyclePolicyCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "name", Value: policy.Name}, bson.E{Key: "instance_type", Value: policy.InstanceType})
	data, err := mapstruct.Struct2Map(policy)
	if err != nil {
		return err
	}
	isUpsert := true
	_, err = c.UpdateOne(ctx, filter, bson.M{"$set": data}, &options.UpdateOptions{Upsert: &isUpsert})
	return err
}

// FindPolicy 获取销毁规则
func (r RecyclePolicy) FindPolicy(ctx context.Context, policy *entity.RecyclePolicy) ([]*entity.RecyclePolicy, error) {
	c := entity.GetRecyclePolicyCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "instance_type", Value: policy.InstanceType})
	if policy.Name != "" {
		filter = append(filter, bson.E{Key: "name", Value: policy.Name})
	}
	var p []*entity.RecyclePolicy
	_, err := FindMany(ctx, c, filter, &p, nil)
	if err != nil {
		return nil, err
	}

	return p, nil
}

// Delete 删除释放规则
func (r RecyclePolicy) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	c := entity.GetRecyclePolicyCollection(GetEngine())

	filter := DefaultFilter(ctx)
	filter = append(filter, Filter("_id", objectID))

	err = Delete(ctx, c, filter)
	return err
}

// UpdateOne 更新策略
func (r RecyclePolicy) UpdateOne(ctx context.Context, id string, policy *entity.RecyclePolicy) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}
	c := entity.GetRecyclePolicyCollection(GetEngine())
	data, err := mapstruct.Struct2Map(policy)
	if err != nil {
		return err
	}
	filter := DefaultFilter(ctx, bson.E{Key: "_id", Value: objectID})
	err = UpdateOne(ctx, c, filter, data)
	return err
}
