package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// ResChangelistModel account model
var ResChangelistModel = new(ResChangelist)

// ResChangelist account
type ResChangelist struct{}

// Query 查询列表
func (a ResChangelist) Query(ctx context.Context, params *schema.ResChangelistQueryParams) ([]entity.ResChangelist, uint64, error) {
	c := entity.GetResChangelistCollection(GetEngine())
	filter := DefaultFilter(ctx)

	if columnFilter := ColumnFilter(params.ResChangelistColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]entity.ResChangelist, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{new(entity.ResChangelist).Ordering()}
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}
	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, err
}

// FindPK 根据主键查询
func (a ResChangelist) FindPK(ctx context.Context, id string) (*entity.ResChangelist, error) {
	obdID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	c := entity.GetResChangelistCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "_id", Value: obdID})

	var ac *entity.ResChangelist
	_, err = FindOne(ctx, c, filter, &ac)
	if err != nil {
		return nil, err
	}

	return ac, nil
}

// Create 创建
func (a ResChangelist) Create(ctx context.Context, at *entity.ResChangelist) (string, error) {
	if at.ID.IsZero() {
		at.ID = primitive.NewObjectID()
	}
	d, err := mapstruct.Struct2Map(at)
	if err != nil {
		return "", err
	}

	BeforeInsert(d)
	c := entity.GetResChangelistCollection(GetEngine())
	result, err := c.InsertOne(ctx, d)
	if err != nil {
		return "", err
	}
	if s, ok := result.InsertedID.(primitive.ObjectID); ok {
		return s.Hex(), nil
	}
	return "", nil
}

// Delete delete
func (a ResChangelist) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetResChangelistCollection(GetEngine())

	return Delete(ctx, c, filter)
}

// CreateOrUpdate 创建或修改
func (a ResChangelist) CreateOrUpdate(ctx context.Context, data *entity.ResChangelist) error {
	c := entity.GetResChangelistCollection(GetEngine())
	filter := map[string]interface{}{
		"_id": data.ID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.ResChangelist
		err = result.Decode(&m)
		if err != nil {
			return err
		}
		data.CreatedTime = m.CreatedTime
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		// 主键此处不重置
		// data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}
	return err
}

// Update 修改
func (a ResChangelist) Update(ctx context.Context, taskID string, data map[string]interface{}) error {
	idObj, err := primitive.ObjectIDFromHex(taskID)
	if err != nil {
		return err
	}
	filter := map[string]interface{}{
		"_id": idObj,
	}
	c := entity.GetResChangelistCollection(GetEngine())
	err = UpdateOne(ctx, c, filter, data)
	return err
}
