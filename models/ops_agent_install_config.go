package models

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
)

// OpsAgentInstallConfigModel ops-agent-model
var OpsAgentInstallConfigModel = new(OpsAgentInstallConfig)

// OpsAgentInstallConfig ops-agent
type OpsAgentInstallConfig struct {
}

// CreateOrUpdate 创建或更新
func (o OpsAgentInstallConfig) CreateOrUpdate(ctx context.Context, data *entity.OpsAgentInstallConfig) error {
	c := entity.GetOpsAgentInstallConfigCollection(GetEngine())
	dataMap, err := mapstruct.Struct2Map(data)
	if err != nil {
		return err
	}

	isUpsert := true
	_, err = c.UpdateOne(ctx, bson.M{
		"isp_id":    data.IspID,
		"region_id": data.RegionID,
		"cmdb_env":  data.CmdbEnv,
	}, bson.M{"$set": dataMap}, &options.UpdateOptions{Upsert: &isUpsert})
	return err
}

// FindOne find-one
func (o OpsAgentInstallConfig) FindOne(ctx context.Context, filter map[string]interface{}) (*entity.OpsAgentInstallConfig, error) {
	c := entity.GetOpsAgentInstallConfigCollection(GetEngine())

	var data entity.OpsAgentInstallConfig

	runningFilter := DefaultFilter(ctx, mapToFilter(filter)...)
	_, err := FindOne(ctx, c, runningFilter, &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

// FindMany find-many
func (o OpsAgentInstallConfig) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.OpsAgentInstallConfig, error) {
	c := entity.GetOpsAgentInstallConfigCollection(GetEngine())

	var data []*entity.OpsAgentInstallConfig

	runningFilter := DefaultFilter(ctx, mapToFilter(filter)...)
	_, err := FindMany(ctx, c, runningFilter, &data, nil)
	if err != nil {
		return nil, err
	}

	return data, nil
}
