package models

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// ResourceBindTags resource-bind-tag-model
var ResourceBindTags = new(resourceBindTags)

// resourceTags resource-tags
type resourceBindTags struct {
}

// 通过标签查资源
// 通过资源查标签
// 通过标签绑定资源
// 通过资源绑定标签
// 资源解绑标签
// 标签解绑资源
// 获取/创建标签并返回ID

// FindByResourceType 根据资源类型查询
func (r resourceBindTags) FindByResourceType(ctx context.Context) {

}

// FindByResAndTagID ...
func (r resourceBindTags) FindByResAndTagID(ctx context.Context, resID primitive.ObjectID, resType string, tagID primitive.ObjectID) (*entity.ResBindTags, error) {
	c := entity.GetResBindTagsCollection(GetEngine())
	f := map[string]interface{}{
		"bind_res_id":   resID,
		"bind_res_type": resType,
		"tags_id":       tagID,
	}
	filter := DefaultFilter(ctx, mapToFilter(f)...)
	resBindTags := &entity.ResBindTags{}
	_, err := FindOne(ctx, c, filter, resBindTags)
	if err != nil {
		return nil, err
	}
	return resBindTags, nil
}

// FindResourceIDs 根据key与value查询对应的资源id列表
func (r resourceBindTags) FindResourceIDs(ctx context.Context, resType, tagKey string, tagValues []string) ([]primitive.ObjectID, error) {
	resTagColl := entity.GetResTagsCollection(GetEngine())
	f := map[string]interface{}{
		"key": tagKey,
	}

	if len(tagValues) != 0 {
		f["value"] = map[string]interface{}{"$in": tagValues}
	}

	filter := DefaultFilter(ctx, mapToFilter(f)...)

	var resTags []*entity.ResTags
	cur, err := resTagColl.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &resTags)
	if err != nil {
		return nil, err
	}

	var tagIDS []primitive.ObjectID
	for _, v := range resTags {
		tagIDS = append(tagIDS, v.ID)
	}

	regBingTagcoll := entity.GetResBindTagsCollection(GetEngine())
	bindTagFilter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{
		"bind_res_type": resType,
		"tags_id": map[string]interface{}{
			"$in": tagIDS,
		},
	})...)

	var resBindTag []entity.ResBindTags
	cur, err = regBingTagcoll.Find(ctx, bindTagFilter)
	if err != nil {
		return nil, err
	}
	err = cur.All(ctx, &resBindTag)
	if err != nil {
		return nil, err
	}

	var resIDS []primitive.ObjectID
	for _, v := range resBindTag {
		resIDS = append(resIDS, v.BindResID)
	}
	return resIDS, err
}

// Bind 资源绑定tag通用方法
func (r resourceBindTags) Bind(ctx context.Context, tags []*cloudman.ResourceTag, resourceType string, instanceID primitive.ObjectID, source string) error {
	/* 判断tags是否存在

	 */

	var err error

	c := entity.GetResTagsCollection(GetEngine())
	bindResTagCollection := entity.GetResBindTagsCollection(GetEngine())
	for _, tag := range tags {
		data := &entity.ResTags{
			Key:    tag.Key,
			Value:  tag.Value,
			Source: source,
			Type:   tag.Type,
		}
		data.CreatedTime = time.Now().Unix()

		filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{
			"key":   tag.Key,
			"value": tag.Value,
		})...)

		_, err := FindOne(ctx, c, filter, data)
		if err == mongo.ErrNoDocuments {
			// 先创建tag再绑定
			data.ID = primitive.NewObjectID()
			err = Insert(ctx, c, data)
		}

		if err == nil {
			// 存在则绑定
			rData := entity.ResBindTags{
				TagsID:      data.ID,
				BindResType: resourceType,
				BindResID:   instanceID,
				Source:      source,
			}
			rData.ID = primitive.NewObjectID()
			rData.CreatedTime = time.Now().Unix()

			err = Insert(ctx, bindResTagCollection, rData)
		}

		if err != nil && !errors.Is(err, mongo.WriteException{}) {
			if _, ok := err.(mongo.WriteException); !ok {
				logger.Errorf("models-Bind_tags error", err.Error())
			}
		}
	}
	return err
}

func (r resourceBindTags) BindByID(ctx context.Context, resID primitive.ObjectID, resType string, tagID primitive.ObjectID, source string) error {
	c := entity.GetResBindTagsCollection(GetEngine())
	rData := entity.ResBindTags{
		TagsID:      tagID,
		BindResType: resType,
		BindResID:   resID,
		Source:      source,
	}
	rData.ID = primitive.NewObjectID()
	rData.CreatedTime = time.Now().Unix()
	return Insert(ctx, c, rData)
}

func (r resourceBindTags) DelAll(ctx context.Context, resourceType string, instanceID primitive.ObjectID, source string) error {
	c := entity.GetResBindTagsCollection(GetEngine())
	filter := DefaultFilter(ctx)
	filter = append(filter, bson.E{Key: "bind_res_id", Value: instanceID}, bson.E{Key: "bind_res_type", Value: resourceType})
	_, err := c.UpdateMany(ctx, filter, bson.D{{"$set", bson.D{{"is_delete", 1}}}})
	if err != nil {
		return err
	}
	return nil
}
