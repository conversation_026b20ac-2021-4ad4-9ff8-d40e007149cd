package models

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// SecurityGroupModel zone-model
var SecurityGroupModel = new(SecurityGroup)

// SecurityGroup zone
type SecurityGroup struct {
}

// Query query
func (r SecurityGroup) Query(ctx context.Context, params *schema.SecurityGroupQueryParams) ([]*entity.SecurityGroup, uint64, error) {
	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.SecurityGroupColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.SecurityGroup, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.SecurityGroup{}.Ordering()} // 加入默认排序
	OrderFields = append(OrderFields, params.BuildToOrderFields()...)

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get xx
func (r SecurityGroup) Get(ctx context.Context, oid string) (*entity.SecurityGroup, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var sgEntity entity.SecurityGroup
	_, err = FindOne(ctx, c, filter, &sgEntity)
	return &sgEntity, err
}

// GetBySecurityGroupID xx
func (r SecurityGroup) GetBySecurityGroupID(ctx context.Context, sgID string) (*entity.SecurityGroup, error) {
	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("SecurityGroupId", sgID))
	var sgEntity entity.SecurityGroup
	_, err := FindOne(ctx, c, filter, &sgEntity)
	return &sgEntity, err
}

// GetCustomTags xxx
func (r SecurityGroup) GetCustomTags(ctx context.Context, ispID string, regionID string) ([]string, error) {
	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "isp_id", Value: ispID}, bson.E{Key: "RegionID", Value: regionID})
	cursor, err := c.Aggregate(ctx, []bson.M{
		{
			"$match": filter,
		},
		{
			"$group": bson.M{
				"_id": "$custom_tag",
			},
		},
		{
			"$project": bson.M{
				"_id":        0,
				"custom_tag": "$_id",
			},
		},
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 获取不重复的 customTag 值并打印
	var result []bson.M
	if err = cursor.All(ctx, &result); err != nil {
		return nil, err
	}

	var customTags []string
	for _, doc := range result {
		if doc["custom_tag"] != nil && doc["custom_tag"].(string) != "" {
			customTags = append(customTags, doc["custom_tag"].(string))
		}
	}

	return customTags, nil
}

// GetCustomTagSecurityGroups xxx
func (r SecurityGroup) GetCustomTagSecurityGroups(ctx context.Context, ispID string, regionID string, customTag string) ([]*entity.SecurityGroup, error) {
	c := entity.GetSecurityGroupCollection(GetEngine())

	filterMap := map[string]interface{}{
		"isp_id":     ispID,
		"RegionID":   regionID,
		"custom_tag": customTag,
	}
	if customTag == "默认节点" {
		delete(filterMap, "custom_tag")
		filterMap["$or"] = []map[string]interface{}{
			{"custom_tag": ""},
			{"custom_tag": map[string]interface{}{"$exists": false}},
		}
	}
	filter := DefaultFilter(ctx, mapToFilter(filterMap)...)
	var sgs []*entity.SecurityGroup
	_, err := FindMany(ctx, c, filter, &sgs)
	if err != nil {
		return nil, err
	}
	return sgs, nil
}

// FindManyWithPK find-many
func (r SecurityGroup) FindManyWithPK(ctx context.Context, ids []string) ([]entity.SecurityGroup, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.SecurityGroup
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindMany find-many
func (r SecurityGroup) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.SecurityGroup, error) {
	c := entity.GetSecurityGroupCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
		if option[0].WithoutIsp {
			filters = DefaultFilter(ctx, mapToFilter(filter)...)
		}
	}

	var resource []*entity.SecurityGroup

	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// FindManyWithIDToMap to map
func (r SecurityGroup) FindManyWithIDToMap(ctx context.Context, securityGroupIDs []string) (map[string]entity.SecurityGroup, error) {
	if len(securityGroupIDs) == 0 {
		return nil, nil
	}

	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		"SecurityGroupId", map[string]interface{}{"$in": securityGroupIDs},
	})

	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	m := map[string]entity.SecurityGroup{}
	for cur.Next(ctx) {
		var sg entity.SecurityGroup
		if err = cur.Decode(&sg); err != nil {
			return nil, err
		}
		m[sg.SecurityGroupID] = sg
	}

	return m, nil
}

// Update 更新
func (r SecurityGroup) Update(ctx context.Context, id string, updateData interface{}) error {
	data, err := mapstruct.Struct2Map(updateData)
	if err != nil {
		return err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetSecurityGroupCollection(GetEngine())
	return UpdateMany(ctx, c, filter, data)
}

// UpdateCustomTagBySgID 更新
func (r SecurityGroup) UpdateCustomTagBySgID(ctx context.Context, sgIDs []string, customTag string) error {
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"SecurityGroupId": map[string]interface{}{
		"$in": sgIDs,
	}})...)
	c := entity.GetSecurityGroupCollection(GetEngine())
	return UpdateMany(ctx, c, filter, map[string]interface{}{
		"custom_tag":   customTag,
		"updated_time": time.Now().Unix(),
	})
}

// UpdateLevelTag 更新
func (r SecurityGroup) UpdateLevelTag(ctx context.Context, sgID string, levelTag entity.RuleTag) error {
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"SecurityGroupId": sgID})...)
	c := entity.GetSecurityGroupCollection(GetEngine())
	return UpdateOne(ctx, c, filter, map[string]interface{}{
		"level_tag": levelTag,
	})
}

// CreateOrUpdate 创建/更新
func (r SecurityGroup) CreateOrUpdate(ctx context.Context, data *entity.SecurityGroup) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	action := "create"
	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := map[string]interface{}{
		"SecurityGroupId": data.SecurityGroupID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.SecurityGroup
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		for i := 0; i < len(data.Permissions); i++ {
			for _, perm := range m.Permissions {
				if data.Permissions[i].SecurityGroupRuleID == perm.SecurityGroupRuleID {
					data.Permissions[i].Tags = perm.Tags
					break
				}
			}
		}
		data.CustomTag = m.CustomTag
		data.LevelTag = m.LevelTag
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
		if err != nil {
			return action, err
		}
	}

	if data.SecurityGroupType == "enterprise" {
		_, err = IPModel.BatchCreateOrUpdate(ctx, SecurityGroupModelToIPs(data), true)
		if err != nil {
			return "", err
		}
	}

	return action, err
}

func SecurityGroupModelToIPs(data *entity.SecurityGroup) []*entity.IP {
	ips := make([]*entity.IP, 0)
	for _, rule := range data.Permissions {
		if rule.Direction != "ingress" {
			continue
		}
		address := rule.SourceCidrIP
		addressType := "cidr"
		// 非法的ip直接排除掉
		if address == "0.0.0.0/0" {
			continue
		} else if strings.Contains(address, "/32") {
			addressType = "ip"
			address = strings.TrimSuffix(address, "/32")
		}
		ips = append(ips, &entity.IP{
			Type:             addressType,
			CreateType:       "sync",
			Address:          address,
			BindInstanceType: "security_group",
			InstanceID:       data.SecurityGroupID,
			Desc:             rule.Description,
			RegionID:         data.RegionID,
			IspID:            data.IspID,
			IspType:          data.IspType,
		})
	}
	return ips
}

// BatchCreateOrUpdate 批量创建或更新
func (r SecurityGroup) BatchCreateOrUpdate(ctx context.Context, data []*entity.SecurityGroup) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.SecurityGroupID, v.SecurityGroupName)

		if v.SecurityGroupID != "" && v.SecurityGroupName != "" {
			action, err := r.CreateOrUpdate(ctx, v)
			if err != nil {
				errText := fmt.Sprintf("security_group_id: %s, security_group_name: %s, errors: %v", v.SecurityGroupID, v.SecurityGroupName, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// SecurityGroupModelToPb ...
func SecurityGroupModelToPb(ctx context.Context, sgEntities []*entity.SecurityGroup) ([]*cloudman.SecurityGroupEntity, error) {
	var accountIds []string
	for _, a := range sgEntities {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(ctx, accountIds)
		if err != nil {
			return nil, err
		}
	}

	var sgPbs []*cloudman.SecurityGroupEntity
	for _, sg := range sgEntities {
		if sg.CustomTag == "" {
			sg.CustomTag = "默认节点"
		}
		sgPB := &cloudman.SecurityGroupEntity{
			CreationTime:            sg.CreationTime,
			VpcId:                   sg.VpcID,
			ServiceManaged:          sg.ServiceManaged,
			Description:             sg.Description,
			SecurityGroupId:         sg.SecurityGroupID,
			ResourceGroupId:         sg.ResourceGroupID,
			SecurityGroupName:       sg.SecurityGroupName,
			EcsCount:                int32(sg.EcsCount),
			ServiceID:               sg.ServiceID,
			SecurityGroupType:       sg.SecurityGroupType,
			AvailableInstanceAmount: int32(sg.AvailableInstanceAmount),
			RegionID:                sg.RegionID,
			IspId:                   sg.IspID,
			IspType:                 sg.IspType,
			IspName:                 ac[sg.IspID].Name,
			CustomTag:               sg.CustomTag,
			LevelTag:                &cloudman.RuleTag{Level: sg.LevelTag.Level, Value: sg.LevelTag.Value},
			NeedCleanup:             sg.NeedCleanup,
		}
		for _, t := range sg.Tags {
			sgPB.Tags = append(sgPB.Tags, &cloudman.ResourceTag{
				Key:   t.TagKey,
				Value: t.TagValue,
			})
		}

		for _, p := range sg.Permissions {
			priority, _ := strconv.Atoi(p.Priority)
			var tags []*cloudman.RuleTag
			for _, tag := range p.Tags {
				tags = append(tags, &cloudman.RuleTag{
					Level: tag.Level,
					Value: tag.Value,
				})
			}
			sgPB.Permissions = append(sgPB.Permissions, &cloudman.SecurityGroupPermission{
				CreateTime:              p.CreateTime,
				Description:             p.Description,
				DestCidrIp:              p.DestCidrIP,
				DestGroupId:             p.DestGroupID,
				DestGroupName:           p.DestGroupName,
				DestGroupOwnerAccount:   p.DestGroupOwnerAccount,
				DestPrefixListId:        p.DestPrefixListID,
				DestPrefixListName:      p.DestPrefixListName,
				Direction:               p.Direction,
				IpProtocol:              p.IPProtocol,
				Ipv6DestCidrIp:          p.Ipv6DestCidrIP,
				Ipv6SourceCidrIp:        p.Ipv6SourceCidrIP,
				NicType:                 p.NicType,
				Policy:                  p.Policy,
				PortRange:               p.PortRange,
				Priority:                int32(priority),
				SourceCidrIp:            p.SourceCidrIP,
				SourceGroupId:           p.SourceGroupID,
				SourceGroupName:         p.SourceGroupName,
				SourceGroupOwnerAccount: p.SourceGroupOwnerAccount,
				SourcePortRange:         p.SourcePortRange,
				SourcePrefixListId:      p.SourcePrefixListID,
				SourcePrefixListName:    p.SourcePrefixListName,
				SecurityGroupRuleId:     p.SecurityGroupRuleID,
				Tags:                    tags,
			})
		}

		sgPbs = append(sgPbs, sgPB)
	}
	return sgPbs, nil
}

// MarkCleanup 标记已释放待清理
func (r SecurityGroup) MarkCleanup(ctx context.Context, ispID, regionID string, latestUpdateVersion string) (int64, error) {
	e := entity.GetSecurityGroupCollection(GetEngine())
	_, err := e.UpdateMany(ctx,
		bson.D{
			{"isp_id", ispID},
			{"RegionID", regionID},
			{"is_delete", 0},
		},
		bson.D{{"$set", bson.M{"NeedCleanup": false}}})
	if err != nil {
		return 0, err
	}
	res, err := e.UpdateMany(ctx,
		bson.D{
			{"isp_id", ispID},
			{"RegionID", regionID},
			{"UpdateVersion", bson.D{{"$ne", latestUpdateVersion}}},
			{"is_delete", 0},
		},
		bson.D{{"$set", bson.M{"NeedCleanup": true}}})
	if err != nil {
		return 0, err
	}
	return res.MatchedCount, nil
}

// Delete ...
func (r SecurityGroup) Delete(ctx context.Context, sgIDs []string) error {
	e := entity.GetSecurityGroupCollection(GetEngine())
	err := DeleteMany(ctx, e, map[string]interface{}{
		"SecurityGroupId": map[string]interface{}{
			"$in": sgIDs,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (r SecurityGroup) GetBySecurityName(ctx context.Context, regionId, sgName string) ([]*entity.SecurityGroup, error) {
	c := entity.GetSecurityGroupCollection(GetEngine())
	filter := DefaultFilter(ctx)
	filter = append(filter, Filter("SecurityGroupName", sgName), Filter("RegionID", regionId))
	var sgEntities []*entity.SecurityGroup
	_, err := FindMany(ctx, c, filter, &sgEntities)
	return sgEntities, err
}
