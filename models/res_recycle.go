package models

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// RecycleModel recycle-model
var RecycleModel = new(Recycle)

// Recycle recycle
type Recycle struct {
}

// Query query
func (r Recycle) Query(ctx context.Context, params *schema.RecycleQueryParams) ([]*entity.Recycle, uint64, error) {
	c := entity.GetRecycleCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.RecycleColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]*entity.Recycle, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.RecyclePolicyEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get get
func (r Recycle) Get(ctx context.Context, oid string) (*entity.Recycle, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetRecycleCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var recycleEntity entity.Recycle
	_, err = FindOne(ctx, c, filter, &recycleEntity)
	return &recycleEntity, err
}

// Create 创建回收站
func (r Recycle) Create(ctx context.Context, data *entity.Recycle) error {
	data.ID = primitive.NewObjectID()
	data.CreatedTime = time.Now().Unix()
	c := entity.GetRecycleCollection(GetEngine())

	return Insert(ctx, c, data)
}

// BatchCreate 资源批量放入回收站
func (r Recycle) BatchCreate(ctx context.Context, data []*entity.Recycle) error {
	addTime := time.Now().Unix()
	for _, v := range data {
		v.CreatedTime = addTime
	}
	c := entity.GetRecycleCollection(GetEngine())
	s, _ := json.Marshal(data)
	var tmp []interface{}
	_ = json.Unmarshal(s, &tmp)
	return InsertMany(ctx, c, tmp)
}

// FindMany 查询多条记录
func (r Recycle) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.Recycle, error) {
	c := entity.GetRecycleCollection(GetEngine())
	f := DefaultFilter(ctx, mapToFilter(filter)...)
	var list []*entity.Recycle
	_, err := FindMany(ctx, c, f, &list, nil)
	return list, err
}

// UpdateMany 更新记录
func (r Recycle) UpdateMany(ctx context.Context, filter map[string]interface{}, doc map[string]interface{}) error {
	c := entity.GetRecycleCollection(GetEngine())
	f := DefaultFilter(ctx, mapToFilter(filter)...)

	return UpdateMany(ctx, c, f, doc)
}

// Delete 删除
func (r Recycle) Delete(ctx context.Context, pk string) error {
	oid, err := primitive.ObjectIDFromHex(pk)
	if err != nil {
		return err
	}
	c := entity.GetRecycleCollection(GetEngine())
	f := DefaultFilter(ctx, bson.E{Key: "_id", Value: oid})

	return Delete(ctx, c, f)
}

// AllTaskWaitRun 暂停实现
func (r Recycle) AllTaskWaitRun(ctx context.Context, oid []string) error {
	// in 查询
	var pks []primitive.ObjectID
	for _, pk := range oid {
		id, err := primitive.ObjectIDFromHex(pk)
		if err != nil {
			return err
		}
		pks = append(pks, id)
	}
	// c := entity.GetRecycleCollection(GetEngine())
	// f := DefaultFilter(ctx,
	// 	bson.E{Key: "_id", Value: map[string]interface{}{"$in": pks}},
	// 	bson.E{Key: "status", Value: map[string]interface{}{"$ne": constant.TaskWaitRun}},
	// )
	// var res []entity.Recycle
	// res, err := c.Find(ctx, f)
	// if err != nil {
	// 	return err
	// }
	//
	// if int(count) == len(oid) {
	// 	return nil
	// }

	return fmt.Errorf("包含不允许操作的资源")
}
