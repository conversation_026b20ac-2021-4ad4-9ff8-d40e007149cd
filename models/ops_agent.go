package models

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// OpsAgentModel ops-agent-model
var OpsAgentModel = new(OpsAgent)

// OpsAgent ops-agent
type OpsAgent struct {
}

// Create 新增ops-agent
func (o OpsAgent) Create(ctx context.Context, data *entity.OpsAgent) error {
	c := entity.GetOpsAgentCollection(GetEngine())
	data.ID = primitive.NewObjectID() // 基于时间，可能会出现重复
	data.CreatedTime = time.Now().Unix()
	data.UpdatedTime = time.Now().Unix()
	return Insert(ctx, c, data)
}

// Update 更新ops-agent信息
func (o OpsAgent) Update(ctx context.Context, agentID string, data *entity.OpsAgent) error {
	c := entity.GetOpsAgentCollection(GetEngine())
	filter := mapToFilter(map[string]interface{}{"agent_id": agentID})

	doc, err := mapstruct.Struct2Map(data)
	if err != nil {
		return err
	}

	return UpdateOne(ctx, c, filter, doc)
}

// UpdateStatus 更新ops-agent状态
func (o OpsAgent) UpdateStatus(ctx context.Context, agentID string, status int32) error {
	c := entity.GetOpsAgentCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "agent_id", Value: agentID})

	err := UpdateOne(ctx, c, filter, map[string]interface{}{
		"status": status,
	})
	if err != nil {
		logger.Errorf("failed to update agent status: %s, %d, %v", agentID, status, err)
	}

	err = HostResourceModel.UpdateAgentStatus(ctx, agentID, status)
	if err != nil {
		logger.Errorf("failed to update host's agent status: %s, %d, %v", agentID, status, err)
	}
	return nil
}

// AgentReportHostInfo agent上报的主机信息
type AgentReportHostInfo struct {
	GPUList          []string
	CPUModel         string
	CPUCore          int32
	MemTotal         int64
	OSName           string
	NetWorkInterface []AgentReportNetworkInterface
}

// AgentReportNetworkInterface ...
type AgentReportNetworkInterface struct {
	Name      string
	Bandwidth string
}

// CreateOrUpdate 创建或更新
func (o OpsAgent) CreateOrUpdate(ctx context.Context, data *entity.OpsAgent, info *AgentReportHostInfo) error {
	c := entity.GetOpsAgentCollection(GetEngine())
	filter := mapToFilter(map[string]interface{}{"agent_id": data.AgentID})
	// 判断agent表是否已存在条目，不存在则新增
	count, err := c.CountDocuments(ctx, filter)
	if err != nil {
		return err
	}
	if count == 0 {
		if err = o.Create(ctx, data); err != nil {
			return err
		}
	}
	data.UpdatedTime = time.Now().Unix()
	memTotal := info.MemTotal / 1048576

	var hostNetworkInstances []entity.InstanceNetworkInterface
	for _, v := range info.NetWorkInterface {
		hostNetworkInstances = append(hostNetworkInstances, entity.InstanceNetworkInterface{
			NetworkInterfaceID: v.Name,
			Bandwidth:          v.Bandwidth,
		})
	}

	// 更新host_resource表中对应的条目
	err = HostResourceModel.UpdateAgent(ctx, map[string]interface{}{
		"agent_id": data.AgentID, // 使用AgentID匹配更新
	}, &entity.HostAgent{
		AgentID:      data.AgentID,
		AgentType:    data.NodeType,
		AgentStatus:  data.Status,
		AgentVersion: data.Version,
		AgentEnv:     data.Env,
		AgentUpdate:  data.UpdatedTime,
	}, &entity.HostResource{
		GPUSpec:      strings.Join(info.GPUList, ";"),
		CPU:          info.CPUCore,
		Memory:       int32(math.Floor(float64(memTotal) + 0.5)),
		InstanceType: info.CPUModel,
		OSName:       info.OSName,
		HostName:     data.Hostname,
		NetworkInterfaces: entity.InstanceNetworkInterfaces{
			NetworkInterface: hostNetworkInstances,
		},
	})

	if err != nil {
		logger.Warnf("ops-agent触发-更新主机资源失败: error:%s, host:%s, agent_id:%s", err.Error(), data.Hostname, data.AgentID)
	}
	// 更新agent表本身的内容
	return o.Update(ctx, data.AgentID, data)
}

// Query query
func (o OpsAgent) Query(ctx context.Context, params *schema.OpsAgentQueryParams) ([]entity.OpsAgent, uint64, error) {
	c := entity.GetOpsAgentCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetCustomSearch()))

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	if columnFilter := ColumnFilter(params.OpsAgentColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.OpsAgent, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.OpsAgent{}.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// CountCondition 统计总数
func (o OpsAgent) CountCondition(ctx context.Context, params *schema.OpsAgentQueryParams) (count int64, err error) {
	c := entity.GetOpsAgentCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.OpsAgentColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	return c.CountDocuments(ctx, filter)
}

// FindOne find-one
func (o OpsAgent) FindOne(ctx context.Context, filter map[string]interface{}) (*entity.OpsAgent, error) {
	c := entity.GetOpsAgentCollection(GetEngine())

	var data entity.OpsAgent

	runningFilter := DefaultFilter(ctx, mapToFilter(filter)...)
	_, err := FindOne(ctx, c, runningFilter, &data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

// FindMany find-many
func (o OpsAgent) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.OpsAgent, error) {
	c := entity.GetOpsAgentCollection(GetEngine())

	var data []*entity.OpsAgent

	runningFilter := DefaultFilter(ctx, mapToFilter(filter)...)
	_, err := FindMany(ctx, c, runningFilter, &data, nil)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// DeleteByAgentID delete
func (o OpsAgent) DeleteByAgentID(ctx context.Context, agentID string) error {
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"agent_id": agentID})...)
	c := entity.GetOpsAgentCollection(GetEngine())

	return Delete(ctx, c, filter)
}

// DistinctVersion -
func (o OpsAgent) DistinctVersion(ctx context.Context) ([]string, error) {
	c := entity.GetOpsAgentCollection(GetEngine())
	dbResult, err := c.Distinct(ctx, "version", bson.M{
		"is_delete": 0,
		"status":    1,
	})
	if err != nil {
		return nil, err
	}
	result := []string{}
	for _, item := range dbResult {
		if fmt.Sprint(item) == "" {
			continue
		}
		result = append(result, fmt.Sprint(item))
	}
	return result, nil
}
