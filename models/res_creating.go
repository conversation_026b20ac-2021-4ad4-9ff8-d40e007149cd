package models

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
)

// ResCreatingModel account model
var ResCreatingModel = new(ResCreating)

// ResCreating account
type ResCreating struct{}

// FindPK 根据主键查询
func (a ResCreating) FindPK(ctx context.Context, id string) (*entity.ResCreating, error) {
	obdID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	c := entity.GetResCreatingCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "_id", Value: obdID})

	var ac *entity.ResCreating
	_, err = FindOne(ctx, c, filter, &ac)
	if err != nil {
		return nil, err
	}

	return ac, nil
}

// Create 创建
func (a ResCreating) Create(ctx context.Context, at *entity.ResCreating) (string, error) {
	if at.ID.IsZero() {
		at.ID = primitive.NewObjectID()
	}
	d, err := mapstruct.Struct2Map(at)
	if err != nil {
		return "", err
	}

	BeforeInsert(d)
	c := entity.GetResCreatingCollection(GetEngine())
	result, err := c.InsertOne(ctx, d)
	if err != nil {
		return "", err
	}
	if s, ok := result.InsertedID.(primitive.ObjectID); ok {
		return s.Hex(), nil
	}
	return "", nil
}

// DeleteByName delete
func (a ResCreating) DeleteByName(ctx context.Context, resourceType, instanceName string) error {
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"resource_type": resourceType, "instance_name": instanceName})...)
	c := entity.GetResCreatingCollection(GetEngine())

	return Delete(ctx, c, filter)
}

// DeleteByOrder delete
func (a ResCreating) DeleteByOrder(ctx context.Context, orderID string) error {
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"order_id": orderID})...)
	c := entity.GetResCreatingCollection(GetEngine())

	return DeleteMany(ctx, c, filter)
}

// CreateOrUpdate 创建或修改
func (a ResCreating) CreateOrUpdate(ctx context.Context, data *entity.ResCreating) error {
	c := entity.GetResCreatingCollection(GetEngine())
	filter := map[string]interface{}{
		"order_id":      data.OrderID,
		"resource_type": data.ResourceType,
		"instance_name": data.InstanceName,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.ResCreating
		err = result.Decode(&m)
		if err != nil {
			return err
		}
		data.CreatedTime = m.CreatedTime
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if errors.Is(err, mongo.ErrNoDocuments) {
		// 主键此处不重置
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}
	return err
}

// FindMany -
func (a ResCreating) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.ResCreating, error) {
	c := entity.GetResCreatingCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
	}
	var resource []*entity.ResCreating
	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// FindOne -
func (a ResCreating) FindOne(ctx context.Context, filter map[string]interface{}) (*entity.ResCreating, error) {
	c := entity.GetResCreatingCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	cursor := c.FindOne(ctx, filters)
	err := cursor.Err()
	if err != nil {
		return nil, err
	}

	var resource *entity.ResCreating
	err = cursor.Decode(&resource)
	return resource, err
}
