package models

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// IPWhitelistModel zone-model
var IPWhitelistModel = new(IPWhitelist)

// IPWhitelist zone
type IPWhitelist struct {
}

func (r IPWhitelist) Query(ctx context.Context, params *schema.IPWhitelistQueryParam) ([]*entity.IPWhitelist, uint64, error) {
	c := entity.GetIPWhitelistCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.IPWhitelistColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.IPWhitelist, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.IPWhitelist{}.Ordering()} // 加入默认排序
	OrderFields = append(OrderFields, params.BuildToOrderFields()...)

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// CreateOrUpdate 创建/更新
func (r IPWhitelist) CreateOrUpdate(ctx context.Context, data *entity.IPWhitelist) (string, error) {
	action := "create"
	c := entity.GetIPWhitelistCollection(GetEngine())
	filter := map[string]interface{}{
		"GlobalIgName": data.GlobalIgName,
		"RegionId":     data.RegionID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.IPWhitelist
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	if err != nil {
		return "", err
	}

	_, err = IPModel.BatchCreateOrUpdate(ctx, IPWhitelistToIPs(data), true)
	if err != nil {
		return "", err
	}

	return action, err
}

func IPWhitelistToIPs(data *entity.IPWhitelist) []*entity.IP {
	ips := make([]*entity.IP, 0)
	for _, ip := range strings.Split(data.GIPList, ",") {
		address := ip
		addressType := "cidr"
		// 非法的ip直接排除掉
		if address == "0.0.0.0/0" {
			continue
		} else if strings.Contains(address, "/32") {
			addressType = "ip"
			address = strings.TrimSuffix(address, "/32")
		}
		ips = append(ips, &entity.IP{
			Type:             addressType,
			CreateType:       "sync",
			Address:          address,
			BindInstanceType: "polardb_whitelist",
			InstanceID:       data.GlobalSecurityGroupID,
			Desc:             "",
			RegionID:         data.RegionID,
			IspID:            data.IspID,
			IspType:          data.IspType,
		})
	}
	return ips
}

// BatchCreateOrUpdate 批量创建或更新
func (r IPWhitelist) BatchCreateOrUpdate(ctx context.Context, data []*entity.IPWhitelist) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := v.GlobalIgName

		if v.GlobalIgName == "" {
			continue
		}
		action, err := r.CreateOrUpdate(ctx, v)
		if err != nil {
			errText := fmt.Sprintf("name: %s, errors: %v", v.GlobalIgName, err.Error())
			if action == "update" {
				result.UpdateErrors = append(result.UpdateErrors, errText)
			} else {
				result.Errors = append(result.Errors, errText)
			}
			continue
		}

		if action == "update" {
			result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
		} else {
			result.SuccessCreated = append(result.SuccessCreated, dbFlag)
		}
		result.Success = append(result.Success, dbFlag)
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

func IPWhitelistsToPb(data []*entity.IPWhitelist) ([]*cloudman.IPWhitelist, error) {
	res := make([]*cloudman.IPWhitelist, 0)

	var accountIds []string
	for _, a := range data {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(context.Background(), accountIds)
		if err != nil {
			return nil, err
		}
	}

	for _, e := range data {
		res = append(res, &cloudman.IPWhitelist{
			Name:           e.GlobalIgName,
			WhitelistId:    e.GlobalSecurityGroupID,
			SecurityIpType: e.SecurityIPType,
			Ips:            strings.Split(e.GIPList, ","),
			DbInstanceIds:  e.DBInstances,
			RegionId:       e.RegionID,
			IspId:          e.IspID,
			IspType:        e.IspType,
			IspName:        ac[e.IspID].Name,
		})
	}
	return res, nil
}

// GetBySecurityGroupID xx
func (r IPWhitelist) GetBySecurityGroupID(ctx context.Context, sgID string) (*entity.IPWhitelist, error) {
	c := entity.GetIPWhitelistCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("GlobalSecurityGroupId", sgID))
	var sgEntity entity.IPWhitelist
	_, err := FindOne(ctx, c, filter, &sgEntity)
	return &sgEntity, err
}

// GetByName xx
func (r IPWhitelist) GetByName(ctx context.Context, regionID string, name string) ([]*entity.IPWhitelist, error) {
	c := entity.GetIPWhitelistCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("GlobalIgName", name), Filter("RegionId", regionID))
	var sgEntity []*entity.IPWhitelist
	_, err := FindMany(ctx, c, filter, &sgEntity)
	return sgEntity, err
}
