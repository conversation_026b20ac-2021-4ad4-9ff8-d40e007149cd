package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// MysqlClusterEndpointResourceModel 主机资源model
var MysqlClusterEndpointResourceModel = new(MysqlClusterEndpointResource)

// MysqlClusterEndpointResource 主机资源
type MysqlClusterEndpointResource struct{}

// BatchCreateOrUpdate 批量创建或更新
func (h MysqlClusterEndpointResource) BatchCreateOrUpdate(ctx context.Context, data []*entity.MysqlClusterEndpointResource) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.DBClusterID, v.DBEndpointID)

		if v.DBClusterID != "" && v.DBEndpointID != "" {
			action, err := h.CreateOrUpdate(ctx, v.DBClusterID, v)
			if err != nil {
				errText := fmt.Sprintf("cluster_id: %s, errors: %v", v.DBClusterID, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				go hooks.PubMysqlEPHandler(context.Background(), hooks.UpdateMysqlEP, *v)
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				go hooks.PubMysqlEPHandler(context.Background(), hooks.CreateMysqlEP, *v)
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// CreateOrUpdate 创建/更新,clusterID和db_name才能确定唯一
func (h MysqlClusterEndpointResource) CreateOrUpdate(ctx context.Context, clusterID string, data *entity.MysqlClusterEndpointResource) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	action := "create"
	c := entity.GetMysqlClusterEndpointResCollection(GetEngine())
	filter := map[string]interface{}{
		"DBClusterID":  clusterID,
		"DBEndpointId": data.DBEndpointID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.MysqlClusterEndpointResource
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	return action, err
}

// FindMany find many
func (h MysqlClusterEndpointResource) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.MysqlClusterEndpointResource, error) {
	c := entity.GetMysqlClusterEndpointResCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)

	cursor, err := c.Find(ctx, filters)
	if err != nil {
		return nil, err
	}

	var resource []*entity.MysqlClusterEndpointResource
	err = cursor.All(ctx, &resource)
	return resource, err
}
