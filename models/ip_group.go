package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// IPGroupModel zone-model
var IPGroupModel = new(IPGroup)

// IPGroup zone
type IPGroup struct {
}

func (r IPGroup) Query(ctx context.Context, params *schema.IPGroupQueryParam) ([]*entity.IPGroup, uint64, error) {
	c := entity.GetIPGroupCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.IPGroupColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.IPGroup, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.IPGroup{}.Ordering()} // 加入默认排序
	OrderFields = append(OrderFields, params.BuildToOrderFields()...)

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// CreateOrUpdate 创建/更新
func (r IPGroup) CreateOrUpdate(ctx context.Context, data *entity.IPGroup) (string, error) {
	action := "create"
	c := entity.GetIPGroupCollection(GetEngine())
	filter := map[string]interface{}{
		"Name": data.Name,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.IPGroup
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.CreatedTime = m.CreatedTime
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	if err != nil {
		return "", err
	}

	return action, err
}

// BatchCreateOrUpdate 批量创建或更新
func (r IPGroup) BatchCreateOrUpdate(ctx context.Context, data []*entity.IPGroup) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := v.Name

		if v.Name == "" {
			continue
		}
		action, err := r.CreateOrUpdate(ctx, v)
		if err != nil {
			errText := fmt.Sprintf("name: %s, errors: %v", v.Name, err.Error())
			if action == "update" {
				result.UpdateErrors = append(result.UpdateErrors, errText)
			} else {
				result.Errors = append(result.Errors, errText)
			}
			continue
		}

		if action == "update" {
			result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
		} else {
			result.SuccessCreated = append(result.SuccessCreated, dbFlag)
		}
		result.Success = append(result.Success, dbFlag)
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

func IPGroupsToPb(ctx context.Context, data []*entity.IPGroup) ([]*cloudman.IPGroup, error) {
	res := make([]*cloudman.IPGroup, 0)
	for _, e := range data {
		ee := &cloudman.IPGroup{
			Id:          e.ID.Hex(),
			Name:        e.Name,
			Description: e.Description,
			CreatedTime: time.Unix(e.CreatedTime, 0).Format(time.DateTime),
			UpdatedTime: time.Unix(e.UpdatedTime, 0).Format(time.DateTime),
			CreateUser:  e.CreateUser,
			UpdateUser:  e.UpdateUser,
			Ips:         []string{},
			IpIds:       e.RelatedIPs,
		}
		ips, err := IPModel.FindManyWithPK(ctx, e.RelatedIPs)
		if err != nil {
			return nil, err
		}
		for _, ip := range ips {
			ee.Ips = append(ee.Ips, ip.Address)
		}
		res = append(res, ee)
	}
	return res, nil
}

func PbToIPGroup(name, description string, ipIds []string) (*entity.IPGroup, error) {
	return &entity.IPGroup{
		Name:        name,
		Description: description,
		RelatedIPs:  ipIds,
	}, nil
}

// Get xx
func (r IPGroup) Get(ctx context.Context, oid string) (*entity.IPGroup, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetIPGroupCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var zoneEntity entity.IPGroup
	_, err = FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// Delete delete
func (r IPGroup) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetIPGroupCollection(GetEngine())

	return Delete(ctx, c, filter)
}

// FindManyWithPK find-many
func (r IPGroup) FindManyWithPK(ctx context.Context, ids []string) ([]entity.IPGroup, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetIPGroupCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.IPGroup
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}
