package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// MysqlDatabaseAccountResourceModel 主机资源model
var MysqlDatabaseAccountResourceModel = new(MysqlDatabaseAccountResource)

// MysqlDatabaseAccountResource 主机资源
type MysqlDatabaseAccountResource struct{}

// BatchCreateOrUpdate 批量创建或更新
func (h MysqlDatabaseAccountResource) BatchCreateOrUpdate(ctx context.Context, data []*entity.MysqlDatabaseAccountResource) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.ClusterID, v.AccountName)

		if v.ClusterID != "" && v.AccountName != "" {
			action, err := h.CreateOrUpdate(ctx, v.ClusterID, v)
			if err != nil {
				errText := fmt.Sprintf("cluster_id: %s, errors: %v", v.ClusterID, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// CreateOrUpdate 创建/更新,clusterID和db_name才能确定唯一
func (h MysqlDatabaseAccountResource) CreateOrUpdate(ctx context.Context, clusterID string, data *entity.MysqlDatabaseAccountResource) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	action := "create"
	c := entity.GetMysqlDatabaseAccountResCollection(GetEngine())
	filter := map[string]interface{}{
		"cluster_id":  clusterID,
		"AccountName": data.AccountName,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.MysqlDatabaseAccountResource
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	return action, err
}

// Query 查询
func (h MysqlDatabaseAccountResource) Query(ctx context.Context, params *schema.MysqlDatabaseAccountResourceQueryParams) ([]*entity.MysqlDatabaseAccountResource, uint64, error) {
	c := entity.GetMysqlClusterResCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.MysqlDatabaseAccountResourceColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]*entity.MysqlDatabaseAccountResource, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.MysqlClusterDBResourceEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	if params.TagKey != "" {
		tagFilter, err := h.withTagsFilter(ctx, filter, params.TagKey, params.TagValues)
		if err != nil {
			return list, 0, nil
		}
		filter = tagFilter
	}

	// oids, _ := getFilterWithPK([]string{"608aea025ac12fa358b0eb47"})
	// cur, err := c.Find(ctx,
	// 	bson.M{
	// 	"is_delete":0,
	// 	"_id": bson.M{"$in": oids}}, findOption)
	// if err != nil {
	// 	return nil, 0, err
	// }
	// err = cur.All(ctx, &list)

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter.Map(), &list, findOption)
	if err != nil {
		return list, 0, err
	}

	return list, pr.Total, err
}

func (h MysqlDatabaseAccountResource) withTagsFilter(ctx context.Context, filter bson.D, tagKey string, tagValue []string) (bson.D, error) {
	oid, err := h.WithTagsOptions(ctx, tagKey, tagValue)
	if err != nil {
		return filter, err
	}
	if len(oid) == 0 {
		return filter, errors.New("not found tag")
	}
	// for _, v := range oid {
	// 	fmt.Println("vvvv:", v.Hex())
	// }
	filter = append(filter, bson.E{Key: "_id", Value: bson.M{"$in": oid}})
	return filter, nil
}

// WithTagsOptions 根据标签查询主机，返回绑定的资源ID,再使用in查询即可
func (h MysqlDatabaseAccountResource) WithTagsOptions(ctx context.Context, tagKey string, tagValue []string) ([]primitive.ObjectID, error) {
	return ResourceBindTags.FindResourceIDs(ctx, "MysqlCluster", tagKey, tagValue)
}

// FindMany find many
func (h MysqlDatabaseAccountResource) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.MysqlDatabaseAccountResource, error) {
	c := entity.GetMysqlDatabaseAccountResCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)

	cursor, err := c.Find(ctx, filters)
	if err != nil {
		return nil, err
	}

	var resource []*entity.MysqlDatabaseAccountResource
	err = cursor.All(ctx, &resource)
	return resource, err
}
