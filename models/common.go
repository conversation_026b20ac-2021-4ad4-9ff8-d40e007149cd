package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// WrapPageQuery 包装分页查询结果
func WrapPageQuery(
	ctx context.Context,
	c *mongo.Collection,
	pp schema.PaginationParam,
	filter interface{},
	out interface{},
	opts ...*options.FindOptions,
) (*schema.PaginationResult, error) {
	total, err := FindPage(ctx, c, pp, filter, out, opts...)
	if err != nil {
		return nil, err
	}
	return &schema.PaginationResult{
		Total: uint64(total),
	}, nil
}

// FindPage 查询分页数据
func FindPage(
	ctx context.Context,
	c *mongo.Collection,
	pp schema.PaginationParam,
	filter interface{},
	out interface{},
	opts ...*options.FindOptions,
) (int64, error) {
	count, err := c.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	} else if count == 0 {
		return 0, nil
	}
	current, pageSize := pp.GetCurrent(), pp.GetPageSize()
	opt := new(options.FindOptions)
	if len(opts) > 0 {
		opt = opts[0]
	}
	if current > 0 && pageSize > 0 {
		opt.SetSkip(int64((current - 1) * pageSize))
		opt.SetLimit(int64(pageSize))
	} else if pageSize > 0 {
		opt.SetLimit(int64(pageSize))
	}
	// opt.SetProjection(bson.M{"args": 0}) // 剔除字段和指定字段选项
	// opt.Sort = bson.D{{}}
	_, err = FindMany(ctx, c, filter, out, opt)
	return count, err
}

// FindOne 查询单条数据
func FindOne(ctx context.Context, c *mongo.Collection, filter, out interface{}) (bool, error) {
	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err != nil {
		return false, err
	}
	err = result.Decode(out)
	if err != nil {
		return false, err
	}
	return true, nil
}

// FindPK 根据oid精确查找
func FindPK(ctx context.Context, c *mongo.Collection, oid string, out interface{}) (bool, error) {
	pk, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return false, err
	}

	result := c.FindOne(ctx, bson.M{"_id": pk})
	err = result.Err()
	if err != nil {
		return false, err
	}

	err = result.Decode(out)
	if err != nil {
		return false, err
	}
	return true, nil
}

// FindMany 查询多条数据
func FindMany(ctx context.Context, c *mongo.Collection, filter, out interface{}, opt ...*options.FindOptions) (bool, error) {
	cursor, err := c.Find(ctx, filter, opt...)
	if err != nil {
		return false, err
	}
	err = cursor.All(ctx, out)
	if err != nil {
		return false, err
	}
	return true, nil
}

/*
========================数据写入mongo方法========================
*/

// BeforeInsert before insert
func BeforeInsert(data map[string]interface{}) {
	data["created_time"] = time.Now().Unix()
	data["is_delete"] = 0
}

// BeforeUpdate before update
func BeforeUpdate(data map[string]interface{}) {
	delete(data, "_id")
	data["updated_time"] = time.Now().Unix()
}

// Insert 插入数据
func Insert(ctx context.Context, c *mongo.Collection, doc interface{}) error {
	_, err := c.InsertOne(ctx, doc)
	return err
}

// InsertByJSON 使用json格式插入数据避免nil值被写入
func InsertByJSON(ctx context.Context, c *mongo.Collection, doc interface{}) error {
	m, err := mapstruct.Struct2Map(doc)
	if err != nil {
		return err
	}
	_, err = c.InsertOne(ctx, m)
	return err
}

// InsertMany 插入多条数据
func InsertMany(ctx context.Context, c *mongo.Collection, docs []interface{}) error {
	_, err := c.InsertMany(ctx, docs)
	return err
}

// UpdateFields 更新自定字段数据
func UpdateFields(ctx context.Context, c *mongo.Collection, filter, doc interface{}) error {
	return UpdateOne(ctx, c, filter, doc)
}

// UpdateManyFields 更新多条指定字段的数据
func UpdateManyFields(ctx context.Context, c *mongo.Collection, filter, doc interface{}) error {
	return UpdateMany(ctx, c, filter, doc)
}

// UpdateOne 更新数据
func UpdateOne(ctx context.Context, c *mongo.Collection, filter, doc interface{}) error {
	_, err := c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: doc}})
	return err
}

// FindOneAndUpdate 查询并更新
func FindOneAndUpdate(ctx context.Context, c *mongo.Collection, filter, doc interface{}) (*mongo.SingleResult, error) {
	res := c.FindOneAndUpdate(ctx, filter, bson.D{{Key: "$set", Value: doc}})
	return res, res.Err()
}

// UpdateMany 更新多条数据
func UpdateMany(ctx context.Context, c *mongo.Collection, filter, doc interface{}) error {
	_, err := c.UpdateMany(ctx, filter, bson.D{{Key: "$set", Value: doc}})
	return err
}

// UpdateManyWithResult 更新多条数据
func UpdateManyWithResult(ctx context.Context, c *mongo.Collection, filter, doc interface{}) (*mongo.UpdateResult, error) {
	return c.UpdateMany(ctx, filter, bson.D{{Key: "$set", Value: doc}})
}

// Delete 删除数据
func Delete(ctx context.Context, c *mongo.Collection, filter interface{}) error {
	var _, err = c.UpdateOne(ctx, filter, map[string]interface{}{
		"$set": map[string]interface{}{
			"is_delete":    1,
			"deleted_time": time.Now().Unix(),
		},
	})
	return err
}

// DeleteMany 删除多条数据
func DeleteMany(ctx context.Context, c *mongo.Collection, filter interface{}) error {
	_, err := c.UpdateMany(ctx, filter, bson.D{{Key: "$set", Value: bson.M{"deleted_time": time.Now().Unix(), "is_delete": 1}}})
	return err
}

/*
========================通用filter方法========================
*/

// DefaultFilter 默认的查询参数,is_delete:0
func DefaultFilter(ctx context.Context, params ...bson.E) bson.D {
	var d bson.D
	for _, v := range params {
		if v.Key != "" {
			d = append(d, v)
		}
	}
	d = append(d, Filter("is_delete", 0))
	return d
}

// NoDelFilter 不过滤delete字段
func NoDelFilter(ctx context.Context, params ...bson.E) bson.D {
	var d bson.D
	if len(params) > 0 {
		d = append(d, params...)
	}
	return d
}

// RegexFilter 正则过滤
func RegexFilter(key, value string) bson.E {
	return bson.E{
		Key: key,
		Value: bson.M{
			"$regex": value,
			// "$options": "i",
		},
	}
}

// OrRegexFilter 正则过滤($or)
func OrRegexFilter(key, value string) bson.M {
	return bson.M{
		key: bson.M{
			"$regex":   value,
			"$options": "i",
		},
	}
}

// Filter 过滤
func Filter(key string, value interface{}) bson.E {
	return bson.E{
		Key:   key,
		Value: value,
	}
}

// ResReadDefaultFilter 过滤用户可查看的资源
func ResReadDefaultFilter(ctx context.Context, resourceType string, params ...bson.E) bson.D {
	filter := DefaultFilter(ctx, params...)
	if permission.IsAdminFromCtx(ctx) || permission.HasViewFromCtx(ctx) {
		return filter
	}

	allIspAllowed, allIspIDs := permission.GetUserUnionInstancesFromCtx(ctx, "admin_all_account", "admin_"+resourceType+"_account", "view_all_account", "view_"+resourceType+"_account")
	allTagsAllowed, allTagIDs := permission.GetUserUnionInstancesFromCtx(ctx, "admin_all_sys_tag", "admin_"+resourceType+"_sys_tag", "view_all_sys_tag", "view_"+resourceType+"_sys_tag")
	if allIspAllowed || allTagsAllowed {
		return filter
	}

	return ResourceFilter(ctx, filter, allIspIDs, allTagIDs)
}

// AccountDefaultFilter 过滤云厂商_id集合
func AccountDefaultFilter(ctx context.Context, params ...bson.E) bson.D {
	filter := DefaultFilter(ctx, params...)
	if permission.IsAdminFromCtx(ctx) || permission.HasViewFromCtx(ctx) {
		return filter
	}

	allIspAllowed, allIspIDs := permission.GetUserUnionInstancesFromCtx(ctx, "admin_all_account", "admin_host_account", "admin_mysql_account", "admin_redis_account")
	if allIspAllowed {
		return filter
	}

	return AccountFilter(ctx, filter, allIspIDs)
}

// ResourceFilter ...
func ResourceFilter(ctx context.Context, filter bson.D, ispIDs, tagIDs []string) bson.D {
	if len(ispIDs) == 0 {
		ispIDs = append(ispIDs, constant.NilFilterValue)
	}
	tags := IDToTagsFilter(ctx, tagIDs)
	if len(tags) == 0 {
		tags = append(tags, bson.D{{Key: "Tags.Tag", Value: bson.D{{Key: "$elemMatch", Value: bson.M{"TagKey": constant.NilFilterValue, "TagValue": constant.NilFilterValue}}}}})
	}

	filter = append(filter, bson.E{Key: "$or", Value: bson.A{bson.D{{
		Key:   "isp_id",
		Value: bson.D{{"$in", ispIDs}},
	}}, bson.D{{
		Key:   "$or",
		Value: tags,
	}}}})
	return filter
}

// AccountFilter ...
func AccountFilter(ctx context.Context, filter bson.D, ispIDs []string) bson.D {
	var ispObjIDs []primitive.ObjectID
	for _, v := range ispIDs {
		objID, err := primitive.ObjectIDFromHex(v)
		if err == nil {
			ispObjIDs = append(ispObjIDs, objID)
		}
	}

	if len(ispObjIDs) == 0 {
		nilObjID, _ := primitive.ObjectIDFromHex("FFFFFFFF")
		ispObjIDs = append(ispObjIDs, nilObjID)
	}

	filter = append(filter, bson.E{Key: "$or", Value: bson.A{bson.D{{
		Key:   "_id",
		Value: bson.D{{"$in", ispObjIDs}},
	}}}})
	return filter
}

// IDToTagsFilter ...
func IDToTagsFilter(ctx context.Context, tagIDs []string) []interface{} {
	var tags []interface{}
	for _, v := range tagIDs {
		tag, err := ResourceTags.Get(ctx, v)
		if err != nil {
			return []interface{}{}
		}
		tags = append(tags, bson.D{{Key: "Tags.Tag", Value: bson.D{{Key: "$elemMatch", Value: bson.M{"TagKey": tag.Key, "TagValue": tag.Value}}}}})
	}
	return tags
}

// CheckTag ...
type CheckTag struct {
	Key   string
	Value string
	Type  string
}

// PbToCheckTags ...
func PbToCheckTags(tags []*cloudman.ResourceTag) []CheckTag {
	var checkTags []CheckTag
	for _, v := range tags {
		checkTags = append(checkTags, CheckTag{
			Key:   v.Key,
			Value: v.Value,
			Type:  v.Type,
		})
	}
	return checkTags
}

// EntityToCheckTags ...
func EntityToCheckTags(tags []entity.Tag) []CheckTag {
	var checkTags []CheckTag
	for _, v := range tags {
		checkTags = append(checkTags, CheckTag{
			Key:   v.TagKey,
			Value: v.TagValue,
		})
	}
	return checkTags
}

// RedisEntityToCheckTags ...
func RedisEntityToCheckTags(tags []entity.RedisInstanceTag) []CheckTag {
	var checkTags []CheckTag
	for _, v := range tags {
		checkTags = append(checkTags, CheckTag{
			Key:   v.Key,
			Value: v.Value,
		})
	}
	return checkTags
}

// CheckIspAndTagsPermFromCtx resource: host, mysql, redis  permType: admin, view
func CheckIspAndTagsPermFromCtx(ctx context.Context, resource string, permType string, isp string, checkTags []CheckTag) bool {
	return CheckIspAndTagsPerm(ctx, permission.GetUsername(ctx), resource, permType, isp, checkTags)
}

// CheckIspAndTagsPerm resource: host, mysql, redis  permType: admin, view
func CheckIspAndTagsPerm(ctx context.Context, username string, resource string, permType string, isp string, checkTags []CheckTag) bool {
	if permission.IsAdmin(ctx, username) || permission.HasView(ctx, username) && permType == "view" {
		return true
	}

	var allIspAllowed, allTagsAllowed bool
	var allIspIDs, allTagIDs []string
	if permType == "view" {
		allIspAllowed, allIspIDs = permission.GetUserUnionInstances(ctx, username, "admin_all_account", "admin_"+resource+"_account", "view_all_account", "view_"+resource+"_account")
		allTagsAllowed, allTagIDs = permission.GetUserUnionInstances(ctx, username, "admin_all_sys_tag", "admin_"+resource+"_sys_tag", "view_all_sys_tag", "view_"+resource+"_sys_tag")
	} else {
		allIspAllowed, allIspIDs = permission.GetUserUnionInstances(ctx, username, "admin_all_account", "admin_"+resource+"_account")
		allTagsAllowed, allTagIDs = permission.GetUserUnionInstances(ctx, username, "admin_all_sys_tag", "admin_"+resource+"_sys_tag")
	}

	if allIspAllowed || allTagsAllowed {
		return true
	}
	for _, v := range allIspIDs {
		if v == isp {
			return true
		}
	}
	for _, v := range allTagIDs {
		tag, _ := ResourceTags.Get(ctx, v)
		if tag == nil {
			continue
		}
		for _, checkTag := range checkTags {
			// 检查tag是否符合并且当前该tag为系统标签
			if checkTag.Key == tag.Key && checkTag.Value == tag.Value && tag.Type == "system" {
				return true
			}
		}
	}
	return false
}

// OrderFieldFunc 排序字段转换函数
type OrderFieldFunc func(string) string

// ParseOrder 解析排序字段
func ParseOrder(items []*schema.OrderField, handle ...OrderFieldFunc) bson.D {
	d := make(bson.D, 0)
	for _, item := range items {
		key := item.Key
		if len(handle) > 0 {
			key = handle[0](key)
		}

		direction := 1
		if item.Direction == schema.OrderByDESC {
			direction = -1
		}
		d = append(d, bson.E{Key: key, Value: direction})
	}

	return d
}

// ColumnFilter 字段过滤器,目前仅支持or(或)查询
func ColumnFilter(columns schema.BindQueryParams) []bson.E {
	var bsonEs []bson.E
	m := columns.BuildToMap()
	for key, value := range m {
		if i, ok := value.(int32); ok && i == 0 {
			continue
		}
		if value == "" {
			continue
		}
		if str, ok := value.(string); ok && str[0] == '$' {
			bsonEs = append(bsonEs, RegexFilter(key, str[1:]))
		} else {
			bsonEs = append(bsonEs, Filter(key, value))
		}
	}
	return bsonEs
}

// SearchFilter 搜索过滤器
func SearchFilter(search schema.SearchParams) bson.E {
	var bsonA bson.A
	if search.Keywords == "" {
		return bson.E{}
	}
	for _, s := range search.SearchField {
		bsonA = append(bsonA, OrRegexFilter(s, search.Keywords))
	}
	return Filter("$or", bsonA)
}

func mapToFilter(data map[string]interface{}) []bson.E {
	r := make([]bson.E, 0)
	for k, v := range data {
		r = append(r, Filter(k, v))
	}
	return r
}

func getFilterWithPK(data []string) (objs []primitive.ObjectID, err error) {
	for _, x := range data {
		obj, err := primitive.ObjectIDFromHex(x)
		if err != nil {
			break
		}
		objs = append(objs, obj)
	}
	return
}
