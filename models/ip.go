package models

import (
	"context"
	"errors"
	"fmt"
	"net"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// IPModel zone-model
var IPModel = new(IP)

// IP zone
type IP struct {
}

// Query query
func (r IP) Query(ctx context.Context, params *schema.IPQueryParams) ([]*entity.IP, uint64, error) {
	c := entity.GetIPCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.IPColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.IP, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.IP{}.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	logger.Info(fmt.Sprintf("Filter:%+v", filter.Map()))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get xx
func (r IP) Get(ctx context.Context, oid string) (*entity.IP, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetIPCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var zoneEntity entity.IP
	_, err = FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// FindManyWithPK find-many
func (r IP) FindManyWithPK(ctx context.Context, ids []string) ([]entity.IP, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetIPCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.IP
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindMany find-many
func (r IP) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.IP, error) {
	c := entity.GetIPCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
		if option[0].WithoutIsp {
			filters = DefaultFilter(ctx, mapToFilter(filter)...)
		}
	}

	var resource []*entity.IP

	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// FindManyWithIPs ...
func (r IP) FindManyWithIPs(ctx context.Context, ipIDs []string) ([]*entity.IP, error) {
	if len(ipIDs) == 0 {
		return nil, nil
	}

	c := entity.GetIPCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "address", Value: map[string]interface{}{"$in": ipIDs},
	})

	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	var list []*entity.IP
	for cur.Next(ctx) {
		var ip *entity.IP
		if err = cur.Decode(&ip); err != nil {
			return nil, err
		}
		list = append(list, ip)
	}

	return list, nil
}

func (r IP) IsLegal(data *entity.IP) error {
	if data.Address == "0.0.0.0/0" {
		return fmt.Errorf("不是合法的地址")
	}
	if net.ParseIP(data.Address) == nil {
		_, _, err := net.ParseCIDR(data.Address)
		if err != nil {
			return fmt.Errorf("不是合法的ipv4地址或网段")
		}
	}

	return nil
}

// CreateOrUpdate 创建/更新
func (r IP) CreateOrUpdate(ctx context.Context, data *entity.IP, fromSync bool) (string, error) {
	err := r.IsLegal(data)
	if err != nil {
		return "", err
	}
	action := "create"

	c := entity.GetIPCollection(GetEngine())
	// 以ip的地址为唯一键
	filter := map[string]interface{}{
		"address": data.Address,
	}

	result := c.FindOne(ctx, filter)
	err = result.Err()
	if err == nil {
		var m entity.IP
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		if !fromSync {
			m.Desc = data.Desc
		}
		m.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, m)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	return action, err
}

// BatchCreateOrUpdate 批量创建或更新
func (r IP) BatchCreateOrUpdate(ctx context.Context, data []*entity.IP, fromSync bool) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s_%s", v.Address, v.IspID)

		if v.Address != "" && v.IspID != "" {
			action, err := r.CreateOrUpdate(ctx, v, fromSync)
			if err != nil {
				errText := fmt.Sprintf("address: %s, isp_id: %s, errors: %v", v.Address, v.IspID, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// IPModelToPb ...
func IPModelToPb(ctx context.Context, ipEntities []*entity.IP) ([]*cloudman.IPEntity, error) {
	var accountIds []string
	for _, a := range ipEntities {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(ctx, accountIds)
		if err != nil {
			return nil, err
		}
	}

	var ipPbs []*cloudman.IPEntity
	for _, ipEntity := range ipEntities {

		ipPb := &cloudman.IPEntity{
			Id:               ipEntity.ID.Hex(),
			Type:             ipEntity.Type,
			CreateType:       ipEntity.CreateType,
			Address:          ipEntity.Address,
			BindInstanceType: ipEntity.BindInstanceType,
			InstanceId:       ipEntity.InstanceID,
			Desc:             ipEntity.Desc,
			IspId:            ipEntity.IspID,
			IspType:          ipEntity.IspType,
			IspName:          ac[ipEntity.IspID].Name,
			RegionId:         ipEntity.RegionID,
			CreatedTime:      time.Unix(ipEntity.CreatedTime, 0).Format(time.DateTime),
			UpdatedTime:      time.Unix(ipEntity.UpdatedTime, 0).Format(time.DateTime),
		}
		ipPbs = append(ipPbs, ipPb)
	}

	return ipPbs, nil
}

// Delete delete
func (r IP) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetIPCollection(GetEngine())

	logger.Info(fmt.Sprintf("Filter:%+v", filter.Map()))

	return Delete(ctx, c, filter)
}
