package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// ResourceOrder 资源订单
var ResourceOrder = new(resourceOrder)

type resourceOrder struct {
}

// Query 订单查询
func (r resourceOrder) Query(ctx context.Context, params *schema.ResourceOrderQueryParams) ([]entity.ResOrder, uint64, error) {
	c := entity.GetResOrderCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	if columnFilter := ColumnFilter(params.ResourceOrderColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}
	filter = append(filter, bson.E{
		Key: "action", Value: bson.M{"$ne": "cmdb_diff"},
	})
	var list = make([]entity.ResOrder, 0)

	findOption := &options.FindOptions{}
	OrderFields := params.BuildToOrderFields()
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, entity.ResOrderEntity.Ordering()) // 加入默认排序
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get ...
func (r resourceOrder) Get(ctx context.Context, oid string) (*entity.ResOrder, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetResOrderCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var resOrderEntity entity.ResOrder
	_, err = FindOne(ctx, c, filter, &resOrderEntity)
	return &resOrderEntity, err
}

// Create ...
func (r resourceOrder) Create(ctx context.Context, data *entity.ResOrder) (primitive.ObjectID, error) {
	// t := time.Now()
	data.ID = primitive.NewObjectID()
	data.CreatedTime = time.Now().Unix()
	c := entity.GetResOrderCollection(GetEngine())
	defer func() {
		go hooks.PubOrderStartHandler(*data)
	}()
	return data.ID, Insert(ctx, c, data)
}

// Update xxx
func (r resourceOrder) Update(ctx context.Context, id string, updateData interface{}, queryOptions ...map[string]interface{}) error {
	data, err := mapstruct.Struct2Map(updateData)
	if err != nil {
		return err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	if len(queryOptions) != 0 {
		filter = append(filter, mapToFilter(queryOptions[0])...)
	}
	if msg, ok := data["error_msg"]; ok {
		TaskLogResultModel.Create(ctx, &entity.TaskLogResult{
			TaskLogID:  id,
			TaskResult: msg.(string),
		})
	}

	c := entity.GetResOrderCollection(GetEngine())
	return UpdateMany(ctx, c, filter, data)
}

func (r resourceOrder) GetLogList(ctx context.Context, params *schema.ResourceOrderLogQueryParams) ([]entity.TaskLogResult, uint64, error) {
	c := entity.GetTaskLogResultCollection(GetEngine())

	filter := DefaultFilter(ctx)

	if columnFilter := ColumnFilter(params.ResourceOrderColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.TaskLogResult, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.TaskLogResultEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

func (r resourceOrder) FindResourceTask(ctx context.Context, taskType string, accountID string, regionID string) ([]entity.ResOrder, error) {
	c := entity.GetResOrderCollection(GetEngine())
	var list []entity.ResOrder
	filter := DefaultFilter(ctx,
		bson.E{Key: "task_type", Value: taskType},
		bson.E{Key: "account_id", Value: accountID},
		bson.E{Key: "bind_region_id", Value: regionID},
	)
	_, err := FindMany(ctx, c, filter, &list)

	return list, err
}

func (r resourceOrder) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.ResOrder, error) {
	c := entity.GetResOrderCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
		if option[0].WithoutIsp {
			filters = DefaultFilter(ctx, mapToFilter(filter)...)
		}
	}

	var resource []*entity.ResOrder

	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// UpdateStatusDetail xxx
func (r resourceOrder) UpdateStatusDetail(ctx context.Context, id string, statusDetail string) error {
	data := map[string]string{
		"status_detail": statusDetail,
	}
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}
	filter := mapToFilter(map[string]interface{}{"_id": objectID})
	c := entity.GetResOrderCollection(GetEngine())
	return UpdateOne(ctx, c, filter, data)
}

// UpdateeStatus
func (r resourceOrder) UpdateStatus(ctx context.Context, id string, status int32) error {
	data := map[string]int32{
		"status": status,
	}
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}
	filter := mapToFilter(map[string]interface{}{"_id": objectID})
	c := entity.GetResOrderCollection(GetEngine())
	return UpdateOne(ctx, c, filter, data)
}
