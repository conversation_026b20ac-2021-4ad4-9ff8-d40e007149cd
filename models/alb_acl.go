package models

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// AlbACLModel zone-model
var AlbACLModel = new(AlbACL)

// AlbACL zone
type AlbACL struct {
}

// Query ...
func (r AlbACL) Query(ctx context.Context, params *schema.ALBAclQueryParam) ([]*entity.AlbACL, uint64, error) {
	c := entity.GetAlbACLCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.ALBAclColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.AlbACL, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.AlbACL{}.Ordering()} // 加入默认排序
	OrderFields = append(OrderFields, params.BuildToOrderFields()...)

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// CreateOrUpdate 创建/更新
func (r AlbACL) CreateOrUpdate(ctx context.Context, data *entity.AlbACL) (string, error) {
	action := "create"
	c := entity.GetAlbACLCollection(GetEngine())
	filter := map[string]interface{}{
		"AclId":    data.ACLID,
		"RegionId": data.RegionID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.AlbACL
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	if err != nil {
		return "", err
	}

	_, err = IPModel.BatchCreateOrUpdate(ctx, AlbAclToIPs(data), true)
	if err != nil {
		return "", err
	}

	return action, err
}

func AlbAclToIPs(data *entity.AlbACL) []*entity.IP {
	ips := make([]*entity.IP, 0)
	for _, entry := range data.AclEntries {
		address := entry.Entry
		addressType := "cidr"
		// 非法的ip直接排除掉
		if address == "0.0.0.0/0" {
			continue
		} else if strings.Contains(address, "/32") {
			addressType = "ip"
			address = strings.TrimSuffix(address, "/32")
		}
		ips = append(ips, &entity.IP{
			Type:             addressType,
			CreateType:       "sync",
			Address:          address,
			BindInstanceType: "alb_acl",
			InstanceID:       data.ACLID,
			Desc:             entry.Description,
			RegionID:         data.RegionID,
			IspID:            data.IspID,
			IspType:          data.IspType,
		})
	}
	return ips
}

// BatchCreateOrUpdate 批量创建或更新
func (r AlbACL) BatchCreateOrUpdate(ctx context.Context, data []*entity.AlbACL) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := v.ACLID

		if v.ACLID == "" {
			continue
		}
		action, err := r.CreateOrUpdate(ctx, v)
		if err != nil {
			errText := fmt.Sprintf("name: %s, errors: %v", v.ACLID, err.Error())
			if action == "update" {
				result.UpdateErrors = append(result.UpdateErrors, errText)
			} else {
				result.Errors = append(result.Errors, errText)
			}
			continue
		}

		if action == "update" {
			result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
		} else {
			result.SuccessCreated = append(result.SuccessCreated, dbFlag)
		}
		result.Success = append(result.Success, dbFlag)
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

func ALBAclsToPb(data []*entity.AlbACL) ([]*cloudman.ALBAclEntity, error) {
	res := make([]*cloudman.ALBAclEntity, 0)

	var accountIds []string
	for _, a := range data {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(context.Background(), accountIds)
		if err != nil {
			return nil, err
		}
	}

	for _, d := range data {
		e := &cloudman.ALBAclEntity{
			AclId:            d.ACLID,
			Name:             d.ACLName,
			Status:           d.ACLStatus,
			AddressIpVersion: d.AddressIPVersion,
			CreateTime:       d.CreateTime,
			AclEntries:       []*cloudman.AclEntry{},
			RelatedListeners: []*cloudman.RelatedListener{},
			RegionId:         d.RegionID,
			IspId:            d.IspID,
			IspType:          d.IspType,
			IspName:          ac[d.IspID].Name,
		}
		for _, aclEntry := range d.AclEntries {
			e.AclEntries = append(e.AclEntries, &cloudman.AclEntry{
				Status:      aclEntry.Status,
				Ip:          aclEntry.Entry,
				Description: aclEntry.Description,
			})
		}

		for _, rl := range d.RelatedListeners {
			e.RelatedListeners = append(e.RelatedListeners, &cloudman.RelatedListener{
				Status:           rl.Status,
				ListenerPort:     fmt.Sprintf("%d", rl.ListenerPort),
				ListenerProtocol: rl.ListenerProtocol,
				LbId:             rl.ListenerID,
			})
		}
		res = append(res, e)
	}
	return res, nil
}

// GetByAclID xx
func (r AlbACL) GetByAclID(ctx context.Context, sgID string) (*entity.AlbACL, error) {
	c := entity.GetAlbACLCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("AclId", sgID))
	var sgEntity entity.AlbACL
	_, err := FindOne(ctx, c, filter, &sgEntity)
	return &sgEntity, err
}

func (r AlbACL) UpdateAclStatusByID(ctx context.Context, sgID, newStatus string) error {
	c := entity.GetAlbACLCollection(GetEngine())
	filter := DefaultFilter(ctx)
	filter = append(filter, Filter("AclId", sgID))
	change := map[string]interface{}{
		"AclStatus": newStatus,
	}
	return UpdateOne(ctx, c, filter, change)
}
