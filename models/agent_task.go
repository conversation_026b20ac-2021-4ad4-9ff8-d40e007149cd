package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// AgentTaskModel account model
var AgentTaskModel = new(AgentTask)

// AgentTask account
type AgentTask struct{}

// Query 查询列表
func (a AgentTask) Query(ctx context.Context, params *schema.AgentTaskQueryParams) ([]entity.AgentTask, uint64, error) {
	c := entity.GetAgentTaskCollection(GetEngine())
	filter := DefaultFilter(ctx)

	if columnFilter := ColumnFilter(params.AgentTaskColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]entity.AgentTask, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{new(entity.AgentTask).Ordering()}
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}
	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, err
}

// FindPK 根据主键查询
func (a AgentTask) FindPK(ctx context.Context, id string) (*entity.AgentTask, error) {
	obdID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	c := entity.GetAgentTaskCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "_id", Value: obdID})

	var ac *entity.AgentTask
	_, err = FindOne(ctx, c, filter, &ac)
	if err != nil {
		return nil, err
	}

	return ac, nil
}

// Create 创建
func (a AgentTask) Create(ctx context.Context, at *entity.AgentTask) (string, error) {
	if at.ID.IsZero() {
		at.ID = primitive.NewObjectID()
	}
	d, err := mapstruct.Struct2Map(at)
	if err != nil {
		return "", err
	}

	BeforeInsert(d)
	c := entity.GetAgentTaskCollection(GetEngine())
	result, err := c.InsertOne(ctx, d)
	if err != nil {
		return "", err
	}
	if s, ok := result.InsertedID.(primitive.ObjectID); ok {
		return s.Hex(), nil
	}
	return "", nil
}

// Delete delete
func (a AgentTask) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetAgentTaskCollection(GetEngine())

	return Delete(ctx, c, filter)
}

// CreateOrUpdate 创建或修改
func (a AgentTask) CreateOrUpdate(ctx context.Context, data *entity.AgentTask) error {
	c := entity.GetAgentTaskCollection(GetEngine())
	filter := map[string]interface{}{
		"_id": data.ID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.AgentTask
		err = result.Decode(&m)
		if err != nil {
			return err
		}
		data.CreatedTime = m.CreatedTime
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		// 主键此处不重置
		// data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}
	return err
}

// Update 修改
func (a AgentTask) Update(ctx context.Context, taskID string, data map[string]interface{}) error {
	idObj, err := primitive.ObjectIDFromHex(taskID)
	if err != nil {
		return err
	}
	filter := map[string]interface{}{
		"_id": idObj,
	}
	c := entity.GetAgentTaskCollection(GetEngine())
	err = UpdateOne(ctx, c, filter, data)
	return err
}

// ForwardStatus 直接变更任务状态
func (a AgentTask) ForwardStatus(ctx context.Context, taskID string, newStatus int32) error {
	c := entity.GetAgentTaskCollection(GetEngine())
	id, _ := primitive.ObjectIDFromHex(taskID)
	filter := map[string]interface{}{
		"_id": id,
	}
	change := map[string]interface{}{
		"status": newStatus,
	}
	err := UpdateOne(ctx, c, mapToFilter(filter), change)
	return err
}
