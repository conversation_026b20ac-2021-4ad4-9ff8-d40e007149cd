package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// HostResourceModel 主机资源model
var HostResourceModel = new(HostResource)

// HostResource 主机资源
type HostResource struct {
}

// FindExpireResource 寻找过期资源,仅返回instanceID
func (h HostResource) FindExpireResource(ctx context.Context, filter map[string]interface{}) ([]string, error) {
	option := &FindManyOption{}
	option.Projection = map[string]int{"InstanceID": 1}
	result, err := h.FindMany(ctx, filter, option)
	if err != nil {
		return nil, err
	}

	var instanceID []string
	for _, val := range result {
		instanceID = append(instanceID, val.InstanceID)
	}

	return instanceID, err
}

// FindExpireResourceInfo 寻找过期资源,返回资源详情interface
func (h HostResource) FindExpireResourceInfo(ctx context.Context, filter map[string]interface{}) (map[string]interface{}, error) {
	// option := &mongo.FindOptions{Projection: []string{"InstanceID"}}
	data, err := h.FindMany(ctx, filter)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	for _, v := range data {
		result[v.InstanceID] = v
	}

	return result, nil
}

// MarkCleanup 标记已释放待清理
func (h HostResource) MarkCleanup(ctx context.Context, ispID, regionID string, latestUpdateVersion string) (int64, error) {
	markList, _ := HostResourceModel.FindMany(ctx, map[string]interface{}{
		"isp_id":        ispID,
		"RegionID":      regionID,
		"is_delete":     0,
		"NeedCleanup":   bson.D{{"$ne", true}},
		"UpdateVersion": bson.D{{"$ne", latestUpdateVersion}},
	})
	for _, data := range markList {
		delRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "host",
			InstanceID:   data.InstanceID,
			InstanceName: data.InstanceName,
			Data: map[string]utils.FieldChangeList{
				"_del": {Before: string(delRaw), After: ""},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	}

	e := entity.GetHostResCollection(GetEngine())
	_, err := e.UpdateMany(ctx,
		bson.D{
			{"isp_id", ispID},
			{"RegionID", regionID},
			{"is_delete", 0},
		},
		bson.D{{"$set", bson.M{"NeedCleanup": false}}})
	if err != nil {
		return 0, err
	}
	res, err := e.UpdateMany(ctx,
		bson.D{
			{"isp_id", ispID},
			{"RegionID", regionID},
			{"UpdateVersion", bson.D{{"$ne", latestUpdateVersion}}},
			{"is_delete", 0},
		},
		bson.D{{"$set", bson.M{"NeedCleanup": true}}})
	if err != nil {
		return 0, err
	}
	return res.MatchedCount, nil
}

// BatchResult 批量创建/更新通用返回信息
type BatchResult struct {
	Errors         []string `json:"error"`
	Success        []string `json:"success"`
	SuccessCreated []string `json:"success_created"`
	SuccessUpdated []string `json:"success_updated"`
	Unmodified     []string `json:"unmodified"`
	UpdateErrors   []string `json:"update_error"`
}

// HostUpdateOption ...
type HostUpdateOption struct {
	UpdateTags bool
	UpdateDesc bool
	FromSync   bool
}

// BatchCreateOrUpdate 批量创建或更新(data数组必须为同一个云厂商)
func (h HostResource) BatchCreateOrUpdate(ctx context.Context, data []*entity.HostResource, option HostUpdateOption) (*BatchResult, error) {
	result := &BatchResult{}
	if len(data) == 0 {
		return result, nil
	}
	var err error
	var updateResource []entity.HostResource
	// var createResource []entity.HostResource
	var ispID = ""

	for _, v := range data {
		if v.InstanceID != "" {
			if v.IspID != "" {
				ispID = v.IspID
			}
			var resTags []*cloudman.ResourceTag
			for _, tag := range v.Tags.Tag {
				resTags = append(resTags, &cloudman.ResourceTag{Key: tag.TagKey, Value: tag.TagValue})
			}
			action, err := h.CreateOrUpdate(ctx, v.InstanceID, v, resTags, option)
			if err != nil {
				errText := fmt.Sprintf("instanceId: %s, errors: %v", v.InstanceID, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "nothing" {
				result.Unmodified = append(result.Unmodified, v.InstanceID)
			} else if action == "update" {
				updateResource = append(updateResource, *v)
				result.SuccessUpdated = append(result.SuccessUpdated, v.InstanceID)
			} else if action == "create" {
				// createResource = append(createResource, *v)
				result.SuccessCreated = append(result.SuccessCreated, v.InstanceID)
			}
			result.Success = append(result.Success, v.InstanceID)
		}
	}

	go func() {
		baseCtx := context.WithValue(context.Background(), constant.HookCtxMeta, map[string]string{
			"isp_id":   ispID,
			"username": permission.GetUsername(ctx),
		})
		m, ok := ctx.Value(constant.OrderCtxMeta).(map[string]string)
		orderUsername := ""
		if ok {
			value, ok2 := m["order_username"]
			if ok2 {
				orderUsername = value
			}
		}
		baseCtx = context.WithValue(baseCtx, constant.OrderCtxMeta, map[string]string{
			"order_username": orderUsername,
		})
		// hooks.PubCreateHostResourceHandler(baseCtx, createResource...)

		// update时需要重新获取一遍实例数据，不然会缺少cmdb的id导致同步失败
		instanceIDs := []string{}
		for _, res := range updateResource {
			instanceIDs = append(instanceIDs, res.InstanceID)
		}
		entities, err := h.GetByInstanceIDs(baseCtx, instanceIDs)
		if err != nil {
			logger.Errorf("get instance by ids failed: %v", err)
			return
		}
		realUpdateResource := []entity.HostResource{}
		for _, e := range entities {
			realUpdateResource = append(realUpdateResource, *e)
		}
		// 分多次请求, 避免cmdb接口未响应导致报错
		batchSize := 100
		for i := 0; i < len(realUpdateResource); i += batchSize {
			updates := realUpdateResource[i:min(len(realUpdateResource), i+batchSize)]
			hooks.PubUpdateHostResourceHandler(baseCtx, updates...)
		}
	}()

	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// CreateOrUpdate 创建/更新, 新增主机自动生成agentID
// accountIndex = 0: 需要重新获取管理编号
func (h HostResource) CreateOrUpdate(ctx context.Context, instanceID string, data *entity.HostResource, tags []*cloudman.ResourceTag, option HostUpdateOption) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	var err error
	var retErr error
	realAction := ""

	c := entity.GetHostResCollection(GetEngine())
	filter := map[string]interface{}{
		"InstanceID": instanceID,
		"is_delete":  0,
	}

	result := c.FindOne(ctx, filter)
	mongoError := result.Err() // 避免出现AgentID被覆盖的情况
	if mongoError == nil {
		var m entity.HostResource
		err = result.Decode(&m) // 合并查询结果，避免因为空值修改错误
		if err != nil {
			return "", err
		}

		data.ID = m.ID
		raw, _ := mapstruct.Struct2Map(data)
		// 针对几个数组字段的零值处理
		if len(data.PublicIPAddress) == 0 {
			raw["PublicIpAddress"] = []string{}
		}
		if len(data.InnerIPAddress) == 0 {
			raw["InnerIpAddress"] = []string{}
		}
		// 移除Release及AutoReleaseTime字段,不接受控制台传入,仅允许回收站修改该字段
		delete(raw, "Recyclable")
		delete(raw, "AutoReleaseTime")
		delete(raw, "NeedCleanup")
		delete(raw, "BkCmdb")
		delete(raw, "UpdateVersion")
		if data.IspType == "aws" {
			delete(raw, "EipAddress")
		}
		// jumpserver类型的描述信息由用户自行维护，不要覆盖
		if !option.UpdateDesc {
			delete(raw, "Description")
		}
		if !option.UpdateTags {
			delete(raw, "Tags")
		}
		_, updateErr := c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: raw}})
		retErr = updateErr
		// 由于部分字段（如NetworkInterfaces）的内部结构较为复杂，部分数据下UpdateOne的modifiedCount结果字段始终为1
		// 所以无法通过modifiedCount去判断是否真实有修改，从而确定是否同步CMDB
		// 此处采用自行序列化并比对字段的方式判断是否真实修改
		dbRaw, _ := mapstruct.Struct2Map(m)
		if len(data.PublicIPAddress) == 0 {
			dbRaw["PublicIpAddress"] = []string{}
		}
		if len(data.InnerIPAddress) == 0 {
			dbRaw["InnerIpAddress"] = []string{}
		}
		isModified, changeField := utils.IsStructModified(dbRaw, raw)
		if isModified {
			c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: map[string]interface{}{
				"updated_time":  time.Now().Unix(),
				"UpdateVersion": data.UpdateVersion,
			}}})
			realAction = "update"
			changeItem := utils.ResourceChangeList{
				AccountID:    data.IspID,
				Type:         "host",
				InstanceID:   m.InstanceID,
				InstanceName: m.InstanceName,
				Data:         changeField,
			}
			go func() {
				hooks.PubResourceChangeListHandler(context.Background(), changeItem)
			}()
		} else {
			c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: map[string]interface{}{
				"UpdateVersion": data.UpdateVersion,
			}}})
			realAction = "nothing"
		}
	} else if mongoError == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		realAction = "create"
		_, retErr = c.InsertOne(ctx, data)
		addRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "host",
			InstanceID:   data.InstanceID,
			InstanceName: data.InstanceName,
			Data: map[string]utils.FieldChangeList{
				"_add": {Before: "", After: string(addRaw)},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	} else {
		retErr = mongoError
	}

	if option.UpdateTags {
		// if err := ResourceBindTags.DelAll(ctx, "host", data.ID, data.IspType); err != nil {
		// 	return "", err
		// }
		if data.Tags.Tag != nil {
			// err := ResourceBindTags.Bind(ctx, tags, "host", data.ID, data.IspType)
			if err != nil {
				if !errors.Is(err, mongo.WriteException{}) {
					logger.Errorf("bind tags error:", err.Error())
					return "", err
				}
			}
			for _, v := range tags {
				data.Tags.Tag = append(data.Tags.Tag, entity.Tag{
					TagValue: v.Value,
					TagKey:   v.Key,
				})
			}
		}
	}

	_, err = IPModel.BatchCreateOrUpdate(ctx, HostToIPEntities(data), option.FromSync)
	if err != nil {
		logger.Errorf("create host: update ip error: %v", err)
		return "", err
	}

	return realAction, retErr
}

// Query query,customFilter可选字段，传递多项只取第一个
func (h HostResource) Query(ctx context.Context, params *schema.HostResourceQueryParams, customFilter ...map[string]interface{}) ([]*entity.HostResource, uint64, error) {
	c := entity.GetHostResCollection(GetEngine())
	filter := ResReadDefaultFilter(ctx, "host", SearchFilter(params.GetSearch()))
	if len(customFilter) != 0 {
		filter = append(filter, mapToFilter(customFilter[0])...)
	}

	if params.SearchColumnField.SearchValue != "" {
		if params.SearchColumnField.SearchKey == "PublicIpAddress" {
			// 公网IP需要从两个字段里or搜索
			filter = append(filter, Filter("$or", bson.A{
				OrRegexFilter("EipAddress.IpAddress", params.SearchColumnField.SearchValue),
				OrRegexFilter("PublicIpAddress", params.SearchColumnField.SearchValue),
			}))
		} else if params.SearchColumnField.SearchKey == "InstanceName" && utils.IsMatchRange(params.SearchColumnField.SearchValue) {
			// 支持范围搜索
			filter = append(filter, RegexFilter(
				params.SearchColumnField.SearchKey,
				utils.GetHostnameRangeRegex(strings.TrimSpace(params.SearchColumnField.SearchValue)),
			))
		} else {
			filter = append(filter, RegexFilter(params.GetSearchColumnField()))
		}
	}

	if columnFilter := ColumnFilter(params.HostResourceColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]*entity.HostResource, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.HostResourceEntity.Ordering(), {
		Key:       "InstanceID",
		Direction: 1,
	}} // 加入默认排序
	if len(params.Ordering) > 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	if params.TagKey != "" {
		tagFilter := h.withTagsFilter(ctx, filter, params.TagKey, params.TagValues)
		filter = tagFilter
	}

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter.Map(), &list, findOption)
	if err != nil {
		return list, 0, err
	}

	return list, pr.Total, err
}

func (h HostResource) withTagsFilter(ctx context.Context, filter bson.D, tagKey string, tagValue []string) bson.D {
	// {"Tags.Tag": {$elemMatch: {"TagKey": "env", "TagValue": {$in: ["dev", "sf", "rel"]}}}}
	valueFilter := bson.D{{Key: "$exists", Value: true}}
	if len(tagValue) > 0 {
		valueFilter = bson.D{{Key: "$in", Value: tagValue}}
	}
	filter = append(
		filter, bson.E{
			Key: "Tags.Tag", Value: bson.D{{
				Key: "$elemMatch", Value: bson.D{
					{Key: "TagKey", Value: "env"},
					{Key: "TagValue", Value: valueFilter}}}},
		})
	return filter
}

// WithTagsOptions 根据标签查询主机，返回绑定的资源ID,再使用in查询即可
func (h HostResource) WithTagsOptions(ctx context.Context, tagKey string, tagValue []string) ([]primitive.ObjectID, error) {
	return ResourceBindTags.FindResourceIDs(ctx, "host", tagKey, tagValue)
}

// UpdateWithTagOption 根据旧标签查询主机，并更新该标签Key的Value
func (h HostResource) UpdateWithTagOption(ctx context.Context, tagKey string, tagValue string, newValue string) error {
	c := entity.GetHostResCollection(GetEngine())
	return UpdateMany(ctx, c, bson.D{{
		Key: "Tags.Tag", Value: bson.D{{
			Key: "$elemMatch", Value: bson.D{
				{Key: "TagKey", Value: tagKey},
				{Key: "TagValue", Value: tagValue}}}}}},
		bson.D{{Key: "Tags.Tag.$.TagValue", Value: newValue}})
}

// FindWithPK 根据主键进行查询
func (h HostResource) FindWithPK(ctx context.Context, pk string) (*entity.HostResource, error) {
	c := entity.GetHostResCollection(GetEngine())
	obj, err := primitive.ObjectIDFromHex(pk)
	if err != nil {
		return nil, err
	}
	filter := DefaultFilter(ctx, bson.E{Key: "_id", Value: obj})
	var res entity.HostResource
	_, err = FindOne(ctx, c, filter, &res)
	return &res, err
}

// FindWithManyPK 根据主键列表进行查询
func (h HostResource) FindWithManyPK(ctx context.Context, pk []string) ([]*entity.HostResource, error) {
	c := entity.GetHostResCollection(GetEngine())
	ids, err := getFilterWithPK(pk)
	if err != nil {
		return nil, err
	}
	filter := ResReadDefaultFilter(ctx, "host", bson.E{Key: "_id", Value: bson.M{
		"$in": ids,
	}})
	var res []*entity.HostResource
	cursor, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cursor.All(ctx, &res)
	return res, err
}

// FindManyOption mongo多实例查询选项
type FindManyOption struct {
	options.FindOptions
	WithoutIsp bool
}

// FindMany find many
func (h HostResource) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.HostResource, error) {
	c := entity.GetHostResCollection(GetEngine())
	filters := ResReadDefaultFilter(ctx, "host", mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
		if option[0].WithoutIsp {
			filters = DefaultFilter(ctx, mapToFilter(filter)...)
		}
	}

	var resource []*entity.HostResource

	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// FiledOptions 表描述信息
func (h HostResource) FiledOptions(ctx context.Context) []*entity.FiledStruct {
	return entity.HostResourceEntity.Meta()
}

// FindOne 查找单条记录
func (h HostResource) FindOne(ctx context.Context, filter map[string]interface{}) (*entity.HostResource, error) {
	c := entity.GetHostResCollection(GetEngine())
	filters := ResReadDefaultFilter(ctx, "host", mapToFilter(filter)...)

	cursor := c.FindOne(ctx, filters)
	err := cursor.Err()
	if err != nil {
		return nil, err
	}

	var resource *entity.HostResource
	err = cursor.Decode(&resource)
	return resource, err
}

// FindOneIncludeDeleted 查找单条记录
func (h HostResource) FindOneIncludeDeleted(ctx context.Context, filter map[string]interface{}) (*entity.HostResource, error) {
	c := entity.GetHostResCollection(GetEngine())

	cursor := c.FindOne(ctx, filter)
	err := cursor.Err()
	if err != nil {
		return nil, err
	}

	var resource *entity.HostResource
	err = cursor.Decode(&resource)
	return resource, err
}

// FindManyIncludeDeleted 查找多条记录
func (h HostResource) FindManyIncludeDeleted(ctx context.Context, filter map[string]interface{}) ([]entity.HostResource, error) {
	c := entity.GetHostResCollection(GetEngine())

	var resource []entity.HostResource

	_, err := FindMany(ctx, c, filter, &resource)
	return resource, err
}

// UpdateOne 更新单条数据
func (h HostResource) UpdateOne(ctx context.Context, filter map[string]interface{}, data *entity.HostResource) error {
	c := entity.GetHostResCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	updateData, err := mapstruct.Struct2Map(data)
	if err != nil {
		return err
	}
	// defer func() {
	// 	go hooks.PubUpdateHostResourceHandler(context.Background(), *data)
	// }()
	return UpdateOne(ctx, c, filters, updateData)
}

// UpdateMany 更新多条数据
func (h HostResource) UpdateMany(ctx context.Context, filter map[string]interface{}, doc interface{}) error {
	c := entity.GetHostResCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	return UpdateMany(ctx, c, filters, doc)
}

// UpdateAgentStatus 更新agent状态
func (h HostResource) UpdateAgentStatus(ctx context.Context, agentID string, status int32) error {
	c := entity.GetHostResCollection(GetEngine())
	filters := DefaultFilter(ctx, bson.E{Key: "agent_id", Value: agentID})
	return UpdateOne(ctx, c, filters, map[string]interface{}{
		"agent_status": status,
	})
}

// UpdateAgent 更新agent状态
func (h HostResource) UpdateAgent(ctx context.Context, filter map[string]interface{}, agent *entity.HostAgent, doc *entity.HostResource) error {
	c := entity.GetHostResCollection(GetEngine())
	// 判断host条目是否能找到
	var hostRes *entity.HostResource
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	_, err := FindOne(ctx, c, filters, &hostRes)
	if err != nil {
		return err
	}
	// 先只映射基础agent数据
	agentData, err := mapstruct.Struct2Map(agent)
	if err != nil {
		return err
	}
	// 如果为自定义厂商，且用户允许agent修正数据，则做完整的数据融合
	if hostRes.IspType == "jumpserver" || (hostRes.IspType == "custom" && hostRes.AllowChangeData == true) {
		hostData, err := mapstruct.Struct2Map(doc)
		if err != nil {
			return err
		}
		mapstruct.MapAssign(agentData, hostData)
	}
	delete(agentData, "created_time")
	delete(agentData, "Tags")
	delete(agentData, "Description")

	// 执行host表的变更
	return UpdateOne(ctx, c, filters, agentData)
	// if err != nil {
	// 	return err
	// }

	// 暂时不触发CMDB变更
	// 触发CMDB变更
	// go func() {
	// 	baseCtx := context.Background()
	// 	// 重新获取一遍host，再触发CMDB更新
	// 	_, err := FindOne(baseCtx, c, filters, &hostRes)
	// 	if err != nil {
	// 		logger.Errorf(baseCtx, "get new host error: %v", err.Error())
	// 	}
	// 	go hooks.PubUpdateHostResourceHandler(context.Background(), *hostRes)
	// }()

	// return nil
}

// UpdateMonitorAgentByIPs 更新IP列表的监控状态
func (h HostResource) UpdateMonitorAgentByIPs(ctx context.Context, ips []string, status int32) error {
	c := entity.GetHostResCollection(GetEngine())

	filter := mapToFilter(map[string]interface{}{
		"InnerIpAddress": map[string]interface{}{
			"$in": ips,
		},
	})
	filter = DefaultFilter(ctx, filter...)
	return UpdateMany(ctx, c, filter, map[string]interface{}{
		"MonitorStatus": status,
	})
}

// UpdateCmdbInfo 更新cmdb相关数据
func (h HostResource) UpdateCmdbInfo(ctx context.Context, cmdbMap map[string]entity.BkCmdbInfo) error {
	c := entity.GetHostResCollection(GetEngine())
	for instanceID, cmdbInfo := range cmdbMap {
		_, err := c.UpdateOne(ctx, bson.M{
			"InstanceID": instanceID,
			"is_delete":  0,
		}, bson.M{"$set": bson.M{
			"BkCmdb": cmdbInfo,
		}})
		if err != nil {
			return err
		}
	}
	return nil
}

// UpdateLockStatus 更新锁定状态
func (h HostResource) UpdateLockStatus(ctx context.Context, instanceIDs []string, status bool) error {
	if len(instanceIDs) == 0 {
		return fmt.Errorf("instanceIDs is null")
	}

	objs, err := getFilterWithPK(instanceIDs)
	if err != nil {
		return err
	}
	c := entity.GetHostResCollection(GetEngine())
	filter := mapToFilter(map[string]interface{}{
		"_id": map[string]interface{}{
			"$in": objs,
		},
	})
	return UpdateMany(ctx, c, filter, map[string]interface{}{
		"is_lock": status,
	})
}

// UpdateLockStatus是通过主键
// 这里通过InstanceID 更新锁定状态
func (h HostResource) UpdateLockStatusWithInstanceID(ctx context.Context, instanceIDs []string, status bool) error {
	if len(instanceIDs) == 0 {
		return fmt.Errorf("instanceIDs is null")
	}
	c := entity.GetHostResCollection(GetEngine())
	filter := DefaultFilter(ctx,
		bson.E{Key: "InstanceID", Value: map[string]interface{}{
			"$in": instanceIDs,
		}})
	return UpdateMany(ctx, c, filter, map[string]interface{}{
		"is_lock": status,
	})
}

// IsLockOrRecycle 检查是否锁定或回收
func (h HostResource) IsLockOrRecycle(ctx context.Context, instanceID []string) (bool, error) {
	c := entity.GetHostResCollection(GetEngine())
	filter := DefaultFilter(ctx,
		bson.E{Key: "$or", Value: []bson.M{
			{"is_lock": true},
			{"Recyclable": true},
		}},
		bson.E{Key: "InstanceID", Value: map[string]interface{}{
			"$in": instanceID,
		}})

	total, err := c.CountDocuments(ctx, filter)
	return total != 0, err

}

// SetRecyclable 标记为可回收
func (h HostResource) SetRecyclable(ctx context.Context, instanceID []string, recycle bool) error {
	c := entity.GetHostResCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "InstanceID", Value: bson.M{"$in": instanceID}})
	err := UpdateMany(ctx, c, filter, map[string]interface{}{"Recyclable": recycle})
	if err != nil {
		return err
	}
	return err
}

// SetRelease 设置资源为已释放
func (h HostResource) SetRelease(ctx context.Context, instanceID []string) error {
	releaseList, _ := HostResourceModel.FindMany(ctx, map[string]interface{}{
		"InstanceID": map[string]interface{}{
			"$in": instanceID,
		}},
	)
	for _, data := range releaseList {
		delRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "host",
			InstanceID:   data.InstanceID,
			InstanceName: data.InstanceName,
			Data: map[string]utils.FieldChangeList{
				"_del": {Before: string(delRaw), After: ""},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	}

	return h.UpdateMany(ctx, map[string]interface{}{
		"InstanceID": map[string]interface{}{
			"$in": instanceID,
		},
	}, map[string]interface{}{
		"is_delete":    1,
		"update_user":  permission.GetUsername(ctx),
		"deleted_time": time.Now().Unix(),
		"NeedCleanup":  false,
	})
}

// SetEipAddress 设置弹性IP
func (h HostResource) SetEipAddress(ctx context.Context, instanceID string, eip entity.InstanceEipAddress) error {
	return UpdateOne(ctx, entity.GetHostResCollection(GetEngine()), map[string]interface{}{
		"InstanceID": instanceID,
	}, map[string]interface{}{
		"EipAddress": eip,
	})
}

// Count 根据条件计数
func (h HostResource) Count(ctx context.Context, filter map[string]interface{}) (int64, error) {
	if filter == nil {
		return 0, nil
	}
	c := entity.GetHostResCollection(GetEngine())
	filters := ResReadDefaultFilter(ctx, "host", mapToFilter(filter)...)
	return c.CountDocuments(ctx, filters)
}

// UpdateAgent 更新主机agent状态
// func (h HostResource) UpdateAgent(ctx context.Context, region, innerIP, agentId, status string) error {
// 	c := entity.GetHostResCollection(GetEngine())
// 	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"RegionID": region, "innerIP": innerIP})...)
// 	_, err := c.UpdateOne(ctx, filter , map[string]interface{}{
// 		"agent_id": agentId,
// 		"agent_status": status,
// 	})
//
// 	return err
// }

// BatchUpdateAttr 批量修改属性
func (h HostResource) BatchUpdateAttr(ctx context.Context, data *entity.HostResource, tags []*cloudman.ResourceTag, tagRule string) error {
	// 仅支持更新标签,数据自动修正
	if data.InstanceID != "" {
		// 需先读取原数据
		r, _ := h.FindOne(ctx, map[string]interface{}{
			"InstanceID": data.InstanceID,
		})
		var originTags []entity.Tag
		if r != nil {
			originTags = r.Tags.Tag
		}
		var newTags []entity.Tag
		if tagRule == "add" {
			newTags = append(newTags, originTags...)
			for _, addTag := range data.Tags.Tag {
				duplicated := false
				for _, originTag := range originTags {
					if originTag.TagKey == addTag.TagKey && originTag.TagValue == addTag.TagValue {
						duplicated = true
						break
					}
				}
				if !duplicated {
					newTags = append(newTags, addTag)
				}
			}
		} else if tagRule == "del" {
			for _, originTag := range originTags {
				del := false
				for _, delTag := range data.Tags.Tag {
					if originTag.TagKey == delTag.TagKey && originTag.TagValue == delTag.TagValue {
						del = true
						break
					}
				}
				if !del {
					newTags = append(newTags, originTag)
				}
			}
		} else {
			newTags = data.Tags.Tag
		}
		data.Tags.Tag = newTags
		var updateTag []*cloudman.ResourceTag
		for _, v := range newTags {
			tagType := ""
			for _, tag := range tags {
				if tag.Key == v.TagKey && tag.Value == v.TagValue {
					tagType = tag.Type
					break
				}
			}
			updateTag = append(updateTag, &cloudman.ResourceTag{
				Key:   v.TagKey,
				Value: v.TagValue,
				Type:  tagType,
			})
		}
		// updateData, _ := mapstruct.Struct2Map(data)

		if err := ResourceBindTags.DelAll(ctx, "host", r.ID, data.IspType); err != nil {
			return err
		}

		// 先更新标签与标签绑定信息
		if len(updateTag) != 0 {
			err := ResourceBindTags.Bind(ctx, updateTag, "host", r.ID, data.IspType)
			if err != nil {
				if !errors.Is(err, mongo.WriteException{}) {
					logger.Errorf("bind tags error:", err.Error())
					return err
				}
			}
		}

		err := h.UpdateOne(ctx, map[string]interface{}{
			"InstanceID": data.InstanceID,
		}, data)
		if err != nil {
			logger.Error(err.Error())
			return err
		}
	}

	return nil
}

// HasHostPermission 检查用户是否有该主机权限
// func (h HostResource) HasHostPermission(ctx context.Context, id string) (bool, error) {
//	hostResource, err := HostResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": id})
//	if hostResource == nil {
//		return false, nil
//	}
//	accounts, err := AccountModel.FindUserAccounts(ctx)
//	if err != nil {
//		return false, nil
//	}
//	for _, v := range accounts {
//		if v == hostResource.IspID {
//			return true, nil
//		}
//	}
//	return false, nil
// }

func addDashBoardFilter(filter bson.D, req *cloudman.HostDashBoardReq) bson.D {
	if !req.DisplayRecyclable {
		filter = append(filter, bson.E{Key: "Recyclable", Value: false})
	}
	if req.IspId != "" {
		filter = append(filter, bson.E{Key: "isp_id", Value: req.IspId})
	}
	if req.RegionId != "" {
		filter = append(filter, bson.E{Key: "RegionID", Value: req.RegionId})
	}
	return filter
}

// AgentStatusTotal ops-agent状态
func (h HostResource) AgentStatusTotal(ctx context.Context, req *cloudman.HostDashBoardReq) (running, stopped int64, err error) {
	runFilter := DefaultFilter(ctx,
		// bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "agent_status", Value: 1},
	)
	stopFilter := DefaultFilter(ctx,
		// bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "agent_status", Value: bson.M{"$ne": 1}},
	)
	runFilter = addDashBoardFilter(runFilter, req)
	stopFilter = addDashBoardFilter(stopFilter, req)

	c := entity.GetHostResCollection(GetEngine())
	running, err = c.CountDocuments(ctx, runFilter)
	if err != nil {
		return
	}
	stopped, err = c.CountDocuments(ctx, stopFilter)
	if err != nil {
		return
	}
	return
}

// MonitorAgentStatusTotal 监控agent状态
func (h HostResource) MonitorAgentStatusTotal(ctx context.Context, req *cloudman.HostDashBoardReq) (running, stopped int64, err error) {
	c := entity.GetHostResCollection(GetEngine())
	runFilter := DefaultFilter(ctx,
		// bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "MonitorStatus", Value: 1},
	)
	stopFilter := DefaultFilter(ctx,
		// bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "MonitorStatus", Value: bson.M{"$ne": 1}},
	)
	runFilter = addDashBoardFilter(runFilter, req)
	stopFilter = addDashBoardFilter(stopFilter, req)

	running, err = c.CountDocuments(ctx, runFilter)
	if err != nil {
		return
	}
	stopped, err = c.CountDocuments(ctx, stopFilter)
	if err != nil {
		return
	}
	return
}

// HostStatusTotal 主机运行状态
func (h HostResource) HostStatusTotal(ctx context.Context, req *cloudman.HostDashBoardReq) (running, stopped int64, err error) {
	c := entity.GetHostResCollection(GetEngine())
	runFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "Status", Value: "Running"},
	)
	stopFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{
			Key:   "Status",
			Value: bson.M{"$ne": "Running"},
		},
	)
	runFilter = addDashBoardFilter(runFilter, req)
	stopFilter = addDashBoardFilter(stopFilter, req)

	running, err = c.CountDocuments(ctx, runFilter)
	if err != nil {
		return
	}
	stopped, err = c.CountDocuments(ctx, stopFilter)
	if err != nil {
		return
	}

	return
}

// ResStatusTotal 资源状态
func (h HostResource) ResStatusTotal(ctx context.Context, day time.Duration, req *cloudman.HostDashBoardReq) (expired, release, needCleanup int64, err error) {
	c := entity.GetHostResCollection(GetEngine())
	timeBefore := time.Now().Add(day).Unix()
	expiredFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "ExpiredTime", Value: bson.M{"$lte": timeBefore, "$ne": 0}},
	)
	releaseFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "AutoReleaseTime", Value: bson.M{"$lte": timeBefore, "$ne": 0}},
	)
	needCleanupFilter := DefaultFilter(
		ctx,
		bson.E{Key: "NeedCleanup", Value: true},
	)
	expiredFilter = addDashBoardFilter(expiredFilter, req)
	releaseFilter = addDashBoardFilter(releaseFilter, req)
	needCleanupFilter = addDashBoardFilter(needCleanupFilter, req)

	expired, err = c.CountDocuments(ctx, expiredFilter)
	if err != nil {
		return
	}
	release, err = c.CountDocuments(ctx, releaseFilter)
	if err != nil {
		return
	}
	needCleanup, err = c.CountDocuments(ctx, needCleanupFilter)
	return
}

// FindExistsInstancename ...
func (h HostResource) FindExistsInstancename(ctx context.Context, hostnames []string) ([]string, error) {
	findOption := FindManyOption{}
	findOption.Projection = map[string]interface{}{"InstanceName": 1}
	findOption.WithoutIsp = true
	res := []string{}
	// 搜索已有数据的表
	hostsExists, err := HostResourceModel.FindMany(ctx, map[string]interface{}{"MarkCleanup": bson.M{"$ne": true}, "InstanceName": map[string]interface{}{
		"$in": hostnames,
	}}, &findOption)
	if err != nil {
		return nil, err
	}
	for _, h := range hostsExists {
		res = append(res, h.InstanceName)
	}
	// 搜索创建中数据的表
	resInProcess, err := ResCreatingModel.FindMany(ctx, map[string]interface{}{"resource_type": "host", "instance_name": map[string]interface{}{
		"$in": hostnames,
	}})
	if err != nil {
		return nil, err
	}
	for _, h := range resInProcess {
		res = append(res, h.InstanceName)
	}
	return res, nil
}

// FindAllOSNames ...
func (h HostResource) FindAllOSNames(ctx context.Context) ([]string, error) {
	c := entity.GetHostResCollection(GetEngine())
	cursor, err := c.Aggregate(ctx, bson.A{bson.D{
		{"$group", bson.D{
			{"_id", "$OSName"},
			{"count", bson.D{{"$sum", 1}}}},
		},
	}})
	if err != nil {
		return nil, err
	}

	type Result struct {
		OSName string `bson:"_id"`
		Count  int    `bson:"count"`
	}

	var osNames []string
	for cursor.Next(ctx) {
		var result Result
		err := cursor.Decode(&result)
		if err != nil {
			continue
		}
		if result.OSName != "" {
			osNames = append(osNames, result.OSName)
		}
	}
	return osNames, nil
}

// FindRegexInstanceName ...
func (h HostResource) FindRegexInstanceName(ctx context.Context, regex primitive.Regex) ([]string, error) {
	findOption := FindManyOption{}
	findOption.Projection = map[string]interface{}{"InstanceName": 1}
	findOption.WithoutIsp = true
	res := []string{}
	// 搜索已有数据的表
	hosts, err := HostResourceModel.FindMany(ctx, map[string]interface{}{"InstanceName": regex}, &findOption)
	if err != nil {
		return nil, err
	}
	for _, h := range hosts {
		res = append(res, h.InstanceName)
	}
	// 搜索创建中数据的表
	resInProcess, err := ResCreatingModel.FindMany(ctx, map[string]interface{}{"resource_type": "host", "instance_name": regex})
	if err != nil {
		return nil, err
	}
	for _, h := range resInProcess {
		res = append(res, h.InstanceName)
	}
	return res, nil
}

// HoldInstancePlace ...
func (h HostResource) HoldInstancePlace(ctx context.Context, data []byte, orderID string, ispType string) (err error) {
	var req map[string]interface{}
	err = json.Unmarshal(data, &req)
	if err != nil {
		return err
	}

	hostname, _ := req["HostName"].(string)
	amountF, _ := req["Amount"].(float64)
	amount := int(amountF)
	uniqueSuffix, _ := req["UniqueSuffix"].(bool)
	serviceTreeNodes, _ := req["serviceTreeNodes"].([]interface{})

	var instanceNames []string
	if hostname != "" {
		if err := cloudutils.RuleCheck(hostname, cloudutils.Linux); err == nil {
			instanceNames, err = cloudutils.GetCloudutils(HostResourceModel).GenInstanceName(
				context.Background(),
				hostname,
				amount, uniqueSuffix, cloudutils.Linux,
			)
			if err != nil {
				return err
			}
			if instanceNames == nil {
				return fmt.Errorf("主机名重复")
			}
		}
	}

	initTreeNode := ""
	if len(serviceTreeNodes) > 0 {
		treenode, _ := serviceTreeNodes[0].(string)
		res, err := Verify(ctx, &cloudman.VerifyRequest{SearchKey: "node_admin", List: []string{treenode}})
		if err != nil {
			return fmt.Errorf("verify treenode error: %s", err.Error())
		}
		if len(res.Deny) != 0 {
			return fmt.Errorf("denied to attach treenode: %s", res.Deny)
		}
		initTreeNode = treenode
	}
	for _, heldHostname := range instanceNames {
		err := ResCreatingModel.CreateOrUpdate(ctx, &entity.ResCreating{
			OrderID:      orderID,
			ResourceType: "host",
			InstanceName: heldHostname,
			TreeNode:     initTreeNode,
			Tag:          "",
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// Verify ...
func Verify(ctx context.Context, req *cloudman.VerifyRequest) (*cloudman.VerifyResponse, error) {
	conf := cfg.GetServiceTreeConfig()
	cli, err := cfg.GetIAMCli(conf.IAMEnv)
	if err != nil {
		return nil, err
	}
	token, err := cli.CreateToken(ctx, "privilege", permission.GetUsername(ctx))
	if err != nil {
		return nil, err
	}
	reqByte, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	resBody, code, err := utils.HTTPPost(ctx, conf.ServiceTreePrefix+"service-tree/api/v1/verify", http.Header{"Authorization": []string{"Bearer " + token.Token}}, reqByte, time.Second*10)
	if err != nil {
		return nil, err
	}
	if code != 200 {
		return nil, errors.New(string(resBody))
	}
	res := struct {
		RetCode int32  `json:"retcode"`
		Message string `json:"message"`
		Data    struct {
			Pass []string `json:"pass"`
			Deny []string `json:"deny"`
		}
	}{}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		return nil, err
	}
	if res.RetCode != 0 {
		return nil, fmt.Errorf("addnode error: %s", res.Message)
	}
	return &cloudman.VerifyResponse{Pass: res.Data.Pass, Deny: res.Data.Deny}, nil
}

// ReleaseInstancePlace ...
func (h HostResource) ReleaseInstancePlace(ctx context.Context, data []byte, orderID string) error {
	err := ResCreatingModel.DeleteByOrder(ctx, orderID)
	if err != nil {
		return err
	}
	return nil
}

var hostBriefCache sync.Map

// GetBrief -
func (h HostResource) GetBrief(ctx context.Context, accountID, regionID string) (map[string]int64, error) {
	if iData, ok := hostBriefCache.Load(accountID + regionID); ok && cfg.GetSystemConfig().Env != "local" {
		data := iData.(map[string]int64)
		if time.Now().UnixNano()-data["timestamp"] <= int64(3*time.Minute) {
			return data, nil
		}
	}
	commonFilter := bson.D{
		Filter("isp_id", accountID),
	}
	if regionID != "all" && regionID != "" {
		commonFilter = append(commonFilter, Filter("RegionID", regionID))
	}
	afterSevenDayStamp := time.Now().Add(7 * 24 * time.Hour).Unix()
	beforeSevenDayStamp := time.Now().Add(-7 * 24 * time.Hour).Unix()

	// 总主机数量
	totalFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
	)
	totalFilter = append(totalFilter, commonFilter...)
	// 正在运行，含回收站
	runningFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "Status", Value: "Running"},
	)
	runningFilter = append(runningFilter, commonFilter...)
	// agent正在运行，含回收站
	agentRunFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "agent_status", Value: 1},
	)
	agentRunFilter = append(agentRunFilter, commonFilter...)

	// 7天即将过期
	expireFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "ExpiredTime", Value: bson.M{"$lte": afterSevenDayStamp, "$ne": 0}},
	)
	expireFilter = append(expireFilter, commonFilter...)
	// 7天新创建
	createFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "CreationTime", Value: bson.M{"$gte": beforeSevenDayStamp, "$ne": 0}},
	)
	createFilter = append(createFilter, commonFilter...)
	// 7天新释放
	deleteFilter := NoDelFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "deleted_time", Value: bson.M{"$gte": beforeSevenDayStamp, "$ne": 0}},
	)
	deleteFilter = append(deleteFilter, commonFilter...)
	// 回收站
	recycleFilter := DefaultFilter(ctx,
		bson.E{Key: "Recyclable", Value: true},
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
	)
	recycleFilter = append(recycleFilter, commonFilter...)
	// 已释放，待清理
	cleanupFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: true},
	)
	cleanupFilter = append(cleanupFilter, commonFilter...)

	c := entity.GetHostResCollection(GetEngine())
	totalCount, err := c.CountDocuments(ctx, totalFilter)
	if err != nil {
		return nil, err
	}
	runningCount, err := c.CountDocuments(ctx, runningFilter)
	if err != nil {
		return nil, err
	}
	agentRunningCount, err := c.CountDocuments(ctx, agentRunFilter)
	if err != nil {
		return nil, err
	}
	expireCount, err := c.CountDocuments(ctx, expireFilter)
	if err != nil {
		return nil, err
	}
	createdCount, err := c.CountDocuments(ctx, createFilter)
	if err != nil {
		return nil, err
	}
	deletedCount, err := c.CountDocuments(ctx, deleteFilter)
	if err != nil {
		return nil, err
	}
	recycledCount, err := c.CountDocuments(ctx, recycleFilter)
	if err != nil {
		return nil, err
	}
	needCleanupCount, err := c.CountDocuments(ctx, cleanupFilter)
	if err != nil {
		return nil, err
	}
	m := map[string]int64{
		"total":         totalCount,
		"running":       runningCount,
		"agent_running": agentRunningCount,
		"7day_created":  createdCount,
		"7day_deleted":  deletedCount,
		"7day_expire":   expireCount,
		"need_cleanup":  needCleanupCount,
		"recycled":      recycledCount,
		"timestamp":     time.Now().UnixNano(),
	}
	hostBriefCache.Store(accountID+regionID, m)
	return m, nil
}

var hostDailyCountCache sync.Map

// DailyCount -
type DailyCount struct {
	DayName string
	State   int
	Created int
	Deleted int
}

type dailyCountCachePack struct {
	dc []DailyCount
	ts int64
}

// AddDailyCount ...
func AddDailyCount(counts ...[]DailyCount) []DailyCount {
	m := map[string][]DailyCount{}
	var daySort []string
	for _, count := range counts {
		for _, day := range count {
			_, ok := m[day.DayName]
			if !ok {
				m[day.DayName] = []DailyCount{}
				daySort = append(daySort, day.DayName)
			}
			m[day.DayName] = append(m[day.DayName], day)
		}
	}
	var res []DailyCount
	sort.Strings(daySort)
	for i := len(daySort) - 1; i >= 0; i-- {
		dailyCount := DailyCount{DayName: daySort[i]}
		delta := 0
		for _, count := range m[daySort[i]] {
			dailyCount.State += count.State
			delta += count.Created - count.Deleted
		}
		if delta > 0 {
			dailyCount.Created = delta
		} else {
			dailyCount.Deleted = -delta
		}
		res = append(res, dailyCount)
	}
	return res
}

// GetDailyCount -
func (h HostResource) GetDailyCount(ctx context.Context, accountID, regionID string, timeBefore int32) ([]DailyCount, error) {
	cacheKey := fmt.Sprintf("%s.%s.%d", accountID, regionID, timeBefore)
	if iData, ok := hostDailyCountCache.Load(cacheKey); ok {
		data := iData.(dailyCountCachePack)
		if time.Now().UnixNano()-data.ts <= int64(3*time.Minute) && cfg.GetSystemConfig().Env != "local" {
			return data.dc, nil
		}
	}

	commonFilter := bson.D{
		Filter("isp_id", accountID),
	}
	if regionID != "all" && regionID != "" {
		commonFilter = append(commonFilter, Filter("RegionID", regionID))
	}
	// 当前时间戳
	nowStamp := time.Now().Unix()
	// 北京时间今天0点时间戳
	todayStamp := nowStamp - (nowStamp+8*3600)%86400
	// n天前北京时间0点的时间戳
	dayBeforeStamp := todayStamp - int64(timeBefore)*86400

	// 总主机数量
	totalFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
	)
	totalFilter = append(totalFilter, commonFilter...)
	// 增加的主机
	createFilter := NoDelFilter(ctx,
		bson.E{Key: "CreationTime", Value: bson.M{"$gte": dayBeforeStamp, "$ne": 0}},
	)
	createFilter = append(createFilter, commonFilter...)
	// 删除的主机
	deleteFilter := NoDelFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "deleted_time", Value: bson.M{"$gte": dayBeforeStamp, "$ne": 0}},
	)
	deleteFilter = append(deleteFilter, commonFilter...)

	c := entity.GetHostResCollection(GetEngine())
	// 当前总数
	totalCount, err := c.CountDocuments(ctx, totalFilter)
	if err != nil {
		return nil, err
	}
	createdHost := []entity.HostResource{}
	_, err = FindMany(ctx, c, createFilter, &createdHost)
	if err != nil {
		return nil, err
	}
	deletedHost := []entity.HostResource{}
	_, err = FindMany(ctx, c, deleteFilter, &deletedHost)
	if err != nil {
		return nil, err
	}

	dayData := map[int64]DailyCount{}
	for _, h := range createdHost {
		dayPass := (todayStamp + 86400 - h.CreationTime) / 86400
		s := dayData[dayPass]
		s.Created++
		dayData[dayPass] = s
	}
	for _, h := range deletedHost {
		dayPass := (todayStamp + 86400 - h.DeletedTime) / 86400
		s := dayData[dayPass]
		s.Deleted++
		dayData[dayPass] = s
	}
	result := []DailyCount{}
	for i := 0; i < int(timeBefore); i++ {
		dayName := time.Unix(todayStamp-int64(i)*86400, 0).In(time.FixedZone("", 28800)).Format("2006-01-02")
		s := dayData[int64(i)]
		result = append(result, DailyCount{
			DayName: dayName,
			State:   int(totalCount),
			Created: s.Created,
			Deleted: s.Deleted,
		})
		totalCount = totalCount - int64(s.Created) + int64(s.Deleted)
	}
	hostDailyCountCache.Store(cacheKey, dailyCountCachePack{
		dc: result,
		ts: time.Now().UnixNano(),
	})
	return result, nil
}

// HostDefaultFilterMap 默认的查询参数
func HostDefaultFilterMap(ctx context.Context, filter map[string]interface{}) map[string]interface{} {
	filter["Recyclable"] = false
	filter["NeedCleanup"] = bson.M{"$ne": true}
	filter["is_delete"] = 0
	return filter
}

// HostModelToPb ...
func HostModelToPb(ctx context.Context, resp []*entity.HostResource) ([]*cloudman.HostResDetail, error) {
	// 查询account信息
	accountIDMap := map[string]bool{}
	for _, v := range resp {
		accountIDMap[v.IspID] = true
	}
	var accountIds []string
	for a := range accountIDMap {
		if a != "" {
			accountIds = append(accountIds, a)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(ctx, accountIds)
		if err != nil {
			return nil, err
		}
	}

	// 查询securityGroup信息
	sgIDMap := map[string]bool{}
	for _, v := range resp {
		for _, sgID := range v.SecurityGroupIds.SecurityGroupID {
			sgIDMap[sgID] = true
		}
	}
	var securityGroupIDs []string
	for sgID := range sgIDMap {
		securityGroupIDs = append(securityGroupIDs, sgID)
	}
	var sgMap map[string]entity.SecurityGroup
	if len(securityGroupIDs) != 0 {
		sgMap, err = SecurityGroupModel.FindManyWithIDToMap(ctx, securityGroupIDs)
		if err != nil {
			return nil, err
		}
	}

	var list []*cloudman.HostResDetail
	for _, v := range resp {
		var tags []*cloudman.ResourceTag
		for _, tag := range v.Tags.Tag {
			tags = append(tags, &cloudman.ResourceTag{Key: tag.TagKey, Value: tag.TagValue})
		}
		var sgInfos []*cloudman.SecurityGroupInfo
		for _, sgID := range v.SecurityGroupIds.SecurityGroupID {
			sg := sgMap[sgID]
			sgInfos = append(sgInfos, &cloudman.SecurityGroupInfo{
				SecurityGroupId:   sgID,
				SecurityGroupName: sg.SecurityGroupName,
				Description:       sg.Description,
				VpcId:             sg.VpcID,
			})
		}

		detail := &cloudman.HostResDetail{
			Id:                 v.ID.Hex(),
			InstanceId:         v.InstanceID,
			InstanceType:       v.InstanceType,
			HostName:           v.HostName,
			Status:             v.Status,
			Cpu:                v.CPU,
			Memory:             v.Memory,
			CreationTime:       int32(v.CreationTime),
			Islock:             v.IsLock,
			Isp:                v.IspID,
			RegionId:           v.RegionID,
			IspType:            ac[v.IspID].AType,
			IspName:            ac[v.IspID].Name,
			PublicAddress:      v.PublicIPAddress,
			PrivateAddress:     v.InnerIPAddress,
			Tags:               tags,
			OSType:             v.OSType,
			OsName:             v.OSName,
			InstanceName:       v.InstanceName,
			EipAddress:         v.EipAddress.IPAddress,
			ExpiredTime:        int32(v.ExpiredTime),
			Description:        v.Description,
			ImageId:            v.ImageID,
			ZoneId:             v.ZoneID,
			OpsAgentId:         v.AgentID,
			OpsAgentStatus:     entity.TranslateStatus(v.AgentStatus, v.AgentUpdate),
			OpsAgentEnv:        v.AgentEnv,
			MonitorStatus:      v.MonitorStatusToString(),
			UpdatedTime:        v.UpdatedTime,
			InstanceChargeType: v.InstanceChargeType,
			Recyclable:         v.Recyclable,
			AutoReleaseTime:    v.AutoReleaseTime,
			NeedCleanup:        v.NeedCleanup,
			VpcAttr: &cloudman.InstanceVpcAttribute{
				VpcId:        v.VpcAttributes.VpcID,
				VSwitchId:    v.VpcAttributes.VSwitchID,
				NatIpAddress: v.VpcAttributes.NatIPAddress,
			},
			SecurityGroupInfos: sgInfos,
			NetworkInterface:   NetworkInterfaceToPb(v.NetworkInterfaces),
		}
		if v.BkCmdb != nil {
			detail.CmdbInfo = &cloudman.CmdbInfo{
				InstId:     v.BkCmdb.InstID,
				GameRegion: v.BkCmdb.GameRegionName,
				Process:    v.BkCmdb.ProcessName,
			}
		}
		if len(v.PublicIPAddress) == 0 {
			detail.PublicAddress = []string{v.EipAddress.IPAddress}
		}
		for _, i := range v.NetworkInterfaces.NetworkInterface {
			for _, ipv6 := range i.Ipv6Sets.Ipv6Set {
				detail.Ipv6Address = append(detail.Ipv6Address, ipv6.Ipv6Address)
			}
		}

		list = append(list, detail)
	}
	return list, nil
}

// NetworkInterfaceToPb ...
func NetworkInterfaceToPb(e entity.InstanceNetworkInterfaces) []*cloudman.InstanceNetworkInterface {
	var networkInterfaces []*cloudman.InstanceNetworkInterface
	for _, v := range e.NetworkInterface {
		var ipv6Set []string
		for _, set := range v.Ipv6Sets.Ipv6Set {
			ipv6Set = append(ipv6Set, set.Ipv6Address)
		}
		var privateIPSets []*cloudman.PrivateIpSet
		for _, set := range v.PrivateIPSets.PrivateIPSet {
			privateIPSets = append(privateIPSets, &cloudman.PrivateIpSet{
				Primary:          set.Primary,
				PrivateIpAddress: set.PrivateIPAddress,
			})
		}
		networkInterfaces = append(networkInterfaces, &cloudman.InstanceNetworkInterface{
			Type:               v.Type,
			MacAddress:         v.MacAddress,
			NetworkInterfaceId: v.NetworkInterfaceID,
			Ipv6Sets:           ipv6Set,
			PrimaryIpAddress:   v.PrimaryIPAddress,
			PrivateIpSets:      privateIPSets,
			Bandwidth:          v.Bandwidth,
		})
	}
	return networkInterfaces
}

// HostsToIPEntities ...
func HostsToIPEntities(hosts []*entity.HostResource) []*entity.IP {
	var ips []*entity.IP
	for _, host := range hosts {
		ips = append(ips, HostToIPEntities(host)...)
	}
	return ips
}

// HostToIPEntities ...
func HostToIPEntities(host *entity.HostResource) []*entity.IP {
	var ips []*entity.IP
	createType := "sync"
	if host.IspType == "custom" {
		createType = host.IspType
	}
	for _, innerIP := range host.InnerIPAddress {
		ips = append(ips, &entity.IP{
			Type:             "ip",
			CreateType:       createType,
			Address:          innerIP,
			BindInstanceType: "host",
			InstanceID:       host.InstanceID,
			Desc:             "",
			RegionID:         host.RegionID,
			IspID:            host.IspID,
			IspType:          host.IspType,
		})
	}

	for _, publicIP := range host.PublicIPAddress {
		ips = append(ips, &entity.IP{
			Type:             "ip",
			CreateType:       "sync",
			Address:          publicIP,
			BindInstanceType: "host",
			InstanceID:       host.InstanceID,
			Desc:             "",
			RegionID:         host.RegionID,
			IspID:            host.IspID,
			IspType:          host.IspType,
		})
	}
	return ips
}

func (h HostResource) GetByInstanceIDs(ctx context.Context, instanceIDs []string) ([]*entity.HostResource, error) {
	filter := map[string]interface{}{"InstanceID": map[string]interface{}{"$in": instanceIDs}}
	return h.FindMany(ctx, filter)
}

func (h HostResource) GerByRegionInstanceIDs(ctx context.Context, regionId string, instanceIDs []string) ([]*entity.HostResource, error) {
	filter := map[string]interface{}{"InstanceID": map[string]interface{}{"$in": instanceIDs}, "RegionID": regionId}
	return h.FindMany(ctx, filter)
}

// UpdateInstanceStatus 更新实例状态
func (h HostResource) UpdateInstanceStatus(ctx context.Context, instanceIDs []string, status string) error {
	if len(instanceIDs) == 0 {
		return fmt.Errorf("instanceIDs is null")
	}

	filter := mapToFilter(map[string]interface{}{
		"InstanceID": map[string]interface{}{
			"$in": instanceIDs,
		},
	})
	c := entity.GetHostResCollection(GetEngine())
	return UpdateMany(ctx, c, filter, map[string]interface{}{
		"Status": status,
	})
}
