package models

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// TaskLogModel task-log-model
var TaskLogModel = new(TaskLog)

// TaskLog task-log
type TaskLog struct {
}

// TaskDecode task-decode
type TaskDecode struct {
	ID    TaskInlineData `bson:"_id" json:"_id"`
	Count uint           `bson:"count" json:"count"`
}

// TaskInlineData task-inline-data
type TaskInlineData struct {
	DateTime string `bson:"datetime"`
	Status   uint   `bson:"status"`
}

// Create create
func (t TaskLog) Create(ctx context.Context, data *entity.TaskLog) (oid string, err error) {
	d, err := mapstruct.Struct2Map(data)
	if err != nil {
		return "", err
	}
	BeforeInsert(d)
	c := entity.GetTaskLogCollection(GetEngine())
	result, err := c.InsertOne(ctx, d)
	if err != nil {
		return "", err
	}
	if s, ok := result.InsertedID.(primitive.ObjectID); ok {
		return s.Hex(), nil
	}

	return "", fmt.Errorf("InsertedID not string")
}

// Update update
func (t TaskLog) Update(ctx context.Context, id string, data map[string]interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetTaskLogCollection(GetEngine())
	return UpdateOne(ctx, c, filter, data)
}

// Query query
func (t TaskLog) Query(ctx context.Context, params *schema.TaskLogQueryParams) ([]entity.TaskLog, uint64, error) {
	c := entity.GetTaskLogCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.TaskLogColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.TaskLog, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{new(entity.TaskLog).Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// GetFilter get filter
func (t TaskLog) GetFilter(ctx context.Context, f map[string]interface{}) (*entity.TaskLog, error) {
	filter := DefaultFilter(ctx, mapToFilter(f)...)
	c := entity.GetTaskLogResultCollection(GetEngine())

	var m entity.TaskLog
	_, err := FindOne(ctx, c, filter, &m)

	return &m, err
}

// GetLogResult 获取日志详情
func (t TaskLog) GetLogResult(ctx context.Context, params *schema.TaskLogResultQueryParams) ([]entity.TaskLogResult, uint64, error) {
	c := entity.GetTaskLogResultCollection(GetEngine())
	filter := DefaultFilter(ctx)

	if columnFilter := ColumnFilter(params.TaskLogResultColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.TaskLogResult, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{new(entity.TaskLogResult).Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}
