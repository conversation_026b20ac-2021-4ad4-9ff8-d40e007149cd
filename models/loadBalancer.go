package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// LoadBalancerModel zone-model
var LoadBalancerModel = new(LoadBalancer)

// LoadBalancer zone
type LoadBalancer struct {
}

// Query query
func (r LoadBalancer) Query(ctx context.Context, params *schema.LoadBalancerQueryParams, customFilter ...map[string]interface{}) ([]*entity.LoadBalancer, uint64, error) {
	c := entity.GetLoadBalancerCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))
	if len(customFilter) != 0 {
		filter = append(filter, mapToFilter(customFilter[0])...)
	}

	if columnFilter := ColumnFilter(params.LoadBalancerColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	filter = append(filter, primitive.E{
		Key:   "Recyclable",
		Value: bson.M{"$ne": true},
	})

	var list = make([]*entity.LoadBalancer, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.LoadBalancer{}.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	logger.Info(fmt.Sprintf("Filter:%+v", filter.Map()))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get xx
func (r LoadBalancer) Get(ctx context.Context, oid string) (*entity.LoadBalancer, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetLoadBalancerCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var zoneEntity entity.LoadBalancer
	_, err = FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// FindManyWithPK find-many
func (r LoadBalancer) FindManyWithPK(ctx context.Context, ids []string) ([]entity.LoadBalancer, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetLoadBalancerCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.LoadBalancer
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindMany find-many
func (r LoadBalancer) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.LoadBalancer, error) {
	c := entity.GetLoadBalancerCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
		if option[0].WithoutIsp {
			filters = DefaultFilter(ctx, mapToFilter(filter)...)
		}
	}

	var resource []*entity.LoadBalancer

	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// FindManyWithIDToMap to map
func (r LoadBalancer) FindManyWithIDToMap(ctx context.Context, loadBalancerIDs []string) (map[string]entity.LoadBalancer, error) {
	if len(loadBalancerIDs) == 0 {
		return nil, nil
	}

	c := entity.GetLoadBalancerCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		"LoadBalancerId", map[string]interface{}{"$in": loadBalancerIDs},
	})

	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	m := map[string]entity.LoadBalancer{}
	for cur.Next(ctx) {
		var lb entity.LoadBalancer
		if err = cur.Decode(&lb); err != nil {
			return nil, err
		}
		m[lb.LoadBalancerID] = lb
	}

	return m, nil
}

// CreateOrUpdate 创建/更新
func (r LoadBalancer) CreateOrUpdate(ctx context.Context, data *entity.LoadBalancer) (string, error) {
	action := "create"
	c := entity.GetLoadBalancerCollection(GetEngine())
	filter := map[string]interface{}{
		"LoadBalancerID": data.LoadBalancerID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.LoadBalancer
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
		if err != nil {
			return action, err
		}
	}

	if data.LoadBalancerType == "slb" {
		_, err = IPModel.CreateOrUpdate(ctx, LoadBalancerToIPEntity(data), true)
		if err != nil {
			return "", err
		}
	}

	return action, err
}

// BatchCreateOrUpdate 批量创建或更新
func (r LoadBalancer) BatchCreateOrUpdate(ctx context.Context, data []*entity.LoadBalancer) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.LoadBalancerID, v.LoadBalancerName)

		if v.LoadBalancerID != "" && v.LoadBalancerName != "" {
			action, err := r.CreateOrUpdate(ctx, v)
			if err != nil {
				errText := fmt.Sprintf("loadBalancer_id: %s, loadBalancer_name: %s, errors: %v", v.LoadBalancerID, v.LoadBalancerName, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// LoadBalancerModelToPb ...
func LoadBalancerModelToPb(ctx context.Context, lbEntities []*entity.LoadBalancer) ([]*cloudman.LoadBalancerEntity, error) {
	var accountIds []string
	for _, a := range lbEntities {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(ctx, accountIds)
		if err != nil {
			return nil, err
		}
	}

	var lbPbs []*cloudman.LoadBalancerEntity
	for _, lbEntity := range lbEntities {
		var backendServers []*cloudman.LoadBalancerBackendServer
		for _, server := range lbEntity.BackendServers {
			backendServer := &cloudman.LoadBalancerBackendServer{
				Description: server.Description,
				ServerID:    server.ServerID,
				ServerIP:    server.ServerIP,
				Type:        server.Type,
				Weight:      server.Weight,
			}
			backendServers = append(backendServers, backendServer)
		}

		var listeners []*cloudman.LoadBalancerListener
		for _, listener := range lbEntity.Listeners {
			l := &cloudman.LoadBalancerListener{
				AclId:             listener.ACLID,
				AclStatus:         listener.ACLStatus,
				AclType:           listener.ACLType,
				BackendServerPort: listener.BackendServerPort,
				Bandwidth:         listener.Bandwidth,
				Description:       listener.Description,
				ListenerPort:      listener.ListenerPort,
				ListenerProtocol:  listener.ListenerProtocol,
				LoadBalancerId:    listener.LoadBalancerID,
				Scheduler:         listener.Scheduler,
				Status:            listener.Status,
				VServerGroupId:    listener.VServerGroupID,
			}
			listeners = append(listeners, l)
		}

		var tags []*cloudman.ResourceTag
		for _, tag := range lbEntity.Tags {
			tags = append(tags, &cloudman.ResourceTag{
				Key:   tag.TagKey,
				Value: tag.TagValue,
			})
		}

		lbPb := &cloudman.LoadBalancerEntity{
			ID:                           lbEntity.ID.Hex(),
			Address:                      lbEntity.Address,
			AddressIPVersion:             lbEntity.AddressIPVersion,
			AddressType:                  lbEntity.AddressType,
			AutoReleaseTime:              lbEntity.AutoReleaseTime,
			BackendServers:               backendServers,
			Bandwidth:                    lbEntity.Bandwidth,
			CreateTime:                   lbEntity.CreateTime,
			CreateTimeStamp:              lbEntity.CreateTimeStamp,
			DeleteProtection:             lbEntity.DeleteProtection,
			EndTime:                      lbEntity.EndTime,
			EndTimeStamp:                 lbEntity.EndTimeStamp,
			InstanceChargeType:           lbEntity.InstanceChargeType,
			InternetChargeType:           lbEntity.InternetChargeType,
			Listeners:                    listeners,
			LoadBalancerID:               lbEntity.LoadBalancerID,
			LoadBalancerName:             lbEntity.LoadBalancerName,
			LoadBalancerSpec:             lbEntity.LoadBalancerSpec,
			LoadBalancerStatus:           lbEntity.LoadBalancerStatus,
			MasterZoneID:                 lbEntity.MasterZoneID,
			ModificationProtectionReason: lbEntity.ModificationProtectionReason,
			ModificationProtectionStatus: lbEntity.ModificationProtectionStatus,
			NetworkType:                  lbEntity.NetworkType,
			PayType:                      lbEntity.PayType,
			RegionID:                     lbEntity.RegionID,
			RegionIDAlias:                lbEntity.RegionIDAlias,
			RenewalCycUnit:               lbEntity.RenewalCycUnit,
			RenewalDuration:              lbEntity.RenewalDuration,
			RenewalStatus:                lbEntity.RenewalStatus,
			ResourceGroupID:              lbEntity.ResourceGroupID,
			SlaveZoneID:                  lbEntity.SlaveZoneID,
			Tags:                         tags,
			VSwitchID:                    lbEntity.VSwitchID,
			VpcID:                        lbEntity.VpcID,
			IspId:                        lbEntity.IspID,
			IspType:                      lbEntity.IspType,
			IspName:                      ac[lbEntity.IspID].Name,
			LoadBalancerType:             lbEntity.LoadBalancerType,
			NeedCleanup:                  lbEntity.NeedCleanup,
			Ipv6AddressType:              lbEntity.Ipv6AddressType,
		}
		if lbEntity.SecurityGroupIds != nil {
			lbPb.SecurityGroupIds = *lbEntity.SecurityGroupIds
		}
		lbPbs = append(lbPbs, lbPb)
	}

	return lbPbs, nil
}

// LoadBalancerToIPEntity ...
func LoadBalancerToIPEntity(lb *entity.LoadBalancer) *entity.IP {
	createType := "sync"
	if lb.IspType == "custom" {
		createType = lb.IspType
	}
	return &entity.IP{
		Type:             "ip",
		CreateType:       createType,
		Address:          lb.Address,
		BindInstanceType: "loadBalancer",
		InstanceID:       lb.LoadBalancerID,
		Desc:             "",
		RegionID:         lb.RegionID,
		IspID:            lb.IspID,
		IspType:          lb.IspType,
	}
}

func LoadBalancerModelToPbDetail(ctx context.Context, lb *entity.LoadBalancer) (*cloudman.DescribeLoadBalancerDetailRes, error) {
	ac, err := AccountModel.FindPK(ctx, lb.IspID)
	if err != nil {
		return nil, err
	}

	res := &cloudman.DescribeLoadBalancerDetailRes{
		LoadBalancerName: lb.LoadBalancerName,
		LoadBalancerID:   lb.LoadBalancerID,
		VpcID:            lb.VpcID,
		IspId:            lb.IspID,
		IspType:          lb.IspType,
		IspName:          ac.Name,
		LoadBalancerType: lb.LoadBalancerType,
		Address:          lb.Address,
		RegionID:         lb.RegionID,
		Listeners:        []*cloudman.ALBListener{},
		ZoneMappings:     []*cloudman.ZoneMappings{},
	}

	for _, listener := range lb.ALBListeners {
		res.Listeners = append(res.Listeners, &cloudman.ALBListener{
			ListenerID:           listener.ListenerID,
			ListenerProtocol:     listener.ListenerProtocol,
			ListenerPort:         listener.ListenerPort,
			DefaultServerGroupID: listener.DefaultServerGroupID,
			IdleTimeout:          listener.IdleTimeout,
			RequestTimeout:       listener.RequestTimeout,
			GzipEnabled:          listener.GzipEnabled,
			ListenerStatus:       listener.ListenerStatus,
			ListenerDescription:  listener.ListenerDescription,
		})
	}

	for _, z := range lb.ZoneMappings {
		zoneMappings := &cloudman.ZoneMappings{
			LoadBalancerAddresses: []*cloudman.LoadBalancerAddresses{},
			ZoneId:                z.ZoneId,
			VSwitchId:             z.VSwitchId,
		}
		for _, lb := range z.LoadBalancerAddresses {
			zoneMappings.LoadBalancerAddresses = append(zoneMappings.LoadBalancerAddresses, &cloudman.LoadBalancerAddresses{
				Address:         lb.Address,
				IntranetAddress: lb.IntranetAddress,
				Ipv6Address:     lb.Ipv6Address,
			})
		}
		res.ZoneMappings = append(res.ZoneMappings, zoneMappings)
	}

	return res, nil
}

func (h LoadBalancer) FindOne(ctx context.Context, filter map[string]interface{}) (*entity.LoadBalancer, error) {
	c := entity.GetLoadBalancerCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)

	cursor := c.FindOne(ctx, filters)
	err := cursor.Err()
	if err != nil {
		return nil, err
	}

	var resource *entity.LoadBalancer
	err = cursor.Decode(&resource)
	return resource, err
}

func (h LoadBalancer) GetByInstanceIDs(ctx context.Context, instanceIDs []string) ([]*entity.LoadBalancer, error) {
	filter := map[string]interface{}{"LoadBalancerID": map[string]interface{}{"$in": instanceIDs}}
	return h.FindMany(ctx, filter)
}

func (h LoadBalancer) GetByRegionInstanceIDs(ctx context.Context, regionID string, instanceIDs []string) ([]*entity.LoadBalancer, error) {
	filter := map[string]interface{}{"LoadBalancerID": map[string]interface{}{"$in": instanceIDs}, "RegionID": regionID}
	return h.FindMany(ctx, filter)
}

// HoldInstancePlace ...
func (h LoadBalancer) HoldInstancePlace(ctx context.Context, data []byte, orderID string, ispType string) error {
	var req map[string]interface{}
	err := json.Unmarshal(data, &req)
	if err != nil {
		return err
	}

	var instanceName = ""
	instanceName, _ = req["LoadBalancerName"].(string)
	var instanceNames []string
	if instanceName != "" {
		if err := cloudutils.RuleCheck(instanceName, cloudutils.Linux); err == nil {
			instanceNames, err = cloudutils.GetCloudutils(MysqlClusterResourceModel).GenInstanceName(
				context.Background(),
				instanceName,
				1, true, cloudutils.Linux,
			)
			if err != nil {
				return err
			}
			if instanceNames == nil {
				return fmt.Errorf("主机名重复")
			}
		}
	}

	initTreeNode := ""
	for _, heldInstanceName := range instanceNames {
		err := ResCreatingModel.CreateOrUpdate(ctx, &entity.ResCreating{
			OrderID:      orderID,
			ResourceType: "lb",
			InstanceName: heldInstanceName,
			TreeNode:     initTreeNode,
			Tag:          "",
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// ReleaseInstancePlace ...
func (h LoadBalancer) ReleaseInstancePlace(ctx context.Context, data []byte, orderID string) error {
	err := ResCreatingModel.DeleteByOrder(ctx, orderID)
	if err != nil {
		return err
	}
	return nil
}

// MarkCleanup 标记已释放待清理
func (h LoadBalancer) MarkCleanup(ctx context.Context, ispID, regionID string, latestUpdateVersion string) (int64, error) {
	e := entity.GetLoadBalancerCollection(GetEngine())
	_, err := e.UpdateMany(ctx,
		bson.D{
			{Key: "isp_id", Value: ispID},
			{Key: "RegionID", Value: regionID},
			{Key: "is_delete", Value: 0},
		},
		bson.D{{Key: "$set", Value: bson.M{"NeedCleanup": false}}})
	if err != nil {
		return 0, err
	}
	res, err := e.UpdateMany(ctx,
		bson.D{
			{Key: "isp_id", Value: ispID},
			{Key: "RegionID", Value: regionID},
			{Key: "UpdateVersion", Value: bson.D{{Key: "$ne", Value: latestUpdateVersion}}},
			{Key: "is_delete", Value: 0},
		},
		bson.D{{Key: "$set", Value: bson.M{"NeedCleanup": true}}})
	if err != nil {
		return 0, err
	}
	return res.MatchedCount, nil
}

func (h LoadBalancer) Cleanup(ctx context.Context, ids []string) error {
	e := entity.GetLoadBalancerCollection(GetEngine())
	_, err := e.DeleteMany(ctx, bson.D{
		{Key: "NeedCleanup", Value: true},
		{Key: "LoadBalancerID", Value: bson.D{{Key: "$in", Value: ids}}},
	})
	return err
}

// Count 统计符合条件的数量
func (h LoadBalancer) Count(ctx context.Context, filter map[string]interface{}) (int64, error) {
	if filter == nil {
		return 0, nil
	}
	c := entity.GetLoadBalancerCollection(GetEngine())
	return c.CountDocuments(ctx, filter)
}

// FindExpireResource 寻找过期资源,仅返回instanceID
func (h LoadBalancer) FindExpireResource(ctx context.Context, filter map[string]interface{}) (instanceID []string, err error) {
	return nil, err
}

// FindExpireResourceInfo 寻找过期资源,返回资源详情interface
func (h LoadBalancer) FindExpireResourceInfo(ctx context.Context, filter map[string]interface{}) (map[string]interface{}, error) {
	return nil, nil
}

// SetRecyclable 设置资源为可回收
func (h LoadBalancer) SetRecyclable(ctx context.Context, instanceID []string, recycle bool) error {
	c := entity.GetLoadBalancerCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "LoadBalancerID", Value: bson.M{"$in": instanceID}})
	err := UpdateMany(ctx, c, filter, map[string]interface{}{"Recyclable": recycle})
	if err != nil {
		return err
	}
	return err
}

// UpdateMany 更新多条数据
func (h LoadBalancer) UpdateMany(ctx context.Context, filter map[string]interface{}, doc interface{}) error {
	c := entity.GetLoadBalancerCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	return UpdateMany(ctx, c, filters, doc)
}

// SetRelease 设置资源为已释放
func (h LoadBalancer) SetRelease(ctx context.Context, instanceID []string) error {
	return h.UpdateMany(ctx, map[string]interface{}{
		"LoadBalancerID": map[string]interface{}{
			"$in": instanceID,
		},
	}, map[string]interface{}{
		"is_delete":   1,
		"update_user": permission.GetUsername(ctx),
	})
}
