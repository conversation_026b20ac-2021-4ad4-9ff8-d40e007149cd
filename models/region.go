package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// RegionModel zone-model
var RegionModel = new(Region)

// Region zone
type Region struct {
}

// FindWithRid 通过region-id获取详情
func (r Region) FindWithRid(ctx context.Context, ispType, regionID string) (*entity.Region, error) {
	c := entity.GetRegionCollection(GetEngine())
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{
		"isp_type":  ispType,
		"region_id": regionID,
	})...)

	var region entity.Region
	_, err := FindOne(ctx, c, filter, &region)
	return &region, err
}

// Query query
func (r Region) Query(ctx context.Context, params *schema.RegionQueryParams) ([]entity.Region, uint64, error) {
	c := entity.GetRegionCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.RegionColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.Region, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.Region{}.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	logger.Info(fmt.Sprintf("Filter:%+v", filter.Map()))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get get
func (r Region) Get(ctx context.Context, oid string) (*entity.Region, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetRegionCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var zoneEntity entity.Region
	_, err = FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// FindManyWithPK find-many
func (r Region) FindManyWithPK(ctx context.Context, ids []string) ([]entity.Region, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetRegionCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.Region
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindOrCreate 不存在则创建
func (r Region) FindOrCreate(ctx context.Context, data *entity.Region) error {
	if data.IspType == "" {
		return errors.New("region is nil")
	}
	c := entity.GetRegionCollection(GetEngine())
	err := c.FindOne(ctx, bson.D{{"region_id", data.RegionID}, {"isp_type", data.IspType}}).Err()
	data.CreatedTime = time.Now().Unix()
	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		err = Insert(ctx, c, data)
	}
	return err
}

// BatchFindOrCreate 批量更新或创建
func (r Region) BatchFindOrCreate(ctx context.Context, ispType string, data []*entity.Region) error {
	for _, v := range data {
		err := r.FindOrCreate(ctx, v)
		if err != nil {
			return err
		}
	}

	return nil
}
