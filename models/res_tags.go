package models

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// ResourceTags resource-tag-model
var ResourceTags = new(resourceTags)

// resourceTags resource-tags
type resourceTags struct {
}

// Query query
func (r resourceTags) Query(ctx context.Context, params *schema.TagsQueryParams) ([]entity.ResTags, uint64, error) {
	c := entity.GetResTagsCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.TagsColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.ResTags, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.ResTagsEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get get
func (r resourceTags) Get(ctx context.Context, oid string) (*entity.ResTags, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetResTagsCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var resTagsEntity entity.ResTags
	_, err = FindOne(ctx, c, filter, &resTagsEntity)
	return &resTagsEntity, err
}

// GetByKeyValue ...
func (r resourceTags) GetByKeyValue(ctx context.Context, key string, value string) (*entity.ResTags, error) {
	c := entity.GetResTagsCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("key", key), Filter("value", value))
	var resTagsEntity entity.ResTags
	_, err := FindOne(ctx, c, filter, &resTagsEntity)
	return &resTagsEntity, err
}

// Create create
func (r resourceTags) Create(ctx context.Context, data *entity.ResTags) error {
	// t := time.Now()
	data.ID = primitive.NewObjectID()
	data.CreatedTime = time.Now().Unix()
	c := entity.GetResTagsCollection(GetEngine())

	err := c.FindOne(ctx, DefaultFilter(ctx, bson.E{Key: "key", Value: data.Key}, bson.E{Key: "value", Value: data.Value})).Err()
	if err == mongo.ErrNoDocuments {
		return Insert(ctx, c, data)
	}
	return errors.New("不能创建相同key、value的标签")
}

// Update xxx
func (r resourceTags) Update(ctx context.Context, id string, updateData interface{}, queryOptions ...map[string]interface{}) error {
	data, err := mapstruct.Struct2Map(updateData)
	if err != nil {
		return err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	if len(queryOptions) != 0 {
		filter = append(filter, mapToFilter(queryOptions[0])...)
	}

	c := entity.GetResTagsCollection(GetEngine())

	var resTagsEntity entity.ResTags
	_, err = FindOne(ctx, c, DefaultFilter(ctx, bson.E{Key: "key", Value: data["Key"]}, bson.E{Key: "value", Value: data["Value"]}), &resTagsEntity)
	if err == mongo.ErrNoDocuments || objectID == resTagsEntity.ID {
		return UpdateMany(ctx, c, filter, data)
	}
	return errors.New("已存在相同key和value的标签")

}
