package models

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

const (
	// HostResTpl 主机资源模板
	HostResTpl = "host"
	// MysqlResTpl 数据库资源模板
	MysqlResTpl = "mysql"
	// RedisResTpl Redis资源模板
	RedisResTpl = "redis"
)

// ResourceTemplate resource-template-model
var ResourceTemplate = new(resourceTemplate)

// resourceTemplate resource-template
type resourceTemplate struct {
}

// Query query
func (r resourceTemplate) Query(ctx context.Context, params *schema.ResTemplateQueryParams) ([]entity.ResTemplate, uint64, error) {
	c := entity.GetResTemplateCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.ResTemplateColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.ResTemplate, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.ResTemplateEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get get
func (r resourceTemplate) Get(ctx context.Context, oid string) (*entity.ResTemplate, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetResTemplateCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var resGroupEntity entity.ResTemplate
	_, err = FindOne(ctx, c, filter, &resGroupEntity)
	return &resGroupEntity, err
}

// Create create
func (r resourceTemplate) Create(ctx context.Context, data *entity.ResTemplate) error {
	// t := time.Now()
	data.ID = primitive.NewObjectID()
	data.CreatedTime = time.Now().Unix()
	c := entity.GetResTemplateCollection(GetEngine())

	return Insert(ctx, c, data)
}

// Update xxx
func (r resourceTemplate) Update(ctx context.Context, id string, updateData interface{}, queryOptions ...map[string]interface{}) error {
	data, err := mapstruct.Struct2Map(updateData)
	if err != nil {
		return err
	}

	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	if len(queryOptions) != 0 {
		filter = append(filter, mapToFilter(queryOptions[0])...)
	}

	c := entity.GetResTemplateCollection(GetEngine())
	return UpdateMany(ctx, c, filter, data)
}

func ResourceTemplateToPb(e *entity.ResTemplate) *cloudman.ResTemCreateReq {
	res := &cloudman.ResTemCreateReq{
		Id:   e.ID.Hex(),
		Name: e.Name,
		Desc: e.Desc,
		Info: e.Info,
		Isp:  e.ISP,
		Type: e.Type,
		Rid:  e.RId,
	}
	if e.InitOption != nil {
		res.InitOption = &cloudman.InitOption{
			Lock:           e.InitOption.Lock,
			EnableInit:     e.InitOption.EnableInit,
			JumpserverHost: e.InitOption.JumpserverHost,
			PipelineId:     e.InitOption.PipelineId,
			BuiltinParams:  e.InitOption.BuiltinParams,
			PipelineParams: []*cloudman.PipelineParam{},
		}
		for _, p := range e.InitOption.PipelineParams {
			res.InitOption.PipelineParams = append(res.InitOption.PipelineParams, &cloudman.PipelineParam{
				Key:              p.Key,
				Value:            p.Value,
				EnableGzipBase64: p.EnableGzipBase64,
			})
		}
	}
	return res
}

func PbToResourceTemplate(req *cloudman.ResTemCreateReq) (e *entity.ResTemplate) {
	data := &entity.ResTemplate{
		Name: req.Name,
		Desc: req.Desc,
		Type: req.Type,
		ISP:  req.Isp,
		Info: req.Info,
		RId:  req.Rid,
	}
	if req.InitOption != nil {
		data.InitOption = &entity.InitOption{
			Lock:           req.InitOption.Lock,
			EnableInit:     req.InitOption.EnableInit,
			JumpserverHost: req.InitOption.JumpserverHost,
			PipelineId:     req.InitOption.PipelineId,
			BuiltinParams:  req.InitOption.BuiltinParams,
			PipelineParams: []*entity.PipelineParam{},
		}
		for _, p := range req.InitOption.PipelineParams {
			data.InitOption.PipelineParams = append(data.InitOption.PipelineParams, &entity.PipelineParam{
				Key:              p.Key,
				Value:            p.Value,
				EnableGzipBase64: p.EnableGzipBase64,
			})
		}
	}
	return data
}
