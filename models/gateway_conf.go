package models

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// GatewayConfModel zone-model
var GatewayConfModel = new(GatewayConf)

// GatewayConf zone
type GatewayConf struct {
}

// Get 获取配置
func (g GatewayConf) Get(ctx context.Context) (*entity.GatewayConf, error) {
	c := entity.GetGatewayConfCollection(GetEngine())
	filter := DefaultFilter(ctx)

	var conf entity.GatewayConf
	ok, _ := FindOne(ctx, c, filter, &conf)
	if !ok {
		return nil, errors.New("未发现相关配置")
	}

	return &conf, nil
}

// CreateOrUpdate 创建或更新
func (g GatewayConf) CreateOrUpdate(ctx context.Context, conf *entity.GatewayConf) error {
	c := entity.GetGatewayConfCollection(GetEngine())
	oldConf, err := g.Get(ctx)
	conf.ID = primitive.NewObjectID()
	if err != nil {
		return Insert(ctx, c, conf)
	}

	return UpdateOne(ctx, c, mapToFilter(map[string]interface{}{"_id": oldConf.ID}), map[string]interface{}{
		"public_protoc":   conf.PublicProtoc,
		"public_address":  conf.PublicAddress,
		"private_protoc":  conf.PrivateProtoc,
		"private_address": conf.PrivateAddress,
	})
}
