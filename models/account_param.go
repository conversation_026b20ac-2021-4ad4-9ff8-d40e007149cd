package models

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// AccountParamModel account-param model
var AccountParamModel = new(AccountParam)

// AccountParam account-param
type AccountParam struct{}

// Query 查询列表
func (a AccountParam) Query(ctx context.Context, params *schema.AccountParamQueryParams) ([]entity.AccountParam, uint64, error) {
	c := entity.GetAccountParamCollection(GetEngine())
	filter := DefaultFilter(ctx)

	if columnFilter := ColumnFilter(params.AccountParamColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.AccountParam, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{new(entity.AccountParam).Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, err
}

// Get 获取详情
func (a AccountParam) Get(ctx context.Context, id string) (*entity.AccountParam, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	c := entity.GetAccountParamCollection(GetEngine())
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)

	var accountParamEntity entity.AccountParam
	_, err = FindOne(ctx, c, filter, &accountParamEntity)
	if err != nil {
		return nil, err
	}

	return &accountParamEntity, err
}

// Upset Upset
func (a AccountParam) Upset(ctx context.Context, accountParam *entity.AccountParam) (string, error) {
	d, err := mapstruct.Struct2Map(accountParam)
	if err != nil {
		return "", err
	}

	BeforeInsert(d)
	c := entity.GetAccountParamCollection(GetEngine())
	upSet := true
	fr := &options.FindOneAndReplaceOptions{Upsert: &upSet}
	res := c.FindOneAndReplace(ctx, bson.M{
		"bind_account_id": accountParam.BindAccountID,
		"param_type":      accountParam.ParamType,
		"bind_region":     accountParam.BindRegionID,
		"is_delete":       0}, accountParam, fr,
	)
	if err != nil {
		return "", res.Err()
	}
	return "", nil
}

// Create 创建
func (a AccountParam) Create(ctx context.Context, accountParam *entity.AccountParam) (string, error) {
	accountParam.ID = primitive.NewObjectID()
	d, err := mapstruct.Struct2Map(accountParam)
	if err != nil {
		return "", err
	}

	BeforeInsert(d)
	// 判断是否已存在相同模板
	c := entity.GetAccountParamCollection(GetEngine())
	total, err := c.CountDocuments(ctx, bson.M{
		"bind_account_id": accountParam.BindAccountID,
		"param_type":      accountParam.ParamType,
		"bind_region_id":  accountParam.BindRegionID,
		"is_delete":       0})
	if err != nil || total != 0 {
		return "", fmt.Errorf("创建失败,模板已存在")
	}
	result, err := c.InsertOne(ctx, d)
	if err != nil {
		return "", err
	}
	if s, ok := result.InsertedID.(primitive.ObjectID); ok {
		return s.Hex(), nil
	}
	return "", nil
}

// Update update
func (a AccountParam) Update(ctx context.Context, id string, data map[string]interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetAccountParamCollection(GetEngine())
	return UpdateMany(ctx, c, filter, data)
}

// Delete delete
func (a AccountParam) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetAccountParamCollection(GetEngine())

	return Delete(ctx, c, filter)
}

// GetHostParams 获取主机参数
func (a AccountParam) GetHostParams(ctx context.Context, ispID string, rid string) (*AccountParamsModel, error) {
	c := entity.GetAccountParamCollection(GetEngine())
	filter := DefaultFilter(ctx)
	filter = append(filter,
		bson.E{Key: "bind_account_id", Value: ispID},
		bson.E{Key: "param_type", Value: "host"},
		bson.E{Key: "bind_region_id", Value: rid},
	)
	var data entity.AccountParam
	ok, err := FindOne(ctx, c, filter, &data)
	if err != nil || !ok {
		return nil, err
	}

	var mData *schema.APHostTplParams
	err = json.Unmarshal([]byte(data.Params), &mData)
	return &AccountParamsModel{params: *mData}, nil
}

// AccountParamsModel 云厂商参数model
type AccountParamsModel struct {
	// ispID string
	// rid   string
	params schema.APHostTplParams
}

// DescribePrice 获取主机单价
func (a AccountParamsModel) DescribePrice(ctx context.Context, resType string, input []byte) (*cloudman.SalePrice, error) {
	return &cloudman.SalePrice{Price: 0}, nil
}

// DescribeAutoSnapshotPolicyEx 磁盘自动快照策略
func (a AccountParamsModel) DescribeAutoSnapshotPolicyEx(ctx context.Context, input *common.PageCommon) (*cloudman.AutoSnapshotPolicyEXResp, error) {
	// TODO implement me
	return nil, nil
}

// DescribeChargeType 获取付费类型
func (a AccountParamsModel) DescribeChargeType(ctx context.Context) (*cloudman.ChargeTypesResp, error) {
	if len(a.params.InstanceChargeType) == 0 {
		return nil, nil
	}
	var types = map[string]string{"PrePaid": "包年包月", "PostPaid": "按量付费"}
	var list []*cloudman.ValueLabelResp
	for _, v := range a.params.InstanceChargeType {
		label, ok := types[v]
		if !ok {
			continue
		}
		list = append(list, &cloudman.ValueLabelResp{
			Value: v,
			Label: label,
		})
	}

	return &cloudman.ChargeTypesResp{List: list, Source: cloudman.Datasource_custom}, nil
}

// DescribeImages 获取镜像列表
func (a AccountParamsModel) DescribeImages(ctx context.Context, input *common.DescribeImagesInput) (*cloudman.ImagesResp, error) {
	if len(a.params.Images) == 0 {
		return nil, nil
	}
	var list []*cloudman.ImageResp
	for _, v := range a.params.Images {
		if input.OsType != "" {
			if strings.Contains(strings.ToLower(input.GetOSType()), strings.ToLower(v.OSType)) {
				list = append(list, &cloudman.ImageResp{
					OsName:    v.OSName,
					ImageId:   v.ID,
					ImageName: v.Name,
					OsType:    v.OSType,
					IsPublic:  v.IsPublic,
					// TODO: 缺少镜像类型: 公共镜像/自定义镜像
				})
			}
		} else {
			list = append(list, &cloudman.ImageResp{
				OsName:    v.OSName,
				ImageId:   v.ID,
				ImageName: v.Name,
				OsType:    v.OSType,
				IsPublic:  v.IsPublic,
				// TODO: 缺少镜像类型: 公共镜像/自定义镜像
			})
		}
	}

	return &cloudman.ImagesResp{List: list, Source: cloudman.Datasource_custom}, nil
}

// DescribeInstanceTypes 获取实例类型
func (a AccountParamsModel) DescribeInstanceTypes(ctx context.Context, input *common.DescribeInstanceTypesInput) (*cloudman.InstanceTypesResp, error) {
	if len(a.params.Instances) == 0 {
		return nil, nil
	}
	var list []*cloudman.InstanceTypeResp
	for _, v := range a.params.Instances {
		list = append(list, &cloudman.InstanceTypeResp{
			MemorySize:         v.MemorySize,
			CPUCoreCount:       v.CPUCoreCount,
			InstanceTypeFamily: v.InstanceTypeFamily,
			InstanceTypeID:     v.InstanceTypeID,
		})
	}

	return &cloudman.InstanceTypesResp{List: list, Source: cloudman.Datasource_custom}, nil
}

// DescribeKeyPairs 获取密钥对
func (a AccountParamsModel) DescribeKeyPairs(ctx context.Context, input *common.DescribeKeyPairsInput) (*cloudman.KeyPairResp, error) {
	return nil, nil
}

// DescribeVolumeTypes 获取磁盘类型
func (a AccountParamsModel) DescribeVolumeTypes(ctx context.Context, input *common.DescribeVolumeTypesInput) (*cloudman.VolumeTypesResp, error) {
	mData := a.params

	systemDisk := &cloudman.VolumeType{
		CateGoryOptions: []*cloudman.ValueLabelResp{},
		MinSize:         mData.SystemDisk.MinSize,
		MaxSize:         mData.SystemDisk.MaxSize,
		MinCount:        1,
		MaxCount:        1,
	}
	for _, v := range mData.SystemDisk.CateGoryOptions {
		systemDisk.CateGoryOptions = append(systemDisk.CateGoryOptions, &cloudman.ValueLabelResp{
			Value: v.Value,
			Label: v.Label,
		})
	}
	dataDisk := &cloudman.VolumeType{
		CateGoryOptions: nil,
		MinSize:         mData.SystemDisk.MinSize,
		MaxSize:         mData.SystemDisk.MaxSize,
		MinCount:        mData.DataDisk.MinCount,
		MaxCount:        mData.DataDisk.MaxCount,
	}
	for _, v := range mData.DataDisk.CateGoryOptions {
		dataDisk.CateGoryOptions = append(dataDisk.CateGoryOptions, &cloudman.ValueLabelResp{
			Value: v.Value,
			Label: v.Label,
		})
	}
	return &cloudman.VolumeTypesResp{SystemDisk: systemDisk, DataDisk: dataDisk, Source: cloudman.Datasource_custom}, nil
}

// DescribeNetworks 获取vpc信息
func (a AccountParamsModel) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	if len(a.params.NetWorks) == 0 {
		return nil, nil
	}
	mData := a.params

	var list []*cloudman.NetworkInfo
	for _, v := range mData.NetWorks {
		list = append(list, &cloudman.NetworkInfo{
			Name:        v.Name,
			VpcId:       v.ID,
			Status:      v.Status,
			Description: v.Description,
			IsDefault:   v.IsDefault,
		})
	}

	return &cloudman.NetworkResp{List: list, Source: cloudman.Datasource_custom}, nil
}

// DescribeSubnets 获取子网/交换机
func (a AccountParamsModel) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	if len(a.params.NetWorks) == 0 {
		return nil, nil
	}
	mData := a.params

	var list []*cloudman.VSwitchInfo
	for _, v := range mData.NetWorks {
		if input.VpcID == v.ID {
			for _, val := range v.VSwitches {
				list = append(list, &cloudman.VSwitchInfo{
					Status:      val.Status,
					VSwitchId:   val.ID,
					VSwitchName: val.Name,
				})
			}
		}
	}

	return &cloudman.VSwitchResp{List: list, Source: cloudman.Datasource_custom}, nil
}

// DescribeSecurityGroups 获取安全组
func (a AccountParamsModel) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	if len(a.params.NetWorks) == 0 {
		return nil, nil
	}
	mData := a.params
	var list []*cloudman.SecurityGroupInfo
	for _, v := range mData.NetWorks {
		if input.VpcID == v.ID {
			for _, val := range v.SecurityGroups {
				list = append(list, &cloudman.SecurityGroupInfo{
					Description:       val.Description,
					VpcId:             val.VpcID,
					SecurityGroupId:   val.ID,
					SecurityGroupName: val.Name,
				})
			}
		}
	}
	return &cloudman.SecurityGroupResp{List: list, Source: cloudman.Datasource_custom}, nil
}
