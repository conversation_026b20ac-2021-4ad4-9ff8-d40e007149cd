package models

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// SyncTaskModel sync task model
var SyncTaskModel = new(SyncTask)

// SyncTask sync task
type SyncTask struct {
}

// Get get
func (s SyncTask) Get(ctx context.Context, id string) (*entity.SyncTask, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	c := entity.GetSyncTaskCollection(GetEngine())
	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)

	var syncTaskEntity entity.SyncTask
	_, err = FindOne(ctx, c, filter, &syncTaskEntity)
	if err != nil {
		return nil, err
	}

	return &syncTaskEntity, err
}

// Update update
func (s SyncTask) Update(ctx context.Context, id string, data map[string]interface{}) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	BeforeUpdate(data)

	filter := DefaultFilter(ctx, mapToFilter(map[string]interface{}{"_id": objectID})...)
	c := entity.GetSyncTaskCollection(GetEngine())
	return UpdateOne(ctx, c, filter, data)
}

// UpdateMany update many
func (s SyncTask) UpdateMany(ctx context.Context, cond, data map[string]interface{}) ([]string, error) {
	BeforeUpdate(data)
	filter := DefaultFilter(ctx, mapToFilter(cond)...)
	c := entity.GetSyncTaskCollection(GetEngine())

	result, err := UpdateManyWithResult(ctx, c, filter, data)
	if err != nil {
		return nil, err
	}

	res := make([]string, 0)
	if objs, ok := result.UpsertedID.([]primitive.ObjectID); ok {
		for _, v := range objs {
			res = append(res, v.String())
		}
	}

	return res, nil
}

// Query query
func (s SyncTask) Query(ctx context.Context, params *schema.SyncTaskQueryParams) ([]entity.SyncTask, uint64, error) {
	c := entity.GetSyncTaskCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.SyncTaskColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]entity.SyncTask, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{new(entity.SyncTask).Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Create create
func (s SyncTask) Create(ctx context.Context, data *entity.SyncTask) (string, error) {
	d, err := mapstruct.Struct2Map(data)
	if err != nil {
		return "", err
	}

	BeforeInsert(d)
	c := entity.GetSyncTaskCollection(GetEngine())
	result, err := c.InsertOne(ctx, d)
	if err != nil {
		return "", err
	}
	if s, ok := result.InsertedID.(primitive.ObjectID); ok {
		return s.Hex(), nil
	}
	return "", nil
}

// FindResourceTask ...
func (s SyncTask) FindResourceTask(ctx context.Context, taskType string, accountID string, regionID string) ([]entity.SyncTask, error) {
	c := entity.GetSyncTaskCollection(GetEngine())
	var list []entity.SyncTask
	filter := DefaultFilter(ctx,
		bson.E{Key: "task_type", Value: taskType},
		bson.E{Key: "account_id", Value: accountID},
		bson.E{Key: "bind_region_source_id", Value: regionID},
	)
	_, err := FindMany(ctx, c, filter, &list)

	return list, err
}

// FindByAccountID ...
func (s SyncTask) FindByAccountID(ctx context.Context, accountID string) ([]entity.SyncTask, error) {
	c := entity.GetSyncTaskCollection(GetEngine())
	var list []entity.SyncTask
	filter := DefaultFilter(ctx,
		bson.E{Key: "account_id", Value: accountID},
	)
	_, err := FindMany(ctx, c, filter, &list)
	return list, err
}
