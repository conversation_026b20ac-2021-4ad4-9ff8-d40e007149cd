package models

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// DomainModel zone-model
var DomainModel = new(Domain)

// Domain zone
type Domain struct {
}

// Query query
func (r Domain) Query(ctx context.Context, params *schema.DomainQueryParams) ([]*entity.Domain, uint64, error) {
	c := entity.GetDomainCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))

	if columnFilter := ColumnFilter(params.DomainColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	var list = make([]*entity.Domain, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.Domain{}.Ordering()} // 加入默认排序
	OrderFields = append(OrderFields, params.BuildToOrderFields()...)

	findOption.SetSort(ParseOrder(OrderFields))

	logger.Info(fmt.Sprintf("Filter:%+v", filter.Map()))

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter, &list, findOption)
	if err != nil {
		return nil, 0, err
	}

	return list, pr.Total, nil
}

// Get xx
func (r Domain) Get(ctx context.Context, oid string) (*entity.Domain, error) {
	objectID, err := primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, err
	}

	c := entity.GetDomainCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("_id", objectID))
	var zoneEntity entity.Domain
	_, err = FindOne(ctx, c, filter, &zoneEntity)
	return &zoneEntity, err
}

// GetByDomainID xx
func (r Domain) GetByDomainID(ctx context.Context, id int) (*entity.Domain, error) {
	c := entity.GetDomainCollection(GetEngine())
	filter := DefaultFilter(ctx)

	filter = append(filter, Filter("DomainId", id))
	var domainEntity entity.Domain
	_, err := FindOne(ctx, c, filter, &domainEntity)
	return &domainEntity, err
}

// FindManyWithPK find-many
func (r Domain) FindManyWithPK(ctx context.Context, ids []string) ([]entity.Domain, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	obj, err := getFilterWithPK(ids)
	if err != nil {
		return nil, err
	}
	c := entity.GetDomainCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "_id", Value: map[string]interface{}{"$in": obj},
	})

	var list []entity.Domain
	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cur.All(ctx, &list)
	return list, err
}

// FindMany find-many
func (r Domain) FindMany(ctx context.Context, filter map[string]interface{}, option ...*FindManyOption) ([]*entity.Domain, error) {
	c := entity.GetDomainCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	opt := &options.FindOptions{}
	if len(option) != 0 {
		opt = &option[0].FindOptions
		if option[0].WithoutIsp {
			filters = DefaultFilter(ctx, mapToFilter(filter)...)
		}
	}

	var resource []*entity.Domain

	_, err := FindMany(ctx, c, filters, &resource, opt)
	return resource, err
}

// FindManyWithIDToMap to map
func (r Domain) FindManyWithIDToMap(ctx context.Context, domainIDs []string) (map[int]entity.Domain, error) {
	if len(domainIDs) == 0 {
		return nil, nil
	}

	c := entity.GetDomainCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "DomainId", Value: map[string]interface{}{"$in": domainIDs},
	})

	cur, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	m := map[int]entity.Domain{}
	for cur.Next(ctx) {
		var domain entity.Domain
		if err = cur.Decode(&domain); err != nil {
			return nil, err
		}
		m[domain.DomainID] = domain
	}

	return m, nil
}

// CreateOrUpdate 创建/更新
func (r Domain) CreateOrUpdate(ctx context.Context, data *entity.Domain) (string, error) {
	// 标签特殊处理: 从标签model中获取标签并完成绑定
	action := "create"
	c := entity.GetDomainCollection(GetEngine())
	filter := map[string]interface{}{
		"DomainId": data.DomainID,
	}

	result := c.FindOne(ctx, filter)
	err := result.Err()
	if err == nil {
		var m entity.Domain
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}
		action = "update"
		data.ID = m.ID
		data.UpdatedTime = time.Now().Unix()
		err = UpdateOne(ctx, c, filter, data)
	}

	if err == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		err = Insert(ctx, c, data)
	}

	return action, err
}

// BatchCreateOrUpdate 批量创建或更新
func (r Domain) BatchCreateOrUpdate(ctx context.Context, data []*entity.Domain) (*BatchResult, error) {
	result := &BatchResult{}

	for _, v := range data {
		dbFlag := fmt.Sprintf("%d:%s", v.DomainID, v.Name)

		if v.DomainID != 0 && v.Name != "" {
			action, err := r.CreateOrUpdate(ctx, v)
			if err != nil {
				errText := fmt.Sprintf("domain_id: %d, name: %s, errors: %v", v.DomainID, v.Name, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "update" {
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else {
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	var err error
	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// DomainModelToPb ...
func DomainModelToPb(ctx context.Context, domainEntities []*entity.Domain) ([]*cloudman.DomainEntity, error) {
	var accountIds []string
	for _, a := range domainEntities {
		if a.IspID != "" {
			accountIds = append(accountIds, a.IspID)
		}
	}
	var ac map[string]entity.Account
	var err error
	if len(accountIds) != 0 {
		ac, err = AccountModel.FindManyWithPkToMap(ctx, accountIds)
		if err != nil {
			return nil, err
		}
	}

	var domainProtos []*cloudman.DomainEntity
	for _, domainEntity := range domainEntities {
		var tags []*cloudman.ResourceTag
		for _, tag := range domainEntity.TagList {
			tags = append(tags, &cloudman.ResourceTag{
				Key:   tag.TagKey,
				Value: tag.TagValue,
			})
		}

		var recordList []*cloudman.Record
		for _, record := range domainEntity.RecordList {
			recordList = append(recordList, &cloudman.Record{
				RecordId:      record.RecordID,
				Value:         record.Value,
				Status:        record.Status,
				UpdatedOn:     record.UpdatedOn,
				Name:          record.Name,
				Line:          record.Line,
				LineId:        record.LineID,
				Type:          record.Type,
				Weight:        record.Weight,
				MonitorStatus: record.MonitorStatus,
				Remark:        record.Remark,
				TTL:           record.TTL,
				MX:            record.MX,
				DefaultNS:     record.DefaultNS,
			})
		}

		domainProtos = append(domainProtos, &cloudman.DomainEntity{
			DomainId:         int32(domainEntity.DomainID),
			Name:             domainEntity.Name,
			Status:           domainEntity.Status,
			TTL:              int32(domainEntity.TTL),
			CNAMESpeedup:     domainEntity.CNAMESpeedup,
			DNSStatus:        domainEntity.DNSStatus,
			Grade:            domainEntity.Grade,
			GroupId:          int32(domainEntity.GroupID),
			SearchEnginePush: domainEntity.SearchEnginePush,
			Remark:           domainEntity.Remark,
			Punycode:         domainEntity.Punycode,
			EffectiveDNS:     domainEntity.EffectiveDNS,
			GradeLevel:       int32(domainEntity.GradeLevel),
			GradeTitle:       domainEntity.GradeTitle,
			IsVip:            domainEntity.IsVip,
			VipStartAt:       domainEntity.VipStartAt,
			VipEndAt:         domainEntity.VipEndAt,
			VipAutoRenew:     domainEntity.VipAutoRenew,
			RecordCount:      int32(domainEntity.RecordCount),
			CreatedOn:        domainEntity.CreatedOn,
			UpdatedOn:        domainEntity.UpdatedOn,
			Owner:            domainEntity.Owner,
			TagList:          tags,
			IspId:            domainEntity.IspID,
			IspType:          domainEntity.IspType,
			IspName:          ac[domainEntity.IspID].Name,
			RecordList:       recordList,
			RegionId:         domainEntity.RegionID,
		})
	}
	return domainProtos, nil
}
