package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// MysqlDatabaseResourceModel 主机资源model
var MysqlDatabaseResourceModel = new(MysqlDatabaseResource)

// MysqlDatabaseResource 主机资源
type MysqlDatabaseResource struct{}

// BatchCreateOrUpdate 批量创建或更新
func (h MysqlDatabaseResource) BatchCreateOrUpdate(ctx context.Context, data []*entity.MysqlDatabaseResource) (*BatchResult, error) {
	result := &BatchResult{}
	var err error
	var updateResource []entity.MysqlDatabaseResource
	var createResource []entity.MysqlDatabaseResource
	var ispID = ""

	for _, v := range data {
		dbFlag := fmt.Sprintf("%s:%s", v.ClusterID, v.DBName)
		if v.IspID != "" {
			ispID = v.IspID
		}
		if v.ClusterID != "" && v.DBName != "" {
			action, err := h.CreateOrUpdate(ctx, v.ClusterID, v)
			if err != nil {
				errText := fmt.Sprintf("cluster_id: %s, db_name: %s, errors: %v", v.ClusterID, v.DBName, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}
			if action == "nothing" {
				result.Unmodified = append(result.Unmodified, dbFlag)
			} else if action == "update" {
				updateResource = append(updateResource, *v)
				result.SuccessUpdated = append(result.SuccessUpdated, dbFlag)
			} else if action == "create" {
				createResource = append(createResource, *v)
				result.SuccessCreated = append(result.SuccessCreated, dbFlag)
			}
			result.Success = append(result.Success, dbFlag)
		}
	}

	userName := permission.GetUsername(ctx)
	go func() {
		baseCtx := context.WithValue(context.Background(), constant.HookCtxMeta, map[string]string{
			"isp_id":   ispID,
			"username": userName,
		})
		// 等cluster数据写入之后，才能写入db，否则关联关系建立失败
		time.Sleep(20 * time.Second)
		hooks.PubChangeDatabaseResourceHandler(baseCtx, hooks.UpdateDatabaseResource, createResource...)
		hooks.PubChangeDatabaseResourceHandler(baseCtx, hooks.CreateDatabaseResource, updateResource...)
	}()

	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// CreateOrUpdate 创建/更新,clusterID和db_name才能确定唯一
func (h MysqlDatabaseResource) CreateOrUpdate(ctx context.Context, clusterID string, data *entity.MysqlDatabaseResource) (string, error) {
	var err error
	var retErr error
	realAction := ""

	c := entity.GetMysqlDatabaseResCollection(GetEngine())
	filter := map[string]interface{}{
		"cluster_id": clusterID,
		"DBName":     data.DBName,
		"is_delete":  0,
	}

	result := c.FindOne(ctx, filter)
	mongoError := result.Err()
	if mongoError == nil {
		var m entity.MysqlDatabaseResource
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}

		data.ID = m.ID
		raw, _ := mapstruct.Struct2Map(data)
		_, updateErr := c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: raw}})
		retErr = updateErr
		// 此处采用自行序列化并比对字段的方式判断是否真实修改
		dbRaw, _ := mapstruct.Struct2Map(m)
		delete(raw, "UpdateVersion")
		delete(dbRaw, "UpdateVersion")
		isModified, changeField := utils.IsStructModified(dbRaw, raw)
		if isModified {
			c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: map[string]interface{}{
				"updated_time": time.Now().Unix(),
			}}})
			realAction = "update"
			changeItem := utils.ResourceChangeList{
				AccountID:    data.IspID,
				Type:         "database",
				InstanceID:   m.ClusterID + " ~ " + m.DBName,
				InstanceName: m.DBName,
				Data:         changeField,
			}
			go func() {
				hooks.PubResourceChangeListHandler(context.Background(), changeItem)
			}()
		} else {
			realAction = "nothing"
		}
	} else if mongoError == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		realAction = "create"
		_, retErr = c.InsertOne(ctx, data)
		addRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "database",
			InstanceID:   data.ClusterID + " ~ " + data.DBName,
			InstanceName: data.DBName,
			Data: map[string]utils.FieldChangeList{
				"_add": {Before: "", After: string(addRaw)},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	} else {
		retErr = mongoError
	}

	return realAction, retErr
}

// Query 查询
func (h MysqlDatabaseResource) Query(ctx context.Context, params *schema.MysqlDatabaseResourceQueryParams, customFilter ...map[string]interface{}) ([]*entity.MysqlDatabaseResource, uint64, error) {
	c := entity.GetMysqlDatabaseResCollection(GetEngine())
	filter := DefaultFilter(ctx, SearchFilter(params.GetSearch()))
	if len(customFilter) != 0 {
		filter = append(filter, mapToFilter(customFilter[0])...)
	}

	if columnFilter := ColumnFilter(params.MysqlDatabaseResourceColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]*entity.MysqlDatabaseResource, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.MysqlClusterDBResourceEntity.Ordering()} // 加入默认排序
	if len(OrderFields) == 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	if params.TagKey != "" {
		tagFilter, err := h.withTagsFilter(ctx, filter, params.TagKey, params.TagValues)
		if err != nil {
			return list, 0, nil
		}
		filter = tagFilter
	}

	// oids, _ := getFilterWithPK([]string{"608aea025ac12fa358b0eb47"})
	// cur, err := c.Find(ctx,
	// 	bson.M{
	// 	"is_delete":0,
	// 	"_id": bson.M{"$in": oids}}, findOption)
	// if err != nil {
	// 	return nil, 0, err
	// }
	// err = cur.All(ctx, &list)

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter.Map(), &list, findOption)
	if err != nil {
		return list, 0, err
	}

	return list, pr.Total, err
}

func (h MysqlDatabaseResource) withTagsFilter(ctx context.Context, filter bson.D, tagKey string, tagValue []string) (bson.D, error) {
	oid, err := h.WithTagsOptions(ctx, tagKey, tagValue)
	if err != nil {
		return filter, err
	}
	if len(oid) == 0 {
		return filter, errors.New("not found tag")
	}
	// for _, v := range oid {
	// 	fmt.Println("vvvv:", v.Hex())
	// }
	filter = append(filter, bson.E{Key: "_id", Value: bson.M{"$in": oid}})
	return filter, nil
}

// WithTagsOptions 根据标签查询主机，返回绑定的资源ID,再使用in查询即可
func (h MysqlDatabaseResource) WithTagsOptions(ctx context.Context, tagKey string, tagValue []string) ([]primitive.ObjectID, error) {
	return ResourceBindTags.FindResourceIDs(ctx, "MysqlCluster", tagKey, tagValue)
}

// FindMany find many
func (h MysqlDatabaseResource) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.MysqlDatabaseResource, error) {
	c := entity.GetMysqlDatabaseResCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)

	cursor, err := c.Find(ctx, filters)
	if err != nil {
		return nil, err
	}

	var resource []*entity.MysqlDatabaseResource
	err = cursor.All(ctx, &resource)
	return resource, err
}

// UpdateCmdbInfo 更新cmdb相关数据
func (h MysqlDatabaseResource) UpdateCmdbInfo(ctx context.Context, cmdbMap map[string]entity.BkCmdbInfo) error {
	c := entity.GetMysqlDatabaseResCollection(GetEngine())
	for instanceID, cmdbInfo := range cmdbMap {
		_, err := c.UpdateOne(ctx, bson.M{
			"$expr": bson.M{
				"$eq": bson.A{instanceID, bson.M{
					"$concat": bson.A{"$cluster_id", "_", "$DBName"},
				}},
			},
		}, bson.M{"$set": bson.M{
			"BkCmdb": cmdbInfo,
		}})
		if err != nil {
			return err
		}
	}
	return nil
}

// DoCleanup 标记已释放待清理
func (h MysqlDatabaseResource) DoCleanup(ctx context.Context, ispID, clusterID string, latestUpdateVersion string) (int64, error) {
	e := entity.GetMysqlDatabaseResCollection(GetEngine())
	markList, _ := MysqlDatabaseResourceModel.FindMany(ctx, map[string]interface{}{
		"isp_id":        ispID,
		"cluster_id":    clusterID,
		"is_delete":     0,
		"UpdateVersion": bson.D{{"$ne", latestUpdateVersion}},
	})
	for _, data := range markList {
		delRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "database",
			InstanceID:   data.ClusterID + "~" + data.DBName,
			InstanceName: data.DBName,
			Data: map[string]utils.FieldChangeList{
				"_del": {Before: string(delRaw), After: ""},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	}
	res, err := e.UpdateMany(ctx,
		bson.D{
			{"isp_id", ispID},
			{"cluster_id", clusterID},
			{"is_delete", 0},
			{"UpdateVersion", bson.D{{"$ne", latestUpdateVersion}}},
		},
		bson.D{{"$set", bson.M{"is_delete": 1}}})
	if err != nil {
		return 0, err
	}
	return res.MatchedCount, nil
}
