package models

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

var RecycleCfgModel = new(RecycleCfg)

type RecycleCfg struct {
}

func (r RecycleCfg) GetByRegionID(ctx context.Context, regionID string) (*entity.RecycleCfg, error) {
	c := entity.GetRecycleCfgCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "region_id", Value: regionID,
	})

	res := &entity.RecycleCfg{}
	_, err := FindOne(ctx, c, filter, res)
	if err != nil {
		return nil, err
	}
	return res, nil
}
