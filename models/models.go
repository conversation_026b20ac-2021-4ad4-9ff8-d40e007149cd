package models

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/x/mongo/driver"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// CreateIndexes 创建索引
func CreateIndexes(ctx context.Context, cli *mongo.Client) error {
	return createIndexes(
		ctx,
		cli,
		entity.ResGroupEntity,
		entity.ResGroupPolicyEntity,
		entity.RecyclePolicyEntity,
		entity.RecycleEntity,
		entity.ResTemplateEntity,
		entity.ResTagsEntity,
		entity.TaskLogEntity,
		entity.TaskLogResultEntity,
		entity.ResOrderEntity,
		entity.HostResourceEntity,
		entity.AccountEntity,
		entity.AccountParamEntity,
		entity.RegionEntity,
		entity.SyncTaskEntity,
		entity.ResBindTagsEntity,
		entity.MysqlClusterDBResourceEntity,
		entity.MysqlDatabaseResourceEntity,
		entity.MysqlDatabaseAccountResourceEntity,
		entity.MysqlClusterEndpointDBResourceEntity,
		entity.RedisResourceEntity,
		entity.RedisAccountResourceEntity,
		entity.OpsAgentEntity,
		entity.AgentTaskEntity,
		entity.CloudAgentEntity,
		entity.ResSnapshotEntity,
		entity.ResChangelistEntity,
		entity.SecurityGroupEntity,
		entity.LoadBalancerEntity,
		entity.EipEntity,
		entity.DomainEntity,
		entity.IPEntity,
		entity.PolicyEntity,
		entity.IPWhitelistEntity,
		entity.AlbACLEntity,
		entity.LBServerGroupEntity,
	)
}

type indexer interface {
	CreateIndexes(ctx context.Context, cli *mongo.Client) error
}

func createIndexes(ctx context.Context, cli *mongo.Client, indexers ...indexer) error {
	// TODO: errorGroup
	var errors []string
	for _, idx := range indexers {
		err := idx.CreateIndexes(ctx, cli)
		if err != nil {
			if eType, ok := err.(driver.Error); ok {
				if eType.Name == "IndexOptionsConflict" || eType.Name == "IndexKeySpecsConflict" {
					continue
				}
			}
			errors = append(errors, err.Error())
		}
	}
	if len(errors) != 0 {
		return fmt.Errorf("%v", strings.Join(errors, ";"))
	}
	return nil
}

var (
	// 全局MongoDB客户端实例
	mongoClient *mongo.Client
	clientOnce  sync.Once
)

// GetEngine 获取MongoDB连接实例（单例模式）
func GetEngine() *mongo.Client {
	clientOnce.Do(func() {
		mongoConfig := map[string]map[string]string{}
		zest.SafeGet("mongodb", &mongoConfig)
		dsn := mongoConfig["op-cloudman-takumi"]["dsn"]

		// 设置连接池选项
		opts := options.Client().
			ApplyURI(dsn).
			SetMaxPoolSize(100).                  // 最大连接数
			SetMinPoolSize(10).                   // 最小连接数
			SetMaxConnIdleTime(30 * time.Minute). // 连接最大空闲时间
			SetRetryWrites(true)                  // 启用重试写入

		// 创建客户端
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		client, err := mongo.Connect(ctx, opts)
		if err != nil {
			panic(fmt.Sprintf("Failed to connect to MongoDB: %v", err))
		}

		// 验证连接
		err = client.Ping(ctx, nil)
		if err != nil {
			panic(fmt.Sprintf("Failed to ping MongoDB: %v", err))
		}

		mongoClient = client
	})

	return mongoClient
}

// // GetEngine 获取mongo client
// func GetEngine() *mongo.Client {
// 	mongoConfig := map[string]map[string]string{}
// 	zest.SafeGet("mongodb", &mongoConfig)
// 	dsn := mongoConfig["op-cloudman-takumi"]["dsn"]
// 	client, _ := mongo.Connect(context.Background(), options.Client().ApplyURI(dsn))
// 	return client
// }
