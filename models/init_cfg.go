package models

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

var InitCfgModel = new(InitCfg)

type InitCfg struct {
}

func (r InitCfg) GetByRegionID(ctx context.Context, regionID string) (*entity.InitCfg, error) {
	c := entity.GetInitCfgCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{
		Key: "region_id", Value: regionID,
	})

	res := &entity.InitCfg{}
	_, err := FindOne(ctx, c, filter, res)
	if err != nil {
		return nil, err
	}
	return res, nil
}
