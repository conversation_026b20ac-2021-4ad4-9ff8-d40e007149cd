package models

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"

	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// RedisResourceModel 主机资源model
var RedisResourceModel = new(RedisResource)

// RedisResource 主机资源
type RedisResource struct{}

// FindExpireResource 寻找过期资源,仅返回instanceID
func (h RedisResource) FindExpireResource(ctx context.Context, filter map[string]interface{}) (instanceID []string, err error) {
	return nil, err
}

// FindExpireResourceInfo 寻找过期资源,返回资源详情interface
func (h RedisResource) FindExpireResourceInfo(ctx context.Context, filter map[string]interface{}) (map[string]interface{}, error) {
	return nil, nil
}

// SetRecyclable 设置资源为可回收
func (h RedisResource) SetRecyclable(ctx context.Context, instanceID []string, recycle bool) error {
	c := entity.GetRedisResCollection(GetEngine())
	filter := DefaultFilter(ctx, bson.E{Key: "InstanceID", Value: bson.M{"$in": instanceID}})
	err := UpdateMany(ctx, c, filter, map[string]interface{}{"Recyclable": recycle})
	if err != nil {
		return err
	}
	return err
}

// SetRelease 设置资源为已释放
func (h RedisResource) SetRelease(ctx context.Context, instanceID []string) error {
	releaseList, _ := RedisResourceModel.FindMany(ctx, map[string]interface{}{
		"InstanceID": map[string]interface{}{
			"$in": instanceID,
		}},
	)
	for _, data := range releaseList {
		delRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "redis",
			InstanceID:   data.InstanceID,
			InstanceName: data.InstanceName,
			Data: map[string]utils.FieldChangeList{
				"_del": {Before: string(delRaw), After: ""},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	}

	return h.UpdateMany(ctx, map[string]interface{}{
		"InstanceID": map[string]interface{}{
			"$in": instanceID,
		},
	}, map[string]interface{}{
		"is_delete":   1,
		"update_user": permission.GetUsername(ctx),
	})
}

// BatchCreateOrUpdate 批量创建或更新
func (h RedisResource) BatchCreateOrUpdate(ctx context.Context, data []*entity.RedisResource) (*BatchResult, error) {
	result := &BatchResult{}
	var err error
	var updateResource []entity.RedisResource
	var createResource []entity.RedisResource
	var ispID = ""

	for _, v := range data {
		if v.InstanceID != "" {
			if v.IspID != "" {
				ispID = v.IspID
			}
			action, err := h.CreateOrUpdate(ctx, v.InstanceID, v)
			if err != nil {
				errText := fmt.Sprintf("InstanceID: %s, errors: %v", v.InstanceID, err.Error())
				if action == "update" {
					result.UpdateErrors = append(result.UpdateErrors, errText)
				} else {
					result.Errors = append(result.Errors, errText)
				}
				continue
			}

			if action == "nothing" {
				result.Unmodified = append(result.Unmodified, v.InstanceID)
			} else if action == "update" {
				updateResource = append(updateResource, *v)
				result.SuccessUpdated = append(result.SuccessUpdated, v.InstanceID)
			} else if action == "create" {
				createResource = append(createResource, *v)
				result.SuccessCreated = append(result.SuccessCreated, v.InstanceID)
			}
			result.Success = append(result.Success, v.InstanceID)
		}
	}

	go func() {
		baseCtx := context.WithValue(context.Background(), constant.HookCtxMeta, map[string]string{
			"isp_id":   ispID,
			"username": permission.GetUsername(ctx),
		})
		m, ok := ctx.Value(constant.OrderCtxMeta).(map[string]string)
		orderUsername := ""
		if ok {
			value, ok2 := m["order_username"]
			if ok2 {
				orderUsername = value
			}
		}
		baseCtx = context.WithValue(baseCtx, constant.OrderCtxMeta, map[string]string{
			"order_username": orderUsername,
		})
		hooks.PubChangeCacheResourceHandler(baseCtx, hooks.CreateCacheResource, createResource...)
		hooks.PubChangeCacheResourceHandler(baseCtx, hooks.UpdateCacheResource, updateResource...)
	}()

	if len(result.Errors) != 0 {
		err = errors.New("部分更新成功")
	}

	return result, err
}

// CreateOrUpdate 创建/更新,clusterID和db_name才能确定唯一
func (h RedisResource) CreateOrUpdate(ctx context.Context, instanceID string, data *entity.RedisResource) (string, error) {
	var err error
	var retErr error
	realAction := ""

	c := entity.GetRedisResCollection(GetEngine())
	filter := map[string]interface{}{
		"InstanceID": instanceID,
		"is_delete":  0,
	}

	result := c.FindOne(ctx, filter)
	mongoError := result.Err()
	if mongoError == nil {
		var m entity.RedisResource
		err = result.Decode(&m)
		if err != nil {
			return "", err
		}

		data.ID = m.ID
		raw, _ := mapstruct.Struct2Map(data)
		// 以下部分内容copy from host的同名方法，差异之处注释
		delete(raw, "Recyclable")
		delete(raw, "AutoReleaseTime")
		delete(raw, "NeedCleanup")
		delete(raw, "BkCmdb")
		delete(raw, "UpdateVersion")
		// delete(raw, "Tags")
		_, updateErr := c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: raw}})
		retErr = updateErr
		// 此处采用自行序列化并比对字段的方式判断是否真实修改
		dbRaw, _ := mapstruct.Struct2Map(m)
		isModified, changeField := utils.IsStructModified(dbRaw, raw)
		if isModified {
			c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: map[string]interface{}{
				"updated_time":  time.Now().Unix(),
				"UpdateVersion": data.UpdateVersion,
			}}})
			realAction = "update"
			changeItem := utils.ResourceChangeList{
				AccountID:    data.IspID,
				Type:         "redis",
				InstanceID:   m.InstanceID,
				InstanceName: m.InstanceName,
				Data:         changeField,
			}
			go func() {
				hooks.PubResourceChangeListHandler(context.Background(), changeItem)
			}()
		} else {
			c.UpdateOne(ctx, filter, bson.D{{Key: "$set", Value: map[string]interface{}{
				"UpdateVersion": data.UpdateVersion,
			}}})
			realAction = "nothing"
		}
	} else if mongoError == mongo.ErrNoDocuments {
		data.ID = primitive.NewObjectID()
		data.CreatedTime = time.Now().Unix()
		data.UpdatedTime = time.Now().Unix()
		realAction = "create"
		_, retErr = c.InsertOne(ctx, data)
		addRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "redis",
			InstanceID:   data.InstanceID,
			InstanceName: data.InstanceName,
			Data: map[string]utils.FieldChangeList{
				"_add": {Before: "", After: string(addRaw)},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	} else {
		retErr = mongoError
	}

	return realAction, retErr
}

// Query 查询
func (h RedisResource) Query(ctx context.Context, params *schema.RedisResourceQueryParams, customFilter ...map[string]interface{}) ([]*entity.RedisResource, uint64, error) {
	c := entity.GetRedisResCollection(GetEngine())
	filter := ResReadDefaultFilter(ctx, "redis", SearchFilter(params.GetSearch()))
	if len(customFilter) != 0 {
		filter = append(filter, mapToFilter(customFilter[0])...)
	}

	if params.SearchColumnField.SearchValue != "" {
		filter = append(filter, RegexFilter(params.GetSearchColumnField()))
	}

	if columnFilter := ColumnFilter(params.RedisResourceColumnParam); len(columnFilter) != 0 {
		filter = append(filter, columnFilter...)
	}

	var list = make([]*entity.RedisResource, 0)

	findOption := &options.FindOptions{}
	OrderFields := []*schema.OrderField{entity.RedisResourceEntity.Ordering(), &schema.OrderField{
		Key:       "InstanceID",
		Direction: 1,
	}} // 加入默认排序
	if len(params.Ordering) > 0 {
		OrderFields = append(OrderFields, params.BuildToOrderFields()...)
	}

	findOption.SetSort(ParseOrder(OrderFields))

	if params.TagKey != "" {
		tagFilter, err := h.withTagsFilter(ctx, filter, params.TagKey, params.TagValues)
		if err != nil {
			return list, 0, nil
		}
		filter = tagFilter
	}

	// oids, _ := getFilterWithPK([]string{"608aea025ac12fa358b0eb47"})
	// cur, err := c.Find(ctx,
	// 	bson.M{
	// 	"is_delete":0,
	// 	"_id": bson.M{"$in": oids}}, findOption)
	// if err != nil {
	// 	return nil, 0, err
	// }
	// err = cur.All(ctx, &list)

	pr, err := WrapPageQuery(ctx, c, params.PaginationParam, filter.Map(), &list, findOption)
	if err != nil {
		return list, 0, err
	}

	return list, pr.Total, err
}

func (h RedisResource) withTagsFilter(ctx context.Context, filter bson.D, tagKey string, tagValue []string) (bson.D, error) {
	oid, err := h.WithTagsOptions(ctx, tagKey, tagValue)
	if err != nil {
		return filter, err
	}
	if len(oid) == 0 {
		return filter, errors.New("not found tag")
	}
	// for _, v := range oid {
	// 	fmt.Println("vvvv:", v.Hex())
	// }
	filter = append(filter, bson.E{Key: "_id", Value: bson.M{"$in": oid}})
	return filter, nil
}

// WithTagsOptions 根据标签查询redis, 返回绑定的资源ID,再使用in查询即可
func (h RedisResource) WithTagsOptions(ctx context.Context, tagKey string, tagValue []string) ([]primitive.ObjectID, error) {
	return ResourceBindTags.FindResourceIDs(ctx, "Redis", tagKey, tagValue)
}

// FindMany find many
func (h RedisResource) FindMany(ctx context.Context, filter map[string]interface{}) ([]*entity.RedisResource, error) {
	c := entity.GetRedisResCollection(GetEngine())
	filters := ResReadDefaultFilter(ctx, "redis", mapToFilter(filter)...)

	cursor, err := c.Find(ctx, filters)
	if err != nil {
		return nil, err
	}

	var resource []*entity.RedisResource
	err = cursor.All(ctx, &resource)
	return resource, err
}

// FindWithManyPK 根据主键列表进行查询
func (h RedisResource) FindWithManyPK(ctx context.Context, pk []string) ([]*entity.RedisResource, error) {
	c := entity.GetRedisResCollection(GetEngine())
	ids, err := getFilterWithPK(pk)
	if err != nil {
		return nil, err
	}
	filter := ResReadDefaultFilter(ctx, "redis", bson.E{Key: "_id", Value: bson.M{
		"$in": ids,
	}})
	var res []*entity.RedisResource
	cursor, err := c.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = cursor.All(ctx, &res)
	return res, err
}

// FindOne 查找单条记录
func (h RedisResource) FindOne(ctx context.Context, filter map[string]interface{}) (*entity.RedisResource, error) {
	c := entity.GetRedisResCollection(GetEngine())
	filters := ResReadDefaultFilter(ctx, "redis", mapToFilter(filter)...)

	cursor := c.FindOne(ctx, filters)
	err := cursor.Err()
	if err != nil {
		return nil, err
	}

	var resource *entity.RedisResource
	err = cursor.Decode(&resource)
	return resource, err
}

// FindOneIncludeDeleted 查找单条记录
func (h RedisResource) FindOneIncludeDeleted(ctx context.Context, filter map[string]interface{}) (*entity.RedisResource, error) {
	c := entity.GetRedisResCollection(GetEngine())

	cursor := c.FindOne(ctx, filter)
	err := cursor.Err()
	if err != nil {
		return nil, err
	}

	var resource *entity.RedisResource
	err = cursor.Decode(&resource)
	return resource, err
}

// FiledOptions 表描述信息
func (h RedisResource) FiledOptions(ctx context.Context) []*entity.FiledStruct {
	return entity.RedisResourceEntity.Meta()
}

// UpdateLockStatus 更新锁定状态
func (h RedisResource) UpdateLockStatus(ctx context.Context, instanceIDs []string, status bool) error {
	objs, err := getFilterWithPK(instanceIDs)
	if err != nil {
		return err
	}
	c := entity.GetRedisResCollection(GetEngine())
	filter := mapToFilter(map[string]interface{}{
		"_id": map[string]interface{}{
			"$in": objs,
		},
	})
	return UpdateMany(ctx, c, filter, map[string]interface{}{
		"is_lock": status,
	})
}

// UpdateLockStatus是通过主键
// 这里通过InstanceID 更新锁定状态
func (h RedisResource) UpdateLockStatusWithInstanceID(ctx context.Context, instanceIDs []string, status bool) error {
	if len(instanceIDs) == 0 {
		return fmt.Errorf("instanceIDs is null")
	}
	c := entity.GetRedisResCollection(GetEngine())
	filter := DefaultFilter(ctx,
		bson.E{Key: "InstanceID", Value: map[string]interface{}{
			"$in": instanceIDs,
		}})
	return UpdateMany(ctx, c, filter, map[string]interface{}{
		"is_lock": status,
	})
}

// UpdateMany 更新多条数据
func (h RedisResource) UpdateMany(ctx context.Context, filter map[string]interface{}, doc interface{}) error {
	c := entity.GetRedisResCollection(GetEngine())
	filters := DefaultFilter(ctx, mapToFilter(filter)...)
	return UpdateMany(ctx, c, filters, doc)
}

// Count 统计符合条件的数量
func (h RedisResource) Count(ctx context.Context, filter map[string]interface{}) (int64, error) {
	if filter == nil {
		return 0, nil
	}
	c := entity.GetRedisResCollection(GetEngine())
	filters := ResReadDefaultFilter(ctx, "redis", mapToFilter(filter)...)
	return c.CountDocuments(ctx, filters)
}

// HasRedisPermission 检查用户是否有该Redis权限
func (h RedisResource) HasRedisPermission(ctx context.Context, id string) (bool, error) {
	redisResource, err := RedisResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": id})
	if redisResource == nil {
		return false, nil
	}
	accounts, err := AccountModel.FindUserAccounts(ctx)
	if err != nil {
		return false, nil
	}
	for _, v := range accounts {
		if v == redisResource.IspID {
			return true, nil
		}
	}
	return false, nil
}

// MarkCleanup 标记已释放待清理
func (h RedisResource) MarkCleanup(ctx context.Context, ispID, regionID string, latestUpdateVersion string) (int64, error) {
	e := entity.GetRedisResCollection(GetEngine())

	markList, _ := RedisResourceModel.FindMany(ctx, map[string]interface{}{
		"isp_id":        ispID,
		"RegionID":      regionID,
		"is_delete":     0,
		"NeedCleanup":   bson.D{{"$ne", true}},
		"UpdateVersion": bson.D{{"$ne", latestUpdateVersion}},
	})
	for _, data := range markList {
		delRaw, _ := json.Marshal(data)
		changeItem := utils.ResourceChangeList{
			AccountID:    data.IspID,
			Type:         "redis",
			InstanceID:   data.InstanceID,
			InstanceName: data.InstanceName,
			Data: map[string]utils.FieldChangeList{
				"_del": {Before: string(delRaw), After: ""},
			},
		}
		go func() {
			hooks.PubResourceChangeListHandler(context.Background(), changeItem)
		}()
	}

	_, err := e.UpdateMany(ctx,
		bson.D{
			{"isp_id", ispID},
			{"RegionID", regionID},
			{"is_delete", 0},
		},
		bson.D{{"$set", bson.M{"NeedCleanup": false}}})
	if err != nil {
		return 0, err
	}
	res, err := e.UpdateMany(ctx,
		bson.D{
			{"isp_id", ispID},
			{"RegionID", regionID},
			{"UpdateVersion", bson.D{{"$ne", latestUpdateVersion}}},
			{"is_delete", 0},
		},
		bson.D{{"$set", bson.M{"NeedCleanup": true}}})
	if err != nil {
		return 0, err
	}
	return res.MatchedCount, nil
}

// HoldInstancePlace ...
func (h RedisResource) HoldInstancePlace(ctx context.Context, data []byte, orderID string, ispType string) error {
	var req map[string]interface{}
	err := json.Unmarshal(data, &req)
	if err != nil {
		return err
	}

	var instanceName string
	if ispType == "aliyun" {
		instanceName, _ = req["InstanceName"].(string)
	} else if ispType == "aws" {
		instanceName, _ = req["ReplicationGroupId"].(string)
	}
	amountF, _ := req["Amount"].(float64)
	amount := int(amountF)
	serviceTreeNodes, _ := req["serviceTreeNodes"].([]interface{})

	var instanceNames []string
	if instanceName != "" {
		if err := cloudutils.RuleCheck(instanceName, cloudutils.Linux); err == nil {
			instanceNames, err = cloudutils.GetCloudutils(RedisResourceModel).GenInstanceName(
				context.Background(),
				instanceName,
				amount, true, cloudutils.Linux,
			)
			if err != nil {
				return err
			}
			if instanceNames == nil {
				return fmt.Errorf("主机名重复")
			}
		}
		// req["UniqueSuffix"].(bool) = false
	}

	initTreeNode := ""
	if len(serviceTreeNodes) > 0 {
		initTreeNode, _ = serviceTreeNodes[0].(string)
	}
	for _, heldInstanceName := range instanceNames {
		err := ResCreatingModel.CreateOrUpdate(ctx, &entity.ResCreating{
			OrderID:      orderID,
			ResourceType: "redis",
			InstanceName: heldInstanceName,
			TreeNode:     initTreeNode,
			Tag:          "",
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// ReleaseInstancePlace ...
func (h RedisResource) ReleaseInstancePlace(ctx context.Context, data []byte, orderID string) error {
	err := ResCreatingModel.DeleteByOrder(ctx, orderID)
	if err != nil {
		return err
	}
	return nil
}

// FindExistsInstancename ...
func (h RedisResource) FindExistsInstancename(ctx context.Context, instanceNames []string) ([]string, error) {
	redises, err := RedisResourceModel.FindMany(ctx, map[string]interface{}{"MarkCleanup": bson.M{"$ne": true}, "InstanceName": map[string]interface{}{
		"$in": instanceNames,
	}})
	if err != nil {
		return nil, err
	}
	res := []string{}
	for _, h := range redises {
		res = append(res, h.InstanceName)
	}
	// 搜索创建中数据的表
	resInProcess, err := ResCreatingModel.FindMany(ctx, map[string]interface{}{"resource_type": "redis", "instance_name": map[string]interface{}{
		"$in": instanceNames,
	}})
	if err != nil {
		return nil, err
	}
	for _, h := range resInProcess {
		res = append(res, h.InstanceName)
	}
	return res, nil
}

// FindRegexInstanceName ...
func (h RedisResource) FindRegexInstanceName(ctx context.Context, regex primitive.Regex) ([]string, error) {
	redises, err := RedisResourceModel.FindMany(ctx, map[string]interface{}{"InstanceName": regex})
	if err != nil {
		return nil, err
	}
	res := []string{}
	for _, h := range redises {
		res = append(res, h.InstanceName)
	}
	// 搜索创建中数据的表
	resInProcess, err := ResCreatingModel.FindMany(ctx, map[string]interface{}{"resource_type": "redis", "instance_name": regex})
	if err != nil {
		return nil, err
	}
	for _, h := range resInProcess {
		res = append(res, h.InstanceName)
	}
	return res, nil
}

// RedisEntityToPb redis-entity-to-pb
func RedisEntityToPb(v entity.RedisResource) *cloudman.RedisDetail {
	var tags []*cloudman.ResourceTag
	for _, tag := range v.Tags.Tag {
		tags = append(tags, &cloudman.ResourceTag{Key: tag.Key, Value: tag.Value})
	}
	return &cloudman.RedisDetail{
		Id:               v.ID.Hex(),
		IsLock:           v.IsLock,
		IspId:            v.IspID,
		IspType:          v.IspType,
		VpcId:            v.VpcID,
		CreateTime:       v.CreateTime,
		Tags:             tags,
		ResourceGroupId:  v.ResourceGroupID,
		ZoneId:           v.ZoneID,
		Port:             v.Port,
		RegionID:         v.RegionID,
		EngineVersion:    v.EngineVersion,
		InstanceClass:    v.InstanceClass,
		QPS:              v.QPS,
		Connections:      v.Connections,
		Bandwidth:        v.Bandwidth,
		PrivateIp:        v.PrivateIP,
		NetworkType:      v.NetworkType,
		Capacity:         v.Capacity,
		InstanceId:       v.InstanceID,
		InstanceType:     v.InstanceType,
		InstanceName:     v.InstanceName,
		InstanceStatus:   v.TransStatus(),
		ChargeType:       v.ChargeType,
		ConnectionDomain: v.ConnectionDomain,
	}
}

var redisBriefCache sync.Map

// GetBrief -
func (h RedisResource) GetBrief(ctx context.Context, accountID, regionID string) (map[string]int64, error) {
	if iData, ok := redisBriefCache.Load(accountID + regionID); ok && cfg.GetSystemConfig().Env != "local" {
		data := iData.(map[string]int64)
		if time.Now().UnixNano()-data["timestamp"] <= int64(3*time.Minute) {
			return data, nil
		}
	}
	commonFilter := bson.D{
		Filter("isp_id", accountID),
	}
	if regionID != "all" && regionID != "" {
		commonFilter = append(commonFilter, Filter("RegionID", regionID))
	}
	afterSevenDayStamp := time.Now().Add(7 * 24 * time.Hour).Unix()
	beforeSevenDayStamp := time.Now().Add(-7 * 24 * time.Hour).Unix()

	// 总主机数量
	totalFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
	)
	totalFilter = append(totalFilter, commonFilter...)
	// 正在运行，含回收站
	runningFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "Status", Value: "Running"},
	)
	runningFilter = append(runningFilter, commonFilter...)
	// agent正在运行，含回收站
	agentRunFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "agent_status", Value: 1},
	)
	agentRunFilter = append(agentRunFilter, commonFilter...)

	// 7天即将过期
	expireFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "ExpiredTime", Value: bson.M{"$lte": afterSevenDayStamp, "$ne": 0}},
	)
	expireFilter = append(expireFilter, commonFilter...)
	// 7天新创建
	createFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "CreateTime", Value: bson.M{"$gte": beforeSevenDayStamp, "$ne": 0}},
	)
	createFilter = append(createFilter, commonFilter...)
	// 7天新释放
	deleteFilter := NoDelFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "deleted_time", Value: bson.M{"$gte": beforeSevenDayStamp, "$ne": 0}},
	)
	deleteFilter = append(deleteFilter, commonFilter...)
	// 回收站
	recycleFilter := DefaultFilter(ctx,
		bson.E{Key: "Recyclable", Value: true},
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
	)
	recycleFilter = append(recycleFilter, commonFilter...)
	// 已释放，待清理
	cleanupFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: true},
	)
	cleanupFilter = append(cleanupFilter, commonFilter...)

	c := entity.GetRedisResCollection(GetEngine())
	totalCount, err := c.CountDocuments(ctx, totalFilter)
	if err != nil {
		return nil, err
	}
	runningCount, err := c.CountDocuments(ctx, runningFilter)
	if err != nil {
		return nil, err
	}
	agentRunningCount, err := c.CountDocuments(ctx, agentRunFilter)
	if err != nil {
		return nil, err
	}
	expireCount, err := c.CountDocuments(ctx, expireFilter)
	if err != nil {
		return nil, err
	}
	createdCount, err := c.CountDocuments(ctx, createFilter)
	if err != nil {
		return nil, err
	}
	deletedCount, err := c.CountDocuments(ctx, deleteFilter)
	if err != nil {
		return nil, err
	}
	recycledCount, err := c.CountDocuments(ctx, recycleFilter)
	if err != nil {
		return nil, err
	}
	needCleanupCount, err := c.CountDocuments(ctx, cleanupFilter)
	if err != nil {
		return nil, err
	}
	m := map[string]int64{
		"total":         totalCount,
		"running":       runningCount,
		"agent_running": agentRunningCount,
		"7day_created":  createdCount,
		"7day_deleted":  deletedCount,
		"7day_expire":   expireCount,
		"need_cleanup":  needCleanupCount,
		"recycled":      recycledCount,
		"timestamp":     time.Now().UnixNano(),
	}
	redisBriefCache.Store(accountID+regionID, m)
	return m, nil
}

var redisDailyCountCache sync.Map

// GetDailyCount -
func (h RedisResource) GetDailyCount(ctx context.Context, accountID, regionID string, timeBefore int32) ([]DailyCount, error) {
	cacheKey := fmt.Sprintf("%s.%s.%d", accountID, regionID, timeBefore)
	if iData, ok := redisDailyCountCache.Load(cacheKey); ok {
		data := iData.(dailyCountCachePack)
		if time.Now().UnixNano()-data.ts <= int64(3*time.Minute) && cfg.GetSystemConfig().Env != "local" {
			return data.dc, nil
		}
	}

	commonFilter := bson.D{
		Filter("isp_id", accountID),
	}
	if regionID != "all" && regionID != "" {
		commonFilter = append(commonFilter, Filter("RegionID", regionID))
	}
	// 当前时间戳
	nowStamp := time.Now().Unix()
	// 北京时间今天0点时间戳
	todayStamp := nowStamp - (nowStamp+8*3600)%86400
	// n天前北京时间0点的时间戳
	dayBeforeStamp := todayStamp - int64(timeBefore)*86400

	// 总主机数量
	totalFilter := DefaultFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
	)
	totalFilter = append(totalFilter, commonFilter...)
	// 增加的主机
	createFilter := NoDelFilter(ctx,
		bson.E{Key: "CreationTime", Value: bson.M{"$gte": dayBeforeStamp, "$ne": 0}},
	)
	createFilter = append(createFilter, commonFilter...)
	// 删除的主机
	deleteFilter := NoDelFilter(ctx,
		bson.E{Key: "NeedCleanup", Value: bson.M{"$ne": true}},
		bson.E{Key: "deleted_time", Value: bson.M{"$gte": dayBeforeStamp, "$ne": 0}},
	)
	deleteFilter = append(deleteFilter, commonFilter...)

	c := entity.GetRedisResCollection(GetEngine())
	// 当前总数
	totalCount, err := c.CountDocuments(ctx, totalFilter)
	if err != nil {
		return nil, err
	}
	var createdHost []entity.RedisResource
	_, err = FindMany(ctx, c, createFilter, &createdHost)
	if err != nil {
		return nil, err
	}
	var deletedHost []entity.RedisResource
	_, err = FindMany(ctx, c, deleteFilter, &deletedHost)
	if err != nil {
		return nil, err
	}

	dayData := map[int64]DailyCount{}
	for _, h := range createdHost {
		dayPass := (todayStamp + 86400 - h.CreatedTime) / 86400
		s := dayData[dayPass]
		s.Created++
		dayData[dayPass] = s
	}
	for _, h := range deletedHost {
		dayPass := (todayStamp + 86400 - h.DeletedTime) / 86400
		s := dayData[dayPass]
		s.Deleted++
		dayData[dayPass] = s
	}
	var result []DailyCount
	for i := 0; i < int(timeBefore); i++ {
		dayName := time.Unix(todayStamp-int64(i)*86400, 0).In(time.FixedZone("", 28800)).Format("2006-01-02")
		s := dayData[int64(i)]
		result = append(result, DailyCount{
			DayName: dayName,
			State:   int(totalCount),
			Created: s.Created,
			Deleted: s.Deleted,
		})
		totalCount = totalCount - int64(s.Created) + int64(s.Deleted)
	}
	redisDailyCountCache.Store(cacheKey, dailyCountCachePack{
		dc: result,
		ts: time.Now().UnixNano(),
	})
	return result, nil
}

func (h RedisResource) GetByInstanceIDs(ctx context.Context, instanceIDs []string) ([]*entity.RedisResource, error) {
	filter := map[string]interface{}{"InstanceID": map[string]interface{}{"$in": instanceIDs}}
	return h.FindMany(ctx, filter)
}
