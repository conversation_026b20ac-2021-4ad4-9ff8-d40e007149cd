package core

import (
	"context"
	"fmt"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	provider "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// RecycleCore 回收站相关核心逻辑
var RecycleCore = recycle{
	runeModel: ResourceModelMap,
}

// ResourceModelMap 资源组model对象
var ResourceModelMap = map[string]ResourceModel{
	"host":  models.HostResourceModel,
	"redis": models.RedisResourceModel,
	"mysql": models.MysqlClusterResourceModel,
	"lb":    models.LoadBalancerModel,
}

// type Resource struct {
// 	Model  ResourceModel
// 	Entity []entity.Collectioner
// }

type recycle struct {
	runeModel map[string]ResourceModel
}

// GetModel 获取model
func (r recycle) GetModel(instanceType string) (ResourceModel, error) {
	if m, ok := r.runeModel[instanceType]; ok {
		return m, nil
	}
	return nil, fmt.Errorf("recycle:%s model not found", instanceType)
}

// SetReleaseTime 给资源设置释放时间,方便前端展示,已释放的资源则跳过
func (r recycle) SetReleaseTime(ctx context.Context, instanceType string, recEnt []*entity.Recycle) error {
	// * 获取释放策略
	policy, err := models.RecyclePolicyModel.FindPolicy(ctx, &entity.RecyclePolicy{
		InstanceType: instanceType,
		Name:         "",
	})

	if err != nil {
		return err
	}

	// 检查是否存在关闭自动释放策略，一旦存在该策略,所有释放条件将全部无效
	for _, v := range policy {
		if v.Enabled == false {
			logger.Infof("%s存在关闭自动释放策略,跳过", instanceType)
			return nil
		}
	}

	var instanceID []string
	instanceMap := make(map[string]*entity.Recycle)

	for _, v := range recEnt {
		// 只有等待释放的资源才会被计算
		instanceID = append(instanceID, v.InstanceID)
		instanceMap[v.InstanceID] = v
	}

	// var wg sync.WaitGroup
	for _, val := range policy {
		filter, _, err := val.ToFilter()
		if err != nil {
			return err
		}
		filter["InstanceID"] = map[string]interface{}{
			"$in": instanceID,
		}
		oInstanceID, err := r.runeModel[instanceType].FindExpireResource(ctx, filter)
		if err != nil {
			return err
		}
		if oInstanceID != nil {
			for _, v := range oInstanceID {
				if res, ok := instanceMap[v]; ok {
					res.DestroyTime = res.CreatedTime + int64(val.ReleaseTime*3600)
					delete(instanceMap, v)
				}
			}
		}

		var tmpInstance []string
		for k := range instanceMap {
			tmpInstance = append(tmpInstance, k)
		}

		instanceID = tmpInstance
	}

	return nil
}

// 资源真正被释放时会将原纪录中的destory_time保存为释放时间

// GetExpireResource 获取过期资源列表,recEnt还未被释放的回收站实例
func (r recycle) GetExpireResource(ctx context.Context, instanceType string, recEnt []*entity.Recycle) ([]interface{}, error) {
	policy, err := models.RecyclePolicyModel.FindPolicy(ctx, &entity.RecyclePolicy{
		InstanceType: instanceType,
		Name:         "",
	})

	if err != nil {
		return nil, err
	}

	// 检查是否存在关闭自动释放策略，一旦存在该策略,所有释放条件将全部无效
	for _, v := range policy {
		if v.Enabled == false {
			logger.Infof("%s存在关闭自动释放策略,跳过", instanceType)
			return nil, nil
		}
	}

	var instanceID []string
	instanceMap := make(map[string]*entity.Recycle)
	var recyces []interface{} // 满足释放条件的实例列表

	for _, v := range recEnt {
		// 只有等待释放的资源才会被计算
		instanceID = append(instanceID, v.InstanceID)
		instanceMap[v.InstanceID] = v
	}

	// var wg sync.WaitGroup
	t := time.Now().Unix()
	for _, val := range policy {
		filter, _, err := val.ToFilter()
		if err != nil {
			return nil, err
		}
		filter["InstanceID"] = map[string]interface{}{
			"$in": instanceID,
		}
		Instance, err := r.runeModel[instanceType].FindExpireResourceInfo(ctx, filter)
		if err != nil {
			return nil, err
		}
		if Instance != nil {
			for k, v := range Instance {
				if res, ok := instanceMap[k]; ok {
					if (t - res.CreatedTime) < int64(val.ReleaseTime*3600) {
						// 不满足释放条件
						delete(instanceMap, k)
					} else {
						recyces = append(recyces, v)
					}
				}
			}
		}

		var tmpInstance []string
		for k := range instanceMap {
			tmpInstance = append(tmpInstance, k)
			logger.Debugf("发现符合释放条件的实例:%s", k)
		}

		instanceID = tmpInstance
	}

	return recyces, nil
}

// RunRelease 执行释放
func (r recycle) RunRelease(ctx context.Context, instanceType, ispType string, orderID string, initProvide common.InitProvider, instanceID []string, isForce bool) error {
	if instanceType == "" {
		return fmt.Errorf("instanceType or ispType is enpty")
	}

	providerName := fmt.Sprintf("%s_%s", ispType, instanceType)
	providerFunc, ok := provider.ProvideMap[providerName]
	if ok {
		p, err := providerFunc(initProvide)
		if err != nil {
			return err
		}
		err = p.DeleteInstance(ctx, 60*time.Second, orderID, common.DeleteInstanceForm{
			Force:     isForce,
			Instances: instanceID,
		})
		if err != nil {
			return err
		}
	}

	return nil
}
