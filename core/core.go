package core

import (
	"context"
)

// ResourceModel 支持释放的资源模型
type ResourceModel interface {
	// FindExpireResource 寻找过期资源,仅返回instanceID
	FindExpireResource(ctx context.Context, filter map[string]interface{}) (instanceID []string, err error)
	// FindExpireResourceInfo 寻找过期资源,返回资源详情interface
	FindExpireResourceInfo(ctx context.Context, filter map[string]interface{}) (map[string]interface{}, error)
	// SetRecyclable 标记资源是否可以释放
	SetRecyclable(ctx context.Context, instanceID []string, recycle bool) error
	// SetRelease 设置资源为已释放
	SetRelease(ctx context.Context, instanceID []string) error
	// Count 统计符合条件的数量
	Count(ctx context.Context, filter map[string]interface{}) (int64, error)
}
