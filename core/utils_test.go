package core

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"testing"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

func TestGenInstanceName(t *testing.T) {
	// r := regexp.MustCompile(`\[\d{0,8}(\\,[0-8]{0, 1})\\]`)
	// r := regexp.MustCompile(`\[\d{0,8}(\\\\,[0-8])?`)
	r := regexp.MustCompile(`\[\d{0,8}(,[0-8])?]`)
	matchList := r.FindStringSubmatch("hk4s-sh-prod-[0100001,6].mhy.com")
	fmt.Println(matchList)
	if len(matchList) == 0 {
		panic("no match")
	}

	str := strings.Trim(matchList[0], "[")
	str = strings.Trim(str, "]")

	var beginstr string
	var bitstr string
	for index, val := range strings.Split(str, ",") {
		if index == 0 {
			beginstr = val
		} else {
			bitstr = val
		}
	}

	begin, _ := strconv.Atoi(beginstr)
	bits, _ := strconv.Atoi(bitstr)
	if len(beginstr) > bits {

	}

	fmt.Println("begin, bits:", begin, bits)
}

func TestGenInstanceName1(t *testing.T) {
	// fmt.Println(GenInstanceName(context.Background(), "hk4s-sh-prod-[0, 8].mhy.com", 100, "aaa", false, Linux))
}

func TestGenInstanceName2(t *testing.T) {
	// fmt.Println(genInstanceName("hk4e-sh-prod-", ".mhy.com", 0, 8, 100))
	fmt.Println(utils.MaxUInt(9))
}
