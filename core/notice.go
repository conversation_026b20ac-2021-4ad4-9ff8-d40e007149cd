package core

import (
	"context"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	notifySdk "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/op-notifyman-sdk"
)

// SendNoticeCtx 使用通知平台发送消息(使用队列接管通知?)
func SendNoticeCtx(ctx context.Context, msg *notifySdk.QNotify) error {
	config := cfg.GetNotifyCfg()

	msg.NotifyManAddress = config.NotifyAddress
	msg.PlateFromID = config.Items["order"].PlateFromID
	msg.ToolID = config.Items["order"].ToolID
	msg.TemplateID = config.Items["order"].TemplateID
	return notifySdk.SendMessage(ctx, msg)
}

// SendNotice 使用通知平台发送消息,默认5秒超时
func SendNotice(msg *notifySdk.QNotify) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return SendNoticeCtx(ctx, msg)
}
