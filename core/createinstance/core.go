package createinstance

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
)

// Model ...
type Model interface {
	// HoldInstancePlace ...
	HoldInstancePlace(ctx context.Context, data []byte, orderID string, ispType string) error
	// ReleaseInstancePlace ...
	ReleaseInstancePlace(ctx context.Context, data []byte, orderID string) error
}

// ModelMap ...
var ModelMap = map[string]Model{
	"host":  models.HostResourceModel,
	"rds":   models.MysqlClusterResourceModel,
	"redis": models.RedisResourceModel,
	"mysql": models.MysqlClusterResourceModel,
	"lb":    models.LoadBalancerModel,
}
