package jobman

import (
	"fmt"
	"strconv"
	"strings"

	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	jobSDK "platgit.mihoyo.com/op-plat/op-jobman/sdk"
)

// GetCronCli ...
func GetCronCli() (*jobSDK.JobMan, error) {
	prodToken, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
	if err != nil {
		return nil, err
	}
	cronCli := jobSDK.InitSDK(jobSDK.CrontabSDKParams{
		Address: fmt.Sprintf("%s/backend/jobman", cfg.GetJobmanConfig().ProdURLBase),
		Token:   fmt.Sprintf("Bearer %s", prodToken),
	})
	return &cronCli, nil
}

// CreateSyncCron ...
func CreateSyncCron(ctx context.Context, syncTaskID string, taskName string, cycle uint32) (string, string, error) {
	jobmanCfg := cfg.GetJobmanConfig()
	if !jobmanCfg.Enable {
		return "", "", nil
	}
	cli, err := GetCronCli()
	if err != nil {
		return "", "", err
	}
	templateID, cronTaskID, err := cli.AddCronSimpleHttpTask(ctx, jobSDK.AddCronSimpleHttpTaskParams{
		HttpTaskStep: jobSDK.HttpTaskStep{
			Method:  "POST",
			Path:    fmt.Sprintf("%s/op-cloudman-takumi/api/v1/task/%s/run", jobmanCfg.CurEnvPrefix, syncTaskID),
			Headers: fmt.Sprintf("Authorization=Bearer %s", jobmanCfg.TokenForJobmanCron),
		},
		TaskRaw: jobSDK.TaskRaw{
			Name:     fmt.Sprintf(taskName),
			CronSpec: fmt.Sprintf("0 0/%s * * * *", strconv.Itoa(int(cycle))),
		},
	})
	if err != nil {
		return "", "", err
	}
	cronTaskID = strings.Trim(cronTaskID, "\"")
	return templateID, cronTaskID, nil
}

// UpdateSyncCron ...
func UpdateSyncCron(ctx context.Context, taskID string, cycle uint32) error {
	jobmanCfg := cfg.GetJobmanConfig()
	if !jobmanCfg.Enable {
		return nil
	}
	cli, err := GetCronCli()
	if err != nil {
		return err
	}
	taskInfo, err := cli.GetCronTaskInfo(ctx, taskID)
	if err != nil {
		return err
	}
	err = cli.EditCronTask(ctx, taskID,
		jobSDK.TaskRaw{
			TemplateID:     taskInfo.TemplateID,
			Name:           taskInfo.Name,
			CronSpec:       fmt.Sprintf("* 0/%s * * * *", strconv.Itoa(int(cycle))),
			TemplateParams: taskInfo.GlobalParams,
		},
	)
	if err != nil {
		return err
	}
	return nil
}

// DeleteSyncCron ...
func DeleteSyncCron(ctx context.Context, taskID string, templateID string) error {
	jobmanCfg := cfg.GetJobmanConfig()
	if !jobmanCfg.Enable {
		return nil
	}
	cli, err := GetCronCli()
	if err != nil {
		return err
	}
	err = cli.DeleteCronTask(ctx, taskID)
	if err != nil {
		return err
	}
	err = cli.DeleteTemplateTask(ctx, templateID)
	if err != nil {
		return err
	}
	return nil
}
