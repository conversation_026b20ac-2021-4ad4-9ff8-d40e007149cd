package jobman

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// FetchDatabase -
func FetchDatabase(ctx context.Context, logger *logrus.Logger, env, dbClusterEndpoint, clusterID string) ([]string, error) {
	result := []string{}
	if !cfg.GetJobmanConfig().Enable {
		logger.Infof("配置未启用作业平台任务下发验证...跳过")
		return []string{}, nil
	}
	gCtx := context.Background()
	syncCfg := cfg.GetSyncOptionConf()
	createJobBody := generateJobmanBody("getDatabase-"+clusterID, env, []Server{
		{AgentID: syncCfg.DbFetchAgent},
	}, "shell", fmt.Sprintf(`cd /home/<USER>/op-databaseman-agent/op-databaseman-agent && ./main -t "agent.toml" -m "showDatabases" -H "%s"`, dbClusterEndpoint))
	jobmanTaskID, err := CreateJobmanFastTask(gCtx, logger, env, createJobBody)
	if err != nil {
		logger.Errorf("FetchDatabase.CreateJobmanFastTask.error: %s", err.Error())
		return []string{}, err
	}
	time.Sleep(10 * time.Second)
	agentRunIDMap, _, err := GetJobmanTaskDetail(gCtx, logger, env, jobmanTaskID)
	if err != nil {
		logger.Errorf("FetchDatabase.GetJobmanTaskDetail.error: %v", err)
		return []string{}, err
	}
	if agentRunIDMap[syncCfg.DbFetchAgent] == "" {
		logger.Errorf("FetchDatabase.GetJobmanTaskDetail.empty: %v", err)
		return []string{}, err
	}
	fullLog, _, err := GetJobmanTaskLog(gCtx, logger, env, syncCfg.DbFetchAgent, agentRunIDMap[syncCfg.DbFetchAgent])
	if err != nil {
		logger.Errorf("FetchDatabase.GetJobmanTaskLog.error: %v", err.Error())
		return []string{}, err
	}
	startLog := false
	for _, l := range fullLog {
		if strings.Contains(l, "执行查询语句") {
			startLog = true
			continue
		}
		if startLog && l != "" {
			result = append(result, l)
		}
	}
	return result, nil
}

// CreateJobmanFastTask -
func CreateJobmanFastTask(ctx context.Context, logger *logrus.Logger, env string, createJobBody string) (string, error) {
	tokenHeader := http.Header{}
	base := ""
	if env == "prod" {
		base = cfg.GetJobmanConfig().ProdURLBase
		token, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
		if err != nil {
			return "", err
		}
		tokenHeader.Add("Authorization", "Bearer "+token)
	} else {
		base = cfg.GetJobmanConfig().TestURLBase
		token, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().TestIAMEnv)
		if err != nil {
			return "", err
		}
		tokenHeader.Add("Authorization", "Bearer "+token)
	}
	createJobURL := base + "/backend/jobman/api/v1/tasks"
	createJobResp, _, err := utils.HTTPPost(ctx, createJobURL, tokenHeader, []byte(createJobBody), 5*time.Second)
	if err != nil {
		return "", err
	}
	jobmanTaskID := gjson.GetBytes(createJobResp, "data.taskId").String()
	if jobmanTaskID == "" {
		return "", fmt.Errorf("CreateJobmanFastTask: cannot parse jobmanTaskID")
	}
	return jobmanTaskID, nil
}

// GetJobmanTaskDetail -
func GetJobmanTaskDetail(ctx context.Context, logger *logrus.Logger, env, jobmanTaskID string) (map[string]string, gjson.Result, error) {
	tokenHeader := http.Header{}
	base := ""
	if env == "prod" {
		base = cfg.GetJobmanConfig().ProdURLBase
		token, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
		if err != nil {
			return nil, gjson.Result{}, err
		}
		tokenHeader.Add("Authorization", "Bearer "+token)
	} else {
		base = cfg.GetJobmanConfig().TestURLBase
		token, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().TestIAMEnv)
		if err != nil {
			return nil, gjson.Result{}, err
		}
		tokenHeader.Add("Authorization", "Bearer "+token)
	}
	taskQueryURL := base + fmt.Sprintf("/backend/jobman/api/v1/tasks/%s?sStatus=0&page=1&size=2000", jobmanTaskID)
	taskQueryResp, _, err := utils.HTTPGet(ctx, taskQueryURL, tokenHeader, nil, 5*time.Second)
	taskQueryCode := gjson.GetBytes(taskQueryResp, "code").Int()
	if err != nil || taskQueryCode != 2000 {
		retErr := fmt.Errorf("查询作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
		logger.Errorf(retErr.Error())
		return nil, gjson.Result{}, retErr
	}
	agentRunIDMap := map[string]string{}
	agentRunContentMap := map[string]string{}
	taskQueryResult := gjson.GetBytes(taskQueryResp, "data.listData.list")
	taskQueryResult.ForEach(func(key, value gjson.Result) bool {
		agentID := value.Get("agentId").String()
		agentRunIDMap[agentID] = value.Get("_id").String()
		agentRunContentMap[agentID] = value.Get("explain").String()
		return true
	})
	return agentRunIDMap, taskQueryResult, nil
}

// GetJobmanTaskLog -
func GetJobmanTaskLog(ctx context.Context, logger *logrus.Logger, env, agentID, jobmanRunID string) ([]string, gjson.Result, error) {
	tokenHeader := http.Header{}
	base := ""
	if env == "prod" {
		base = cfg.GetJobmanConfig().ProdURLBase
		token, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
		if err != nil {
			return nil, gjson.Result{}, err
		}
		tokenHeader.Add("Authorization", "Bearer "+token)
	} else {
		base = cfg.GetJobmanConfig().TestURLBase
		token, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().TestIAMEnv)
		if err != nil {
			return nil, gjson.Result{}, err
		}
		tokenHeader.Add("Authorization", "Bearer "+token)
	}
	taskQueryURL := base + fmt.Sprintf("/backend/jobman/api/v1/task/log/%s/%s/0", agentID, jobmanRunID)
	taskQueryResp, _, err := utils.HTTPGet(ctx, taskQueryURL, tokenHeader, nil, 5*time.Second)
	taskQueryCode := gjson.GetBytes(taskQueryResp, "code").Int()
	if err != nil || taskQueryCode != 2000 {
		retErr := fmt.Errorf("查询作业平台日志失败, error: %v, jobmanRunID: %v", err, jobmanRunID)
		logger.Errorf(retErr.Error())
		return nil, gjson.Result{}, retErr
	}
	logContent := []string{}
	taskQueryResult := gjson.GetBytes(taskQueryResp, "data.result")
	taskQueryResult.ForEach(func(key, value gjson.Result) bool {
		if value.Get("outputType").String() != "stdout" {
			return true
		}
		logContent = append(logContent, value.Get("explain").String())
		return true
	})
	return logContent, taskQueryResult, nil
}
