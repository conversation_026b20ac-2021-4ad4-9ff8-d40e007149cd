package jobman

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"moul.io/http2curl"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"

	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// Server -
type Server struct {
	AgentID        string
	HostName       string
	InstanceName   string
	InstanceID     string
	PrivateAddress string
}

// Check -
func Check(ctx context.Context, logger *logrus.Logger, orderID string, cmdbEnv string, serverList []Server, maxTime time.Duration, platform string) error {
	// 准备
	timeout := time.Now().Add(maxTime)
	if !cfg.GetJobmanConfig().Enable {
		logger.Infof("配置未启用作业平台任务下发验证...跳过")
		return nil
	}
	reportStore, err := common.FetchOrderStatusDetail(ctx, orderID)
	if err != nil {
		logger.Errorf("无法获取订单%s信息：%s", orderID, err.Error())
		return fmt.Errorf("无法获取订单%s信息：%s", orderID, err.Error())
	}
	if len(serverList) == 0 {
		logger.Infof("作业平台(%s-%s)检查：无实例", platform, cmdbEnv)
		return nil
	}
	logger.Infof("作业平台(%s-%s)检查：%d个实例", platform, cmdbEnv, len(serverList))
	agentMap := map[string]Server{}
	for _, server := range serverList {
		agentMap[server.AgentID] = server
	}

	// 构造发送任务结构体
	var jobType, jobCmd string
	if platform == "windows" {
		jobType = "powershell"
		jobCmd = "(ipconfig|Select-String \"IPV4\"|Out-String).Trim().Replace(\"`r`n\", \",\")"
	} else {
		jobType = "shell"
		jobCmd = "#!/bin/bash \necho \"hostname:$(hostname),ip:$(hostname -I)\""
	}
	createJobBody := generateJobmanBody("cloudman_createinstance_"+orderID, cmdbEnv, serverList, jobType, jobCmd)
	jobmanTaskID, err := CreateJobmanFastTask(ctx, logger, cmdbEnv, createJobBody)
	if err != nil {
		for _, server := range serverList {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.jobs.jobman.last_time", server.InstanceName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.jobman.status", server.InstanceName):    "create_failed",
				common.ReportSprintf("%s.jobs.jobman.is_err", server.InstanceName):    true,
				common.ReportSprintf("%s.last_time", server.InstanceName):             time.Now().Unix(),
				common.ReportSprintf("%s.status", server.InstanceName):                "jobman_failed",
				common.ReportSprintf("%s.is_err", server.InstanceName):                true,
			})
		}
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
		logger.Errorf("创建作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
		return fmt.Errorf("创建作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
	}
	logger.Infof("创建作业平台任务：%s", jobmanTaskID)
	for _, server := range serverList {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.jobs.jobman.last_time", server.InstanceName): time.Now().Unix(),
			common.ReportSprintf("%s.jobs.jobman.status", server.InstanceName):    "started",
		})
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)

	time.Sleep(5 * time.Second)

	tokenHeader := http.Header{}
	if cmdbEnv == "prod" {
		token, _ := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
		tokenHeader.Add("Authorization", "Bearer "+token)
	} else {
		token, _ := cfg.GetIAMCliToken(cfg.GetJobmanConfig().TestIAMEnv)
		tokenHeader.Add("Authorization", "Bearer "+token)
	}
	// 查询任务详细信息
	taskQueryURL := cfg.GetJobmanConfig().TestURLBase + fmt.Sprintf("/backend/jobman/api/v1/tasks/%s?sStatus=0&page=1&size=2000", jobmanTaskID)
	if cmdbEnv == "prod" {
		taskQueryURL = cfg.GetJobmanConfig().ProdURLBase + fmt.Sprintf("/backend/jobman/api/v1/tasks/%s?sStatus=0&page=1&size=2000", jobmanTaskID)
	}
	queryRetryCount := 0
	errorInstance := []string{}
	for {
		if time.Now().After(timeout) {
			return fmt.Errorf("作业平台检查执行超时")
		}
		if queryRetryCount == 3 {
			logger.Errorf("调用作业平台获取任务执行信息失败达3次，不再尝试")
			return fmt.Errorf("调用作业平台获取任务执行信息失败达3次，不再尝试")
		}
		taskQueryResp, _, err := utils.HTTPGet(ctx, taskQueryURL, tokenHeader, nil, 5*time.Second)
		taskQueryCode := gjson.GetBytes(taskQueryResp, "code").Int()
		if err != nil || taskQueryCode != 2000 {
			logger.Errorf("查询作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
			queryRetryCount++
			continue
		}
		loadingFlag := false
		errorInstance = []string{}
		taskQueryResult := gjson.GetBytes(taskQueryResp, "data.listData.list")
		taskQueryResult.ForEach(func(key, value gjson.Result) bool {
			serverObj := agentMap[value.Get("agentId").String()]
			valid, loading := parseCheckLog(logger, serverObj, value.Raw, platform)
			if loading {
				loadingFlag = true
				logger.Infof("确认作业平台结果-执行中：%+v", serverObj)
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.jobs.jobman.last_time", serverObj.InstanceName): time.Now().Unix(),
					common.ReportSprintf("%s.jobs.jobman.status", serverObj.InstanceName):    "executing",
				})
			} else if valid {
				logger.Infof("确认作业平台结果-符合预期：%+v", serverObj)
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.jobs.jobman.last_time", serverObj.InstanceName):  time.Now().Unix(),
					common.ReportSprintf("%s.jobs.jobman.status", serverObj.InstanceName):     "success",
					common.ReportSprintf("%s.jobs.jobman.is_success", serverObj.InstanceName): true,
					common.ReportSprintf("%s.jobs.jobman.is_err", serverObj.InstanceName):     false,
					common.ReportSprintf("%s.last_time", serverObj.InstanceName):              time.Now().Unix(),
					common.ReportSprintf("%s.status", serverObj.InstanceName):                 "agent_verified",
					common.ReportSprintf("%s.is_success", serverObj.InstanceName):             true,
					common.ReportSprintf("%s.is_err", serverObj.InstanceName):                 false,
				})
			} else {
				errorInstance = append(errorInstance, serverObj.InstanceID)
				logger.Errorf("确认作业平台结果-不通过，server: %+v, jobman_result:%s", serverObj, value.Raw)
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.jobs.jobman.last_time", serverObj.InstanceName): time.Now().Unix(),
					common.ReportSprintf("%s.jobs.jobman.status", serverObj.InstanceName):    "not_valid",
					common.ReportSprintf("%s.jobs.jobman.is_err", serverObj.InstanceName):    true,
					common.ReportSprintf("%s.last_time", serverObj.InstanceName):             time.Now().Unix(),
					common.ReportSprintf("%s.status", serverObj.InstanceName):                "jobman_check_invalid",
					common.ReportSprintf("%s.is_err", serverObj.InstanceName):                true,
				})
			}
			return true
		})
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
		if loadingFlag {
			logger.Infof("作业平台尚有任务未执行完，10秒后再次查询")
			time.Sleep(10 * time.Second)
			continue
		} else {
			break
		}
	}
	if len(errorInstance) > 0 {
		return fmt.Errorf("以下实例无法通过作业平台任务校验：%v", errorInstance)
	}
	return nil
}

func generateJobmanBody(name string, env string, serverList []Server, jobType, jobCmd string) string {
	serverListObj := []map[string]interface{}{}
	for _, server := range serverList {
		o := map[string]interface{}{}
		o["AgentId"] = server.AgentID
		o["agentId"] = server.AgentID
		o["ops_agent_id"] = server.AgentID
		o["Host"] = server.HostName
		o["InstanceName"] = server.InstanceName
		o["Name"] = server.InstanceID
		o["instance_id"] = server.InstanceID
		o["private_address"] = []string{server.PrivateAddress}
		serverListObj = append(serverListObj, o)
	}
	serverListBin, _ := json.Marshal(serverListObj)
	result := ""
	result, _ = sjson.Set(result, "account", "root")
	if env == "prod" {
		result, _ = sjson.Set(result, "accountId", cfg.GetJobmanConfig().ProdRootAccountID)
	} else {
		result, _ = sjson.Set(result, "accountId", cfg.GetJobmanConfig().TestRootAccountID)
	}
	result, _ = sjson.Set(result, "jobType", jobType)
	result, _ = sjson.Set(result, "content", jobCmd)
	result, _ = sjson.Set(result, "name", name)
	result, _ = sjson.Set(result, "sandbox", false)
	result, _ = sjson.SetRaw(result, "serverList", string(serverListBin))
	result, _ = sjson.Set(result, "timeOut", 15)
	return result
}

// UpdateAgentOnJobman ...
func UpdateAgentOnJobman(ctx context.Context, logger *logrus.Logger, cmdbEnv string, hosts []*entity.HostResource, binaryURL string) error {
	var agentIDs []string
	for _, host := range hosts {
		agentIDs = append(agentIDs, host.AgentID)
	}
	// 准备
	jobmanConfig := cfg.GetJobmanConfig()
	if !jobmanConfig.Enable {
		return errors.New("配置未启用作业平台...无法更新agent")
	}
	logger.Infof("作业平台(%s)更新agent", cmdbEnv)

	// 构造发送任务结构体
	tokenHeader := http.Header{}
	updateAgentURL := ""
	prodToken, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
	if err != nil {
		return err
	}
	testToken, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().TestIAMEnv)
	if err != nil {
		return err
	}

	type UpdateAgentsReq struct {
		AgentIds  []string `json:"agent_ids"`
		BinaryURL string   `json:"binary_url"`
	}

	body, _ := json.Marshal(UpdateAgentsReq{BinaryURL: binaryURL, AgentIds: agentIDs})

	if cmdbEnv == "prod" {
		tokenHeader.Add("Authorization", "Bearer "+prodToken)
		updateAgentURL = cfg.GetJobmanConfig().ProdURLBase + "/backend/jobman/api/v5/agent/update"
	} else {
		tokenHeader.Add("Authorization", "Bearer "+testToken)
		updateAgentURL = cfg.GetJobmanConfig().TestURLBase + "/backend/jobman/api/v5/agent/update"
	}
	tokenHeader.Add("Content-Type", "application/json")

	logger.Infof("请求作业平台--url:%s, body:%s", updateAgentURL, string(body))
	resp, code, err := utils.HTTPPost(ctx, updateAgentURL, tokenHeader, body, 5*time.Second)
	logger.Infof("code: %d, msg: %s", code, string(resp))
	if err != nil {
		return fmt.Errorf("请求作业平台更新agent失败, error: %v", err)
	}
	if code != 200 {
		return fmt.Errorf("请求作业平台更新agent失败, code: %d, msg: %s", code, string(resp))
	}
	logger.Infof("更新agent发送成功, 等待回调")

	return nil
}

// InstallAgentOnJobman ...
func InstallAgentOnJobman(ctx context.Context, password string, command string, logger *logrus.Logger, orderID string, cmdbEnv string, maxTime time.Duration) error {
	// 准备
	timeout := time.Now().Add(maxTime)
	jobmanConfig := cfg.GetJobmanConfig()
	if !jobmanConfig.Enable {
		return errors.New("配置未启用作业平台任务下发验证...无法安装agent")
	}
	server := Server{
		AgentID:        jobmanConfig.OfficeProxyAgentID,
		HostName:       "office-agent-proxy",
		InstanceID:     "office-agent-proxy",
		PrivateAddress: "",
	}
	logger.Infof("作业平台(%s)安装agent", cmdbEnv)

	// 构造发送任务结构体
	tokenHeader := http.Header{}
	createJobURL, createJobBody := "", ""
	prodToken, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
	if err != nil {
		return err
	}
	testToken, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().TestIAMEnv)
	if err != nil {
		return err
	}
	if cmdbEnv == "prod" {
		tokenHeader.Add("Authorization", "Bearer "+prodToken)
		createJobURL = cfg.GetJobmanConfig().ProdURLBase + "/backend/jobman/api/v1/tasks"
		createJobBody = generateJobmanInstallAgentBody(orderID, password, command, cmdbEnv, server)
	} else {
		tokenHeader.Add("Authorization", "Bearer "+testToken)
		createJobURL = cfg.GetJobmanConfig().TestURLBase + "/backend/jobman/api/v1/tasks"
		createJobBody = generateJobmanInstallAgentBody(orderID, password, command, cmdbEnv, server)
	}

	createJobResp, _, err := utils.HTTPPost(ctx, createJobURL, tokenHeader, []byte(createJobBody), 5*time.Second)
	jobmanTaskID := gjson.GetBytes(createJobResp, "data.taskId").String()
	if err != nil || jobmanTaskID == "" {
		logger.Errorf("创建作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
		return fmt.Errorf("创建作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
	}
	logger.Infof("创建作业平台任务：%s", jobmanTaskID)

	time.Sleep(10 * time.Second)
	// 查询任务详细信息
	taskQueryURL := cfg.GetJobmanConfig().TestURLBase + fmt.Sprintf("/backend/jobman/api/v1/tasks/%s?sStatus=0&page=1&size=2000", jobmanTaskID)
	if cmdbEnv == "prod" {
		taskQueryURL = cfg.GetJobmanConfig().ProdURLBase + fmt.Sprintf("/backend/jobman/api/v1/tasks/%s?sStatus=0&page=1&size=2000", jobmanTaskID)
	}
	queryRetryCount := 0
	var errorInstance []string
	for {
		if time.Now().After(timeout) {
			return fmt.Errorf("作业平台检查执行超时")
		}
		if queryRetryCount == 3 {
			logger.Errorf("调用作业平台获取任务执行信息失败达3次，不再尝试")
			return fmt.Errorf("调用作业平台获取任务执行信息失败达3次，不再尝试")
		}
		taskQueryResp, _, err := utils.HTTPGet(ctx, taskQueryURL, tokenHeader, nil, 5*time.Second)
		taskQueryCode := gjson.GetBytes(taskQueryResp, "code").Int()
		if err != nil || taskQueryCode != 2000 {
			logger.Errorf("查询作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
			queryRetryCount++
			continue
		}
		loadingFlag := false
		errorInstance = []string{}
		taskQueryResult := gjson.GetBytes(taskQueryResp, "data.listData.list")
		taskQueryResult.ForEach(func(key, value gjson.Result) bool {
			valid, loading := checkJobStatus(value.Raw)
			if loading {
				loadingFlag = true
				logger.Infof("确认作业平台结果-执行中：%+v", server)
			} else if valid {
				logger.Infof("确认作业平台结果-符合预期：%+v", server)
			} else {
				errorInstance = append(errorInstance, server.InstanceID)
				logger.Errorf("确认作业平台结果-不通过，server: %+v, jobman_result:%s", server, value.Raw)
			}
			return true
		})
		if loadingFlag {
			logger.Infof("作业平台尚有任务未执行完，10秒后再次查询")
			time.Sleep(10 * time.Second)
			continue
		} else {
			break
		}
	}
	if len(errorInstance) > 0 {
		return fmt.Errorf("以下实例无法通过作业平台任务校验：%v", errorInstance)
	}
	return nil
}

func generateJobmanInstallAgentBody(orderID string, password string, command string, cmdbEnv string, server Server) string {
	var fastExecParamObj []map[string]interface{}
	fastExecParamObj = append(fastExecParamObj, map[string]interface{}{
		"name":     "password",
		"value":    password,
		"isSecret": true,
	})
	fastExecParamBin, _ := json.Marshal(fastExecParamObj)
	var serverListObj []map[string]interface{}
	o := map[string]interface{}{}
	o["AgentId"] = server.AgentID
	o["agentId"] = server.AgentID
	o["ops_agent_id"] = server.AgentID
	o["Host"] = server.HostName
	o["InstanceName"] = server.InstanceName
	o["Name"] = server.InstanceID
	o["instance_id"] = server.InstanceID
	o["private_address"] = []string{server.PrivateAddress}
	serverListObj = append(serverListObj, o)
	serverListBin, _ := json.Marshal(serverListObj)
	result := ""
	result, _ = sjson.Set(result, "account", "root")
	if cmdbEnv == "prod" {
		result, _ = sjson.Set(result, "accountId", cfg.GetJobmanConfig().ProdRootAccountID)
	} else {
		result, _ = sjson.Set(result, "accountId", cfg.GetJobmanConfig().TestRootAccountID)
	}
	result, _ = sjson.Set(result, "content", command)
	result, _ = sjson.Set(result, "jobType", "shell")
	result, _ = sjson.Set(result, "name", "cloudman_installagent_"+orderID)
	result, _ = sjson.Set(result, "sandbox", false)
	result, _ = sjson.SetRaw(result, "serverList", string(serverListBin))
	result, _ = sjson.Set(result, "timeOut", 60)
	result, _ = sjson.Set(result, "fast_exec_param", string(fastExecParamBin))
	return result
}

// CurlOnJobman ...
func CurlOnJobman(ctx context.Context, req *http.Request, maxTime time.Duration) (string, error) {
	// 准备
	timeout := time.Now().Add(maxTime)
	jobmanConfig := cfg.GetJobmanConfig()
	if !jobmanConfig.Enable {
		return "", errors.New("配置未启用作业平台...无法转发curl请求")
	}
	server := Server{
		AgentID:        jobmanConfig.OfficeProxyAgentID,
		HostName:       "office-agent-proxy",
		InstanceID:     "office-agent-proxy",
		PrivateAddress: "",
	}

	// 构造发送任务结构体
	tokenHeader := http.Header{}
	createJobURL, createJobBody := "", ""
	prodToken, err := cfg.GetIAMCliToken(cfg.GetJobmanConfig().ProdIAMEnv)
	if err != nil {
		return "", err
	}
	tokenHeader.Add("Authorization", "Bearer "+prodToken)
	createJobURL = cfg.GetJobmanConfig().ProdURLBase + "/backend/jobman/api/v1/tasks"
	createJobBody = generateJobmanCurlBody(req, server)

	createJobResp, code, err := utils.HTTPPost(ctx, createJobURL, tokenHeader, []byte(createJobBody), 5*time.Second)
	if code != 200 {
		return "", fmt.Errorf("作业平台返回http错误码: %d", code)
	}
	jobmanTaskID := gjson.GetBytes(createJobResp, "data.taskId").String()
	if err != nil || jobmanTaskID == "" {
		return "", fmt.Errorf("创建作业平台任务失败, error: %v, jobmanTaskID: %v", err, jobmanTaskID)
	}

	time.Sleep(200 * time.Millisecond)
	// 查询任务详细信息
	queryRetryCount := 0
	for {
		tasksQueryURL := cfg.GetJobmanConfig().ProdURLBase + fmt.Sprintf("/backend/jobman/api/v1/tasks/%s", jobmanTaskID)
		tasksQueryResp, _, err := utils.HTTPGet(ctx, tasksQueryURL, tokenHeader, nil, 2*time.Second)
		if err != nil {
			return "", err
		}
		tasksQueryResult := gjson.GetBytes(tasksQueryResp, "data.listData.list")
		taskRes := struct {
			SStatus int32  `json:"sStatus"`
			Explain string `json:"explain"`
		}{}
		tasksQueryResult.ForEach(func(key, value gjson.Result) bool {
			_ = json.Unmarshal([]byte(value.Raw), &taskRes)
			return true
		})

		if time.Now().After(timeout) {
			return "", fmt.Errorf("作业平台检查执行超时")
		}
		if queryRetryCount == 10 {
			return "", fmt.Errorf("调用作业平台获取任务执行信息失败达10次，不再尝试")
		}

		switch taskRes.SStatus {
		case -1:
		case 1:
		case 2:
			if len(taskRes.Explain) == 0 {
				return "", errors.New("作业平台返回日志为空")
			}
			if taskRes.Explain[0] == '{' || taskRes.Explain[0] == '[' {
				return taskRes.Explain, nil
			}
		case 3:
			return "", errors.New("作业平台任务执行失败")
		case 4:
			return "", errors.New("作业平台返回NulNode错误")
		case 5:
			return "", errors.New("作业平台下发agent失败")
		default:
			return "", errors.New("作业平台返回未知错误码")
		}
		queryRetryCount++
		time.Sleep(time.Duration(200*queryRetryCount) * time.Millisecond)
	}
}

func generateJobmanCurlBody(req *http.Request, server Server) string {
	var serverListObj []map[string]interface{}
	o := map[string]interface{}{}
	o["AgentId"] = server.AgentID
	o["agentId"] = server.AgentID
	o["ops_agent_id"] = server.AgentID
	o["Host"] = server.HostName
	o["InstanceName"] = server.InstanceName
	o["Name"] = server.InstanceID
	o["instance_id"] = server.InstanceID
	o["private_address"] = []string{server.PrivateAddress}
	serverListObj = append(serverListObj, o)
	serverListBin, _ := json.Marshal(serverListObj)
	result := ""
	result, _ = sjson.Set(result, "account", "root")
	result, _ = sjson.Set(result, "accountId", cfg.GetJobmanConfig().ProdRootAccountID)
	command, _ := http2curl.GetCurlCommand(req)
	cmdStr := command.String()
	index := strings.Index(cmdStr, "curl") + len("curl")
	cmdStr = cmdStr[0:index] + " -s -k" + cmdStr[index:]
	result, _ = sjson.Set(result, "content", cmdStr)
	result, _ = sjson.Set(result, "jobType", "shell")
	result, _ = sjson.Set(result, "name", fmt.Sprintf("curl %s %s", req.Method, req.URL.String()))
	result, _ = sjson.Set(result, "sandbox", false)
	result, _ = sjson.SetRaw(result, "serverList", string(serverListBin))
	result, _ = sjson.Set(result, "timeOut", 60)
	return result
}

func deepCopy(src interface{}, dst interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, dst)
}

var r = regexp.MustCompile(`hostname:(.*),ip:(.*)`)

type logResult struct {
	Status  int    `json:"sStatus"`
	Explain string `json:"explain"`
}

func checkJobStatus(logJSON string) (valid bool, loading bool) {
	logObj := logResult{}
	err := json.Unmarshal([]byte(logJSON), &logObj)
	if err != nil {
		return false, false
	}
	if logObj.Status == 1 {
		return false, true
	} else if logObj.Status == 3 {
		return false, false
	}
	return true, false
}

func parseCheckLog(logger *logrus.Logger, server Server, logJSON string, platform string) (valid bool, loading bool) {
	valid, loading = checkJobStatus(logJSON)
	if !valid || loading {
		return valid, loading
	}

	logObj := logResult{}
	err := json.Unmarshal([]byte(logJSON), &logObj)
	if err != nil {
		return false, false
	}
	if platform == "windows" {
		ips := strings.Split(logObj.Explain, ",")
		ipMatch := false
		for _, ipPair := range ips {
			ipArr := strings.Split(ipPair, ":")
			if len(ipArr) == 2 && strings.TrimSpace(ipArr[1]) == server.PrivateAddress {
				ipMatch = true
				break
			}
		}
		if !ipMatch {
			return false, false
		}
	} else {
		m := r.FindStringSubmatch(logObj.Explain)
		if len(m) != 3 {
			return false, false
		}
		hostName := strings.TrimSpace(m[1])
		ipSet := strings.Split(m[2], " ")
		ipMatch := false
		if hostName != server.HostName {
			logger.Warnf("云端实例名称与主机hostname不一致；实例名称：%s，主机名：%s", server.HostName, hostName)
			// 实例名称与主机名称不一致时，不阻断
			// return false, false
		}
		for _, ip := range ipSet {
			if strings.TrimSpace(ip) == server.PrivateAddress {
				ipMatch = true
			}
		}
		if !ipMatch {
			return false, false
		}
	}
	return true, false
}
