package jumpserversdk

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
)

// Host 描述虚拟机实例
type Host struct {
	ID        string   `json:"id"`
	HostName  string   `json:"hostname"`
	IP        string   `json:"ip"`
	Protocols []string `json:"protocols"`
	OS        string   `json:"os"`
	Domain    string   `json:"domain"`
	Platform  string   `json:"platform"`
	Comment   string   `json:"comment"`
	OrgID     string   `json:"org_id"`
	OrgName   string   `json:"org_name"`
	IsActive  bool     `json:"is_active"`
}

// HostRes ...
type HostRes struct {
	Count    int     `json:"count"`
	Next     string  `json:"next"`
	Previous string  `json:"previous"`
	Results  []*Host `json:"results"`
}

// ListHosts 虚拟机列表，分页接口，不支持过滤字段
func (m JumpserverClient) ListHosts(server string, offset int, limit int) ([]*Host, error) {
	res := &HostRes{}
	err := m.reqJumpserver("GET", server, fmt.Sprintf("/api/v1/perms/users/assets/?offset=%d&limit=%d", offset, limit), http.Header{}, nil, res)
	if err != nil {
		return nil, err
	}
	return res.Results, nil
}

// ExecuteCommandReq ...
type ExecuteCommandReq struct {
	Command string   `json:"command"`
	RunAs   string   `json:"run_as"`
	Hosts   []string `json:"hosts"`
}

// ExecuteCommandRes ...
type ExecuteCommandRes struct {
	ID           string `json:"id"`
	LogURL       string `json:"log_url"`
	IsFinished   bool   `json:"is_finished"`
	DateCreated  string `json:"date_created"`
	DateFinished string `json:"date_finished"`
}

// ExecuteCommand ...
func (m JumpserverClient) ExecuteCommand(server string, req *ExecuteCommandReq) (*ExecuteCommandRes, error) {
	reqBin, _ := json.Marshal(req)
	res := &ExecuteCommandRes{}
	header := http.Header{}
	header.Set("Content-Disposition", "attachment; filename=\"test.txt\"")
	err := m.reqJumpserver("POST", server, "/api/v1/ops/command-executions/", header, reqBin, res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetCommandLogReq ...
type GetCommandLogReq struct {
	ID string `json:"id"`
}

// GetCommandLogRes ...
type GetCommandLogRes struct {
	Data  string `json:"data"`
	IsEnd bool   `json:"is_end"`
	Mark  string `json:"mark"`
}

// GetCommandLog ...
func (m JumpserverClient) GetCommandLog(server string, req *GetCommandLogReq) (*GetCommandLogRes, error) {
	reqBin, _ := json.Marshal(req)
	res := &GetCommandLogRes{}
	err := m.reqJumpserver("GET", server, fmt.Sprintf("/api/v1/ops/celery/task/%s/log/", req.ID), http.Header{}, reqBin, res)
	if err != nil {
		return nil, err
	}
	if strings.Contains(res.Data, "任务结束") {
		res.IsEnd = true
	}
	return res, nil
}

// GetHostSystemUsersReq ...
type GetHostSystemUsersReq struct {
	Host string `json:"host"`
}

// GetHostSystemUsersRes ...
type GetHostSystemUsersRes struct {
	ID                   string   `json:"id"`
	Name                 string   `json:"name"`
	Username             string   `json:"username"`
	Priority             int      `json:"priority"`
	Protocol             string   `json:"protocol"`
	LoginMode            string   `json:"login_mode"`
	SftpRoot             string   `json:"sftp_root"`
	UsernameSameWithUser bool     `json:"false"`
	SuEnabled            bool     `json:"su_enabled"`
	SuFrom               string   `json:"su_from"`
	Actions              []string `json:"actions"`
}

// GetHostSystemUsers ...
func (m JumpserverClient) GetHostSystemUsers(server string, req *GetHostSystemUsersReq) (*GetHostSystemUsersRes, error) {
	var res []*GetHostSystemUsersRes
	err := m.reqJumpserver("GET", server, fmt.Sprintf("/api/v1/perms/users/assets/%s/system-users/", req.Host), http.Header{}, nil, &res)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return nil, errors.New("no system user found")
	}
	return res[0], nil
}

// GetUserProfileRes ...
type GetUserProfileRes struct {
	ID string `json:"id"`
}

// GetUserProfile ...
func (m JumpserverClient) GetUserProfile(server string) (*GetUserProfileRes, error) {
	res := &GetUserProfileRes{}
	err := m.reqJumpserver("GET", server, "/api/v1/users/profile/", http.Header{}, nil, res)
	if err != nil {
		return nil, err
	}
	if res.ID == "" {
		return nil, errors.New("get null user profile")
	}
	return res, nil
}
