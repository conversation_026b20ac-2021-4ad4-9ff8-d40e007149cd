package jumpserversdk

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"time"

	"golang.org/x/net/context"
	"gopkg.in/twindagger/httpsig.v1"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jobman"
)

// JumpserverClient 米哈游计算平台sdk客户端
type JumpserverClient struct {
	ak string
	sk string
}

// NewJumpserverSdk 米哈游计算平台sdk客户端实例创建
func NewJumpserverSdk(ak, sk string) *JumpserverClient {
	return &JumpserverClient{
		ak: ak,
		sk: sk,
	}
}

type sigAuth struct {
	KeyID    string
	SecretID string
}

func (auth *sigAuth) sign(r *http.Request) error {
	headers := []string{"(request-target)", "date"}

	signer, err := httpsig.NewRequestSigner(auth.KeyID, auth.SecretID, "hmac-sha256")
	if err != nil {
		return err
	}
	return signer.SignRequest(r, headers, nil)
}

func (m JumpserverClient) reqJumpserver(method string, server string, api string, header http.Header, data []byte, out interface{}) error {
	auth := &sigAuth{
		KeyID:    m.ak,
		SecretID: m.sk,
	}
	if server[len(server)-1] == '/' {
		server = server[0 : len(server)-1]
	}
	reqURL := server + api
	gmtFmt := "Mon, 02 Jan 2006 15:04:05 GMT"
	// will remove if jumpserver fix this
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	reader := bytes.NewReader(nil)
	if data != nil {
		reader = bytes.NewReader(data)
	}
	req, err := http.NewRequest(method, reqURL, reader)
	req.Header = header
	req.Header.Add("Date", time.Now().Format(gmtFmt))
	req.Header.Add("Accept", "application/json")
	req.Header.Add("X-JMS-ORG", "00000000-0000-0000-0000-000000000000")
	req.Header.Add("Content-Type", "application/json")
	if err != nil {
		return err
	}
	if err := auth.sign(req); err != nil {
		return err
	}
	opsAgentCfg := cfg.GetOpsAgentConf()
	body := []byte{}
	if opsAgentCfg.JumpserverReqType == "jobman" {
		res, err := jobman.CurlOnJobman(context.Background(), req, time.Second*10)
		if err != nil {
			return err
		}
		body = []byte(res)
	} else {
		resp, err := client.Do(req)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		body, err = ioutil.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		json.MarshalIndent(body, "", "    ")
	}

	errResp := struct {
		Error string `json:"error"`
	}{}

	_ = json.Unmarshal(body, &errResp)
	if errResp.Error != "" {
		return errors.New(errResp.Error)
	}
	err = json.Unmarshal(body, out)
	if err != nil {
		return err
	}
	return nil
}
