package constant

// Redis规格
const (
	CacheR6gLagre = "cache.r6g.large"
	CacheR6gXlarge = "cache.r6g.xlarge"
	CacheR6g2xlarge = "cache.r6g.2xlarge"
	CacheR6g4xlarge = "cache.r6g.4xlarge"
	CacheR6g8xlarge = "cache.r6g.8xlarge"
	CacheR6g12xlarge = "cache.r6g.12xlarge"
	CacheR6g16xlarge = "cache.r6g.16xlarge"
)

// CacheMemMap CacheMemMap
var CacheMemMap = map[string]int64{
	CacheR6gLagre: 13, // "13.07 GB",
	CacheR6gXlarge: 26, //"26.32 GB",
	CacheR6g2xlarge: 52, //"52.82 GB",
	CacheR6g4xlarge: 105, // "105.81 GB",
	CacheR6g8xlarge: 209, //"209.55 GB",
	CacheR6g12xlarge: 317, //"317.77 GB",
	CacheR6g16xlarge: 419, //"419.09 GB",
}

// CacheBandwidthMap CacheBandwidthMap
var CacheBandwidthMap = map[string]int64 {
	CacheR6gLagre: 10,
	CacheR6gXlarge: 10,
	CacheR6g2xlarge: 10,
	CacheR6g4xlarge: 10,
	CacheR6g8xlarge: 12,
	CacheR6g12xlarge: 20,
	CacheR6g16xlarge: 25,
}