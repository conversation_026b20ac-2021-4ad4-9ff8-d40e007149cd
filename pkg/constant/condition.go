package constant // mongo操作符
const (
	// MongoLIKE the db operator
	MongoLIKE = "$regex"
	// MongoIN in
	MongoIN = "$in"
	// MongoOR or
	MongoOR = "$or"
	// MongoAND and
	MongoAND = "$and"
	// MongoEQ eq
	MongoEQ = "$eq"
	// MongoNE ne
	MongoNE = "$ne"
	// MongoNIN nin
	MongoNIN = "$nin"
	// MongoLT lt
	MongoLT = "$lt"
	// MongoLTE 小于等于
	MongoLTE = "$lte"
	// MongoGT the db operator
	MongoGT = "$gt"
	// MongoGTE the db operator
	MongoGTE = "$gte"
	// MongoExists the db operator
	MongoExists = "$exists"
	// MongoNot the db operator
	MongoNot = "$not"
	// MongoOPTIONS options
	MongoOPTIONS = "$options"
	// MongoCount the db operator
	MongoCount = "$count"
	// MongoGroup the db operator
	MongoGroup = "$group"
	// MongoMatch the db operator
	MongoMatch = "$match"
	// MongoSum the db operator
	MongoSum = "$sum"
	// MongoPush the db operator
	MongoPush = "$push"
	// MongoUNSET the db operator
	MongoUNSET = "$unset"
	// MongoAddToSet The $addToSet operator adds a value to an array unless the value is already present, in which case $addToSet does nothing to that array.
	MongoAddToSet = "$addToSet"
	// MongoPull The $pull operator removes from an existing array all instances of a value or values that match a specified condition.
	MongoPull = "$pull"
	// MongoAll matches arrays that contain all elements specified in the query.
	MongoAll = "$all"
	// MongoProject passes along the documents with the requested fields to the next stage in the pipeline
	MongoProject = "$project"
	// MongoSize counts and returns the total number of items in an array
	MongoSize = "$size"
)
