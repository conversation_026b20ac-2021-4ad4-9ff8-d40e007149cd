package constant

// ping结果
const (
	WaitPing = 0 // 未执行ping测试
	pinging  = 1
	PingPong = 2 // ping测试成功
	PingErr  = 3 // ping测试失败
)

// 任务状态
const (
	TaskFormRunning int32 = -1 // 任务待审批
	TaskWaitRun     int32 = 0  // 任务未执行
	TaskRunning     int32 = 1  // 任务执行中
	TaskSuccess     int32 = 2  // 任务执行成功
	TaskErr         int32 = 3  // 任务执行失败
)

// agent状态
const (
	AgentRunning      = 1
	AgentLost         = 2
	AgentNoReport     = 3
	AgentUpdated      = 4
	AgentUpdateFailed = 5
	AgentUpdating     = 6
)

// TaskStatusMap 任务状态map
var TaskStatusMap = map[string]int32{"审批中": -1, "未执行": 0, "执行中": 1, "执行成功": 2, "执行失败": 3}

// 资源状态
const (
	ResourceRunning = "Running" // 资源运行中
	ResourceStopped = "Stopped" // 资源已停止
	ResourcePending = "Pending" // 资源准备中
)

// Account
const (
	AllAccountID   = "allAccount" // 代表所有Account的AccountID
	AllAccountName = "所有云厂商"      // 代表所有Account的AccountName
)

// 内置同步任务
const (
	SyncMonitorAgetnStatus     = "RefreshMonitorStatus"
	SyncAgentStatus            = "RefreshAgentStatus"
	SyncMonitorAgetnStatusName = "同步监控状态"
	SyncAgentStatusName        = "同步作业Agent状态"
)

const (
	// HookCtxMeta hock传递过程中的ctx-key
	HookCtxMeta = "hook_meta"
	// OrderCtxMeta order传递过程中的ctx-key
	OrderCtxMeta = "order_meta"
)
