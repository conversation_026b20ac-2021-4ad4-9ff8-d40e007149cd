package tpl

import (
	"bytes"
	"context"
	"encoding/json"
	"text/template"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// GenCreateResFormDescribe 生成表单摘要信息
func GenCreateResFormDescribe(ctx context.Context, ispType string, data []byte) (string, error) {
	switch ispType {
	case "aliyun":
		return createAliyunHostResDescribe(ctx, data)
	case "aws":
	}
	return "", nil
}

func createAliyunHostResDescribe(ctx context.Context, data []byte) (string, error) {
	var aliInput entity.HostResource
	// todo: 利用生成表格的方法实现
	err := json.Unmarshal(data, &aliInput)
	if err != nil {
		return "", err
	}
	var mdTpl = `
|字段|值|
|--|--|
{{ range $k, $v := .Map }}| {{$k}} | {{$v}} |
{{ end }}
`
	var buf bytes.Buffer
	tt := struct {
		Map map[string]string
	}{Map: map[string]string{"aaaa": "1111", "aa1a": "11112"}}
	render, err := template.New("mdTpl").Parse(mdTpl)
	if err != nil {
		return "", err
	}
	err = render.Execute(&buf, tt)
	if err != nil {
		return "", err
	}
	return buf.String(), err
}
