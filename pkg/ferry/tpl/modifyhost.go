package tpl

import (
	"bytes"
	"context"
	"encoding/json"
	ecs20140526 "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"text/template"
)

// GenModifyHostResFormDescribe 生成修改表单详情
func GenModifyHostResFormDescribe(ctx context.Context, ispType string, data []byte) (string, error) {
	switch ispType {
	// case "aliyun":
	// 	pass
	// case "aws":
	// 	pass
	// default:
	// 	pass
	}

	return "", nil
}

// ModifyAliyunHostResDescribe 修改阿里云主机
func ModifyAliyunHostResDescribe(ctx context.Context, data []byte) (string, error) {
	var aliInput *ecs20140526.RunInstancesRequest
	err := json.Unmarshal(data, &aliInput)
	if err != nil {
		return "", err
	}
	var mdTpl = `
|字段|值|
|--|--|
{{ range $k, $v := .Map }}| {{$k}} | {{$v}} |
{{ end }}
`
	var buf bytes.Buffer
	tt := struct {
		Map map[string]string
	}{Map: map[string]string{"aaaa": "1111", "aa1a": "11112"}}
	render, err := template.New("mdTpl").Parse(mdTpl)
	if err != nil {
		return "", err
	}
	err = render.Execute(&buf, tt)
	if err != nil {
		return "", err
	}
	return buf.String(), err
}
