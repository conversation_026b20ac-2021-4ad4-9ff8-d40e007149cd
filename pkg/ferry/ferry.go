package ferry

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// TPL title:工单标题;flowID:流程ID,creator:创建者
var TPL = `
{
    "title": "{{ .formTitle }}",
    "priority": 1,
    "process": {{ .formFlowID }},
    "creator_by_third_app":"{{ .formCreator }}",
    "tpls": {
        "form_structure": [
            {
                "list": [
                    {
                        "key": "1642496982000_44267",
                        "name": "申请人",
                        "type": "input",
                        "model": "formCreator",
                        "options": {
                            "dataType": "string",
                            "disabled": true,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": false
                        }
                    }, {
                        "key": "1642496982000_44268",
                        "name": "资源类型",
                        "type": "input",
                        "model": "resType",
                        "options": {
                            "dataType": "string",
                            "disabled": true,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": false
                        }
                    }, {
                        "key": "1642496982000_44269",
                        "name": "资源操作",
                        "type": "input",
                        "model": "action",
                        "options": {
                            "dataType": "string",
                            "disabled": true,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": false
                        }
                    }, {
                        "key": "1642496982000_44270",
                        "name": "所属云厂商",
                        "type": "input",
                        "model": "ispName",
                        "options": {
                            "dataType": "string",
                            "disabled": true,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": false
                        }
                    }, {
                        "key": "1642496982000_44271",
                        "name": "表单摘要",
                        "type": "textarea",
                        "model": "describe",
                        "options": {
                            "dataType": "string",
                            "disabled": true,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": false
                        }
                    }, {
                        "key": "1642496982000_44272",
                        "name": "详细信息URL",
                        "type": "input",
                        "model": "info",
                        "options": {
                            "dataType": "string",
                            "disabled": true,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": true
                        }
					}, {
                        "key": "1642496982000_44370",
                        "name": "申请理由",
                        "type": "input",
                        "model": "formReason",
                        "options": {
                            "dataType": "string",
                            "disabled": false,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": false
                        }
                    }, {
                        "key": "1642496982000_44371",
                        "name": "资源订单ID",
                        "type": "input",
                        "model": "orderID",
                        "options": {
                            "dataType": "string",
                            "disabled": true,
                            "labelWidthStatus": true,
                            "labelWidthDisabled": false
                        }
                    }
                ],
                "config": {
                    "size": "small",
                    "labelWidth": 100,
                    "customClass": "",
                    "labelPosition": "right"
                },
                "id": 27
            }
        ],
        "form_data": [
            {
				"formCreator": "{{ .formCreator }}",
				"resType": "{{ .resType }}",
                "action": "{{ .action }}",
                "ispName": "{{ .IspName }}",
                "describe": "{{ .describe }}",
				"info": "{{ .info }}",
                "formReason": "{{ .formReason }}",
                "orderID": "{{ .orderID }}"
            }
        ]
    }
}
`

// FormValues 发起工单时所需参数项
type FormValues struct {
	FormTitle   string `json:"formTitle"`
	FormFlowID  string `json:"formFlowID"`
	FormCreator string `json:"formCreator"`
	ResType     string `json:"resType"`
	Action      string `json:"action"`
	IspName     string `json:"IspName"`
	FormReason  string `json:"formReason"`
	Info        string `json:"info"`
	OrderID     string `json:"orderID"`
	Describe    string `json:"describe"`
	// callback/gateway-token由ferry中的任务决定
}

// Client ferry-cli
type Client struct {
	HTTPClient *http.Client
	APIHost    string
	Token      string
}

// NewFerryClient new-ferry-client
func NewFerryClient(host, token string) *Client {
	return &Client{
		HTTPClient: &http.Client{Timeout: 5 * time.Second},
		APIHost:    host,
		Token:      token,
	}
}

// CreateTicket 创建工单
func (cli *Client) CreateTicket(ctx context.Context, value FormValues) (ticketID int, retErr error) {
	params, err := mapstruct.Struct2Map(value)
	if err != nil {
		return 0, err
	}
	data, err := utils.RenderTpl(TPL, params)
	if err != nil {
		return 0, err
	}
	logger.Info(data)

	req, err := http.NewRequest("POST", cli.APIHost+"/backend/ferry/api/v1/work-order-auth/ThirdAppCreate", strings.NewReader(data))
	if err != nil {
		return 0, err
	}
	req.Header.Add("Authorization", "Bearer "+cli.Token)
	req.Header.Add("Content-Type", "application/json")
	resp, err := cli.HTTPClient.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 400 {
		return 0, fmt.Errorf("bad ferry resp status code %d", resp.StatusCode)
	}
	respSchema := &struct {
		Code int    `json:"code"`
		Data int    `json:"data"`
		Msg  string `json:"msg"`
	}{}
	respData, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(respData, respSchema)
	if err != nil {
		return 0, err
	}
	if respSchema.Code != 200 {
		return 0, fmt.Errorf("bad ferry resp status %d, errMsg: %s", respSchema.Code, respSchema.Msg)
	}
	return respSchema.Data, nil
}
