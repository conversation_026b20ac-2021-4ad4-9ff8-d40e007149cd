package colorlog

import (
	"fmt"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/qlog/color"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

func printTimeNow() string {
	// 时间使用黄色打印
	wirteTime := time.Now().Format(utils.TimeCommon)
	return color.PrintfWithReturn("@y" + wirteTime)
}

// Error 错误颜色
func Error(str string) string {
	return fmt.Sprintf("[%s] [%s] - %s", printTimeNow(), color.PrintfWithReturn("@rERROR"), str)
}

// INFO 常规消息颜色
func INFO(str string) string {
	return fmt.Sprintf("[%s] [%s] - %s", printTimeNow(), color.PrintfWithReturn("@yINFO"), str)
}
