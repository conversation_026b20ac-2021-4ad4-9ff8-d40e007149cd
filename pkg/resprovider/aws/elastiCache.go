package aws

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	estypes "github.com/aws/aws-sdk-go-v2/service/elasticache/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// ElasticCacheProvider elastiCache
type ElasticCacheProvider struct {
	logger *logrus.Logger

	dryRun    bool
	accountID string
	regionID  string
}

type CreateCacheInput struct {
	CacheSubnetGroupName    string        `json:"CacheSubnetGroupName"`    // 子网分组
	SecurityGroupIds        []string      `json:"SecurityGroupIds"`        // 安全组
	Engine                  string        `json:"Engine"`                  // 引擎类型
	EngineVersion           string        `json:"EngineVersion"`           // 引擎版本
	Category                string        `json:"Category"`                // 实例类型(cluster_on: 集群模式开启, cluster_off: 集群模式关闭)
	NumShards               *int32        `json:"NumShards"`               // 分片数
	NumReplicas             *int32        `json:"NumReplicas"`             // 副本数
	CacheNodeType           string        `json:"CacheNodeType"`           // 实例规格
	CacheParameterGroupName string        `json:"CacheParameterGroupName"` // 参数模板
	AuthToken               string        `json:"AuthToken"`               // 默认密码
	InstanceName            string        `json:"InstanceName"`            // 实例名称
	Tags                    []estypes.Tag `json:"Tags"`                    // 标签
}

func (c CreateCacheInput) GetTags(instanceName string) []estypes.Tag {
	tags := []estypes.Tag{
		{
			Key:   aws.String("nap"),
			Value: aws.String(hostname_util.GetHostNameArea(instanceName)),
		},
		{
			Key:   aws.String("env"),
			Value: aws.String(hostname_util.GetHostNameEnv(instanceName)),
		},
		{
			Key:   aws.String("region"),
			Value: aws.String(hostname_util.GetHostNameRegion(instanceName)),
		},
		{
			Key:   aws.String("module"),
			Value: aws.String(hostname_util.GetHostNameModule(instanceName)),
		},
		{
			Key:   aws.String("region_module"),
			Value: aws.String(hostname_util.GetHostNameRegionModule(instanceName)),
		},
		{
			Key:   aws.String("serial_number"),
			Value: aws.String(hostname_util.GetHostNameRegionModule(instanceName)),
		},
	}
	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	for _, t := range c.Tags {
		if slices.Contains(systemTag, aws.StringValue(t.Key)) {
			continue
		}
		tags = append(tags, estypes.Tag{
			Key:   t.Key,
			Value: t.Value,
		})
	}

	return tags
}

// CreateInstance 创建实例
func (e ElasticCacheProvider) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	var rData CreateCacheInput
	reportStore := common.NewReportInstanceOrderStatus(orderID, e.dryRun)
	err = json.Unmarshal(data, &rData)
	if err != nil {
		e.logger.Errorf("序列化创建参数失败: %s", err.Error())
		return nil, err
	}

	instanceNames, err := cloudutils.GetCloudutils(models.RedisResourceModel).GenInstanceName(
		context.Background(),
		rData.InstanceName,
		1, true, cloudutils.Linux,
	)
	if err != nil {
		return nil, fmt.Errorf("生成数据库集群名失败：%s", err.Error())
	}
	if len(instanceNames) == 0 {
		return nil, fmt.Errorf("生成数据库集群名失败")
	}
	instanceName := instanceNames[0]

	if e.dryRun {
		return nil, nil
	}
	aCtx, aCancel := context.WithTimeout(context.Background(), duration)
	defer aCancel()

	resp := &elasticache.CreateReplicationGroupOutput{}
	reportStore.ReportStart(ctx, instanceName)
	clusterMode := estypes.ClusterModeDisabled
	if rData.Category == "cluster_on" {
		clusterMode = estypes.ClusterModeEnabled
	}
	err = agentsdk.SyncCall(ctx, e.regionID, "redis", "CreateReplicationGroup", e.accountID, []interface{}{aCtx, &elasticache.CreateReplicationGroupInput{
		ReplicationGroupDescription: aws.String(instanceName),
		ReplicationGroupId:          aws.String(instanceName),
		AtRestEncryptionEnabled:     aws.Bool(false),
		AuthToken:                   aws.String(rData.AuthToken),
		AutoMinorVersionUpgrade:     aws.Bool(true),
		AutomaticFailoverEnabled:    aws.Bool(true),
		CacheNodeType:               aws.String(rData.CacheNodeType),
		CacheParameterGroupName:     aws.String(rData.CacheParameterGroupName),
		CacheSubnetGroupName:        aws.String(rData.CacheSubnetGroupName),
		ClusterMode:                 clusterMode,
		Engine:                      aws.String(rData.Engine),
		EngineVersion:               aws.String(rData.EngineVersion),
		MultiAZEnabled:              aws.Bool(true),
		NumCacheClusters:            rData.NumShards,
		Port:                        aws.Int32(6379),
		ReplicasPerNodeGroup:        rData.NumReplicas,
		SecurityGroupIds:            rData.SecurityGroupIds,
		Tags:                        rData.GetTags(instanceName),
	}}, resp)
	if err != nil {
		reportStore.ReportFailed(ctx, instanceName)
		e.logger.Errorf("创建实例失败:%s", err.Error())
		return nil, err
	}
	reportStore.ReportInstanceId(ctx, instanceName, aws.StringValue(resp.ReplicationGroup.ReplicationGroupId))
	reportStore.ReportCustomStatus(ctx, instanceName, "created")
	// 创建实例后需要判断是否正常启动
	e.logger.Infof("实例创建订单提交成功: %+v, 等待60秒后检查启动状态", *resp)
	time.Sleep(1 * time.Minute)
	err = e.createAfterAction(aCtx, resp.ReplicationGroup.ReplicationGroupId)
	if err != nil {
		reportStore.ReportFailed(ctx, instanceName)
		return nil, err
	}
	reportStore.ReportSuccess(ctx, instanceName)
	return []string{aws.StringValue(resp.ReplicationGroup.ReplicationGroupId)}, err
}

func (e ElasticCacheProvider) createAfterAction(ctx context.Context, groupID *string) error {
	// 创建实例后需要判断是否正常启动
	resp := &elasticache.DescribeReplicationGroupsOutput{}
	err := agentsdk.SyncCall(ctx, e.regionID, "redis", "DescribeReplicationGroups", e.accountID, []interface{}{"", &elasticache.DescribeReplicationGroupsInput{
		ReplicationGroupId: groupID,
	}}, resp)
	if err != nil {
		return err
	}
	if aws.StringValue(resp.ReplicationGroups[0].Status) == "available" {
		e.logger.Infof("groupID: %s已准备就绪", aws.StringValue(groupID))
		return nil
	}
	e.logger.Infof("groupID: %s未就绪,30秒后继续查询", aws.StringValue(groupID))
	time.Sleep(30 * time.Second)
	return e.createAfterAction(ctx, groupID)
}

// UpdateInstance 更新实例
func (e ElasticCacheProvider) UpdateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) error {
	var form common.OperateForm
	err := json.Unmarshal(data, &form)
	if err != nil {
		return err
	}

	switch form.Action {
	case "backup":
		realInstances := form.Instances
		reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
		waitCluster := map[string]string{}
		var errCluster []string

		// 重试实例id处理，要确保实际重试的在原始列表中
		if len(form.RetryInstances) != 0 {
			oriInstanceMap := map[string]bool{}
			realRetryInstances := []string{}
			for _, i := range form.Instances {
				oriInstanceMap[i] = true
			}
			for _, r := range form.RetryInstances {
				if oriInstanceMap[r] {
					realRetryInstances = append(realRetryInstances, r)
				}
			}
			e.logger.Infof("检测到重试配置，本次重试的实例列表：%v", realRetryInstances)
			realInstances = realRetryInstances
		} else {
			e.logger.Infoln("开始备份实例:", form.Instances)
		}

		backupTime := time.Now().Format("2006-01-02-15-04")
		for _, clusterID := range realInstances {
			// 仅支持集群备份
			time.Sleep(time.Duration(1) * time.Second)
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.instance_id", clusterID): clusterID,
				common.ReportSprintf("%s.start_time", clusterID):  time.Now().Unix(),
				common.ReportSprintf("%s.status", clusterID):      "preparing",
			})
			snapshotName := fmt.Sprintf("%s-%s", clusterID, backupTime)
			singleResp := &elasticache.CreateSnapshotOutput{}
			err := agentsdk.SyncCall(ctx, e.regionID, "redis", "CreateSnapshot", e.accountID, []interface{}{"", &elasticache.CreateSnapshotInput{
				SnapshotName: aws.String(snapshotName),
				// CacheClusterId:     aws.String(clusterID),
				ReplicationGroupId: aws.String(clusterID),
				Tags:               []estypes.Tag{{Key: aws.String("backup-time"), Value: aws.String(backupTime)}},
			}}, singleResp)
			if err != nil {
				e.logger.Errorf("备份实例(%s)失败:%s", clusterID, err.Error())
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.status", clusterID):    "start_failed",
					common.ReportSprintf("%s.last_time", clusterID): time.Now().Unix(),
					common.ReportSprintf("%s.is_err", clusterID):    true,
				})
				errCluster = append(errCluster, clusterID)
				continue
			}
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", clusterID):                        time.Now().Unix(),
				common.ReportSprintf("%s.status", clusterID):                           "started",
				common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapshotName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapshotName):    "started",
			})
			e.logger.Infof("创建实例(%s)备份任务成功:requestID: %s", clusterID, singleResp.ResultMetadata)
			waitCluster[clusterID] = snapshotName
		}
		reportStore = common.ReportOrderStatusDetail(ctx, orderID, true, reportStore, "", "")

		if len(waitCluster) == 0 {
			e.logger.Errorln("没有实例备份成功")
			return fmt.Errorf("备份失败")
		}

		e.logger.Infof("实例:%s, 创建备份任务成功!等待30秒后查询备份任务结果", waitCluster)
		time.Sleep(30 * time.Second) // 等待10秒后查询备份任务结果

		// 轮询获取备份结果
		successCluster, err := e.getBackResult(ctx, orderID, waitCluster, timeout, reportStore)
		if err != nil {
			return err
		}
		// 判断传入集群与成功集群的差集
		unfinishedCluster := []string{}
		if len(waitCluster)-len(successCluster) > 0 {
			successMap := map[string]bool{}
			for _, c := range successCluster {
				successMap[c] = true
			}
			for c := range waitCluster {
				if !successMap[c] {
					unfinishedCluster = append(unfinishedCluster, c)
				}
			}
		}
		// 比对传入数量和成功数量，得到最终成功与否判断和日志
		if len(errCluster) != 0 || len(unfinishedCluster) > 0 {
			e.logger.Infof("以下实例备份成功：%v", successCluster)
			if len(errCluster) != 0 {
				e.logger.Errorf("以下实例备份创建备份任务时失败：%v", errCluster)
			}
			if len(unfinishedCluster) > 0 {
				e.logger.Errorf("以下实例成功创建备份任务，但是没有在指定时间范围内结束备份，请通过控制台检查：%v", unfinishedCluster)

			}
			return fmt.Errorf("备份失败")
		}
		reportStore, _ = common.GetOrderStatusDetail(ctx, orderID)
		storeSuccess, storeFailed, _ := common.StatusDetailCount(reportStore)
		if len(storeFailed) != 0 {
			e.logger.Errorf("回顾所有实例发现失败实例: %v", storeFailed)
			return fmt.Errorf("备份失败")
		}
		if len(storeSuccess) != len(form.Instances) {
			e.logger.Errorf("回顾成功实例总数量不正确，已标记成功的实例：%v", storeSuccess)
			return fmt.Errorf("备份失败")
		}
		e.logger.Infoln("所有实例备份成功:", successCluster)
		return nil
	}

	return fmt.Errorf("未能识别的操作")
}

// DeleteInstance 删除实例
func (e ElasticCacheProvider) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()

	for _, instance := range form.Instances {
		resp := &elasticache.DeleteReplicationGroupOutput{}
		err := agentsdk.SyncCall(ctx, e.regionID, "redis", "DeleteReplicationGroup", e.accountID, []interface{}{"", &elasticache.DeleteReplicationGroupInput{
			ReplicationGroupId:   aws.String(instance),
			RetainPrimaryCluster: aws.Bool(false),
		}}, resp)
		if err != nil {
			return err
		}
	}

	return nil
}

// TestPing 测试API连通性
func (e ElasticCacheProvider) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}

// GetSnapshotCount ...
func (e *ElasticCacheProvider) GetSnapshotCount(ctx context.Context) (int32, error) {
	var totalCount int
	times := 1
	var marker *string
	for times == 1 || marker != nil {
		// TODO: maybe wrong logic, need to refine
		ccIn := &elasticache.DescribeReplicationGroupsInput{}
		ccOut := &elasticache.DescribeReplicationGroupsOutput{}
		err := agentsdk.SyncCall(ctx, e.regionID, "redis", "DescribeReplicationGroups", e.accountID, []interface{}{nil, ccIn}, ccOut)
		if err != nil {
			return 0, err
		}
		for _, rg := range ccOut.ReplicationGroups {
			totalCount += len(rg.NodeGroups)
		}

		flag := true
		clusterIn := &elasticache.DescribeCacheClustersInput{ShowCacheNodeInfo: &flag}
		clusterOut := &elasticache.DescribeCacheClustersOutput{}
		err = agentsdk.SyncCall(ctx, e.regionID, "redis", "DescribeCacheClusters", e.accountID, []interface{}{nil, clusterIn}, clusterOut)
		if err != nil {
			return 0, err
		}

		totalCount += len(clusterOut.CacheClusters)
		times++
		marker = ccOut.Marker
	}
	return int32(totalCount), nil
}

func (e ElasticCacheProvider) getBackResult(ctx context.Context, orderID string, snapNames map[string]string, maxTime time.Duration, reportStore string) (success []string, err error) {
	timeout := time.Now().Add(maxTime)
	var waitCluster map[string]string
	err = deepCopy(snapNames, &waitCluster)
	if err != nil {
		return
	}
loop:
	if time.Now().After(timeout) {
		return success, fmt.Errorf("获取结果超时")
	}
	nextCluster := make(map[string]string)
	for clusterID, snapName := range waitCluster {
		resp := &elasticache.DescribeSnapshotsOutput{}
		err := agentsdk.SyncCall(ctx, e.regionID, "redis", "DescribeSnapshots", e.accountID, []interface{}{"", &elasticache.DescribeSnapshotsInput{
			SnapshotName: aws.String(snapName),
		}}, resp)
		if err != nil {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapName):    "fetch_error",
				common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, snapName):    true,
			})
			e.logger.Errorf("获取实例(%s)备份任务(%s)失败: %s", clusterID, snapName, err.Error())
			nextCluster[clusterID] = snapName
			continue
		}
		if len(resp.Snapshots) == 0 {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapName):    "fetch_error",
				common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, snapName):    true,
			})
			e.logger.Errorf("获取实例(%s)备份任务(%s)结果异常,未发现实例任务/任务后台创建中,等待30秒后再次查询", clusterID, snapName)
			nextCluster[clusterID] = snapName
			continue
		}
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapName):    aws.StringValue(resp.Snapshots[0].SnapshotStatus),
			common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapName): time.Now().Unix(),
		})
		if aws.StringValue(resp.Snapshots[0].SnapshotStatus) == "available" {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.jobs.j%s.is_success", clusterID, snapName): true,
				common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, snapName):     false,
			})
			e.logger.Infof("实例(%s)任务(%s)备份成功", clusterID, snapName)
			success = append(success, clusterID)
		} else {
			e.logger.Infof("实例(%s)任务(%s)正在备份中,等待下次查询", clusterID, snapName)
			nextCluster[clusterID] = snapName
		}
		time.Sleep(200 * time.Millisecond)
	}
	for _, successCluster := range success {
		if len(nextCluster[successCluster]) == 0 {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.status", successCluster):     "success",
				common.ReportSprintf("%s.is_success", successCluster): true,
				common.ReportSprintf("%s.is_err", successCluster):     false,
				common.ReportSprintf("%s.last_time", successCluster):  time.Now().Unix(),
			})
		}
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
	waitCluster = nextCluster
	if len(nextCluster) != 0 {
		e.logger.Warnf("获取实例(%v)备份任务失败,30秒后继续查询", nextCluster)
		time.Sleep(30 * time.Second)
		goto loop
	}

	return
}

func deepCopy(src interface{}, dst interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, dst)
}

// GetElastiCache 生成ElastiCache客户端
func GetElastiCache(regionID, accountID string, logger *logrus.Logger, dryRun bool) (*ElasticCacheProvider, error) {
	if logger == nil {
		return nil, fmt.Errorf("logger object is null")
	}

	return &ElasticCacheProvider{logger: logger, dryRun: dryRun, accountID: accountID, regionID: regionID}, nil
}
