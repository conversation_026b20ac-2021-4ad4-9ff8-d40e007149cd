package aws

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"slices"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/acm"
	"github.com/aws/aws-sdk-go/service/elbv2"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// LoadBalancerProvider elastiCache
type LoadBalancerProvider struct {
	logger *logrus.Logger

	dryRun    bool
	accountID string
	regionID  string
}

func NewLoadBalancerProvider(regionID string, accountID string, logger *logrus.Logger, dryRun bool) *LoadBalancerProvider {
	return &LoadBalancerProvider{
		logger:    logger,
		dryRun:    dryRun,
		accountID: accountID,
		regionID:  regionID,
	}
}

type LBZoneMapping struct {
	SubnetId *string `json:"VSSwitchId"`
	ZoneId   *string `json:"ZoneId"`
}

type CreateLBInput struct {
	LoadBalancerName string          `json:"LoadBalancerName"` // 实例名称
	IpAddressType    string          `json:"IpAddressType"`    // ipv4/dualstack 仅ipv4/双栈
	Scheme           string          `json:"Scheme"`           // internet-facing/internal 公网/私网
	Certs            []string        `json:"Certs"`            // https默认证书
	ZoneMappings     []LBZoneMapping `json:"ZoneMappings"`     // 可用区和子网的映射关系
	Type             string          `json:"LoadBalancerType"` // application/network/gateway
	SecurityGroupIds []string        `json:"SecurityGroupIds"` // 安全组IP
	Tags             []*elbv2.Tag    `json:"Tags"`             // 标签
	VpcID            string          `json:"VpcId"`            // vpcId
}

func (lb *CreateLBInput) GetTags(lbName string) []*elbv2.Tag {
	tags := []*elbv2.Tag{
		{
			Key:   aws.String("nap"),
			Value: aws.String(hostname_util.GetLbNameArea(lbName)),
		},
		{
			Key:   aws.String("env"),
			Value: aws.String(hostname_util.GetLbNameEnv(lbName)),
		},
		{
			Key:   aws.String("region"),
			Value: aws.String(hostname_util.GetLbNameRegion(lbName)),
		},
		{
			Key:   aws.String("module"),
			Value: aws.String(hostname_util.GetLbNameModule(lbName)),
		},
		{
			Key:   aws.String("region_module"),
			Value: aws.String(hostname_util.GetLbNameRegionModule(lbName)),
		},
		{
			Key:   aws.String("serial_number"),
			Value: aws.String(hostname_util.GetLbNameNum(lbName)),
		},
	}

	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	for _, t := range lb.Tags {
		if slices.Contains(systemTag, aws.StringValue(t.Key)) {
			continue
		}
		tags = append(tags, &elbv2.Tag{
			Key:   t.Key,
			Value: t.Value,
		})
	}
	return tags
}

var tgRex = regexp.MustCompile(`[a-zA-Z]+|\d+`)

var modulePortMap = map[string]int{
	"dp":   30401,
	"muip": 30701,
	"oa":   31001,
	"gp":   20000,
}

func getTargetGroupName(lbName string) (string, error) {
	names := strings.Split(lbName, "-")

	if len(names) != 6 {
		return "", fmt.Errorf("wrong lb name, %s", lbName)
	}
	moduleNum := tgRex.FindAllString(names[4], -1)
	module := moduleNum[0]
	// convert module name
	if module == "glbdp" {
		module = "gp"
	}
	port, ok := modulePortMap[module]
	if !ok {
		port = 0
	}
	return fmt.Sprintf("tg-%s-%s-%s-%s%d", names[1], names[2], names[3], module, port), nil
}

func getTargetGroupPort(lbName string) (int, error) {
	names := strings.Split(lbName, "-")

	if len(names) != 6 {
		return 0, fmt.Errorf("wrong lb name, %s", lbName)
	}
	moduleNum := tgRex.FindAllString(names[4], -1)
	module := moduleNum[0]
	// convert module name
	if module == "glbdp" {
		module = "gp"
	}
	port, ok := modulePortMap[module]
	if !ok {
		return 0, fmt.Errorf("wrong module name, %s", module)
	}
	return port, nil
}

func (p LoadBalancerProvider) GetSnapshotCount(ctx context.Context) (int32, error) {
	return 0, nil
}

func (p LoadBalancerProvider) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}

// CreateInstance 创建实例
func (p LoadBalancerProvider) CreateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) (instances []string, err error) {
	input := &CreateLBInput{}
	reportStore := common.NewReportInstanceOrderStatus(orderID, p.dryRun)
	err = json.Unmarshal(data, input)
	if err != nil {
		return
	}
	if p.dryRun {
		return
	}

	client := NewLoadBalancerClient(p.regionID, p.accountID)
	subnets := make([]*string, 0, len(input.ZoneMappings))
	for _, zone := range input.ZoneMappings {
		subnets = append(subnets, zone.SubnetId)
	}

	in := &elbv2.CreateLoadBalancerInput{
		IpAddressType:  aws.String(input.IpAddressType),
		Name:           aws.String(input.LoadBalancerName),
		Scheme:         aws.String(input.Scheme),
		SecurityGroups: aws.StringSlice(input.SecurityGroupIds),
		Subnets:        subnets,
		Tags:           input.GetTags(input.LoadBalancerName),
		Type:           aws.String(input.Type),
	}
	reportStore.ReportStart(ctx, input.LoadBalancerName)
	lb, err := client.CreateLoadBalancer(ctx, in)
	if err != nil {
		reportStore.ReportFailed(ctx, input.LoadBalancerName)
		return
	}
	reportStore.ReportInstanceId(ctx, input.LoadBalancerName, aws.StringValue(lb.LoadBalancerArn))
	reportStore.ReportCustomStatus(ctx, input.LoadBalancerName, "created")
	tgName, err := getTargetGroupName(input.LoadBalancerName)
	if err != nil {
		reportStore.ReportFailed(ctx, input.LoadBalancerName)
		return
	}
	port, err := getTargetGroupPort(input.LoadBalancerName)
	if err != nil {
		reportStore.ReportFailed(ctx, input.LoadBalancerName)
		return
	}
	reportStore.ReportJobStart(ctx, input.LoadBalancerName, "CreateTargetGroup")
	targetGroup, err := client.CreateTargetGroup(context.Background(), &elbv2.CreateTargetGroupInput{
		Name:                       aws.String(tgName),
		Port:                       aws.Int64(int64(port)),
		Protocol:                   aws.String("HTTP"),
		VpcId:                      aws.String(input.VpcID),
		HealthCheckEnabled:         aws.Bool(true),
		HealthCheckPath:            aws.String("/alive"),
		HealthCheckIntervalSeconds: aws.Int64(5),
		HealthCheckProtocol:        aws.String("HTTP"),
	})
	if err != nil {
		reportStore.ReportJobFailed(ctx, input.LoadBalancerName, "CreateTargetGroup")
		return
	}
	reportStore.ReportJobSuccess(ctx, input.LoadBalancerName, "CreateTargetGroup")
	reportStore.ReportJobStart(ctx, input.LoadBalancerName, "CreateHttpListener")
	listener, err := client.CreateListener(context.Background(), &elbv2.CreateListenerInput{
		DefaultActions: []*elbv2.Action{
			{
				TargetGroupArn: targetGroup.TargetGroupArn,
				Type:           aws.String("forward"),
			},
		},
		LoadBalancerArn: lb.LoadBalancerArn,
		Port:            aws.Int64(80),
		Protocol:        aws.String("HTTP"),
	})
	if err != nil {
		reportStore.ReportJobFailed(ctx, input.LoadBalancerName, "CreateHttpListener")
		return
	}
	reportStore.ReportJobSuccess(ctx, input.LoadBalancerName, "CreateHttpListener")
	logrus.Infof("create http listener success, listener: %+v", listener)

	certificates := make([]*elbv2.Certificate, 0, len(input.Certs))
	for _, cert := range input.Certs {
		certificates = append(certificates, &elbv2.Certificate{
			CertificateArn: aws.String(cert),
		})
	}
	reportStore.ReportJobStart(ctx, input.LoadBalancerName, "CreateHttpsListener")
	httpsListener, err := client.CreateListener(context.Background(), &elbv2.CreateListenerInput{
		DefaultActions: []*elbv2.Action{
			{
				TargetGroupArn: targetGroup.TargetGroupArn,
				Type:           aws.String("forward"),
			},
		},
		LoadBalancerArn: lb.LoadBalancerArn,
		Port:            aws.Int64(443),
		Certificates:    certificates,
		Protocol:        aws.String("HTTPS"),
	})
	if err != nil {
		reportStore.ReportJobFailed(ctx, input.LoadBalancerName, "CreateHttpsListener")
		return
	}
	logrus.Infof("create https listener success, listener: %+v", httpsListener)
	reportStore.ReportJobSuccess(ctx, input.LoadBalancerName, "CreateHttpsListener")
	err = p.WaitForInstanceActive(ctx, aws.StringValue(lb.LoadBalancerArn), timeout)
	if err != nil {
		return
	}
	reportStore.ReportSuccess(ctx, input.LoadBalancerName)
	instances = append(instances, aws.StringValue(lb.LoadBalancerArn))
	return
}

func (p LoadBalancerProvider) WaitForInstanceActive(ctx context.Context, lbArn string, timeout time.Duration) error {
	client := NewLoadBalancerClient(p.regionID, p.accountID)

	timeoutCtx, timeoutCancel := context.WithTimeout(context.Background(), timeout)
	defer timeoutCancel()
	ticker := time.NewTicker(10 * time.Second)
	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("wait for loadbalancer active timeout")
		case <-ticker.C:
			lb, err := client.DescribeLoadbalancers(timeoutCtx, lbArn)
			if err != nil {
				return err
			}
			if aws.StringValue(lb[0].State.Code) == "active" {
				logrus.Info("loadbalancer is active")
				return nil
			}
		}
	}
}

// UpdateInstance 更新实例
func (p LoadBalancerProvider) UpdateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) error {
	return nil
}

// DeleteInstance 删除实例
func (p LoadBalancerProvider) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	client := NewLoadBalancerClient(p.regionID, p.accountID)
	for _, instanceID := range form.Instances {
		err := client.DeleteLoadBalancer(ctx, instanceID)
		if err != nil {
			return err
		}
		p.logger.Infof("delete loadbalancer %s success", instanceID)
	}
	return nil

}

// LoadBalancerClient aws lb-client
type LoadBalancerClient struct {
	regionID  string
	accountID string
}

// NewLoadBalancerClient 创建client
func NewLoadBalancerClient(regionID, accountID string) *LoadBalancerClient {
	return &LoadBalancerClient{
		regionID:  regionID,
		accountID: accountID,
	}
}

// DescribeLoadbalancers lbArns最多20个
func (c *LoadBalancerClient) DescribeLoadbalancers(ctx context.Context, lbArns ...string) ([]*elbv2.LoadBalancer, error) {
	in := &elbv2.DescribeLoadBalancersInput{}
	if len(lbArns) > 0 {
		in.SetLoadBalancerArns(aws.StringSlice(lbArns))
	} else {
		// PageSize最大为400
		in.SetPageSize(100)
	}
	maxLoop := 10
	res := make([]*elbv2.LoadBalancer, 0)
	for range maxLoop {
		out := &elbv2.DescribeLoadBalancersOutput{}
		err := agentsdk.SyncCall(ctx, c.regionID, "lb", "DescribeLoadBalancers", c.accountID, []interface{}{in}, out)
		if err != nil {
			return nil, err
		}
		res = append(res, out.LoadBalancers...)
		if out.NextMarker == nil {
			break
		}
		in.SetMarker(aws.StringValue(out.NextMarker))
	}

	return res, nil
}

func (c *LoadBalancerClient) DescribeListeners(ctx context.Context, lbArn string) ([]*elbv2.Listener, error) {
	in := &elbv2.DescribeListenersInput{}
	in.SetLoadBalancerArn(lbArn)
	// PageSize最大为400
	in.SetPageSize(400)
	maxLoop := 10
	res := make([]*elbv2.Listener, 0)

	for range maxLoop {
		out := &elbv2.DescribeListenersOutput{}
		err := agentsdk.SyncCall(ctx, c.regionID, "lb", "DescribeListeners", c.accountID, []interface{}{in}, out)
		if err != nil {
			return nil, err
		}
		res = append(res, out.Listeners...)
		if out.NextMarker == nil {
			break
		}
		in.SetMarker(aws.StringValue(out.NextMarker))
	}

	return res, nil
}

func (c *LoadBalancerClient) CreateLoadBalancer(ctx context.Context, in *elbv2.CreateLoadBalancerInput) (*elbv2.LoadBalancer, error) {
	out := &elbv2.CreateLoadBalancerOutput{}
	err := agentsdk.SyncCall(ctx, c.regionID, "lb", "CreateLoadBalancer", c.accountID, []interface{}{in}, out)
	if err != nil {
		return nil, err
	}
	if len(out.LoadBalancers) == 0 {
		return nil, nil
	}
	return out.LoadBalancers[0], nil
}

func (c *LoadBalancerClient) CreateTargetGroup(ctx context.Context, in *elbv2.CreateTargetGroupInput) (*elbv2.TargetGroup, error) {
	out := &elbv2.CreateTargetGroupOutput{}
	err := agentsdk.SyncCall(ctx, c.regionID, "lb", "CreateTargetGroup", c.accountID, []interface{}{in}, out)
	if err != nil {
		return nil, err
	}
	if len(out.TargetGroups) == 0 {
		return nil, nil
	}
	return out.TargetGroups[0], nil

}

func (c *LoadBalancerClient) DeleteLoadBalancer(ctx context.Context, lbArn string) error {
	in := &elbv2.DeleteLoadBalancerInput{}
	in.SetLoadBalancerArn(lbArn)
	out := &elbv2.DeleteLoadBalancerOutput{}
	err := agentsdk.SyncCall(ctx, c.regionID, "lb", "DeleteLoadBalancer", c.accountID, []interface{}{in}, out)
	return err
}

func (c *LoadBalancerClient) CreateListener(ctx context.Context, in *elbv2.CreateListenerInput) (*elbv2.Listener, error) {
	out := &elbv2.CreateListenerOutput{}
	err := agentsdk.SyncCall(ctx, c.regionID, "lb", "CreateListener", c.accountID, []interface{}{in}, out)
	if err != nil {
		return nil, err
	}
	if len(out.Listeners) == 0 {
		return nil, nil
	}
	return out.Listeners[0], nil
}

func (c *LoadBalancerClient) DescribeZones(ctx context.Context) (*cloudman.DescribeZonesRes, error) {
	describeZonesReq := &ec2.DescribeAvailabilityZonesInput{}
	res := &ec2.DescribeAvailabilityZonesOutput{}
	err := agentsdk.SyncCall(ctx, c.regionID, "host", "DescribeAvailabilityZones", c.accountID, []interface{}{"", describeZonesReq}, res)
	if err != nil {
		return nil, err
	}

	resp := &cloudman.DescribeZonesRes{
		Total: int32(len(res.AvailabilityZones)),
		List:  make([]*cloudman.ALBZone, 0, len(res.AvailabilityZones)),
	}
	for _, zone := range res.AvailabilityZones {
		resp.List = append(resp.List, &cloudman.ALBZone{
			LocalName: aws.StringValue(zone.ZoneName),
			ZoneId:    aws.StringValue(zone.ZoneId),
		})
	}
	return resp, nil

}

func (c *LoadBalancerClient) ListCertificates(ctx context.Context) (*cloudman.ListCertificatesRes, error) {
	req := &acm.ListCertificatesInput{}
	req.SetMaxItems(100)
	res := &acm.ListCertificatesOutput{}
	err := agentsdk.SyncCall(ctx, c.regionID, "acm", "ListCertificates", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	resp := &cloudman.ListCertificatesRes{
		Total: int32(len(res.CertificateSummaryList)),
		List:  make([]*cloudman.Certificate, 0, len(res.CertificateSummaryList)),
	}

	for _, cert := range res.CertificateSummaryList {
		resp.List = append(resp.List, &cloudman.Certificate{
			CertIdentifier: aws.StringValue(cert.CertificateArn),
			Domain:         aws.StringValue(cert.DomainName),
		})
	}
	return resp, nil
}
