package aws

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/kms"

	"github.com/aws/aws-sdk-go-v2/service/ec2"
	ec2types "github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	ec22 "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/ec2"
	awsUtil "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/util"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// HostProvider HostProvider
type HostProvider struct {
	logger    *logrus.Logger
	dryRun    bool
	accountID string
	regionID  string
}

// GetHostProvider GetHostProvider
func GetHostProvider(regionID, accountID string, logger *logrus.Logger, dryRun bool) (*HostProvider, error) {
	awsOption, err := cfg.GetAwsOptionConf()
	if err != nil {
		return nil, err
	}
	if regionID == "" {
		regionID = awsOption.DefaultRegionID
	}
	return &HostProvider{
		logger:    logger,
		dryRun:    dryRun,
		accountID: accountID,
		regionID:  regionID,
	}, nil
}

// RunInstancesRequestSystemDisk RunInstancesRequestSystemDisk
type RunInstancesRequestSystemDisk struct {
	Size       *int32  `json:"Size,omitempty" xml:"Size,omitempty"`
	Category   *string `json:"Category,omitempty" xml:"Category,omitempty"`
	IOPS       *int32  `json:"IOPS,omitempty" xml:"IOPS,omitempty"`
	Throughput *int32  `json:"Throughput,omitempty" xml:"Throughput,omitempty"`
	// Description          *string `json:"Description,omitempty" xml:"Description,omitempty"`
	// PerformanceLevel     *string `json:"PerformanceLevel,omitempty" xml:"PerformanceLevel,omitempty"`
	// AutoSnapshotPolicyId *string `json:"AutoSnapshotPolicyId,omitempty" xml:"AutoSnapshotPolicyId,omitempty"`
}

func (r *RunInstancesRequestSystemDisk) blockDeviceMapping() (*ec2types.BlockDeviceMapping, error) {
	ebs := &ec2types.EbsBlockDevice{
		VolumeSize: r.Size,
		VolumeType: ec2types.VolumeType(aws.StringValue(r.Category)),
	}
	if *r.Category == "gp3" {
		ebs.Throughput = r.Throughput
		ebs.Iops = r.IOPS
	}
	// 默认系统盘设备名称
	deviceName := "/dev/xvda"
	return &ec2types.BlockDeviceMapping{
		DeviceName: &deviceName,
		Ebs:        ebs,
	}, nil
}

// RunInstancesRequestDataDisk RunInstancesRequestDataDisk
type RunInstancesRequestDataDisk struct {
	// PerformanceLevel     *string `json:"PerformanceLevel,omitempty" xml:"PerformanceLevel,omitempty"`
	// AutoSnapshotPolicyId *string `json:"AutoSnapshotPolicyId,omitempty" xml:"AutoSnapshotPolicyId,omitempty"`
	// Encrypted            *string `json:"Encrypted,omitempty" xml:"Encrypted,omitempty"`
	// Description          *string `json:"Description,omitempty" xml:"Description,omitempty"`
	// SnapshotId           *string `json:"SnapshotId,omitempty" xml:"SnapshotId,omitempty"`
	Device     *string `json:"Device,omitempty" xml:"Device,omitempty"`
	Size       *int32  `json:"Size,omitempty" xml:"Size,omitempty"`
	DiskName   *string `json:"DiskName,omitempty" xml:"DiskName,omitempty"`
	Category   *string `json:"Category,omitempty" xml:"Category,omitempty"`
	IOPS       *int32  `json:"IOPS,omitempty" xml:"IOPS,omitempty"`
	Throughput *int32  `json:"Throughput,omitempty" xml:"Throughput,omitempty"`
	// EncryptAlgorithm     *string `json:"EncryptAlgorithm,omitempty" xml:"EncryptAlgorithm,omitempty"`
	// DeleteWithInstance   *bool   `json:"DeleteWithInstance,omitempty" xml:"DeleteWithInstance,omitempty"`
	// KMSKeyId             *string `json:"KMSKeyId,omitempty" xml:"KMSKeyId,omitempty"`
}

func (r *RunInstancesRequestDataDisk) blockDeviceMapping(index int) (*ec2types.BlockDeviceMapping, error) {
	var lastChar = 'b'
	lastChar += int32(index)
	// 数据盘设备名称从/dev/sdb开始
	deviceName := aws.String("/dev/sd" + string(lastChar))
	ebs := &ec2types.EbsBlockDevice{
		VolumeSize: r.Size,
		VolumeType: ec2types.VolumeType(*r.Category),
	}
	// gp3类型可设置IOPS和Throughput
	if *r.Category == "gp3" {
		ebs.Throughput = r.Throughput
		ebs.Iops = r.IOPS
	}
	return &ec2types.BlockDeviceMapping{
		DeviceName: deviceName,
		Ebs:        ebs,
	}, nil
}

// RunInstancesRequestTag RunInstancesRequestTag
type RunInstancesRequestTag struct {
	Key   *string `json:"Key,omitempty" xml:"Key,omitempty"`
	Value *string `json:"Value,omitempty" xml:"Value,omitempty"`
}

type ZoneMapping struct {
	SubnetId *string `json:"VSwitchId,omitempty" field:"交换机ID"`
	ZoneId   *string `json:"ZoneId,omitempty" field:"可用区ID"`
}

type BindEIPConfig struct {
	Enable     bool    `json:"enable"`
	IpamPoolId *string `json:"ipamPoolId"` // IP池ID
}

// CreateInstanceInput CreateInstanceInput
type CreateInstanceInput struct {
	ImageID          *string                        `json:"ImageID,omitempty" field:"镜像ID"`
	InstanceType     *string                        `json:"InstanceType,omitempty" field:"实例类型"`
	KeyPairName      *string                        `json:"KeyPairName,omitempty" field:"密钥名称"`
	SystemDisk       *RunInstancesRequestSystemDisk `json:"SystemDisk,omitempty" field:"系统盘"`
	DataDisk         []*RunInstancesRequestDataDisk `json:"DataDisk,omitempty" field:"数据盘"`
	VpcID            *string                        `json:"VpcId,omitempty" field:"vpc-id"`
	SecurityGroupIDs []string                       `json:"SecurityGroupIds,omitempty" field:"安全组id"`
	HostName         *string                        `json:"HostName,omitempty" field:"主机名"`
	Tags             []*RunInstancesRequestTag      `json:"Tag,omitempty" filed:"标签"`
	Amount           *int32                         `json:"Amount,omitempty" field:"购买数量"`
	RegionID         *string                        `field:"地域id"`
	UserData         *string                        `json:"UserData,omitempty" field:"实例自定义数据"`
	BindEIP          *BindEIPConfig                 `json:"bindEIP,omitempty" field:"是否绑定EIP"`
	ZoneMappings     []*ZoneMapping                 `json:"ZoneMappings,omitempty" field:"可用区映射"`
	LoginType        *string                        `json:"LoginType,omitempty" field:"登录类型"`
}

// GetUserData 获取base64-userdata
func (c CreateInstanceInput) GetUserData() *string {
	if c.UserData != nil {
		return aws.String(base64.StdEncoding.EncodeToString([]byte(aws.StringValue(c.UserData))))
	}

	return nil
}

// NameTag
var (
	NameTag       = "Name"
	AreaTag       = "Area"
	EnvTag        = "Env"
	NamePrefixTag = "NamePrefix"
	IDSuffixTag   = "IdSuffix"
)

// NamePrefixValFormat NamePrefixValFormat
const NamePrefixValFormat = "NamePrefix:%s"

// TagSpecifications TagSpecifications
func (c *CreateInstanceInput) TagSpecifications(hostname string) []ec2types.TagSpecification {
	tags := []ec2types.Tag{
		{
			Key:   aws.String("nap"),
			Value: aws.String(hostname_util.GetHostNameArea(hostname)),
		},
		{
			Key:   aws.String("env"),
			Value: aws.String(hostname_util.GetHostNameEnv(hostname)),
		},
		{
			Key:   aws.String("region"),
			Value: aws.String(hostname_util.GetHostNameRegion(hostname)),
		},
		{
			Key:   aws.String("module"),
			Value: aws.String(hostname_util.GetHostNameModule(hostname)),
		},
		{
			Key:   aws.String("region_module"),
			Value: aws.String(hostname_util.GetHostNameRegionModule(hostname)),
		},
		{
			Key:   aws.String("serial_number"),
			Value: aws.String(hostname_util.GetHostNameNum(hostname)),
		},
	}
	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	for _, t := range c.Tags {
		if slices.Contains(systemTag, aws.StringValue(t.Key)) {
			continue
		}
		tags = append(tags, ec2types.Tag{
			Key: t.Key, Value: t.Value,
		})
	}

	// 实例需要加上name的标签
	instanceTags := append(tags, ec2types.Tag{
		Key:   &NameTag,
		Value: &hostname,
	})
	return []ec2types.TagSpecification{
		{
			ResourceType: "instance",
			Tags:         instanceTags,
		},
		{
			ResourceType: "volume",
			Tags:         tags,
		},
		{
			ResourceType: "network-interface",
			Tags:         tags,
		},
	}
}

// BlockDeviceMappings SystemDisk与DataDisk转义为BlockDeviceMappings
func (c *CreateInstanceInput) BlockDeviceMappings() ([]ec2types.BlockDeviceMapping, error) {
	_, err := cfg.GetAwsOptionConf()
	if err != nil {
		return nil, err
	}

	var bdms []ec2types.BlockDeviceMapping
	systemBDM, err := c.SystemDisk.blockDeviceMapping()
	if err != nil {
		return nil, err
	}
	bdms = append(bdms, *systemBDM)
	for index, d := range c.DataDisk {
		dataBDM, err := d.blockDeviceMapping(index)
		if err != nil {
			return nil, err
		}
		bdms = append(bdms, *dataBDM)
	}

	return bdms, nil
}

// NetworkInterfaces NetworkInterfaces
func (c *CreateInstanceInput) NetworkInterfaces(subnetId string) []ec2types.InstanceNetworkInterfaceSpecification {
	return []ec2types.InstanceNetworkInterfaceSpecification{
		{
			AssociatePublicIpAddress: aws.Bool(false),
			DeviceIndex:              aws.Int32(0),
			DeleteOnTermination:      aws.Bool(true),
			SubnetId:                 aws.String(subnetId),
			Groups:                   c.SecurityGroupIDs,
		},
	}

}

// GetMaxAssignedID GetMaxAssignedID
func (h *HostProvider) GetMaxAssignedID(ctx context.Context, prefix string) (int, error) {
	tagKeyFiled := "tag-key"
	namePrefix := fmt.Sprintf(NamePrefixValFormat, prefix)

	descOut := &ec2.DescribeInstancesOutput{}
	err := agentsdk.SyncCall(ctx, h.regionID, "host", "DescribeInstances", h.accountID, []interface{}{"", &ec2.DescribeInstancesInput{
		Filters: []ec2types.Filter{
			{
				Name:   &tagKeyFiled,
				Values: []string{namePrefix},
			},
		},
	}}, descOut)
	if err != nil {
		logrus.Errorf("获取指定Tag（%s）对应的主机报错：%s", prefix, err)
		return -1, err
	}
	var maxAssignedID int
	for _, item := range descOut.Reservations {
		for _, inst := range item.Instances {
			var instName string
			for _, tag := range inst.Tags {
				if *tag.Key == NameTag {
					instName = *tag.Value
				}
			}
			idStr := instName[len(prefix):]
			if id, err := strconv.Atoi(idStr); err != nil {
				logrus.Errorf("主机名（%s）ID后缀（%s）不是数字形式：%s", instName, idStr, err)
				continue
			} else if id > maxAssignedID {
				maxAssignedID = id
			}
		}
	}
	return maxAssignedID, nil
}

// CreateInstance CreateInstance
func (h *HostProvider) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) ([]string, error) {
	req := new(CreateInstanceInput)
	reportStore := common.NewReportInstanceOrderStatus(orderID, h.dryRun)
	err := json.Unmarshal(data, req)
	if err != nil {
		return nil, err
	}
	amount := aws.Int32Value(req.Amount)

	h.logger.Infoln("购买数量: ", amount)

	// 生成主机名并检查是否重复
	if err = cloudutils.RuleCheck(aws.StringValue(req.HostName), cloudutils.Linux); err != nil {
		return nil, fmt.Errorf("主机名规则检查不通过：%s", err.Error())
	}
	var hostnames []string
	hostnames, err = cloudutils.GetCloudutils(models.HostResourceModel).
		GenInstanceName(
			ctx,
			aws.StringValue(req.HostName),
			int(amount),
			true,
			cloudutils.Linux,
		)
	if err != nil {
		return nil, fmt.Errorf("主机名生成失败：%s", err.Error())
	}
	if len(hostnames) != int(amount) {
		return nil, fmt.Errorf("hostname_count mismatch req_count")
	}
	for _, hostname := range hostnames {
		reportStore.ReportStart(ctx, hostname)
	}

	// 检查EIP是否足够
	if req.BindEIP.Enable {
		limit, err := ec22.GetEipLimit(ctx, h.regionID, h.accountID)
		if err != nil {
			return nil, err
		}
		num, _, err := ec22.ListEIP(ctx, h.regionID, h.accountID)
		if err != nil {
			return nil, err
		}
		if limit-num < int(*req.Amount) {
			msg := fmt.Sprintf("AWS在区域（%s）下可分配的EIP数量（%d）小于请求数量（%d）", *req.RegionID, limit-num, *req.Amount)
			logrus.Error(msg)
			return nil, fmt.Errorf(msg)
		}
	}

	bdms, err := req.BlockDeviceMappings()
	if err != nil {
		return nil, err
	}
	imageID := *req.ImageID

	subnetIdAmountMap := make(map[string]int32, 0)
	zoneMappingLength := len(req.ZoneMappings)
	for index, zoneMappings := range req.ZoneMappings {
		amount := int(aws.Int32Value(req.Amount)) / zoneMappingLength
		if int32(index)+1 <= aws.Int32Value(req.Amount)%int32(zoneMappingLength) {
			amount += 1
		}
		subnetIdAmountMap[aws.StringValue(zoneMappings.SubnetId)] = int32(amount)
	}

	inputs := make([]*ec2.RunInstancesInput, 0)
	index := 0

	for subnetId, amount := range subnetIdAmountMap {
		if amount == 0 {
			continue
		}
		for range amount {
			input := &ec2.RunInstancesInput{
				ImageId:             &imageID,
				InstanceType:        ec2types.InstanceType(*req.InstanceType),
				BlockDeviceMappings: bdms,
				TagSpecifications:   req.TagSpecifications(hostnames[index]),
				MaxCount:            aws.Int32(1),
				MinCount:            aws.Int32(1),
				NetworkInterfaces:   req.NetworkInterfaces(subnetId),
				DryRun:              aws.Bool(h.dryRun),
				UserData:            req.GetUserData(),
				MetadataOptions: &ec2types.InstanceMetadataOptionsRequest{
					InstanceMetadataTags: ec2types.InstanceMetadataTagsStateEnabled,
				},
				Monitoring: &ec2types.RunInstancesMonitoringEnabled{
					Enabled: aws.Bool(true),
				},
			}
			// 只有LoginType为KeyPairName时，才需要指定KeyName
			if aws.StringValue(req.LoginType) == "KeyPairName" {
				input.KeyName = req.KeyPairName
			}
			inputs = append(inputs, input)
			index += 1
		}
	}

	var g errgroup.Group
	// 设置并发数为2
	g.SetLimit(2)
	instances := map[string]ec2types.Instance{}
	for _, input := range inputs {
		g.Go(func() error {
			out, err := h.createInstance(ctx, input)
			if err != nil {
				return err
			}
			// 处理dryRun模式的空返回
			if out == nil {
				return nil
			}
			for _, inst := range out.Instances {
				for _, tag := range inst.Tags {
					if aws.StringValue(tag.Key) == NameTag {
						instances[aws.StringValue(tag.Value)] = inst
						break
					}
				}
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		h.logger.Errorf("创建主机失败：%s", err.Error())
		for _, hostname := range hostnames {
			if _, ok := instances[hostname]; ok {
				reportStore.ReportCustomStatus(ctx, hostname, "created")
				continue
			}
			reportStore.ReportFailed(ctx, hostname)
		}
		return nil, err
	}
	// dryRun模式下，直接退出，不进行下一步操作
	if h.dryRun {
		return nil, nil
	}

	if len(hostnames) != len(instances) {
		return nil, fmt.Errorf("申请%d个主机，执行结果为%d个，不一致", len(hostnames), len(instances))
	}
	instanceMap := map[string]string{}
	for hostname, instance := range instances {
		instanceMap[aws.StringValue(instance.InstanceId)] = hostname
		reportStore.ReportInstanceId(ctx, hostname, aws.StringValue(instance.InstanceId))
		reportStore.ReportCustomStatus(ctx, hostname, "created")
	}

	var instanceIDs []string
	for _, inst := range instances {
		instanceIDs = append(instanceIDs, aws.StringValue(inst.InstanceId))
	}
	err = h.afterCreate(ctx, instanceMap, reportStore)
	if err != nil {
		h.logger.Errorf("aws主机创建状态轮询失败：%s", err.Error())
	}

	for hostname, inst := range instances {
		if !req.BindEIP.Enable {
			continue
		}
		if !req.BindEIP.Enable {
			continue
		}

		reportStore.ReportJobStart(ctx, hostname, "AssignEIP")
		// 绑定EIP
		err = ec22.AssignEIP(ctx, h.regionID, h.accountID, inst.InstanceId, req.BindEIP.IpamPoolId)
		if err != nil {
			h.logger.Errorf("绑定EIP错误，%s：%s", *inst.InstanceId, err.Error())
			continue
		}
		reportStore.ReportJobSuccess(ctx, hostname, "AssignEIP")
	}
	return instanceIDs, err
}

func (h *HostProvider) createInstance(ctx context.Context, input *ec2.RunInstancesInput) (*ec2.RunInstancesOutput, error) {
	out := &ec2.RunInstancesOutput{}
	err := agentsdk.SyncCall(ctx, h.regionID, "host", "RunInstances", h.accountID,
		[]interface{}{"", input}, out)
	if err != nil {
		DryRunOperation := "api error DryRunOperation: Request would have succeeded, but DryRun flag is set"
		errMsg := err.Error()
		// 如果是DryRun模式，且错误信息包含DryRunOperation，则认为是预检通过，直接返回nil
		if aws.BoolValue(input.DryRun) && strings.Contains(errMsg, DryRunOperation) {
			return nil, nil
		}
		return nil, err
	}
	return out, nil
}

func (h *HostProvider) afterCreate(ctx context.Context, instanceMap map[string]string, reportStore *common.ReportInstanceOrderStatus) error {
	times := 0
	interval := 10
	h.logger.Infof("aws主机创建状态轮询开始")
	fullInstances := []string{}
	for id := range instanceMap {
		fullInstances = append(fullInstances, id)
	}
	waitInstances := fullInstances
	for times*interval < 1800 {
		times++
		time.Sleep(time.Duration(interval) * time.Second)
		stashInstances := []string{}
		apiRound := 1
		var nextToken *string
		for apiRound == 1 || nextToken != nil {
			instanceIn := &ec2.DescribeInstanceStatusInput{NextToken: nextToken, InstanceIds: waitInstances}
			instanceOut := &ec2.DescribeInstanceStatusOutput{}
			err := agentsdk.SyncCall(ctx, h.regionID, "host", "DescribeInstanceStatus", h.accountID, []interface{}{"", instanceIn}, instanceOut)
			if err != nil {
				return err
			}
			for _, inst := range instanceOut.InstanceStatuses {
				instanceID := aws.StringValue(inst.InstanceId)
				state := inst.InstanceState.Name
				reportStore.ReportCustomStatus(ctx, instanceMap[instanceID], string(state))
				if state == ec2types.InstanceStateNameRunning {
					reportStore.ReportSuccess(ctx, instanceMap[instanceID])
				} else {
					stashInstances = append(stashInstances, instanceID)
				}
			}
			apiRound++
			nextToken = instanceOut.NextToken
		}
		if len(stashInstances) == 0 {
			break
		}
		waitInstances = stashInstances
	}
	h.logger.Infof("aws主机创建状态轮询结束，instance:%+v", instanceMap)
	return nil
}

// UpdateInstance UpdateInstance
func (h *HostProvider) UpdateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) error {
	var form common.UpdateInstanceForm
	err := json.Unmarshal(data, &form)
	if err != nil {
		return fmt.Errorf("请求参数不能转换为UpdateInstanceForm格式：%v", err)
	}

	switch form.Action {
	case "reboot":
		h.logger.Infoln("开始重启实例")
		out := &ec2.RebootInstancesOutput{}
		err = agentsdk.SyncCall(ctx, h.regionID, "host", "RebootInstances", h.accountID, []interface{}{"", &ec2.RebootInstancesInput{
			InstanceIds: form.Instances,
		}}, out)
		if err != nil {
			h.logger.Errorln("重启实例失败:", err.Error())
			return err
		}
		err = h.waitForInstanceUtilSpecificStatus(ctx, form.Instances, 10*time.Minute, ec2types.InstanceStateNameRunning)
		if err != nil {
			h.logger.Errorln("重启实例失败:", err.Error())
			return err
		}
		h.logger.Infoln("重启实例成功")
	case "start":
		h.logger.Infoln("开始启动实例")
		out := &ec2.StartInstancesOutput{}
		err = agentsdk.SyncCall(ctx, h.regionID, "host", "StartInstances", h.accountID, []interface{}{"", &ec2.StartInstancesInput{InstanceIds: form.Instances}}, out)
		if err != nil {
			h.logger.Errorln("启动实例失败:", err.Error())
			return err
		}
		err = h.waitForInstanceUtilSpecificStatus(ctx, form.Instances, 10*time.Minute, ec2types.InstanceStateNameRunning)
		if err != nil {
			h.logger.Errorln("等待实例启动失败:", err.Error())
			return err
		}
		// 更新实例状态
		err = models.HostResourceModel.UpdateInstanceStatus(ctx, form.Instances, "Running")
		if err != nil {
			h.logger.Errorf("更新实例状态失败: %v", err.Error())
			return err
		}
		h.logger.Infoln("启动实例成功")
	case "stop":
		h.logger.Infoln("开始停止实例")
		out := &ec2.StopInstancesOutput{}
		err = agentsdk.SyncCall(ctx, h.regionID, "host", "StopInstances", h.accountID, []interface{}{"", &ec2.StopInstancesInput{
			InstanceIds: form.Instances,
		}}, out)
		if err != nil {
			h.logger.Errorln("启动实例失败:", err.Error())
			return err
		}
		err = h.waitForInstanceUtilSpecificStatus(ctx, form.Instances, 10*time.Minute, ec2types.InstanceStateNameStopped)
		if err != nil {
			h.logger.Errorln("等待实例停止失败:", err.Error())
			return err
		}
		h.logger.Infoln("停止实例成功")
		// 更新实例状态
		err = models.HostResourceModel.UpdateInstanceStatus(ctx, form.Instances, "Stopped")
		if err != nil {
			h.logger.Errorf("更新实例状态失败: %v", err.Error())
			return err
		}
	}

	return nil
}

// waitForInstanceUtilSpecificStatus 等待实例到某个状态结束
func (h *HostProvider) waitForInstanceUtilSpecificStatus(ctx context.Context, instanceIDs []string, waitTimeout time.Duration, targetStatus ec2types.InstanceStateName) error {
	// 轮询任务结束，直到任务完成或者超时
	ticker := time.NewTicker(time.Second * 5)
	timeout := time.NewTimer(waitTimeout)
	waitInstances := instanceIDs
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("轮询因为context取消")
		case <-timeout.C:
			return fmt.Errorf("轮询因为超时取消")
		case <-ticker.C:
			if len(waitInstances) == 0 {
				return nil
			}
			apiRound := 1
			var nextToken *string
			stashInstances := make([]string, 0)
			for apiRound == 1 || nextToken != nil {
				instanceIn := &ec2.DescribeInstanceStatusInput{NextToken: nextToken, InstanceIds: waitInstances}
				instanceOut := &ec2.DescribeInstanceStatusOutput{}
				err := agentsdk.SyncCall(ctx, h.regionID, "host", "DescribeInstanceStatus", h.accountID, []interface{}{"", instanceIn}, instanceOut)
				if err != nil {
					return err
				}
				for _, inst := range instanceOut.InstanceStatuses {
					instanceID := aws.StringValue(inst.InstanceId)
					state := inst.InstanceState.Name
					if state != targetStatus {
						stashInstances = append(stashInstances, instanceID)
					}
				}
				apiRound++
				nextToken = instanceOut.NextToken
			}
			waitInstances = stashInstances
		}
	}
}

// DeleteInstance DeleteInstance
func (h *HostProvider) DeleteInstance(ctx context.Context, timeout time.Duration, orderID string, form common.DeleteInstanceForm) error {
	// todo:
	ctx, cancel := context.WithTimeout(context.TODO(), timeout)
	defer cancel()
	reportStore := ""
	for _, instanceID := range form.Instances {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.instance_id", instanceID): instanceID,
			common.ReportSprintf("%s.start_time", instanceID):  time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceID):      "preparing",
		})
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
	out := &ec2.TerminateInstancesOutput{}
	err := agentsdk.SyncCall(ctx, h.regionID, "host", "TerminateInstances", h.accountID, []interface{}{"", &ec2.TerminateInstancesInput{InstanceIds: form.Instances}}, out)
	if err != nil {
		for _, instanceID := range form.Instances {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceID): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceID):    "error",
				common.ReportSprintf("%s.is_error", instanceID):  true,
			})
		}
		common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
		return err
	}
	for _, instanceID := range form.Instances {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", instanceID):  time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceID):     "success",
			common.ReportSprintf("%s.is_success", instanceID): true,
		})
	}
	common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
	return nil
}

// TestPing TestPing
func (h *HostProvider) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	var err error
	if kmsName != "" {
		kmsClient, ok := kms.Clients["cloudman"]
		if !ok {
			return nil, errors.New("cannot get kms client: 'cloudman'")
		}
		ak, sk, err = kmsClient.GetRealAK(kmsName, ak, sk)
		if err != nil {
			return nil, err
		}
	}
	regions := &ec2.DescribeRegionsOutput{}
	err = agentsdk.SyncCallWithAKSK(ctx, h.regionID, "host", "DescribeRegions", "aws", ak, sk, false, []interface{}{"", &ec2.DescribeRegionsInput{}}, regions)
	// 可以从ec2中获取region
	if err != nil {
		return nil, err
	}

	var regionList []*common.Region
	for _, region := range regions.Regions {
		if aws.StringValue(region.OptInStatus) != string(ec2types.AvailabilityZoneOptInStatusNotOptedIn) {
			regionList = append(regionList, &common.Region{
				Name:     awsUtil.GetRegionCN(aws.StringValue(region.RegionName)).CN,
				RegionID: aws.StringValue(region.RegionName),
				IspType:  "aws",
				Endpoint: aws.StringValue(region.Endpoint),
			})
		}
	}
	return regionList, err
}

// GetSnapshotCount ...
func (h *HostProvider) GetSnapshotCount(ctx context.Context) (int32, error) {
	var totalCount int
	times := 1
	var nextToken *string
	for times == 1 || nextToken != nil {
		instanceIn := &ec2.DescribeInstancesInput{NextToken: nextToken}
		instanceOut := &ec2.DescribeInstancesOutput{}
		err := agentsdk.SyncCall(ctx, h.regionID, "host", "DescribeInstances", h.accountID, []interface{}{"", instanceIn}, instanceOut)
		if err != nil {
			return 0, err
		}
		for _, r := range instanceOut.Reservations {
			totalCount += len(r.Instances)
		}
		times++
		nextToken = instanceOut.NextToken
	}
	return int32(totalCount), nil
}

type Ec2Client struct {
	RegionID  string
	AccountID string
}

func NewEc2Client(regionID, accountID string) *Ec2Client {
	return &Ec2Client{
		RegionID:  regionID,
		AccountID: accountID,
	}
}

func (a *Ec2Client) GetAvailabilityZones(ctx context.Context) (*ec2.DescribeAvailabilityZonesOutput, error) {
	describeZonesReq := &ec2.DescribeAvailabilityZonesInput{}
	res := &ec2.DescribeAvailabilityZonesOutput{}
	err := agentsdk.SyncCall(ctx, a.RegionID, "host", "DescribeAvailabilityZones", a.AccountID, []interface{}{"", describeZonesReq}, res)
	if err != nil {
		return nil, err
	}

	return res, err
}
