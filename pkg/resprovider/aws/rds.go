package aws

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/aws/aws-sdk-go-v2/service/rds"
	"github.com/aws/aws-sdk-go-v2/service/rds/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// RDSProvider rds-
type RDSProvider struct {
	logger *logrus.Logger

	dryRun    bool
	accountID string
	regionID  string
}

// CreateInstance 创建实例
func (R RDSProvider) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	var rData CreateDBInput
	reportStore := common.NewReportInstanceOrderStatus(orderID, R.dryRun)
	err = json.Unmarshal(data, &rData)
	if err != nil {
		return nil, err
	}
	instanceNames, err := cloudutils.GetCloudutils(models.MysqlClusterResourceModel).GenInstanceName(
		context.Background(),
		tea.StringValue(rData.DBClusterIdentifier),
		1, true, cloudutils.Linux,
	)
	if err != nil {
		return nil, fmt.Errorf("生成数据库集群名失败：%s", err.Error())
	}
	if len(instanceNames) == 0 {
		return nil, fmt.Errorf("生成数据库集群名失败")
	}
	dBClusterIdentifier := instanceNames[0]
	ctx, cancel := context.WithTimeout(ctx, duration)
	defer cancel()
	ok := R.CheckDBClusterExists(ctx, dBClusterIdentifier)
	if ok {
		return nil, fmt.Errorf("cluster: %s已存在", dBClusterIdentifier)
	}
	if R.dryRun {
		// 不支持dryun参数
		return nil, nil
	}

	dbCluster := &rds.CreateDBClusterOutput{}
	reportStore.ReportStart(ctx, dBClusterIdentifier)
	err = agentsdk.SyncCall(ctx, R.regionID, "mysql", "CreateDBCluster", R.accountID, []interface{}{"", &rds.CreateDBClusterInput{
		DBClusterIdentifier:         aws.String(dBClusterIdentifier),
		Engine:                      rData.Engine,
		AvailabilityZones:           rData.AvailabilityZone,
		BackupRetentionPeriod:       aws.Int32(7),
		CopyTagsToSnapshot:          aws.Bool(true),
		DBSubnetGroupName:           rData.DBSubnetGroupName,
		DeletionProtection:          aws.Bool(true),
		EngineMode:                  rData.GetEngineMode(),
		EngineVersion:               rData.EngineVersion,
		MasterUserPassword:          rData.MasterUserPassword,
		MasterUsername:              rData.MasterUsername,
		StorageEncrypted:            aws.Bool(true),
		Tags:                        rData.GetTags(dBClusterIdentifier),
		VpcSecurityGroupIds:         rData.VpcSecurityGroupIds,
		DBClusterParameterGroupName: rData.DBClusterParameterGroupName,
	}}, dbCluster)
	if err != nil {
		reportStore.ReportFailed(ctx, dBClusterIdentifier)
		R.logger.Errorf("创建DBCluster:%s失败:%s", dBClusterIdentifier, err.Error())
		return nil, err
	}
	reportStore.ReportInstanceId(ctx, dBClusterIdentifier, aws.StringValue(dbCluster.DBCluster.DBClusterIdentifier))
	reportStore.ReportCustomStatus(ctx, dBClusterIdentifier, "created")
	R.logger.Infof("创建DBCluster:%s成功, 准备创建DBInstance", dBClusterIdentifier)
	for i := range 2 {
		zone := rData.AvailabilityZone[i]
		dbInstanceIdentifier := fmt.Sprintf("%s-%s", aws.StringValue(dbCluster.DBCluster.DBClusterIdentifier), zone)
		dbInstance := &rds.CreateDBInstanceOutput{}
		reportStore.ReportJobStart(ctx, dBClusterIdentifier, fmt.Sprintf("CreateInstance%d", i))
		err = agentsdk.SyncCall(ctx, R.regionID, "mysql", "CreateDBInstance", R.accountID, []interface{}{"", &rds.CreateDBInstanceInput{
			DBInstanceClass:      rData.DBInstanceClass,                   // 规格
			Engine:               dbCluster.DBCluster.Engine,              // 数据库引擎
			AvailabilityZone:     aws.String(zone),                        // 可用区
			DBClusterIdentifier:  dbCluster.DBCluster.DBClusterIdentifier, // 实例ID
			DBSubnetGroupName:    rData.DBSubnetGroupName,                 // 绑定子网
			EngineVersion:        dbCluster.DBCluster.EngineVersion,       // 数据库版本
			StorageEncrypted:     aws.Bool(true),                          // 存储加密
			Tags:                 rData.GetTags(dBClusterIdentifier),      // 资源标签
			DBInstanceIdentifier: aws.String(dbInstanceIdentifier),        // 实例名称
			CopyTagsToSnapshot:   aws.Bool(true),                          // 是否复制标签到快照
		}}, dbInstance)
		if err != nil {
			R.logger.Errorf(
				"DBCluster:%s创建DBInstance:%s失败",
				dBClusterIdentifier,
				dbInstanceIdentifier)
			reportStore.ReportJobFailed(ctx, dBClusterIdentifier, fmt.Sprintf("CreateInstance%d", i))
			return nil, err
		}
		R.logger.Infof(
			"DBCluster:%s创建DBInstance:%s成功",
			dBClusterIdentifier,
			dbInstanceIdentifier)
		reportStore.ReportJobSuccess(ctx, dBClusterIdentifier, fmt.Sprintf("CreateInstance%d", i))
	}
	// 等待数据库实例就绪
	err = R.WaitForInstanceActive(ctx, dBClusterIdentifier, 20*time.Minute)
	if err != nil {
		reportStore.ReportFailed(ctx, dBClusterIdentifier)
		return nil, err
	}
	reportStore.ReportSuccess(ctx, dBClusterIdentifier)
	// resp.DBInstance.DBName
	return []string{aws.StringValue(dbCluster.DBCluster.DBClusterIdentifier)}, nil
}

func (R RDSProvider) WaitForInstanceActive(ctx context.Context, clusterID string, timeout time.Duration) error {
	timeoutCtx, timeoutCancel := context.WithTimeout(context.Background(), timeout)
	defer timeoutCancel()
	ticker := time.NewTicker(30 * time.Second)

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("wait for instance active timeout")
		case <-ticker.C:
			resp := &rds.DescribeDBInstancesOutput{}
			err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "DescribeDBInstances", R.accountID, []interface{}{"", &rds.DescribeDBInstancesInput{
				Filters: []types.Filter{
					{
						Name:   aws.String("db-cluster-id"),
						Values: []string{clusterID},
					},
				},
			}}, resp)
			if err != nil {
				return err
			}
			isFinish := true
			for _, instance := range resp.DBInstances {
				if aws.StringValue(instance.DBInstanceStatus) != "available" {
					isFinish = false
					break
				}
			}
			if isFinish {
				return nil
			}
			R.logger.Infof("实例未就绪, 等待30秒后再次查询...")
		}
	}
}

// CheckDBClusterExists 检查DBCluster是否存在
func (R RDSProvider) CheckDBClusterExists(ctx context.Context, instance string) bool {
	resp := &rds.DescribeDBClustersOutput{}
	err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "DescribeDBClusters", R.accountID, []interface{}{"", &rds.DescribeDBClustersInput{
		Filters: []types.Filter{
			{
				Name:   aws.String("db-cluster-id"),
				Values: []string{instance},
			},
		},
	}}, resp)
	return err == nil && len(resp.DBClusters) != 0
}

// UpdateInstance 更新实例
func (R RDSProvider) UpdateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) error {
	var form common.OperateForm
	err := json.Unmarshal(data, &form)
	if err != nil {
		return err
	}

	switch form.Action {
	case "backup":
		realInstances := form.Instances
		reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
		waitCluster := make(map[string]string)
		var errCluster []string

		// 重试实例id处理，要确保实际重试的在原始列表中
		if len(form.RetryInstances) != 0 {
			oriInstanceMap := map[string]bool{}
			realRetryInstances := []string{}
			for _, i := range form.Instances {
				oriInstanceMap[i] = true
			}
			for _, r := range form.RetryInstances {
				if oriInstanceMap[r] {
					realRetryInstances = append(realRetryInstances, r)
				}
			}
			R.logger.Infof("检测到重试配置，本次重试的实例列表：%v", realRetryInstances)
			realInstances = realRetryInstances
		} else {
			R.logger.Infoln("开始备份实例:", form.Instances)
		}

		backupTime := time.Now().Format("2006-01-02-15-04")
		for _, clusterID := range realInstances {
			// 仅支持集群备份
			time.Sleep(time.Duration(1) * time.Second)
			snapshotName := fmt.Sprintf("%s-%s", clusterID, backupTime)
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.instance_id", clusterID): clusterID,
				common.ReportSprintf("%s.start_time", clusterID):  time.Now().Unix(),
				common.ReportSprintf("%s.status", clusterID):      "preparing",
			})
			singleResp := &rds.CreateDBClusterSnapshotOutput{}
			err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "CreateDBClusterSnapshot", R.accountID, []interface{}{"", &rds.CreateDBClusterSnapshotInput{
				DBClusterIdentifier:         aws.String(clusterID),
				DBClusterSnapshotIdentifier: aws.String(snapshotName),
			}}, singleResp)
			if err != nil {
				R.logger.Errorf("备份实例(%s)失败:%s", clusterID, err.Error())
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.status", clusterID):    "start_failed",
					common.ReportSprintf("%s.last_time", clusterID): time.Now().Unix(),
					common.ReportSprintf("%s.is_err", clusterID):    true,
				})
				errCluster = append(errCluster, clusterID)
				continue
			}
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", clusterID):                        time.Now().Unix(),
				common.ReportSprintf("%s.status", clusterID):                           "started",
				common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapshotName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapshotName):    "started",
			})
			R.logger.Infof("备份实例(%s)成功:requestID: %s", clusterID, singleResp.ResultMetadata)
			waitCluster[clusterID] = snapshotName
		}
		reportStore = common.ReportOrderStatusDetail(ctx, orderID, true, reportStore, "", "")

		if len(waitCluster) == 0 {
			R.logger.Errorln("没有实例备份成功")
			return fmt.Errorf("备份失败")
		}

		R.logger.Infof("实例:%s, 创建备份任务成功!等待30秒后查询备份任务结果", waitCluster)
		time.Sleep(30 * time.Second) // 等待10秒后查询备份任务结果

		// 备份最大时间修改为15分钟
		successCluster, err := R.getBackResult(ctx, orderID, waitCluster, timeout, reportStore)
		if err != nil {
			return err
		}
		// 判断传入集群与成功集群的差集
		unfinishedCluster := []string{}
		if len(waitCluster)-len(successCluster) > 0 {
			successMap := map[string]bool{}
			for _, c := range successCluster {
				successMap[c] = true
			}
			for c := range waitCluster {
				if !successMap[c] {
					unfinishedCluster = append(unfinishedCluster, c)
				}
			}
		}
		// 比对传入数量和成功数量，得到最终成功与否判断和日志
		if len(errCluster) != 0 || len(unfinishedCluster) > 0 {
			R.logger.Infof("以下实例备份成功：%v", successCluster)
			if len(errCluster) != 0 {
				R.logger.Errorf("以下实例备份创建备份任务时失败：%v", errCluster)
			}
			if len(unfinishedCluster) > 0 {
				R.logger.Errorf("以下实例成功创建备份任务，但是没有在指定时间范围内结束备份，请通过控制台检查：%v", unfinishedCluster)

			}
			return fmt.Errorf("备份失败")
		}
		reportStore, _ = common.GetOrderStatusDetail(ctx, orderID)
		storeSuccess, storeFailed, _ := common.StatusDetailCount(reportStore)
		if len(storeFailed) != 0 {
			R.logger.Errorf("回顾所有实例发现失败实例: %v", storeFailed)
			return fmt.Errorf("备份失败")
		}
		if len(storeSuccess) != len(form.Instances) {
			R.logger.Errorf("回顾成功实例总数量不正确，已标记成功的实例：%v", storeSuccess)
			return fmt.Errorf("备份失败")
		}
		R.logger.Infoln("所有实例备份成功:", successCluster)
		return nil
	}

	return fmt.Errorf("未能识别的操作")
}

// DeleteInstance 删除实例
func (R RDSProvider) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()
	// 先查询已存在DBInstance, 判断instance参数,是否允许删除
	// var dbClusterFilter []types.Filter
	var dbCluster []*types.DBCluster
	for _, v := range form.Instances {
		// dbClusterFilter = append(dbClusterFilter, types.Filter{
		// 	Name:   aws.String("db-cluster-id"),
		// 	Values: []string{v},
		// })
		resp := &rds.ModifyDBClusterOutput{}
		err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "ModifyDBCluster", R.accountID, []interface{}{"", &rds.ModifyDBClusterInput{
			DeletionProtection:  aws.Bool(false),
			DBClusterIdentifier: aws.String(v),
		}}, resp)
		if err != nil {
			return err
		}
		dbCluster = append(dbCluster, resp.DBCluster)
		R.logger.Infof("解除DBCluster: %s删除保护成功;", aws.StringValue(resp.DBCluster.DBClusterIdentifier))
	}

	// 先删除DBInstance, 再删除DBCluster
	for _, v := range dbCluster {
		for _, instance := range v.DBClusterMembers {
			dbInstanceResp := &rds.DeleteDBInstanceOutput{}
			err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "DeleteDBInstance", R.accountID, []interface{}{"", &rds.DeleteDBInstanceInput{
				DBInstanceIdentifier:      instance.DBInstanceIdentifier,
				DeleteAutomatedBackups:    aws.Bool(true),
				FinalDBSnapshotIdentifier: nil,
				SkipFinalSnapshot:         aws.Bool(true),
			}}, dbInstanceResp)
			if err != nil {
				R.logger.Errorf(
					"DBCluster:%s下,DBInstance:%s删除失败:%s",
					aws.StringValue(v.DBClusterIdentifier),
					aws.StringValue(instance.DBInstanceIdentifier),
					err.Error(),
				)
				return err
			}
			R.logger.Infof(
				"DBCluster:%s下,DBInstance:%s删除成功!",
				aws.StringValue(dbInstanceResp.DBInstance.DBClusterIdentifier),
				aws.StringValue(dbInstanceResp.DBInstance.DBInstanceIdentifier),
			)
		}
		dbClusterResp := &rds.DeleteDBClusterOutput{}
		err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "DeleteDBCluster", R.accountID, []interface{}{"", &rds.DeleteDBClusterInput{
			DBClusterIdentifier:       v.DBClusterIdentifier,
			FinalDBSnapshotIdentifier: nil,
			SkipFinalSnapshot:         aws.Bool(true),
		}}, dbClusterResp)
		if err != nil {
			R.logger.Errorf(
				"DBCluster:%s删除失败:%s",
				aws.StringValue(v.DBClusterIdentifier),
				err.Error(),
			)
			return err
		}
		R.logger.Infof("DBCluster:%s删除成功", aws.StringValue(dbClusterResp.DBCluster.DBClusterIdentifier))
	}

	return nil
}

// TestPing 测试连通性
func (R RDSProvider) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}

// GetSnapshotCount ...
func (R *RDSProvider) GetSnapshotCount(ctx context.Context) (int32, error) {
	var totalCount int
	times := 1
	var marker *string
	for times == 1 || marker != nil {
		clusterOut := &rds.DescribeDBClustersOutput{}
		clusterInput := &rds.DescribeDBClustersInput{Marker: marker}
		err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "DescribeDBClusters", R.accountID, []interface{}{"",
			clusterInput,
		}, clusterOut)
		if err != nil {
			return 0, err
		}
		totalCount += len(clusterOut.DBClusters)
		times++
		marker = clusterOut.Marker
	}
	return int32(totalCount), nil
}

func (R RDSProvider) getBackResult(ctx context.Context, orderID string, snapNames map[string]string, maxTime time.Duration, reportStore string) (success []string, err error) {
	timeout := time.Now().Add(maxTime)
	var waitCluster map[string]string
	err = deepCopy(snapNames, &waitCluster)
	if err != nil {
		return
	}
loop:
	if time.Now().After(timeout) {
		return success, fmt.Errorf("获取结果超时")
	}
	nextCluster := make(map[string]string)
	for clusterID, snapName := range waitCluster {
		resp := &rds.DescribeDBClusterSnapshotsOutput{}
		err := agentsdk.SyncCall(ctx, R.regionID, "mysql", "DescribeDBClusterSnapshots", R.accountID, []interface{}{"", &rds.DescribeDBClusterSnapshotsInput{
			DBClusterSnapshotIdentifier: aws.String(snapName),
			IncludePublic:               aws.Bool(true),
			IncludeShared:               aws.Bool(true),
		}}, resp)
		if err != nil {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapName):    "fetch_error",
				common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, snapName):    true,
			})
			R.logger.Errorf("获取实例(%s)备份任务(%s)失败: %s", clusterID, snapName, err.Error())
			nextCluster[clusterID] = snapName
			continue
		}
		if len(resp.DBClusterSnapshots) == 0 {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapName):    "fetch_error",
				common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, snapName):    true,
			})
			R.logger.Errorf("获取实例(%s)备份任务(%s)结果异常,未发现实例任务/任务后台创建中,等待30秒后再次查询", clusterID, snapName)
			nextCluster[clusterID] = snapName
			continue
		}
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.jobs.j%s.status", clusterID, snapName):    aws.StringValue(resp.DBClusterSnapshots[0].Status),
			common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, snapName): time.Now().Unix(),
		})
		if aws.StringValue(resp.DBClusterSnapshots[0].Status) == "available" {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.jobs.j%s.is_success", clusterID, snapName): true,
				common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, snapName):     false,
			})
			R.logger.Infof("实例(%s)任务(%s)备份成功", clusterID, snapName)
			success = append(success, clusterID)
		} else {
			R.logger.Infof("实例(%s)任务(%s)正在备份中,等待下次查询", clusterID, snapName)
			nextCluster[clusterID] = snapName
		}
		time.Sleep(200 * time.Millisecond)
	}
	for _, successCluster := range success {
		if len(nextCluster[successCluster]) == 0 {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.status", successCluster):     "success",
				common.ReportSprintf("%s.is_success", successCluster): true,
				common.ReportSprintf("%s.is_err", successCluster):     false,
				common.ReportSprintf("%s.last_time", successCluster):  time.Now().Unix(),
			})
		}
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
	waitCluster = nextCluster
	if len(nextCluster) != 0 {
		R.logger.Warnf("获取实例(%v)备份任务失败,30秒后继续查询", nextCluster)
		time.Sleep(30 * time.Second)
		goto loop
	}

	return
}

// GetRDS 生成rds-client
func GetRDS(regionID, accountID string, logger *logrus.Logger, dryRun bool) (*RDSProvider, error) {
	if logger == nil {
		return nil, fmt.Errorf("logger object is null")
	}

	return &RDSProvider{logger: logger, dryRun: dryRun, accountID: accountID, regionID: regionID}, nil
}
