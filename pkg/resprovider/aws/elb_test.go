package aws

import (
	"context"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/elbv2"
	"github.com/sirupsen/logrus"
)

func TestDescribeLoadBalancers(t *testing.T) {
	client := NewLoadBalancerClient("ap-southeast-1", "680214522c78794fe897c51d")
	lbs, err := client.DescribeLoadbalancers(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	for _, lb := range lbs {
		logrus.Infof("lb is %+v", lb)

		listeners, err := client.DescribeListeners(context.Background(), aws.StringValue(lb.LoadBalancerArn))
		if err != nil {
			t.Fatal(err)
		}
		for _, listener := range listeners {
			logrus.Infof("listener is %+v", listener)
		}
	}
}

func TestCreateLoadBalancer(t *testing.T) {
	client := NewLoadBalancerClient("ap-southeast-1", "680214522c78794fe897c51d")
	lb, err := client.CreateLoadBalancer(context.Background(), &elbv2.CreateLoadBalancerInput{
		IpAddressType:  aws.String("ipv4"),
		Name:           aws.String("lb-dev-xieyi-sg-test0001"),
		Scheme:         aws.String("internet-facing"),
		SecurityGroups: aws.StringSlice([]string{"sg-0ccfe8839094ea365"}),
		Subnets:        aws.StringSlice([]string{"subnet-06c3826122daea083", "subnet-075358a8d4606dd34"}),
		Type:           aws.String("application"),
	})
	if err != nil {
		t.Fatal(err)
	}
	logrus.Infof("create loadbalancer success, lb: %+v", lb)

	targetGroup, err := client.CreateTargetGroup(context.Background(), &elbv2.CreateTargetGroupInput{
		Name:     aws.String("tg-dev-xieyi-sg-http80"),
		Port:     aws.Int64(80),
		Protocol: aws.String("HTTP"),
		VpcId:    aws.String("vpc-075f2919674c0cd91"),
	})
	if err != nil {
		t.Fatal(err)
	}
	logrus.Infof("create target group success, target group: %+v", targetGroup)
	targetGroup2, err := client.CreateTargetGroup(context.Background(), &elbv2.CreateTargetGroupInput{
		Name:     aws.String("tg-dev-xieyi-sg-https443"),
		Port:     aws.Int64(443),
		Protocol: aws.String("HTTPS"),
		VpcId:    aws.String("vpc-075f2919674c0cd91"),
	})
	if err != nil {
		t.Fatal(err)
	}

	listener, err := client.CreateListener(context.Background(), &elbv2.CreateListenerInput{
		DefaultActions: []*elbv2.Action{
			{
				TargetGroupArn: targetGroup.TargetGroupArn,
				Type:           aws.String("forward"),
			},
		},
		LoadBalancerArn: lb.LoadBalancerArn,
		Port:            aws.Int64(80),
		Protocol:        aws.String("HTTP"),
	})
	if err != nil {
		t.Fatal(err)
	}
	logrus.Infof("create listener success, listener: %+v", listener)

	listener2, err := client.CreateListener(context.Background(), &elbv2.CreateListenerInput{
		DefaultActions: []*elbv2.Action{
			{
				TargetGroupArn: targetGroup2.TargetGroupArn,
				Type:           aws.String("forward"),
			},
		},
		LoadBalancerArn: lb.LoadBalancerArn,
		Port:            aws.Int64(443),
		Certificates: []*elbv2.Certificate{{
			CertificateArn: aws.String("arn:aws:acm:ap-southeast-1:674965127428:certificate/a52a01f6-dbd0-4180-99bf-eb4fceb1a960"),
		}},
		Protocol: aws.String("HTTPS"),
	})
	if err != nil {
		t.Fatal(err)
	}
	logrus.Infof("create listener success, listener: %+v", listener2)

	timeoutCtx, timeoutCancel := context.WithTimeout(context.Background(), 300*time.Second)
	ticker := time.NewTicker(10 * time.Second)
	for {
		select {
		case <-timeoutCtx.Done():
			t.Fatal("timeout")
		case <-ticker.C:
			lb, err := client.DescribeLoadbalancers(timeoutCtx, aws.StringValue(lb.LoadBalancerArn))
			if err != nil {
				t.Fatal(err)
			}
			if aws.StringValue(lb[0].State.Code) == "active" {
				timeoutCancel()
				logrus.Info("loadbalancer is active")
				return
			}
		}
	}
}
