package aws

import (
	"slices"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/rds/types"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
)

// CreateDBInput 创建rds-form
type CreateDBInput struct {
	AvailabilityZone []string `json:"AvailabilityZone,omitempty" validate:"" field:"可用区"`
	// AutoMinorVersionUpgrade *bool    `json:"AutoMinorVersionUpgrade,omitempty" validate:"" field:"允许版本自动升级"`
	DBClusterIdentifier *string `json:"DBClusterIdentifier,omitempty" validate:"required" field:"DBClusterID"`
	// DBClusterParameterGroupName *string     `json:"DBClusterParameterGroupName,omitempty" validate:"required" field:"DBCluster参数组ID"`
	DBInstanceClass *string `json:"DBInstanceClass,omitempty" validate:"required" field:"DBInstance类型"`
	// DBParameterGroupName *string  `json:"DBParameterGroupName,omitempty" validate:"" field:"DB参数组id"`
	DBSubnetGroupName   *string  `json:"DBSubnetGroupName,omitempty" validate:"" field:"DB子网组"`
	VpcSecurityGroupIds []string `json:"VpcSecurityGroupIds,omitempty" validate:"" field:"安全组"`
	Engine              *string  `json:"Engine,omitempty" validate:"required" field:"引擎类型"`
	EngineVersion       *string  `json:"EngineVersion,omitempty" validate:"required" field:"引擎版本"`
	// DatabaseName        *string  `json:"DatabaseName,omitempty" validate:"" field:"数据库名称"`
	DeletionProtection *bool   `json:"DeletionProtection,omitempty" validate:"" field:"是否开启删除保护"`
	EngineMode         *string `json:"EngineMode,omitempty" validate:"" field:"引擎模式"`
	MasterUsername     *string `json:"MasterUsername,omitempty" validate:"required" field:"数据库用户"`
	MasterUserPassword *string `json:"MasterUserPassword,omitempty" validate:"required" field:"数据库密码"`
	// OptionGroupName      *string     `json:"OptionGroupName,omitempty" validate:"" field:"数据库选项组"`
	// Port *int32      `json:"Port,omitempty" validate:"" field:"数据库端口"`
	Tags                        []types.Tag `json:"Tags,omitempty" validate:"" field:"标签"`
	DBClusterParameterGroupName *string     `json:"DBClusterParameterGroupName,omitempty" validate:"" field:"DBCluster参数组ID"`
}

func (c CreateDBInput) GetTags(instanceName string) []types.Tag {
	tags := []types.Tag{
		{
			Key:   aws.String("nap"),
			Value: aws.String(hostname_util.GetHostNameArea(instanceName)),
		},
		{
			Key:   aws.String("env"),
			Value: aws.String(hostname_util.GetHostNameEnv(instanceName)),
		},
		{
			Key:   aws.String("region"),
			Value: aws.String(hostname_util.GetHostNameRegion(instanceName)),
		},
		{
			Key:   aws.String("module"),
			Value: aws.String(hostname_util.GetHostNameModule(instanceName)),
		},
		{
			Key:   aws.String("region_module"),
			Value: aws.String(hostname_util.GetHostNameRegionModule(instanceName)),
		},
		{
			Key:   aws.String("serial_number"),
			Value: aws.String(hostname_util.GetHostNameRegionModule(instanceName)),
		},
	}
	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	for _, t := range c.Tags {
		// 通用的标签不允许自定义
		if slices.Contains(systemTag, aws.ToString(t.Key)) {
			continue
		}
		tags = append(tags, types.Tag{
			Key:   t.Key,
			Value: t.Value,
		})
	}

	return tags
}

// GetAvailabilityZone 获取可用区
func (c CreateDBInput) GetAvailabilityZone() []string {
	return c.AvailabilityZone
}

// GetEngineMode 获取引擎模式
func (c CreateDBInput) GetEngineMode() *string {
	if c.EngineMode == nil {
		return aws.String("provisioned")
	}

	return c.Engine
}
