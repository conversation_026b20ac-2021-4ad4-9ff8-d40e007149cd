package jumpserver

import (
	"errors"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/kms"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jumpserversdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// Host mhy计算平台操作类，实现操作通用接口
type Host struct {
	logger    *logrus.Logger
	regionID  string
	dryRun    bool
	accountID string
	Host      string
}

// CreateInstance 创建实例
func (m Host) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	return []string{}, nil
}

// UpdateInstance 更新实例
func (m Host) UpdateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) error {
	return nil
}

// DeleteInstance 销毁实例
func (m Host) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	return nil
}

// TestPing ping
func (m Host) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	var err error
	if kmsName != "" {
		kmsClient, ok := kms.Clients["cloudman"]
		if !ok {
			return nil, errors.New("cannot get kms client: 'cloudman'")
		}
		ak, sk, err = kmsClient.GetRealAK(kmsName, ak, sk)
		if err != nil {
			return nil, err
		}
	}
	err = agentsdk.SyncCallWithAKSK(ctx, m.regionID, "host", "GetUserProfile", "jumpserver", ak, sk, false, []interface{}{m.Host})
	if err != nil {
		return nil, err
	}
	defaultRegion := &common.Region{
		Name:     "上海",
		RegionID: "cn-shanghai",
		IspType:  "jumpserver",
		Endpoint: "",
	}
	return []*common.Region{defaultRegion}, nil
}

// GetSnapshotCount ...
func (m Host) GetSnapshotCount(ctx context.Context) (int32, error) {
	var pageSize, offset int64 = 100, 0
	var totalCount = 0
	for {
		var hosts []*jumpserversdk.Host
		err := agentsdk.SyncCall(ctx, m.regionID, "host", "ListHosts", m.accountID, []interface{}{m.Host, offset, pageSize}, &hosts)
		if err != nil {
			return 0, err
		}
		if len(hosts) == 0 {
			break
		}
		totalCount += len(hosts)
		offset += pageSize
	}
	return int32(totalCount), nil
}

// ResourceJumpserverInstance 新建js操作接口实例
func ResourceJumpserverInstance(regionID, accountID, host string, logger *logrus.Logger, dry bool) (*Host, error) {
	if logger == nil {
		return nil, fmt.Errorf("logger object is null")
	}

	return &Host{logger: logger, regionID: regionID, dryRun: dry, accountID: accountID, Host: host}, nil
}
