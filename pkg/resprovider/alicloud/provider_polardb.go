package alicloud

import (
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"sync"
	"time"

	polardb20170801 "github.com/alibabacloud-go/polardb-20170801/v6/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/kr/pretty"
	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// AliPolardb AliPolardb obj
type AliPolardb struct {
	client *AliPolarDBClient
	logger *logrus.Logger

	dryRun    bool
	accountID string
}

// CreateInstance 创建数据库实例
func (a AliPolardb) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	var req CreatePolarDBInstanceInput
	err = json.Unmarshal(data, &req)
	if err != nil {
		return nil, err
	}
	instanceNames, err := cloudutils.GetCloudutils(models.MysqlClusterResourceModel).GenInstanceName(
		context.Background(),
		tea.StringValue(req.DBClusterDescription),
		int(req.Amount), true, cloudutils.Linux,
	)
	if err != nil {
		return nil, fmt.Errorf("生成数据库集群名失败：%s", err.Error())
	}
	if a.dryRun {
		// 接口不支持参数预校验
		return nil, nil
	}

	// tag参数重新序列化
	tag := struct {
		Tag []*common.Tag `json:"Tag"`
	}{}
	err = json.Unmarshal(data, &tag)
	if err != nil {
		return nil, err
	}
	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	customTag := []*polardb20170801.CreateDBClusterRequestTag{}
	if len(tag.Tag) > 0 {
		for _, t := range tag.Tag {
			// 通用的标签不允许自定义
			if slices.Contains(systemTag, t.Key) {
				continue
			}
			customTag = append(customTag, &polardb20170801.CreateDBClusterRequestTag{
				Key:   &t.Key,
				Value: &t.Value,
			})
		}
	}
	a.logger.Infof("绑定标签为%# v", pretty.Formatter(req.Tag))

	reportStore := ""
	for _, instanceName := range instanceNames {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.start_time", instanceName):    time.Now().Unix(),
			common.ReportSprintf("%s.instance_name", instanceName): instanceName,
			common.ReportSprintf("%s.status", instanceName):        "preparing",
		})
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)

	fullSuccess := true
	instanceMap := map[string]string{}
	var resp *polardb20170801.CreateDBClusterResponseBody
	for i := 0; i < int(req.Amount); i++ {
		req.DBClusterDescription = tea.String(instanceNames[i])
		req.Tag = a.getInstanceTag(instanceNames[i], customTag)
		resp, err = a.client.RunInstance(ctx, duration, req.CreateDBClusterRequest)
		if err != nil {
			fullSuccess = false
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceNames[i]): time.Now().Unix(),
				common.ReportSprintf("%s.is_err", instanceNames[i]):    true,
				common.ReportSprintf("%s.status", instanceNames[i]):    "create_failed",
			})
			a.logger.Errorf("创建实例失败,错误信息: %v", err)
			break
		}
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", instanceNames[i]):   time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceNames[i]):      "created",
			common.ReportSprintf("%s.instance_id", instanceNames[i]): tea.StringValue(resp.DBClusterId),
		})
		instanceMap[tea.StringValue(resp.DBClusterId)] = instanceNames[i]
		instances = append(instances, tea.StringValue(resp.DBClusterId))
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)

	if !fullSuccess {
		a.logger.Errorf("分批创建实例失败,本次创建成功实例为: %v, 请联系管理员手工处理", strings.Join(instances, ","))
		return nil, fmt.Errorf("创建实例失败")
	}

	a.logger.Infof("开始获取已创建实例状态: %v", instances)
	// 后处理实例列表
	err = a.afterCreateAction(ctx, 30*time.Minute, orderID, req, instanceMap, reportStore)
	if err != nil {
		return nil, err
	}
	a.logger.Infof("实例创建成功: %v", instances)

	// 初始化
	initPolarDBFuncs := make(map[string]func(ctx context.Context) error)
	for instanceID, instanceName := range instanceMap {
		instanceName := instanceName
		instanceID := instanceID
		initPolarDBFuncs[instanceName] = func(ctx context.Context) error {
			return a.initPolardb(ctx, instanceID, instanceName, orderID, req)
		}
	}

	reportStore, err = runTaskConcurrently(ctx, initPolarDBFuncs, orderID, reportStore, "init", 10)
	if err != nil {
		return nil, err
	}

	// 上报成功状态
	for _, instanceName := range instanceMap {
		instanceName := instanceName
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", instanceName):  time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceName):     "success",
			common.ReportSprintf("%s.is_success", instanceName): true,
		})
	}

	return instances, err
}

func (a AliPolardb) getInstanceTag(hostname string, customTags []*polardb20170801.CreateDBClusterRequestTag) []*polardb20170801.CreateDBClusterRequestTag {
	// 加上region, env, module, nap, region_module这几个通用的标签
	tags := []*polardb20170801.CreateDBClusterRequestTag{
		{
			Key:   tea.String("nap"),
			Value: tea.String(hostname_util.GetHostNameArea(hostname)),
		},
		{
			Key:   tea.String("env"),
			Value: tea.String(hostname_util.GetHostNameEnv(hostname)),
		},
		{
			Key:   tea.String("region"),
			Value: tea.String(hostname_util.GetHostNameRegion(hostname)),
		},
		{
			Key:   tea.String("module"),
			Value: tea.String(hostname_util.GetHostNameModule(hostname)),
		},
		{
			Key:   tea.String("region_module"),
			Value: tea.String(hostname_util.GetHostNameRegionModule(hostname)),
		},
		{
			Key:   tea.String("serial_number"),
			Value: tea.String(hostname_util.GetHostNameRegionModule(hostname)),
		},
	}
	tags = append(tags, customTags...)
	return tags
}

// afterCreateAction 创建后动作
func (a AliPolardb) afterCreateAction(ctx context.Context, duration time.Duration, orderID string, req CreatePolarDBInstanceInput, instanceMap map[string]string, reportStore string) error {
	var todoList []string
	for id := range instanceMap {
		todoList = append(todoList, id)
	}
	if len(todoList) == 0 {
		return nil
	}

	timeCtx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()
	for {
		select {
		case <-timeCtx.Done():
			for _, instanceID := range todoList {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceMap[instanceID]): time.Now().Unix(),
					common.ReportSprintf("%s.status", instanceMap[instanceID]):    "confirm_timeout",
					common.ReportSprintf("%s.is_err", instanceMap[instanceID]):    true,
				})
			}
			return fmt.Errorf("获取结果超时")
		default:
			resp, err := a.client.DescribeDBClusters(ctx, 1, 50, todoList...)
			if err != nil {
				a.logger.Warnf("获取实例%s状态失败,忽略报错,继续轮询", strings.Join(todoList, ","))
				// 忽略获取实例的报错
				continue
			}
			for _, instance := range resp.Items.DBCluster {
				instanceName := tea.StringValue(instance.DBClusterDescription)
				clusterID := tea.StringValue(instance.DBClusterId)
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
					common.ReportSprintf("%s.status", instanceName):    tea.StringValue(instance.DBClusterStatus),
				})
				if tea.StringValue(instance.DBClusterStatus) != "Running" {
					continue
				}
				// todoList里面去掉当前的集群ID
				todoList, _ = utils.StrArraySplice(todoList, clusterID)
			}
			if len(todoList) == 0 {
				// 上报成功状态
				return nil
			}
			a.logger.Infof("实例%v创建中, 30s后继续查询", todoList)
			time.Sleep(30 * time.Second)
		}
	}
}

func (a AliPolardb) initPolardb(
	ctx context.Context, dbClusterID, instanceName string, orderID string, req CreatePolarDBInstanceInput,
) error {
	// 绑定IP白名单模版
	err := a.BindGlobalSecurityIP(ctx, dbClusterID, instanceName, orderID, req.GlobalSecurityIPTempalteIds)
	if err != nil {
		return err
	}

	// 正式集群修改备份策略
	err = a.ModifyBackupStrategy(ctx, dbClusterID, instanceName, orderID)
	if err != nil {
		return err
	}

	// 创建账号
	err = a.CreateDBAccounts(ctx, req.AccountList, dbClusterID, instanceName, orderID)
	if err != nil {
		return err
	}

	// 创建自定义endpoint和修改集群对应的URL
	err = a.DealEndpoint(ctx, dbClusterID, instanceName, orderID)
	if err != nil {
		return err
	}

	// 绑定安全组
	err = a.BindSecurityGroup(ctx, dbClusterID, instanceName, orderID, req.SecurityGroupIds)
	if err != nil {
		return err
	}

	return a.ChangeMonitorPeriod(ctx, dbClusterID, instanceName)
}

func (a AliPolardb) CreateDBAccounts(ctx context.Context, accounts []*DBAccount, clusterID, instanceName, orderID string) error {
	// 创建数据库账号
	for _, account := range accounts {
		aInput := CreatePolarDBAccountInput{}
		aInput.SetDBClusterId(clusterID)
		aInput.SetAccountType(account.AccountType)
		aInput.SetAccountName(account.AccountName)
		aInput.SetAccountPassword(account.AccountPassword)
		aInput.SetAccountDescription("本账号由云管平台创建")
		err := a.client.CreateAccount(ctx, aInput)
		if err != nil {
			a.logger.Errorf("实例:%s创建账号%s失败, 请联系管理员手工处理, err: %v", clusterID, account.AccountName, err)
			return err
		}
		a.logger.Infof("实例:%s创建账号%s成功", clusterID, account.AccountName)
	}
	a.logger.Infof("实例:%s创建账号成功", clusterID)
	return nil
}

func (a AliPolardb) BindGlobalSecurityIP(ctx context.Context, clusterID, instanceName, orderID string, templateIDs []string) error {
	if len(templateIDs) == 0 {
		return nil
	}

	ids := strings.Join(templateIDs, ",")
	wInput := ModifyGlobalSecurityIPGroupRelationInput{}
	wInput.SetDBClusterId(clusterID)
	wInput.SetRegionId(a.client.regionID)
	wInput.SetGlobalSecurityGroupId(ids)
	err := a.client.ModifyGlobalSecurityIPGroupRelation(ctx, wInput)
	if err != nil {
		a.logger.Errorf("实例:%s绑定IP白名单%s失败, 请联系管理员手工处理", clusterID, ids)
		return err
	}
	a.logger.Infof("实例:%s绑定IP白名单%s成功", clusterID, ids)

	return nil
}

func (a AliPolardb) BindSecurityGroup(ctx context.Context, clusterID, instanceName, orderID string, securityGroupIDs []string) error {
	if len(securityGroupIDs) == 0 {
		return nil
	}

	wInput := ModifyDBClusterAccessWhitelistInput{}
	wInput.SetDBClusterId(clusterID)
	wInput.SetSecurityGroupIds(strings.Join(securityGroupIDs, ","))
	wInput.SetWhiteListType("SecurityGroup")

	err := a.client.ModifyDBClusterAccessWhitelist(ctx, wInput)
	if err != nil {
		a.logger.Errorf("实例:%s绑定安全组失败, 请联系管理员手工处理", clusterID)
		return err
	}

	err = a.waitForReady(ctx, clusterID)
	if err != nil {
		a.logger.Errorf("实例:%s绑定安全组后等待集群ready超时", clusterID)
		return err
	}
	a.logger.Infof("实例:%s绑定安全组成功", clusterID)

	return nil
}

func (a AliPolardb) ChangeMonitorPeriod(ctx context.Context, clusterID, instanceName string) error {
	nameList := strings.Split(instanceName, "-")
	if len(nameList) != 5 || utils.StrArrayIndexOf([]string{"live", "prod", "ce", "beta", "exam"}, nameList[1]) == -1 {
		a.logger.Infof("实例:%s为非正式环境,不调整监控频率", clusterID)
		return nil
	}

	err := a.client.ModifyDBClusterMonitor(ctx, clusterID, "5")
	if err != nil {
		a.logger.Errorf("实例:%s更改监控频率失败, 请联系管理员手工处理", clusterID)
		return err
	}
	a.logger.Infof("实例:%s更改监控频率成功", clusterID)
	return nil
}

func (a AliPolardb) DealEndpoint(ctx context.Context, clusterID, instanceName, orderID string) error {
	// 1. 查询endpoint
	endpoints, err := a.client.DescribeDBClusterEndpoints(ctx, clusterID)
	if err != nil {
		a.logger.Errorf("实例:%s查询endpoint失败, 请联系管理员手工处理", clusterID)
		return err
	}

	hasCustomEndpoint := false
	for _, endpoint := range endpoints.Items {
		if *endpoint.EndpointType == "Custom" && *endpoint.ReadWriteMode == "ReadOnly" {
			hasCustomEndpoint = true
			break
		}
	}

	// 2. 如果没有就创建自定义的只读endpoint
	if !hasCustomEndpoint {
		cInput := CreateDBClusterEndpointInput{}
		cInput.SetEndpointType("Custom")
		cInput.SetDBClusterId(clusterID)
		cInput.SetReadWriteMode("ReadOnly")
		cInput.SetAutoAddNewNodes("Enable")
		cInput.SetDBEndpointDescription(fmt.Sprintf("%s-ro", instanceName))
		err := a.client.CreateDBClusterEndpoint(ctx, cInput)
		if err != nil {
			a.logger.Errorf("实例:%s创建自定义只读endpoint失败, 请联系管理员手工处理", clusterID)
			return err
		}
		// 这里强制等待5s是为了等待endpoint创建成功并且集群状态刷新
		time.Sleep(5 * time.Second)
		a.logger.Infof("实例:%s创建自定义只读endpoint成功", clusterID)
		err = a.waitForReady(ctx, clusterID)
		if err != nil {
			a.logger.Errorf("实例:%s创建自定义只读endpoint后等待集群ready超时", clusterID)
			return err
		}
		// 创建后再查询一次endpoints
		endpoints, err = a.client.DescribeDBClusterEndpoints(ctx, clusterID)
		if err != nil {
			a.logger.Errorf("实例:%s查询endpoint失败, 请联系管理员手工处理", clusterID)
			return err
		}
	}

	// 3. 修改集群的endpoint链接
	for _, endpoint := range endpoints.Items {
		prefix := strings.ReplaceAll(strings.ToLower(instanceName), ".", "dot")
		switch {
		case *endpoint.EndpointType == "Primary":
			continue
		case *endpoint.EndpointType == "Cluster" && *endpoint.ReadWriteMode == "ReadWrite":
			mInput := ModifyDBEndpointAddressInput{}
			mInput.SetDBEndpointId(*endpoint.DBEndpointId)
			mInput.SetDBClusterId(clusterID)
			mInput.SetNetType("Private")
			mInput.SetConnectionStringPrefix(fmt.Sprintf("%s-rw", prefix))
			err = a.client.ModifyDBEndpointAddress(ctx, mInput, 20*time.Minute)
			if err != nil {
				a.logger.Errorf("实例:%s修改集群读写地址失败, 请联系管理员手工处理, err: %v", clusterID, err)
				return err
			}
			a.logger.Infof("实例:%s修改集群读写成功", clusterID)
		case *endpoint.EndpointType == "Custom" && *endpoint.ReadWriteMode == "ReadOnly":
			mInput := ModifyDBEndpointAddressInput{}
			mInput.SetDBEndpointId(*endpoint.DBEndpointId)
			mInput.SetDBClusterId(clusterID)
			mInput.SetNetType("Private")
			mInput.SetConnectionStringPrefix(fmt.Sprintf("%s-ro", prefix))
			err = a.client.ModifyDBEndpointAddress(ctx, mInput, 20*time.Minute)
			if err != nil {
				a.logger.Errorf("实例:%s修改集群只读地址失败, 请联系管理员手工处理, err: %v", clusterID, err)
				return err
			}
			a.logger.Infof("实例:%s修改集群只读地址成功", clusterID)
		default:
			// do nothing
		}
	}

	// 修改地址后，阿里云后台需要进行解析的修改，并且没有接口可以获得这个修改的状态，这里强制休眠3分钟，避免后面同步获取到老的地址
	a.logger.Infof("实例:%s修改集群解析地址成功,等待3分钟后阿里云后台解析修改完毕...", clusterID)
	time.Sleep(3 * time.Minute)

	// 4. 等待集群ready
	return a.waitForReady(ctx, clusterID)
}

func (a AliPolardb) waitForReady(ctx context.Context, instanceID string) error {
	timeout := time.Minute * 5
	return common.Retry(timeout, func() *common.RetryError {
		resp, err := a.client.DescribeDBClusters(ctx, 1, 50, instanceID)
		if err != nil {
			return common.NonRetryableError(err)
		}
		if len(resp.Items.DBCluster) != 1 {
			return common.NonRetryableError(fmt.Errorf("获取实例失败"))
		}

		if tea.StringValue(resp.Items.DBCluster[0].DBClusterStatus) != "Running" {
			return common.RetryableError(errors.New("not ready"))
		}
		return nil
	})
}

func (a AliPolardb) ModifyBackupStrategy(ctx context.Context, clusterID, instanceName, orderID string) error {
	nameList := strings.Split(instanceName, "-")
	if len(nameList) != 5 || utils.StrArrayIndexOf([]string{"live", "prod", "ce", "beta", "exam"}, nameList[1]) == -1 {
		a.logger.Infof("实例:%s为非正式环境,不调整备份策略", clusterID)
		return nil
	}

	mInput := ModifyBackupPolicyInput{}
	mInput.SetDBClusterId(clusterID)
	mInput.SetBackupFrequency("2/24H")
	mInput.SetDataLevel1BackupFrequency("2/24H")
	mInput.SetDataLevel1BackupRetentionPeriod("7")
	mInput.SetDataLevel2BackupRetentionPeriod("0")
	mInput.SetBackupRetentionPolicyOnClusterDeletion("LATEST")
	err := a.client.ModifyBackupPolicy(ctx, mInput)
	if err != nil {
		a.logger.Errorf("实例:%s修改备份策略失败, 请联系管理员手工处理", clusterID)
		return err
	}

	a.logger.Infof("实例:%s修改备份策略成功", clusterID)
	return nil
}

// DeleteInstance 删除实例
func (a AliPolardb) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	for _, v := range form.Instances {
		cluster, err := models.MysqlClusterResourceModel.GetByInstanceID(ctx, v)
		if err != nil {
			return err
		}
		if cluster.PayType != "Postpaid" {
			err = a.client.TransformDBClusterToPostpaid(ctx, v)
			if err != nil {
				a.logger.Errorf("删除前转换实例%s为按量付费失败:%s", v, err.Error())
				return err
			}
			a.logger.Infof("实例%s转换为按量付费成功", v)
		} else {
			a.logger.Infof("实例%s为按量付费, 无需转换", v)
		}

		// 等待10秒，等实例转为按量付费成功
		time.Sleep(10 * time.Second)

		err = a.client.DeleteDBCluster(ctx, polardb20170801.DeleteDBClusterRequest{
			DBClusterId: tea.String(v),
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// TestPing xxxx
func (a AliPolardb) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}

// GetSnapshotCount ...
func (a AliPolardb) GetSnapshotCount(ctx context.Context) (int32, error) {
	var pageSize, page, totalCount int32 = 50, 1, 0
	body, err := a.client.DescribeDBClusters(ctx, page, pageSize)
	if err != nil {
		return 0, err
	}
	totalCount = tea.Int32Value(body.TotalRecordCount)
	return totalCount, nil
}

func (a AliPolardb) backupInstance(ctx context.Context, orderID string, form common.OperateForm, timeout time.Duration) error {
	realInstances := form.Instances
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	waitCluster := map[string]string{}
	errCluster := make([]string, 0)
	instances, err := models.MysqlClusterResourceModel.GetByInstanceIDs(ctx, realInstances)
	if err != nil {
		return err
	}
	instanceNameMap := map[string]string{}
	for _, instance := range instances {
		instanceNameMap[instance.DBClusterID] = instance.DBClusterDescription
	}

	// 重试实例id处理，要确保实际重试的在原始列表中
	if len(form.RetryInstances) != 0 {
		oriInstanceMap := map[string]bool{}
		realRetryInstances := []string{}
		for _, i := range form.Instances {
			oriInstanceMap[i] = true
		}
		for _, r := range form.RetryInstances {
			if oriInstanceMap[r] {
				realRetryInstances = append(realRetryInstances, r)
			}
		}
		a.logger.Infof("检测到重试配置，本次重试的实例列表：%v", realRetryInstances)
		realInstances = realRetryInstances
	} else {
		a.logger.Infoln("开始备份实例:", form.Instances)
	}

	for _, clusterID := range realInstances {
		// CreateBackup流控200/60s
		time.Sleep(300 * time.Millisecond)
		instanceName := instanceNameMap[clusterID]
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.status", clusterID):        "preparing",
			common.ReportSprintf("%s.start_time", clusterID):    time.Now().Unix(),
			common.ReportSprintf("%s.instance_id", clusterID):   clusterID,
			common.ReportSprintf("%s.instance_name", clusterID): instanceName,
			common.ReportSprintf("%s.is_err", clusterID):        false,
			common.ReportSprintf("%s.is_success", clusterID):    false,
		})
		var (
			singleResp *polardb20170801.CreateBackupResponseBody
			err        error
		)
		// 官服使用二级备份进行长存储
		// 或者在表单中配置了二级备份
		if strings.Contains(instanceName, "nap-prod-gf") && (form.IsLevel2Backup == nil || *form.IsLevel2Backup) {
			a.logger.Errorf("实例%s使用二级备份进行长存储", instanceName)
			singleResp, err = a.client.CreateLevel2Backup(ctx, clusterID, 7300)
		} else {
			singleResp, err = a.client.CreateBackup(ctx, clusterID)
		}
		if err != nil {
			a.logger.Errorf("备份实例(%s)失败:%s", clusterID, err.Error())
			errCluster = append(errCluster, clusterID)
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.status", clusterID):    "start_failed",
				common.ReportSprintf("%s.last_time", clusterID): time.Now().Unix(),
				common.ReportSprintf("%s.is_err", clusterID):    true,
			})
			continue
		}
		jobID := tea.StringValue(singleResp.BackupJobId)
		reportStore = common.ResetOrderStatusDetail(ctx, orderID, false, reportStore, common.ReportSprintf("%s.jobs", clusterID))
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", clusterID):                 time.Now().Unix(),
			common.ReportSprintf("%s.status", clusterID):                    "started",
			common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID): time.Now().Unix(),
			common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID):    "started",
		})
		waitCluster[clusterID] = jobID
		a.logger.Infof("创建实例(%s)备份任务(%s)", clusterID, jobID)
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)

	if len(waitCluster) == 0 {
		a.logger.Errorln("没有实例备份成功")
		return fmt.Errorf("备份失败")
	}

	a.logger.Infof("实例:%s, 创建备份任务成功!等待30秒后查询备份任务结果", waitCluster)
	time.Sleep(30 * time.Second) // 等待30秒后查询备份任务结果

	// 轮询获取备份结果
	successCluster, err := a.getBackResult(ctx, orderID, waitCluster, timeout, reportStore)
	if err != nil {
		return err
	}

	return a.getBackSummary(ctx, waitCluster, successCluster, errCluster, orderID, form)
}

func (a AliPolardb) getUnfinsihedCluster(waitCluster map[string]string, successCluster []string) []string {
	unfinishedCluster := []string{}
	if len(waitCluster)-len(successCluster) == 0 {
		return unfinishedCluster
	}
	successMap := map[string]bool{}
	for _, c := range successCluster {
		successMap[c] = true
	}
	for c := range waitCluster {
		if successMap[c] {
			continue
		}
		unfinishedCluster = append(unfinishedCluster, c)
	}
	return unfinishedCluster
}

func (a AliPolardb) getBackSummary(ctx context.Context, waitCluster map[string]string, successCluster, errCluster []string, orderID string, form common.OperateForm) error {
	// 判断传入集群与成功集群的差集
	backupError := fmt.Errorf("备份失败")
	unfinishedCluster := a.getUnfinsihedCluster(waitCluster, successCluster)
	// 比对传入数量和成功数量，得到最终成功与否判断和日志
	if len(errCluster) != 0 || len(unfinishedCluster) > 0 {
		a.logger.Infof("以下实例备份成功：%v", successCluster)
		if len(errCluster) != 0 {
			a.logger.Errorf("以下实例备份创建备份任务时失败：%v", errCluster)
		}
		if len(unfinishedCluster) > 0 {
			a.logger.Errorf("以下实例成功创建备份任务，但是没有在指定时间范围内结束备份，请通过控制台检查：%v", unfinishedCluster)
		}
		return backupError
	}
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	storeSuccess, storeFailed, _ := common.StatusDetailCount(reportStore)
	if len(storeFailed) != 0 {
		a.logger.Errorf("回顾所有实例发现失败实例: %v", storeFailed)
		return backupError
	}
	if len(storeSuccess) != len(form.Instances) {
		a.logger.Errorf("回顾成功实例总数量不正确，已标记成功的实例：%v", storeSuccess)
		return backupError
	}
	a.logger.Infoln("所有实例备份成功:", storeSuccess)
	return nil
}

// UpdateInstance 更新实例
func (a AliPolardb) UpdateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) error {
	var form common.OperateForm
	err := json.Unmarshal(data, &form)
	if err != nil {
		return err
	}

	switch form.Action {
	case "ali_backup", "backup":
		return a.backupInstance(ctx, orderID, form, timeout)
	case "reboot":
		return a.reboot(ctx, form.Instances)
	}

	return fmt.Errorf("未能识别的操作")
}

func (a AliPolardb) UploadBackupToOSS(ctx context.Context, originalOrder, downloadOrder *entity.ResOrder) error {
	a.logger.Infof("开始上传备份数据到OSS上")
	orderID := downloadOrder.ID.Hex()
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)

	resMap := map[string]instanceBackupOrderInfo{}
	err := json.Unmarshal([]byte(originalOrder.StatusDetail), &resMap)
	if err != nil {
		return err
	}
	taskInfo := make([]*backupTaskInfo, 0)
	for _, orderInfo := range resMap {
		instanceID := orderInfo.InstanceID
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.status", instanceID):        "preparing",
			common.ReportSprintf("%s.start_time", instanceID):    time.Now().Unix(),
			common.ReportSprintf("%s.instance_id", instanceID):   instanceID,
			common.ReportSprintf("%s.instance_name", instanceID): orderInfo.InstanceName,
		})

		backup, err := a.client.GetLatestManualBackup(ctx, 1*time.Minute, orderInfo.InstanceID)
		if err != nil {
			reportStore = reportFailed(ctx, orderID, instanceID, reportStore, "start_failed")
			a.logger.Errorf("create instance: %s download task failed, because get backup info failed, err: %v", orderInfo.InstanceName, err)
			continue
		}
		backupID := tea.StringValue(backup.BackupId)
		bucketName, err := getBackupOSSName(downloadOrder.RegionID)
		if err != nil {
			reportStore = reportFailed(ctx, orderID, instanceID, reportStore, "start_failed")
			a.logger.Errorf("create instance: %s download task failed, because get oss bucket name failed, err: %v", orderInfo.InstanceName, err)
			continue
		}
		backupPrefix, err := getBackupOSSPrefix(orderInfo.InstanceName)
		if err != nil {
			reportStore = reportFailed(ctx, orderID, instanceID, reportStore, "start_failed")
			a.logger.Errorf("create instance: %s download task failed, because get oss prefix failed, err: %v", orderInfo.InstanceName, err)
			continue
		}
		taskID, err := a.client.DownloadToOSS(ctx, orderInfo.InstanceID, backupID, bucketName, backupPrefix)
		if err != nil {
			reportStore = reportFailed(ctx, orderID, instanceID, reportStore, "start_failed")
			a.logger.Errorf("create instance: %s download task failed, because create download task failed, err: %v", orderInfo.InstanceName, err)
			continue
		}
		a.logger.Infof("开始上传实例 %s 到OSS上, 备份ID %s, bucket名称: %s, 路径前缀: %s", orderInfo.InstanceName, backupID, bucketName, backupPrefix)

		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", instanceID):                 time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceID):                    "started",
			common.ReportSprintf("%s.jobs.%s.last_time", instanceID, taskID): time.Now().Unix(),
			common.ReportSprintf("%s.jobs.%s.status", instanceID, taskID):    "started",
		})

		taskInfo = append(taskInfo, &backupTaskInfo{
			instanceID:   orderInfo.InstanceID,
			instanceName: orderInfo.InstanceName,
			backupID:     backupID,
			taskID:       taskID,
		})
	}

	unfinished, newReportStore, hasError := a.WaitForDownloadTaskFinish(ctx, taskInfo, reportStore, orderID)

	for _, f := range unfinished {
		instanceID := f.instanceID
		newReportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, newReportStore, common.ReportKv{
			common.ReportSprintf("%s.status", instanceID):    "not_finished",
			common.ReportSprintf("%s.last_time", instanceID): time.Now().Unix(),
			common.ReportSprintf("%s.is_err", instanceID):    true,
		})
	}
	if hasError {
		return fmt.Errorf("has error sub task")
	}
	return nil
}

func reportFailed(ctx context.Context, orderID, instanceID, reportStore, status string) string {
	return common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.status", instanceID):    status,
		common.ReportSprintf("%s.last_time", instanceID): time.Now().Unix(),
		common.ReportSprintf("%s.is_err", instanceID):    true,
	})
}

func getBackupOSSName(regionID string) (string, error) {
	switch regionID {
	case "cn-shanghai":
		return "nap-shanghai-backup", nil
	case "ap-southeast-1":
		return "nap-sg-backup-os", nil
	case "ap-northeast-1":
		return "nap-jp-backup-os", nil
	case "eu-central-1":
		return "nap-eu-backup-os", nil
	case "us-east-1":
		return "nap-us-backup-os", nil
	default:
		return "", fmt.Errorf("wrong region")
	}
}

func getBackupOSSPrefix(instanceName string) (string, error) {
	// prod_gf_cn/20240716/polardb/nap-prod-gf-cn-orderid0001/*
	dateStr := time.Now().Format("20060102")
	region, err := utils.GetRegionByInstanceName(instanceName)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s/%s/polardb/%s/", region, dateStr, instanceName), nil
}

type backupTaskInfo struct {
	instanceID   string
	instanceName string
	backupID     string
	taskID       string
	Err          error
}

// instanceBackupOrderInfo 实例备份工单
type instanceBackupOrderInfo struct {
	InstanceID   string `json:"instance_id"`
	InstanceName string `json:"instance_name"`
}

func (a AliPolardb) WaitForDownloadTaskFinish(ctx context.Context, taskInfos []*backupTaskInfo, reportStore, orderID string) (unfinished []*backupTaskInfo, newReportStore string, hasError bool) {
	currentTasks := taskInfos
	timeout := 24 * time.Hour
	timeCtx, cancel := context.WithTimeout(context.Background(), timeout)
	hasError = false
	newReportStore = reportStore
	unfinished = make([]*backupTaskInfo, 0)
	defer cancel()
	for {
		select {
		case <-timeCtx.Done():
			for _, task := range currentTasks {
				task.Err = fmt.Errorf("get task status timeout")
				unfinished = append(unfinished, task)
			}
			a.logger.Errorf("有%d个任务超时, 请在阿里云控制台上继续查看任务状态", len(currentTasks))
			hasError = true
			return unfinished, newReportStore, hasError
		default:
			nextTasks := make([]*backupTaskInfo, 0)
			for _, task := range currentTasks {
				instanceID := task.instanceID
				taskStatus, err := a.client.GetDownloadStatus(ctx, task.instanceID, task.backupID, task.taskID)
				if err != nil {
					a.logger.Errorf("查询备份上传任务状态失败, err: %v", err)
					task.Err = err
					unfinished = append(unfinished, task)
					hasError = true
					continue
				}
				if taskStatus == "finished" {
					newReportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, newReportStore, common.ReportKv{
						common.ReportSprintf("%s.status", instanceID):                          "success",
						common.ReportSprintf("%s.last_time", instanceID):                       time.Now().Unix(),
						common.ReportSprintf("%s.is_success", instanceID):                      true,
						common.ReportSprintf("%s.jobs.%s.last_time", instanceID, task.taskID):  time.Now().Unix(),
						common.ReportSprintf("%s.jobs.%s.status", instanceID, task.taskID):     "success",
						common.ReportSprintf("%s.jobs.%s.is_success", instanceID, task.taskID): true,
					})
				} else if taskStatus == "failed" || taskStatus == "expired" {
					newReportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, newReportStore, common.ReportKv{
						common.ReportSprintf("%s.status", instanceID):                         "failed",
						common.ReportSprintf("%s.last_time", instanceID):                      time.Now().Unix(),
						common.ReportSprintf("%s.is_err", instanceID):                         true,
						common.ReportSprintf("%s.jobs.%s.last_time", instanceID, task.taskID): time.Now().Unix(),
						common.ReportSprintf("%s.jobs.%s.status", instanceID, task.taskID):    "failed",
						common.ReportSprintf("%s.jobs.%s.is_err", instanceID, task.taskID):    true,
					})
					hasError = true
				} else {
					nextTasks = append(nextTasks, task)
				}
			}
			if len(nextTasks) == 0 {
				return unfinished, newReportStore, hasError
			}
			currentTasks = nextTasks
			a.logger.Infof("还有%d个任务未完成, 等待30秒后重新轮询...", len(currentTasks))
			time.Sleep(30 * time.Second)
		}
	}

}

func (a AliPolardb) reboot(ctx context.Context, instanceIds []string) error {
	mModels, err := models.MysqlClusterResourceModel.GetByInstanceIDs(ctx, instanceIds)
	if err != nil {
		return err
	}

	var wg sync.WaitGroup
	errChan := make(chan error, len(mModels))
	for _, m := range mModels {
		m := m
		wg.Add(1)
		go func() {
			defer wg.Done()
			for _, n := range m.DBNodes.DBNode {
				a.logger.Infof("开始重启实例%s的节点%s, 节点类型: %s", m.DBClusterDescription, n.DBNodeID, n.DBNodeClass)
				err = a.client.RestartNode(ctx, n.DBNodeID)
				if err != nil {
					a.logger.Errorf("重启实例%s的节点%s失败, err: %s", m.DBClusterDescription, n.DBNodeID, err)
					errChan <- fmt.Errorf("restart instance failed, err: %s", err)
					return
				}
				a.logger.Infof("重启实例%s的节点%s完成", m.DBClusterDescription, n.DBNodeID)
				a.logger.Infof("等待实例%s就绪", m.DBClusterDescription)
				err := a.waitForNodeReady(ctx, m.DBClusterID, n.DBNodeID)
				if err != nil {
					a.logger.Errorf("重启实例%s的节点%s失败, 实例未就绪, err: %s", m.DBClusterDescription, n.DBNodeID, err)
					errChan <- fmt.Errorf("restart instance failed, err: %s", err)
					return
				}
				a.logger.Infof("实例%s就绪", m.DBClusterDescription)
			}
		}()
	}

	wg.Wait()
	close(errChan)
	errMessages := make([]string, 0)
	for {
		err, ok := <-errChan
		if !ok {
			break
		}
		errMessages = append(errMessages, err.Error())
	}

	if len(errMessages) != 0 {
		return fmt.Errorf(strings.Join(errMessages, "\n"))
	}
	return nil
}

func deepCopy(src interface{}, dst interface{}) error {
	data, err := json.Marshal(src)
	if err != nil {
		return err
	}

	return json.Unmarshal(data, dst)
}

func (a AliPolardb) getBackResult(ctx context.Context, orderID string, checkCluster map[string]string, maxTime time.Duration, reportStore string) (success []string, err error) {
	waitCluster := checkCluster
	if err != nil {
		return
	}
	timeCtx, cancel := context.WithTimeout(context.Background(), maxTime)
	defer cancel()
	for {
		select {
		case <-timeCtx.Done():
			return success, fmt.Errorf("获取结果超时")
		default:
			nextCluster := make(map[string]string)
			for clusterID, jobID := range waitCluster {
				// DescribeBackupTasks 流控为200/60(s)
				taskResp, err := a.client.DescribeBackupTasks(ctx, 1*time.Minute, clusterID, jobID)
				if err != nil {
					reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
						common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID):    "fetch_error",
						common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID): time.Now().Unix(),
						common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, jobID):    true,
					})
					a.logger.Errorf("获取实例(%s)备份任务(%s)失败: %s", clusterID, jobID, err.Error())
					nextCluster[clusterID] = jobID
					continue
				}
				if len(taskResp.Items.BackupJob) == 0 {
					reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
						common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID):    "fetch_error",
						common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID): time.Now().Unix(),
						common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, jobID):    true,
					})
					a.logger.Errorf("获取实例(%s)备份任务(%s)结果异常,未发现实例任务", clusterID, jobID)
					nextCluster[clusterID] = jobID
					continue
				}
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID):    tea.StringValue(taskResp.Items.BackupJob[0].BackupProgressStatus),
					common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID): time.Now().Unix(),
				})
				if tea.StringValue(taskResp.Items.BackupJob[0].BackupProgressStatus) == "Finished" {
					reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
						common.ReportSprintf("%s.jobs.j%s.is_success", clusterID, jobID): true,
						common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, jobID):     false,
						common.ReportSprintf("%s.status", clusterID):                     "success",
						common.ReportSprintf("%s.is_success", clusterID):                 true,
						common.ReportSprintf("%s.is_err", clusterID):                     false,
						common.ReportSprintf("%s.last_time", clusterID):                  time.Now().Unix(),
					})
					a.logger.Infof("实例(%s)备份成功", clusterID)
					success = append(success, clusterID)
					continue
				}
				a.logger.Infof("实例(%s)正在备份中,等待下次查询", clusterID)
				nextCluster[clusterID] = jobID
			}
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
			waitCluster = nextCluster
			if len(waitCluster) == 0 {
				return
			}
			a.logger.Infof("实例(%v)未备份完成,30秒后再次查询", waitCluster)
			time.Sleep(30 * time.Second)
		}
	}
}

// ResourceAliyunPoldbInstance 初始化AliPolardb
func ResourceAliyunPoldbInstance(regionID, accountID string, logger *logrus.Logger, dryRun bool) (*AliPolardb, error) {
	client := CreateAliPolarDBClient(regionID, accountID)
	if logger == nil {
		return nil, errors.New("logger object is null")
	}
	return &AliPolardb{client: client, logger: logger, dryRun: dryRun, accountID: accountID}, nil
}

func (a AliPolardb) waitForNodeReady(ctx context.Context, instanceID, nodeID string) error {
	timeout := time.Minute * 10
	return common.Retry(timeout, func() *common.RetryError {
		clusterAttribute, err := a.client.DescribeDBClusterAttribute(ctx, instanceID)
		if err != nil {
			if strings.Contains(err.Error(), "Code: Throttling.User") {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}

		for _, node := range clusterAttribute.DBNodes {
			if tea.StringValue(node.DBNodeId) != nodeID {
				continue
			}

			if tea.StringValue(node.DBNodeStatus) != "Running" {
				return common.RetryableError(errors.New("not ready"))
			}
		}

		// 还需要判断一下cluster是否ready
		if tea.StringValue(clusterAttribute.DBClusterStatus) != "Running" {
			return common.RetryableError(errors.New("not ready"))
		}

		return nil
	})
}
