package alicloud

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	ecs20140526V3 "github.com/alibabacloud-go/ecs-20140526/v3/client"

	ecs20140526 "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	vpc20160428 "github.com/alibabacloud-go/vpc-20160428/v6/client"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cache"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// DefaultTimeout 默认超时时间120分钟
const DefaultTimeout = 120

// DefaultInterval 默认间隔时间5秒
const DefaultInterval = 5

var (
	// ErrTagNil tag nil
	ErrTagNil = errors.New("tag is nil")
	// ErrEcsInstanceNil instance nil
	ErrEcsInstanceNil = errors.New("ecs instance is nil")
)

var (
	// Pending 创建中
	Pending = "Pending"
	// Running 运行中
	Running = "Running"
)

// AliEcsClient 阿里云ecs-client
type AliEcsClient struct {
	regionID  string
	accountID string

	//client *ecs20140526.Client
}

// CreateAliEcsClient create ali client
func CreateAliEcsClient(regionID, accountID string) (client *AliEcsClient, err error) {
	// 访问的域名
	// config.Endpoint = tea.String(endpoint)
	return &AliEcsClient{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

// SetTimeout set timeout
//func (a *AliEcsClient) SetTimeout(time time.Duration) {
//	a.client.ReadTimeout = tea.Int(int(time.Milliseconds()))
//}

// SetRegionID 修改region
func (a *AliEcsClient) SetRegionID(regionID string) {
	a.regionID = regionID
}

// GetAliRegions 查询可以使用的阿里云地域
func (a *AliEcsClient) GetAliRegions(ctx context.Context) (*ecs20140526.DescribeRegionsResponseBody, error) {
	describeRegionsReq := &ecs20140526.DescribeRegionsRequest{}
	res := &ecs20140526.DescribeRegionsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeRegions", a.accountID, []interface{}{describeRegionsReq}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// GetAliZones 获取阿里云可用区
func (a *AliEcsClient) GetAliZones(ctx context.Context) (*ecs20140526.DescribeZonesResponseBody, error) {
	describeZonesReq := &ecs20140526.DescribeZonesRequest{
		RegionId:       tea.String(a.regionID),
		AcceptLanguage: tea.String("zh-CN"),
	}
	res := &ecs20140526.DescribeZonesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeZones", a.accountID, []interface{}{describeZonesReq}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// GetAccountAttributes 获取账号下资源配额https://help.aliyun.com/document_detail/25412.htm?spm=a2c4g.********.2.9.5c284b4bknyYsz
func (a *AliEcsClient) GetAccountAttributes(ctx context.Context, zoneID string, attributeNames []string) (*ecs20140526.DescribeAccountAttributesResponseBody, error) {
	describeAccountAttributesReq := &ecs20140526.DescribeAccountAttributesRequest{
		RegionId:      tea.String(a.regionID),
		ZoneId:        tea.String(zoneID),
		AttributeName: tea.StringSlice(attributeNames),
	}
	res := &ecs20140526.DescribeAccountAttributesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeAccountAttributes", a.accountID, []interface{}{describeAccountAttributesReq}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// GetVPCs 获取阿里云VPC列表
func (a *AliEcsClient) GetVPCs(ctx context.Context, page, size int32) (*ecs20140526.DescribeVpcsResponseBody, error) {
	describeVpcsRequest := &ecs20140526.DescribeVpcsRequest{
		RegionId:   tea.String(a.regionID),
		PageNumber: tea.Int32(page),
		PageSize:   tea.Int32(size), // 允许传入的最大值为50
	}
	// 复制代码运行请自行打印 API 的返回值
	res := &ecs20140526.DescribeVpcsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeVpcs", a.accountID, []interface{}{describeVpcsRequest}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, err
}

// AddTags 给资源加标签
func (a *AliEcsClient) AddTags(ctx context.Context, resourceType, resourceID string, tags []common.Tag) error {
	if len(tags) == 0 {
		return ErrTagNil
	}

	var t []*ecs20140526.AddTagsRequestTag
	for _, v := range tags {
		t = append(t, &ecs20140526.AddTagsRequestTag{
			Key:   tea.String(v.Key),
			Value: tea.String(v.Value),
		})
	}

	addTagsRequest := &ecs20140526.AddTagsRequest{
		RegionId:     tea.String(a.regionID),
		ResourceId:   tea.String(resourceID),
		ResourceType: tea.String(resourceType),
		Tag:          t,
	}

	res := &ecs20140526.AddTagsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "AddTags", a.accountID, []interface{}{addTagsRequest}, res)
	return err
}

// RemoveTags 移除资源标签
func (a *AliEcsClient) RemoveTags(ctx context.Context, resourceType, resourceID string, tags []common.Tag) error {
	if len(tags) == 0 {
		return ErrTagNil
	}

	var t []*ecs20140526.RemoveTagsRequestTag
	for _, v := range tags {
		t = append(t, &ecs20140526.RemoveTagsRequestTag{
			Key:   tea.String(v.Key),
			Value: tea.String(v.Value),
		})
	}

	removeTagsRequest := &ecs20140526.RemoveTagsRequest{
		RegionId:     tea.String(a.regionID),
		ResourceId:   tea.String(resourceID),
		ResourceType: tea.String(resourceType),
		Tag:          t,
	}

	res := &ecs20140526.RemoveTagsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "RemoveTags", a.accountID, []interface{}{removeTagsRequest}, res)
	return err
}

// DescribeInstances 获取ecs实例列表
func (a *AliEcsClient) DescribeAllInstances(ctx context.Context, size int32, nextToken string) (*ecs20140526.DescribeInstancesResponseBody, error) {
	describeInstancesRequest := &ecs20140526.DescribeInstancesRequest{
		RegionId:             tea.String(a.regionID),
		MaxResults:           tea.Int32(size),
		AdditionalAttributes: tea.StringSlice([]string{"NETWORK_PRIMARY_ENI_IP"}),
	}
	if nextToken != "" {
		describeInstancesRequest.SetNextToken(nextToken)
	}

	res := &ecs20140526.DescribeInstancesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstances", a.accountID, []interface{}{describeInstancesRequest}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// DescribeInstances 获取ecs实例列表
func (a *AliEcsClient) DescribeInstances(ctx context.Context, page, size int32, zoneID string, instanceIDs ...string) (*ecs20140526.DescribeInstancesResponseBody, error) {
	describeInstancesRequest := &ecs20140526.DescribeInstancesRequest{
		RegionId:             tea.String(a.regionID),
		PageNumber:           tea.Int32(page),
		PageSize:             tea.Int32(size),
		AdditionalAttributes: tea.StringSlice([]string{"NETWORK_PRIMARY_ENI_IP"}),
	}
	if zoneID != "" {
		describeInstancesRequest.ZoneId = tea.String(zoneID)
	}

	if len(instanceIDs) > 0 {
		instanceIDsJSON, _ := json.Marshal(instanceIDs)
		describeInstancesRequest.InstanceIds = tea.String(string(instanceIDsJSON))
	}

	res := &ecs20140526.DescribeInstancesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstances", a.accountID, []interface{}{describeInstancesRequest}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// DescribeInstanceAttribute 获取ecs实例详情
func (a *AliEcsClient) DescribeInstanceAttribute(ctx context.Context, instanceID string) (*ecs20140526.DescribeInstanceAttributeResponseBody, error) {
	request := &ecs20140526.DescribeInstanceAttributeRequest{
		InstanceId: tea.String(instanceID),
	}

	res := &ecs20140526.DescribeInstanceAttributeResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstanceAttribute", a.accountID, []interface{}{request}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// ModifyInstanceChargeType ...
func (a *AliEcsClient) ModifyInstanceChargeType(ctx context.Context, instanceID, regionID string) error {
	resp := &ecs20140526.ModifyInstanceChargeTypeResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "ModifyInstanceChargeType", a.accountID, []interface{}{&ecs20140526.ModifyInstanceChargeTypeRequest{
		InstanceIds:        tea.String(fmt.Sprintf(`["%s"]`, instanceID)),
		RegionId:           tea.String(regionID),
		InstanceChargeType: tea.String("PostPaid"),
		AutoPay:            tea.Bool(true),
	}}, resp)
	if err != nil {
		return err
	}
	return nil
}

// ModifyInstanceName 修改实例名称
func (a *AliEcsClient) ModifyInstanceName(ctx context.Context, instanceID, instanceName string) error {
	resp := &ecs20140526.ModifyInstanceAttributeResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "ModifyInstanceAttribute", a.accountID, []interface{}{&ecs20140526.ModifyInstanceAttributeRequest{
		InstanceId:   &instanceID,
		InstanceName: &instanceName,
		HostName:     &instanceName,
	}}, resp)
	if err != nil {
		return err
	}
	return nil
}

// ModifyInstanceDeletionProtection 修改实例释放保护状态
func (a *AliEcsClient) ModifyInstanceDeletionProtection(ctx context.Context, instanceID string, deletionProtection bool) error {
	resp := &ecs20140526.ModifyInstanceAttributeResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "ModifyInstanceAttribute", a.accountID, []interface{}{&ecs20140526.ModifyInstanceAttributeRequest{
		InstanceId:         tea.String(instanceID),
		DeletionProtection: tea.Bool(deletionProtection),
	}}, resp)
	if err != nil {
		return err
	}
	return nil
}

// GetECSTypeFamilies 查询云服务器ECS提供的实例规格族列表
func (a *AliEcsClient) GetECSTypeFamilies(ctx context.Context) (*ecs20140526.DescribeInstanceTypeFamiliesResponseBody, error) {
	describeInstanceTypeFamiliesRequest := &ecs20140526.DescribeInstanceTypeFamiliesRequest{
		RegionId: tea.String(a.regionID),
	}

	res := &ecs20140526.DescribeInstanceTypeFamiliesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstanceTypeFamilies", a.accountID, []interface{}{describeInstanceTypeFamiliesRequest}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, nil
}

// GetECSInstanceTypes 获取ecs实例类型
func (a *AliEcsClient) GetECSInstanceTypes(ctx context.Context) (*ecs20140526.DescribeInstanceTypesResponseBody, error) {
	req := &ecs20140526.DescribeInstanceTypesRequest{
		InstanceTypeFamily: nil,
		InstanceTypes:      nil,
	}

	resp := &ecs20140526.DescribeInstanceTypesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstanceTypes", a.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}
	return resp.Body, err
}

// GetSecurityGroup 获取安全组
func (a *AliEcsClient) GetSecurityGroup(ctx context.Context, vpcID string, securityGroupIds ...string) ([]*ecs20140526.DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroup, error) {
	maxLoop := 100
	describeSecurityGroupsRequest := &ecs20140526.DescribeSecurityGroupsRequest{
		RegionId:   tea.String(a.regionID),
		VpcId:      tea.String(vpcID),
		MaxResults: tea.Int32(100),
	}

	if len(securityGroupIds) != 0 {
		securityGroupIdsJSON, _ := json.Marshal(securityGroupIds)
		describeSecurityGroupsRequest.SecurityGroupIds = tea.String(string(securityGroupIdsJSON))
	}

	secGroups := make([]*ecs20140526.DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroup, 0)
	for i := 0; i <= maxLoop; i++ {
		res := &ecs20140526.DescribeSecurityGroupsResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeSecurityGroups", a.accountID, []interface{}{describeSecurityGroupsRequest}, res)
		if err != nil {
			return nil, err
		}
		secGroups = append(secGroups, res.Body.SecurityGroups.SecurityGroup...)
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		describeSecurityGroupsRequest.SetNextToken(*res.Body.NextToken)
	}

	return secGroups, nil
}

// GetSecurityGroupAttribute 获取安全组规则
func (a *AliEcsClient) GetSecurityGroupAttribute(ctx context.Context, securityGroupID string) (*ecs20140526V3.DescribeSecurityGroupAttributeResponseBody, error) {
	describeSecurityGroupAttributeRequest := &ecs20140526V3.DescribeSecurityGroupAttributeRequest{
		RegionId:        tea.String(a.regionID),
		SecurityGroupId: tea.String(securityGroupID),
	}

	res := &ecs20140526V3.DescribeSecurityGroupAttributeResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "hostV3", "DescribeSecurityGroupAttribute", a.accountID, []interface{}{describeSecurityGroupAttributeRequest}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, nil
}

// AuthorizeSecurityGroup 增加安全组入方向规则
func (a *AliEcsClient) AuthorizeSecurityGroup(ctx context.Context, req *ecs20140526V3.AuthorizeSecurityGroupRequest) error {
	res := &ecs20140526V3.AuthorizeSecurityGroupResponse{}
	return agentsdk.SyncCall(ctx, a.regionID, "hostV3", "AuthorizeSecurityGroup", a.accountID, []interface{}{req}, res)
}

// RevokeSecurityGroup 删除一条或多条入方向安全组规则
func (a *AliEcsClient) RevokeSecurityGroup(ctx context.Context, regionID, securityGroupID string, ruleIDs []string) error {
	req := &ecs20140526V3.RevokeSecurityGroupRequest{}
	req.SetRegionId(regionID)
	req.SetSecurityGroupId(securityGroupID)
	req.SetSecurityGroupRuleId(tea.StringSlice(ruleIDs))
	res := &ecs20140526V3.RevokeSecurityGroupResponse{}
	return agentsdk.SyncCall(ctx, a.regionID, "hostV3", "RevokeSecurityGroup", a.accountID, []interface{}{req}, res)
}

/*
RunInstances 批量创建ECS实例
单次最大上线为100台,创建前可先调用GetAvailableResource接口判断资源是否备货充足,提前反馈给用户
请求发送成功后,调用WaitForInstance判断资源是否继续,然后继续购买
(alicloud-terraform仅判断RunInstance请求返回结果中第一个id为running就算创建成功,算是个bug? --!)
同一账户下多次购买需要做限流处理
*/
func (a *AliEcsClient) RunInstances(ctx context.Context, timeout time.Duration, buyArg *ecs20140526.RunInstancesRequest) (*ecs20140526.RunInstancesResponseBody, error) {
	// createInstanceReq = buyArg
	createInstanceReq := &ecs20140526V3.RunInstancesRequest{}
	err := utils.Copy(buyArg, createInstanceReq)
	if err != nil {
		return nil, err
	}
	createInstanceReq.NetworkOptions = &ecs20140526V3.RunInstancesRequestNetworkOptions{
		EnableJumboFrame: tea.Bool(false),
	}
	createInstanceReq.RegionId = tea.String(a.regionID)
	wait := common.IncrementalWait(1*time.Second, 1*time.Second)
	var body *ecs20140526.RunInstancesResponseBody
	err = common.Retry(timeout, func() *common.RetryError {
		res := &ecs20140526.RunInstancesResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "hostV3", "RunInstances", a.accountID, []interface{}{createInstanceReq}, res)
		if err != nil {
			if IsThrottling(err) {
				wait()
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		body = res.Body
		return nil
	})

	if err != nil {
		return nil, err
	}

	return body, nil
}

// WaitForInstance 等待Instance就绪
func (a *AliEcsClient) WaitForInstance(ctx context.Context, instanceIDs []*string, targetStatus string, timeout int) error {
	if timeout <= 0 {
		timeout = DefaultTimeout
	}
	buf, err := json.Marshal(instanceIDs)
	if err != nil {
		return err
	}
	describeInstanceReq := &ecs20140526.DescribeInstancesRequest{
		RegionId:    tea.String(a.regionID),
		InstanceIds: tea.String(string(buf)),
		PageSize:    tea.Int32(100),
	}

	var founding bool
	for {
		res := &ecs20140526.DescribeInstancesResponse{}
		err = agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstances", a.accountID, []interface{}{describeInstanceReq}, res)
		if err != nil && !NotFoundError(err) {
			return err
		}

		if len(res.Body.Instances.Instance) != len(instanceIDs) {
			continue
		}

		founding = false
		for _, v := range res.Body.Instances.Instance {
			fmt.Println(tea.StringValue(v.Status), targetStatus)
			if tea.StringValue(v.Status) != targetStatus {
				founding = true
				break
			}
		}
		if !founding {
			return nil
		}
		timeout -= DefaultInterval
		if timeout < 0 {
			return errors.New("wait timeout")
		}

		time.Sleep(DefaultInterval * time.Second)
	}
}

// DeleteInstances 批量删除ECS实例
func (a *AliEcsClient) DeleteInstances(ctx context.Context, instanceIds []string, force bool) error {
	if len(instanceIds) == 0 {
		return ErrEcsInstanceNil
	}

	timeout := 1 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		res := &ecs20140526.DeleteInstancesResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "host", "DeleteInstances", a.accountID, []interface{}{&ecs20140526.DeleteInstancesRequest{
			Force:      tea.Bool(force),
			RegionId:   tea.String(a.regionID),
			InstanceId: tea.StringSlice(instanceIds),
		}}, res)
		if err != nil {
			if strings.Contains(err.Error(), "Code: Throttling.User") {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})

	return err
}

/*
GetAvailableResource 获取待购买实例是否备货充足
*/
func (a *AliEcsClient) GetAvailableResource(ctx context.Context) (*ecs20140526.DescribeAvailableResourceResponseBody, error) {
	availableResourceReq := &ecs20140526.DescribeAvailableResourceRequest{
		RegionId:            tea.String(a.regionID),
		InstanceChargeType:  nil,
		SpotStrategy:        nil,
		SpotDuration:        nil,
		DestinationResource: nil,
		ZoneId:              nil,
		IoOptimized:         nil,
		DedicatedHostId:     nil,
		InstanceType:        nil,
		SystemDiskCategory:  nil,
		DataDiskCategory:    nil,
		NetworkCategory:     nil,
		Cores:               nil,
		Memory:              nil,
		ResourceType:        nil,
		Scope:               nil,
	}

	res := &ecs20140526.DescribeAvailableResourceResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeAvailableResource", a.accountID, []interface{}{availableResourceReq}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, nil
}

// DescribeImages 获取镜像列表
func (a *AliEcsClient) DescribeImages(ctx context.Context, osType string, imageName string) (*ecs20140526.DescribeImagesResponseBody, error) {
	req := &ecs20140526.DescribeImagesRequest{
		RegionId:             tea.String(a.regionID),
		Status:               tea.String("Available"),
		IsSupportIoOptimized: tea.Bool(true),
		IsSupportCloudinit:   tea.Bool(true),
		PageNumber:           tea.Int32(1),
		PageSize:             tea.Int32(100),
	}

	if osType != "" {
		req.OSType = tea.String(osType)
	}

	if imageName != "" {
		req.ImageName = tea.String(imageName)
	}

	resp := &ecs20140526.DescribeImagesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeImages", a.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}

	return resp.Body, nil
}

// GetImageByID ...
func (a *AliEcsClient) GetImageByID(ctx context.Context, imageID string) (*ecs20140526.DescribeImagesResponseBodyImagesImage, error) {
	req := &ecs20140526.DescribeImagesRequest{
		RegionId:             tea.String(a.regionID),
		Status:               tea.String("Available"),
		IsSupportIoOptimized: tea.Bool(true),
		IsSupportCloudinit:   tea.Bool(true),
		PageNumber:           tea.Int32(1),
		PageSize:             tea.Int32(100),
		ImageId:              tea.String(imageID),
	}

	resp := &ecs20140526.DescribeImagesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeImages", a.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}

	if len(resp.Body.Images.Image) == 0 {
		return nil, fmt.Errorf("cannot get image")
	}

	return resp.Body.Images.Image[0], nil
}

// GetKeyPair 获取密钥对, 支持模糊匹配
func (a *AliEcsClient) GetKeyPair(ctx context.Context, keyPairName string) (*ecs20140526.DescribeKeyPairsResponseBody, error) {
	res := &ecs20140526.DescribeKeyPairsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeKeyPairs", a.accountID, []interface{}{&ecs20140526.DescribeKeyPairsRequest{
		RegionId:    tea.String(a.regionID),
		KeyPairName: tea.String(keyPairName),
	}}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, nil
}

// GetVSwitches 获取网络与交换机
func (a *AliEcsClient) GetVSwitches(ctx context.Context, zoneID string, vpcID string) (*ecs20140526.DescribeVSwitchesResponseBody, error) {
	res := &ecs20140526.DescribeVSwitchesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeVSwitches", a.accountID, []interface{}{&ecs20140526.DescribeVSwitchesRequest{
		VpcId:    tea.String(vpcID),
		ZoneId:   tea.String(zoneID),
		RegionId: tea.String(a.regionID),
		PageSize: tea.Int32(50),
	}}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, nil
}

// DescribeDisks 获取磁盘对象
func (a *AliEcsClient) DescribeDisks(ctx context.Context, instanceID string) (*ecs20140526.DescribeDisksResponseBody, error) {
	res := &ecs20140526.DescribeDisksResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeDisks", a.accountID, []interface{}{&ecs20140526.DescribeDisksRequest{
		RegionId:   tea.String(a.regionID),
		InstanceId: tea.String(instanceID),
		PageNumber: tea.Int32(1),
		PageSize:   tea.Int32(100),
	}}, res)

	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// StopInstances 停止实例
func (a *AliEcsClient) StopInstances(ctx context.Context, instanceIDs []string, force ...bool) (*ecs20140526.StopInstancesResponseBody, error) {
	req := &ecs20140526.StopInstancesRequest{
		RegionId:    tea.String(a.regionID),
		InstanceId:  tea.StringSlice(instanceIDs),
		StoppedMode: tea.String("KeepCharging"),
	}
	if len(force) != 0 && force[0] {
		req.ForceStop = tea.Bool(true)
	}
	resp := &ecs20140526.StopInstancesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "StopInstances", a.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}

	return resp.Body, nil
}

// StartInstances 启动实例
func (a *AliEcsClient) StartInstances(ctx context.Context, instanceIDs []string) (*ecs20140526.StartInstancesResponseBody, error) {
	resp := &ecs20140526.StartInstancesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "StartInstances", a.accountID, []interface{}{&ecs20140526.StartInstancesRequest{
		RegionId:   tea.String(a.regionID),
		InstanceId: tea.StringSlice(instanceIDs),
	}}, resp)
	if err != nil {
		return nil, err
	}

	return resp.Body, nil
}

// RebootInstances 重启实例
func (a *AliEcsClient) RebootInstances(ctx context.Context, instanceIDs []string) (*ecs20140526.RebootInstancesResponseBody, error) {
	resp := &ecs20140526.RebootInstancesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "RebootInstances", a.accountID, []interface{}{&ecs20140526.RebootInstancesRequest{
		RegionId:   tea.String(a.regionID),
		InstanceId: tea.StringSlice(instanceIDs),
	}}, resp)
	if err != nil {
		return nil, err
	}

	return resp.Body, nil
}

// GetRegions 获取阿里可用地域，可用来测试账号可用性
func (a *AliEcsClient) GetRegions(ctx context.Context) (*ecs20140526.DescribeRegionsResponseBody, error) {
	resp := &ecs20140526.DescribeRegionsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeRegions", a.accountID, []interface{}{&ecs20140526.DescribeRegionsRequest{
		AcceptLanguage: tea.String("zh-CN"),
	}}, resp)
	if err != nil {
		return nil, err
	}
	return resp.Body, nil
}

// GetAvailEipBySegIDAndLock ...
func (c *AliEcsClient) GetAvailEipBySegIDAndLock(ctx context.Context, segID string) (string, string, error) {
	req := &vpc20160428.DescribeEipAddressesRequest{}
	req.SetRegionId(c.regionID)
	req.SetSegmentInstanceId(segID)
	req.SetStatus("Available")
	req.SetPageSize(100)
	res := &vpc20160428.DescribeEipAddressesResponse{}

	timeout := 5 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		err := agentsdk.SyncCall(ctx, c.regionID, "vpc", "DescribeEipAddresses", c.accountID, []interface{}{req}, res)
		if err != nil {
			if strings.Contains(err.Error(), "Code: Throttling.User") {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
	if err != nil {
		return "", "", err
	}
	if tea.Int32Value(res.Body.TotalCount) == 0 {
		return "", "", fmt.Errorf("cannot find available eip address")
	}
	for _, eipAddress := range res.Body.EipAddresses.EipAddress {
		lockErr := cache.TryLock(ctx, fmt.Sprintf("cloudman-takumi:bind-eip:%s", tea.StringValue(eipAddress.AllocationId)), 60)
		if lockErr == nil {
			return tea.StringValue(eipAddress.AllocationId), tea.StringValue(eipAddress.IpAddress), nil
		}
	}

	return "", "", fmt.Errorf("cannot find available eip address")
}

// AllocateEIP 创建EIP
func (a *AliEcsClient) AllocateEIP(ctx context.Context) (*vpc20160428.AllocateEipAddressResponseBody, error) {
	request := &vpc20160428.AllocateEipAddressRequest{}
	request.SetBandwidth("200")
	request.SetISP("BGP")
	request.SetNetmode("public")
	request.SetInstanceChargeType("PostPaid")
	request.SetInternetChargeType("PayByTraffic")
	request.SetRegionId(a.regionID)

	resp := &vpc20160428.AllocateEipAddressResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "AllocateEipAddress", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return nil, err
	}

	return resp.Body, err
}

// AssociateEIP 绑定EIP到实例
func (a *AliEcsClient) AssociateEIP(ctx context.Context, eipID string, instanceID string, instanceType string) error {
	request := &vpc20160428.AssociateEipAddressRequest{}
	request.SetAllocationId(eipID)
	request.SetInstanceId(instanceID)
	request.SetInstanceType(instanceType)
	resp := &vpc20160428.AssociateEipAddressResponse{}
	timeout := 5 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "AssociateEipAddress", a.accountID, []interface{}{request}, resp)
		if err != nil {
			if strings.Contains(err.Error(), "Code: Throttling.User") {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})

	return err
}

// SetEIPName 设置EIP名称
func (a *AliEcsClient) SetEIPName(ctx context.Context, eipID string, name string) error {
	request := &vpc20160428.ModifyEipAddressAttributeRequest{}
	request.SetAllocationId(eipID)
	request.SetName(name)
	resp := &vpc20160428.ModifyEipAddressAttributeResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "ModifyEipAddressAttribute", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return err
	}

	return err
}

// SetEcs 设置资源标签
func (a *AliEcsClient) SetResourceTag(ctx context.Context, resourceID, resourceType string, tags []*vpc20160428.TagResourcesRequestTag) error {
	request := &vpc20160428.TagResourcesRequest{}
	request.SetResourceId([]*string{&resourceID})
	request.SetResourceType(resourceType)
	request.SetTag(tags)
	request.SetRegionId(a.regionID)

	resp := &vpc20160428.TagResourcesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "TagResources", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return err
	}

	return err
}

// SetECSResourceTag 设置资源标签
func (a *AliEcsClient) SetECSResourceTag(ctx context.Context, resourceID, resourceType string, tags []*ecs20140526.TagResourcesRequestTag) error {
	request := &ecs20140526.TagResourcesRequest{}
	request.SetResourceId([]*string{&resourceID})
	request.SetResourceType(resourceType)
	request.SetTag(tags)
	request.SetRegionId(a.regionID)

	resp := &vpc20160428.TagResourcesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "TagResources", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return err
	}

	return err
}

// UnTagResource 解绑标签
func (a *AliEcsClient) UnTagResource(ctx context.Context, resourceID, resourceType string, isAll bool, tagKeys []string) error {
	request := &vpc20160428.UnTagResourcesRequest{}
	request.SetResourceId([]*string{&resourceID})
	request.SetResourceType("EIP")
	if len(tagKeys) != 0 {
		request.SetTagKey(tea.StringSlice(tagKeys))
	}
	request.SetAll(isAll)
	request.SetRegionId(a.regionID)
	resp := &vpc20160428.TagResourcesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "UnTagResources", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return err
	}

	return err
}

func (a *AliEcsClient) ReleaseEipAddress(ctx context.Context, eipID string) error {
	request := &vpc20160428.ReleaseEipAddressRequest{}
	request.SetAllocationId(eipID)

	resp := &vpc20160428.ReleaseEipAddressResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "ReleaseEipAddress", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return err
	}

	return err
}

func (a *AliEcsClient) UnassociateEipAddress(ctx context.Context, eipID, instanceID, instanceType string) error {
	request := &vpc20160428.UnassociateEipAddressRequest{}
	request.SetAllocationId(eipID)
	request.SetInstanceId(instanceID)
	request.SetInstanceType(instanceType)

	resp := &vpc20160428.UnassociateEipAddressRequest{}
	err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "UnassociateEipAddress", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return err
	}

	return err
}

func (a *AliEcsClient) JoinResourceGroup(ctx context.Context, instanceID, resourceGroupID string) error {
	request := &ecs20140526.JoinResourceGroupRequest{}
	request.SetRegionId(a.regionID)
	request.SetResourceGroupId(resourceGroupID)
	request.SetResourceId(instanceID)
	request.SetResourceType("instance")

	resp := &ecs20140526.JoinResourceGroupResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "JoinResourceGroup", a.accountID, []interface{}{request}, resp)
	if err != nil {
		return err
	}

	return err
}

// JoinSecurityGroup 加入安全组
func (a *AliEcsClient) JoinSecurityGroup(ctx context.Context, instanceId string, securityGroupId string) (*ecs20140526.JoinSecurityGroupResponseBody, error) {
	res := &ecs20140526.JoinSecurityGroupResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "JoinSecurityGroup", a.accountID, []interface{}{&ecs20140526.JoinSecurityGroupRequest{
		RegionId:        tea.String(a.regionID),
		InstanceId:      tea.String(instanceId),
		SecurityGroupId: tea.String(securityGroupId),
	}}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// LeaveSecurityGroup 解绑安全组
func (a *AliEcsClient) LeaveSecurityGroup(ctx context.Context, instanceId string, securityGroupId string) (*ecs20140526.LeaveSecurityGroupResponseBody, error) {
	res := &ecs20140526.LeaveSecurityGroupResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "LeaveSecurityGroup", a.accountID, []interface{}{&ecs20140526.LeaveSecurityGroupRequest{
		RegionId:        tea.String(a.regionID),
		InstanceId:      tea.String(instanceId),
		SecurityGroupId: tea.String(securityGroupId),
	}}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// ModifyDiskName 修改磁盘名称
func (a *AliEcsClient) ModifyDiskName(ctx context.Context, diskId string, newName string) error {
	res := &ecs20140526.ModifyDiskAttributeResponse{}
	return agentsdk.SyncCall(ctx, a.regionID, "host", "ModifyDiskAttribute", a.accountID, []interface{}{&ecs20140526.ModifyDiskAttributeRequest{
		RegionId: tea.String(a.regionID),
		DiskId:   tea.String(diskId),
		DiskName: tea.String(newName),
	}}, res)
}

// AssignIpv6Addresses 分配一个ipv6地址
func (a *AliEcsClient) AssignIpv6Addresses(ctx context.Context, interfaceId string) (string, error) {
	req := &ecs20140526.AssignIpv6AddressesRequest{
		RegionId:           tea.String(a.regionID),
		Ipv6AddressCount:   tea.Int32(1),
		NetworkInterfaceId: tea.String(interfaceId),
	}
	res := &ecs20140526.AssignIpv6AddressesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "AssignIpv6Addresses", a.accountID, []interface{}{req}, res)
	if err != nil {
		return "", err
	}
	if len(res.Body.Ipv6Sets.Ipv6Address) == 0 {
		return "", fmt.Errorf("create ipv6 address failed")
	}
	return tea.StringValue(res.Body.Ipv6Sets.Ipv6Address[0]), nil
}

// UnAssignIpv6Address 回收一个ipv6地址
func (a *AliEcsClient) UnAssignIpv6Address(ctx context.Context, interfaceId string, address string) error {
	req := &ecs20140526.UnassignIpv6AddressesRequest{
		RegionId:           tea.String(a.regionID),
		Ipv6Address:        tea.StringSlice([]string{address}),
		NetworkInterfaceId: tea.String(interfaceId),
	}
	res := &ecs20140526.UnassignIpv6AddressesResponse{}
	return agentsdk.SyncCall(ctx, a.regionID, "host", "UnassignIpv6Addresses", a.accountID, []interface{}{req}, res)
}

// AllocateIpv6InternetBandwidth 分配公网带宽
func (a *AliEcsClient) AllocateIpv6InternetBandwidth(ctx context.Context, gatewayId string, addressId string, chargeType string, bandwidth int32) error {
	timeout := 5 * time.Minute
	req := &vpc20160428.AllocateIpv6InternetBandwidthRequest{
		RegionId:           tea.String(a.regionID),
		Ipv6GatewayId:      tea.String(gatewayId),
		Ipv6AddressId:      tea.String(addressId),
		InternetChargeType: tea.String(chargeType),
		Bandwidth:          tea.Int32(bandwidth),
	}
	err := common.Retry(timeout, func() *common.RetryError {
		res := &vpc20160428.AllocateIpv6InternetBandwidthResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "AllocateIpv6InternetBandwidth", a.accountID, []interface{}{req}, res)
		if err != nil {
			if IsThrottling(err) {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
	return err
}

// WaitIpv6AvailAndGetDetail 等待ipv6就绪以及查询详情
func (a *AliEcsClient) WaitIpv6AvailAndGetDetail(ctx context.Context, ipv6Address string) (*vpc20160428.DescribeIpv6AddressesResponseBody, error) {
	req := &vpc20160428.DescribeIpv6AddressesRequest{
		Ipv6Address: tea.String(ipv6Address),
		RegionId:    tea.String(a.regionID),
	}
	var body *vpc20160428.DescribeIpv6AddressesResponseBody
	timeout := 1 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		res := &vpc20160428.DescribeIpv6AddressesResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "DescribeIpv6Addresses", a.accountID, []interface{}{req}, res)
		if err != nil {
			if IsThrottling(err) {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		if len(res.Body.Ipv6Addresses.Ipv6Address) == 0 {
			return common.NonRetryableError(fmt.Errorf("cannot get ipaddress"))
		}
		if tea.StringValue(res.Body.Ipv6Addresses.Ipv6Address[0].Status) != "Available" {
			return common.RetryableError(fmt.Errorf("ipv6 address is not ready"))
		}
		body = res.Body
		return nil
	})
	if err != nil {
		return nil, err
	}
	return body, nil
}

// GetAliyunBandwidthPackageList 获取带宽包列表
func (a *AliEcsClient) GetAliyunBandwidthPackageList(ctx context.Context) ([]*vpc20160428.DescribeCommonBandwidthPackagesResponseBodyCommonBandwidthPackagesCommonBandwidthPackage, error) {
	req := &vpc20160428.DescribeCommonBandwidthPackagesRequest{}
	req.SetPageSize(10)
	req.SetRegionId(a.regionID)
	totalCount := 0
	maxloop := 20
	resList := make([]*vpc20160428.DescribeCommonBandwidthPackagesResponseBodyCommonBandwidthPackagesCommonBandwidthPackage, 0)
	for i := 0; i < maxloop; i++ {
		req.SetPageNumber(int32(i + 1))
		res := &vpc20160428.DescribeCommonBandwidthPackagesResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "DescribeCommonBandwidthPackages", a.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		totalCount = int(*res.Body.TotalCount)
		resList = append(resList, res.Body.CommonBandwidthPackages.CommonBandwidthPackage...)
		if totalCount <= len(resList) {
			break
		}
	}
	return resList, nil
}

// AddCommonBandwidthPackageIp 添加EIP到共享带宽中
func (a *AliEcsClient) AddCommonBandwidthPackageIp(ctx context.Context, eipId string, packageId string) error {

	req := &vpc20160428.AddCommonBandwidthPackageIpRequest{
		RegionId:           tea.String(a.regionID),
		BandwidthPackageId: tea.String(packageId),
		IpInstanceId:       tea.String(eipId),
	}
	res := &vpc20160428.AddCommonBandwidthPackageIpResponse{}
	timeout := 5 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "AddCommonBandwidthPackageIp", a.accountID, []interface{}{req}, res)
		if err != nil {
			if strings.Contains(err.Error(), "Code: Throttling.User") {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
	return err
}

// WaitEipAvailable 等待EIP状态为available
func (a *AliEcsClient) WaitEipAvailable(ctx context.Context, eipId string) error {
	req := &vpc20160428.DescribeEipAddressesRequest{
		RegionId:     tea.String(a.regionID),
		AllocationId: tea.String(eipId),
	}

	timeout := 1 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		res := &vpc20160428.DescribeEipAddressesResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "DescribeEipAddresses", a.accountID, []interface{}{req}, res)
		if err != nil {
			if IsThrottling(err) {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		if len(res.Body.EipAddresses.EipAddress) == 0 {
			return common.NonRetryableError(fmt.Errorf("cannot get ipaddress"))
		}
		if tea.StringValue(res.Body.EipAddresses.EipAddress[0].Status) != "Available" {
			return common.RetryableError(fmt.Errorf("eip is not ready"))
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveCommonBandwidthPackageIp 移除共享带宽实例中的EIP
func (a *AliEcsClient) RemoveCommonBandwidthPackageIp(ctx context.Context, eipId string, packageId string) error {
	req := &vpc20160428.RemoveCommonBandwidthPackageIpRequest{
		RegionId:           tea.String(a.regionID),
		BandwidthPackageId: tea.String(packageId),
		IpInstanceId:       tea.String(eipId),
	}
	res := &vpc20160428.RemoveCommonBandwidthPackageIpResponse{}
	timeout := 5 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		err := agentsdk.SyncCall(ctx, a.regionID, "vpc", "RemoveCommonBandwidthPackageIp", a.accountID, []interface{}{req}, res)
		if err != nil {
			if strings.Contains(err.Error(), "Code: Throttling.User") {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
	return err
}
