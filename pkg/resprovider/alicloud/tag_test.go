package alicloud

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"
	"unicode"

	alb******** "github.com/alibabacloud-go/alb-********/v2/client"
	ecs******** "github.com/alibabacloud-go/ecs-********/v2/client"
	polardb******** "github.com/alibabacloud-go/polardb-********/v6/client"
	r_kvstore******** "github.com/alibabacloud-go/r-kvstore-********/v7/client"
	"github.com/alibabacloud-go/tea/tea"
)

func tagECS(t *testing.T, regionID, ispID string) {
	client := AliEcsClient{
		regionID:  regionID,
		accountID: ispID,
	}
	ctx := context.Background()
	nextToken := ""
	for {
		response, err := client.DescribeAllInstances(ctx, 100, nextToken)
		if err != nil {
			t.Fatal(err)
		}

		for _, instance := range response.Instances.Instance {
			instance := instance
			instanceName := tea.StringValue(instance.InstanceName)
			instanceNameSeg := strings.Split(instanceName, "-")
			if len(instanceNameSeg) != 5 || instanceNameSeg[0] != "nap" {
				fmt.Printf("skip instance: %s\n", instanceName)
				continue
			}
			area := instanceNameSeg[3]
			env := instanceNameSeg[1]
			region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])

			moduleNum := instanceNameSeg[4]
			module := strings.TrimRightFunc(moduleNum, func(r rune) bool {
				return unicode.IsNumber(r)
			})
			num := strings.TrimLeft(moduleNum, module)
			if module == "" {
				module = "all"
			}
			tags := []*ecs********.TagResourcesRequestTag{
				{
					Key:   tea.String("nap"),
					Value: tea.String(area),
				},
				{
					Key:   tea.String("env"),
					Value: tea.String(env),
				},
				{
					Key:   tea.String("region"),
					Value: tea.String(region),
				},
				{
					Key:   tea.String("module"),
					Value: tea.String(module),
				},
				{
					Key:   tea.String("serial_number"),
					Value: tea.String(num),
				},
				{
					Key:   tea.String("region_module"),
					Value: tea.String(fmt.Sprintf("%s_%s", region, module)),
				},
			}
			// go func() {
			// fmt.Printf("tag ecs(%s), tag: %# v\n", instanceName, pretty.Formatter(tags))
			err := client.SetECSResourceTag(ctx, *instance.InstanceId, "instance", tags)
			if err != nil {
				fmt.Printf("tag instance: %s failed: err: %v\n", instanceName, err)
			}
			fmt.Printf("tag instance: %s success\n", instanceName)
			// }()
			time.Sleep(50 * time.Millisecond)
		}

		if response.NextToken == nil || tea.StringValue(response.NextToken) == "" {
			break
		}
		nextToken = *response.NextToken
	}
}

func tagAliyunECS(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1", "cn-shanghai"}
	ispID := "65572aed9bb2a1793df282a6"
	for _, regionID := range regionIDList {
		tagECS(t, regionID, ispID)
	}
}

func tagAlicloudECS(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65a0ae9ebf0b894146fc4983"
	for _, regionID := range regionIDList {
		tagECS(t, regionID, ispID)
	}

}

func TestTagAllECS(t *testing.T) {

	tagAliyunECS(t)
	tagAlicloudECS(t)
}

func tagPolarDB(t *testing.T, regionID, ispID string) {
	client := CreateAliPolarDBClient(regionID, ispID)
	ctx := context.Background()
	currentNum := 0
	totalCount := 0
	for i := 1; i <= 100; i++ {
		resp, err := client.DescribeDBClusters(ctx, int32(i), 50)
		if totalCount == 0 {
			totalCount = int(*resp.TotalRecordCount)
		}
		currentNum += int(*resp.PageRecordCount)
		if err != nil {
			t.Fatal(err)
		}

		for _, cluster := range resp.Items.DBCluster {
			instanceName := tea.StringValue(cluster.DBClusterDescription)
			instanceNameSeg := strings.Split(instanceName, "-")
			if len(instanceNameSeg) != 5 || instanceNameSeg[0] != "nap" {
				fmt.Printf("%s\n", instanceName)
				continue
			}
			area := instanceNameSeg[3]
			env := instanceNameSeg[1]
			region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])

			moduleNum := instanceNameSeg[4]
			module := strings.TrimRightFunc(moduleNum, func(r rune) bool {
				return unicode.IsNumber(r)
			})
			num := strings.TrimLeft(moduleNum, module)
			if module == "" {
				module = "all"
			}
			tags := []*polardb********.TagResourcesRequestTag{
				{
					Key:   tea.String("nap"),
					Value: tea.String(area),
				},
				{
					Key:   tea.String("env"),
					Value: tea.String(env),
				},
				{
					Key:   tea.String("region"),
					Value: tea.String(region),
				},
				{
					Key:   tea.String("module"),
					Value: tea.String(module),
				},
				{
					Key:   tea.String("serial_number"),
					Value: tea.String(num),
				},
				{
					Key:   tea.String("region_module"),
					Value: tea.String(fmt.Sprintf("%s_%s", region, module)),
				},
			}
			err := client.TagDBClusters(ctx, []string{*cluster.DBClusterId}, tags)
			if err != nil {
				fmt.Printf("tag instance: %s failed: err: %v\n", instanceName, err)
				continue
			}
			fmt.Printf("tag instance: %s success\n", instanceName)
		}
		if currentNum >= totalCount {
			break
		}
	}
}

func tagAliyunPolarDB(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65572aed9bb2a1793df282a6"
	for _, regionID := range regionIDList {
		tagPolarDB(t, regionID, ispID)
	}
}

func tagAlicloudPolarDB(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65a0ae9ebf0b894146fc4983"
	for _, regionID := range regionIDList {
		tagPolarDB(t, regionID, ispID)
	}

}

func TestTagPolarDB(t *testing.T) {

	tagAliyunPolarDB(t)
	tagAlicloudPolarDB(t)
}

func tagRedis(t *testing.T, regionID, ispID string) {
	currentNum := 0
	totalCount := 0
	client := AliKvStoreClient{
		regionID:  regionID,
		accountID: ispID,
	}
	ctx := context.Background()
	for i := 1; i <= 100; i++ {
		resp, err := client.DescribeInstances(ctx, int32(i), 100)
		if err != nil {
			t.Fatal(err)
		}
		if totalCount == 0 {
			totalCount = int(*resp.TotalCount)
		}

		for _, instance := range resp.Instances.KVStoreInstance {
			instanceName := tea.StringValue(instance.InstanceName)
			instanceNameSeg := strings.Split(instanceName, "-")
			if len(instanceNameSeg) != 5 || instanceNameSeg[0] != "nap" {
				fmt.Printf("skip instance: %s\n", instanceName)
				continue
			}
			area := instanceNameSeg[3]
			env := instanceNameSeg[1]
			region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])

			moduleNum := instanceNameSeg[4]
			module := strings.TrimRightFunc(moduleNum, func(r rune) bool {
				return unicode.IsNumber(r)
			})
			num := strings.TrimLeft(moduleNum, module)
			if module == "" {
				module = "all"
			}
			tags := []*r_kvstore********.TagResourcesRequestTag{
				{
					Key:   tea.String("nap"),
					Value: tea.String(area),
				},
				{
					Key:   tea.String("env"),
					Value: tea.String(env),
				},
				{
					Key:   tea.String("region"),
					Value: tea.String(region),
				},
				{
					Key:   tea.String("module"),
					Value: tea.String(module),
				},
				{
					Key:   tea.String("serial_number"),
					Value: tea.String(num),
				},
				{
					Key:   tea.String("region_module"),
					Value: tea.String(fmt.Sprintf("%s_%s", region, module)),
				},
			}
			err := client.TagRedis(ctx, []string{*instance.InstanceId}, tags)
			if err != nil {
				fmt.Printf("tag instance: %s failed: err: %v\n", instanceName, err)
				continue
			}
			fmt.Printf("tag instance: %s success\n", instanceName)
		}
		currentNum += int(*resp.PageSize)
		if currentNum >= totalCount {
			break
		}
	}
}

func tagAliyunRedis(t *testing.T) {
	regionIDList := []string{"cn-shanghai", "ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65f2641304cbd9f8a5427ed0"
	for _, region := range regionIDList {
		fmt.Printf("start %s tag", region)
		tagRedis(t, region, ispID)
	}
}

func tagAlicloudRedis(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65a0ae9ebf0b894146fc4983"
	for _, region := range regionIDList {
		fmt.Printf("start %s tag", region)
		tagRedis(t, region, ispID)
	}
}

func TestTagRedis(t *testing.T) {

	tagAliyunRedis(t)
	tagAlicloudRedis(t)
}

func tagLB(t *testing.T, regionID, ispID string) {
	client := AliLoadBalancerClient{
		regionID:  regionID,
		accountID: ispID,
	}
	ctx := context.Background()
	lbs, err := client.ListAllALBs(ctx)
	if err != nil {
		t.Fatal(err)
	}
	for _, instance := range lbs {
		instanceName := tea.StringValue(instance.LoadBalancerName)
		instanceNameSeg := strings.Split(instanceName, "-")
		if len(instanceNameSeg) != 6 || instanceNameSeg[0] != "lb" {
			fmt.Printf("skip instance: %s\n", instanceName)
			continue
		}
		area := instanceNameSeg[3]
		env := instanceNameSeg[1]
		region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])

		moduleNum := instanceNameSeg[4]
		module := strings.TrimRightFunc(moduleNum, func(r rune) bool {
			return unicode.IsNumber(r)
		})
		num := strings.TrimLeft(moduleNum, module)
		if module == "" {
			module = "all"
		}
		tags := []*alb********.TagResourcesRequestTag{
			{
				Key:   tea.String("nap"),
				Value: tea.String(area),
			},
			{
				Key:   tea.String("env"),
				Value: tea.String(env),
			},
			{
				Key:   tea.String("region"),
				Value: tea.String(region),
			},
			{
				Key:   tea.String("module"),
				Value: tea.String(module),
			},
			{
				Key:   tea.String("serial_number"),
				Value: tea.String(num),
			},
			{
				Key:   tea.String("region_module"),
				Value: tea.String(fmt.Sprintf("%s_%s", region, module)),
			},
		}
		err := client.TagLB(ctx, []string{*instance.LoadBalancerId}, tags)
		if err != nil {
			fmt.Printf("tag instance: %s failed: err: %v\n", instanceName, err)
			continue
		}
		fmt.Printf("tag instance: %s success\n", instanceName)
	}
}

func tagAliyunLB(t *testing.T) {
	regionIDList := []string{"cn-shanghai", "ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "6540c7f36591041c72706a75"
	for _, region := range regionIDList {
		fmt.Printf("start %s tag", region)
		tagLB(t, region, ispID)
	}
}

func tagAlicloudLB(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "665878fc850cd969b864907d"
	for _, region := range regionIDList {
		fmt.Printf("start %s tag", region)
		tagLB(t, region, ispID)
	}
}

func TestTagLB(t *testing.T) {

	fmt.Printf("start tag aliyun\n")
	tagAliyunLB(t)
	fmt.Printf("start tag alicloud\n")
	tagAlicloudLB(t)
}
