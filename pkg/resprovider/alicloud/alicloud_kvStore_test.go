package alicloud

import (
	"context"
	"testing"
)

func TestCreateAccount(t *testing.T) {

	client, err := CreateAliKvStoreClient("cn-shanghai", "6540c7f36591041c72706a75", false, nil)
	if err != nil {
		panic(err)
	}
	ctx := context.Background()
	client.CreateAccount(ctx, "r-uf69ac496588fda4", "readonly", "RoleReadWrite", "Test1234@")
}

func TestCreateBackUp(t *testing.T) {
	client, err := CreateAliKvStoreClient("ap-southeast-1", "665878fc850cd969b864907d", false, nil)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	ctx := context.Background()
	res, err := client.CreateBackup(ctx, "r-gs5t0mz4uziucczw1h", true)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log(res)
}
