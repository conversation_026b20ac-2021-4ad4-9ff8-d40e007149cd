package alicloud

import (
	ram******** "github.com/alibabacloud-go/ram-********/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
)

// AliRamClient 阿里云ram-client
type AliRamClient struct {
	regionID  string
	accountID string
}

// NewAliRamClient ecs.NewClientWithAccessKey
func NewAliRamClient(regionID, accountID string) (*AliRamClient, error) {
	return &AliRamClient{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

// ListPolicies 列出所选的策略
func (c *AliRamClient) ListPolicies(ctx context.Context, policyType string, policyName ...string) ([]*ram********.ListPoliciesResponseBodyPoliciesPolicy, error) {
	req := &ram********.ListPoliciesRequest{}
	req.SetPolicyType(policyType)
	maxLoop := 30
	result := make([]*ram********.ListPoliciesResponseBodyPoliciesPolicy, 0)
	for i := 0; i <= maxLoop; i++ {
		res := &ram********.ListPoliciesResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "ram", "ListPolicies", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		result = append(result, res.Body.Policies.Policy...)
		if !tea.BoolValue(res.Body.IsTruncated) {
			break
		}
		req.Marker = res.Body.Marker
	}

	if len(policyName) == 0 {
		return result, nil
	}

	nameMap := make(map[string]interface{})
	for _, n := range policyName {
		nameMap[n] = true
	}

	policies := make([]*ram********.ListPoliciesResponseBodyPoliciesPolicy, 0)
	for _, p := range result {
		if _, ok := nameMap[*p.PolicyName]; ok {
			policies = append(policies, p)
		}
	}

	return policies, nil
}

// GetPolicy 获取权限策略详情
func (c *AliRamClient) GetPolicy(ctx context.Context, policyType, policyName string) (*ram********.GetPolicyResponseBody, error) {
	req := &ram********.GetPolicyRequest{}
	req.SetPolicyType(policyType)
	req.SetPolicyName(policyName)
	res := &ram********.GetPolicyResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "ram", "GetPolicy", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// ListEntitiesForPolicy 列出引用权限策略的实体
func (c *AliRamClient) ListEntitiesForPolicy(ctx context.Context, policyType, policyName string) (*ram********.ListEntitiesForPolicyResponseBody, error) {
	req := &ram********.ListEntitiesForPolicyRequest{}
	req.SetPolicyType(policyType)
	req.SetPolicyName(policyName)
	res := &ram********.ListEntitiesForPolicyResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "ram", "ListEntitiesForPolicy", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

func (c *AliRamClient) CreatePolicyVersion(ctx context.Context, policyName, policyDocument string, setAsDefault bool) error {
	req := &ram********.CreatePolicyVersionRequest{}
	req.SetPolicyName(policyName)
	req.SetPolicyDocument(policyDocument)
	req.SetSetAsDefault(setAsDefault)
	req.SetRotateStrategy("DeleteOldestNonDefaultVersionWhenLimitExceeded")
	res := &ram********.CreatePolicyVersionResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "ram", "CreatePolicyVersion", c.accountID, []interface{}{req}, res)
}
