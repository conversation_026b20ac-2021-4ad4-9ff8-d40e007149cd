package alicloud

import (
	"fmt"
	"testing"

	alb20200616 "github.com/alibabacloud-go/alb-20200616/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/context"
)

func TestDeleteLB(t *testing.T) {
	ctx := context.Background()

	client, err := NewAliLoadBalancerClient("cn-shanghai", "65572aed9bb2a1793df282a6")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(client.DeleteLoadBalancer(ctx, "alb-itqv72pkazm4apo8og"))
}

func TestDeleteServerGroup(t *testing.T) {
	ctx := context.Background()

	client, err := NewAliLoadBalancerClient("cn-shanghai", "65572aed9bb2a1793df282a6")
	if err != nil {
		t.<PERSON>al(err)
	}
	fmt.Println(client.DeleteServerGroup(ctx, "sgp-0undofl3mv0pfqunka"))
}

func TestDeleteListener(t *testing.T) {
	ctx := context.Background()

	client, err := NewAliLoadBalancerClient("cn-shanghai", "65572aed9bb2a1793df282a6")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(client.DeleteListener(ctx, "lsn-504b79e4r89hj9b83s"))
}

func TestCreateListener(t *testing.T) {
	ctx := context.Background()

	client, err := NewAliLoadBalancerClient("cn-shanghai", "65572aed9bb2a1793df282a6")
	if err != nil {
		t.Fatal(err)
	}

	loadBalanceID := "alb-yd72fzdth74zwqiox0"
	// fmt.Println(client.CreateListenerAndWaitReady(ctx, &alb20200616.CreateListenerRequest{
	// 	DefaultActions: []*alb20200616.CreateListenerRequestDefaultActions{
	// 		{
	// 			ForwardGroupConfig: &alb20200616.CreateListenerRequestDefaultActionsForwardGroupConfig{
	// 				ServerGroupTuples: []*alb20200616.CreateListenerRequestDefaultActionsForwardGroupConfigServerGroupTuples{
	// 					{
	// 						ServerGroupId: tea.String("sgp-hk6c80jwgp1vb4wukn"),
	// 					},
	// 				},
	// 			},
	// 			Type: tea.String("ForwardGroup"),
	// 		},
	// 	},
	// 	GzipEnabled:         tea.Bool(true),
	// 	ListenerDescription: tea.String("由云管平台创建"),
	// 	ListenerPort:        tea.Int32(80),
	// 	ListenerProtocol:    tea.String("HTTP"),
	// 	LoadBalancerId:      tea.String(loadBalanceID),
	// 	XForwardedForConfig: &alb20200616.CreateListenerRequestXForwardedForConfig{
	// 		XForwardedForEnabled: tea.Bool(true),
	// 	},
	// }, loadBalanceID))
	serverGroupId := "sgp-hk6c80jwgp1vb4wukn"
	certificateId := "8937137-cn-hangzhou"
	fmt.Println(client.CreateListenerAndWaitReady(ctx, &alb20200616.CreateListenerRequest{
		DefaultActions: []*alb20200616.CreateListenerRequestDefaultActions{
			{
				ForwardGroupConfig: &alb20200616.CreateListenerRequestDefaultActionsForwardGroupConfig{
					ServerGroupTuples: []*alb20200616.CreateListenerRequestDefaultActionsForwardGroupConfigServerGroupTuples{
						{
							ServerGroupId: tea.String(serverGroupId),
						},
					},
				},
				Type: tea.String("ForwardGroup"),
			},
		},
		GzipEnabled:         tea.Bool(true),
		ListenerDescription: tea.String("由云管平台创建"),
		ListenerPort:        tea.Int32(443),
		ListenerProtocol:    tea.String("HTTPS"),
		LoadBalancerId:      tea.String(loadBalanceID),
		XForwardedForConfig: &alb20200616.CreateListenerRequestXForwardedForConfig{
			XForwardedForEnabled: tea.Bool(true),
		},
		Certificates: []*alb20200616.CreateListenerRequestCertificates{{
			CertificateId: tea.String(certificateId),
		}},
	}, loadBalanceID))
}

func TestBindACL(t *testing.T) {
	ctx := context.Background()

	client, err := NewAliLoadBalancerClient("cn-shanghai", "65572aed9bb2a1793df282a6")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(client.BindListenerACLAndWaitforReady(ctx, "lsn-ef5tytrgjbv69yf9bd", []string{"acl-7ag6zob8dz2zek4eiv", "acl-dd4ynfv7gd1ewwye78"}, "White"))
}
