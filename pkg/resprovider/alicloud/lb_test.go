package alicloud

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestListLBCertificates(t *testing.T) {

	client := AliLoadBalancerClient{
		regionID:  "ap-northeast-1",
		accountID: "65a0ae9ebf0b894146fc4983",
	}
	res, err := client.ListCertificates(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	fmt.Printf("%+v", res)
}

func TestLBListenerDesc(t *testing.T) {
	assert.Equal(t, "HTTP_oaserver", getListenerDesc("HTTP", "lb-sf-ops-oa01-inner"))
	assert.Equal(t, "HTTPS_oaserver", getListenerDesc("HTTPS", "lb-sf-ops-test-oa01-inner"))
}

func TestGetSgName(t *testing.T) {
	sgName, _ := getSgName("lb-sf-ops-cn-oa01-inner")
	assert.Equal(t, "tg-sf-ops-cn-oa_31001", sgName)
	sgName, _ = getSgName("lb-sf-ops-cn-oa01-inner")
	assert.Equal(t, "tg-sf-ops-cn-oa_31001", sgName)
}

func TestListALBServerGroupsServersThrot(t *testing.T) {

	client := AliLoadBalancerClient{
		regionID:  "ap-northeast-1",
		accountID: "65a0ae9ebf0b894146fc4983",
	}
	var wg sync.WaitGroup
	for i := 0; i < 40; i++ {
		go func() {
			defer func() {
				wg.Done()
			}()
			wg.Add(1)
			_, err := client.ListALBServerGroupsServers(context.Background(), "sgp-v5ln4rzae1v30hucjb")
			if err != nil {
				fmt.Printf("err: %v", err)
			}
		}()
	}
	wg.Wait()
	time.Sleep(1 * time.Second)
	fmt.Println("test finished")
}

func TestJoinSecurityGroup(t *testing.T) {

	client := AliLoadBalancerClient{
		regionID:  "ap-northeast-1",
		accountID: "65a0ae9ebf0b894146fc4983",
	}
	lbID := "alb-xw16il4v72lcnyp5vm"
	securityGroupIDs := []string{"sg-6we88shhnf56a0bjz1i0"}
	fmt.Println(client.JoinSecurityGroup(context.Background(), lbID, securityGroupIDs))
}

func TestLeaveSecurityGroup(t *testing.T) {

	client := AliLoadBalancerClient{
		regionID:  "ap-northeast-1",
		accountID: "65a0ae9ebf0b894146fc4983",
	}
	lbID := "alb-xw16il4v72lcnyp5vm"
	securityGroupIDs := []string{"sg-6we88shhnf56a0bjz1i0"}
	fmt.Println(client.LeaveSecurityGroup(context.Background(), lbID, securityGroupIDs))
}

func TestWaitForReady(t *testing.T) {

	client := AliLoadBalancerClient{
		regionID:  "ap-northeast-1",
		accountID: "65a0ae9ebf0b894146fc4983",
	}
	lbID := "alb-xw16il4v72lcnyp5vm"
	fmt.Println(client.WaitForReady(context.Background(), lbID, 2*time.Minute))
}

func TestCreateOrGetServerGroup(t *testing.T) {

	client := AliLoadBalancerClient{
		regionID:  "ap-northeast-1",
		accountID: "65a0ae9ebf0b894146fc4983",
	}
	fmt.Println(client.CreateOrGetDefaultServerGroup(context.Background(), "tg-prod-devops-jp-jobman02", "vpc-6we4gqv3vvqku1ou3u7x3"))
}
