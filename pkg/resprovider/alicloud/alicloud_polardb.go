package alicloud

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dbs******** "github.com/alibabacloud-go/dbs-********/v3/client"
	openapiutil "github.com/alibabacloud-go/openapi-util/service"
	polardb******** "github.com/alibabacloud-go/polardb-********/v6/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// AliPolarDBClient 阿里云PolarDB-client
type AliPolarDBClient struct {
	regionID string

	accountID string
}

// CreateAliPolarDBClient create ali client
func CreateAliPolarDBClient(regionID string, accountID string) (client *AliPolarDBClient) {
	// 访问的域名
	// config.Endpoint = tea.String(endpoint)
	return &AliPolarDBClient{
		regionID:  regionID,
		accountID: accountID,
	}
}

// DescribeDBClusters 获取数据库集群列表
func (c AliPolarDBClient) DescribeDBClusters(ctx context.Context, page, size int32, clusterIds ...string) (*polardb********.DescribeDBClustersResponseBody, error) {
	request := &polardb********.DescribeDBClustersRequest{
		RegionId:     tea.String(c.regionID),
		PageSize:     tea.Int32(size),
		PageNumber:   tea.Int32(page),
		DBClusterIds: tea.String(strings.Join(clusterIds, ",")),
	}
	var body *polardb********.DescribeDBClustersResponseBody
	err := retry(func() error {
		res := &polardb********.DescribeDBClustersResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeDBClusters", c.accountID, []interface{}{request}, res)
		if err != nil {
			return err
		}
		body = res.Body
		return nil
	})

	return body, err
}

// DescribeDBClusterAccessWhitelist 获取集群白名单
func (c AliPolarDBClient) DescribeDBClusterAccessWhitelist(ctx context.Context, clusterID string) (*polardb********.DescribeDBClusterAccessWhitelistResponseBody, error) {
	request := &polardb********.DescribeDBClusterAccessWhitelistRequest{
		DBClusterId: tea.String(clusterID),
	}

	var body *polardb********.DescribeDBClusterAccessWhitelistResponseBody
	err := retry(func() error {
		res := &polardb********.DescribeDBClusterAccessWhitelistResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeDBClusterAccessWhitelist", c.accountID, []interface{}{request}, res)
		if err != nil {
			return err
		}
		body = res.Body
		return nil
	})

	return body, err
}

// DescribeDBClusterAccessWhitelist 获取集群绑定的全局白名单
func (c AliPolarDBClient) DescribeGlobalSecurityIPGroupRelation(ctx context.Context, clusterID string) (*polardb********.DescribeGlobalSecurityIPGroupRelationResponseBody, error) {
	request := &polardb********.DescribeGlobalSecurityIPGroupRelationRequest{
		DBClusterId: tea.String(clusterID),
		RegionId:    tea.String(c.regionID),
	}

	var body *polardb********.DescribeGlobalSecurityIPGroupRelationResponseBody
	err := retry(func() error {
		res := &polardb********.DescribeGlobalSecurityIPGroupRelationResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeGlobalSecurityIPGroupRelation", c.accountID, []interface{}{request}, res)
		if err != nil {
			return err
		}
		body = res.Body
		return nil
	})

	return body, err
}

// DescribeDatabase 获取数据库列表
func (c AliPolarDBClient) DescribeDatabase(ctx context.Context, page, size int32, clusterID string) (*polardb********.DescribeDatabasesResponseBody, error) {
	request := &polardb********.DescribeDatabasesRequest{
		DBClusterId: tea.String(clusterID),
		PageNumber:  tea.Int32(page),
		PageSize:    tea.Int32(size),
	}

	var body *polardb********.DescribeDatabasesResponseBody
	err := retry(func() error {
		res := &polardb********.DescribeDatabasesResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeDatabases", c.accountID, []interface{}{request}, res)
		if err != nil {
			return err
		}
		body = res.Body
		return nil
	})

	return body, err
}

// DescribeAccount 获取数据库账户信息
func (c AliPolarDBClient) DescribeAccount(ctx context.Context, page, size int32, clusterID string) (*polardb********.DescribeAccountsResponseBody, error) {
	request := &polardb********.DescribeAccountsRequest{
		DBClusterId: tea.String(clusterID),
		AccountName: nil,
		PageNumber:  tea.Int32(page),
		PageSize:    tea.Int32(size),
	}

	var body *polardb********.DescribeAccountsResponseBody
	err := retry(func() error {
		res := &polardb********.DescribeAccountsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeAccounts", c.accountID, []interface{}{request}, res)
		if err != nil {
			return err
		}
		body = res.Body
		return nil
	})

	return body, err
}

// DescribeDBClusterEndpoints 查询PolarDB集群的地址信息
func (c AliPolarDBClient) DescribeDBClusterEndpoints(ctx context.Context, clusterID string) (*polardb********.DescribeDBClusterEndpointsResponseBody, error) {
	request := &polardb********.DescribeDBClusterEndpointsRequest{
		DBClusterId: tea.String(clusterID),
	}

	var body *polardb********.DescribeDBClusterEndpointsResponseBody
	err := retry(func() error {
		res := &polardb********.DescribeDBClusterEndpointsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeDBClusterEndpoints", c.accountID, []interface{}{request}, res)
		if err != nil {
			return err
		}
		body = res.Body
		return nil
	})

	return body, err
}

// CreateBackup 通过阿里云对Porlardb数据库进行备份
func (c AliPolarDBClient) CreateBackup(ctx context.Context, DBInstanceID string) (*polardb********.CreateBackupResponseBody, error) {
	request := &polardb********.CreateBackupRequest{
		DBClusterId: tea.String(DBInstanceID),
	}

	resp := &polardb********.CreateBackupResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "CreateBackup", c.accountID, []interface{}{request}, resp)
	if err != nil {
		return nil, err
	}

	return resp.Body, nil
}

// CreateBackup 通过阿里云对Polardb数据库进行备份，并会在指定时间后转储为二级备份
func (c AliPolarDBClient) CreateLevel2Backup(ctx context.Context, DBInstanceID string, level2BackupRetentionPeriod int) (*polardb********.CreateBackupResponseBody, error) {
	params := &openapi.Params{
		Action:      tea.String("CreateBackup"),
		Version:     tea.String("2017-08-01"),
		Protocol:    tea.String("HTTPS"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		Pathname:    tea.String("/"),
		ReqBodyType: tea.String("json"),
		BodyType:    tea.String("json"),
	}
	runtime := &util.RuntimeOptions{}
	queries := make(map[string]interface{})
	queries["DBClusterId"] = tea.String(DBInstanceID)
	queries["DataLevel2BackupRetentionPeriod"] = tea.Int(level2BackupRetentionPeriod)
	request := &openapi.OpenApiRequest{
		Query: openapiutil.Query(queries),
	}
	resp := make(map[string]interface{})
	err := agentsdk.SyncCall(ctx, c.regionID, "common_mysql", "CallApi", c.accountID, []interface{}{params, request, runtime}, &resp)
	if err != nil {
		return nil, err
	}
	// convert resp to CreateBackupResponseBody
	type CreateBackupResponseBody struct {
		BackupJobId *int    `json:"BackupJobId,omitempty" xml:"BackupJobId,omitempty"`
		RequestId   *string `json:"RequestId,omitempty" xml:"RequestId,omitempty"`
	}
	body := &CreateBackupResponseBody{}
	respBody, ok := resp["body"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid response body")
	}
	respBytes, err := json.Marshal(respBody)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(respBytes, body)
	if err != nil {
		return nil, err
	}
	return &polardb********.CreateBackupResponseBody{
		BackupJobId: tea.String(fmt.Sprintf("%d", *body.BackupJobId)),
		RequestId:   body.RequestId,
	}, nil
}

// DescribeBackupTasks 查询备份任务列表
func (c AliPolarDBClient) DescribeBackupTasks(ctx context.Context, timeout time.Duration, DBInstanceID string, jobID string) (*polardb********.DescribeBackupTasksResponseBody, error) {
	request := &polardb********.DescribeBackupTasksRequest{
		DBClusterId: tea.String(DBInstanceID),
		BackupJobId: tea.String(jobID),
	}
	wait := common.IncrementalWait(1*time.Second, 1*time.Second)
	var body *polardb********.DescribeBackupTasksResponseBody
	err := common.Retry(timeout, func() *common.RetryError {
		res := &polardb********.DescribeBackupTasksResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeBackupTasks", c.accountID, []interface{}{request}, res)
		if err != nil {
			// 遇到查询限流时重试
			if IsThrottling(err) {
				wait()
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		body = res.Body
		return nil
	})
	if err != nil {
		return nil, err
	}

	return body, nil
}

// RunInstance 创建polarDB实例,接口不支持批量创建,多台需要使用for循环完成
func (c AliPolarDBClient) RunInstance(ctx context.Context, timeout time.Duration, buyArg *polardb********.CreateDBClusterRequest) (*polardb********.CreateDBClusterResponseBody, error) {
	buyArg.RegionId = tea.String(c.regionID)
	wait := common.IncrementalWait(1*time.Second, 1*time.Second)
	var body *polardb********.CreateDBClusterResponseBody
	err := common.Retry(timeout, func() *common.RetryError {
		res := &polardb********.CreateDBClusterResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "CreateDBCluster", c.accountID, []interface{}{buyArg}, res)
		if err != nil {
			if IsThrottling(err) {
				wait()
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		body = res.Body
		return nil
	})

	return body, err
}

// CreateAccount 创建账号
func (c AliPolarDBClient) CreateAccount(ctx context.Context, input CreatePolarDBAccountInput) error {
	res := &polardb********.CreateAccountResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "CreateAccount", c.accountID, []interface{}{&input.CreateAccountRequest}, res)
	if err != nil {
		return err
	}

	return nil
}

// ModifyDBClusterAccessWhitelist 创建或修改集群的白名单（包括IP白名单和安全组）
func (c AliPolarDBClient) ModifyDBClusterAccessWhitelist(ctx context.Context, input ModifyDBClusterAccessWhitelistInput) error {
	res := &polardb********.ModifyDBClusterAccessWhitelistResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "ModifyDBClusterAccessWhitelist", c.accountID, []interface{}{&input.ModifyDBClusterAccessWhitelistRequest}, res)
	if err != nil {
		// 阿里云绑定安全组接口会出现503报错但实际上绑定成功的问题，可以先跳过该报错
		if strings.Contains(err.Error(), "code: 503, The request has failed due to a temporary failure of the server") {
			return nil
		}
		return err
	}

	return nil
}

// ModifyDBClusterAccessWhitelist 修改集群与全局IP白名单模板的关联关系
func (c AliPolarDBClient) ModifyGlobalSecurityIPGroupRelation(ctx context.Context, input ModifyGlobalSecurityIPGroupRelationInput) error {
	res := &polardb********.ModifyGlobalSecurityIPGroupRelationResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "ModifyGlobalSecurityIPGroupRelation", c.accountID, []interface{}{&input.ModifyGlobalSecurityIPGroupRelationRequest}, res)
	if err != nil {
		return err
	}

	return nil
}

// DeleteDBCluster 删除集群
func (c AliPolarDBClient) DeleteDBCluster(ctx context.Context, input polardb********.DeleteDBClusterRequest) error {
	res := &polardb********.DeleteDBClusterResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DeleteDBCluster", c.accountID, []interface{}{&input}, res)
	if err != nil {
		return err
	}

	return nil
}

// ModifyBackupPolicy 修改备份策略
func (c AliPolarDBClient) ModifyBackupPolicy(ctx context.Context, input ModifyBackupPolicyInput) error {
	res := &polardb********.ModifyBackupPolicyResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "ModifyBackupPolicy", c.accountID, []interface{}{&input}, res)
	if err != nil {
		return err
	}

	return nil
}

// CreateDBClusterEndpoint 创建集群endpoint
func (c AliPolarDBClient) CreateDBClusterEndpoint(ctx context.Context, input CreateDBClusterEndpointInput) error {
	res := &polardb********.CreateDBEndpointAddressResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "mysql", "CreateDBClusterEndpoint", c.accountID, []interface{}{&input}, res)
}

// ModifyDBEndpointAddress 修改endpoint地址，包含重试逻辑
func (c AliPolarDBClient) ModifyDBEndpointAddress(ctx context.Context, input ModifyDBEndpointAddressInput, timeout time.Duration) error {
	err := common.Retry(timeout, func() *common.RetryError {
		res := &polardb********.ModifyDBEndpointAddressResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "ModifyDBEndpointAddress", c.accountID, []interface{}{&input}, res)
		if err != nil {
			// 检查err中是否包含特定的报错信息
			// TODO: 这里检查比较hardcode，可以在底层换一种error的包装方式
			if strings.Contains(err.Error(), fmt.Sprintf("Code: %s", ClusterStatusOperationDenied)) ||
				strings.Contains(err.Error(), fmt.Sprintf("Code: %s", EndpointStatusNotSupport)) {
				return common.RetryableError(err)
			}
			// 如果已经包含该地址，则跳过
			if strings.Contains(err.Error(), fmt.Sprintf("Code: %s", DuplicateConnectionStrOrPort)) {
				return nil
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
	return err
}

// ModifyGlobalSecurityIPGroup 修改IP白名单模板
func (c AliPolarDBClient) ModifyGlobalSecurityIPGroup(ctx context.Context, name, id string, ips []string) error {
	req := &polardb********.ModifyGlobalSecurityIPGroupRequest{}
	req.SetRegionId(c.regionID)
	req.SetGlobalIgName(name)
	req.SetGlobalSecurityGroupId(id)
	req.SetGIpList(strings.Join(ips, ","))
	res := &polardb********.ModifyGlobalSecurityIPGroupResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "mysql", "ModifyGlobalSecurityIPGroup", c.accountID, []interface{}{&req}, res)
}

// TransformDBClusterToPostpaid 将集群转为按量付费
func (c AliPolarDBClient) TransformDBClusterToPostpaid(ctx context.Context, id string) error {
	req := &polardb********.TransformDBClusterPayTypeRequest{}
	req.SetDBClusterId(id)
	req.SetRegionId(c.regionID)
	req.SetPayType("Postpaid")
	res := &polardb********.TransformDBClusterPayTypeResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "mysql", "TransformDBClusterPayType", c.accountID, []interface{}{&req}, res)
}

// TagDBClusters ...
func (c AliPolarDBClient) TagDBClusters(ctx context.Context, clusterIDs []string, tag []*polardb********.TagResourcesRequestTag) error {
	req := &polardb********.TagResourcesRequest{}
	req.SetResourceId(tea.StringSlice(clusterIDs))
	req.SetRegionId(c.regionID)
	req.SetResourceType("cluster")
	req.SetTag(tag)
	res := &polardb********.TagResourcesResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "mysql", "TagResources", c.accountID, []interface{}{&req}, res)
}

// ModifyDBClusterMonitor 修改监控频率
// period只能为5或者60
func (c AliPolarDBClient) ModifyDBClusterMonitor(ctx context.Context, clusterId, period string) error {
	if period != "5" && period != "60" {
		return fmt.Errorf("wrong period value")
	}
	req := &polardb********.ModifyDBClusterMonitorRequest{}
	req.SetDBClusterId(clusterId)
	req.SetPeriod(period)
	res := &polardb********.ModifyDBClusterMonitorResponse{}

	retryTime := 5
	interval := 1 * time.Second
	var err error
	// 这个阿里云接口有时候会报错，重试5次，间隔1秒
	for i := 0; i < retryTime; i++ {
		err = agentsdk.SyncCall(ctx, c.regionID, "mysql", "ModifyDBClusterMonitor", c.accountID, []interface{}{&req}, res)
		if err == nil {
			return nil
		}
		time.Sleep(interval)
	}

	return err
}

func (c AliPolarDBClient) ModifyDBClusterResourceGroup(ctx context.Context, clusterId, resourceGroupId string) error {
	req := &polardb********.ModifyDBClusterResourceGroupRequest{}
	req.SetDBClusterId(clusterId)
	req.SetNewResourceGroupId(resourceGroupId)
	res := &polardb********.ModifyDBClusterResourceGroupResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "ModifyDBClusterResourceGroup", c.accountID, []interface{}{&req}, res)
	if err != nil {
		return err
	}

	return nil
}

func (c AliPolarDBClient) RestartNode(ctx context.Context, nodeID string) error {
	req := &polardb********.RestartDBNodeRequest{}
	req.SetDBNodeId(nodeID)
	res := &polardb********.RestartDBNodeResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "RestartDBNode", c.accountID, []interface{}{&req}, res)
	if err != nil {
		return err
	}
	return nil
}

func (c AliPolarDBClient) DescribeDBClusterAttribute(ctx context.Context, dbClusterID string) (*polardb********.DescribeDBClusterAttributeResponseBody, error) {
	req := &polardb********.DescribeDBClusterAttributeRequest{}
	req.SetDBClusterId(dbClusterID)
	res := &polardb********.DescribeDBClusterAttributeResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeDBClusterAttribute", c.accountID, []interface{}{&req}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

func (c AliPolarDBClient) DownloadToOSS(ctx context.Context, dbClusterID string, backupId string, bucketName string, pathPrefix string) (string, error) {
	req := &dbs********.CreateDownloadRequest{}
	req.SetRegionCode(c.regionID)
	req.SetInstanceName(dbClusterID)
	req.SetBakSetId(backupId)
	req.SetTargetType("OSS")
	req.SetBakSetType("full")
	req.SetFormatType("SQL")
	req.SetTargetBucket(bucketName)
	req.SetTargetPath(pathPrefix)
	// 默认OSS和polarDB的区域一致
	req.SetTargetOssRegion(c.regionID)
	res := &dbs********.CreateDownloadResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "dbs", "CreateDownload", c.accountID, []interface{}{&req}, res)
	if err != nil {
		return "", err
	}
	return tea.StringValue(res.Body.Data.TaskId), nil
}

func (c AliPolarDBClient) GetDownloadStatus(ctx context.Context, dbClusterID string, backupId string, taskID string) (string, error) {
	req := &dbs********.DescribeDownloadTaskRequest{}
	req.SetRegionCode(c.regionID)
	req.SetInstanceName(dbClusterID)
	req.SetBackupSetId(backupId)

	res := &dbs********.DescribeDownloadTaskResponse{}
	timeout := 1 * time.Minute
	err := common.Retry(timeout, func() *common.RetryError {
		err := agentsdk.SyncCall(ctx, c.regionID, "dbs", "DescribeDownloadTask", c.accountID, []interface{}{&req}, res)
		if err != nil {
			// 遇到查询限流或网络中断时重试
			if IsThrottling(err) || IsRequestEOF(err) {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
	if err != nil {
		return "", err
	}
	if tea.Int64Value(res.Body.Data.TotalElements) == 0 {
		return "", fmt.Errorf("cannot find the download task, %s", backupId)
	}

	for _, res := range res.Body.Data.Content.List {
		if tea.StringValue(res.TaskId) != taskID {
			continue
		}
		return tea.StringValue(res.DownloadStatus), nil
	}

	return "", fmt.Errorf("cannot find the download task, %s", backupId)
}

// GetLatestManualBackup 获取最新的手动备份集信息(两天内)
func (c AliPolarDBClient) GetLatestManualBackup(ctx context.Context, timeout time.Duration, instanceID string) (*polardb********.DescribeBackupsResponseBodyItemsBackup, error) {
	nowTime := time.Now()
	startTime := nowTime.Add(-24 * time.Hour * 2).UTC().Format("2006-01-02T15:04Z")
	endTime := nowTime.Add(30 * time.Minute).UTC().Format("2006-01-02T15:04Z")

	request := &polardb********.DescribeBackupsRequest{}
	request.SetDBClusterId(instanceID)
	request.SetBackupMode("Manual")
	request.SetBackupStatus("Success")
	request.SetStartTime(startTime)
	request.SetEndTime(endTime)
	wait := common.IncrementalWait(1*time.Second, 1*time.Second)
	var body *polardb********.DescribeBackupsResponseBody
	err := common.Retry(timeout, func() *common.RetryError {
		res := &polardb********.DescribeBackupsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "mysql", "DescribeBackups", c.accountID, []interface{}{request}, res)
		if err != nil {
			// 遇到查询限流时重试
			if IsThrottling(err) {
				wait()
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		body = res.Body
		return nil
	})
	if err != nil {
		return nil, err
	}

	if len(body.Items.Backup) == 0 {
		return nil, fmt.Errorf("cannot find the backup")
	}

	return body.Items.Backup[0], nil
}
