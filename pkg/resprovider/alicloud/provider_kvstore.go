package alicloud

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

type AliKvStore struct {
	client    *AliKvStoreClient
	logger    *logrus.Logger
	dryRun    bool
	accountID string
}

// CreateInstance 创建实例
func (a AliKvStore) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	a.client.dryRun = a.dryRun
	return a.client.CreateInstance(ctx, duration, orderID, data)
}

func (a AliKvStore) BackupInstance(ctx context.Context, timeout time.Duration, orderID string, form common.OperateForm) error {
	realInstances := form.Instances
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	waitCluster := make(map[string][]string)
	var errCluster []string
	instances, err := models.RedisResourceModel.GetByInstanceIDs(ctx, realInstances)
	if err != nil {
		return err
	}
	instanceNameMap := map[string]string{}
	for _, instance := range instances {
		instanceNameMap[instance.InstanceID] = instance.InstanceName
	}

	// 重试实例id处理，要确保实际重试的在原始列表中
	if len(form.RetryInstances) != 0 {
		oriInstanceMap := map[string]bool{}
		realRetryInstances := []string{}
		for _, i := range form.Instances {
			oriInstanceMap[i] = true
		}
		for _, r := range form.RetryInstances {
			if oriInstanceMap[r] {
				realRetryInstances = append(realRetryInstances, r)
			}
		}
		a.logger.Infof("检测到重试配置，本次重试的实例列表：%v", realRetryInstances)
		realInstances = realRetryInstances
	} else {
		a.logger.Infoln("开始备份实例:", form.Instances)
	}
	for _, clusterID := range realInstances {
		time.Sleep(300 * time.Millisecond)
		instanceName := instanceNameMap[clusterID]
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.instance_id", clusterID):   clusterID,
			common.ReportSprintf("%s.instance_name", clusterID): instanceName,
			common.ReportSprintf("%s.start_time", clusterID):    time.Now().Unix(),
			common.ReportSprintf("%s.status", clusterID):        "preparing",
			common.ReportSprintf("%s.is_err", clusterID):        false,
			common.ReportSprintf("%s.is_success", clusterID):    false,
		})
		isLongTerm := false
		// 只有官服实例进行长期存储
		if strings.HasPrefix(instanceName, "nap-prod-gf-") {
			isLongTerm = true
			a.logger.Infof("实例%s使用长存储", instanceName)
		}
		singleResp, err := a.client.CreateBackup(ctx, clusterID, isLongTerm)
		if err != nil {
			a.logger.Errorf("备份实例(%s)失败:%s", clusterID, err.Error())
			errCluster = append(errCluster, clusterID)
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.status", clusterID):    "start_failed",
				common.ReportSprintf("%s.last_time", clusterID): time.Now().Unix(),
				common.ReportSprintf("%s.is_err", clusterID):    true,
			})
			continue
		}
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", clusterID): time.Now().Unix(),
			common.ReportSprintf("%s.status", clusterID):    "started",
		})
		reportStore = common.ResetOrderStatusDetail(ctx, orderID, false, reportStore, common.ReportSprintf("%s.jobs", clusterID))
		jobID := strings.Split(tea.StringValue(singleResp.BackupJobID), ",")

		if len(jobID) > 0 {
			for i := 0; i < len(jobID); i++ {
				waitCluster[clusterID] = append(waitCluster[clusterID], jobID[i])
				reportStore = common.ReportOrderStatusDetail(ctx, orderID, false, reportStore, common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID[i]), time.Now().Unix())
				reportStore = common.ReportOrderStatusDetail(ctx, orderID, false, reportStore, common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID[i]), "started")
			}
			a.logger.Infof("实例(%s)共创建%d个备份任务:%v", clusterID, len(jobID), jobID)
		} else {
			errCluster = append(errCluster, clusterID)
			a.logger.Errorf("实例(%s)创建备份任务失败", clusterID)
		}
	}
	reportStore = common.ReportOrderStatusDetail(ctx, orderID, true, reportStore, "", "")

	if len(waitCluster) == 0 {
		a.logger.Errorln("没有实例备份成功")
		return fmt.Errorf("备份失败")
	}

	a.logger.Infof("实例:%s, 创建备份任务成功!等待60秒后查询备份任务结果", waitCluster)
	time.Sleep(60 * time.Second) // 等待60秒后查询备份任务结果

	// 轮询获取备份结果
	successCluster, err := a.getBackResult(ctx, orderID, waitCluster, timeout, reportStore)
	if err != nil {
		return err
	}
	return a.getBackSummary(ctx, waitCluster, successCluster, errCluster, orderID, form)
}

func (a AliKvStore) getUnfinsihedCluster(waitCluster map[string][]string, successCluster map[string][]string) map[string][]string {
	unfinishedCluster := map[string][]string{}
	for cluster, jobs := range waitCluster {
		if len(jobs) == len(successCluster[cluster]) {
			continue
		}
		doneJobMap := map[string]bool{}
		for _, j := range successCluster[cluster] {
			doneJobMap[j] = true
		}
		for _, j := range jobs {
			if !doneJobMap[j] {
				unfinishedCluster[cluster] = append(unfinishedCluster[cluster], j)
			}
		}
	}
	return unfinishedCluster
}

func (a AliKvStore) getBackSummary(ctx context.Context, waitCluster map[string][]string, successCluster map[string][]string, errCluster []string, orderID string, form common.OperateForm) error {
	// 判断传入集群与成功集群的差集
	backupError := fmt.Errorf("备份失败")
	unfinishedCluster := a.getUnfinsihedCluster(waitCluster, successCluster)
	// 比对传入数量和成功数量，得到最终成功与否判断和日志
	if len(errCluster) != 0 || len(unfinishedCluster) > 0 {
		a.logger.Infof("以下实例备份成功：%v", successCluster)
		if len(errCluster) != 0 {
			a.logger.Errorf("以下实例备份创建备份任务时失败：%v", errCluster)
		}
		if len(unfinishedCluster) > 0 {
			a.logger.Errorf("以下实例成功创建备份任务，但是没有在指定时间范围内结束备份，请通过控制台检查：%v", unfinishedCluster)
		}
		return backupError
	}
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	storeSuccess, storeFailed, _ := common.StatusDetailCount(reportStore)
	if len(storeFailed) != 0 {
		a.logger.Errorf("回顾所有实例发现失败实例: %v", storeFailed)
		return backupError
	}
	if len(storeSuccess) != len(form.Instances) {
		a.logger.Errorf("回顾成功实例总数量不正确，已标记成功的实例：%v", storeSuccess)
		return backupError
	}
	a.logger.Infoln("所有实例备份成功:", storeSuccess)
	return nil
}

// UpdateInstance 更新实例
func (a AliKvStore) UpdateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) error {
	var form common.OperateForm
	err := json.Unmarshal(data, &form)
	if err != nil {
		return err
	}

	switch form.Action {
	case "backup":
		return a.BackupInstance(ctx, timeout, orderID, form)
	case "reboot":
		return a.RestartInstances(ctx, form.Instances)
	}

	return fmt.Errorf("未能识别的操作")
}

func (a AliKvStore) RestartInstances(ctx context.Context, instanceIDs []string) error {
	var wg sync.WaitGroup

	errChan := make(chan error, len(instanceIDs))
	for _, id := range instanceIDs {
		wg.Add(1)
		id := id
		go func() {
			defer wg.Done()
			a.logger.Infof("开始重启实例%s", id)
			err := a.client.RestartInstance(ctx, id)
			if err != nil {
				errChan <- fmt.Errorf("restart instance failed, err: %s", err)
			}
			a.logger.Infof("重启实例%s完成", id)
			a.logger.Infof("等待实例%s就绪", id)
			err = a.client.waitStatusReady(ctx, id)
			if err != nil {
				errChan <- fmt.Errorf("restart instance failed, err: %s", err)
				return
			}
			a.logger.Infof("实例%s已就绪", id)
		}()
	}

	wg.Wait()
	close(errChan)
	errMessages := make([]string, 0)
	for {
		err, ok := <-errChan
		if !ok {
			break
		}
		errMessages = append(errMessages, err.Error())
	}

	if len(errMessages) != 0 {
		return fmt.Errorf(strings.Join(errMessages, "\n"))
	}
	return nil
}

// DeleteInstance 释放实例对象
func (a AliKvStore) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	// 先查询实例，如果付费类型为「包年包月」，要先转换成「按量付费」才能继续删除动作
	a.logger.Infof("删除前准备查询阿里云redis实例: %v\n", form.Instances)
	reportStore := ""
	for _, instance := range form.Instances {
		reportStore = common.ReportOrderStatusDetail(ctx, orderID, false, reportStore, common.ReportSprintf("%s.last_time", instance), time.Now().Unix())

		instDetail, err := a.client.DescribeInstanceAttribute(ctx, instance)
		if err != nil {
			a.logger.Errorf("删除前查询实例%s失败:%s", instance, err.Error())
			return err
		}
		if len(instDetail.Instances.DBInstanceAttribute) == 0 {
			a.logger.Errorf("删除前查询实例%s无数据", instance)
			return err
		}
		if *instDetail.Instances.DBInstanceAttribute[0].ChargeType == "PrePaid" {
			err = a.client.TransformInstanceChargeType2PostPaid(ctx, instance)
			if err != nil {
				a.logger.Errorf("删除前转换实例 %s 为按量付费失败:%s", instance, err.Error())
				return err
			}
			a.logger.Infof("转换实例 %s 为按量付费成功", instance)
		} else {
			a.logger.Infof("实例 %s 为按量付费，无需转换", instance)
		}
	}
	time.Sleep(5 * time.Second)
	a.logger.Infof("准备释放阿里云redis实例: %v\n", form.Instances)
	err := a.client.DeleteInstance(ctx, duration, form)
	if err != nil {
		a.logger.Errorln("删除实例失败:", err.Error())
		return err
	}
	return nil
}

// TestPing 测试连通性
func (a AliKvStore) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}

// GetSnapshotCount ...
func (a AliKvStore) GetSnapshotCount(ctx context.Context) (int32, error) {
	var pageSize, page, totalCount int32 = 100, 1, 0
	body, err := a.client.DescribeInstances(ctx, page, pageSize)
	if err != nil {
		return 0, err
	}
	totalCount = tea.Int32Value(body.TotalCount)
	return totalCount, nil
}

func (a AliKvStore) getJobResult(ctx context.Context, clusterID, jobID, reportStore, orderID string) (isFinsihed bool, newReportStore string) {
	taskResp, err := a.client.DescribeBackupTasks(ctx, 1*time.Minute, clusterID, jobID)
	if err != nil {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID):    "fetch_error",
			common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID): time.Now().Unix(),
			common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, jobID):    true,
		})
		a.logger.Errorf("获取实例(%s)备份任务(%s)失败: %s", clusterID, jobID, err.Error())
		return false, reportStore
	}
	if len(taskResp.BackupJobs) == 0 {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID):    "fetch_error",
			common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID): time.Now().Unix(),
			common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, jobID):    true,
		})
		a.logger.Errorf("获取实例(%s)备份任务(%s)结果异常,未发现实例任务/任务后台创建中,等待30秒后再次查询", clusterID, jobID)
		return false, reportStore
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
		common.ReportSprintf("%s.jobs.j%s.status", clusterID, jobID):    tea.StringValue(taskResp.BackupJobs[0].BackupProgressStatus),
		common.ReportSprintf("%s.jobs.j%s.last_time", clusterID, jobID): time.Now().Unix(),
	})
	if tea.StringValue(taskResp.BackupJobs[0].BackupProgressStatus) == "Finished" {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.jobs.j%s.is_success", clusterID, jobID): true,
			common.ReportSprintf("%s.jobs.j%s.is_err", clusterID, jobID):     false,
		})
		a.logger.Infof("实例(%s)任务(%s)备份成功", clusterID, jobID)
		return true, reportStore
	}
	a.logger.Infof("实例(%s)任务(%s)正在备份中,等待下次查询", clusterID, jobID)
	return false, reportStore
}

func (a AliKvStore) getBackResult(ctx context.Context, orderID string, checkCluster map[string][]string, maxTime time.Duration, reportStore string) (success map[string][]string, err error) {
	// redis会根据分片返回结果,查询时根据jobId查询
	success = make(map[string][]string)
	waitCluster := make(map[string][]string)
	err = deepCopy(checkCluster, &waitCluster)
	if err != nil {
		return
	}
	timeCtx, cancel := context.WithTimeout(context.Background(), maxTime)
	defer cancel()
	for {
		select {
		case <-timeCtx.Done():
			return success, fmt.Errorf("获取结果超时")
		default:
			nextCluster := make(map[string][]string) // 使用map替代slice加快查询速度
			for clusterID, jobIDs := range waitCluster {
				for _, jobID := range jobIDs {
					var isFinished bool
					isFinished, reportStore = a.getJobResult(ctx, clusterID, jobID, reportStore, orderID)
					if !isFinished {
						nextCluster[clusterID] = append(nextCluster[clusterID], jobID)
					} else {
						success[clusterID] = append(success[clusterID], jobID)
					}
					time.Sleep(200 * time.Millisecond) // 每个查询间隔0.2秒
				}
			}
			for successCluster := range success {
				if len(nextCluster[successCluster]) != 0 {
					continue
				}
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.status", successCluster):     "success",
					common.ReportSprintf("%s.is_success", successCluster): true,
					common.ReportSprintf("%s.is_err", successCluster):     false,
					common.ReportSprintf("%s.last_time", successCluster):  time.Now().Unix(),
				})
			}
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
			waitCluster = nextCluster
			if len(waitCluster) == 0 {
				return
			}
			a.logger.Infof("实例(%v)未备份完成,30秒后再次查询", waitCluster)
			time.Sleep(30 * time.Second)
		}
	}
}

// ResourceKvStore 初始化阿里云redis-provide
func ResourceKvStore(regionID, accountID string, logger *logrus.Logger, dryRun bool) (*AliKvStore, error) {
	client, err := CreateAliKvStoreClient(regionID, accountID, dryRun, logger)
	if err != nil {
		return nil, err
	}

	if logger == nil {
		return nil, errors.New("logger object is null")
	}

	return &AliKvStore{client: client, logger: logger, dryRun: dryRun, accountID: accountID}, err
}
