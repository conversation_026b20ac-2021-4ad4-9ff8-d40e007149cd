package alicloud

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	polardb******** "github.com/alibabacloud-go/polardb-********/v6/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/stretchr/testify/assert"

	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

func TestContinueDBInit(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "65572aed9bb2a1793df282a6",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "65572aed9bb2a1793df282a6",
	}

	instanceMap := map[string]string{}
	ctx := context.Background()
	orderID := "65ddace3076c9b9c2a87043b"
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	reportStatusDetail := map[string]map[string]interface{}{}
	err := json.Unmarshal([]byte(reportStore), &reportStatusDetail)
	if err != nil {
		t.Fatalf("err is :%v", err)
	}
	for k, v := range reportStatusDetail {
		instanceID := v["instance_id"].(string)
		status := v["status"].(string)
		if status == "Creating" {
			instanceMap[instanceID] = k
		}
	}

	req := CreatePolarDBInstanceInput{}
	reqStr := `{
		"AccountName": "superuser",
		"AccountPassword": "",
		"Accounts": [
			{
				"AccountName": "mihoyo",
				"AccountPassword": "9hluQqftFfZBxCM3we9u",
				"AccountType": "Super"
			},
			{
				"AccountName": "work",
				"AccountPassword": "r6xUGRo6otVCaYFEywgy",
				"AccountType": "Normal"
			},
			{
				"AccountName": "readonly",
				"AccountPassword": "bo820GHgUMFDaTk1hVG4",
				"AccountType": "Normal"
			},
			{
				"AccountName": "plat",
				"AccountPassword": "rCtmXzENuZHBJJg2npkm",
				"AccountType": "Normal"
			}
		],
		"Amount": 39,
		"AutoRenew": true,
		"ClusterNetworkType": "VPC",
		"CreationCategory": "basic",
		"DBClusterDescription": "nap-prod-gf-cn-userinfo[0001,4]",
		"DBInstanceNetType": "Intranet",
		"DBInstanceStorage": "",
		"DBNodeClass": "polar.mysql.x4.2xlarge",
		"DBType": "Mysql",
		"DBVersion": "8.0",
		"DefaultTimeZone": "+8:00",
		"LowerCaseTableNames": "0",
		"PayType": "Prepaid",
		"SecurityGroupIds": [
			"sg-uf6g9ph64f4u49gn7bgs",
			"sg-uf68d5s7bttj8qbtz1q4",
			"sg-uf6c4nj55o8p947mbxfx"
		],
		"VPCId": "vpc-uf6ou04yl9ajdovvpeg28",
		"VSwitchId": "vsw-uf6hmfff5lp7cvkola1cv",
		"ZoneId": "cn-shanghai-n",
		"Tag": [
			{
				"key": "nap",
				"value": "cn",
				"type": "user",
				"id": 0
			}
		],
		"Period": "Month",
		"UsedTime": "1",
		"ParameterGroupId": "pcpg-uf61spja824a46a",
		"RegionId": "645629775d89966266b3d393",
		"GlobalSecurityIPTempalteIds": [
			"g-yutfu1hsmjbrkk0cp8iw"
		],
		"DBInstanceStorageType": "PSL5"
	}`
	err = json.Unmarshal([]byte(reqStr), &req)
	if err != nil {
		t.Fatalf("err is :%v", err)
	}

	var wg sync.WaitGroup
	fmt.Println(instanceMap)
	for clusterID, instanceName := range instanceMap {
		wg.Add(1)
		go func(clusterID, instanceName string) {
			defer wg.Done()
			err = a.initPolardb(ctx, clusterID, instanceName, orderID, req)
			if err != nil {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceName):  time.Now().Unix(),
					common.ReportSprintf("%s.is_success", instanceName): false,
				})
			} else {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceName):  time.Now().Unix(),
					common.ReportSprintf("%s.is_success", instanceName): true,
				})
			}
		}(clusterID, instanceName)
	}

	wg.Done()
	time.Sleep(1 * time.Second)
	fmt.Println("all finished")
}

func TestTimeout(t *testing.T) {
	timeCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	for {
		select {
		case <-timeCtx.Done():
			fmt.Println("end")
			return
		default:
			fmt.Println("111111")
			time.Sleep(1 * time.Second)
		}
	}

}

func TestChangeMonitorPeriod(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "65f2641304cbd9f8a5427ed0",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "65f2641304cbd9f8a5427ed0",
	}
	ctx := context.Background()
	fmt.Println(a.ChangeMonitorPeriod(ctx, "pc-uf64erza2y9d48ogn", "nap-prod-xieyi-cn-test0001"))
}

func TestPolarDBEndpoint(t *testing.T) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String("accessKey"),
		AccessKeySecret: tea.String("accessSecret"),
		RegionId:        tea.String("us-east-1"),
		SecurityToken:   tea.String("token"),
	}

	cli, err := polardb********.NewClient(cfg)
	if err != nil {
		panic(err)
	}
	fmt.Println(tea.StringValue(cli.Endpoint))
}

func TestDownloadToOSS(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "6540c7f36591041c72706a75",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "6540c7f36591041c72706a75",
	}
	taskID, err := a.client.DownloadToOSS(context.Background(), "pc-uf6o841j75e4flqx9", "**********", "nap-devops", "backup_test/polardb/********/nap-dev-zy0124-cn-all0001/")
	fmt.Println(taskID, err)
}

func TestGetDownloadStatus(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "6540c7f36591041c72706a75",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "6540c7f36591041c72706a75",
	}

	taskStatus, err := a.client.GetDownloadStatus(context.Background(), "pc-uf6o841j75e4flqx9", "**********", "dt-vf46diow1v1h")
	fmt.Println(taskStatus, err)
}

func TestCreateDBBackcup(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "6540c7f36591041c72706a75",
		},
		logger:    logrus.New(),
		accountID: "6540c7f36591041c72706a75",
	}

	resp, err := a.client.CreateLevel2Backup(context.Background(), "pc-uf6r77nuap774mw3n", 30)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}

func TestBackupPolardb(t *testing.T) {
	data := `{"action":"backup","instances":["xxxx"],"is_force":false,"retry_instances":[], "is_level2_backup": false}`
	form := common.OperateForm{}
	err := json.Unmarshal([]byte(data), &form)
	if err != nil {
		t.Fatalf("err is :%v", err)
	}
	assert.False(t, *form.IsLevel2Backup)
	instanceName := "nap-prod-gf-cn-userinfo0001"
	assert.False(t, strings.Contains(instanceName, "nap-prod-gf") && (form.IsLevel2Backup == nil || *form.IsLevel2Backup))

	data2 := `{"action":"backup","instances":["xxxx"],"is_force":false,"retry_instances":[]}`
	form2 := common.OperateForm{}
	err = json.Unmarshal([]byte(data2), &form2)
	if err != nil {
		t.Fatalf("err is :%v", err)
	}
	assert.Nil(t, form2.IsLevel2Backup)
	assert.True(t, strings.Contains(instanceName, "nap-prod-gf") && (form2.IsLevel2Backup == nil || *form2.IsLevel2Backup))
}
