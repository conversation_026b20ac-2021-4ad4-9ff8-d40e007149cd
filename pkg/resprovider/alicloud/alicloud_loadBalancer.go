package alicloud

import (
	"errors"
	"fmt"
	"strings"
	"time"

	alb******** "github.com/alibabacloud-go/alb-********/v2/client"
	cas20200407 "github.com/alibabacloud-go/cas-20200407/v2/client"
	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	openapiutil "github.com/alibabacloud-go/openapi-util/service"
	slb20140515 "github.com/alibabacloud-go/slb-20140515/v4/client"
	serviceutil "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// AliLoadBalancerClient 阿里云lb-client
type AliLoadBalancerClient struct {
	regionID  string
	accountID string
}

// NewAliLoadBalancerClient ecs.NewClientWithAccessKey
func NewAliLoadBalancerClient(regionID, accountID string) (*AliLoadBalancerClient, error) {
	return &AliLoadBalancerClient{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

// DescribeSLBs ...
// TODO:instanceIds 最多十个
func (c *AliLoadBalancerClient) DescribeSLBs(ctx context.Context, instanceIds ...string) (*slb20140515.DescribeLoadBalancersResponseBody, error) {
	describeLoadBalancersReq := &slb20140515.DescribeLoadBalancersRequest{
		RegionId: tea.String(c.regionID),
	}
	if len(instanceIds) > 0 {
		describeLoadBalancersReq.SetLoadBalancerId(strings.Join(instanceIds, ","))
	}
	res := &slb20140515.DescribeLoadBalancersResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "slb", "DescribeLoadBalancers", c.accountID, []interface{}{describeLoadBalancersReq}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// DescribeSLBAttribute ...
func (c *AliLoadBalancerClient) DescribeSLBAttribute(ctx context.Context, lbID string) (*slb20140515.DescribeLoadBalancerAttributeResponseBody, error) {
	describeLoadBalancersReq := &slb20140515.DescribeLoadBalancerAttributeRequest{
		LoadBalancerId: tea.String(lbID),
		RegionId:       tea.String(c.regionID),
	}
	res := &slb20140515.DescribeLoadBalancerAttributeResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "slb", "DescribeLoadBalancerAttribute", c.accountID, []interface{}{describeLoadBalancersReq}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// DescribeSLBListeners ...
func (c *AliLoadBalancerClient) DescribeSLBListeners(ctx context.Context, lbIDs []string) ([]*slb20140515.DescribeLoadBalancerListenersResponseBodyListeners, error) {
	listeners := make([]*slb20140515.DescribeLoadBalancerListenersResponseBodyListeners, 0)

	// 阿里云DescribeSLBListeners一次性最多只能查询10条,所以id超过10条时需要分多次查询
	for i := 0; i < len(lbIDs); i += 10 {
		describeLoadBalancersReq := &slb20140515.DescribeLoadBalancerListenersRequest{
			LoadBalancerId: tea.StringSlice(lbIDs[i:min(len(lbIDs)-1, i+10)]),
			RegionId:       tea.String(c.regionID),
		}
		res := &slb20140515.DescribeLoadBalancerListenersResponse{}

		err := agentsdk.SyncCall(ctx, c.regionID, "slb", "DescribeLoadBalancerListeners", c.accountID, []interface{}{describeLoadBalancersReq}, res)
		if err != nil {
			return nil, err
		}
		listeners = append(listeners, res.Body.Listeners...)
	}

	return listeners, nil
}

// ListAllALBs 列出全部的alb
// instanceIds 最多20个
func (c *AliLoadBalancerClient) ListAllALBs(ctx context.Context, instanceIds ...string) ([]*alb********.ListLoadBalancersResponseBodyLoadBalancers, error) {
	req := &alb********.ListLoadBalancersRequest{}
	if len(instanceIds) > 0 {
		req.SetLoadBalancerIds(tea.StringSlice(instanceIds))
	}
	maxLoop := 100
	lbs := make([]*alb********.ListLoadBalancersResponseBodyLoadBalancers, 0)
	for i := 0; i <= maxLoop; i++ {
		res := &alb********.ListLoadBalancersResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListLoadBalancers", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		lbs = append(lbs, res.Body.LoadBalancers...)
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		req.SetNextToken(*res.Body.NextToken)
	}
	return lbs, nil
}

// GetLbByID 查询指定ALB
func (c *AliLoadBalancerClient) GetLbByID(ctx context.Context, lbID string) (*alb********.ListLoadBalancersResponseBodyLoadBalancers, error) {
	req := &alb********.ListLoadBalancersRequest{}
	req.SetLoadBalancerIds(tea.StringSlice([]string{lbID}))
	res := &alb********.ListLoadBalancersResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListLoadBalancers", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}
	if len(res.Body.LoadBalancers) == 0 {
		return nil, fmt.Errorf("cannot get alb %s", lbID)
	}
	return res.Body.LoadBalancers[0], nil
}

type AlbDetailResponse struct {
	Headers    map[string]*string `json:"headers,omitempty" xml:"headers,omitempty" require:"true"`
	StatusCode *int32             `json:"statusCode,omitempty" xml:"statusCode,omitempty" require:"true"`
	Body       *AlbResponseBody   `json:"body,omitempty" xml:"body,omitempty" require:"true"`
}

type AlbResponseBody struct {
	alb********.GetLoadBalancerAttributeResponseBody
	SecurityGroupIds *[]string `json:"SecurityGroupIds,omitempty" xml:"SecurityGroupIds,omitempty"`
}

// GetALBAttribute 获取alb详情
func (c *AliLoadBalancerClient) GetALBAttribute(ctx context.Context, lbID string) (*AlbResponseBody, error) {
	cli, err := createAlbClient(ctx, c.accountID, c.regionID)
	if err != nil {
		return nil, err
	}
	query := map[string]interface{}{}
	query["LoadBalancerId"] = lbID
	query["RegionId"] = c.regionID

	req := &openapiv2.OpenApiRequest{
		Query: openapiutil.Query(query),
	}

	result := &AlbDetailResponse{}
	body, err := cli.CallApi(getAlbOpenapiParams("GetLoadBalancerAttribute"), req, &serviceutil.RuntimeOptions{})
	if err != nil {
		return nil, err
	}
	tea.Convert(body, &result)
	return result.Body, nil
}

// ListALBListeners 获取alb监听
func (c *AliLoadBalancerClient) ListALBListeners(ctx context.Context, lbIDs []string) ([]*alb********.ListListenersResponseBodyListeners, error) {
	listeners := make([]*alb********.ListListenersResponseBodyListeners, 0)

	// 分批查询, ListListeners接口有限制一次只能查20个listener
	for i := 0; i < len(lbIDs); i += 5 {
		req := &alb********.ListListenersRequest{}
		req.SetLoadBalancerIds(tea.StringSlice(lbIDs[i:min(i+5, len(lbIDs)-1)]))
		res := &alb********.ListListenersResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListListeners", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		listeners = append(listeners, res.Body.Listeners...)
	}

	return listeners, nil
}

// ListALBServerGroups 获取服务器组
func (c *AliLoadBalancerClient) ListALBServerGroups(ctx context.Context) ([]*alb********.ListServerGroupsResponseBodyServerGroups, error) {
	serverGroups := make([]*alb********.ListServerGroupsResponseBodyServerGroups, 0)
	req := &alb********.ListServerGroupsRequest{}
	maxLoop := 100
	for i := 0; i < maxLoop; i++ {
		res := &alb********.ListServerGroupsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListServerGroups", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		serverGroups = append(serverGroups, res.Body.ServerGroups...)
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		req.SetNextToken(*res.Body.NextToken)
	}

	return serverGroups, nil
}

func (c *AliLoadBalancerClient) GetALBServerGroupByName(ctx context.Context, serverGroupName string) ([]*alb********.ListServerGroupsResponseBodyServerGroups, error) {
	req := &alb********.ListServerGroupsRequest{}
	req.SetServerGroupNames(tea.StringSlice([]string{serverGroupName}))
	res := &alb********.ListServerGroupsResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListServerGroups", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	return res.Body.ServerGroups, nil
}

// ListALBServerGroupsServers 查询服务器
func (c *AliLoadBalancerClient) ListALBServerGroupsServers(ctx context.Context, serverGroupID string) ([]*alb********.ListServerGroupServersResponseBodyServers, error) {
	servers := make([]*alb********.ListServerGroupServersResponseBodyServers, 0)
	req := &alb********.ListServerGroupServersRequest{}
	req.SetServerGroupId(serverGroupID)
	wait := common.IncrementalWait(1*time.Second, 1*time.Second)
	timeout := 30 * time.Second

	maxLoop := 100
	for i := 0; i < maxLoop; i++ {
		res := &alb********.ListServerGroupServersResponse{}
		err := common.Retry(timeout, func() *common.RetryError {
			err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListServerGroupServers", c.accountID, []interface{}{req}, res)
			if err != nil {
				if strings.Contains(err.Error(), "Code: Throttling.User") {
					wait()
					return common.RetryableError(err)
				}
				return common.NonRetryableError(err)
			}
			servers = append(servers, res.Body.Servers...)
			return nil
		})
		if err != nil {
			return nil, err
		}
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		req.SetNextToken(*res.Body.NextToken)
	}

	return servers, nil
}

func (c *AliLoadBalancerClient) DescribeZones(ctx context.Context) (*cloudman.DescribeZonesRes, error) {
	req := &alb********.DescribeZonesRequest{}

	res := &alb********.DescribeZonesResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "DescribeZones", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	out := &cloudman.DescribeZonesRes{
		Total: int32(len(res.Body.Zones)),
		List:  []*cloudman.ALBZone{},
	}
	for _, zone := range res.Body.Zones {
		out.List = append(out.List, &cloudman.ALBZone{
			LocalName: *zone.LocalName,
			ZoneId:    *zone.ZoneId,
		})
	}
	return out, nil
}

func (c *AliLoadBalancerClient) ListALBAcls(ctx context.Context, aclIDs ...string) ([]*alb********.ListAclsResponseBodyAcls, error) {
	maxLoop := 100
	req := &alb********.ListAclsRequest{}
	if len(aclIDs) > 0 {
		req.SetAclIds(tea.StringSlice(aclIDs))
	}
	acls := make([]*alb********.ListAclsResponseBodyAcls, 0)
	for i := 0; i <= maxLoop; i++ {
		res := &alb********.ListAclsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListAcls", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}

		acls = append(acls, res.Body.Acls...)
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		req.SetNextToken(*res.Body.NextToken)
	}
	return acls, nil
}

func (c *AliLoadBalancerClient) ListALBAclByIDs(ctx context.Context, ids ...string) ([]*alb********.ListAclsResponseBodyAcls, error) {
	if len(ids) == 0 {
		return nil, fmt.Errorf("get emtpy ids")
	}
	maxLoop := 100
	acls := make([]*alb********.ListAclsResponseBodyAcls, 0)
	req := &alb********.ListAclsRequest{}
	req.SetAclIds(tea.StringSlice(ids))
	for i := 0; i <= maxLoop; i++ {
		res := &alb********.ListAclsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListAcls", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}

		acls = append(acls, res.Body.Acls...)
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		req.SetNextToken(*res.Body.NextToken)
	}
	return acls, nil
}

func (c *AliLoadBalancerClient) ListAclEntries(ctx context.Context, aclID string) ([]*alb********.ListAclEntriesResponseBodyAclEntries, error) {
	maxLoop := 100
	entries := make([]*alb********.ListAclEntriesResponseBodyAclEntries, 0)
	req := &alb********.ListAclEntriesRequest{}
	req.SetAclId(aclID)
	for i := 0; i <= maxLoop; i++ {
		res := &alb********.ListAclEntriesResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListAclEntries", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}

		entries = append(entries, res.Body.AclEntries...)
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		req.SetNextToken(*res.Body.NextToken)
	}
	return entries, nil
}

func (c *AliLoadBalancerClient) ListAclRelatedListeners(ctx context.Context, aclIDs []string) ([]*alb********.ListAclRelationsResponseBodyAclRelationsRelatedListeners, error) {
	acls := make([]*alb********.ListAclRelationsResponseBodyAclRelationsRelatedListeners, 0)
	req := &alb********.ListAclRelationsRequest{}
	req.SetAclIds(tea.StringSlice(aclIDs))
	res := &alb********.ListAclRelationsResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "ListAclRelations", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	if len(res.Body.AclRelations) > 0 {
		acls = append(acls, res.Body.AclRelations[0].RelatedListeners...)
	}
	return acls, nil
}

func (c *AliLoadBalancerClient) ListAllALBAcls(ctx context.Context) (*cloudman.ListAClsRes, error) {
	acls, err := c.ListALBAcls(ctx)
	if err != nil {
		return nil, err
	}

	out := &cloudman.ListAClsRes{
		Total: int32(len(acls)),
		List:  []*cloudman.ALBAcl{},
	}
	for _, acl := range acls {
		out.List = append(out.List, &cloudman.ALBAcl{
			AclId:   *acl.AclId,
			AclName: *acl.AclName,
		})
	}
	return out, nil
}

func (c *AliLoadBalancerClient) ListCertificates(ctx context.Context) (*cloudman.ListCertificatesRes, error) {
	certificates := make([]*cloudman.Certificate, 0)
	regionIDs := []string{"ap-southeast-1", "cn-hangzhou"}
	for _, id := range regionIDs {
		rCerts, err := c.ListRegionCertificates(ctx, id)
		if err != nil {
			return nil, err
		}
		certificates = append(certificates, rCerts...)
	}
	out := &cloudman.ListCertificatesRes{
		Total: int32(len(certificates)),
		List:  certificates,
	}
	return out, nil
}

func (c *AliLoadBalancerClient) ListRegionCertificates(ctx context.Context, regionID string) ([]*cloudman.Certificate, error) {
	req := cas20200407.ListUserCertificateOrderRequest{}
	req.SetOrderType("CERT")
	res := &cas20200407.ListUserCertificateOrderResponse{}
	err := agentsdk.SyncCall(ctx, regionID, "cas", "ListUserCertificateOrder", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	certificates := make([]*cloudman.Certificate, 0)
	for _, order := range res.Body.CertificateOrderList {
		beforeDate, err := time.Parse("2006-01-02", tea.StringValue(order.StartDate))
		if err != nil {
			return nil, err
		}
		afterDate, err := time.Parse("2006-01-02", tea.StringValue(order.EndDate))
		if err != nil {
			return nil, err
		}
		certificates = append(certificates, &cloudman.Certificate{
			CertIdentifier: fmt.Sprintf("%d-%s", tea.Int64Value(order.CertificateId), regionID),
			CertName:       tea.StringValue(order.Name),
			CommonName:     tea.StringValue(order.CommonName),
			Domain:         tea.StringValue(order.Domain),
			AfterDate:      afterDate.Unix(),
			BeforeDate:     beforeDate.Unix(),
		})
	}
	return certificates, nil
}

func (c *AliLoadBalancerClient) CreateLB(ctx context.Context, req *alb********.CreateLoadBalancerRequest) (*alb********.CreateLoadBalancerResponseBody, error) {
	res := &alb********.CreateLoadBalancerResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "CreateLoadBalancer", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

func (c *AliLoadBalancerClient) GetLBAttribute(ctx context.Context, id string) (*alb********.GetLoadBalancerAttributeResponse, error) {
	req := &alb********.GetLoadBalancerAttributeRequest{}
	req.SetLoadBalancerId(id)
	res := &alb********.GetLoadBalancerAttributeResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "GetLoadBalancerAttribute", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}
	return res, nil
}
func (c *AliLoadBalancerClient) CreateOrGetDefaultServerGroup(ctx context.Context, groupName, vpcID string) (string, error) {
	// 先查询服务器组是否存在
	serverGroups, err := c.GetALBServerGroupByName(ctx, groupName)
	if err != nil {
		return "", err
	}
	if len(serverGroups) > 0 {
		return tea.StringValue(serverGroups[0].ServerGroupId), nil
	}

	// 不存在则创建新的服务器组
	req := &alb********.CreateServerGroupRequest{
		HealthCheckConfig: &alb********.CreateServerGroupRequestHealthCheckConfig{
			HealthCheckCodes: []*string{
				tea.String("http_2xx"),
			},
			HealthCheckEnabled:     tea.Bool(true),
			HealthCheckHttpVersion: tea.String("HTTP1.1"),
			HealthCheckInterval:    tea.Int32(5),
			HealthCheckMethod:      tea.String("GET"),
			HealthCheckPath:        tea.String("/alive"),
			HealthCheckProtocol:    tea.String("HTTP"),
		},
		Protocol:        tea.String("HTTP"),
		Scheduler:       tea.String("Wrr"),
		ServerGroupName: tea.String(groupName),
		ServerGroupType: tea.String("Instance"),
		StickySessionConfig: &alb********.CreateServerGroupRequestStickySessionConfig{
			StickySessionEnabled: tea.Bool(false),
		},
		VpcId: tea.String(vpcID),
	}
	res := &alb********.CreateServerGroupResponse{}
	err = agentsdk.SyncCall(ctx, c.regionID, "alb", "CreateServerGroup", c.accountID, []interface{}{req}, res)
	if err != nil {
		return "", err
	}
	return *res.Body.ServerGroupId, nil
}

func (c *AliLoadBalancerClient) CreateListenerAndWaitReady(ctx context.Context, req *alb********.CreateListenerRequest, lbID string) (string, error) {
	res := &alb********.CreateListenerResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "CreateListener", c.accountID, []interface{}{req}, res)
	if err != nil {
		return "", err
	}

	getAttributeReq := &alb********.GetListenerAttributeRequest{
		ListenerId: res.Body.ListenerId,
	}
	// wait listener ready
	timeout := 2 * time.Minute
	err = common.Retry(timeout, func() *common.RetryError {
		res := &alb********.GetListenerAttributeResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "alb", "GetListenerAttribute", c.accountID, []interface{}{getAttributeReq}, res)
		if err != nil {
			return common.NonRetryableError(err)
		}
		if tea.StringValue(res.Body.ListenerStatus) != "Running" {
			return common.RetryableError(errors.New("not ready"))
		}
		return nil
	})
	if err != nil {
		return "", err
	}

	// wait lb ready
	err = common.Retry(timeout, func() *common.RetryError {
		resp, err := c.GetLBAttribute(ctx, lbID)
		if err != nil {
			return common.NonRetryableError(err)
		}
		if tea.StringValue(resp.Body.LoadBalancerStatus) != "Active" {
			return common.RetryableError(errors.New("not ready"))
		}
		return nil
	})
	if err != nil {
		return "", err
	}

	return tea.StringValue(res.Body.ListenerId), nil
}

func (c *AliLoadBalancerClient) BindListenerACLAndWaitforReady(ctx context.Context, listenerID string, aclIDs []string, aclType string) error {
	if len(aclIDs) == 0 {
		return nil
	}

	req := &alb********.AssociateAclsWithListenerRequest{}
	req.SetAclIds(tea.StringSlice(aclIDs))
	req.SetAclType(aclType)
	req.SetListenerId(listenerID)
	res := &alb********.AssociateAclsWithListenerResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "alb", "AssociateAclsWithListener", c.accountID, []interface{}{req}, res)
	if err != nil {
		return err
	}

	// wait bind success
	timeout := 2 * time.Minute
	err = common.Retry(timeout, func() *common.RetryError {
		resp, err := c.ListAclRelatedListeners(ctx, aclIDs)
		if err != nil {
			return common.NonRetryableError(err)
		}
		for _, l := range resp {
			if tea.StringValue(l.ListenerId) == listenerID && tea.StringValue(l.Status) != "Associated" {
				return common.RetryableError(errors.New("not ready"))
			}
		}
		return nil
	})
	return err
}

func (c *AliLoadBalancerClient) AddEntriesToAcl(ctx context.Context, aclID string, entries []*cloudman.AddALBAclsEntry) error {
	req := &alb********.AddEntriesToAclRequest{}
	req.SetAclId(aclID)
	reqE := make([]*alb********.AddEntriesToAclRequestAclEntries, 0)
	for _, e := range entries {
		ee := &alb********.AddEntriesToAclRequestAclEntries{
			Entry: tea.String(e.Entry),
		}
		if e.Description != "" {
			ee.SetDescription(strings.ReplaceAll(e.Description, " ", ""))
		}
		reqE = append(reqE, ee)
	}
	req.SetAclEntries(reqE)
	res := &alb********.AddEntriesToAclResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "alb", "AddEntriesToAcl", c.accountID, []interface{}{req}, res)
}

func (c *AliLoadBalancerClient) RemoveEntriesFromAcl(ctx context.Context, aclID string, entries []string) error {
	req := &alb********.RemoveEntriesFromAclRequest{}
	req.SetAclId(aclID)
	req.SetEntries(tea.StringSlice(entries))
	res := &alb********.RemoveEntriesFromAclResponse{}

	return agentsdk.SyncCall(ctx, c.regionID, "alb", "RemoveEntriesFromAcl", c.accountID, []interface{}{req}, res)
}

func (c *AliLoadBalancerClient) WaitForReady(ctx context.Context, instanceID string, timeout time.Duration) error {
	err := common.Retry(timeout, func() *common.RetryError {
		resp, err := c.GetLbByID(ctx, instanceID)
		if err != nil {
			return common.NonRetryableError(err)
		}
		if tea.StringValue(resp.LoadBalancerStatus) != "Active" {
			return common.RetryableError(errors.New("not ready"))
		}
		return nil
	})
	return err
}

func (c *AliLoadBalancerClient) DeleteLoadBalancer(ctx context.Context, instanceID string) error {
	req := &alb********.DeleteLoadBalancerRequest{}
	req.SetLoadBalancerId(instanceID)
	res := &alb********.DeleteLoadBalancerResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "alb", "DeleteLoadBalancer", c.accountID, []interface{}{req}, res)
}

func (c *AliLoadBalancerClient) DeleteServerGroup(ctx context.Context, serverGroupID string) error {
	req := &alb********.DeleteServerGroupRequest{}
	req.SetServerGroupId(serverGroupID)
	res := &alb********.DeleteServerGroupResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "alb", "DeleteServerGroup", c.accountID, []interface{}{req}, res)
}

func (c *AliLoadBalancerClient) DeleteListener(ctx context.Context, listenerID string) error {
	req := &alb********.DeleteListenerRequest{}
	req.SetListenerId(listenerID)
	res := &alb********.DeleteListenerResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "alb", "DeleteListener", c.accountID, []interface{}{req}, res)
}

func createAlbClient(ctx context.Context, accountID, regionID string) (*alb********.Client, error) {
	account, err := models.AccountModel.Get(ctx, accountID, true)
	if err != nil {
		return nil, err
	}

	ak, _ := utils.DecryptStrByAes(account.AccessKey, constant.AccountSecret)
	sk, _ := utils.DecryptStrByAes(account.AccessSecret, constant.AccountSecret)

	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(ak),
		AccessKeySecret: tea.String(sk),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(""),
	}

	return alb********.NewClient(cfg)
}

func getAlbOpenapiParams(action string) *openapiv2.Params {
	return &openapiv2.Params{
		Action:      tea.String(action),
		Version:     tea.String("2020-06-16"),
		Protocol:    tea.String("HTTPS"),
		Pathname:    tea.String("/"),
		Method:      tea.String("POST"),
		AuthType:    tea.String("AK"),
		Style:       tea.String("RPC"),
		ReqBodyType: tea.String("formData"),
		BodyType:    tea.String("json"),
	}
}

// JoinSecurityGroup 加入安全组, 直接调用openapi
func (c *AliLoadBalancerClient) JoinSecurityGroup(ctx context.Context, lbID string, securityGroupIDs []string) error {
	cli, err := createAlbClient(ctx, c.accountID, c.regionID)
	if err != nil {
		return err
	}
	query := map[string]interface{}{}
	query["LoadBalancerId"] = lbID
	query["RegionId"] = c.regionID
	query["SecurityGroupIds"] = securityGroupIDs

	req := &openapiv2.OpenApiRequest{
		Query: openapiutil.Query(query),
	}

	result := &LoadBalancerJoinSecurityGroupResponse{}
	body, err := cli.CallApi(getAlbOpenapiParams("LoadBalancerJoinSecurityGroup"), req, &serviceutil.RuntimeOptions{})
	if err != nil {
		return err
	}
	return tea.Convert(body, &result)
}

// LeaveSecurityGroup 解绑安全组, 直接调用openapi
func (c *AliLoadBalancerClient) LeaveSecurityGroup(ctx context.Context, lbID string, securityGroupIDs []string) error {
	cli, err := createAlbClient(ctx, c.accountID, c.regionID)
	if err != nil {
		return err
	}
	query := map[string]interface{}{}
	query["LoadBalancerId"] = lbID
	query["RegionId"] = c.regionID
	query["SecurityGroupIds"] = securityGroupIDs

	req := &openapiv2.OpenApiRequest{
		Query: openapiutil.Query(query),
	}

	result := &LoadBalancerLeaveSecurityGroupResponse{}
	body, err := cli.CallApi(getAlbOpenapiParams("LoadBalancerLeaveSecurityGroup"), req, &serviceutil.RuntimeOptions{})
	if err != nil {
		return err
	}
	return tea.Convert(body, &result)
}

// TagLB ...
func (c AliLoadBalancerClient) TagLB(ctx context.Context, instanceIDs []string, tag []*alb********.TagResourcesRequestTag) error {
	req := &alb********.TagResourcesRequest{}
	req.SetResourceId(tea.StringSlice(instanceIDs))
	req.SetResourceType("loadbalancer")
	req.SetTag(tag)
	res := &alb********.TagResourcesResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "alb", "TagResources", c.accountID, []interface{}{&req}, res)
}

// MoveResourceGroup ...
func (c AliLoadBalancerClient) MoveResourceGroup(ctx context.Context, instanceId, resourceGroupId string) error {
	req := &alb********.MoveResourceGroupRequest{}
	req.SetResourceId(instanceId)
	req.SetResourceType("loadbalancer")
	req.SetNewResourceGroupId(resourceGroupId)
	res := &alb********.TagResourcesResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "alb", "MoveResourceGroup", c.accountID, []interface{}{&req}, res)
}

func (c AliLoadBalancerClient) EnableAccessLog(ctx context.Context, instanceId, instanceName string) error {
	region, err := utils.GetRegionBylbName(instanceName)
	if err != nil {
		return err
	}
	req := &alb********.EnableLoadBalancerAccessLogRequest{}
	req.SetLoadBalancerId(instanceId)
	req.SetLogProject(fmt.Sprintf("lb-%s", region))
	req.SetLogStore(instanceName)

	res := &alb********.EnableLoadBalancerAccessLogResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "alb", "EnableLoadBalancerAccessLog", c.accountID, []interface{}{&req}, res)
}
