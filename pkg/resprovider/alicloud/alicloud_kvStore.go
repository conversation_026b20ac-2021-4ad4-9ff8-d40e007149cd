package alicloud

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	r_kvstore20150101 "github.com/alibabacloud-go/r-kvstore-20150101/v7/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/kr/pretty"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// AliKvStoreClient 阿里云kvStore-client
type AliKvStoreClient struct {
	regionID string
	dryRun   bool
	logger   *logrus.Logger

	accountID string
}

// CreateAliKvStoreClient create ali client
func CreateAliKvStoreClient(regionID string, accountID string, dryRun bool, logger *logrus.Logger) (client *AliKvStoreClient, err error) {
	// 访问的域名
	// config.Endpoint = tea.String(endpoint)
	// if err != nil {
	// 	return nil, err
	// }
	return &AliKvStoreClient{
		logger:    logger,
		accountID: accountID,
		regionID:  regionID,
		dryRun:    dryRun,
	}, nil
}

// CreateInstance 创建实例
func (c AliKvStoreClient) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	var rdata CreateKvStoreInstanceInput
	err = json.Unmarshal(data, &rdata)
	if err != nil {
		return nil, err
	}

	// 目前只支持申请单个实例，所以只生成一个实例名称
	instanceNames, err := cloudutils.GetCloudutils(models.RedisResourceModel).GenInstanceName(
		context.Background(),
		tea.StringValue(rdata.InstanceName),
		1, true, cloudutils.Linux,
	)
	if err != nil {
		return nil, err
	}

	rdata.InstanceName = &instanceNames[0]

	reportStore := ""
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
		common.ReportSprintf("%s.start_time", tea.StringValue(rdata.InstanceName)):    time.Now().Unix(),
		common.ReportSprintf("%s.status", tea.StringValue(rdata.InstanceName)):        "preparing",
		common.ReportSprintf("%s.instance_name", tea.StringValue(rdata.InstanceName)): tea.StringValue(rdata.InstanceName),
	})

	if c.dryRun {
		rdata.DryRun = tea.Bool(true)
	}
	// tag参数重新序列化
	tag := struct {
		Tag []*common.Tag `json:"Tags"`
	}{}
	err = json.Unmarshal(data, &tag)
	if err != nil {
		return nil, err
	}
	hostname := instanceNames[0]
	rdata.Tag = []*r_kvstore20150101.CreateInstanceRequestTag{
		{
			Key:   tea.String("nap"),
			Value: tea.String(hostname_util.GetHostNameArea(hostname)),
		},
		{
			Key:   tea.String("env"),
			Value: tea.String(hostname_util.GetHostNameEnv(hostname)),
		},
		{
			Key:   tea.String("region"),
			Value: tea.String(hostname_util.GetHostNameRegion(hostname)),
		},
		{
			Key:   tea.String("module"),
			Value: tea.String(hostname_util.GetHostNameModule(hostname)),
		},
		{
			Key:   tea.String("region_module"),
			Value: tea.String(hostname_util.GetHostNameRegionModule(hostname)),
		},
		{
			Key:   tea.String("serial_number"),
			Value: tea.String(hostname_util.GetHostNameRegionModule(hostname)),
		},
	}
	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	if len(tag.Tag) > 0 {
		for _, t := range tag.Tag {
			// 通用的标签不允许自定义
			if slices.Contains(systemTag, t.Key) {
				continue
			}
			rdata.Tag = append(rdata.Tag, &r_kvstore20150101.CreateInstanceRequestTag{
				Key:   &t.Key,
				Value: &t.Value,
			})
		}
	}
	c.logger.Infof("绑定标签为%# v", pretty.Formatter(rdata.Tag))

	instanceID := ""
	instanceName := ""
	switch instanceType := tea.StringValue(rdata.InstanceType); instanceType {
	case "Redis":
		resp, err := c.createRedisInstance(ctx, &rdata, orderID)
		if err != nil {
			if IsDryError(err) {
				return []string{}, nil
			}
			if orderID == "" {
				return nil, err
			}
			instanceName := rdata.InstanceName
			common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceName):    "start_failed",
				common.ReportSprintf("%s.is_err", instanceName):    true,
			})
			return nil, err
		}
		instanceID = tea.StringValue(resp.Body.InstanceId)
		instanceName = tea.StringValue(resp.Body.InstanceName)
	case "Tair":
		resp, err := c.createTairInstance(ctx, &rdata, orderID)
		if err != nil {
			if IsDryError(err) {
				return []string{}, nil
			}
			if orderID == "" {
				return nil, err
			}
			instanceName := rdata.InstanceName
			common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceName):    "start_failed",
				common.ReportSprintf("%s.is_err", instanceName):    true,
			})
			return nil, err
		}
		instanceID = tea.StringValue(resp.Body.InstanceId)
		instanceName = tea.StringValue(resp.Body.InstanceName)
	default:
		return nil, fmt.Errorf("wrong instance type: %s", instanceType)
	}

	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", instanceName):   time.Now().Unix(),
		common.ReportSprintf("%s.status", instanceName):      "started",
		common.ReportSprintf("%s.instance_id", instanceName): instanceID,
	})

	// 轮询redis实例状态直到ready
	c.logger.Infof("30秒一次判断本次创建的redis是否就绪，持续30min")
	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()
	reportStore, err = c.waitInstanceReady(ctx, orderID, instanceID, instanceName, reportStore)
	if err != nil {
		return nil, err
	}

	// 1. 绑定安全组
	reportStore, err = c.BindSecurityGroup(ctx, orderID, instanceID, instanceName, reportStore, rdata.SecurityGroupIds)
	if err != nil {
		reportError(ctx, instanceName, orderID, reportStore, "security_group")
		return nil, err
	}

	// 2. 禁用命令
	reportStore, err = c.DisableCommands(ctx, orderID, instanceID, instanceName, reportStore)
	if err != nil {
		reportError(ctx, instanceName, orderID, reportStore, "disable_commands")
		return nil, err
	}

	// 3. 修改备份时间
	reportStore, err = c.ModifyBackupPolicy(ctx, orderID, instanceID, instanceName, reportStore)
	if err != nil {
		reportError(ctx, instanceName, orderID, reportStore, "modify_backup_policy")
		return nil, err
	}

	// 4. 创建账号
	if len(rdata.Accounts) > 0 {
		for _, account := range rdata.Accounts {
			err := c.CreateAccount(ctx, instanceID, account.AccountName, account.AccountPrivilege, account.AccountPassword)
			if err != nil {
				c.logger.Errorf("创建账号%s失败, err: %v", account.AccountName, err)
				reportError(ctx, instanceName, orderID, reportStore, "create_accounts")
				return nil, err
			}
			c.logger.Infof("创建账号%s成功", account.AccountName)
		}
		reportStore = reportSuccess(ctx, instanceName, orderID, reportStore, "create_accounts")
	}

	// 5. 上报信息
	common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", instanceName):  time.Now().Unix(),
		common.ReportSprintf("%s.status", instanceName):     "success",
		common.ReportSprintf("%s.is_success", instanceName): true,
	})

	return []string{instanceID}, err
}

func (c AliKvStoreClient) createRedisInstance(ctx context.Context, rdata *CreateKvStoreInstanceInput, orderID string) (*r_kvstore20150101.CreateInstanceResponse, error) {
	rdata.RegionId = tea.String(c.regionID)
	resp := &r_kvstore20150101.CreateInstanceResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "redis", "CreateInstance", c.accountID, []interface{}{rdata.CreateInstanceRequest}, resp)
	if err != nil {

		return nil, err
	}
	return resp, nil
}

func (c AliKvStoreClient) createTairInstance(ctx context.Context, rdata *CreateKvStoreInstanceInput, orderID string) (*r_kvstore20150101.CreateTairInstanceResponse, error) {
	req := r_kvstore20150101.CreateTairInstanceRequest{}
	req.SetRegionId(c.regionID)
	req.InstanceName = rdata.InstanceName
	req.Password = rdata.Password
	req.InstanceClass = rdata.TairInstanceClass
	req.ZoneId = rdata.ZoneId
	req.SecondaryZoneId = rdata.SecondaryZoneId
	req.VpcId = rdata.VpcId
	req.VSwitchId = rdata.VSwitchId
	req.ChargeType = rdata.ChargeType
	req.AutoRenew = rdata.AutoRenew
	period, err := strconv.Atoi(tea.StringValue(rdata.Period))
	if err != nil {
		return nil, err
	}
	req.SetPeriod(int32(period))
	req.AutoRenewPeriod = rdata.AutoRenewPeriod
	req.ShardCount = rdata.TairShardcount
	req.EngineVersion = rdata.TairEngineVersion
	req.ResourceGroupId = rdata.ResourceGroupId

	// readonly count 参数不能为0
	if tea.Int32Value(rdata.TairReadOnlyCount) != 0 {
		req.ReadOnlyCount = rdata.TairReadOnlyCount
		// 备可用区只读节点数量暂定为1
		req.SetSlaveReadOnlyCount(1)
	}
	if len(rdata.Tag) > 0 {
		req.Tag = make([]*r_kvstore20150101.CreateTairInstanceRequestTag, 0)
		for _, t := range rdata.Tag {
			req.Tag = append(req.Tag, &r_kvstore20150101.CreateTairInstanceRequestTag{
				Key:   t.Key,
				Value: t.Value,
			})
		}
	}

	req.DryRun = rdata.DryRun
	// 目前都是用内存类型的tair
	req.SetInstanceType("tair_rdb")
	fmt.Printf("tair instance create req is: %+v", req)
	resp := &r_kvstore20150101.CreateTairInstanceResponse{}
	err = agentsdk.SyncCall(ctx, c.regionID, "redis", "CreateTairInstance", c.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c AliKvStoreClient) waitStatusReady(ctx context.Context, instanceID string) error {
	timeout := time.Minute * 10
	err := common.Retry(timeout, func() *common.RetryError {
		attribute, err := c.DescribeInstanceAttribute(ctx, instanceID)
		if err != nil {
			return common.NonRetryableError(err)
		}

		if len(attribute.Instances.DBInstanceAttribute) != 1 {
			return common.NonRetryableError(errors.New("not found instance"))
		}

		instance := attribute.Instances.DBInstanceAttribute[0]

		if tea.StringValue(instance.InstanceStatus) != "Normal" {
			return common.RetryableError(errors.New("not ready"))
		}

		return nil
	})
	return err
}

func (c AliKvStoreClient) waitInstanceReady(ctx context.Context, orderID, instanceID, instanceName, reportStore string) (string, error) {
	for {
		select {
		case <-ctx.Done():
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceName):    "confirm_timeout",
				common.ReportSprintf("%s.is_err", instanceName):    true,
			})
			return reportStore, fmt.Errorf("执行超时")
		default:
			resp := &r_kvstore20150101.DescribeInstancesResponse{}
			err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeInstances", c.accountID, []interface{}{&r_kvstore20150101.DescribeInstancesRequest{
				InstanceIds: tea.String(instanceID),
				RegionId:    tea.String(c.regionID),
			}}, resp)
			if err != nil {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
					common.ReportSprintf("%s.status", instanceName):    "confirm_fetch_error",
					common.ReportSprintf("%s.is_err", instanceName):    true,
				})
				c.logger.Errorf("获取实例:%s状态失败", instanceID)
				return reportStore, err
			}
			if len(resp.Body.Instances.KVStoreInstance) == 0 {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
					common.ReportSprintf("%s.status", instanceName):    "confirm_fetch_empty",
					common.ReportSprintf("%s.is_err", instanceName):    true,
				})
				c.logger.Errorf("获取实例:%s状态为空", instanceID)
				return reportStore, err
			}
			if tea.StringValue(resp.Body.Instances.KVStoreInstance[0].InstanceStatus) == "Normal" {
				return reportStore, nil
			}
			c.logger.Warnf("实例:%s状态未就绪,30秒后再次查询", instanceID)
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceName):    tea.StringValue(resp.Body.Instances.KVStoreInstance[0].InstanceStatus),
			})
			time.Sleep(30 * time.Second)
		}
	}
}

func (c AliKvStoreClient) BindSecurityGroup(ctx context.Context, orderID, instanceID, instanceName, reportStore string, securityGroupIDs []string) (string, error) {
	mReq := r_kvstore20150101.ModifySecurityGroupConfigurationRequest{}
	mReq.SetDBInstanceId(instanceID)
	mReq.SetSecurityGroupId(strings.Join(securityGroupIDs, ","))

	resp := &r_kvstore20150101.ModifySecurityGroupConfigurationResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "redis", "ModifySecurityGroupConfiguration", c.accountID, []interface{}{&mReq}, resp)
	if err != nil {
		c.logger.Errorf("实例:%s修改安全组失败: %v", instanceID, err)
		return reportStore, err
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.jobs.security_group.last_time", instanceName):  time.Now().Unix(),
		common.ReportSprintf("%s.jobs.security_group.status", instanceName):     "success",
		common.ReportSprintf("%s.jobs.security_group.is_success", instanceName): true,
	})
	c.logger.Infof("实例:%s修改安全组成功", instanceID)
	return reportStore, nil
}

func (c AliKvStoreClient) DisableCommands(ctx context.Context, orderID, instanceID, instanceName, reportStore string) (string, error) {
	mReq := r_kvstore20150101.ModifyInstanceConfigRequest{}
	mReq.SetInstanceId(instanceID)
	mReq.SetConfig(`{"#no_loose_disabled-commands":"flushall,flushdb,keys,hgetall,del,eval"}`)

	// 禁用命令需要等待前面任务完成，所以加了重试
	err := common.Retry(1*time.Minute, func() *common.RetryError {
		resp := &r_kvstore20150101.ModifyInstanceConfigResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "redis", "ModifyInstanceConfig", c.accountID, []interface{}{&mReq}, resp)
		if err != nil {
			return common.RetryableError(err)
		}
		return nil
	})
	if err != nil {
		c.logger.Errorf("实例:%s禁用命令失败, err: %v", instanceID, err)
		return reportStore, err
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.jobs.disable_commands.last_time", instanceName):  time.Now().Unix(),
		common.ReportSprintf("%s.jobs.disable_commands.status", instanceName):     "success",
		common.ReportSprintf("%s.jobs.disable_commands.is_success", instanceName): true,
	})
	c.logger.Infof("实例:%s禁用命令成功", instanceID)
	return reportStore, nil
}

// UpdateInstance 更新实例
func (c AliKvStoreClient) UpdateInstance(duration time.Duration, data []byte) error {
	return nil
}

// DeleteInstance 删除指定实例
func (c AliKvStoreClient) DeleteInstance(ctx context.Context, duration time.Duration, form common.DeleteInstanceForm) error {
	for _, v := range form.Instances {
		resp := &r_kvstore20150101.DeleteInstanceResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DeleteInstance", c.accountID, []interface{}{&r_kvstore20150101.DeleteInstanceRequest{
			InstanceId: tea.String(v),
		}}, resp)
		if err != nil {
			return err
		}
	}

	return nil
}

// TransformInstanceChargeType2PostPaid 付费类型变更，转为按量付费
func (c AliKvStoreClient) TransformInstanceChargeType2PostPaid(ctx context.Context, instanceID string) error {
	resp := &r_kvstore20150101.TransformInstanceChargeTypeResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "redis", "TransformInstanceChargeType", c.accountID, []interface{}{&r_kvstore20150101.TransformInstanceChargeTypeRequest{
		InstanceId: tea.String(instanceID),
		ChargeType: tea.String("PostPaid"),
	}}, resp)
	if err != nil {
		return err
	}
	return nil
}

// TestPing 测试连通性
func (c AliKvStoreClient) TestPing(timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}

type retryFunc func() error

func retry(f retryFunc) error {
	var waitTime = common.IncrementalWait(5*time.Second, 1*time.Second)

	return common.Retry(1*time.Minute, func() *common.RetryError {
		err := f()
		if err != nil {
			// 遇到查询限流或网络中断时重试
			if IsThrottling(err) || IsRequestEOF(err) {
				waitTime()
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
}

// DescribeInstances 获取kv-store实例
func (c AliKvStoreClient) DescribeInstances(ctx context.Context, page, size int32, instanceIDs ...string) (*r_kvstore20150101.DescribeInstancesResponseBody, error) {
	request := &r_kvstore20150101.DescribeInstancesRequest{
		RegionId: tea.String(c.regionID),
	}

	if len(instanceIDs) > 0 {
		instanceIDsJSON := strings.Join(instanceIDs, ",")
		request.InstanceIds = tea.String(instanceIDsJSON)
	} else {
		request.PageSize = tea.Int32(size)
		request.PageNumber = tea.Int32(page)
	}

	var body *r_kvstore20150101.DescribeInstancesResponseBody
	err := retry(func() error {
		res := &r_kvstore20150101.DescribeInstancesResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeInstances", c.accountID, []interface{}{request}, res)
		if err != nil {
			return err
		}
		body = res.Body
		return nil
	})

	return body, err
}

// DescribeInstanceAttribute 获取kv-store实例
func (c AliKvStoreClient) DescribeInstanceAttribute(ctx context.Context, instanceID string) (*r_kvstore20150101.DescribeInstanceAttributeResponseBody, error) {
	request := &r_kvstore20150101.DescribeInstanceAttributeRequest{
		InstanceId: tea.String(instanceID),
	}
	res := &r_kvstore20150101.DescribeInstanceAttributeResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeInstanceAttribute", c.accountID, []interface{}{request}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// DescribeSecurityGroupConfiguration 获取kv-store实例配置
func (c AliKvStoreClient) DescribeSecurityGroupConfiguration(ctx context.Context, instanceID string) (*r_kvstore20150101.DescribeSecurityGroupConfigurationResponseBody, error) {
	request := &r_kvstore20150101.DescribeSecurityGroupConfigurationRequest{
		InstanceId: tea.String(instanceID),
	}
	res := &r_kvstore20150101.DescribeSecurityGroupConfigurationResponse{}
	err := common.Retry(1*time.Minute, func() *common.RetryError {
		err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeSecurityGroupConfiguration", c.accountID, []interface{}{request}, res)
		if err != nil {
			if strings.Contains(err.Error(), "Code: Throttling.User") {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// DescribeSecurityIps 获取kv-store实例白名单ip
func (c AliKvStoreClient) DescribeSecurityIps(ctx context.Context, instanceID string) (*r_kvstore20150101.DescribeSecurityIpsResponseBody, error) {
	request := &r_kvstore20150101.DescribeSecurityIpsRequest{
		InstanceId: tea.String(instanceID),
	}
	res := &r_kvstore20150101.DescribeSecurityIpsResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeSecurityIps", c.accountID, []interface{}{request}, res)
	if err != nil {
		return nil, err
	}
	return res.Body, nil
}

// DescribeAccounts 获取实例下账户信息
func (c AliKvStoreClient) DescribeAccounts(ctx context.Context, instanceID string) (*r_kvstore20150101.DescribeAccountsResponseBodyAccounts, error) {
	request := &r_kvstore20150101.DescribeAccountsRequest{
		InstanceId: tea.String(instanceID),
	}

	var account *r_kvstore20150101.DescribeAccountsResponseBodyAccounts
	err := retry(func() error {
		resp := &r_kvstore20150101.DescribeAccountsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeAccounts", c.accountID, []interface{}{request}, resp)
		if err != nil {
			return err
		}
		account = resp.Body.Accounts
		return nil
	})

	return account, err
}

// CreateBackup 为Redis实例创建数据备份
func (c AliKvStoreClient) CreateBackup(ctx context.Context, instanceID string, isLongTerm bool) (*r_kvstore20150101.CreateBackupResponseBody, error) {
	request := &r_kvstore20150101.CreateBackupRequest{
		InstanceId: tea.String(instanceID),
	}
	if isLongTerm {
		request.BackupRetentionPeriod = tea.Int64(-1) // 长期保留
	}

	resp := &r_kvstore20150101.CreateBackupResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "redis", "CreateBackup", c.accountID, []interface{}{request}, resp)
	if err != nil {
		return nil, err
	}

	return resp.Body, err
}

// DescribeBackupTasks 查询Redis实例的备份任务执行情况
func (c AliKvStoreClient) DescribeBackupTasks(ctx context.Context, timeout time.Duration, instanceID string, jobID string) (*r_kvstore20150101.DescribeBackupTasksResponseBody, error) {
	request := &r_kvstore20150101.DescribeBackupTasksRequest{
		InstanceId:  tea.String(instanceID),
		BackupJobId: tea.String(jobID),
		JobMode:     tea.String("Manual"), // 只查询手动备份结果
	}

	wait := common.IncrementalWait(5*time.Second, 1*time.Second)
	var body *r_kvstore20150101.DescribeBackupTasksResponseBody
	err := common.Retry(timeout, func() *common.RetryError {
		res := &r_kvstore20150101.DescribeBackupTasksResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeBackupTasks", c.accountID, []interface{}{request}, res)
		if err != nil {
			// 遇到查询限流时重试
			if IsThrottling(err) {
				wait()
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		body = res.Body
		return nil
	})
	if err != nil {
		return nil, err
	}

	return body, nil
}

// DescribeBackups 查看备份集列表,查询集群备份结果使用此方法(只能查询最近两天的结果)
func (c AliKvStoreClient) DescribeBackups(ctx context.Context, timeout time.Duration, instanceID string, backupJobID int64) (*r_kvstore20150101.DescribeBackupsResponseBody, error) {
	nowTime := time.Now()
	startTime := nowTime.Add(-24 * time.Hour * 2).UTC().Format("2006-01-02T15:04Z")
	endTime := nowTime.Add(30 * time.Minute).UTC().Format("2006-01-02T15:04Z")

	request := &r_kvstore20150101.DescribeBackupsRequest{
		InstanceId:  tea.String(instanceID),
		BackupJobId: tea.Int64(backupJobID),
		PageSize:    tea.Int32(300), // 目前分片数都小于300，所以暂时不做分页处理
		PageNumber:  tea.Int32(1),
		StartTime:   tea.String(startTime),
		EndTime:     tea.String(endTime),
	}

	var body *r_kvstore20150101.DescribeBackupsResponseBody
	// 避免限流
	err := common.Retry(timeout, func() *common.RetryError {
		res := &r_kvstore20150101.DescribeBackupsResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "redis", "DescribeBackups", c.accountID, []interface{}{request}, res)
		if err != nil {
			// 遇到查询限流或网络中断时重试
			if IsThrottling(err) || IsRequestEOF(err) {
				return common.RetryableError(err)
			}
			return common.NonRetryableError(err)
		}
		body = res.Body
		return nil
	})

	if err != nil {
		return nil, err
	}

	return body, nil
}

// TagRedis ...
func (c AliKvStoreClient) TagRedis(ctx context.Context, instanceIDs []string, tag []*r_kvstore20150101.TagResourcesRequestTag) error {
	req := &r_kvstore20150101.TagResourcesRequest{}
	req.SetResourceId(tea.StringSlice(instanceIDs))
	req.SetRegionId(c.regionID)
	req.SetResourceType("instance")
	req.SetTag(tag)
	res := &r_kvstore20150101.TagResourcesResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "redis", "TagResources", c.accountID, []interface{}{&req}, res)
}

// ModifyResourceGroup ...
func (c AliKvStoreClient) ModifyResourceGroup(ctx context.Context, instanceID, resourceGroupID string) error {
	req := &r_kvstore20150101.ModifyResourceGroupRequest{}
	req.SetInstanceId(instanceID)
	req.SetRegionId(c.regionID)
	req.SetResourceGroupId(resourceGroupID)
	res := &r_kvstore20150101.ModifyResourceGroupResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "redis", "ModifyResourceGroup", c.accountID, []interface{}{&req}, res)
}

// RestartInstance ...
func (c AliKvStoreClient) RestartInstance(ctx context.Context, instanceID string) error {
	req := &r_kvstore20150101.RestartInstanceRequest{}
	req.SetInstanceId(instanceID)
	req.SetUpgradeMinorVersion(false)
	res := &r_kvstore20150101.RestartInstanceResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "redis", "RestartInstance", c.accountID, []interface{}{&req}, res)
}

// CreateAccount ...
// Privilege: RoleReadOnly, RoleReadWrite
func (c AliKvStoreClient) CreateAccount(ctx context.Context, instanceID string, accountName, privilege, password string) error {
	req := &r_kvstore20150101.CreateAccountRequest{}
	req.SetInstanceId(instanceID)
	req.SetAccountName(accountName)
	req.SetAccountPrivilege(privilege)
	req.SetAccountPassword(password)
	req.SetAccountDescription("由云管平台创建")
	req.SetAccountType("Normal")
	res := &r_kvstore20150101.CreateAccountResponse{}
	return agentsdk.SyncCall(ctx, c.regionID, "redis", "CreateAccount", c.accountID, []interface{}{&req}, res)
}

var backupTimeMap = map[string]string{
	"cn-shanghai":    "20:00Z-21:00Z",
	"ap-northeast-1": "20:00Z-21:00Z",
	"ap-southeast-1": "20:00Z-21:00Z",
	"eu-central-1":   "03:00Z-04:00Z",
	"us-east-1":      "09:00Z-10:00Z",
}

func (c AliKvStoreClient) ModifyBackupPolicy(ctx context.Context, orderID, instanceID, instanceName, reportStore string) (string, error) {
	backupTime, exists := backupTimeMap[c.regionID]
	if !exists {
		// 默认备份时间为04:00-05:00
		backupTime = "20:00Z-21:00Z"
	}

	backupPeriod := "Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday"
	mReq := r_kvstore20150101.ModifyBackupPolicyRequest{}
	mReq.SetInstanceId(instanceID)
	mReq.SetPreferredBackupTime(backupTime)
	mReq.SetPreferredBackupPeriod(backupPeriod)
	resp := &r_kvstore20150101.ModifyBackupPolicyResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "redis", "ModifyBackupPolicy", c.accountID, []interface{}{&mReq}, resp)
	if err != nil {
		c.logger.Errorf("实例:%s修改自动备份策略失败, err: %v", instanceID, err)
		return reportStore, err
	}
	reportStore = reportSuccess(ctx, instanceName, orderID, reportStore, "modify_backup_policy")
	c.logger.Infof("实例:%s修改自动备份策略成功, 备份时间: %s", instanceID, backupTime)
	return reportStore, nil
}
