package alicloud

import (
	"github.com/alibabacloud-go/tea/tea"
	vpc******** "github.com/alibabacloud-go/vpc-********/v5/client"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
)

// AliVpcClient 阿里云vpc-client
type AliVpcClient struct {
	regionID  string
	accountID string
}

// NewAliVpcClient ecs.NewClientWithAccessKey
func NewAliVpcClient(regionID, accountID string) (*AliVpcClient, error) {
	return &AliVpcClient{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

// DescribeEipAddresses ...
func (c *AliVpcClient) DescribeEipAddresses(ctx context.Context) ([]*vpc********.DescribeEipAddressesResponseBodyEipAddressesEipAddress, error) {
	totalCount := 0
	maxLoop := 100
	req := &vpc********.DescribeEipAddressesRequest{
		RegionId: tea.String(c.regionID),
		PageSize: tea.Int32(100),
	}

	eips := make([]*vpc********.DescribeEipAddressesResponseBodyEipAddressesEipAddress, 0)
	for i := 0; i <= maxLoop; i++ {
		res := &vpc********.DescribeEipAddressesResponse{}
		err := agentsdk.SyncCall(ctx, c.regionID, "vpc", "DescribeEipAddresses", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		totalCount = int(*res.Body.TotalCount)
		req.SetPageNumber(int32(i + 1))
		eips = append(eips, res.Body.EipAddresses.EipAddress...)
		if totalCount <= len(eips) {
			break
		}
	}

	return eips, nil
}

// DescribeEipSegments ...
func (c *AliVpcClient) DescribeEipSegment(ctx context.Context) (*vpc********.DescribeEipSegmentResponseBody, error) {
	req := &vpc********.DescribeEipSegmentRequest{
		RegionId: tea.String(c.regionID),
	}
	req.SetPageSize(50)

	res := &vpc********.DescribeEipSegmentResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "vpc", "DescribeEipSegment", c.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	return res.Body, err
}

// GetAvailEipCountBySegID ...
func (c *AliVpcClient) GetAvailEipCountBySegID(ctx context.Context, segID string) (int32, error) {
	req := &vpc********.DescribeEipAddressesRequest{}
	req.SetRegionId(c.regionID)
	req.SetSegmentInstanceId(segID)
	req.SetStatus("Available")
	res := &vpc********.DescribeEipAddressesResponse{}
	err := agentsdk.SyncCall(ctx, c.regionID, "vpc", "DescribeEipAddresses", c.accountID, []interface{}{req}, res)
	if err != nil {
		return 0, err
	}

	return tea.Int32Value(res.Body.TotalCount), err
}
