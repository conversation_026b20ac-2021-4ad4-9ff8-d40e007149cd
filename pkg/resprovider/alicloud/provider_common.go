package alicloud

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

type taskRes struct {
	err          error
	instanceName string
}

// WaitGroup 一个异步结构体
type WaitGroup struct {
	workChan chan int
	wg       sync.WaitGroup
}

// NewPool 生成一个工作池, 并发数限制
func NewPool(concurrency int) *WaitGroup {
	ch := make(chan int, concurrency)
	return &WaitGroup{
		workChan: ch,
		wg:       sync.WaitGroup{},
	}
}

// Add 添加
func (w *WaitGroup) Add(num int) {
	for i := 0; i < num; i++ {
		w.workChan <- i
		w.wg.Add(1)
	}
}

// Done 完结
func (w *WaitGroup) Done() {
	<-w.workChan
	w.wg.Done()
}

// Wait 等待
func (w *WaitGroup) Wait() {
	w.wg.Wait()
}

func runTaskConcurrently(ctx context.Context, funcs map[string]func(ctx context.Context) error, orderID, reportStore, jobName string, concurrency int) (string, error) {
	resChan := make(chan taskRes, len(funcs))

	wg := NewPool(concurrency)
	for instanceName, f := range funcs {
		f := f
		instanceName := instanceName
		wg.Add(1)
		go func() {
			defer func() {
				wg.Done()
				if r := recover(); r != nil {
					fmt.Println("recovered", r)
				}
			}()
			err := f(ctx)
			resChan <- taskRes{
				err:          err,
				instanceName: instanceName,
			}
		}()
	}

	go func() {
		wg.Wait()
		close(resChan)
	}()

	errMessages := make([]string, 0)
	for res := range resChan {
		if res.err != nil {
			reportStore = reportError(ctx, res.instanceName, orderID, reportStore, jobName)
			errMessages = append(errMessages, fmt.Sprintf("实例%s执行任务%s失败,错误信息: %s", res.instanceName, jobName, res.err.Error()))
			continue
		}
		reportStore = reportSuccess(ctx, res.instanceName, orderID, reportStore, jobName)
	}

	if len(errMessages) != 0 {
		return "", fmt.Errorf(strings.Join(errMessages, ","))
	}

	return reportStore, nil
}
func reportError(ctx context.Context, instanceName, orderID, reportStore, jobName string) string {
	return common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.jobs.%s.last_time", instanceName, jobName): time.Now().Unix(),
		common.ReportSprintf("%s.jobs.%s.status", instanceName, jobName):    "failed",
		common.ReportSprintf("%s.jobs.%s.is_err", instanceName, jobName):    true,
		common.ReportSprintf("%s.is_err", instanceName):                     true,
	})
}

func reportSuccess(ctx context.Context, instanceName, orderID, reportStore, jobName string) string {
	return common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.jobs.%s.last_time", instanceName, jobName):  time.Now().Unix(),
		common.ReportSprintf("%s.jobs.%s.status", instanceName, jobName):     "success",
		common.ReportSprintf("%s.jobs.%s.is_success", instanceName, jobName): true,
	})
}
