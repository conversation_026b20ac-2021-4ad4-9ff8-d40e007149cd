package alicloud

import (
	"fmt"
	"math/rand"
	"sync"
	"testing"
	"time"

	"github.com/alibabacloud-go/ecs-20140526/v2/client"
	polardb20170801 "github.com/alibabacloud-go/polardb-20170801/v6/client"
	"github.com/alibabacloud-go/tea/tea"
	vpc20160428 "github.com/alibabacloud-go/vpc-20160428/v6/client"
	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

func TestGenInstanceName(t *testing.T) {
	ctx := context.Background()

	// res, err := cloudutils.GetCloudutils(models.HostResourceModel).Model.FindExistsInstancename(context.Background(), []string{"nap-sf-xieyi-cn-0002"})
	// t.Errorf("instanceNames: %+v, err: %v", res, err)

	instanceNames, err := cloudutils.GetCloudutils(models.MysqlClusterResourceModel).GenInstanceName(ctx, "nap-dev-devops-cn-dragonfly[1,4]", 1, true, cloudutils.Linux)
	t.Errorf("instanceNames: %+v, err: %v", instanceNames, err)

}

func TestAllocateEIP(t *testing.T) {

	client := &AliEcsClient{
		regionID:  "cn-shanghai",
		accountID: "6540eb306e463ff70c0ba44a",
	}
	res, err := client.AllocateEIP(context.Background())
	t.Errorf("resp: %+v, err: %v", res, err)
}

func TestAssociateEIP(t *testing.T) {

	client := &AliEcsClient{
		regionID:  "cn-shanghai",
		accountID: "6540eb306e463ff70c0ba44a",
	}
	err := client.AssociateEIP(context.Background(), "eip-uf69l7jsswghhlydkitl9", "i-uf6hla5dwau8o7ffctu4", "EcsInstance")
	t.Errorf("err: %v", err)
}

func TestSetEipName(t *testing.T) {

	client := &AliEcsClient{
		regionID:  "cn-shanghai",
		accountID: "6540eb306e463ff70c0ba44a",
	}
	err := client.SetEIPName(context.Background(), "eip-uf69l7jsswghhlydkitl9", "nap-sf-xieyi-cn-0002")
	t.Errorf("err: %v", err)
}

func TestSetEipTag(t *testing.T) {

	client := &AliEcsClient{
		regionID:  "cn-shanghai",
		accountID: "6540eb306e463ff70c0ba44a",
	}
	key := "nap"
	value := "cn"
	err := client.SetResourceTag(context.Background(), "eip-uf69l7jsswghhlydkitl9", "EIP", []*vpc20160428.TagResourcesRequestTag{
		{
			Key:   &key,
			Value: &value,
		},
	})
	t.Errorf("err: %v", err)
}

func TestReleaseEIP(t *testing.T) {

	client := &AliEcsClient{
		regionID:  "cn-shanghai",
		accountID: "6540eb306e463ff70c0ba44a",
	}
	err := client.ReleaseEipAddress(context.Background(), "eip-uf6pimuk8g1dkp5qliejc")
	t.Errorf("err: %v", err)
}

func TestDBAfterCreation(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "65572aed9bb2a1793df282a6",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "65572aed9bb2a1793df282a6",
	}
	instanceMap := map[string]string{
		"pc-uf6d646md45877lts": "nap-sf-xieyi-cn-test0003",
	}
	ctx := context.Background()
	orderID := "6554610d6b028549172675bd"
	req := CreatePolarDBInstanceInput{
		AccountList: []*DBAccount{},
		SecurityGroupIds: []string{
			"sg-uf6c4nj55o8p947mbxfx", "sg-uf6f3b4toi2xdqib5ey5", "sg-uf65rbgo6hezme5c71ex",
		},
		GlobalSecurityIPTempalteIds: []string{
			"g-3chx9kselfxipmonkjvw",
			"g-yutfu1hsmjbrkk0cp8iw",
			"g-kd2iop4aur9qwxnvhu6r",
			"g-cr7pevzxuhvedysz8w6s",
		},
	}
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	err := a.afterCreateAction(ctx, 6*time.Minute, orderID, req, instanceMap, reportStore)
	if err != nil {
		t.Errorf("error is %v", err)
	}
}

func TestDBModifyEndpoint(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "6540eb306e463ff70c0ba44a",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "6540eb306e463ff70c0ba44a",
	}
	ctx := context.Background()
	mInput := ModifyDBEndpointAddressInput{}
	clusterID := "pc-uf68tz882a1gqd38t"
	endpointID := "pe-uf6bwuiw5ilrzryrc"
	mInput.SetDBEndpointId(endpointID)
	mInput.SetDBClusterId(clusterID)
	mInput.SetNetType("Private")
	mInput.SetConnectionStringPrefix(fmt.Sprintf("%s-ro", "nap-dev-xieyi-cn-01633"))
	err := a.client.ModifyDBEndpointAddress(ctx, mInput, 5*time.Minute)
	if err != nil {
		a.logger.Errorf("实例:%s修改集群只读地址失败, 请联系管理员手工处理, err: %v", clusterID, err)
		return
	}
	a.logger.Infof("实例:%s修改集群只读地址成功", clusterID)
}

func TestDBRealeaseEndpoint(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "6540eb306e463ff70c0ba44a",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "6540eb306e463ff70c0ba44a",
	}
	ctx := context.Background()
	req := polardb20170801.DeleteDBClusterEndpointRequest{}
	clusterID := "pc-uf68tz882a1gqd38t"
	endpointID := "pe-uf6qf67h0fuxi130n"
	req.SetDBClusterId(clusterID)
	req.SetDBEndpointId(endpointID)
	res := &polardb20170801.DeleteDBClusterEndpointResponse{}
	err := agentsdk.SyncCall(ctx, a.client.regionID, "mysql", "DeleteDBClusterEndpoint", a.client.accountID, []interface{}{&req}, res)

	t.Errorf("error is %v, res: %+v", err, res)
}

func TestReportStore(t *testing.T) {

	orderID := "65549da075c9944f694b0d44"
	instanceName := "nap-sf-xieyi-cn-aa0012"
	ctx := context.Background()
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", instanceName):  time.Now().Unix(),
		common.ReportSprintf("%s.status", instanceName):     "success",
		common.ReportSprintf("%s.is_success", instanceName): true,
	})
}

func TestTransformDBClusterPayType(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "6540eb306e463ff70c0ba44a",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "6540eb306e463ff70c0ba44a",
	}
	ctx := context.Background()
	req := polardb20170801.TransformDBClusterPayTypeRequest{}
	clusterID := "pc-uf6r9q82m57x26rr4"

	req.SetDBClusterId(clusterID)
	req.SetPayType("Postpaid")
	req.SetRegionId(a.client.regionID)
	res := &polardb20170801.TransformDBClusterPayTypeResponse{}
	err := agentsdk.SyncCall(ctx, a.client.regionID, "mysql", "TransformDBClusterPayType", a.client.accountID, []interface{}{&req}, res)

	t.Errorf("error is %v, res: %+v", err, res)
}

func TestRedisAfterCreation(t *testing.T) {

	c := AliKvStoreClient{
		regionID:  "cn-shanghai",
		dryRun:    false,
		logger:    logrus.New(),
		accountID: "6540eb306e463ff70c0ba44a",
	}

	ctx := context.Background()
	orderID := "655495e05dceaa62a79c2782"
	instanceID := "r-uf6058dfb79b71e4"
	instanceName := "nap-sf-xieyi-cn-all0221"
	reportStore, _ := common.GetOrderStatusDetail(ctx, orderID)
	var err error
	// 1. 绑定安全组
	reportStore, err = c.BindSecurityGroup(ctx, orderID, instanceID, instanceName, reportStore, []string{
		"sg-uf6g9ph64f4u49gn7bgs", "sg-uf6f3b4toi2xdqib5ey5", "sg-uf60z4ozhtzc9t2lc3nl", "sg-uf65rbgo6hezme5c71ex",
	})
	if err != nil {
		t.Errorf("err: %v", err)
	}

	// 2. 禁用命令
	_, err = c.DisableCommands(ctx, orderID, instanceID, instanceName, reportStore)
	if err != nil {
		t.Errorf("err: %v", err)

	}
}

func TestDescribeIPWhiteList(t *testing.T) {

	a := &AliPolardb{
		client: &AliPolarDBClient{
			regionID:  "cn-shanghai",
			accountID: "6540eb306e463ff70c0ba44a",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "6540eb306e463ff70c0ba44a",
	}
	ctx := context.Background()
	req := polardb20170801.DescribeGlobalSecurityIPGroupRequest{}

	req.SetRegionId(a.client.regionID)
	res := &polardb20170801.DescribeGlobalSecurityIPGroupResponse{}
	err := agentsdk.SyncCall(ctx, a.client.regionID, "mysql", "DescribeGlobalSecurityIPGroup", a.client.accountID, []interface{}{&req}, res)

	output := []string{}
	for _, o := range res.Body.GlobalSecurityIPGroup {
		output = append(output, *o.GlobalIgName, *o.GlobalSecurityGroupId)
	}

	t.Errorf("error is %v, res: %+v, output: %+v", err, res, output)
}

func TestGetAvailableEip(t *testing.T) {

	aliEcs := &AliEcs{
		client: &AliEcsClient{
			regionID:  "cn-shanghai",
			accountID: "65572aed9bb2a1793df282a6",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "65572aed9bb2a1793df282a6",
	}

	eip, ip, err := aliEcs.getAvailEip(context.Background(), []string{"eipsg-uf6bv7o7ei27dlo7h9251", "eipsg-uf6amvpw4k9q2fwqj7eb4", "eipsg-uf6g5uiznunzg6kkm25qf", "eipsg-uf64schfl6biu5uns4c7e"})
	fmt.Println(eip, ip, err)
}

func TestCreateInBatches(t *testing.T) {

	aliEcs := &AliEcs{
		client: &AliEcsClient{
			regionID:  "cn-shanghai",
			accountID: "65572aed9bb2a1793df282a6",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "65572aed9bb2a1793df282a6",
	}

	amount := 350
	req := &client.RunInstancesRequest{Amount: tea.Int32(int32(amount)), DryRun: tea.Bool(true)}
	hostnames := []string{}
	for i := 1; i <= amount; i++ {
		hostnames = append(hostnames, fmt.Sprintf("nap-test-123-456-%.4d", i))
	}
	req.SetHostNames(tea.StringSlice(hostnames))

	fmt.Println(aliEcs.createInstancesInBatch(context.Background(), 10*time.Minute, req, ""))
}

func TestRunConcurrently(t *testing.T) {

	ctx := context.Background()
	funcs1 := make(map[string]func(ctx context.Context) error)
	funcs2 := make(map[string]func(ctx context.Context) error)

	var testF = func(ctx context.Context, instanceName, reportStore, jobName string) error {
		fmt.Println(instanceName + ": step 1 finish")
		fmt.Println(instanceName + ": step 2 finish")
		if rand.Intn(100) < 10 {
			return fmt.Errorf("随机错误")
		}
		return nil
	}
	instanceNameList := []string{}
	for i := 0; i <= 100; i++ {
		instanceNameList = append(instanceNameList, fmt.Sprintf("test-name-%.4d", i))
	}

	data := &entity.ResOrder{
		Status:     1,
		IspID:      "test",
		IspName:    "test",
		IspType:    "test",
		RegionID:   "test",
		RegionName: "test",
		Type:       "test",
		Action:     "test",
		RawData:    "{}",
		Reason:     "",
	}
	orderObjID, err := models.ResourceOrder.Create(ctx, data)
	if err != nil {
		t.Fatal(err)
	}
	orderID := orderObjID.Hex()
	reportStore := "{}"

	for _, instanceName := range instanceNameList {
		instanceName := instanceName
		funcs1[instanceName] = func(ctx context.Context) error {
			return testF(ctx, instanceName, reportStore, "ut-task1")
		}
	}
	reportStore, err = runTaskConcurrently(ctx, funcs1, orderID, reportStore, "ut-task1", 20)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(reportStore, err)

	for _, instanceName := range instanceNameList {
		instanceName := instanceName
		funcs2[instanceName] = func(ctx context.Context) error {
			return testF(ctx, instanceName, reportStore, "ut-task2")
		}
	}
	reportStore, err = runTaskConcurrently(ctx, funcs2, orderID, reportStore, "ut-task2", 20)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(reportStore, err)
}

func TestGetAvailEip(t *testing.T) {

	ctx := context.Background()
	aliEcs := &AliEcs{
		client: &AliEcsClient{
			regionID:  "cn-shanghai",
			accountID: "65572aed9bb2a1793df282a6",
			// accountID: "65a0ae9ebf0b894146fc4983",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "65572aed9bb2a1793df282a6",
		// accountID: "65a0ae9ebf0b894146fc4983",
	}
	var wg sync.WaitGroup
	type res struct {
		err error
		i   int
		eip string
	}

	results := make(chan res)
	for i := 1; i <= 100; i++ {
		wg.Add(1)
		i := i
		go func() {
			defer wg.Done()
			eip, _, err := aliEcs.getAvailEip(ctx, []string{
				"eipsg-uf6xqtg4ydx8utqfwpj1z",
			})
			if err != nil {
				results <- res{
					err: err,
					i:   i,
				}
				return
			}
			results <- res{
				err: nil,
				i:   i,
				eip: eip,
			}
		}()
	}

	go func() {
		wg.Wait()
		close(results)
	}()

	errCount := 0
	eipCount := 0
	for e := range results {
		if e.err != nil {
			errCount += 1
		} else {
			eipCount += 1
		}
		fmt.Printf("res, i:%d, eip:%s, err:%v\n", e.i, e.eip, e.err)
	}
	fmt.Printf("errCount: %d, eipCount: %d", errCount, eipCount)
}

func TestBindEIP(t *testing.T) {
	aliEcs := &AliEcs{
		client: &AliEcsClient{
			regionID:  "cn-shanghai",
			accountID: "65572aed9bb2a1793df282a6",
			// accountID: "65a0ae9ebf0b894146fc4983",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "65572aed9bb2a1793df282a6",
		// accountID: "65a0ae9ebf0b894146fc4983",
	}

	ctx := context.Background()
	adminCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)

	instanceIDs := []string{}

	instances, err := models.HostResourceModel.GetByInstanceIDs(adminCtx, instanceIDs)
	if err != nil {
		t.Fatal(err)
	}
	bindEipFuncs := make(map[string]func(ctx context.Context) error)
	ipList := make([]string, 0, len(instances))
	for idx, instances := range instances {
		hostname := instances.InstanceName
		instanceID := instances.InstanceID
		bindEipFuncs[hostname] = func(ctx context.Context) error {
			return aliEcs.bindEIP(ctx, hostname, instanceID, nil, nil, nil, idx, ipList)
		}
	}
	data := &entity.ResOrder{
		Status:     1,
		IspID:      "test",
		IspName:    "test",
		IspType:    "test",
		RegionID:   "test",
		RegionName: "test",
		Type:       "test",
		Action:     "test",
		RawData:    "{}",
		Reason:     "",
	}
	orderObjID, err := models.ResourceOrder.Create(ctx, data)
	if err != nil {
		t.Fatal(err)
	}
	orderID := orderObjID.Hex()
	reportStore := "{}"
	_, err = runTaskConcurrently(ctx, bindEipFuncs, orderID, reportStore, "BindEIP", 50)
	if err != nil {
		t.Fatal(err)
	}
}
