package alicloud

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/sirupsen/logrus"
)

func TestDealIpv6(t *testing.T) {
	req := `
	{
  "InstanceChargeType": "PrePaid",
  "os_type": "linux",
  "ImageId": "m-uf6dnd8eq0gan3xtcxk7",
  "InstanceType": "ecs.c7.large",
  "Period": 1,
  "PeriodUnit": "Month",
  "AutoRenew": true,
  "AutoRenewPeriod": 1,
  "InstanceTypeMap": {},
  "LoginType": "PasswordInherit",
  "Password": "",
  "PasswordInherit": false,
  "KeyPairName": "",
  "SystemDisk": {
    "PerformanceLevel": "PL0",
    "Category": "cloud_essd",
    "Size": "50"
  },
  "DataDisk": [
    {
      "PerformanceLevel": "PL0",
      "Category": "cloud_essd",
      "Size": 50,
      "IOPS": 3000,
      "DeviceName": "",
      "Throughput": 125,
      "locked": true
    }
  ],
  "InternetChargeType": "PayByTraffic",
  "InternetMaxBandwidthOut": 0,
  "VpcId": "vpc-uf6ou04yl9ajdovvpeg28",
  "SecurityGroupIds": [
    "sg-uf6ezx26yhagj3cjjqec"
  ],
  "UserData": "",
  "InstanceName": "",
  "HostName": "nap-dev-xieyi-cn-test[0001,4]",
  "Tag": [],
  "Amount": 1,
  "eqHostName": false,
  "reason": "测试ipv6机器",
  "privatePool": {
    "value": "none",
    "id": ""
  },
  "serviceTreeNodes": [],
  "bindEIP": {
    "enable": false,
    "segIDs": []
  },
  "bindDDos": {
    "enable": false,
    "packageID": ""
  },
  "ipv6": {
    "enable": true,
    "public_enable": true,
    "public_paytype": "PayByTraffic",
    "public_bandwidth": 10
  },
  "ZoneMappings": [
    {
      "ZoneId": "cn-shanghai-m",
      "VSwitchId": "vsw-uf6jtvewm7lu18vmv4td0"
    },
    {
      "ZoneId": "cn-shanghai-n",
      "VSwitchId": "vsw-uf6x0ovni67cul6savurx"
    }
  ],
  "RegionId": "cn-shanghai",
  "RegionName": "华东2（上海）",
  "InstanceTypeDetail": {
    "value": "",
    "label": "",
    "MemorySize": 4,
    "CPUCoreCount": 2,
    "InstanceTypeFamily": "ecs.c7",
    "InstanceTypeID": "ecs.c7.large",
    "Status": "Available"
  },
  "InternetMaxdwidthOut": 0,
  "UniqueSuffix": true,
  "VSwitchId": "",
  "IoOptimized": "optimized"
}
	`

	ipv6Config := ipv6Config{}
	err := json.Unmarshal([]byte(req), &ipv6Config)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(ipv6Config)

	a := &AliEcs{
		client: &AliEcsClient{
			regionID:  "cn-shanghai",
			accountID: "6540c7f36591041c72706a75",
		},
		logger:    logrus.New(),
		dryRun:    false,
		accountID: "6540c7f36591041c72706a75",
	}
	ctx := context.Background()
	resp, err := a.client.DescribeInstances(ctx, 1, 1, "", "i-uf6b2vy0oud0ga01hbd7")
	if err != nil {
		t.Fatal(err)
	}
	detail := resp.Instances.Instance[0]
	err = a.dealIpv6(ctx, detail, &ipv6Config)
	if err != nil {
		t.Fatal(err)
	}
}
