package alicloud

import (
	"fmt"
	"strings"

	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/errors"
)

const (
	// NotFound not-found
	NotFound = "NotFound"
	// ResourceNotfound 资源未找到
	ResourceNotfound = "ResourceNotfound"
	// ServiceUnavailable 服务不可用
	ServiceUnavailable = "ServiceUnavailable"
	// InstanceNotFound 实例未找到
	InstanceNotFound = "Instance.Notfound"
	// MessageInstanceNotFound instance is not found
	MessageInstanceNotFound = "instance is not found"
	// Throttling 服务限流(接口级)
	Throttling = "Throttling"
	// ThrottlingUser 服务限流(用户级)
	ThrottlingUser = "Throttling.User"

	// RAMInstanceNotFound 因服务未授权导致的not found
	RAMInstanceNotFound = "Forbidden.InstanceNotFound"
	// AliyunGoClientFailure unknow
	AliyunGoClientFailure = "AliyunGoClientFailure"
	// LogClientTimeout 超时
	LogClientTimeout = "Client.Timeout exceeded while awaiting headers"
	// DryPass dry测试通过
	DryPass = "has been passed"

	// DB相关错误码
	// Endpoint当前状态不支持修改
	EndpointStatusNotSupport = "EndpointStatus.NotSupport"
	// 集群当前状态不支持修改
	ClusterStatusOperationDenied = "OperationDenied.DBClusterStatus"
	DuplicateConnectionStrOrPort = "InvalidConnectionStringOrPort.Duplicate"
)

// ComplexError 正则匹配错误
type ComplexError struct {
	Cause error
	Err   error
	Path  string
	Line  int
}

func (e ComplexError) Error() string {
	if e.Cause == nil {
		e.Cause = Error("<nil cause>")
	}
	if e.Err == nil {
		return fmt.Sprintf("[ERROR] %s:%d:\n%s", e.Path, e.Line, e.Cause.Error())
	}
	return fmt.Sprintf("[ERROR] %s:%d: %s:\n%s", e.Path, e.Line, e.Err.Error(), e.Cause.Error())
}

// Error fmt.error
func Error(msg string, args ...interface{}) error {
	return fmt.Errorf(msg, args...)
}

// NotFoundError 未发现错误
func NotFoundError(err error) bool {
	if err == nil {
		return false
	}
	if e, ok := err.(*ComplexError); ok {
		if e.Err != nil && strings.HasPrefix(e.Err.Error(), ResourceNotfound) {
			return true
		}
		return NotFoundError(e.Cause)
	}

	if e, ok := err.(*errors.ServerError); ok {
		return e.ErrorCode() == InstanceNotFound || e.ErrorCode() == RAMInstanceNotFound || e.ErrorCode() == NotFound || strings.Contains(strings.ToLower(e.Message()), MessageInstanceNotFound)
	}

	if e, ok := err.(*ProviderError); ok {
		return e.ErrorCode() == InstanceNotFound || e.ErrorCode() == RAMInstanceNotFound || e.ErrorCode() == NotFound || strings.Contains(strings.ToLower(e.Message()), MessageInstanceNotFound)
	}

	if e, ok := err.(*RequestError); ok {
		return e.Code == InstanceNotFound || e.Code == RAMInstanceNotFound || e.Code == NotFound || strings.Contains(strings.ToLower(e.Message), MessageInstanceNotFound)
	}

	return false
}

// ProviderError provider error
type ProviderError struct {
	errorCode string
	message   string
}

func (e *ProviderError) Error() string {
	return fmt.Sprintf("[ERROR] Terraform Alicloud Provider Error: Code: %s Message: %s", e.errorCode, e.message)
}

// ErrorCode error code
func (e *ProviderError) ErrorCode() string {
	return e.errorCode
}

// Message message
func (e *ProviderError) Message() string {
	return e.message
}

// IsThrottling 是否为限流
func IsThrottling(err error) bool {
	if err == nil {
		return false
	}

	if e, ok := err.(*errors.ServerError); ok {
		return e.ErrorCode() == Throttling
	}

	if e, ok := err.(*RequestError); ok {
		return e.Code == Throttling
	}

	if strings.Contains(err.Error(), "Code: Throttling.User") {
		return true
	}

	return false
}

// IsRequestEOF 是否为请求中断
func IsRequestEOF(err error) bool {
	if err == nil {
		return false
	}

	return strings.HasSuffix(err.Error(), "EOF")
}

// IsDryError 过滤dryPass-error错误，dry不过是否通过都会报错
func IsDryError(err error) bool {
	if err == nil {
		return false
	}

	return strings.Contains(err.Error(), DryPass)
}
