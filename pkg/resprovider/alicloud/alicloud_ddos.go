package alicloud

import (
	"encoding/json"
	"fmt"

	ddosbgp******** "github.com/alibabacloud-go/ddosbgp-********/v3/client"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
)

// AliDdosBGPClient 阿里云bgp-client
type AliDdosBGPClient struct {
	regionID  string
	accountID string
}

// CreateAliDdosBGPClient create ali client
func CreateAliDdosBGPClient(accountID string) (client *AliDdosBGPClient, err error) {
	// 中国内地的高防使用杭州的endpoint, 海外高防不使用aliyun
	return &AliDdosBGPClient{
		regionID:  "cn-hangzhou",
		accountID: accountID,
	}, nil
}

func (a *AliDdosBGPClient) GetAliyunBGPPackageList(ctx context.Context) ([]*ddosbgp********.DescribeInstanceListResponseBodyInstanceList, error) {
	req := &ddosbgp********.DescribeInstanceListRequest{}
	req.SetPageSize(10)
	req.SetRegionId(a.regionID)
	totalCount := 0
	maxloop := 20
	resList := make([]*ddosbgp********.DescribeInstanceListResponseBodyInstanceList, 0)
	for i := 0; i < maxloop; i++ {
		req.SetPageNo(int32(i + 1))
		res := &ddosbgp********.DescribeInstanceListResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "ddosbgp", "DescribeInstanceList", a.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		resList = append(resList, res.Body.InstanceList...)
		if totalCount <= len(resList) {
			break
		}
	}
	return resList, nil
}

func (a *AliDdosBGPClient) GetAliyunBGPPackageSpec(ctx context.Context, packageId string) (*ddosbgp********.DescribeInstanceSpecsResponseBodyInstanceSpecs, error) {
	req := &ddosbgp********.DescribeInstanceSpecsRequest{}
	req.SetRegionId(a.regionID)
	instanceIdList, err := json.Marshal([]string{packageId})
	if err != nil {
		return nil, err
	}
	req.SetInstanceIdList(string(instanceIdList))
	res := &ddosbgp********.DescribeInstanceSpecsResponse{}
	err = agentsdk.SyncCall(ctx, a.regionID, "ddosbgp", "DescribeInstanceSpecs", a.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}
	if res.Body == nil || len(res.Body.InstanceSpecs) != 1 {
		return nil, fmt.Errorf("get ddos bgp spec failed")
	}

	return res.Body.InstanceSpecs[0], nil
}

func (a *AliDdosBGPClient) AddIp(ctx context.Context, packageId string, ipList []string) error {
	req := &ddosbgp********.AddIpRequest{}
	req.SetInstanceId(packageId)
	req.SetRegionId(a.regionID)

	step := 10
	for i := 0; i < len(ipList); i += step {
		ipMap := []map[string]string{}
		// 根据阿里云说法:10个一组绑定会稳定一些
		for _, ip := range ipList[i:min(len(ipList), i+10)] {
			ipMap = append(ipMap, map[string]string{
				"ip": ip,
			})
		}
		ipJson, err := json.Marshal(ipMap)
		if err != nil {
			return err
		}
		req.SetIpList(string(ipJson))
		res := &ddosbgp********.AddIpResponse{}
		err = agentsdk.SyncCall(ctx, a.regionID, "ddosbgp", "AddIp", a.accountID, []interface{}{req}, res)
		if err != nil {
			return err
		}
	}

	return nil
}
