package alicloud

import "fmt"

// Response response
type Response struct {
	RequestID string
}

// ErrorResponse response error
type ErrorResponse struct {
	Response
	HostID  string
	Code    string
	Message string
}

// RequestError An Error represents a custom error for Aliyun API failure response
type RequestError struct {
	ErrorResponse
	StatusCode int // Status Code of HTTP Response
}

func (e *RequestError) Error() string {
	return fmt.Sprintf("Aliyun API Error: RequestID: %s Status Code: %d Code: %s Message: %s", e.RequestID, e.StatusCode, e.Code, e.Message)
}

// Pagination page
type Pagination struct {
	PageNumber int
	PageSize   int
}

// SetPageSize set size
func (p *Pagination) SetPageSize(size int) {
	p.PageSize = size
}

// Validate valid
func (p *Pagination) Validate() {
	if p.PageNumber < 0 {
		p.PageNumber = 1
	}
	if p.PageSize < 0 {
		p.PageSize = 10
	} else if p.PageSize > 50 {
		p.PageSize = 50
	}
}

// PaginationResult A PaginationResponse represents a response with pagination information
type PaginationResult struct {
	TotalCount int
	PageNumber int
	PageSize   int
}

// NextPage gets the next page of the result set
func (r *PaginationResult) NextPage() *Pagination {
	if r.PageNumber*r.PageSize >= r.TotalCount {
		return nil
	}
	return &Pagination{PageNumber: r.PageNumber + 1, PageSize: r.PageSize}
}
