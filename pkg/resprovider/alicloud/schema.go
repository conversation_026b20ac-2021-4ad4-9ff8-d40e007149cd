package alicloud

import (
	polardb******** "github.com/alibabacloud-go/polardb-********/v6/client"
	r_kvstore******** "github.com/alibabacloud-go/r-kvstore-********/v7/client"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// CreatePolarDBInstanceInput 创建polarDB参数
type CreatePolarDBInstanceInput struct {
	*polardb********.CreateDBClusterRequest
	Amount                      uint         `json:"Amount" validator:"required"`
	AccountList                 []*DBAccount `json:"Accounts"`
	SecurityGroupIds            []string     `json:"SecurityGroupIds"`
	GlobalSecurityIPTempalteIds []string     `json:"GlobalSecurityIPTempalteIds"`
}

type DBAccount struct {
	AccountName     string `json:"AccountName" validator:"required"`
	AccountPassword string `json:"AccountPassword" validator:"required"`
	AccountType     string `json:"AccountType" validator:"required"`
}

// CreatePolarDBAccountInput 创建数据库账号
type CreatePolarDBAccountInput struct {
	polardb********.CreateAccountRequest
}

// ModifyDBClusterAccessWhitelistInput 创建或修改集群的白名单（包括IP白名单和安全组）
type ModifyDBClusterAccessWhitelistInput struct {
	polardb********.ModifyDBClusterAccessWhitelistRequest
}

type CreateDBClusterEndpointInput struct {
	polardb********.CreateDBClusterEndpointRequest
}

type ModifyDBEndpointAddressInput struct {
	polardb********.ModifyDBEndpointAddressRequest
}

type ModifyBackupPolicyInput struct {
	polardb********.ModifyBackupPolicyRequest
}

type RedisAccount struct {
	AccountName      string `json:"AccountName" validator:"required"`
	AccountPassword  string `json:"AccountPassword" validator:"required"`
	AccountPrivilege string `json:"AccountPrivilege" validator:"required"`
}

// CreateKvStoreInstanceInput 创建kvStore-form
type CreateKvStoreInstanceInput struct {
	*r_kvstore********.CreateInstanceRequest
	Amount           uint     `json:"Amount" validator:"required"`
	SecurityGroupIds []string `json:"SecurityGroupIds"`
	// 下面是tair专用参数
	TairEngineVersion *string         `json:"TairEngineVersion"`
	TairReadOnlyCount *int32          `json:"TairReadOnlyCount"`
	TairShardcount    *int32          `json:"TairShardcount"`
	TairInstanceClass *string         `json:"TairInstanceClass"`
	Accounts          []*RedisAccount `json:"Accounts"`
}

// ModifyGlobalSecurityIPGroupRelationInput 绑定IP白名单模版
type ModifyGlobalSecurityIPGroupRelationInput struct {
	polardb********.ModifyGlobalSecurityIPGroupRelationRequest
}

type CertificateOutput struct {
	Certificates []*Certificate `json:"CertIdentifier,omitempty"`
}

type Certificate struct {
	CertIdentifier string `json:"CertIdentifier"`
	CertName       string `json:"CertName"`
	CommonName     string `json:"CommonName"`
	Domain         string `json:"Domain"`
	AfterDate      int64  `json:"AfterDate"`
	BeforeDate     int64  `json:"BeforeDate"`
}

type ZoneMapping struct {
	ZoneId     string `json:"ZoneId"`
	VsSwitchId string `json:"VsSwitchId"`
}

type HTTPListener struct {
	ACLType string   `json:"ACLType"`
	ACLIDs  []string `json:"ACLIDs"`
}

type HTTPsListener struct {
	ACLType string   `json:"ACLType"`
	ACLIDs  []string `json:"ACLIDs"`
	Certs   []string `json:"Certs"`
}

// CreateLBInput 创建LB参数
type CreateLBInput struct {
	ZoneMappings        []ZoneMapping     `json:"ZoneMappings"`
	HTTPListener        HTTPListener      `json:"HTTPListener"`
	HTTPsListener       HTTPsListener     `json:"HTTPsListener"`
	AddressMode         string            `json:"AddressMode"`
	LoadBalancerEdition string            `json:"LoadBalancerEdition"`
	PayType             string            `json:"PayType"`
	Tag                 []*entity.ResTags `json:"Tag"`
	RegionId            string            `json:"RegionId"`
	VpcId               string            `json:"VpcId"`
	AddressType         string            `json:"AddressType"`
	LoadBalancerName    string            `json:"LoadBalancerName"`
	SecurityGroupIds    []string          `json:"SecurityGroupIds"`
	ResourceGroupId     string            `json:"ResourceGroupId"`
	AddressIpVersion    string            `json:"AddressIpVersion"`
}
