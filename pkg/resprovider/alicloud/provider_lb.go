package alicloud

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"slices"
	"strings"
	"time"

	alb******** "github.com/alibabacloud-go/alb-********/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/kr/pretty"
	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// AliLB 阿里云LB provider
type AliLB struct {
	client    *AliLoadBalancerClient
	logger    *logrus.Logger
	dryRun    bool
	accountID string
}

// ResourceKvStore 初始化阿里云lb-provide
func ResourceLb(regionID, accountID string, logger *logrus.Logger, dryRun bool) (*AliLB, error) {
	client, err := NewAliLoadBalancerClient(regionID, accountID)
	if err != nil {
		return nil, err
	}

	if logger == nil {
		return nil, errors.New("logger object is null")
	}

	return &AliLB{client: client, logger: logger, dryRun: dryRun, accountID: accountID}, err
}

// CreateInstance 创建实例
func (a AliLB) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	var rdata CreateLBInput
	err = json.Unmarshal(data, &rdata)
	if err != nil {
		return nil, err
	}
	reportStore := ""
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
		common.ReportSprintf("%s.start_time", rdata.LoadBalancerName):    time.Now().Unix(),
		common.ReportSprintf("%s.status", rdata.LoadBalancerName):        "preparing",
		common.ReportSprintf("%s.instance_name", rdata.LoadBalancerName): rdata.LoadBalancerName,
	})
	lbName := rdata.LoadBalancerName
	ipVersion := "IPv4"
	if rdata.AddressIpVersion != "" {
		ipVersion = rdata.AddressIpVersion
	}
	createReq := &alb********.CreateLoadBalancerRequest{
		AddressAllocatedMode: &rdata.AddressMode,
		AddressIpVersion:     tea.String(ipVersion),
		AddressType:          &rdata.AddressType,
		DryRun:               &a.dryRun,
		LoadBalancerBillingConfig: &alb********.CreateLoadBalancerRequestLoadBalancerBillingConfig{
			PayType: &rdata.PayType,
		},
		LoadBalancerEdition: &rdata.LoadBalancerEdition,
		LoadBalancerName:    &rdata.LoadBalancerName,
		Tag: []*alb********.CreateLoadBalancerRequestTag{
			{
				Key:   tea.String("nap"),
				Value: tea.String(hostname_util.GetLbNameArea(lbName)),
			},
			{
				Key:   tea.String("env"),
				Value: tea.String(hostname_util.GetLbNameEnv(lbName)),
			},
			{
				Key:   tea.String("region"),
				Value: tea.String(hostname_util.GetLbNameRegion(lbName)),
			},
			{
				Key:   tea.String("module"),
				Value: tea.String(hostname_util.GetLbNameModule(lbName)),
			},
			{
				Key:   tea.String("region_module"),
				Value: tea.String(hostname_util.GetLbNameRegionModule(lbName)),
			},
			{
				Key:   tea.String("serial_number"),
				Value: tea.String(hostname_util.GetLbNameNum(lbName)),
			},
		},
		VpcId:           &rdata.VpcId,
		ResourceGroupId: tea.String(rdata.ResourceGroupId),
		ZoneMappings:    []*alb********.CreateLoadBalancerRequestZoneMappings{},
	}
	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	for _, t := range rdata.Tag {
		// 通用的标签不允许自定义
		if slices.Contains(systemTag, t.Key) {
			continue
		}
		createReq.Tag = append(createReq.Tag, &alb********.CreateLoadBalancerRequestTag{
			Key:   tea.String(t.Key),
			Value: tea.String(t.Value),
		})
	}
	for _, z := range rdata.ZoneMappings {
		createReq.ZoneMappings = append(createReq.ZoneMappings, &alb********.CreateLoadBalancerRequestZoneMappings{
			VSwitchId: tea.String(z.VsSwitchId),
			ZoneId:    tea.String(z.ZoneId),
		})
	}
	resp, err := a.client.CreateLB(ctx, createReq)
	if err != nil {
		// 跳过dryRun预检错误
		DryRunOperation := "Request validation has been passed with DryRun flag set."
		errMsg := err.Error()
		if strings.Contains(errMsg, DryRunOperation) && a.dryRun {
			return nil, nil
		}
		return nil, err
	}
	instanceID := tea.StringValue(resp.LoadBalancerId)
	a.logger.Infof("绑定标签为%# v", pretty.Formatter(rdata.Tag))

	// 调用sdk创建实例
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", rdata.LoadBalancerName):   time.Now().Unix(),
		common.ReportSprintf("%s.status", rdata.LoadBalancerName):      "started",
		common.ReportSprintf("%s.instance_id", rdata.LoadBalancerName): instanceID,
	})

	// 轮询实例状态直到ready
	a.logger.Infof("30秒一次判断本次创建的lb是否就绪，持续30min")
	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()
	reportStore, err = a.waitForReady(ctx, orderID, instanceID, rdata.LoadBalancerName, reportStore)
	if err != nil {
		return nil, err
	}

	// 创建默认机器组
	sgName, err := getSgName(rdata.LoadBalancerName)
	if err != nil {
		return nil, err
	}
	serverGroupId, err := a.client.CreateOrGetDefaultServerGroup(ctx, sgName, rdata.VpcId)
	if err != nil {
		reportError(ctx, rdata.LoadBalancerName, orderID, reportStore, "server_group")
		return nil, err
	}
	a.logger.Infof("创建默认机器组 %s 成功", sgName)
	reportStore = reportSuccess(ctx, rdata.LoadBalancerName, orderID, reportStore, "server_group")

	// 先添加安全组, 再创建监听，避免被扫描
	if len(rdata.SecurityGroupIds) > 0 {
		err = a.client.JoinSecurityGroup(ctx, instanceID, rdata.SecurityGroupIds)
		if err != nil {
			reportError(ctx, rdata.LoadBalancerName, orderID, reportStore, "security_groups")
			return nil, err
		}
		a.logger.Infof("加入安全组成功")
		err = a.client.WaitForReady(ctx, instanceID, 15*time.Minute)
		if err != nil {
			reportError(ctx, rdata.LoadBalancerName, orderID, reportStore, "security_groups")
			return nil, err
		}
		a.logger.Infof("加入安全组后等待状态就绪完成")
		reportStore = reportSuccess(ctx, rdata.LoadBalancerName, orderID, reportStore, "security_groups")
	}

	// 创建监听&&等待监听ready
	// http监听
	_, err = a.client.CreateListenerAndWaitReady(ctx, &alb********.CreateListenerRequest{
		DefaultActions: []*alb********.CreateListenerRequestDefaultActions{
			{
				ForwardGroupConfig: &alb********.CreateListenerRequestDefaultActionsForwardGroupConfig{
					ServerGroupTuples: []*alb********.CreateListenerRequestDefaultActionsForwardGroupConfigServerGroupTuples{
						{
							ServerGroupId: tea.String(serverGroupId),
						},
					},
				},
				Type: tea.String("ForwardGroup"),
			},
		},
		GzipEnabled:         tea.Bool(true),
		ListenerDescription: tea.String(getListenerDesc("HTTP", rdata.LoadBalancerName)),
		ListenerPort:        tea.Int32(80),
		ListenerProtocol:    tea.String("HTTP"),
		LoadBalancerId:      resp.LoadBalancerId,
		XForwardedForConfig: &alb********.CreateListenerRequestXForwardedForConfig{
			XForwardedForEnabled: tea.Bool(true),
		},
	}, instanceID)
	if err != nil {
		reportError(ctx, rdata.LoadBalancerName, orderID, reportStore, "http_listener")
		return nil, err
	}
	a.logger.Infof("创建http监听成功")
	reportStore = reportSuccess(ctx, rdata.LoadBalancerName, orderID, reportStore, "http_listener")

	certificates := make([]*alb********.CreateListenerRequestCertificates, 0)
	for _, c := range rdata.HTTPsListener.Certs {
		certificates = append(certificates, &alb********.CreateListenerRequestCertificates{
			CertificateId: tea.String(c),
		})
	}
	_, err = a.client.CreateListenerAndWaitReady(ctx, &alb********.CreateListenerRequest{
		DefaultActions: []*alb********.CreateListenerRequestDefaultActions{
			{
				ForwardGroupConfig: &alb********.CreateListenerRequestDefaultActionsForwardGroupConfig{
					ServerGroupTuples: []*alb********.CreateListenerRequestDefaultActionsForwardGroupConfigServerGroupTuples{
						{
							ServerGroupId: tea.String(serverGroupId),
						},
					},
				},
				Type: tea.String("ForwardGroup"),
			},
		},
		GzipEnabled:         tea.Bool(true),
		ListenerDescription: tea.String(getListenerDesc("HTTPS", rdata.LoadBalancerName)),
		ListenerPort:        tea.Int32(443),
		ListenerProtocol:    tea.String("HTTPS"),
		LoadBalancerId:      resp.LoadBalancerId,
		XForwardedForConfig: &alb********.CreateListenerRequestXForwardedForConfig{
			XForwardedForEnabled: tea.Bool(true),
		},
		Certificates: certificates,
	}, instanceID)
	if err != nil {
		reportError(ctx, rdata.LoadBalancerName, orderID, reportStore, "https_listener")
		return nil, err
	}
	a.logger.Infof("创建https监听成功")
	reportStore = reportSuccess(ctx, rdata.LoadBalancerName, orderID, reportStore, "https_listener")

	// 开启访问日志
	err = a.client.EnableAccessLog(ctx, tea.StringValue(resp.LoadBalancerId), rdata.LoadBalancerName)
	if err != nil {
		return nil, err
	}
	a.logger.Infof("开启访问日志成功")

	// 上报信息
	common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", rdata.LoadBalancerName):  time.Now().Unix(),
		common.ReportSprintf("%s.status", rdata.LoadBalancerName):     "success",
		common.ReportSprintf("%s.is_success", rdata.LoadBalancerName): true,
	})

	return []string{tea.StringValue(resp.LoadBalancerId)}, err
}

func (a AliLB) waitForReady(ctx context.Context, orderID, instanceID, instanceName, reportStore string) (string, error) {
	for {
		select {
		case <-ctx.Done():
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceName):    "confirm_timeout",
				common.ReportSprintf("%s.is_err", instanceName):    true,
			})
			return reportStore, fmt.Errorf("执行超时")
		default:
			resp, err := a.client.GetLBAttribute(ctx, instanceID)
			if err != nil {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
					common.ReportSprintf("%s.status", instanceName):    "confirm_fetch_error",
					common.ReportSprintf("%s.is_err", instanceName):    true,
				})
				a.logger.Errorf("获取实例:%s状态失败", instanceID)
				return reportStore, err
			}
			if tea.StringValue(resp.Body.LoadBalancerStatus) == "Active" {
				return reportStore, nil
			}
			a.logger.Warnf("实例:%s状态未就绪,30秒后再次查询", instanceID)
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceName):    tea.StringValue(resp.Body.LoadBalancerStatus),
			})
			time.Sleep(30 * time.Second)
		}
	}
}

func getListenerDesc(protocol, lbName string) string {
	// "lb-sf-ops-oa01-inner" -> "oa" -> "oaserver"

	shortNameMap := map[string]string{
		"oa":   "oaserver",
		"muip": "muipserver",
		"dp":   "dispatch",
	}
	names := strings.Split(lbName, "-")
	moduleSeq := names[len(names)-2]
	moduleShortName := strings.TrimFunc(moduleSeq, func(r rune) bool {
		return r >= '0' && r <= '9'
	})
	moduleName := shortNameMap[moduleShortName]
	return fmt.Sprintf("%s_%s", protocol, moduleName)
}

var sgRex = regexp.MustCompile(`[a-zA-Z]+|\d+`)

var modulePortMap = map[string]int{
	"dp":   30401,
	"muip": 30701,
	"oa":   31001,
	"gp":   20000,
}

func getSgName(lbName string) (string, error) {
	names := strings.Split(lbName, "-")

	if len(names) != 6 {
		return "", fmt.Errorf("wrong lb name, %s", lbName)
	}
	moduleNum := sgRex.FindAllString(names[4], -1)
	module := moduleNum[0]
	// convert module name
	if module == "glbdp" {
		module = "gp"
	}
	port, ok := modulePortMap[module]
	if !ok {
		port = 0
	}
	return fmt.Sprintf("tg-%s-%s-%s-%s_%d", names[1], names[2], names[3], module, port), nil
}

// UpdateInstance 更新实例
func (a AliLB) UpdateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) error {
	return nil
}

// DeleteInstance 删除实例
func (a AliLB) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	for _, instanceID := range form.Instances {
		err := a.client.DeleteLoadBalancer(ctx, instanceID)
		if err != nil {
			return err
		}
		a.logger.Infof("delete loadbalancer %s success", instanceID)
	}
	return nil
}

// GetSnapshotCount ...
func (a AliLB) GetSnapshotCount(ctx context.Context) (int32, error) {
	var totalCount int32 = 0
	return totalCount, nil
}

// TestPing 测试连通性
func (a AliLB) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}
