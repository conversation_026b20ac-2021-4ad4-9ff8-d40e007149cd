package alicloud

import (
	"fmt"
	"strings"
	"testing"
	"unicode"

	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/context"
)

const (
	rgAliyunCN     = "rg-aek2dtzmr7e4znq"
	rgAliyunCB3    = "rg-aek2wznwxz4dkwy"
	rgAliyunSF     = "rg-aekzzkclrswigpi"
	rgAliyunDevops = "rg-aekzetb2pw2pu7q"
	rgAliyunQA     = "rg-aek23i72b5ulmtq"

	rgAliCloudCB3    = "rg-aek3tel7pkrrr7i"
	rgAliCloudJP     = "rg-aek4bmngtnam3uq"
	rgAliCloudEU     = "rg-aek3e3ejwmzenwq"
	rgAliCloudUS     = "rg-aek4ixhm6pvtleq"
	rgAliCloudSG     = "rg-aek4vsmbbx5cppa"
	rgAliCloudDevops = "rg-aek5j3ogrkdsqya"

	aliyunIspID   = "65572aed9bb2a1793df282a6"
	alicloudIspID = "65a0ae9ebf0b894146fc4983"
)

func moveECSRG(t *testing.T, regionID, ispID string) {
	client := AliEcsClient{
		regionID:  regionID,
		accountID: ispID,
	}
	ctx := context.Background()
	nextToken := ""
	for {
		response, err := client.DescribeAllInstances(ctx, 100, nextToken)
		if err != nil {
			t.Fatal(err)
		}

		for _, instance := range response.Instances.Instance {
			instance := instance
			instanceName := tea.StringValue(instance.InstanceName)
			instanceNameSeg := strings.Split(instanceName, "-")
			if tea.StringValue(instance.Status) != "Running" {
				fmt.Printf("skip not running instance: %s\n", instanceName)
				continue
			}

			if len(instanceNameSeg) != 5 || instanceNameSeg[0] != "nap" {
				fmt.Printf("skip instance: %s\n", instanceName)
				continue
			}
			env := instanceNameSeg[1]
			region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])

			moduleNum := instanceNameSeg[4]
			module := strings.TrimRightFunc(moduleNum, func(r rune) bool {
				return unicode.IsNumber(r)
			})
			if module == "" {
				module = "all"
			}

			newRGID := ""
			if region == "prod_gf_cn" {
				newRGID = rgAliyunCN
			} else if region == "prod_cb01_cn" || region == "prod_cb02_cn" {
				newRGID = rgAliyunCB3
			} else if region == "prod_cb01_us" || region == "prod_cb02_us" || region == "prod_cb01_jp" || region == "prod_cb02_jp" || region == "prod_cb01_eu" || region == "prod_cb02_eu" {
				newRGID = rgAliCloudCB3
			} else if env == "sf" {
				newRGID = rgAliyunSF
			}
			if instanceNameSeg[2] == "devops" && ispID == alicloudIspID {
				newRGID = rgAliCloudDevops
			} else if instanceNameSeg[2] == "devops" && ispID == aliyunIspID {
				newRGID = rgAliyunDevops
			} else if env == "qa" && ispID == aliyunIspID {
				newRGID = rgAliyunQA
			}
			if newRGID == "" {
				fmt.Printf("skip no move instance: %s\n", instanceName)
				continue
			}
			if tea.StringValue(instance.ResourceGroupId) == newRGID {
				fmt.Printf("skip already moved instance: %s\n", instanceName)
				continue
			}

			err := client.JoinResourceGroup(ctx, *instance.InstanceId, newRGID)
			if err != nil {
				fmt.Printf("rg instance: %s failed: err: %v\n", instanceName, err)
			}
			fmt.Printf("rg instance: %s success\n", instanceName)
		}

		if response.NextToken == nil || tea.StringValue(response.NextToken) == "" {
			break
		}
		nextToken = *response.NextToken
	}
}

func moveAliyunECSRG(t *testing.T) {
	ispID := "65572aed9bb2a1793df282a6"
	regionID := "cn-shanghai"
	moveECSRG(t, regionID, ispID)
}

func moveAlicloudECSRG(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65a0ae9ebf0b894146fc4983"
	for _, regionID := range regionIDList {
		moveECSRG(t, regionID, ispID)
	}
}

func TestMoveECSRG(t *testing.T) {

	moveAliyunECSRG(t)
	moveAlicloudECSRG(t)
}

func movePolarDB(t *testing.T, regionID, ispID string) {
	client := CreateAliPolarDBClient(regionID, ispID)
	ctx := context.Background()
	currentNum := 0
	totalCount := 0
	for i := 1; i <= 100; i++ {
		resp, err := client.DescribeDBClusters(ctx, int32(i), 50)
		if totalCount == 0 {
			totalCount = int(*resp.TotalRecordCount)
		}
		currentNum += int(*resp.PageRecordCount)
		if err != nil {
			t.Fatal(err)
		}

		for _, cluster := range resp.Items.DBCluster {
			instanceName := tea.StringValue(cluster.DBClusterDescription)
			instanceNameSeg := strings.Split(instanceName, "-")
			if len(instanceNameSeg) != 5 || instanceNameSeg[0] != "nap" {
				fmt.Printf("%s\n", instanceName)
				continue
			}
			newRGID := ""
			env := instanceNameSeg[1]
			region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])
			if region == "prod_gf_cn" {
				newRGID = rgAliyunCN
			} else if region == "prod_cb01_cn" || region == "prod_cb02_cn" {
				newRGID = rgAliyunCB3
			} else if region == "prod_cb01_us" || region == "prod_cb02_us" || region == "prod_cb01_jp" || region == "prod_cb02_jp" || region == "prod_cb01_eu" || region == "prod_cb02_eu" {
				newRGID = rgAliCloudCB3
			} else if env == "sf" {
				newRGID = rgAliyunSF
			}
			if instanceNameSeg[2] == "devops" && ispID == alicloudIspID {
				newRGID = rgAliCloudDevops
			} else if instanceNameSeg[2] == "devops" && ispID == aliyunIspID {
				newRGID = rgAliyunDevops
			}
			if newRGID == "" {
				fmt.Printf("skip no move instance: %s\n", instanceName)
				continue
			}
			if tea.StringValue(cluster.ResourceGroupId) == newRGID {
				fmt.Printf("skip already moved instance: %s\n", instanceName)
				continue
			}

			err := client.ModifyDBClusterResourceGroup(ctx, *cluster.DBClusterId, newRGID)
			if err != nil {
				fmt.Printf("rg instance: %s failed: err: %v\n", instanceName, err)
			}
			fmt.Printf("rg instance: %s success\n", instanceName)
		}
		if currentNum >= totalCount {
			break
		}
	}
}

func moveAliyunPolarRG(t *testing.T) {
	ispID := "65572aed9bb2a1793df282a6"
	regionID := "cn-shanghai"
	movePolarDB(t, regionID, ispID)
}

func moveAlicloudPolarRG(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65a0ae9ebf0b894146fc4983"
	for _, regionID := range regionIDList {
		movePolarDB(t, regionID, ispID)
	}
}

func TestMovePolarRG(t *testing.T) {

	moveAliyunPolarRG(t)
	moveAlicloudPolarRG(t)
}

func moveRedisRG(t *testing.T, regionID, ispID string) {
	currentNum := 0
	totalCount := 0
	client := AliKvStoreClient{
		regionID:  regionID,
		accountID: ispID,
	}
	ctx := context.Background()
	for i := 1; i <= 100; i++ {
		resp, err := client.DescribeInstances(ctx, int32(i), 100)
		if err != nil {
			t.Fatal(err)
		}
		if totalCount == 0 {
			totalCount = int(*resp.TotalCount)
		}

		for _, instance := range resp.Instances.KVStoreInstance {
			instanceName := tea.StringValue(instance.InstanceName)
			instanceNameSeg := strings.Split(instanceName, "-")
			if len(instanceNameSeg) != 5 || instanceNameSeg[0] != "nap" {
				fmt.Printf("skip instance: %s\n", instanceName)
				continue
			}
			newRGID := ""
			env := instanceNameSeg[1]
			region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])
			if region == "prod_gf_cn" {
				newRGID = rgAliyunCN
			} else if region == "prod_cb01_cn" || region == "prod_cb02_cn" {
				newRGID = rgAliyunCB3
			} else if region == "prod_cb01_us" || region == "prod_cb02_us" || region == "prod_cb01_jp" || region == "prod_cb02_jp" || region == "prod_cb01_eu" || region == "prod_cb02_eu" {
				newRGID = rgAliCloudCB3
			} else if region == "prod_gf_jp" {
				newRGID = rgAliCloudJP
			} else if region == "prod_gf_us" {
				newRGID = rgAliCloudUS
			} else if region == "prod_gf_eu" {
				newRGID = rgAliCloudEU
			} else if region == "prod_gf_sg" {
				newRGID = rgAliCloudSG
			} else if env == "sf" {
				newRGID = rgAliyunSF
			}
			if instanceNameSeg[2] == "devops" && ispID == alicloudIspID {
				newRGID = rgAliCloudDevops
			} else if instanceNameSeg[2] == "devops" && ispID == aliyunIspID {
				newRGID = rgAliyunDevops
			}
			if newRGID == "" {
				fmt.Printf("skip no move instance: %s\n", instanceName)
				continue
			}
			if tea.StringValue(instance.ResourceGroupId) == newRGID {
				fmt.Printf("skip already moved instance: %s\n", instanceName)
				continue
			}

			err := client.ModifyResourceGroup(ctx, *instance.InstanceId, newRGID)
			if err != nil {
				fmt.Printf("rg instance: %s failed: err: %v\n", instanceName, err)
			}
			fmt.Printf("rg instance: %s success\n", instanceName)
		}
		currentNum += int(*resp.PageSize)
		if currentNum >= totalCount {
			break
		}
	}
}

func moveAliyunRedisRG(t *testing.T) {
	ispID := "65572aed9bb2a1793df282a6"
	regionID := "cn-shanghai"
	moveRedisRG(t, regionID, ispID)
}

func moveAlicloudRedisRG(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65a0ae9ebf0b894146fc4983"
	for _, regionID := range regionIDList {
		moveRedisRG(t, regionID, ispID)
	}
}

func TestMoveRedisRG(t *testing.T) {

	moveAliyunRedisRG(t)
	moveAlicloudRedisRG(t)
}

func moveLB(t *testing.T, regionID, ispID string) {
	client := AliLoadBalancerClient{
		regionID:  regionID,
		accountID: ispID,
	}
	ctx := context.Background()
	lbs, err := client.ListAllALBs(ctx)
	if err != nil {
		t.Fatal(err)
	}
	for _, instance := range lbs {
		instanceName := tea.StringValue(instance.LoadBalancerName)
		instanceNameSeg := strings.Split(instanceName, "-")
		if len(instanceNameSeg) != 6 || instanceNameSeg[0] != "lb" {
			fmt.Printf("skip instance: %s\n", instanceName)
			continue
		}
		newRGID := ""
		env := instanceNameSeg[1]
		region := fmt.Sprintf("%s_%s_%s", instanceNameSeg[1], instanceNameSeg[2], instanceNameSeg[3])
		if region == "prod_gf_cn" {
			newRGID = rgAliyunCN
		} else if region == "prod_cb01_cn" || region == "prod_cb02_cn" {
			newRGID = rgAliyunCB3
		} else if region == "prod_cb01_us" || region == "prod_cb02_us" || region == "prod_cb01_jp" || region == "prod_cb02_jp" || region == "prod_cb01_eu" || region == "prod_cb02_eu" {
			newRGID = rgAliCloudCB3
		} else if region == "prod_gf_jp" {
			newRGID = rgAliCloudJP
		} else if region == "prod_gf_us" {
			newRGID = rgAliCloudUS
		} else if region == "prod_gf_eu" {
			newRGID = rgAliCloudEU
		} else if region == "prod_gf_sg" {
			newRGID = rgAliCloudSG
		} else if env == "sf" {
			newRGID = rgAliyunSF
		}
		if instanceNameSeg[2] == "devops" && ispID == alicloudIspID {
			newRGID = rgAliCloudDevops
		} else if instanceNameSeg[2] == "devops" && ispID == aliyunIspID {
			newRGID = rgAliyunDevops
		}
		if newRGID == "" {
			fmt.Printf("skip no move instance: %s\n", instanceName)
			continue
		}
		err := client.MoveResourceGroup(ctx, *instance.LoadBalancerId, newRGID)
		if err != nil {
			fmt.Printf("rg instance: %s failed: err: %v\n", instanceName, err)
			continue
		}
		fmt.Printf("rg instance: %s success\n", instanceName)
	}
}

func moveAliyunALBRG(t *testing.T) {
	ispID := "65572aed9bb2a1793df282a6"
	regionID := "cn-shanghai"
	moveLB(t, regionID, ispID)
}

func moveAlicloudALBRG(t *testing.T) {
	regionIDList := []string{"ap-northeast-1", "ap-southeast-1", "eu-central-1", "us-east-1"}
	ispID := "65a0ae9ebf0b894146fc4983"
	for _, regionID := range regionIDList {
		moveLB(t, regionID, ispID)
	}
}
func TestMoveALBRG(t *testing.T) {

	moveAliyunALBRG(t)
	moveAlicloudALBRG(t)
}
