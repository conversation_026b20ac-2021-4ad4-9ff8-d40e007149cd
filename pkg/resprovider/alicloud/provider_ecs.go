package alicloud

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/kms"

	ecs20140526 "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	vpc******** "github.com/alibabacloud-go/vpc-********/v6/client"
	"github.com/kr/pretty"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	hostname_util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hostname"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// AliEcs aliecs obj
type AliEcs struct {
	client *AliEcsClient
	logger *logrus.Logger

	dryRun    bool
	accountID string
}

// CheckHostnameUnique ...
func CheckHostnameUnique(req *ecs20140526.RunInstancesRequest) error {
	if tea.StringValue(req.HostName) != "" {
		if err := cloudutils.RuleCheck(tea.StringValue(req.HostName), cloudutils.Linux); err == nil {
			instances, err := cloudutils.GetCloudutils(models.HostResourceModel).GenInstanceName(
				context.Background(),
				tea.StringValue(req.HostName),
				int(tea.Int32Value(req.Amount)), tea.BoolValue(req.UniqueSuffix), cloudutils.Linux,
			)
			if err != nil {
				return err
			}
			if instances == nil {
				return fmt.Errorf("主机名重复")
			}
			req.InstanceName = nil
			req.HostName = nil
			req.HostNames = tea.StringSlice(instances)
		}
		req.UniqueSuffix = tea.Bool(false)
	}
	return nil
}

type ipv6Config struct {
	Body struct {
		Enable          bool   `json:"enable"`
		PublicEnable    bool   `json:"public_enable"`
		PublicPaytype   string `json:"public_paytype"`
		PublicBandwidth int32  `json:"public_bandwidth"`
	} `json:"ipv6"`
}

type bandwidthConfig struct {
	Body struct {
		Enable    bool   `json:"enable"`
		PackageID string `json:"packageID"`
	} `json:"bindBandwidth"`
}

// CreateInstance 创建实例
func (a AliEcs) CreateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) (instances []string, err error) {
	var req ecs20140526.RunInstancesRequest
	err = json.Unmarshal(data, &req)
	if err != nil {
		return nil, err
	}
	// 判断主机列表是否满足要求;由于复用阿里云生成规则,阿里云无需调用core.GenInstance方法
	// 开启主机名全局唯一后，自动生成主机名列表
	if err = CheckHostnameUnique(&req); err != nil {
		return nil, err
	}
	var hostNameList []string
	if tea.StringValue(req.HostName) != "" {
		hostNameList = append(hostNameList, tea.StringValue(req.HostName))
	} else {
		hostNameList = tea.StringSliceValue(req.HostNames)
	}
	if len(hostNameList) != int(*req.Amount) {
		return nil, fmt.Errorf("hostname_count mismatch req_count")
	}
	reportStore := ""
	for _, hostName := range hostNameList {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.start_time", hostName):                    time.Now().Unix(),
			common.ReportSprintf("%s.instance_name", hostName):                 hostName,
			common.ReportSprintf("%s.status", hostName):                        "preparing",
			common.ReportSprintf("%s.jobs.CreateInstance.last_time", hostName): time.Now().Unix(),
			common.ReportSprintf("%s.jobs.CreateInstance.status", hostName):    "preparing",
		})
	}

	// 序列化可用区及交换机映射配置
	zoneMappings := struct {
		Mapping []struct {
			ZoneId    string `json:"ZoneId"`
			VSwitchId string `json:"VSwitchId"`
			Amount    int32
			HostNames []*string
		} `json:"ZoneMappings"`
	}{}
	err = json.Unmarshal(data, &zoneMappings)
	if err != nil {
		return nil, err
	}
	if len(zoneMappings.Mapping) == 0 {
		return nil, fmt.Errorf("可用区及交换机映射未配置")
	}
	zoneMappingLength := len(zoneMappings.Mapping)
	startIndex := 0
	for index := range zoneMappings.Mapping {
		amount := tea.Int32Value(req.Amount) / int32(zoneMappingLength)
		if index+1 <= int(tea.Int32Value(req.Amount))%zoneMappingLength {
			amount += 1
		}
		zoneMappings.Mapping[index].HostNames = req.HostNames[startIndex : startIndex+int(amount)]
		startIndex = startIndex + int(amount)
		zoneMappings.Mapping[index].Amount = amount
	}

	// 参数准备
	req.DryRun = tea.Bool(a.dryRun)
	userData := tea.StringValue(req.UserData)
	if userData != "" {
		req.UserData = tea.String(base64.StdEncoding.EncodeToString([]byte(userData)))
	}

	privatePool := struct {
		Body struct {
			Value string `json:"value"`
			ID    string `json:"id"`
		} `json:"privatePool"`
	}{}
	err = json.Unmarshal(data, &privatePool)
	if err != nil {
		return nil, err
	}
	if privatePool.Body.Value == "open" {
		req.PrivatePoolOptions = &ecs20140526.RunInstancesRequestPrivatePoolOptions{MatchCriteria: tea.String(privatePool.Body.Value)}
	}
	if privatePool.Body.Value == "target" {
		req.PrivatePoolOptions = &ecs20140526.RunInstancesRequestPrivatePoolOptions{MatchCriteria: tea.String(privatePool.Body.Value), Id: tea.String(privatePool.Body.ID)}
	}

	// tag参数重新序列化
	tag := struct {
		Tag []*common.Tag `json:"Tag"`
	}{}
	err = json.Unmarshal(data, &tag)
	if err != nil {
		return nil, err
	}

	// 加上region, env, module, nap, region_module这几个通用的标签
	hostnameExample := hostNameList[0]
	req.Tag = []*ecs20140526.RunInstancesRequestTag{
		{
			Key:   tea.String("nap"),
			Value: tea.String(hostname_util.GetHostNameArea(hostnameExample)),
		},
		{
			Key:   tea.String("env"),
			Value: tea.String(hostname_util.GetHostNameEnv(hostnameExample)),
		},
		{
			Key:   tea.String("region"),
			Value: tea.String(hostname_util.GetHostNameRegion(hostnameExample)),
		},
		{
			Key:   tea.String("module"),
			Value: tea.String(hostname_util.GetHostNameModule(hostnameExample)),
		},
		{
			Key:   tea.String("region_module"),
			Value: tea.String(hostname_util.GetHostNameRegionModule(hostnameExample)),
		},
	}

	systemTag := []string{"nap", "env", "region", "module", "serial_number", "region_module"}
	if len(tag.Tag) > 0 {
		for _, t := range tag.Tag {
			// 通用的标签不允许自定义
			if slices.Contains(systemTag, t.Key) {
				continue
			}
			req.Tag = append(req.Tag, &ecs20140526.RunInstancesRequestTag{
				Key:   &t.Key,
				Value: &t.Value,
			})
		}
	}
	a.logger.Infof("绑定标签为%# v", pretty.Formatter(req.Tag))
	// 默认开启I/O优化实例
	ioOptimized := "optimized"
	req.IoOptimized = &ioOptimized

	// 默认开启安全加固
	securityStrategy := "active"
	req.SecurityEnhancementStrategy = &securityStrategy
	// 默认开启释放保护
	if tea.StringValue(req.InstanceChargeType) == "PostPaid" {
		req.DeletionProtection = tea.Bool(true)
	}

	// 序列化EIP配置
	eipConfig := struct {
		Body struct {
			Enable bool     `json:"enable"`
			SegIDs []string `json:"segIDs"`
		} `json:"bindEIP"`
	}{}
	err = json.Unmarshal(data, &eipConfig)
	if err != nil {
		return nil, err
	}

	// 序列化ddos配置
	ddosConfig := struct {
		Body struct {
			Enable    bool   `json:"enable"`
			PackageID string `json:"packageID"`
		} `json:"bindDDos"`
	}{}
	err = json.Unmarshal(data, &ddosConfig)
	if err != nil {
		return nil, err
	}

	// 序列化袋宽包配置
	bandwidthPackageConfig := bandwidthConfig{}
	err = json.Unmarshal(data, &bandwidthPackageConfig)
	if err != nil {
		return nil, err
	}

	ipv6Config := ipv6Config{}
	err = json.Unmarshal(data, &ipv6Config)
	if err != nil {
		return nil, err
	}

	// 计算每个可用区的机器数，调用接口申请
	instanceIDs := make([]string, 0)

	// 设置device字段，避免镜像带数据盘导致无法创建资源的bug
	a.setDeviceField(ctx, &req)

	for _, zoneMap := range zoneMappings.Mapping {
		// 执行
		req.SetAmount(zoneMap.Amount)
		req.SetZoneId(zoneMap.ZoneId)
		req.SetVSwitchId(zoneMap.VSwitchId)
		req.SetHostNames(zoneMap.HostNames)

		if a.dryRun {
			req.SetHostNames(tea.StringSlice([]string{"test-hostname"}))
			req.SetAmount(1)
		} else if zoneMap.Amount == 0 {
			a.logger.Warnf("可用区-%s购买数量等于0, 跳过申请该可用区机器...", zoneMap.ZoneId)
			continue
		}

		instanceIDsSet, err := a.createInstancesInBatch(ctx, timeout, &req, orderID)
		if err != nil {
			if IsDryError(err) {
				return []string{}, nil
			}
			// 执行错误，上报记录
			for _, hostName := range hostNameList {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", hostName):                     time.Now().Unix(),
					common.ReportSprintf("%s.status", hostName):                        "failed",
					common.ReportSprintf("%s.is_err", hostName):                        true,
					common.ReportSprintf("%s.jobs.CreateInstance.last_time", hostName): time.Now().Unix(),
					common.ReportSprintf("%s.jobs.CreateInstance.status", hostName):    "start_failed",
					common.ReportSprintf("%s.jobs.CreateInstance.is_err", hostName):    true,
				})
			}
			common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
			return nil, err
		}
		instanceIDs = append(instanceIDs, instanceIDsSet...)
	}

	// 在实时状态中绑定hostName和instanceID
	instanceID2HostName := map[string]string{}
	if len(instanceIDs) != len(hostNameList) {
		for _, hostName := range hostNameList {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", hostName):                     time.Now().Unix(),
				common.ReportSprintf("%s.status", hostName):                        "failed",
				common.ReportSprintf("%s.is_err", hostName):                        true,
				common.ReportSprintf("%s.jobs.CreateInstance.last_time", hostName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.CreateInstance.status", hostName):    "start_result_error",
				common.ReportSprintf("%s.jobs.CreateInstance.is_err", hostName):    true,
			})
		}
		return nil, fmt.Errorf("创建主机返回数量(%d)与申请数量(%d)不一致", len(instanceIDs), len(hostNameList))
	}
	for index, hostName := range hostNameList {
		instanceID2HostName[instanceIDs[index]] = hostName
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", hostName):                     time.Now().Unix(),
			common.ReportSprintf("%s.status", hostName):                        "created",
			common.ReportSprintf("%s.instance_id", hostName):                   instanceIDs[index],
			common.ReportSprintf("%s.jobs.CreateInstance.last_time", hostName): time.Now().Unix(),
			common.ReportSprintf("%s.jobs.CreateInstance.status", hostName):    "created",
		})
	}
	reportStore = common.ReportOrderStatusDetail(ctx, orderID, true, reportStore, "", "")
	// 记录instance详情，后续子任务使用
	instanceDetailMap := map[string]*ecs20140526.DescribeInstancesResponseBodyInstancesInstance{}
	reportStore = a.afterCreate(ctx, orderID, instanceID2HostName, reportStore, instanceDetailMap)

	// 绑定EIP
	ipList := make([]string, len(instanceID2HostName))
	if eipConfig.Body.Enable {
		bindEipFuncs := make(map[string]func(ctx context.Context) error)
		idx := 0
		for instanceID, hostname := range instanceID2HostName {
			bindEipFuncs[hostname] = func(ctx context.Context) error {
				return a.bindEIP(ctx, hostname, instanceID, eipConfig.Body.SegIDs, tag.Tag, &bandwidthPackageConfig, idx, ipList)
			}
			idx += 1
		}

		reportStore, err = runTaskConcurrently(ctx, bindEipFuncs, orderID, reportStore, "BindEIP", 50)
		if err != nil {
			return nil, err
		}
	}

	// 绑定Serial nubmer标签
	bindTagFuncs := make(map[string]func(ctx context.Context) error)
	for instanceID, hostname := range instanceID2HostName {
		tags := []*ecs20140526.TagResourcesRequestTag{
			{
				Key:   tea.String("serial_number"),
				Value: tea.String(hostname_util.GetHostNameNum(hostname)),
			},
		}
		bindTagFuncs[hostname] = func(ctx context.Context) error {
			return a.client.SetECSResourceTag(ctx, instanceID, "instance", tags)
		}
	}
	reportStore, err = runTaskConcurrently(ctx, bindTagFuncs, orderID, reportStore, "BindSerialNumberTags", 50)
	if err != nil {
		return nil, err
	}

	// 更改磁盘名称
	changeDiskNameFuncs := make(map[string]func(ctx context.Context) error)
	for instanceID, hostname := range instanceID2HostName {
		changeDiskNameFuncs[hostname] = func(ctx context.Context) error {
			return a.ChangeInstanceDiskName(ctx, instanceID, hostname)
		}
	}
	reportStore, err = runTaskConcurrently(ctx, changeDiskNameFuncs, orderID, reportStore, "ChangeDiskName", 50)
	if err != nil {
		return nil, err
	}

	// 绑定ipv6地址
	if ipv6Config.Body.Enable {
		dealIpv6Funcs := make(map[string]func(ctx context.Context) error)
		for _, instanceDetail := range instanceDetailMap {
			dealIpv6Funcs[tea.StringValue(instanceDetail.InstanceName)] = func(ctx context.Context) error {
				return a.dealIpv6(ctx, instanceDetail, &ipv6Config)
			}
		}
		reportStore, err = runTaskConcurrently(ctx, dealIpv6Funcs, orderID, reportStore, "dealIpv6", 50)
		if err != nil {
			return nil, err
		}
	}

	if ddosConfig.Body.Enable {
		a.logger.Infoln("开始绑定高防包")
		client, err := CreateAliDdosBGPClient(a.accountID)
		if err != nil {
			a.logger.Errorln("绑定高防包失败")
			return nil, err
		}
		a.logger.Infof("高防包%s绑定ip列表%+v", ddosConfig.Body.PackageID, ipList)
		err = client.AddIp(ctx, ddosConfig.Body.PackageID, ipList)
		if err != nil {
			a.logger.Errorf("绑定高防包失败, err: %v", err)
			return nil, err
		}
	}

	// 上报成功状态
	for _, instanceName := range hostNameList {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", instanceName):  time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceName):     "success",
			common.ReportSprintf("%s.is_success", instanceName): true,
		})
	}

	return instanceIDs, nil
}

func (a AliEcs) dealIpv6(ctx context.Context, instanceDetail *ecs20140526.DescribeInstancesResponseBodyInstancesInstance, ipv6Config *ipv6Config) error {
	// 使用第一个网卡的id
	if len(instanceDetail.NetworkInterfaces.NetworkInterface) == 0 {
		return fmt.Errorf("cannot get network interface")
	}
	networkinterfaceId := tea.StringValue(instanceDetail.NetworkInterfaces.NetworkInterface[0].NetworkInterfaceId)

	ipv6Address, err := a.client.AssignIpv6Addresses(ctx, networkinterfaceId)
	if err != nil {
		return err
	}

	if !ipv6Config.Body.PublicEnable {
		return nil
	}

	ipv6Detail, err := a.client.WaitIpv6AvailAndGetDetail(ctx, ipv6Address)
	if err != nil {
		return err
	}
	if len(ipv6Detail.Ipv6Addresses.Ipv6Address) == 0 {
		return fmt.Errorf("cannot get ipv6 address")
	}
	gatewayId := tea.StringValue(ipv6Detail.Ipv6Addresses.Ipv6Address[0].Ipv6GatewayId)
	addressId := tea.StringValue(ipv6Detail.Ipv6Addresses.Ipv6Address[0].Ipv6AddressId)
	// 绑定公网带宽
	return a.client.AllocateIpv6InternetBandwidth(ctx, gatewayId, addressId, ipv6Config.Body.PublicPaytype, ipv6Config.Body.PublicBandwidth)
}

func (a AliEcs) ChangeInstanceDiskName(ctx context.Context, instanceID, instanceName string) error {
	diskResp, err := a.client.DescribeDisks(ctx, instanceID)
	if err != nil {
		return err
	}
	dataDiskIndex := 1
	systemDiskIndex := 1
	for _, disk := range diskResp.Disks.Disk {
		diskName := ""
		switch tea.StringValue(disk.Type) {
		case "system":
			diskName = fmt.Sprintf("disk%s-system%.2d", strings.TrimLeft(instanceName, "nap"), systemDiskIndex)
			systemDiskIndex += 1
		case "data":
			diskName = fmt.Sprintf("disk%s-data%.2d", strings.TrimLeft(instanceName, "nap"), dataDiskIndex)
			dataDiskIndex += 1
		default:
			return fmt.Errorf("wrong disk type")
		}
		if diskName == tea.StringValue(disk.DiskName) {
			continue
		}
		err := a.client.ModifyDiskName(ctx, tea.StringValue(disk.DiskId), diskName)
		if err != nil {
			return err
		}
	}
	return nil
}

func (a AliEcs) setDeviceField(ctx context.Context, req *ecs20140526.RunInstancesRequest) {
	if len(req.DataDisk) == 0 {
		return
	}
	imageID := tea.StringValue(req.ImageId)
	image, err := a.client.GetImageByID(ctx, imageID)
	if err != nil {
		return
	}
	if image.DiskDeviceMappings == nil {
		return
	}
	index := 0
	for _, disk := range image.DiskDeviceMappings.DiskDeviceMapping {
		if tea.StringValue(disk.Type) != "data" {
			continue
		}
		// 如果请求中没有足够的数据盘,则不设置该字段
		if index >= len(req.DataDisk) {
			return
		}
		req.DataDisk[index].Device = disk.Device
		index += 1
	}
}

func (a AliEcs) createInstancesInBatch(ctx context.Context, timeout time.Duration, req *ecs20140526.RunInstancesRequest, orderID string) ([]string, error) {
	instanceIDs := make([]string, 0)

	totalAmount := tea.Int32Value(req.Amount)
	allHostnames := req.HostNames
	// 重试时需要考虑部分成功的情况
	hostnameIndex := 0
	for totalAmount > 0 {
		currentAmount := min(totalAmount, 100)
		req.SetAmount(currentAmount)
		req.SetHostNames(allHostnames[hostnameIndex : hostnameIndex+int(currentAmount)])
		body, err := a.client.RunInstances(ctx, timeout, req)
		if err != nil {
			return nil, err
		}
		fmt.Printf("create instance in batch request is: %+v", req)
		instanceIDs = append(instanceIDs, tea.StringSliceValue(body.InstanceIdSets.InstanceIdSet)...)
		totalAmount -= currentAmount
		hostnameIndex += int(currentAmount)
	}

	return instanceIDs, nil
}

func (a AliEcs) createEIP(ctx context.Context) (string, string, error) {
	eipRes, err := a.client.AllocateEIP(ctx)
	if err != nil {
		return "", "", err
	}
	id := tea.StringValue(eipRes.AllocationId)
	ip := tea.StringValue(eipRes.EipAddress)
	return id, ip, nil
}

func (a AliEcs) getAvailEip(ctx context.Context, eipInstanceIDs []string) (string, string, error) {
	for _, id := range eipInstanceIDs {
		availEipId, ipAddress, err := a.client.GetAvailEipBySegIDAndLock(ctx, id)
		if err == nil {
			return availEipId, ipAddress, err
		}
	}

	return "", "", fmt.Errorf("cannot find available eips")
}

func (a AliEcs) bindEIP(ctx context.Context, hostname, instanceID string, eipSegIDs []string, tags []*common.Tag, bandwidthConfig *bandwidthConfig, idx int, ipList []string) error {
	// 1.根据EIP组ID找到一个可用的EIP
	availEipAllocationID := ""
	ipAddress := ""
	var err error
	if len(eipSegIDs) != 0 {
		availEipAllocationID, ipAddress, err = a.getAvailEip(ctx, eipSegIDs)
		if err != nil {
			return err
		}
	} else {
		availEipAllocationID, ipAddress, err = a.createEIP(ctx)
		if err != nil {
			return err
		}
	}

	// 2. 绑定EIP
	err = a.client.AssociateEIP(ctx, availEipAllocationID, instanceID, "EcsInstance")
	if err != nil {
		return err
	}

	// 3. 用hostname设置EIP名称
	err = a.client.SetEIPName(ctx, availEipAllocationID, hostname)
	if err != nil {
		return err
	}
	// 4. 设置EIP标签
	if len(tags) > 0 {
		eipTags := make([]*vpc********.TagResourcesRequestTag, 0)
		for _, t := range tags {
			eipTags = append(eipTags, &vpc********.TagResourcesRequestTag{
				Key:   &t.Key,
				Value: &t.Value,
			})
		}
		// 增加serial number标签
		eipTags = append(eipTags, &vpc********.TagResourcesRequestTag{
			Key:   tea.String("serial_number"),
			Value: tea.String(hostname_util.GetHostNameNum(hostname)),
		})
		err = a.client.SetResourceTag(ctx, availEipAllocationID, "EIP", eipTags)
		if err != nil {
			return err
		}
	}
	// 5. 如果有需要，加入到带宽包
	if bandwidthConfig.Body.Enable {
		err = a.client.AddCommonBandwidthPackageIp(ctx, availEipAllocationID, bandwidthConfig.Body.PackageID)
		if err != nil {
			return err
		}
	}

	// 这里使用下标避免race condition
	ipList[idx] = ipAddress
	return nil
}

func (a AliEcs) afterCreate(ctx context.Context, orderID string, instanceMap map[string]string, reportStore string, instanceDetail map[string]*ecs20140526.DescribeInstancesResponseBodyInstancesInstance) string {
	// 轮询instanceID以确认机器状态，每10s一次，持续半小时，全部running提前退出
	times := 0
	interval := 10
	fullInstances := []string{}
	for instanceID := range instanceMap {
		fullInstances = append(fullInstances, instanceID)
	}
	waitInstances := fullInstances
	client, _ := CreateAliEcsClient(a.client.regionID, a.client.accountID)
	for times*interval < 1800 {
		times++
		time.Sleep(time.Duration(interval) * time.Second)
		stashInstances := []string{}
		sendInstance := []string{}
		for _, inst := range waitInstances {
			if len(sendInstance) < 100 {
				sendInstance = append(sendInstance, inst)
			} else {
				stashInstances = append(stashInstances, inst)
			}
		}
		resp, err := client.DescribeInstances(ctx, 1, 100, "", sendInstance...)
		if err != nil {
			times += 10
			continue
		}
		for _, inst := range resp.Instances.Instance {
			// 记录instanceDetail详情
			instanceDetail[tea.StringValue(inst.InstanceId)] = inst
			if instanceMap[tea.StringValue(inst.InstanceId)] == "" {
				continue
			}
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceMap[tea.StringValue(inst.InstanceId)]): time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceMap[tea.StringValue(inst.InstanceId)]):    tea.StringValue(inst.Status),
			})
			if tea.StringValue(inst.Status) != "Running" {
				stashInstances = append(stashInstances, tea.StringValue(inst.InstanceId))
			} else {
				reportStore = common.ReportOrderStatusDetail(ctx, orderID, false, reportStore, common.ReportSprintf("%s.jobs.CreateInstance.is_success", instanceMap[tea.StringValue(inst.InstanceId)]), true)
			}
		}
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
		if len(stashInstances) == 0 {
			break
		}
		waitInstances = stashInstances
	}
	// 当检查确认机器状态正常，则上报并移出轮询名单
	a.logger.Infof("aliyun主机创建状态轮询结束，instance:%+v", instanceMap)
	return reportStore
}

// waitForInstanceUtilSpecificStatus 等待实例到某个状态结束
func (a AliEcs) waitForInstanceUtilSpecificStatus(ctx context.Context, instanceIDs []string, waitTimeout time.Duration, status string) error {
	// 轮询任务结束，直到任务完成或者超时
	ticker := time.NewTicker(time.Second * 5)
	timeout := time.NewTimer(waitTimeout)
	waitInstances := instanceIDs
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("轮询因为context取消")
		case <-timeout.C:
			return fmt.Errorf("轮询因为超时取消")
		case <-ticker.C:
			stashInstances := []string{}
			sendInstance := []string{}
			// 由于阿里云接口限制，每批只能查找100个机器的状态
			for _, inst := range waitInstances {
				if len(sendInstance) < 100 {
					sendInstance = append(sendInstance, inst)
					continue
				}
				stashInstances = append(stashInstances, inst)
			}
			resp, err := a.client.DescribeInstances(ctx, 1, 100, "", sendInstance...)
			if err != nil {
				continue
			}
			for _, inst := range resp.Instances.Instance {
				if tea.StringValue(inst.Status) != status {
					stashInstances = append(stashInstances, tea.StringValue(inst.InstanceId))
				}
			}
			if len(stashInstances) == 0 {
				return nil
			}
			waitInstances = stashInstances
		}
	}
}

// UpdateInstance 更新实例
func (a AliEcs) UpdateInstance(ctx context.Context, timeout time.Duration, orderID string, data []byte) error {
	var form common.UpdateInstanceForm
	err := json.Unmarshal(data, &form)
	if err != nil {
		return err
	}

	switch form.Action {
	case "reboot":
		a.logger.Infof("开始重启实例, instances: %+v\n", form.Instances)
		_, err = a.client.RebootInstances(ctx, form.Instances)
		if err != nil {
			a.logger.Errorln("重启实例失败:", err.Error())
			return err
		}
		// 等待实例重启完毕
		a.logger.Infoln("等待实例就绪")
		a.waitForInstanceUtilSpecificStatus(ctx, form.Instances, 30*time.Minute, "Running")
		a.logger.Infoln("重启实例成功")
	case "start":
		a.logger.Infof("开始启动实例: %s", form.Instances)
		if len(form.Instances) > 100 {
			a.logger.Errorln("启动实例失败, 每次最多只能同时启动100个实例")
		}
		_, err = a.client.StartInstances(ctx, form.Instances)
		if err != nil {
			a.logger.Errorf("启动实例失败: %v", err.Error())
			return err
		}
		a.logger.Infoln("启动实例成功")
		// 等待实例到运行状态
		err = a.waitForInstanceUtilSpecificStatus(ctx, form.Instances, 10*time.Minute, "Running")
		if err != nil {
			a.logger.Errorf("等待实例到运行状态失败: %v", err.Error())
			return err
		}
		// 更新实例状态
		err = models.HostResourceModel.UpdateInstanceStatus(ctx, form.Instances, "Running")
		if err != nil {
			a.logger.Errorf("更新实例状态失败: %v", err.Error())
			return err
		}
	case "stop":
		a.logger.Infof("开始停止实例: %s", form.Instances)
		if len(form.Instances) > 100 {
			a.logger.Errorln("停止实例失败, 每次最多只能同时停止100个实例")
		}
		_, err = a.client.StopInstances(ctx, form.Instances, form.IsForce)
		if err != nil {
			a.logger.Errorf("停止实例失败: %v", err.Error())
			return err
		}
		a.logger.Infoln("停止实例成功")
		// 等待实例到停止状态
		err = a.waitForInstanceUtilSpecificStatus(ctx, form.Instances, 15*time.Minute, "Stopped")
		if err != nil {
			a.logger.Errorf("等待实例到停止状态失败: %v", err.Error())
			return err
		}
		// 更新实例状态
		err = models.HostResourceModel.UpdateInstanceStatus(ctx, form.Instances, "Stopped")
		if err != nil {
			a.logger.Errorf("更新实例状态失败: %v", err.Error())
			return err
		}
	}

	return nil
}

// DeleteInstance 释放实例对象
func (a AliEcs) DeleteInstance(ctx context.Context, timeout time.Duration, orderID string, form common.DeleteInstanceForm) error {
	a.logger.Infof("删除前准备查询阿里云主机实例: %v\n", form.Instances)
	reportStore := ""
	for _, instanceID := range form.Instances {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.instance_id", instanceID): instanceID,
			common.ReportSprintf("%s.start_time", instanceID):  time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceID):      "preparing",
		})
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
	existsInstances := make([]string, 0)
	for _, instance := range form.Instances {
		instDetail, err := a.client.DescribeInstanceAttribute(ctx, instance)
		if err != nil {
			// 如果实例不存在，直接跳过
			if strings.Contains(err.Error(), "StatusCode: 404") {
				a.logger.Infof("实例不存在跳过: %s\n", instance)
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instance):  time.Now().Unix(),
					common.ReportSprintf("%s.status", instance):     "skip",
					common.ReportSprintf("%s.is_success", instance): true,
				})
				continue
			}
			common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instance): time.Now().Unix(),
				common.ReportSprintf("%s.status", instance):    "change_paytype_failed",
				common.ReportSprintf("%s.is_err", instance):    false,
			})
			a.logger.Errorf("删除前查询实例%s失败:%s", instance, err.Error())
			return err
		}
		existsInstances = append(existsInstances, instance)

		// 删除前必须达到Stopped状态
		if *instDetail.Status != "Stopped" {
			a.logger.Warnf("实例%s未停止，退出...", instance)
			common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instance): time.Now().Unix(),
				common.ReportSprintf("%s.status", instance):    "waiting_to_stop",
				common.ReportSprintf("%s.is_err", instance):    true,
			})
			return errors.New("实例未停止")
		}

		// 预付费转按量
		if *instDetail.InstanceChargeType == "PrePaid" {
			err = a.client.ModifyInstanceChargeType(ctx, instance, *instDetail.RegionId)
			if err != nil {
				common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instance): time.Now().Unix(),
					common.ReportSprintf("%s.status", instance):    "change_paytype_error",
					common.ReportSprintf("%s.is_err", instance):    false,
				})
				a.logger.Errorf("删除前转换实例%s为按量付费失败:%s", instance, err.Error())
				return err
			}
			a.logger.Infof("实例%s转换为按量付费成功", instance)
		} else {
			a.logger.Infof("实例%s为按量付费，无需转换", instance)
		}

		// 如果为强制释放，做一次关闭释放保护动作, 忽略错误
		if form.Force {
			err = a.client.ModifyInstanceDeletionProtection(ctx, instance, false)
			if err == nil {
				a.logger.Infof("实例%s关闭释放保护成功", instance)
			} else {
				a.logger.Errorf("实例%s关闭释放保护失败, err: %v", instance, err)
			}
		}

		if instDetail.EipAddress != nil && tea.StringValue(instDetail.EipAddress.AllocationId) != "" {
			// 由于申请的是EIP段，不需要释放EIP,只需要修改EIP的名称和标签
			reportStore, err = a.UnassociateAndResetEIP(ctx, orderID, tea.StringValue(instDetail.EipAddress.AllocationId), instance, reportStore)
			if err != nil {
				common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instance):                     time.Now().Unix(),
					common.ReportSprintf("%s.jobs.UnAssociateEIP.last_time", instance): time.Now().Unix(),
					common.ReportSprintf("%s.jobs.UnAssociateEIP.status", instance):    "failed",
					common.ReportSprintf("%s.jobs.UnAssociateEIP.is_err", instance):    true,
				})
				return err
			}
		}
	}

	for _, instanceID := range existsInstances {
		reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
			common.ReportSprintf("%s.last_time", instanceID): time.Now().Unix(),
			common.ReportSprintf("%s.status", instanceID):    "processing",
		})
	}
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
	time.Sleep(5 * time.Second)
	a.logger.Infof("开始执行删除操作，每批10台，5秒一批")
	// 开始执行
	errFlag := false
	step := 10
	for i := 0; i < len(existsInstances); i += step {
		deleteBatch := existsInstances[i:min(len(existsInstances), i+step)]
		a.logger.Infof("准备释放阿里云主机实例: %v", deleteBatch)
		err := a.client.DeleteInstances(ctx, deleteBatch, form.Force)
		if err != nil {
			for _, instanceID := range deleteBatch {
				reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
					common.ReportSprintf("%s.last_time", instanceID): time.Now().Unix(),
					common.ReportSprintf("%s.status", instanceID):    "delete_error",
					common.ReportSprintf("%s.is_err", instanceID):    true,
				})
			}
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, nil)
			a.logger.Errorf("共计%d个实例删除失败:%s，删除流程继续执行", len(deleteBatch), err.Error())
			errFlag = true
			continue
		}
		a.logger.Infof("共计%d个实例删除成功", len(deleteBatch))
		for _, instanceID := range deleteBatch {
			reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
				common.ReportSprintf("%s.last_time", instanceID):  time.Now().Unix(),
				common.ReportSprintf("%s.status", instanceID):     "success",
				common.ReportSprintf("%s.is_success", instanceID): true,
			})
		}
		time.Sleep(5 * time.Second)
	}

	if errFlag {
		return fmt.Errorf("删除操作部分失败，请检查子任务详情")
	}
	return nil
}

func (a AliEcs) UnassociateAndResetEIP(ctx context.Context, orderID, eipID, instanceID, reportStore string) (string, error) {
	reportStore = common.ReportOrderStatusDetailKv(ctx, orderID, false, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", instanceID):                 time.Now().Unix(),
		common.ReportSprintf("%s.jobs.ReleaseEIP.last_time", instanceID): time.Now().Unix(),
		common.ReportSprintf("%s.jobs.ReleaseEIP.status", instanceID):    "started",
	})
	err := a.client.UnassociateEipAddress(ctx, eipID, instanceID, "EcsInstance")
	if err != nil {
		return reportStore, err
	}
	common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", instanceID):                 time.Now().Unix(),
		common.ReportSprintf("%s.jobs.ReleaseEIP.last_time", instanceID): time.Now().Unix(),
		common.ReportSprintf("%s.jobs.ReleaseEIP.status", instanceID):    "unassociated",
	})

	err = a.client.SetEIPName(ctx, eipID, eipID)
	if err != nil {
		return reportStore, err
	}
	err = a.client.UnTagResource(ctx, eipID, "EIP", true, nil)
	if err != nil {
		return reportStore, err
	}
	// 如果EIP在带宽包中，将其移出
	resp, total, err := models.EipModel.Query(ctx, &schema.EipQueryParams{
		EipColumnParam: schema.EipColumnParam{
			AllocationID: eipID,
		},
	})
	if total != 1 {
		return reportStore, errors.New("cannot find eip in mongo")
	}
	if resp[0].BandwidthPackageID != "" {
		// 移出带宽包前判断下EIP状态
		err = a.client.WaitEipAvailable(ctx, eipID)
		if err != nil {
			return reportStore, err
		}
		err = a.client.RemoveCommonBandwidthPackageIp(ctx, eipID, resp[0].BandwidthPackageID)
		if err != nil {
			return reportStore, err
		}
	}

	return common.ReportOrderStatusDetailKv(ctx, orderID, true, reportStore, common.ReportKv{
		common.ReportSprintf("%s.last_time", instanceID):                  time.Now().Unix(),
		common.ReportSprintf("%s.jobs.ReleaseEIP.last_time", instanceID):  time.Now().Unix(),
		common.ReportSprintf("%s.jobs.ReleaseEIP.status", instanceID):     "reset",
		common.ReportSprintf("%s.jobs.ReleaseEIP.is_success", instanceID): true,
	}), nil
}

// TestPing 测试连通性
func (a AliEcs) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	var err error
	if kmsName != "" {
		kmsClient, ok := kms.Clients["cloudman"]
		if !ok {
			return nil, errors.New("cannot get kms client: 'cloudman'")
		}
		ak, sk, err = kmsClient.GetRealAK(kmsName, ak, sk)
		if err != nil {
			return nil, err
		}
	}
	//a.client.SetTimeout(timeout)
	resp := &ecs20140526.DescribeRegionsResponse{}
	err = agentsdk.SyncCallWithAKSK(ctx, a.client.regionID, "host", "DescribeRegions", "aliyun", ak, sk, false, []interface{}{&ecs20140526.DescribeRegionsRequest{
		AcceptLanguage: tea.String("zh-CN"),
	}}, resp)
	if err != nil {
		return nil, err
	}
	//res, err := a.client.GetRegions(ctx)
	//if err != nil {
	//	return nil, err
	//}

	var region []*common.Region
	for _, v := range resp.Body.Regions.Region {
		region = append(region, &common.Region{
			Name:     tea.StringValue(v.LocalName),
			RegionID: tea.StringValue(v.RegionId),
			IspType:  "aliyun",
		})
	}

	return region, nil
}

// GetSnapshotCount ...
func (a AliEcs) GetSnapshotCount(ctx context.Context) (int32, error) {
	var pageSize, page, totalCount int32 = 1, 1, 0
	body, err := a.client.DescribeInstances(ctx, page, pageSize, "")
	if err != nil {
		return 0, err
	}
	totalCount = tea.Int32Value(body.TotalCount)
	return totalCount, nil
}

// ResourceAliyunInstance 初始化aliecs
func ResourceAliyunInstance(regionID, accountID string, logger *logrus.Logger, dry bool) (*AliEcs, error) {
	if regionID == "" {
		regionID = "cn-hangzhou"
	}

	client, err := CreateAliEcsClient(regionID, accountID)
	if err != nil {
		return nil, err
	}

	if logger == nil {
		return nil, errors.New("logger object is null")
	}

	return &AliEcs{client: client, logger: logger, dryRun: dry, accountID: accountID}, err
}
