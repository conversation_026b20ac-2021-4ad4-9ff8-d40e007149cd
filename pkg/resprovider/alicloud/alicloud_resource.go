package alicloud

import (
	"slices"

	resourcemanager******** "github.com/alibabacloud-go/resourcemanager-********/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// AliResourceClient ...
type AliResourceClient struct {
	regionID  string
	accountID string
}

// NewAliResourceClient ...
func NewAliResourceClient(regionID, accountID string) (*AliResourceClient, error) {
	return &AliResourceClient{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

func checkResourceGroupsTag(tags []*resourcemanager********.ListResourceGroupsResponseBodyResourceGroupsResourceGroupTagsTag) bool {
	systemConfig := cfg.GetSystemConfig()
	if systemConfig.SyncEnv == "" {
		return true
	}

	envTag := ""
	for _, tag := range tags {
		if tea.StringValue(tag.TagKey) == "env" {
			envTag = tea.StringValue(tag.TagValue)
			break
		}
	}
	switch systemConfig.SyncEnv {
	case cfg.SyncEnvProd:
		return slices.Contains(constant.EnvProdTags, envTag)
	case cfg.SyncEnvTest:
		return slices.Contains(constant.EnvTestTags, envTag)
	default:
		return true
	}
}

// ListResourceGroups ...
func (c *AliResourceClient) ListResourceGroups(ctx context.Context) ([]*resourcemanager********.ListResourceGroupsResponseBodyResourceGroupsResourceGroup, error) {
	req := &resourcemanager********.ListResourceGroupsRequest{}
	req.SetPageSize(100)
	req.SetStatus("OK")
	req.SetIncludeTags(true)
	totalCount := int32(0)
	currentNum := int32(0)
	result := make([]*resourcemanager********.ListResourceGroupsResponseBodyResourceGroupsResourceGroup, 0)
	for i := 1; i <= 10; i++ {
		res := &resourcemanager********.ListResourceGroupsResponse{}
		req.SetPageNumber(int32(i))
		err := agentsdk.SyncCall(ctx, c.regionID, "resource", "ListResourceGroups", c.accountID, []interface{}{req}, res)
		if err != nil {
			return nil, err
		}
		if totalCount == 0 {
			totalCount = tea.Int32Value(res.Body.TotalCount)
		}
		for _, resourceGroups := range res.Body.ResourceGroups.ResourceGroup {
			if !checkResourceGroupsTag(resourceGroups.Tags.Tag) {
				continue
			}
			result = append(result, resourceGroups)
		}
		currentNum += tea.Int32Value(res.Body.PageSize)
		if currentNum >= totalCount {
			break
		}
	}

	return result, nil
}
