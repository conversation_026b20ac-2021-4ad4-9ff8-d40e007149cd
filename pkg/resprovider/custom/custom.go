package custom

import (
	"fmt"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"

	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// Custom 自定义资源
type Custom struct {
	logger    *logrus.Logger
	accountID string
	regionID  string
	dryRun    bool
}

// CreateInstance 创建实例
func (c Custom) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	// panic("implement me")
	return nil, nil
}

// UpdateInstance 更新实例
func (c Custom) UpdateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) error {
	return nil
}

// DeleteInstance 删除实例
func (c Custom) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	c.logger.Infoln("释放自定义厂商实例成功")
	return nil
}

// TestPing 测试连通性
func (c Custom) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	return nil, nil
}

// GetSnapshotCount ...
func (c Custom) GetSnapshotCount(ctx context.Context) (int32, error) {
	adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	filter := models.HostDefaultFilterMap(adminCtx, map[string]interface{}{
		"isp_id":   c.accountID,
		"RegionID": c.regionID,
	})
	count, err := models.HostResourceModel.Count(adminCtx, filter)
	if err != nil {
		return 0, fmt.Errorf("get host count error: %s, %s", c.accountID, c.regionID)
	}
	return int32(count), nil
}

// ResourceCustomInstance init-resource
func ResourceCustomInstance(regionID, accountID string, logger *logrus.Logger, dry bool) *Custom {
	return &Custom{logger: logger, regionID: regionID, accountID: accountID, dryRun: dry}
}
