package common

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
)

// ReportOrderStatusDetail ...
func ReportOrderStatusDetail(ctx context.Context, orderID string, doReport bool, store string, key string, value interface{}) string {
	result := store
	if key != "" {
		newJSON, err := sjson.Set(result, key, value)
		if err != nil {
			return store
		}
		result = newJSON
	}
	if doReport {
		err := models.ResourceOrder.UpdateStatusDetail(ctx, orderID, result)
		if err != nil {
			logger.Warnf("write backup_detail failed: %s", err.Error())
		}
	}
	return result
}

// ResetOrderStatusDetail -
func ResetOrderStatusDetail(ctx context.Context, orderID string, doReport bool, store string, key string) string {
	result := store
	if key != "" {
		newJSON, err := sjson.Delete(result, key)
		if err != nil {
			return store
		}
		result = newJSON
	}
	if doReport {
		err := models.ResourceOrder.UpdateStatusDetail(ctx, orderID, result)
		if err != nil {
			logger.Warnf("write backup_detail failed: %s", err.Error())
		}
	}
	return result
}

// ReportKv ...
type ReportKv map[string]interface{}

// ReportOrderStatusDetailKv ...
func ReportOrderStatusDetailKv(ctx context.Context, orderID string, doReport bool, store string, kv ReportKv) string {
	result := store
	if len(kv) > 0 {
		for k, v := range kv {
			newJSON, err := sjson.Set(result, k, v)
			if err != nil {
				return store
			}
			result = newJSON
		}
	}
	if doReport {
		err := models.ResourceOrder.UpdateStatusDetail(ctx, orderID, result)
		if err != nil {
			logger.Warnf("write backup_detail failed: %s", err.Error())
		}
	}
	return result
}

// GetOrderStatusDetail ...
func GetOrderStatusDetail(ctx context.Context, orderID string) (string, error) {
	order, err := models.ResourceOrder.Get(ctx, orderID)
	if err != nil {
		return "", err
	}
	return order.StatusDetail, nil
}

// ReportSprintf -
func ReportSprintf(format string, args ...interface{}) string {
	argsNew := []interface{}{}
	for _, a := range args {
		if s, ok := a.(string); ok {
			// 含有半角点 . 的字符串，要进行转译以免被当作层级
			argsNew = append(argsNew, strings.Replace(s, `.`, `\.`, -1))
		} else {
			argsNew = append(argsNew, a)
		}
	}
	return fmt.Sprintf(format, argsNew...)
}

// FetchOrderStatusDetail -
func FetchOrderStatusDetail(ctx context.Context, orderID string) (string, error) {
	o, err := models.ResourceOrder.Get(ctx, orderID)
	if err != nil {
		return "", err
	}
	return o.StatusDetail, nil
}

// StatusDetailCount -
func StatusDetailCount(store string) (success []string, failed []string, total int) {
	m := gjson.Parse(store).Map()
	total = len(m)
	for _, r := range m {
		if r.Get("is_err").Bool() {
			failed = append(failed, r.Get("instance_id").String())
		} else if r.Get("is_success").Bool() {
			success = append(success, r.Get("instance_id").String())
		}
	}
	return
}

// StatusDetailKeyExists ...
func StatusDetailKeyExists(store string, key string) bool {
	return gjson.Get(store, key).Exists()
}

type ReportInstanceOrderStatus struct {
	OrderID     string
	isDryRun    bool
	startTime   int64
	reportStore string
}

func NewReportInstanceOrderStatus(orderID string, isDryRun bool) *ReportInstanceOrderStatus {
	return &ReportInstanceOrderStatus{
		OrderID:     orderID,
		isDryRun:    isDryRun,
		reportStore: "",
	}
}

func (r *ReportInstanceOrderStatus) ReportStart(ctx context.Context, instanceName string) {
	if r.isDryRun {
		return
	}
	r.startTime = time.Now().Unix()
	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.start_time", instanceName):    r.startTime,
		ReportSprintf("%s.instance_name", instanceName): instanceName,
		ReportSprintf("%s.status", instanceName):        "preparing",
	})
}

func (r *ReportInstanceOrderStatus) ReportFailed(ctx context.Context, instanceName string) {
	if r.isDryRun {
		return
	}
	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
		ReportSprintf("%s.status", instanceName):    "failed",
		ReportSprintf("%s.is_err", instanceName):    true,
	})

}

func (r *ReportInstanceOrderStatus) ReportCustomStatus(ctx context.Context, instanceName string, status string) {
	if r.isDryRun {
		return
	}
	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.last_time", instanceName): time.Now().Unix(),
		ReportSprintf("%s.status", instanceName):    status,
	})
}

func (r *ReportInstanceOrderStatus) ReportInstanceId(ctx context.Context, instanceName, instanceId string) {
	if r.isDryRun {
		return
	}
	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.instance_id", instanceName): instanceId,
	})
}

func (r *ReportInstanceOrderStatus) ReportSuccess(ctx context.Context, instanceName string) {
	if r.isDryRun {
		return
	}
	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.last_time", instanceName):  time.Now().Unix(),
		ReportSprintf("%s.status", instanceName):     "success",
		ReportSprintf("%s.is_success", instanceName): true,
	})
}

func (r *ReportInstanceOrderStatus) ReportJobStart(ctx context.Context, instanceName, jobName string) {
	if r.isDryRun {
		return
	}
	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.last_time", instanceName):                  time.Now().Unix(),
		ReportSprintf("%s.jobs.%s.last_time", instanceName, jobName): time.Now().Unix(),
		ReportSprintf("%s.jobs.%s.status", instanceName, jobName):    "preparing",
	})
}

func (r *ReportInstanceOrderStatus) ReportJobFailed(ctx context.Context, instanceName, jobName string) {
	if r.isDryRun {
		return
	}

	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.jobs.%s.last_time", instanceName, jobName): time.Now().Unix(),
		ReportSprintf("%s.jobs.%s.status", instanceName, jobName):    "failed",
		ReportSprintf("%s.jobs.%s.is_err", instanceName, jobName):    true,
		ReportSprintf("%s.is_err", instanceName):                     true,
		ReportSprintf("%s.status", instanceName):                     "failed",
		ReportSprintf("%s.last_time", instanceName):                  time.Now().Unix(),
	})
}

func (r *ReportInstanceOrderStatus) ReportJobSuccess(ctx context.Context, instanceName, jobName string) {
	if r.isDryRun {
		return
	}

	r.reportStore = ReportOrderStatusDetailKv(ctx, r.OrderID, true, r.reportStore, ReportKv{
		ReportSprintf("%s.jobs.%s.last_time", instanceName, jobName):  time.Now().Unix(),
		ReportSprintf("%s.jobs.%s.status", instanceName, jobName):     "finished",
		ReportSprintf("%s.jobs.%s.is_success", instanceName, jobName): true,
		ReportSprintf("%s.last_time", instanceName):                   time.Now().Unix(),
	})
}
