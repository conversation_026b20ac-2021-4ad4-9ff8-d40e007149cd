package common

// Tag 通用标签
type Tag struct {
	Key   string
	Value string
}

// UpdateInstanceForm 更新实例表单
type UpdateInstanceForm struct {
	Action      string   `json:"action"`
	Instances   []string `json:"instances" field:"实例列表"`
	CallbackURL string   `json:"callback_url"`
	IsForce     bool     `json:"is_force"`
}

// DeleteInstanceForm 删除实例
type DeleteInstanceForm struct {
	Force     bool     `json:"force" field:"强制删除"`
	Instances []string `json:"instances" field:"实例列表"`
}

// BackUpInstanceForm 备份实例表单
type BackUpInstanceForm struct {
	Status    string   `json:"status" field:"状态"`
	Instances []string `json:"instances" field:"实例列表"`
}

// OperateForm 操作类通用表单
type OperateForm struct {
	Action         string   `json:"action" field:"动作"`
	Instances      []string `json:"instances" field:"实例"`
	RetryInstances []string `json:"retry_instances"`
	IsForce        bool     `json:"is_force"`
	IsLevel2Backup *bool    `json:"is_level2_backup"` // 是否二级备份
}

type ActionData struct {
	IsForce        bool  `json:"is_force"`
	IsLevel2Backup *bool `json:"is_level2_backup"` // 是否二级备份
}
