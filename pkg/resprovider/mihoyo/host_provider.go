package mihoyo

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mihoyosdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

// Host mhy计算平台操作类，实现操作通用接口
type Host struct {
	logger    *logrus.Logger
	regionID  string
	dryRun    bool
	accountID string
}

// CreateInstance 创建实例
func (m Host) CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) {
	req := CreateInstanceInput{}
	err = json.Unmarshal(data, &req)
	if err != nil {
		return nil, fmt.Errorf("CreateInstance.Unmarshal.failed:%s", err.Error())
	}
	if m.dryRun {
		// todo
		return nil, nil
	}
	successID := []string{}
	for i := 0; i < int(req.Amount); i++ {
		finalHostName := fmt.Sprintf("%s_%d", req.HostName, time.Now().UnixNano())
		mReq := mihoyosdk.HostCreateReq{
			Name:       finalHostName,
			DataCenter: req.RegionID,
			VMType:     req.InstanceTypeDetail.InstanceTypeFamily,
			Resource: mihoyosdk.HostResource{
				CPU:    req.InstanceTypeDetail.CPUCoreCount,
				Memory: req.InstanceTypeDetail.MemorySize,
				Gpu:    0,
			},
			ImageID:  req.ImageID,
			Volumes:  nil,
			Password: req.Password,
		}
		mReq.Volumes = append(mReq.Volumes, mihoyosdk.HostVolume{
			Arch:       "local",
			IsBootDisk: true,
			Size:       req.SystemDisk.Size,
			Type:       "ssd",
		})
		if len(req.DataDisk) != 0 {
			mReq.Volumes = append(mReq.Volumes, mihoyosdk.HostVolume{
				Arch:       "local",
				IsBootDisk: false,
				Size:       req.DataDisk[0].Size,
				Type:       "ssd",
			})
		}

		m.logger.Infof("mihoyosdk.CreateInstance.%d.dump:%+v", i, mReq)
		instanceID := ""
		err := agentsdk.SyncCall(ctx, "", "host", "CreateVM", m.accountID, []interface{}{mReq}, &instanceID)
		if err != nil {
			m.logger.Errorf("mihoyosdk.CreateInstance.%d.failed:%s", i, err.Error())
			return successID, fmt.Errorf("创建mihoyo虚拟主机，执行第%d时出错：%s", i, err.Error())
		}
		m.logger.Infof("mihoyosdk.CreateInstance.%d.ok:%s", i, instanceID)
		successID = append(successID, instanceID)
	}
	m.logger.Infof("wait 120 second for instance start")
	time.Sleep(120 * time.Second)
	return successID, nil
}

// UpdateInstance 更新实例
func (m Host) UpdateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) error {
	if m.dryRun {
		return nil
	}
	var form common.UpdateInstanceForm
	err := json.Unmarshal(data, &form)
	if err != nil {
		return err
	}

	for _, item := range form.Instances {
		switch form.Action {
		case "reboot":
			m.logger.Infof("实例%s开始重启", item)
			err := agentsdk.SyncCall(ctx, "", "host", "OperateVM", m.accountID, []interface{}{item, "reboot"})
			if err != nil {
				m.logger.Errorf("实例%s重启失败：%s", item, err.Error())
				return err
			}
			m.logger.Infof("实例%s重启成功", item)
		case "start":
			m.logger.Infof("实例%s开始启动", item)
			err := agentsdk.SyncCall(ctx, "", "host", "OperateVM", m.accountID, []interface{}{item, "poweron"})
			if err != nil {
				m.logger.Errorf("实例%s启动失败：%s", item, err.Error())
				return err
			}
			m.logger.Infof("实例%s启动成功", item)
		case "stop":
			m.logger.Infof("实例%s开始关机", item)
			err := agentsdk.SyncCall(ctx, "", "host", "OperateVM", m.accountID, []interface{}{item, "shutdown"})
			if err != nil {
				m.logger.Errorf("实例%s关机失败：%s", item, err.Error())
				return err
			}
			m.logger.Infof("实例%s关机成功", item)
		}
	}
	return nil
}

// DeleteInstance 销毁实例
func (m Host) DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error {
	if m.dryRun {
		// todo
		return nil
	}
	for _, instance := range form.Instances {
		err := agentsdk.SyncCall(ctx, "", "host", "DestroyVM", m.accountID, []interface{}{instance})
		if err != nil {
			m.logger.Errorf("mihoyosdk.DeleteInstance.%s.failed:%s", instance, err.Error())
			return err
		}
		m.logger.Infof("mihoyosdk.DeleteInstance.%s.ok", instance)
	}
	return nil
}

// TestPing ping
func (m Host) TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error) {
	defaultRegion := &common.Region{
		Name:     "上海（松江）",
		RegionID: "shanghai",
		IspType:  "mihoyo",
		Endpoint: "",
	}
	return []*common.Region{defaultRegion}, nil
}

// GetSnapshotCount ...
func (m Host) GetSnapshotCount(ctx context.Context) (int32, error) {
	var pageSize, currentPage int64 = 100, 1
	var totalCount = 0
	for {
		var hosts []*mihoyosdk.Host
		err := agentsdk.SyncCall(ctx, "", "host", "ListVms", m.accountID, []interface{}{pageSize, currentPage}, &hosts)
		if err != nil {
			return 0, err
		}
		if hosts == nil {
			return 0, errors.New("nil hosts from mihoyo")
		}
		if len(hosts) == 0 {
			break
		}
		totalCount += len(hosts)
		currentPage++
	}
	return int32(totalCount), nil
}

// ResourceMihoyoInstance 新建mhy操作接口实例
func ResourceMihoyoInstance(regionID, accountID string, logger *logrus.Logger, dry bool) (*Host, error) {
	if logger == nil {
		return nil, fmt.Errorf("logger object is null")
	}

	return &Host{logger: logger, regionID: regionID, dryRun: dry, accountID: accountID}, nil
}

// CreateInstanceInput 处理创建实例时的前端输入
type CreateInstanceInput struct {
	OsType             string `json:"os_type"`
	ImageID            string `json:"ImageID"`
	InstanceType       string `json:"InstanceType"`
	InstanceTypeDetail struct {
		CPUCoreCount       int64  `json:"CPUCoreCount"`
		MemorySize         int64  `json:"MemorySize"`
		InstanceTypeFamily string `json:"InstanceTypeFamily"`
	} `json:"InstanceTypeDetail"`
	LoginType  string `json:"LoginType"`
	Password   string `json:"Password"`
	SystemDisk struct {
		Size int64 `json:"Size"`
	} `json:"SystemDisk"`
	DataDisk []struct {
		Size int64 `json:"Size"`
	} `json:"DataDisk"`
	UserData   string `json:"UserData"`
	HostName   string `json:"HostName"`
	Reason     string `json:"reason"`
	RegionID   string `json:"RegionID"`
	RegionName string `json:"RegionName"`
	Amount     int64  `json:"Amount"`
}
