package resprovider

import (
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/jumpserver"

	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/aws"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/custom"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/mihoyo"
)

var aliRDS = func(i common.InitProvider, dryRun ...bool) (Provider, error) {
	isDry := false
	if len(dryRun) != 0 {
		isDry = dryRun[0]
	}
	return alicloud.ResourceAliyunPoldbInstance(i.RegionID, i.IspID, i.Logger, isDry)
}

var awsRDS = func(i common.InitProvider, dryRun ...bool) (Provider, error) {
	isDry := false
	if len(dryRun) != 0 {
		isDry = dryRun[0]
	}
	return aws.GetRDS(i.RegionID, i.IspID, i.Logger, isDry)
}

// ProvideMap isp
var ProvideMap = map[string]ProviderFunc{
	"aliyun_host": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return alicloud.ResourceAliyunInstance(i.RegionID, i.IspID, i.Logger, isDry)
	},
	"custom_host": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return custom.ResourceCustomInstance(i.RegionID, i.IspID, i.Logger, isDry), nil
	},
	"aws_host": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return aws.GetHostProvider(i.RegionID, i.IspID, i.Logger, isDry)
	},
	"mihoyo_host": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return mihoyo.ResourceMihoyoInstance(i.RegionID, i.IspID, i.Logger, isDry)
	},
	"jumpserver_host": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return jumpserver.ResourceJumpserverInstance(i.RegionID, i.IspID, i.Host, i.Logger, isDry)
	},
	//"aliyun_polardb": aliRDS,
	//"aliyun_rds":     aliRDS,
	"aliyun_mysql": aliRDS,
	"aliyun_redis": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return alicloud.ResourceKvStore(i.RegionID, i.IspID, i.Logger, isDry)
	},
	"aws_redis": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return aws.GetElastiCache(i.RegionID, i.IspID, i.Logger, isDry)
	},
	//"aws_rds":     awsRDS,
	//"aws_polardb": awsRDS,
	"aws_mysql": awsRDS,
	"aliyun_lb": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return alicloud.ResourceLb(i.RegionID, i.IspID, i.Logger, isDry)
	},
	"aws_lb": func(i common.InitProvider, dryRun ...bool) (Provider, error) {
		isDry := false
		if len(dryRun) != 0 {
			isDry = dryRun[0]
		}
		return aws.NewLoadBalancerProvider(i.RegionID, i.IspID, i.Logger, isDry), nil
	},
}

// ProviderFunc isp func
type ProviderFunc func(prov common.InitProvider, dryRun ...bool) (Provider, error)

// HostDescribeProvider 主机描述provider
type HostDescribeProvider interface {
	GetImages() error        // 获取支持的镜像列表
	GetNetworks() error      // 获取网络
	GetSecurityGroup() error // 获取网络
}

// Provider 主机资源Provider
type Provider interface {
	CreateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) (instances []string, err error) // 创建资源
	UpdateInstance(ctx context.Context, duration time.Duration, orderID string, data []byte) error                           // 修改资源
	DeleteInstance(ctx context.Context, duration time.Duration, orderID string, form common.DeleteInstanceForm) error        // 删除资源
	TestPing(ctx context.Context, kmsName, ak, sk string, timeout time.Duration) ([]*common.Region, error)                   // 测试连通性
	GetSnapshotCount(ctx context.Context) (int32, error)                                                                     // 每日快照
}
