package notification

import (
	"encoding/json"
	"errors"
)

type SendHoyowaveAppNotificationReq struct {
	ReceiverId     string `json:"receiver_id"`
	ReceiverIdType string `json:"receiver_id_type"`
	MsgType        string `json:"msg_type"`
	Content        string `json:"content"`
}

type SendHoyowaveAppNotificationRes struct {
	Status int `json:"Status"` // 状态码
}

var ErrorHoyowaveAppSend = errors.New("hoyowave app send error")

type Element interface {
	GetTag() string
}

type HeaderTemplate string

const (
	HeaderTemplateDefault HeaderTemplate = "default"
	HeaderTemplateInfo    HeaderTemplate = "info"
	HeaderTemplateSuccess HeaderTemplate = "success"
	HeaderTemplateWarning HeaderTemplate = "warning"
	HeaderTemplateDanger  HeaderTemplate = "danger"
	HeaderTemplateInvalid HeaderTemplate = "invalid"
)

type Header struct {
	Title    string          `json:"title"`
	Template *HeaderTemplate `json:"template,omitempty"`
}

func NewHeader(title string, template HeaderTemplate) *Header {
	return &Header{
		Title:    title,
		Template: &template,
	}
}

type PlainText struct {
	Tag  string `json:"tag"`
	Text string `json:"text"`
}

func (c *PlainText) GetTag() string {
	return "plain_text"
}

func NewPlainText(text string) *PlainText {
	return &PlainText{
		Tag:  "plain_text",
		Text: text,
	}
}

type Markdown struct {
	Tag  string `json:"tag"`
	Text string `json:"text"`
}

func (c *Markdown) GetTag() string {
	return "markdown"
}

func NewMarkDown(text string) *Markdown {
	return &Markdown{
		Tag:  "markdown",
		Text: text,
	}
}

type LayoutType string

const (
	LayoutTypeFlow      LayoutType = "flow"
	LayoutTypeColumn    LayoutType = "column"
	LayoutTypeColumnSet LayoutType = "column_set"
)

type Ratio string

const (
	LayoutRatioBisect         Ratio = "1:1"
	LayoutRatioTrisectEqual   Ratio = "1:1:1"
	LayoutRatioBisecHemisect  Ratio = "2:1"
	LayoutRatioHemisectBisect Ratio = "1:2"
)

type Layout struct {
	Layout   LayoutType `json:"tag"`
	Elements []Element  `json:"elements"`
	Ratio    Ratio      `json:"ratio,omitempty"`
}

func (c *Layout) GetTag() string {
	return string(c.Layout)
}

type Card struct {
	Header *Header `json:"header"`
	Layout *Layout `json:"card"`
}

func NewColumnCard(header *Header, elements []Element) Card {
	return Card{
		Header: header,
		Layout: &Layout{
			Layout:   LayoutTypeColumn,
			Elements: elements,
		},
	}
}

type JoinChatReq struct {
	ChatId  string   `json:"chat_id"`
	UidList []string `json:"uid_list"`
}

type JoinChatRes struct {
	ChatId string `json:"chat_id"`
}

type BaseResponse[E any] struct {
	Retcode int    `json:"retcode,omitempty"`
	Code    int    `json:"code,omitempty"`
	Message string `json:"message,omitempty"`
	Data    E      `json:"data,omitempty"`
	Msg     string `json:"msg,omitempty" redis:"msg"`
	Count   int    `json:"count,omitempty" redis:"count"`
}

func (p *BaseResponse[T]) MarshalBinary() ([]byte, error) {
	return json.Marshal(p)
}

func (p *BaseResponse[T]) UnMarshalBinary(buff []byte) error {
	return json.Unmarshal(buff, p)
}

const (
	RetCodeSuccess = 0
	RetCodeFailed  = 1
)

type Option interface {
	GetText() string
	SetText(string)
	IsDisable() bool
	SetDisableOption(bool)
	IsNotMarkAsRead() bool
	SetNotMarkAsRead(bool)
}
type OptionFn func(Option)

type CardButtonStyle string

const (
	ButtonStyleDefault     CardButtonStyle = "default"
	ButtonStylePrimary     CardButtonStyle = "primary"
	ButtonStyleDanger      CardButtonStyle = "danger"
	ButtonStyleWarning     CardButtonStyle = "warning"
	ButtonStyleSuccess     CardButtonStyle = "success"
	ButtonStylePrimaryText CardButtonStyle = "primary_text"
)

type Button struct {
	Tag    string           `json:"tag"`
	Text   string           `json:"text"`
	Style  *CardButtonStyle `json:"style,omitempty"`
	Option Option           `json:"option"`
}

func (b *Button) GetTag() string {
	return "button"
}

type ButtonFn func(*Button)

func WithButtonStyle(style CardButtonStyle) ButtonFn {
	return func(button *Button) {
		button.Style = &style
	}
}

func NewButton(text string, option Option, fns ...ButtonFn) *Button {
	res := &Button{
		Text:   text,
		Option: option,
	}

	for _, fn := range fns {
		fn(res)
	}
	res.Tag = res.GetTag()
	return res
}

type URLOption struct {
	Tag           string    `json:"tag"`
	Text          *string   `json:"text,omitempty"`
	DisableOption *bool     `json:"disable_option,omitempty"`
	MultiURL      *MultiURL `json:"multi_url"`
	NotMarkAsRead *bool     `json:"not_mark_as_read,omitempty"`
}

func (o *URLOption) SetNotMarkAsRead(b bool) {
	o.NotMarkAsRead = &b
}

func (o *URLOption) IsNotMarkAsRead() bool {
	if o.NotMarkAsRead == nil {
		return false
	}
	return *o.NotMarkAsRead
}

func (o *URLOption) GetText() string {
	if o.Text == nil {
		return ""
	}
	return *o.Text
}

func (o *URLOption) SetText(s string) {
	o.Text = &s
}

func (o *URLOption) IsDisable() bool {
	if o.DisableOption == nil {
		return false
	}
	return *o.DisableOption
}

func (o *URLOption) SetDisableOption(b bool) {
	o.DisableOption = &b
}

func (o *URLOption) GetTag() string {
	return "url"
}

func NewURLOption(url *MultiURL, fns ...OptionFn) *URLOption {
	res := &URLOption{MultiURL: url}
	for _, optionFn := range fns {
		optionFn(res)
	}
	res.Tag = res.GetTag()

	return res
}

type MultiURL struct {
	URL        string  `json:"url"`
	IosURL     *string `json:"ios_url,omitempty"`
	AndroidURL *string `json:"android_url,omitempty"`
	MacURL     *string `json:"mac_url,omitempty"`
	WinURL     *string `json:"win_url,omitempty"`
}
