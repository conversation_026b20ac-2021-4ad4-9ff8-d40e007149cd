package notification

import (
	"encoding/json"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/rest"
)

type Client struct {
	client *rest.Client
	appKey string
}

func New(url, token, appKey string) *Client {
	return &Client{
		client: rest.NewClient(url).WithToken(token),
		appKey: appKey,
	}
}
func (c *Client) SendHoyowaveCard(receiverId, receiverIdType string, card Card) error {
	uri := fmt.Sprintf("hoyowave/app/%s/message/send", c.appKey)
	content, err := json.Marshal(card)
	if err != nil {
		return err
	}
	req := &SendHoyowaveAppNotificationReq{
		ReceiverId:     receiverId,
		ReceiverIdType: receiverIdType,
		MsgType:        "card",
		Content:        string(content),
	}
	reply := new(BaseResponse[SendHoyowaveAppNotificationRes])

	err = c.client.Post(uri, req).Into(reply)
	if err != nil {
		return err
	}
	if reply.Code != RetCodeSuccess {
		return ErrorHoyowaveAppSend
	}
	if reply.Data.Status != 0 {
		return ErrorHoyowaveAppSend
	}
	return nil
}
