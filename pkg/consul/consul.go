package consul

import (
	"fmt"
	consulApi "github.com/hashicorp/consul/api"
	"net"
	"os"
	"time"
)

var client *consulApi.Client
var config *consulApi.Config

// Option consul-api-option
type Option func(c *consulApi.Config)

// NewClient 新建consul-client
func NewClient(address string, opts ...Option) (*consulApi.Client, error) {
	config = consulApi.DefaultConfig()
	config.Address = address
	config.WaitTime = 4 * time.Second
	for _, o := range opts {
		o(config)
	}
	return consulApi.NewClient(config)
}

// NewGlobalClient 新建global-consul-client
func NewGlobalClient(address string, opts ...Option) (err error) {
	client, err = NewClient(address, opts...)
	return err
}

// WithAuth 增加账号密码认证选项
func WithAuth(username, password string) Option {
	return func(c *consulApi.Config) {
		c.HttpAuth = &consulApi.HttpBasicAuth{
			Username: username,
			Password: password,
		}
	}
}

// WithToken 增加token认证选项
func WithToken(token string) Option {
	return func(c *consulApi.Config) {
		c.Token = token
	}
}

// LocalIP 获取本机IP
func LocalIP() string {
	address, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, addr := range address {
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				return ipNet.IP.String()
			}
		}
	}
	return ""
}

// LocalHostname 获取本机hostname
func LocalHostname() string {
	hostname, err := os.Hostname()
	if err != nil {
		return ""
	}
	return hostname
}

// GetNodeName 获取node-name
func GetNodeName() string {
	// service, _, err := client.Catalog().Service("", "", nil)
	//
	// if err != nil {
	// 	return ""
	// }
	return ""
}

// HTTPCheck consul http类
type HTTPCheck struct {
	ServerID   string
	ServerName string
	ServerPort int    // 服务端口
	Timeout    string // 健康检查超时时间
	Interval   string // 健康检查执行间隔
	Expired    string // 检查失败后服务删除时间
}

// RegisterHTTPServer 注册http服务
func RegisterHTTPServer(check HTTPCheck, tags ...string) error {
	registration := new(consulApi.AgentServiceRegistration)
	registration.ID = check.ServerID
	registration.Name = check.ServerName
	registration.Port = check.ServerPort
	registration.Tags = tags
	registration.Address = LocalIP()

	registration.Check = &consulApi.AgentServiceCheck{
		HTTP:                           fmt.Sprintf("http://%s:%d/ping", registration.Address, registration.Port),
		Timeout:                        check.Timeout,
		Interval:                       check.Interval,
		DeregisterCriticalServiceAfter: check.Expired,
	}

	err := client.Agent().ServiceRegister(registration)
	if err != nil {
		return err
	}
	return nil
}

// TakumiGrpcCheck takumi类服务
type TakumiGrpcCheck struct {
	ServerID   string
	ServerName string
	ServerIP   string
	ServerPort int    // 服务端口
	Timeout    string // 健康检查超时时间
	Interval   string // 健康检查执行间隔
	Expired    string // 检查失败后服务删除时间
}

// RegisterTakumiGrpcServer 注册takumi服务
func RegisterTakumiGrpcServer(check TakumiGrpcCheck, tags ...string) error {
	registration := new(consulApi.AgentServiceRegistration)
	registration.ID = check.ServerID
	registration.Name = check.ServerName
	registration.Port = check.ServerPort
	registration.Tags = tags
	registration.Address = check.ServerIP

	registration.Check = &consulApi.AgentServiceCheck{
		GRPC:                           fmt.Sprintf("%s:%d", registration.Address, registration.Port),
		Timeout:                        check.Timeout,
		Interval:                       check.Interval,
		DeregisterCriticalServiceAfter: check.Expired,
	}

	err := client.Agent().ServiceRegister(registration)
	if err != nil {
		return err
	}
	return nil
}
