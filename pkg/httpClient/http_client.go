package httpclient

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"time"
)

// Httpclient httpclient
type Httpclient struct {
	caFile   string
	certFile string
	keyFile  string
	header   map[string]string
	httpCli  *http.Client
}

// NewHttpclient new http-client
func NewHttpclient() *Httpclient {
	return &Httpclient{
		httpCli: &http.Client{},
		header:  make(map[string]string),
	}
}

// JSONReqHeader JSON请求头
var JSONReqHeader = http.Header{
	"Content-type": {"application/json"},
}

// GetClient 获取原client
func (client *Httpclient) GetClient() *http.Client {
	return client.httpCli
}

// SetTLSNoVerity 跳过证书验证
func (client *Httpclient) SetTLSNoVerity() error {
	tlsConf := &tls.Config{
		InsecureSkipVerify: true,
	}

	trans := client.NewTransPort()
	trans.TLSClientConfig = tlsConf
	client.httpCli.Transport = trans

	return nil
}

// SetTLSVerityConfig 指定tls配置
func (client *Httpclient) SetTLSVerityConfig(tlsConf *tls.Config) {
	trans := client.NewTransPort()
	trans.TLSClientConfig = tlsConf
	client.httpCli.Transport = trans
}

// NewTransPort new transport
func (client *Httpclient) NewTransPort() *http.Transport {
	return &http.Transport{
		Proxy:               http.ProxyFromEnvironment,
		TLSHandshakeTimeout: 5 * time.Second,
		Dial: (&net.Dialer{
			Timeout:   5 * time.Second,
			KeepAlive: 30 * time.Second,
		}).Dial,
		ResponseHeaderTimeout: 30 * time.Second,
	}
}

// SetTimeOut 设置超时时间
func (client *Httpclient) SetTimeOut(timeOut time.Duration) {
	client.httpCli.Timeout = timeOut
}

// SetHeader 设置http header
func (client *Httpclient) SetHeader(key, value string) {
	client.header[key] = value
}

// GetHeader Get http header
func (client *Httpclient) GetHeader(key string) string {
	val, _ := client.header[key]
	return val
}

// GET get-request
func (client *Httpclient) GET(url string, header http.Header, data []byte) ([]byte, error) {
	return client.Request(url, "GET", header, data)

}

// GETCtx get-request
func (client *Httpclient) GETCtx(ctx context.Context, url string, header http.Header, data []byte) ([]byte, error) {
	return client.RequestCtx(ctx, url, "GET", header, data)

}

// POST post-request
func (client *Httpclient) POST(url string, header http.Header, data []byte) ([]byte, error) {
	return client.Request(url, "POST", header, data)
}

// POSTCtx post-request
func (client *Httpclient) POSTCtx(ctx context.Context, url string, header http.Header, data []byte) ([]byte, error) {
	return client.RequestCtx(ctx, url, "POST", header, data)
}

// DELETE delete-request
func (client *Httpclient) DELETE(url string, header http.Header, data []byte) ([]byte, error) {
	return client.Request(url, "DELETE", header, data)
}

// PUT put-request
func (client *Httpclient) PUT(url string, header http.Header, data []byte) ([]byte, error) {
	return client.Request(url, "PUT", header, data)
}

// GETEx 扩展get,基于RequestEx实现
func (client *Httpclient) GETEx(url string, header http.Header, data []byte) (int, []byte, error) {
	return client.RequestEx(url, "GET", header, data)
}

// POSTEx 扩展post,基于RequestEx实现
func (client *Httpclient) POSTEx(url string, header http.Header, data []byte) (int, []byte, error) {
	return client.RequestEx(url, "POST", header, data)
}

// DELETEEx 扩展delete,基于RequestEx实现
func (client *Httpclient) DELETEEx(url string, header http.Header, data []byte) (int, []byte, error) {
	return client.RequestEx(url, "DELETE", header, data)
}

// PUTEx 扩展put,基于RequestEx实现
func (client *Httpclient) PUTEx(url string, header http.Header, data []byte) (int, []byte, error) {
	return client.RequestEx(url, "PUT", header, data)
}

// RequestCtx request with ctx
func (client *Httpclient) RequestCtx(ctx context.Context, url, method string, header http.Header, data []byte) ([]byte, error) {
	var req *http.Request
	var errReq error
	if data != nil {
		req, errReq = http.NewRequestWithContext(ctx, method, url, bytes.NewReader(data))
	} else {
		req, errReq = http.NewRequestWithContext(ctx, method, url, nil)
	}

	if errReq != nil {
		return nil, errReq
	}

	req.Close = true

	return client.request(req, header)
}

func (client *Httpclient) request(req *http.Request, header http.Header) ([]byte, error) {
	if header != nil {
		req.Header = header
	}

	for key, value := range client.header {
		req.Header.Set(key, value)
	}

	rsp, err := client.httpCli.Do(req)
	if err != nil {
		return nil, err
	}

	if rsp.StatusCode >= http.StatusBadRequest {
		return nil, fmt.Errorf("statuscode:%d, status:%s", rsp.StatusCode, rsp.Status)
	}

	defer rsp.Body.Close()

	body, err := ioutil.ReadAll(rsp.Body)

	return body, err
}

// Request request
func (client *Httpclient) Request(url, method string, header http.Header, data []byte) ([]byte, error) {
	var req *http.Request
	var errReq error
	if data != nil {
		req, errReq = http.NewRequest(method, url, bytes.NewReader(data))
	} else {
		req, errReq = http.NewRequest(method, url, nil)
	}

	if errReq != nil {
		return nil, errReq
	}

	req.Close = true

	if header != nil {
		req.Header = header
	}

	for key, value := range client.header {
		req.Header.Set(key, value)
	}

	rsp, err := client.httpCli.Do(req)
	if err != nil {
		return nil, err
	}

	if rsp.StatusCode >= http.StatusBadRequest {
		return nil, fmt.Errorf("statuscode:%d, status:%s", rsp.StatusCode, rsp.Status)
	}

	defer rsp.Body.Close()

	body, err := ioutil.ReadAll(rsp.Body)

	return body, err
}

// RequestEx 扩展requestEx
func (client *Httpclient) RequestEx(url, method string, header http.Header, data []byte) (int, []byte, error) {
	var req *http.Request
	var errReq error
	if data != nil {
		req, errReq = http.NewRequest(method, url, bytes.NewReader(data))
	} else {
		req, errReq = http.NewRequest(method, url, nil)
	}

	if errReq != nil {
		return 0, nil, errReq
	}

	req.Close = true

	if header != nil {
		req.Header = header
	}

	for key, value := range client.header {
		req.Header.Set(key, value)
	}

	rsp, err := client.httpCli.Do(req)
	if err != nil {
		return 0, nil, err
	}

	defer rsp.Body.Close()

	body, err := ioutil.ReadAll(rsp.Body)

	return rsp.StatusCode, body, err
}

// DoWithTimeout 超时请求
func (client *Httpclient) DoWithTimeout(timeout time.Duration, req *http.Request) (*http.Response, error) {
	ctx, _ := context.WithTimeout(req.Context(), timeout)
	req = req.WithContext(ctx)
	return client.httpCli.Do(req)
}
