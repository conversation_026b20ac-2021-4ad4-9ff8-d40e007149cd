package utils

import (
	"fmt"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

var hostnameRangeRegex = regexp.MustCompile(`\[(\d{1,}),(\d{1,})\]`)

func IsMatchRange(originalStr string) bool {
	return hostnameRangeRegex.MatchString(originalStr)
}

func GetHostnameRangeRegex(originalStr string) string {
	matchStrs := hostnameRangeRegex.FindStringSubmatch(originalStr)
	if len(matchStrs) != 3 {
		return originalStr
	}
	min, err := strconv.Atoi(matchStrs[1])
	if err != nil {
		return originalStr
	}
	max, err := strconv.Atoi(matchStrs[2])
	if err != nil {
		return originalStr
	}
	// 编号不能超过9999
	if max > 9999 {
		return originalStr
	}
	if max < min {
		max = min
	}
	rangeRegex := RegexForRange(min, max)
	prefix := originalStr[:strings.Index(originalStr, "[")]
	return fmt.Sprintf("%s(%s)$", prefix, rangeRegex)
}

func RegexForRange(min, max int) string {
	var positiveSubpatterns, negativeSubpatterns []string

	if min < 0 {
		minAbs := 1
		if max < 0 {
			minAbs = abs(max)
		}
		maxAbs := abs(min)

		negativeSubpatterns = splitToPatterns(minAbs, maxAbs)
		min = 0
	}

	if max >= 0 {
		positiveSubpatterns = splitToPatterns(min, max)
	}

	negativeOnlySubpatterns := difference(negativeSubpatterns, positiveSubpatterns, "-%s")
	positiveOnlySubpatterns := difference(positiveSubpatterns, negativeSubpatterns, "%s")
	intersectedSubpatterns := intersection(negativeSubpatterns, positiveSubpatterns, "-?%s")

	subpatterns := append(negativeOnlySubpatterns, intersectedSubpatterns...)
	subpatterns = append(subpatterns, positiveOnlySubpatterns...)

	return strings.Join(subpatterns, "|")
}

func splitToPatterns(min, max int) []string {
	var subpatterns []string

	start := min
	for _, stop := range splitToRanges(min, max) {
		subpatterns = append(subpatterns, rangeToPattern(start, stop))
		start = stop + 1
	}

	return subpatterns
}

func splitToRanges(min, max int) []int {
	stops := map[int]struct{}{max: {}}

	ninesCount := 1
	stop := fillByNines(min, ninesCount)
	for min <= stop && stop < max {
		stops[stop] = struct{}{}

		ninesCount++
		stop = fillByNines(min, ninesCount)
	}

	zerosCount := 1
	stop = fillByZeros(max+1, zerosCount) - 1
	for min < stop && stop <= max {
		stops[stop] = struct{}{}

		zerosCount++
		stop = fillByZeros(max+1, zerosCount) - 1
	}

	var sortedStops []int
	for stop := range stops {
		sortedStops = append(sortedStops, stop)
	}
	sort.Ints(sortedStops)

	return sortedStops
}

func fillByNines(integer, ninesCount int) int {
	strInt := strconv.Itoa(integer)
	prefix := ""
	if len(strInt)-ninesCount > 0 {
		prefix = strInt[:len(strInt)-ninesCount]
	}
	return toInt(prefix + strings.Repeat("9", ninesCount))
}

func fillByZeros(integer, zerosCount int) int {
	return integer - integer%(pow10(zerosCount))
}

func rangeToPattern(start, stop int) string {
	var pattern string
	var anyDigitCount int

	startStr, stopStr := strconv.Itoa(start), strconv.Itoa(stop)
	for i := 0; i < len(startStr) && i < len(stopStr); i++ {
		startDigit := startStr[i]
		stopDigit := stopStr[i]

		if startDigit == stopDigit {
			pattern += string(startDigit)
		} else if startDigit != '0' || stopDigit != '9' {
			pattern += fmt.Sprintf("[%c-%c]", startDigit, stopDigit)
		} else {
			anyDigitCount++
		}
	}

	if anyDigitCount == 1 {
		pattern += `\d`
	} else if anyDigitCount > 1 {
		pattern += fmt.Sprintf(`\d{%d}`, anyDigitCount)
	}
	zeroCount := 4 - int(math.Max(1, math.Ceil(math.Log10(float64(stop+1)))))
	zeroPrefix := strings.Repeat("0", zeroCount)

	return zeroPrefix + pattern
}

func difference(a, b []string, format string) []string {
	bMap := make(map[string]bool)
	for _, item := range b {
		bMap[item] = true
	}
	var result []string
	for _, item := range a {
		if !bMap[item] {
			result = append(result, fmt.Sprintf(format, item))
		}
	}
	return result
}

func intersection(a, b []string, format string) []string {
	bMap := make(map[string]bool)
	for _, item := range b {
		bMap[item] = true
	}
	var result []string
	for _, item := range a {
		if bMap[item] {
			result = append(result, fmt.Sprintf(format, item))
		}
	}
	return result
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func toInt(s string) int {
	val, _ := strconv.Atoi(s)
	return val
}

func pow10(n int) int {
	return int(math.Pow10(n))
}
