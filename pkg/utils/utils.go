package utils

import (
	"encoding/json"
	"strconv"
	"strings"
)

// ContainsStr 快速校验错误
func ContainsStr(checkText string, text ...string) bool {
	for _, text := range text {
		if strings.Contains(checkText, text) {
			return true
		}
	}
	return false
}

// CheckError 检查错误
func CheckError(err error, text ...string) bool {
	errText := err.Error()
	return ContainsStr(errText, text...)
}

// CheckErrorWithLower 检查错误
func CheckErrorWithLower(err error, errorText ...string) bool {
	errText := strings.ToLower(err.Error())
	return ContainsStr(errText)
}

// StrToInt 字符串转int
func StrToInt(str string, defaultVal int) int {
	val, err := strconv.Atoi(str)
	if err != nil {
		return defaultVal
	}
	return val
}

// StrToBool 字符串转bool
func StrToBool(str string) bool {
	if v, err := strconv.Atoi(str); err == nil {
		return v > 0
	}
	return StrNotIn(str, []string{"", "0"})
}

// ToMap interface to map
func ToMap(v interface{}) map[string]interface{} {
	buf, err := json.Marshal(v)
	if err != nil {
		return nil
	}

	var m map[string]interface{}
	err = json.Unmarshal(buf, &m)
	if err != nil {
		return nil
	}

	return m
}

// StrLeftFill 字符串左填充
// str 原始字符串
// fill预填充字符
// length 终止长度
func StrLeftFill(str string, fill string, length int) string {
	if len(str) >= length {
		return str
	}

	// r1 := strings.Repeat(fill, length)
	// return fmt.Sprintf("%s%s", r1[0:len(r1)-len(str)-1], str)
	var fillStr string
	for i := length; i > 0; i-- {
		if i <= len(str) {
			fillStr += str
			break
		}
		fillStr += fill
	}

	return fillStr
}

// MaxUInt 生成指定长度的最大数
func MaxUInt(bits int) uint64 {
	if bits >= 64 || bits <= 0 {
		return 0
	}
	r, _ := strconv.ParseUint(strings.Repeat("9", bits), 10, 64)
	return r
}
