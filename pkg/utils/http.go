package utils

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"time"
)

// CtxKeyMethod -
const CtxKeyMethod = "method"

// HTTPGet -
func HTTPGet(ctx context.Context, url string, header http.Header, query map[string]string, timeout time.Duration) ([]byte, int, error) {
	cli := http.Client{Timeout: timeout}
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, 0, err
	}
	req.Header = header
	q := req.URL.Query()
	for k, v := range query {
		q.Add(k, v)
	}
	req.URL.RawQuery = q.Encode()
	resp, err := cli.Do(req)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, 0, err
	}
	return respBody, resp.StatusCode, nil
}

// HTTPPost -
func HTTPPost(ctx context.Context, url string, header http.Header, body []byte, timeout time.Duration) ([]byte, int, error) {
	cli := http.Client{Timeout: timeout}
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if m, ok := ctx.Value(CtxKeyMethod).(string); ok {
		req.Method = m
	}
	if err != nil {
		return nil, 0, err
	}
	req.Header = header
	resp, err := cli.Do(req)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, 0, err
	}
	return respBody, resp.StatusCode, nil
}

// HTTPDelete -
func HTTPDelete(ctx context.Context, url string, header http.Header, body []byte, timeout time.Duration) ([]byte, int, error) {
	cli := http.Client{Timeout: timeout}
	req, err := http.NewRequestWithContext(ctx, "DELETE", url, bytes.NewReader(body))
	if err != nil {
		return nil, 0, err
	}
	req.Header = header
	resp, err := cli.Do(req)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, 0, err
	}
	return respBody, resp.StatusCode, nil
}

// HTTPPut -
func HTTPPut(ctx context.Context, url string, header http.Header, body []byte, timeout time.Duration) ([]byte, int, error) {
	cli := http.Client{Timeout: timeout}
	req, err := http.NewRequestWithContext(ctx, "PUT", url, bytes.NewReader(body))
	if m, ok := ctx.Value(CtxKeyMethod).(string); ok {
		req.Method = m
	}
	if err != nil {
		return nil, 0, err
	}
	req.Header = header
	resp, err := cli.Do(req)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, 0, err
	}
	return respBody, resp.StatusCode, nil
}
