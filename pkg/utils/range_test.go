package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRegex(t *testing.T) {
	assert.Equal(t, GetHostnameRangeRegex("dev-test[1,1]"), `dev-test(0001)$`)
	assert.Equal(t, GetHostnameRangeRegex("dev-test[1,50]"), `dev-test(000[1-9]|00[1-4]\d|0050)$`)
	assert.Equal(t, GetHostnameRangeRegex("dev-test[1,500]"), `dev-test(000[1-9]|00[1-9]\d|0[1-4]\d{2}|0500)$`)
	assert.Equal(t, GetHostnameRangeRegex("dev-test[1,5000]"), `dev-test(000[1-9]|00[1-9]\d|0[1-9]\d{2}|[1-4]\d{3}|5000)$`)
}
