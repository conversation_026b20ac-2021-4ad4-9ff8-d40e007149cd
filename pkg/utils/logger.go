package utils

import (
	"io"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
)

// CmdbLogger -
func CmdbLogger(local *logrus.Logger) *logrus.Logger {
	if cfg.GetCmdbConfig().LogTo == "order" && local != nil {
		if local == nil {
			return logrus.StandardLogger()
		}
		return local
	} else if cfg.GetCmdbConfig().LogTo == "std" {
		return logrus.StandardLogger()
	} else if cfg.GetCmdbConfig().LogTo == "null" {
		l := logrus.New()
		l.Out = io.Discard
		return l
	} else {
		return logrus.StandardLogger()
	}
}
