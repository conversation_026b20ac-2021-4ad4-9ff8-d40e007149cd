package utils

import (
	"fmt"
	"strings"
)

const delimeters = "-"

func GetRegionBylbName(instanceName string) (string, error) {
	segs := strings.Split(instanceName, delimeters)
	if len(segs) != 6 {
		return "", fmt.<PERSON>rrorf("incorrect name format")
	}
	if segs[1] == "sf" {
		return fmt.Sprintf("%s-%s", segs[1], segs[2]), nil
	}

	return fmt.Sprintf("%s-%s-%s", segs[1], segs[2], segs[3]), nil
}

func GetRegionByInstanceName(instanceName string) (string, error) {
	segs := strings.Split(instanceName, delimeters)
	if len(segs) != 5 {
		return "", fmt.Errorf("incorrect name format")
	}
	if segs[1] == "sf" {
		return fmt.Sprintf("%s_%s", segs[1], segs[2]), nil
	}

	return fmt.Sprintf("%s_%s_%s", segs[1], segs[2], segs[3]), nil
}
