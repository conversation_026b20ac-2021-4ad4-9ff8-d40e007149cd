package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
)

// Keys ==>Object.Keys(
func Keys(val interface{}) (s []string) {
	// m, ok := val.(map[string]interface{})
	// if ok {
	if reflect.ValueOf(val).Kind() == reflect.Map {

		iter := reflect.ValueOf(val).MapRange()
		for iter.Next() {
			k := iter.Key()
			s = append(s, fmt.Sprintf("%v", k))
		}
	}
	// }
	return
}

//
// func ArrayToSet([]interface{}, func()) {
//
// }

// UniqueFunc 去重方法
type UniqueFunc func() bool

// Unique 去重
func Unique(f UniqueFunc) {}

// IsStructModified -
func IsStructModified(oldData map[string]interface{}, newData map[string]interface{}) (bool, map[string]FieldChangeList) {
	change := map[string]FieldChangeList{}
	for k := range newData {
		jNew, _ := json.Marshal(newData[k])
		jOld, _ := json.Marshal(oldData[k])
		if string(jNew) != string(jOld) {
			change[k] = FieldChangeList{
				Before: string(jOld),
				After:  string(jNew),
			}
			continue
		}
	}
	if len(change) > 0 {
		return true, change
	}
	return false, nil
}

// FieldChangeList -
type FieldChangeList struct {
	Before string `json:"before"`
	After  string `json:"after"`
}

// ResourceChangeList -
type ResourceChangeList struct {
	AccountID    string                     `json:"account_id"`
	Type         string                     `json:"type"`
	InstanceID   string                     `json:"instanceID"`
	InstanceName string                     `json:"instanceName"`
	Data         map[string]FieldChangeList `json:"data"`
}
