package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"sync"
)

// Flag xxx
type Flag struct {
	terminal bool
	mtx      sync.Mutex
}

func (f *Flag) read() bool {
	f.mtx.Lock()
	defer f.mtx.Unlock()
	return f.terminal
}

func (f *Flag) write(ter bool) {
	f.mtx.Lock()
	defer f.mtx.Unlock()
	f.terminal = ter
	return
}

// FileExists file exists
func FileExists(name string) bool {
	if _, err := os.Stat(name); err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return true
}

// FileRest 将目标文件置空
func FileRest(name string) error {
	return ioutil.WriteFile(name, []byte(""), 0666)
}

// CreateFile 创建文件
func CreateFile(filename string) error {
	//mask := syscall.Umask(0) // https://www.dazhuanlan.com/2020/01/31/5e33a43643b35/
	//defer syscall.Umask(mask)
	_, err := os.Stat(filename)
	if os.IsNotExist(err) {
		fmt.Println("文件不存在,准备创建")
		return ioutil.WriteFile(filename, []byte(""), 0666)
	}
	return err
}

// ChmodModePerm 改变权限属性
func ChmodModePerm(filename ...string) {
	//mask := syscall.Umask(0) // https://www.dazhuanlan.com/2020/01/31/5e33a43643b35/
	//defer syscall.Umask(mask)
	for _, f := range filename {
		os.Chmod(f, os.ModePerm)
	}
}

// CreatePath linux下文件与文件夹不得同名,若文件夹名被文件名覆盖,使用force参数可进行删除后创建文件的动作
func CreatePath(filePath string, force ...bool) error {
	//mask := syscall.Umask(0) // https://www.dazhuanlan.com/2020/01/31/5e33a43643b35/
	//defer syscall.Umask(mask)
	f, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		fmt.Println("文件夹不存在,创建:", filePath, os.ModePerm)
		return os.MkdirAll(filePath, os.ModePerm)
	}
	if !f.IsDir() && (len(force) != 0 && force[0]) {
		err = os.Remove(filePath)
		if err != nil {
			return err
		}
		return os.MkdirAll(filePath, os.ModePerm)
	}
	return err
}

// GetFileMd5 获取文件md5
func GetFileMd5(filename string) (string, error) {
	f, err := os.Open(filename)
	if err != nil {
		return "", err
	}
	hash := md5.New()
	io.Copy(hash, f)
	return hex.EncodeToString(hash.Sum(nil)), err
}

// CheckFileMd5 检查文件md5
func CheckFileMd5(filename, md5 string) (bool, error) {
	oldMd5, err := GetFileMd5(filename)
	if err != nil {
		return false, err
	}

	return oldMd5 == md5, nil
}
