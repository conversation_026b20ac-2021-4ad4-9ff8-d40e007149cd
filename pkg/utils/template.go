package utils

import (
	"bytes"
	"fmt"
	htmlTpl "html/template"
	"text/template"
	"time"
)

// RenderTpl 模板渲染
func RenderTpl(tplStr string, replaceIf interface{}) (string, error) {
	randNum := time.Now().UnixNano()
	tpl, err := template.New(fmt.Sprintf("tpl-%v", randNum)).Parse(tplStr)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	err = tpl.Execute(&buf, replaceIf)
	return buf.String(), err
}

// RenderHTMLTpl HTML模板渲染
func RenderHTMLTpl(tplStr string, replaceIf interface{}) (string, error) {
	randNum := time.Now().UnixNano()
	tpl, err := htmlTpl.New(fmt.Sprintf("html-tpl-%v", randNum)).Parse(tplStr)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	err = tpl.Execute(&buf, replaceIf)
	return buf.String(), err
}
