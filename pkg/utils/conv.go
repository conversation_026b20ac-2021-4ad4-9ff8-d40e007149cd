package utils

import "strconv"

// SafeToString to string
func SafeToString(val interface{}) string {
	v, ok := val.(string)
	if ok {
		return v
	}

	return ""
	// if reflect.TypeOf(val).Kind() == reflect.String {
	// 	return
	// }
}

// SafeToInt32 to int32
func SafeToInt32(val interface{}) int32 {
	v, ok := val.(int32)
	if ok {
		return v
	}

	return 0
}

// SafeToFloat64 to float64
func SafeToFloat64(val interface{}) float64 {
	v, ok := val.(float64)
	if ok {
		return v
	}

	return 0
}

// SafeToBool to bool
func SafeToBool(val interface{}) bool {
	v, ok := val.(bool)
	if ok {
		return v
	}

	return false
}

// StringSafeToInt to int
func StringSafeToInt(val string, acq int) int {
	res, err := strconv.Atoi(val)
	if err != nil {
		return acq
	}

	return res
}
