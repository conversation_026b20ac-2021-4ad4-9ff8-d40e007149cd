package utils

// StrArrayUnique get unique string array
func StrArrayUnique(a []string) (ret []string) {
	ret = make([]string, 0)
	set := make(map[string]struct{})
	for _, v := range a {
		if _, ok := set[v]; !ok {
			set[v] = struct{}{}
			ret = append(ret, v)
		}
	}
	return ret
}

// StrArrayIndexOf 查询string是否在列表中
func StrArrayIndexOf(s1 []string, s2 string) int {
	for i := 0; i < len(s1); i++ {
		if s1[i] == s2 {
			return i
		}
	}

	return -1
}

// StrArraySplice 从数组中删除指定元素
func StrArraySplice(s1 []string, s2 string) ([]string, int) {
	var newArr []string
	delCount := 1
	for i := 0; i < len(s1); i++ {
		if s1[i] != s2 {
			newArr = append(newArr, s1[i])
		} else {
			delCount++
		}
	}

	return newArr, delCount
}

// StrArrayToMap 字符串列表转map
func StrArrayToMap(a []string) map[string]struct{} {
	set := make(map[string]struct{})
	for _, v := range a {
		set[v] = struct{}{}
	}

	return set
}
