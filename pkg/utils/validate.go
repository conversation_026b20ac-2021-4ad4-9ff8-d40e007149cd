/*
自定义验证方法
*/

package utils

// MustInt xxx
func MustInt(i, d int) int {
	if i < 0 {
		return d
	}
	return i
}

// IntMustIn xxx
func IntMustIn(i int, list []int) bool {
	for _, v := range list {
		if i == v {
			return true
		}
	}
	return false
}

// MustStr default
func MustStr(str, defaultVal string) string {
	if str != "" {
		return str
	}
	return defaultVal
}

// StrNotIn string not in list
func StrNotIn(str string, list []string) bool {
	for _, v := range list {
		if str == v {
			return false
		}
	}
	return true
}

// StrNotEmpty string not empty
func StrNotEmpty(str ...string) bool {
	for _, v := range str {
		if v != "" {
			return true
		}
	}
	return false
}
