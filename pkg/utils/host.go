package utils

import (
	"net"
	"os"
	"strings"
)

// LocalIP 获取本机IP
func LocalIP() string {
	address, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, addr := range address {
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				return ipNet.IP.String()
			}
		}
	}
	return ""
}

// LocalHostname 获取本机主机名
func LocalHostname() string {
	hostname, err := os.Hostname()
	if err != nil {
		return ""
	}
	return hostname
}

// ResourceIsProd -
func ResourceIsProd(name string) bool {
	return strings.Contains(name, "-prod-")
}

// ResourceIsTest -
func ResourceIsTest(name string) bool {
	return strings.Contains(name, "-test-")
}
