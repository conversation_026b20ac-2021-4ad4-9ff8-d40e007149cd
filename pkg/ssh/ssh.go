package ssh

import (
	"encoding/json"
	"golang.org/x/crypto/ssh"
	"io"
	"time"
)

// Auth ssh-auth
type Auth struct {
	User        string          `json:"ssh_user" binding:"required"`
	Password    string          `json:"ssh_password"`
	PemPath     string          `json:"ssh_pem_path"`     // 私钥地址
	PemPassword string          `json:"ssh_pem_password"` // 私钥加密密码
	PemRaw      json.RawMessage `json:"ssh_pem_raw"`
}

// NewSSHClient 新建ssh
func NewSSHClient(auth Auth, address string) (client *Client, err error) {
	client = &Client{Auth: auth, Addr: address}
	err = client.NewClient()
	return
}

// Client ssh-client
type Client struct {
	Auth
	Addr string

	client  *ssh.Client
	Session *ssh.Session
}

// NewClient 初始化client
func (c *Client) NewClient() (err error) {
	config := &ssh.ClientConfig{
		Config:          ssh.Config{},
		User:            c.User,
		Auth:            []ssh.AuthMethod{ssh.Password(c.Password)},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         10 * time.Second,
	}

	if c.PemPath != "" {
		if signer, err := LoadPrivateKeyWithFile(c.PemPath, c.PemPassword); err == nil {
			//config.Auth = []ssh.AuthMethod{ssh.PublicKeys(signer)}
			config.Auth = []ssh.AuthMethod{ssh.PublicKeys(signer)}
		}
	}

	if len(c.PemRaw) != 0 {
		if signer, err := ssh.ParsePrivateKey(c.PemRaw); err == nil {
			config.Auth = []ssh.AuthMethod{ssh.PublicKeys(signer)}
		}
		// if signer, err := Load
	}

	c.client, err = ssh.Dial("tcp", c.Addr, config)
	return
}

// RunCommand 执行ssh命令
func (c *Client) RunCommand(command string, w io.Writer) (err error) {
	session, err := c.client.NewSession()

	if err != nil {
		return
	}

	session.Stdout = w
	session.Stderr = w
	err = session.Start(command)
	if err != nil {
		return
	}
	c.Session = session
	return
}

// Close 关闭连接
func (c Client) Close() (err error) {
	if c.client != nil {
		_ = c.client.Close()
		c.client = nil
	}

	return
}
