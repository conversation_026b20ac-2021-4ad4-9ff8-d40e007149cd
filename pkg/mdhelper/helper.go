package mdhelper

import "fmt"

// Markdown md
type Markdown struct {
	mdString     string
	newlineSplit string
}

// NewMarkdown markdown
func NewMarkdown() Markdown {
	return Markdown{
		newlineSplit: "\n",
	}
}

// SetLineSplit 设置换行符标识
func (m *Markdown) SetLineSplit(sep string) {
	m.newlineSplit = sep
}

// SetRaw 插入原始markdown
func (m *Markdown) SetRaw(raw string) {
	m.mdString += raw
}

// SetTable 插入markdown表格
func (m *Markdown) SetTable(tableTitle []string, tableContent [][]string) {
	// markdown语法不支持合并单元格,可以使用table语法替代
	title := "|"
	splitLine := "|"
	content := ""
	for _, v := range tableTitle {
		title += v + "|"
		splitLine += "--|"
	}

	for i := 0; i < len(tableContent); i++ {
		tc := tableContent[i]
		for contentIndex := 0; contentIndex < len(tableTitle); contentIndex++ {
			if len(tc) < contentIndex+1 {
				content += "| "
				continue
			}

			content += "|" + tc[contentIndex]
		}
		content += "|" + m.newlineSplit
	}

	m.mdString += fmt.Sprint(m.newlineSplit, title, m.newlineSplit, splitLine, m.newlineSplit, content, m.newlineSplit)
}

// String to-string
func (m *Markdown) String() string {
	return m.mdString
}
