package agent

import (
	"bytes"
	"crypto/des"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"

	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

const (
	keyPadding = "nieyunfeng" // 默认加盐
	// DefaultKey 默认加密key
	DefaultKey = "mihoyo"
)

// EntryptDesECB 使用ECB算法加密数据
func EntryptDesECB(data, key []byte) (string, error) {
	if len(data) == 0 {
		return "", errors.New("entrypt Data not having")
	}
	if len(key) < 8 {
		bs := []byte(keyPadding)
		key = append(key, bs[0:(8-len(key))]...)
	} else if len(key) > 8 {
		key = key[:8]
	}
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("EntryptDesECB newCipher error[%v]", err)
	}
	bs := block.BlockSize()
	data = PKCS5Padding(data, bs)
	if len(data)%bs != 0 {
		return "", fmt.Errorf("EntryptDesECB Need a multiple of the blocksize")
	}
	out := make([]byte, len(data))
	dst := out
	for len(data) > 0 {
		block.Encrypt(dst, data[:bs])
		data = data[bs:]
		dst = dst[bs:]
	}

	//return base64.StdEncoding.EncodeToString(out)
	return hex.EncodeToString(out), nil
}

// DecryptDESECB 加密被ECB加密对象
func DecryptDESECB(d string, key []byte) (string, error) {
	//data, err := base64.StdEncoding.DecodeString(d)
	if d == "" {
		return "", errors.New("decrypt Data is Empty")
	}
	data, err := hex.DecodeString(d)
	if err != nil {
		return "", fmt.Errorf("DecryptDES Decode base64 error[%v]", err)
	}
	if len(key) < 8 {
		bs := []byte(keyPadding)
		key = append(key, bs[0:(8-len(key))]...)
	} else if len(key) > 8 {
		key = key[:8]
	}
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("DecryptDES NewCipher error[%v]", err)
	}
	bs := block.BlockSize()
	if len(data)%bs != 0 {
		return "", fmt.Errorf("DecryptDES crypto/cipher: input not full blocks")
	}
	out := make([]byte, len(data))
	dst := out
	for len(data) > 0 {
		block.Decrypt(dst, data[:bs])
		data = data[bs:]
		dst = dst[bs:]
	}
	out = PKCS5UnPadding(out)
	return string(out), nil
}

// PKCS5Padding PKCS5补全
func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// PKCS5UnPadding PKCS5剔除补足字段
func PKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

// GenerateID 生成agentID, id生成规则,地域+ip+ssh端口
func GenerateID(region, innerIPPort string) (string, error) {
	return EntryptDesECB([]byte(strings.Join([]string{region, innerIPPort}, "_")), []byte(DefaultKey))
}

// GetAccountIndexID ...
func GetAccountIndexID(ctx context.Context, ispID string) (uint32, error) {
	var accountIndexID uint32 = 22
	var accountEntity entity.Account
	accEnt := entity.GetAccountCollection(models.GetEngine())
	_, err := models.FindPK(ctx, accEnt, ispID, &accountEntity)
	if err == nil {
		accountIndexID = accountEntity.IndexID
	} else {
		return 22, fmt.Errorf("cannot get accountIndexID, err: %s", err.Error())
	}
	return accountIndexID, nil
}

// DeCodeAgentID 对agentID进行解码
func DeCodeAgentID(agentID string) (aid string, err error) {
	aid, err = DecryptDESECB(agentID, []byte(DefaultKey))
	if err != nil {
		return "", err
	}

	return
}

// IDDecodeIPPort agent-id转ip+端口
func IDDecodeIPPort(aid string) (region, innerIPPort string, err error) {
	str := strings.Split(aid, "_")
	if len(str) != 2 {
		return "", "", errors.New("agentId format error")
	}
	region, innerIPPort = str[0], str[1]
	// 仅支持ipv4或ipv4+端口生产的agent
	if !utils.TestIPV4(innerIPPort) && !utils.TestIPV4Port(innerIPPort) {
		return "", "", errors.New("innerIPPort isn't ipv4mask")
	}
	return
}

// DecodeAgentInfo 结构agentID中包含的信息
func DecodeAgentInfo(agentID string) (region, innerIPPort string, err error) {
	aid, err := DeCodeAgentID(agentID)
	if err != nil {
		return "", "", err
	}

	return IDDecodeIPPort(aid)
}
