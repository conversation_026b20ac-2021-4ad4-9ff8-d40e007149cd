package agent

// InstallScript ...
var InstallScript = `
#!/bin/bash

BinHost="************:8898" # replace.BinHost
workDir="/home/<USER>/op-agent"

DEFAULT_GATEWAY="ws://**************:30800/api/v1/agent/reg?agentId=0cbc96ef0c504d864417705db6ca9ac0a35bb4ebff0d78599f0d988540f99e7ebd6403056c5505be"
GATEWAY=${2:-${DEFAULT_GATEWAY}}
DEFAULT_VERSION="0.0.1"
VERSION=${3:-${DEFAULT_VERSION}}
DEFAULT_ClientType="normal"
ClientType=${4:-${DEFAULT_ClientType}}
DEFAULT_LOGLEVEL="DEBUG"
LOGLEVEL=${5:-${DEFAULT_LOGLEVEL}}
CONF="${workDir}/op-agent-conf.yml"

DEFAULT_PROXY_IP="127.0.0.1"
PROXY_IP=${6:-${DEFAULT_PROXY_IP}}
DEFAULT_GET_NGINX="http://************:8898/ops-agent/tools/nginx-1.18.0-build.tar.gz"
GET_NGINX=${7:-${DEFAULT_GET_NGINX}}
DEFAULT_ToolPath="/opt/miOps-tools"
ToolPath=${8:-${DEFAULT_ToolPath}}
NGINX_Tar="${ToolPath}/nginx.tar.gz"

cmd="op-agent"
releaseURL="http://**************:38080/op-agent/op-agent"
AgentScriptURL="http://${BinHost}/op-agent/agentInstall.sh" # 用于proxy初始化同步agent安装脚本

rCmd="${workDir}/${cmd}"
#DownloadUrl="https://这里改为文件服务器上agent安装脚本的地址,更新agent会用到"

cronTab="*/1 * * * *" # 每一分钟检测一次存活
getCron="crontab -l|grep -v 'no crontab for'| grep -v '^$'"

pidFile="${workDir}/${pid}"
realFile="${workDir}/.run.sh"
upgradeCmd="${realFile} CheckOrRollback"
upgradeCronCmd="*/1 * * * * bash -c '${upgradeCmd}'"
upgradeCronFileSuffix="_new"
upgradeRunFile="${rCmd}${upgradeCronFileSuffix}"
upgradeConfFile="${CONF}${upgradeCronFileSuffix}"
rollFileSuffix="_old"
rollRunFile="${rCmd}${rollFileSuffix}"
rollConfFile="${CONF}${rollFileSuffix}"
errLogFile="/tmp/ys-op-agent.logger.err"

runCmd="${rCmd} -c ${CONF} -d"
cronCmd="${cronTab} ${runCmd}"

test ! -d "${workDir}" && mkdir -p "${workDir}" # 无相关权限时将执行失败

function InstallNginx() {
    set sh -e
    set sh -x
    test ! -d "${ToolPath}/agent" && mkdir -p "${ToolPath}/agent" # 无相关权限时将执行失败
    curl -o ${NGINX_Tar} ${GET_NGINX}
    tar zxf ${NGINX_Tar} -C ${ToolPath}
    sed -i "s#/usr/share/nginx/html/op-agent/#${ToolPath}/agent/#g" ${ToolPath}/nginx/conf/nginx.conf
    ${ToolPath}/nginx/sbin/nginx -p ${ToolPath}/nginx/ -c conf/nginx.conf
    InitProxy
    echo "proxy_ip" ${PROXY_IP}
    set sh +x
    set sh +e
}

function InitProxy() {
#   curl -o ${ToolPath}/agent/agentInstall.sh ${AgentScriptURL}
  if [ ${PROXY_IP} == "127.0.0.1" ];then
    export PROXY_IP=
` + "`ip add|grep -e ens33 -e eth0|grep inet|grep -v '127'|head -n1|awk '{print $2}'|awk -F\"/\" '{print $1}'`" + `
  fi
#   sed -i "1,/a/s#BinHost=.*#BinHost=\"${PROXY_IP}:38080\"#" ${ToolPath}/agent/agentInstall.sh
}

function addCron() {
    echo "=======run addCron======="
    oCron=
` + "`crontab -l|grep -v \"no crontab for\"| grep -v \"^$\" | grep -v \"${runCmd}\"`" + `
    echo -e "${oCron}\n${cronCmd}" | crontab -
    if [ $? == 0 ];then
      echo "addCron success."
    else
      echo "addCron failed."
    fi
}

function delCron() {
    echo "=======run delCron======="
#    crontab -l 2>&1|grep -v "no crontab for"|grep -v "${runCmd}" | crontab -
    crontab -l|grep -v "no crontab for"| grep -v "^$"|grep -v "${runCmd}" | crontab -
    if [ $? == 0 ];then
      echo "delCron success."
    else
      echo "delCron failed."
    fi
}

function InitialInstall() {
    if [ ${ClientType} == "master" ];then
      InstallNginx
    fi
    curl -o "${rCmd}" ${releaseURL} && chmod +x "${rCmd}"
    if [ $? != 0 ];then
      echo "install failed"
      exit 1
    fi

    initCmd="${rCmd} init -c ${CONF} -s ${GATEWAY} --log-level=${LOGLEVEL} --client-type=${ClientType} --bind-proxy-address=${PROXY_IP}"
    echo "will run cmd: ${initCmd}"
    ${initCmd}
    echo "install ${cmd} success."
}

function install() {
  InitialInstall && addCron && start
}

# master缺少关闭nginx的动作
function uninstall() {
    if stop && delCron;then
#      rm -rf "${workDir}"
      echo "uninstall success."
    fi
}

function start() {
    sh -c "${runCmd}" && echo "${cmd} will running."
}

function stop() {
    sh -c "${runCmd} stop" && echo "${cmd} stopped."
}

# 程序无法自杀，需借助外部方法
function updateByCron() {
  preUpgrade
  delCron
  oCron=$(crontab -l|grep -v "no crontab for"| grep -v "^$"| grep -v "${runCmd}")
  echo -e "${oCron}\n${upgradeCronCmd}" | crontab -
  if [ $? == 0 ];then
    echo "addUpgradeCron success."
  else
    echo "addUpgradeCron failed."
  fi
}

function preUpgrade() {
  curl -o "${realFile}" ${AgentScriptURL} && chmod +x "${realFile}"
  curl -o ${upgradeRunFile} ${releaseURL} && chmod +x "${upgradeRunFile}"
` + "`${upgradeRunFile} init -c ${upgradeConfFile} -s ${GATEWAY} --log-level=${LOGLEVEL} --client-type=${ClientType} --bind-proxy-address=${PROXY_IP}`" + `
}

# 程序自更的前提是基本配置文件不会变化，如安装路径，配置文件名称
function CheckOrRollback() {
  uninstall
  crontab -l|grep -v "no crontab for"| grep -v "^$"|grep -v "${upgradeCmd}" | crontab -
  mv ${rCmd} ${rollRunFile}
  mv ${CONF} ${rollConfFile}
  mv ${upgradeConfFile} ${CONF}
  mv ${upgradeRunFile} ${rCmd}
  start
  pid=
` + "`ps -ef|grep ${rCmd}|grep -v 'grep'|awk '{print $2}'`" + `
  sleep 11
  if [ "${pid}" != "" ] && test -s "${errLogFile}";then
    addCron
    echo "upgrade success"
    return
  fi
  echo "upgrade failed"
  rollBack
  exit 127
}

function rollBack() {
  mv ${rollRunFile} ${rCmd}
  mv ${rollConfFile} ${CONF}
  addCron && start
}

function update() {
    uninstall && install
}

if [ $# == 0 ];then
  install
else
  $1
fi
`
