package qcron

import (
	"fmt"
	"testing"
)

// 测试QTime 时间转化函数
func TestChangeTime(t *testing.T) {
	var x uint
	x = 120
	s := ChangeTime(x)
	fmt.Println(s)

	fmt.Println("---------------------------")

	x = 98
	s = ChangeTime(x)
	fmt.Println(s)

	fmt.Println("---------------------------")

	x = 47
	s = ChangeTime(x)
	fmt.Println(s)

	fmt.Println("---------------------------")

	x = 0
	s = ChangeTime(x)
	fmt.Println(s)

	fmt.Println("---------------------------")

	x = 1441
	s = ChangeTime(x)
	fmt.Println(s)

	fmt.Println("---------------------------")
}
