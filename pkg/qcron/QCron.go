package qcron

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

// TaskForm 任务请求表单
type TaskForm struct {
	TaskName string `json:"task_name"`
	CronSpec string `json:"cronspec"`
	Command  string `json:"command"`
}

func doRequest(request *http.Request) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	req := request.WithContext(ctx)

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}

	if resp.StatusCode > 300 {
		raw, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		return fmt.Errorf("%v", string(raw))
	}
	return nil
}

// NewCronTask 新建定时任务
func NewCronTask(qCronAddress string, form TaskForm) error {
	b, _ := json.Marshal(form)
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/qCrontab/AddTask", qCronAddress), bytes.NewReader(b))
	if err != nil {
		return err
	}
	fmt.Println("添加任务成功")
	return doRequest(req)
}

// StartCronTask 更新定时任务
func StartCronTask(qCronAddress string, form TaskForm) error {
	b, _ := json.Marshal(form)
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/qCrontab/StartTask", qCronAddress), bytes.NewReader(b))
	if err != nil {
		return err
	}
	fmt.Println("任务开启成功")
	return doRequest(req)
}

// UpdateCronTask 更新定时任务
func UpdateCronTask(qCronAddress string, form TaskForm) error {
	b, _ := json.Marshal(form)
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/qCrontab/UpdateTask", qCronAddress), bytes.NewReader(b))
	if err != nil {
		return err
	}
	return doRequest(req)
}

// StopCronTask 停止定时任务
func StopCronTask(qCronAddress string, form TaskForm) error {
	b, _ := json.Marshal(form)
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/qCrontab/StopTask", qCronAddress), bytes.NewReader(b))
	if err != nil {
		return err
	}
	return doRequest(req)
}

// CronAction action nickname
type CronAction string

// cron action
const (
	NewCron    CronAction = "new"
	StopCron   CronAction = "stop"
	StartCron  CronAction = "start"
	UpdateCron CronAction = "update"
)

// CronFunc xxx
type CronFunc func(string, TaskForm) error

var actionSet = map[CronAction]CronFunc{
	NewCron:    NewCronTask,
	StopCron:   StopCronTask,
	StartCron:  StartCronTask,
	UpdateCron: UpdateCronTask,
}

// CronTask xxx
func CronTask(action CronAction, qCronAddress string, form TaskForm) error {
	f := actionSet[action]
	return f(qCronAddress, form)
}
