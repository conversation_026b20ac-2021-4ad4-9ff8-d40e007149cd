package qlog

import (
	"fmt"
	"io"
	"os"

	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/qlog/color"
)

// TerminalWriter 终端输出
type TerminalWriter struct {
	io.Writer
}

var (
	// Stdout stdout
	Stdout = &TerminalWriter{os.Stdout}
	// Stderr stderr
	Stderr = &TerminalWriter{os.Stderr}
)

func (w *TerminalWriter) checkOutput(s string) {
	if _, err := io.WriteString(w, s); err != nil {
		logger.Fatalf("Write to %v failed.", w)
	}
}

// Color 颜色
func (w *TerminalWriter) Color(syntax string) *TerminalWriter {
	escapeCode := color.Colorize(syntax)
	w.checkOutput(escapeCode)
	return w
}

// Reset rest
func (w *TerminalWriter) Reset() *TerminalWriter {
	w.checkOutput(color.ResetCode)
	return w
}

// Print print
func (w *TerminalWriter) Print(a ...interface{}) *TerminalWriter {
	fmt.Fprint(w, a...)
	return w
}

// Nl nl
func (w *TerminalWriter) Nl(a ...interface{}) *TerminalWriter {
	length := 1
	if len(a) > 0 {
		length = a[0].(int)
	}
	for i := 0; i < length; i++ {
		w.checkOutput("\n")
	}
	return w
}

// Colorf color printf
func (w *TerminalWriter) Colorf(format string, a ...interface{}) *TerminalWriter {
	w.checkOutput(color.Sprintf(format, a...))
	return w
}

// Clear clear
func (w *TerminalWriter) Clear() *TerminalWriter {
	w.checkOutput("\033[2J")
	return w
}

// ClearLine clear line
func (w *TerminalWriter) ClearLine() *TerminalWriter {
	w.checkOutput("\033[2K")
	return w
}

// Move move
func (w *TerminalWriter) Move(x, y int) *TerminalWriter {
	w.checkOutput(fmt.Sprintf("\033[%d;%dH", x, y))
	return w
}

// Up up
func (w *TerminalWriter) Up(x int) *TerminalWriter {
	w.checkOutput(fmt.Sprintf("\033[%dA", x))
	return w
}

// Down down
func (w *TerminalWriter) Down(x int) *TerminalWriter {
	w.checkOutput(fmt.Sprintf("\033[%dB", x))
	return w
}

// Right right
func (w *TerminalWriter) Right(x int) *TerminalWriter {
	w.checkOutput(fmt.Sprintf("\033[%dC", x))
	return w
}

// Left left
func (w *TerminalWriter) Left(x int) *TerminalWriter {
	w.checkOutput(fmt.Sprintf("\033[%dD", x))
	return w
}
