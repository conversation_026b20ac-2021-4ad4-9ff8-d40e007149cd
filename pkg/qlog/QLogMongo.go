package qlog

import (
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
)

var (
	// MongoInstance mongo instance
	MongoInstance *Mongo
)

// MongoConf mongo-conf
type MongoConf struct {
	Host string `toml:"host"`
	Port string `toml:"port"`
	User string `toml:"user"`
	Pass string `toml:"pass"`
	Db   string `toml:"db"`
}

// Mongo mongo
type Mongo struct {
	Mongo    *mongo.Client
	db       string
	DbHandle *mongo.Database
}

// NewMongo new mongo
func NewMongo(conf *MongoConf) *Mongo {

	var ss string
	if conf.User != "" {
		ss = fmt.Sprintf("mongodb://%s:%s@%s:%s", conf.User, conf.Pass, conf.Host, conf.Port)
	} else {
		// 这里可以修改一下连接url  用stuido 3T导出url
		ss = fmt.Sprintf("mongodb://%s:%s", conf.Host, conf.Port)
		//ss = fmt.Sprintf("mongodb://cc:cc@**********:27017/cmdb?replicaSet=rs0")
	}

	fmt.Printf("mongo connect: %v", ss)

	client, err := mongo.NewClient(options.Client().ApplyURI(ss))
	if err != nil {
		fmt.Printf("NewClient 1 err:%v\n", err)
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	err = client.Connect(ctx)
	if err != nil {
		fmt.Printf("NewClient 2 err:%v\n", err)
		return nil
	}

	//err = client.Ping(context.TODO(), nil)
	//if err != nil {
	//	fmt.Printf("NewClient 3 err:%v\n", err)
	//	return nil
	//}

	m := Mongo{}
	m.Mongo = client
	m.db = conf.Db
	m.DbHandle = client.Database(m.db)

	MongoInstance = &m
	fmt.Printf("finish mongo init:%v   %v\n", m.DbHandle, m.db)

	return &m
}

// GenerateID xxx
func GenerateID() string {
	return primitive.NewObjectID().Hex()
}
