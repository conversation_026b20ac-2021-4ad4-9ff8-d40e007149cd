package qlog

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/qlog/color"
)

// TaskLog 任务日志
type TaskLog struct {
	ID          primitive.ObjectID `bson:"_id" json:"_id,omitempty"`
	TaskID      primitive.ObjectID `bson:"task_id"`   // 父任务id
	TaskName    string             `bson:"task_name"` // 父任务名
	ServerID    int
	ServerName  string
	Output      string `bson:"output"`
	Error       string `bson:"error"`
	Status      int    `bson:"status"`
	ProcessTime int    `bson:"processtime"`
	CreateTime  int64  `bson:"createtime"`
}

var (
	// TaskLogTable task log table
	TaskLogTable = "QTaskLog"
)

// TaskLogAdd 添加一条日志
func TaskLogAdd(t *TaskLog) string {
	resp, _ := MongoInstance.DbHandle.Collection(TaskLogTable).InsertOne(context.TODO(), t)
	if oid, ok := resp.InsertedID.(primitive.ObjectID); ok {
		return oid.String()
	}
	return "ok"
}

// TaskLogAddError 添加一条彩色日志 红色
func TaskLogAddError(t *TaskLog) string {
	t.Output = color.PrintfWithReturn("@r" + t.Output)
	t.Error = color.PrintfWithReturn("@r" + t.Error)
	resp, _ := MongoInstance.DbHandle.Collection(TaskLogTable).InsertOne(context.TODO(), t)
	if oid, ok := resp.InsertedID.(primitive.ObjectID); ok {
		return oid.String()
	}
	return "ok"
}

// TaskLogAddWarn 添加一条彩色日志 黄色
func TaskLogAddWarn(t *TaskLog) string {
	t.Output = color.PrintfWithReturn("@y" + t.Output)
	t.Error = color.PrintfWithReturn("@y" + t.Error)
	resp, _ := MongoInstance.DbHandle.Collection(TaskLogTable).InsertOne(context.TODO(), t)
	if oid, ok := resp.InsertedID.(primitive.ObjectID); ok {
		return oid.String()
	}
	return "ok"
}

// TaskLogGetByID 啥意思?
func TaskLogGetByID(id string) *TaskLog {
	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)

	user := TaskLog{}

	MongoInstance.DbHandle.Collection(TaskLogTable).FindOne(ctx, bson.M{"_id": id}).Decode(&user)

	return &user
}

// TaskLogDelByTaskID 啥意思?
func TaskLogDelByTaskID(taskID string) (*TaskLog, error) {
	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)

	user := TaskLog{}

	MongoInstance.DbHandle.Collection(TaskLogTable).FindOne(ctx, bson.M{"task_id": taskID}).Decode(&user)

	return &user, nil
}

// TaskLogGetByName 加入分页 并根据时间逆序排序返回 可以分页为空 为空返回所有值
func TaskLogGetByName(taskname string, index int, limit int) []TaskLog {

	ret := []TaskLog{}

	// 分页返回值筛选规则
	var findoptions options.FindOptions

	if limit != 0 {
		findoptions.SetLimit(int64(limit))
		findoptions.SetSkip(int64(index * limit))
	}

	// findoptions.SetSort(bson.D{{"createtime", 1}}) 正序
	// 下面这个是逆序 返回最新的结果
	findoptions.SetSort(bson.D{{"createtime", -1}})

	var cur *mongo.Cursor
	var err error

	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)

	cur, err = MongoInstance.DbHandle.Collection(TaskLogTable).Find(ctx, bson.M{"task_name": taskname}, &findoptions)
	defer cur.Close(ctx)
	for cur.Next(ctx) {

		s := TaskLog{}
		err = cur.Decode(&s)
		if err != nil {
			fmt.Printf("check err:%v\n", err)
			continue
		}
		ret = append(ret, s)
	}

	return ret
}
