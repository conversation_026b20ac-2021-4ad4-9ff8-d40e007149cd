package qlog

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// 任务执行状态
const (
	TaskSUCCESS = 0  // 任务执行成功
	TaskERROR   = -1 // 任务执行出错
	TaskTIMEOUT = -2 // 任务执行超时
)

// Task task
type Task struct {
	ID            primitive.ObjectID `bson:"_id" json:"_id,omitempty"`
	GroupID       int
	ServerIds     string
	ServerType    int
	TaskName      string `bson:"task_name"`
	Description   string `bson:"description"`
	CronSpec      string `bson:"cronspec"`    // 定时设置时间
	Concurrent    int    `bson:"concurrent"`  // 是否允许并发
	Command       string `bson:"command"`     // 执行的命令
	Timeout       int    `bson:"timeout"`     // 超时的时间
	ExecuteTimes  int    `bson:"executeTime"` // 执行的时间
	PrevTime      int64  `bson:"prevtime"`
	Status        int    `bson:"status"` // 允许状态  1是执行 0是不执行
	IsNotify      int
	NotifyType    int
	NotifyTplID   int
	NotifyUserIds string
	CreateID      int
	UpdateID      int
	CreateTime    int64 `bson:"createtime"`
	UpdateTime    int64 `bson:"updatetime"`
}

var (
	// TaskTable task table
	TaskTable = "QCrontabTask"
)

// TaskGetByName 通过任务名获取任务详情
func TaskGetByName(name string) *Task {

	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)

	user := Task{}

	MongoInstance.DbHandle.Collection(TaskTable).FindOne(ctx, bson.M{"task_name": name}).Decode(&user)

	return &user
}
