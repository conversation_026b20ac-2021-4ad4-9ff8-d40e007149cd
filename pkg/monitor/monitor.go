package monitor

import (
	"context"
	"encoding/json"
	"net/url"
	"strings"

	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	httpclient "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/httpClient"
)

// MonitFunc 获取监控值方法
type MonitFunc func(hostname string) (*MonitResp, error)

// MonitConfig 监控配置项
type MonitConfig struct {
	Token        string `json:"token"`
	QueryAddress string `json:"address"` // http://************:3000/api/datasources/proxy/1/query?db=pjsh-monitor&epoch=ms
	DBIndex      string `json:"db_index"`
}

// MonitResp 监控通用返回
type MonitResp struct {
	Result []MonitRespResult `json:"results"`
}

// MonitRespResult 监控series返回
type MonitRespResult struct {
	Series []MonitSeries `json:"series"`
}

// MonitSeries 监控series详情
type MonitSeries struct {
	Columns []string    `json:"columns"`
	Tags    MonitTags   `json:"tags"`
	Name    string      `json:"name"`
	Values  [][]float32 `json:"values"`
}

// MonitTags 监控标签
type MonitTags struct {
	Host     string `json:"host"`
	Path     string `json:"path"`
	Instance string `json:"instance"`
}

func (m MonitConfig) request(ctx context.Context, sql string) (*MonitResp, error) {
	queryAddress := strings.ReplaceAll(m.QueryAddress, "{{db_index}}", m.DBIndex)
	client := httpclient.NewHttpclient()
	client.SetHeader("Authorization", "Bearer "+m.Token)
	urlPath := queryAddress + "&q=" + url.PathEscape(sql)
	logger.Debugf("grafana address: %s", urlPath)
	resp, err := client.POSTCtx(ctx, urlPath, nil, nil)
	if err != nil {
		return nil, err
	}

	var mResp MonitResp
	err = json.Unmarshal(resp, &mResp)
	if err != nil {
		return nil, err
	}

	return &mResp, nil
}

// GetCPUValue 获取CPU监控值
func (m MonitConfig) GetCPUValue(ctx context.Context, sql string, hostname string) (*MonitResp, error) {
	// cpuSQL := `SELECT mean(usage_user) as "user", mean(usage_system) as "system", mean(usage_softirq) as "softirq", mean(usage_steal) as "steal", mean(usage_nice) as "nice", mean(usage_irq) as "irq", mean(usage_iowait) as "iowait", mean(usage_guest) as "guest", mean(usage_guest_nice) as "guest_nice"  FROM "cpu" WHERE "host" =~ /{{host}}$/ and cpu = 'cpu-total' AND time >= now() - 1h GROUP BY time(10s), *`
	// cpuSQL := `SELECT mean("Percent_Privileged_Time") AS "% Priviledged Time", mean("Percent_User_Time") AS "% User Time", mean("Percent_Interrupt_Time") AS "% Interrupt Time" FROM "win_cpu" WHERE ("host" =~ /^{{host}}$/) AND time >= now() - 1h GROUP BY time(2s) fill(linear)`

	return m.request(ctx, strings.ReplaceAll(sql, "{{host}}", hostname))
}

// GetMemValue 获取内存监控值
func (m MonitConfig) GetMemValue(ctx context.Context, sql string, hostname string) (*MonitResp, error) {
	// memSQL := `SELECT mean(total) as total, mean(used) as used, mean(cached) as cached, mean(free) as free, mean(buffered) as buffered  FROM "mem" WHERE host =~ /{{host}}$/ AND time >= now() - 1h GROUP BY time(10s), host ORDER BY asc`
	// memSQL := `SELECT mean("Available_Bytes") as "Available Bytes", mean("Pool_Nonpaged_Bytes") as "Pool Nonpaged Bytes", mean("Pool_Paged_Bytes") as "Pool Paged Bytes" FROM "win_mem" WHERE host =~ /{{host}}$/ AND time >= now() - 1h GROUP BY time(2s), host fill(linear) ORDER BY asc`

	return m.request(ctx, strings.ReplaceAll(sql, "{{host}}", hostname))
}

// GetIOValue 获取IO监控值
func (m MonitConfig) GetIOValue(ctx context.Context, sql, hostname string) (*MonitResp, error) {
	// ioSQL := `SELECT non_negative_derivative(mean(read_bytes),1s) as "read" FROM "diskio" WHERE "host" =~ /{{host}}$/ AND "name" =~ /()$/  AND time >= now() - 1h GROUP BY time(10s), *;SELECT non_negative_derivative(mean(write_bytes),1s) as "write" FROM "diskio" WHERE "host" =~ /{{host}}$/ AND "name" =~ /()$/ AND time >= now() - 1h GROUP BY time(10s), *`
	// ioSQL := `SELECT mean("Percent_Disk_Read_Time") AS "% Disk Read Time", mean("Percent_Disk_Write_Time") AS "% Disk Write Time" FROM "win_disk" WHERE "host" =~ /{{host}}$/ AND "instance" =~ /^E:$/ AND time >= now() - 24h GROUP BY time(1m), "host", instance fill(0)`
	// ioSQL := `SELECT non_negative_derivative(mean(read_bytes),1s) as "read" FROM "diskio" WHERE "host" =~ /{{host}}$/ AND "name" =~ /()$/  AND time >= now() - 1h GROUP BY time(10s), *;SELECT non_negative_derivative(mean(write_bytes),1s) as "write" FROM "diskio" WHERE "host" =~ /{{host}}$/ AND "name" =~ /()$/ AND time >= now() - 1h GROUP BY time(10s), *`

	return m.request(ctx, strings.ReplaceAll(sql, "{{host}}", hostname))
}

// GetNetValue 获取网络监控值
func (m MonitConfig) GetNetValue(ctx context.Context, sql, hostname string) (*MonitResp, error) {
	// netSQL := `SELECT non_negative_derivative(mean(bytes_recv),1s)*8 as "in"  FROM "net" WHERE host =~ /{{host}}/ AND interface =~ /()/ AND time >= now() - 1h GROUP BY time(10s), * fill(none);SELECT non_negative_derivative(mean(bytes_sent),1s)*8 as "out" FROM "net" WHERE host =~ /{{host}}/ AND interface =~ /()/ AND time >= now() - 1h GROUP BY time(10s), * fill(none)`
	// netSQL := `SELECT mean("Bytes_Received_persec") AS "Bytes Received persec", mean("Bytes_Sent_persec") AS "Bytes Sent persec" FROM "win_net" WHERE "host" =~ /{{host}}$/ AND "instance" =~ /Aquantia AQtion 10Gbit Network Adapter$/  AND time >= now() - 24h GROUP BY time(1m), "host", instance fill(linear)`

	return m.request(ctx, strings.ReplaceAll(sql, "{{host}}", hostname))
}

// GetDiskValue 获取磁盘使用量
func (m MonitConfig) GetDiskValue(ctx context.Context, sql, hostname string) (*MonitResp, error) {
	return m.request(ctx, strings.ReplaceAll(sql, "{{host}}", hostname))
}

// GetUPTimeValue 获取运行时间
func (m MonitConfig) GetUPTimeValue(ctx context.Context, sql, hostname string) (*MonitResp, error) {
	return m.request(ctx, strings.ReplaceAll(sql, "{{host}}", hostname))
}
