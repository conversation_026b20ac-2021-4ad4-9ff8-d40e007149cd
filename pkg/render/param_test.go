package render

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"testing"
)

func TestParamRender(t *testing.T) {
	input := `{"form": {"InstanceName": "test-xxxxx"}, "instance_id": "test_instance_id"}`
	output, _ := GetRenderedParam(input, "form", true)
	fmt.Println(output)
	fmt.Println(gzipBase64Decode(output))
	output, _ = GetRenderedParam(input, "instance_id", false)
	fmt.Println(output)
	fmt.Println(gzipBase64Decode("H4sIAAAAAAAA/wEAAP//AAAAAAAAAAA="))
}

func gzipBase64Decode(s string) (string, error) {
	decodeRes, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		return "", err
	}

	// Gzip decompress the decoded data
	var buf bytes.Buffer
	buf.Write(decodeRes)
	gz, err := gzip.NewReader(&buf)
	if err != nil {
		return "", err
	}
	defer gz.Close()

	// Read the decompressed data
	decompressed := new(bytes.Buffer)
	if _, err := decompressed.ReadFrom(gz); err != nil {
		return "", err
	}

	return decompressed.String(), nil
}

func TestBoolParamRender(t *testing.T) {
	rawData := `{"InstanceChargeType":"PrePaid","os_type":"linux","ImageId":"m-uf6dnd8eq0gan3xtcxk7","InstanceType":"ecs.c7.large","Period":1,"PeriodUnit":"Month","AutoRenew":true,"AutoRenewPeriod":1,"InstanceTypeMap":{},"LoginType":"PasswordInherit","Password":"","PasswordInherit":false,"KeyPairName":"","SystemDisk":{"PerformanceLevel":"PL0","Category":"cloud_essd","Size":"50"},"DataDisk":[{"PerformanceLevel":"PL0","Category":"cloud_essd","Size":50,"IOPS":3000,"DeviceName":"","Throughput":125,"locked":true}],"InternetChargeType":"PayByTraffic","InternetMaxBandwidthOut":0,"VpcId":"vpc-uf6ou04yl9ajdovvpeg28","SecurityGroupIds":["sg-uf6ezx26yhagj3cjjqec"],"UserData":"","InstanceName":"","HostName":"nap-dev-xieyi-cn-test[0002,4]","Tag":[],"Amount":1,"eqHostName":false,"reason":"测试ipv6","privatePool":{"value":"none","id":""},"serviceTreeNodes":[],"bindEIP":{"enable":false,"segIDs":[]},"bindDDos":{"enable":false,"packageID":""},"ipv6":{"enable":true,"public_enable":true,"public_paytype":"PayByTraffic","public_bandwidth":10},"ZoneMappings":[{"ZoneId":"cn-shanghai-m","VSwitchId":"vsw-uf6jtvewm7lu18vmv4td0"},{"ZoneId":"cn-shanghai-n","VSwitchId":"vsw-uf6x0ovni67cul6savurx"}],"RegionId":"cn-shanghai","RegionName":"华东2（上海）","InstanceTypeDetail":{"value":"","label":"","MemorySize":4,"CPUCoreCount":2,"InstanceTypeFamily":"ecs.c7","InstanceTypeID":"ecs.c7.large","Status":"Available"},"InternetMaxdwidthOut":0,"UniqueSuffix":true,"VSwitchId":"","IPV6":{"enable":true,"public_enable":true,"public_paytype":"PayByBandwidth","public_bandwidth":5},"IoOptimized":"optimized"}`
	formMap := map[string]interface{}{}
	json.Unmarshal([]byte(rawData), &formMap)
	data := map[string]interface{}{
		"form":          formMap,
		"instance_id":   "test",
		"instance_name": "terst",
	}
	inputJson, err := json.Marshal(data)
	if err != nil {
		t.Fatal(err)
	}
	output, _ := GetRenderedParam(string(inputJson), "form.ipv6.enable", false)
	fmt.Println(output)
}
