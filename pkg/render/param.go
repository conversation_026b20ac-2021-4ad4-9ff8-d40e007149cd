package render

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"

	"github.com/tidwall/gjson"
)

func GetRenderedParam(inputJSON, value string, enableGzipBase64 bool) (newValue string, err error) {
	res := gjson.Get(inputJSON, value)
	output := res.String()
	if enableGzipBase64 {
		gzipRes, err := Gzip(output)
		if err != nil {
			return "", err
		}
		output = base64.StdEncoding.EncodeToString(gzipRes)
	}

	return output, nil
}

func GetBuiltinParams(intpuJSON, name string) (newValue string, err error) {
	switch name {
	// 目前只有一个特殊处理的变量
	case "form_gzip_base64":
		return GetRenderedParam(intpuJSON, "form", true)
	default:
		return GetRenderedParam(intpuJSON, name, false)
	}
}

func Gzip(s string) ([]byte, error) {
	var b bytes.Buffer
	w := gzip.NewWriter(&b)
	if _, err := w.Write([]byte(s)); err != nil {
		return nil, err
	}
	if err := w.Close(); err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}
