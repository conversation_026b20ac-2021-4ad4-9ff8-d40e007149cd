package opnotifymansdk

import (
	"context"
	"encoding/json"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	httpclient "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/httpClient"
)

// QNotify 请求体
type QNotify struct {
	Name             string   `json:"name"`                  // 任务名称
	Message          string   `json:"message"`               // 发送消息
	PlateFromID      string   `json:"plateFromId"`           // 平台id
	ToolID           string   `json:"toolId"`                // 工具id
	TemplateID       string   `json:"templateId,omitempty"`  // 模板ID
	TemplateMsg      string   `json:"TemplateMsg,omitempty"` // 模板消息
	PhoneNumber      string   `json:"phoneNumber,omitempty"` // 电话号码
	ToEmail          []string `json:"toEmail,omitempty"`     // 电子邮件
	Subject          string   `json:"subject,omitempty"`     // 邮件消息主题
	MikuTitle        string   `json:"mikuTitle,omitempty"`   // Miku 消息主题
	MikuUser         []string `json:"mikuUser,omitempty"`    // miku 用户
	NotifyManAddress string   `json:"-"`
}

// SendMessage 发消息
func SendMessage(ctx context.Context, input *QNotify) error {
	data, err := json.Marshal(input)
	header := httpclient.JSONReqHeader
	token, err := cfg.GetIAMCliToken("cur")
	if err != nil {
		return err
	}
	header.Set("Authorization", "Bearer "+token)
	result, err := httpclient.NewHttpclient().POSTCtx(ctx, input.NotifyManAddress, header, data)
	logger.Infof("SendMessage.resp: %s", string(result))
	return err
}

// Struct2Map 转换效率不高，避免在查询时使用，仅限于数据写入时使用
func Struct2Map(v interface{}) (map[string]interface{}, error) {
	buf, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}
	data := make(map[string]interface{})
	if err := json.Unmarshal(buf, &data); err != nil {
		return nil, err
	}
	return data, nil
}
