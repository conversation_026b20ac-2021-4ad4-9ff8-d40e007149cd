package cmdb

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

func convertHostEntity(single entity.HostResource) AliEcs {
	obj := AliEcs{}
	obj.BkInstName = fmt.Sprintf("%v_%v", single.InstanceID, single.RegionID)
	obj.HostName = single.HostName
	obj.InstanceName = single.InstanceName

	publicIPAddress := single.PublicIPAddress
	if publicIPAddress == nil {
		publicIPAddress = []string{}
	}
	publicIPAddressJSON, _ := json.Marshal(map[string]interface{}{
		"IpAddress": publicIPAddress,
	})
	obj.PublicIPAddress = string(publicIPAddressJSON)

	innerIPAddress := single.InnerIPAddress
	if innerIPAddress == nil {
		innerIPAddress = []string{}
	}
	innerIPAddressJSON, _ := json.Marshal(map[string]interface{}{
		"IpAddress": innerIPAddress,
	})
	obj.InnerIPAddress = string(innerIPAddressJSON)

	eipJSON, _ := json.Marshal(single.EipAddress)
	obj.EipAddress = string(eipJSON)

	if len(single.InnerIPAddress) != 0 {
		privateIPAddressArray := make([]map[string]interface{}, 0)
		privateIPAddress := make(map[string]interface{})
		privateIPAddress["PrimaryIpAddress"] = single.InnerIPAddress[0]
		privateIPAddressArray = append(privateIPAddressArray, privateIPAddress)
		NetworkInterface := make(map[string]interface{})
		NetworkInterface["NetworkInterface"] = privateIPAddressArray
		privateIPAddressJSON, _ := json.Marshal(NetworkInterface)
		obj.NetworkInterfaces = string(privateIPAddressJSON)
	} else {
		obj.NetworkInterfaces = "{}"
	}

	// vpc to string
	vpcJSON, err := json.Marshal(single.VpcAttributes)
	if err != nil {
		logrus.Error(err)
	}
	vpcString := string(vpcJSON)

	obj.CPU = single.CPU
	obj.Description = single.Description
	obj.VpcAttributes = vpcString
	obj.Memory = single.Memory
	obj.ZoneID = single.ZoneID
	obj.InstanceID = single.InstanceID
	obj.ImageID = single.ImageID
	obj.Status = single.Status
	obj.ZoneID = single.ZoneID
	obj.InstanceType = single.InstanceType
	obj.Agentid = single.AgentID
	obj.UpdateTime = time.Now().Unix()
	obj.RegionID = single.RegionID
	obj.InstanceChargeType = single.InstanceChargeType
	// +0时区传到cmdb会无法正确解析，这里在保证时间正确的前提下全部转到+8
	if single.CreationTime > 0 {
		creationTime := time.Unix(single.CreationTime, 0)
		obj.CreationTime = creationTime.In(time.FixedZone("", 28800)).Format("2006-01-02T15:04:05-07:00")
	}
	startTime, err := time.Parse("2006-01-02T15:04Z07:00", single.StartTime)
	if err == nil {
		// for aliyun
		obj.StartTime = startTime.In(time.FixedZone("", 28800)).Format("2006-01-02T15:04:05-07:00")
	} else {
		startTime, err := time.Parse("2006-01-02 15:04:05 Z0700 MST", single.StartTime)
		if err == nil {
			// for aws
			obj.StartTime = startTime.In(time.FixedZone("", 28800)).Format("2006-01-02T15:04:05-07:00")
		}
	}
	autoReleaseTime, err := time.Parse("2006-01-02T15:04Z07:00", single.AutoReleaseTime)
	if err == nil {
		obj.AutoReleaseTime = autoReleaseTime.In(time.FixedZone("", 28800)).Format("2006-01-02T15:04:05-07:00")
	}

	return obj
}

func convertMysqlEntity(single entity.MysqlClusterResource) AliPorlardb {
	tt := time.Now().Unix()
	obj := AliPorlardb{}
	obj.BkInstName = single.DBClusterID
	obj.Dbclusterdescription = single.DBClusterDescription
	obj.Dbtype = single.DBType
	obj.Dbclusterstatus = single.DBClusterStatus
	obj.Regionid = single.RegionID
	obj.Zoneid = single.ZoneID
	obj.Dbversion = single.DBVersion
	obj.Paytype = single.PayType
	obj.Engine = single.Engine
	obj.Resourcegroupid = single.ResourceGroupID
	obj.Expired = single.Expired
	obj.Lockmode = single.LockMode
	obj.Createtime = single.CreateTime
	obj.UpdateTime = tt

	singleMysqlHostAttrubite := PorlardbAddress{
		PrimaryPrivateAddress: "",
		PrimaryPrivatePort:    "",
		PrimaryPublicAddress:  "",
		PrimaryPublicPort:     "",
		ClusterPrivateAddress: "",
		ClusterPrivatePort:    "",
		ClusterPublicAddress:  "",
		ClusterPublicPort:     "",
		FlagPrimaryPrivate:    false,
		FlagPrimaryPublic:     false,
		FlagClusterPrivate:    false,
		FlagClusterPublic:     false,
	}

	resp, _ := models.MysqlClusterEndpointResourceModel.FindMany(context.Background(), map[string]interface{}{
		"DBClusterID": single.DBClusterID,
	})

	var epList []*cloudman.MysqlEndpoint
	epListJSON, _ := json.Marshal(resp)
	_ = json.Unmarshal(epListJSON, &epList)
	//currentEndpoint := &cloudman.MysqlEndpoints{List: nil}
	//r := handlers.MysqlRes{}
	//currentEndpoint, _ = r.GetEndpoints(context.Background(), &cloudman.ObjectID{Id: single.DBClusterID})

	for _, singleEndpoint := range epList {
		if singleEndpoint.EndpointType == "Primary" {
			for _, addressItemSigle := range singleEndpoint.AddressItems {
				if addressItemSigle.NetType == "Public" {
					singleMysqlHostAttrubite.PrimaryPublicAddress = addressItemSigle.ConnectionString
					singleMysqlHostAttrubite.PrimaryPublicPort = addressItemSigle.Port
					singleMysqlHostAttrubite.FlagPrimaryPublic = true
				}
				if addressItemSigle.NetType == "Private" {
					singleMysqlHostAttrubite.PrimaryPrivateAddress = addressItemSigle.ConnectionString
					singleMysqlHostAttrubite.PrimaryPrivatePort = addressItemSigle.Port
					singleMysqlHostAttrubite.FlagPrimaryPrivate = true
				}
			}
		}

		if singleEndpoint.EndpointType == "Cluster" {
			for _, addressItemSigle := range singleEndpoint.AddressItems {
				if addressItemSigle.NetType == "Public" {
					singleMysqlHostAttrubite.ClusterPublicAddress = addressItemSigle.ConnectionString
					singleMysqlHostAttrubite.ClusterPublicPort = addressItemSigle.Port
					singleMysqlHostAttrubite.FlagClusterPublic = true
				}
				if addressItemSigle.NetType == "Private" {
					singleMysqlHostAttrubite.ClusterPrivateAddress = addressItemSigle.ConnectionString
					singleMysqlHostAttrubite.ClusterPrivatePort = addressItemSigle.Port
					singleMysqlHostAttrubite.FlagClusterPrivate = true
				}
			}
		}
	}

	if singleMysqlHostAttrubite.ClusterPrivatePort != "" && singleMysqlHostAttrubite.FlagClusterPrivate == true {
		obj.ClusterPrivatePort = singleMysqlHostAttrubite.ClusterPrivatePort
	}
	if singleMysqlHostAttrubite.ClusterPrivateAddress != "" && singleMysqlHostAttrubite.FlagClusterPrivate == true {
		obj.ClusterPrivateAddress = singleMysqlHostAttrubite.ClusterPrivateAddress
	}
	if singleMysqlHostAttrubite.ClusterPublicPort != "" && singleMysqlHostAttrubite.FlagClusterPublic == true {
		obj.ClusterPublicPort = singleMysqlHostAttrubite.ClusterPublicPort
	}
	if singleMysqlHostAttrubite.ClusterPublicAddress != "" && singleMysqlHostAttrubite.FlagClusterPublic == true {
		obj.ClusterPublicAddress = singleMysqlHostAttrubite.ClusterPublicAddress
	}
	if singleMysqlHostAttrubite.PrimaryPrivatePort != "" && singleMysqlHostAttrubite.FlagPrimaryPrivate == true {
		obj.PrimaryPrivatePort = singleMysqlHostAttrubite.PrimaryPrivatePort
	}
	if singleMysqlHostAttrubite.PrimaryPrivateAddress != "" && singleMysqlHostAttrubite.FlagPrimaryPrivate == true {
		obj.PrimaryPrivateAddress = singleMysqlHostAttrubite.PrimaryPrivateAddress
	}
	if singleMysqlHostAttrubite.PrimaryPublicPort != "" && singleMysqlHostAttrubite.FlagPrimaryPublic == true {
		obj.PrimaryPublicPort = singleMysqlHostAttrubite.PrimaryPublicPort
	}
	if singleMysqlHostAttrubite.PrimaryPublicAddress != "" && singleMysqlHostAttrubite.FlagPrimaryPublic == true {
		obj.PrimaryPublicAddress = singleMysqlHostAttrubite.PrimaryPublicAddress
	}

	if obj.ClusterPrivatePort == "" || obj.ClusterPrivateAddress == "" {
		fmt.Println("--发现集群内网地址和端口为空--")
		fmt.Println(obj)
	}
	return obj
}

func convertDatabaseEntity(singleDatabases entity.MysqlDatabaseResource) AliDatabase {
	obj := AliDatabase{}
	obj.BkInstName = singleDatabases.ClusterID + "_" + singleDatabases.DBName
	obj.Dbname = singleDatabases.DBName
	obj.DBDescription = singleDatabases.DBDescription
	obj.CharacterSetName = singleDatabases.CharacterSetName
	obj.DBstatus = singleDatabases.DBStatus
	obj.ClusterID = singleDatabases.ClusterID
	return obj
}

func convertRedisEntity(single entity.RedisResource) AliKvStore {
	raw := make(AliKvStore)
	inrec, _ := json.Marshal(single)
	json.Unmarshal(inrec, &raw)
	raw["InstanceId"] = single.InstanceID
	raw["bk_inst_name"] = single.InstanceID
	raw["RegionId"] = single.RegionID
	delete(raw, "RegionID")
	delete(raw, "CreateTime")
	delete(raw, "EndTime")
	delete(raw, "DestroyTime")
	return raw
}

func convertLBEntity(single entity.LoadBalancer) AliLB {
	obj := AliLB{
		BkInstName: single.LoadBalancerName,
		BKSN:       single.LoadBalancerID,
		BkVendor:   single.IspType,
	}
	return obj
}

var statusMap = map[string]string{
	"Running": "3",
	"Stopped": "5",
}

func IsDiffHostEntityWithBkHost(entity *entity.HostResource, host *BkHost) bool {
	status, ok := statusMap[entity.Status]
	if !ok {
		status = "1"
	}
	outerIP := ""
	if entity.EipAddress.IPAddress != "" {
		outerIP = entity.EipAddress.IPAddress
	}
	if entity.HostName != host.BkHostname {
		return true
	} else if entity.InstanceName != host.InstanceName {
		return true
	} else if status != host.BkCloudHostStatus {
		return true
	} else if outerIP != "" && outerIP != host.BkHostOuterIp {
		return true
	}

	return false
}

func conevertEntityToHost(entity *entity.HostResource) *BkHost {
	innerIP := ""
	if len(entity.InnerIPAddress) > 0 {
		innerIP = entity.InnerIPAddress[0]
	}
	outerIP := ""
	if entity.EipAddress.IPAddress != "" {
		outerIP = entity.EipAddress.IPAddress
	}
	host := &BkHost{
		BKHostInnerIP: innerIP, // 目前这里取第一个ip
		BkHostOuterIp: outerIP, // 目前这里为空
		BkAssetID:     entity.InstanceID,
		BkSn:          entity.ID.Hex(), // 云管平台的数据库ID
		BkComment:     entity.Description,
		BkHostname:    entity.HostName,
		BkCloudInstID: entity.InstanceID,
		Zone:          entity.ZoneID,
		AgentID:       entity.AgentID,
		BkOSName:      entity.OSName,
		InstanceName:  entity.InstanceName,
		ImageID:       entity.ImageID,
	}

	// CPU核数, 有限制不能小于1
	if entity.CPUOptions.CoreCount >= 1 {
		host.BkCPU = &entity.CPU
	}
	// 内存大小, 有限制不能小于1
	if entity.Memory >= 1 {
		host.BkMem = &entity.Memory
	}

	status, ok := statusMap[entity.Status]
	if !ok {
		status = "1"
	}
	host.BkCloudHostStatus = status

	host.CreateTime = time.Unix(entity.CreationTime, 0).Format("2006-01-02 15:04:05")

	tagsByte, err := json.Marshal(entity.Tags)
	if err == nil {
		host.Tags = string(tagsByte)
	}

	if entity.BkCmdb != nil && entity.BkCmdb.HostID != 0 {
		host.BKHostID = &entity.BkCmdb.HostID
	}

	return host
}
