package cmdb

import (
	"encoding/json"
)

// cmdb模型名称
const (
	ALIPolarDB  string = "rds"
	ALIEcs      string = "host"
	ALIKVStore  string = "redis"
	ALIDatabase string = "polardb_database"
	ALILB       string = "bk_load_balance"
)

// CmdbTypeMap -
var CmdbTypeMap = map[string]string{
	"host":  ALIEcs,
	"mysql": ALIPolarDB,
	"redis": ALIKVStore,
}

const (
	IdleBizID = 1
)

// CommonResponse -
type CommonResponse struct {
	Result      bool            `json:"result"`
	BKErrorCode int             `json:"bk_error_code"`
	BKErrorMsg  string          `json:"bk_error_msg"`
	Permission  string          `json:"permission"`
	Data        json.RawMessage `json:"data"`
}

// BatchResult -
type BatchResult struct {
	Errors         []string      `json:"error"`
	Success        []interface{} `json:"success"`
	SuccessCreated []int64       `json:"success_created"`
	SuccessUpdated []int64       `json:"success_updated"`
	UpdateErrors   []string      `json:"update_error"`
}

type bkCmdbQueryResult struct {
	Result      bool `json:"result"`
	BkErrorCode int  `json:"bk_error_code"`
	Data        struct {
		Count int               `json:"count"`
		Info  []json.RawMessage `json:"info"`
	}
}

type bkCmdbDeleteRequest struct {
	Delete struct {
		InstIDs []int `json:"inst_ids"`
	} `json:"delete"`
}

type bkCmdbHostDeleteRequest struct {
	BkHostIDs string `json:"bk_host_id"`
}

// AliEcs -
type AliEcs struct {
	BkInstName         string `json:"bk_inst_name,omitempty"`
	HostName           string `json:"HostName,omitempty"`
	InstanceName       string `json:"InstanceName,omitempty"`
	PublicIPAddress    string `json:"PublicIpAddress,omitempty"`
	InnerIPAddress     string `json:"InnerIpAddress,omitempty"`
	EipAddress         string `json:"EipAddress,omitempty"`
	NetworkInterfaces  string `json:"NetworkInterfaces,omitempty"`
	CPU                int32  `json:"Cpu,omitempty"`
	Description        string `json:"Description,omitempty"`
	VpcAttributes      string `json:"VpcAttributes,omitempty"`
	Memory             int32  `json:"Memory,omitempty"`
	ZoneID             string `json:"ZoneId,omitempty"`
	InstanceID         string `json:"InstanceId,omitempty"`
	ImageID            string `json:"ImageId,omitempty"`
	Status             string `json:"Status,omitempty"`
	InstanceType       string `json:"InstanceType,omitempty"`
	Agentid            string `json:"Agentid,omitempty"`
	UpdateTime         int64  `json:"updateTime,omitempty"`
	RegionID           string `json:"RegionId,omitempty"`
	CreationTime       string `json:"CreationTime,omitempty"`
	StartTime          string `json:"StartTime,omitempty"`
	AutoReleaseTime    string `json:"AutoReleaseTime,omitempty"`
	InstanceChargeType string `json:"InstanceChargeType,omitempty"`
}

// AliPorlardb -
type AliPorlardb struct {
	BkInstName            string `json:"bk_inst_name,omitempty"`
	Dbclusterdescription  string `json:"dbclusterdescription,omitempty"`
	Dbtype                string `json:"dbtype,omitempty"`
	Dbclusterstatus       string `json:"dbclusterstatus,omitempty"`
	Regionid              string `json:"regionid,omitempty"`
	Zoneid                string `json:"zoneid,omitempty"`
	Dbversion             string `json:"dbversion,omitempty"`
	Paytype               string `json:"paytype,omitempty"`
	Engine                string `json:"engine,omitempty"`
	Resourcegroupid       string `json:"resourcegroupid,omitempty"`
	Expired               string `json:"expired,omitempty"`
	Lockmode              string `json:"lockmode,omitempty"`
	Createtime            string `json:"createtime,omitempty"`
	UpdateTime            int64  `json:"updateTime,omitempty"`
	ClusterPrivatePort    string `json:"ClusterPrivatePort,omitempty"`
	ClusterPrivateAddress string `json:"ClusterPrivateAddress,omitempty"`
	ClusterPublicPort     string `json:"ClusterPublicPort,omitempty"`
	ClusterPublicAddress  string `json:"ClusterPublicAddress,omitempty"`
	PrimaryPrivatePort    string `json:"PrimaryPrivatePort,omitempty"`
	PrimaryPrivateAddress string `json:"PrimaryPrivateAddress,omitempty"`
	PrimaryPublicPort     string `json:"PrimaryPublicPort,omitempty"`
	PrimaryPublicAddress  string `json:"PrimaryPublicAddress,omitempty"`
}

// AliDatabase -
type AliDatabase struct {
	BkInstName       string `json:"bk_inst_name,omitempty"`
	Dbname           string `json:"dbname,omitempty"`
	DBDescription    string `json:"DBDescription,omitempty"`
	CharacterSetName string `json:"CharacterSetName,omitempty"`
	DBstatus         string `json:"DBstatus,omitempty"`
	ClusterID        string `json:"clusterId,omitempty"`
}

type AliLB struct {
	BkAssetID  string `json:"bk_asset_id,omitempty"`  // 实例ID
	BkInstName string `json:"bk_inst_name,omitempty"` // 实例名称
	BKSN       string `json:"bk_sn"`                  // 唯一序号
	BkModel    string `json:"bk_model"`               // 型号
	BkVendor   string `json:"bk_vendor"`              // 厂商
	RegionID   string `json:"RegionID"`               // 区域ID
}

// AliKvStore -
type AliKvStore map[string]interface{}

// PorlardbAddress -
type PorlardbAddress struct {
	PrimaryPrivateAddress string
	PrimaryPrivatePort    string
	PrimaryPublicAddress  string
	PrimaryPublicPort     string
	ClusterPrivateAddress string
	ClusterPrivatePort    string
	ClusterPublicAddress  string
	ClusterPublicPort     string
	FlagPrimaryPrivate    bool
	FlagPrimaryPublic     bool
	FlagClusterPrivate    bool
	FlagClusterPublic     bool
}

// AssocQueryReq -
type AssocQueryReq struct {
	BaseObjType string
	InstType    string
	InstID      int
	TargetType  string
	TargetID    int
	Relation    string
}

type BkHost struct {
	BKHostID          *int32  `json:"bk_host_id,omitempty"`
	BKHostInnerIP     string  `json:"bk_host_innerip"`
	BkHostOuterIp     string  `json:"bk_host_outerip"`
	BkAssetID         string  `json:"bk_asset_id"`
	BkSn              string  `json:"bk_sn"`
	BkComment         string  `json:"bk_comment"`
	BkHostname        string  `json:"bk_host_name"`
	BkCPU             *int32  `json:"bk_cpu"`
	BkMem             *int32  `json:"bk_mem"`
	CreateTime        string  `json:"create_time"`
	BkCloudInstID     string  `json:"bk_cloud_inst_id"`
	BkCloudHostStatus string  `json:"bk_cloud_host_status"`
	Zone              string  `json:"zone"`
	AgentID           string  `json:"agent_id"`
	BkOSName          string  `json:"bk_os_name"`
	InstanceName      string  `json:"InstanceName"`
	ImageID           string  `json:"ImageId"`
	Tags              string  `json:"tags"`
	BkState           *string `json:"bk_state,omitempty"`
}

// IPInfo TODO
// ip search info
type IPInfo struct {
	Data  []string `json:"data"`
	Exact int64    `json:"exact"`
	Flag  string   `json:"flag"`
}

// HostCommonSearch the structure of the host common query condition
type HostCommonSearch struct {
	AppID     int64             `json:"bk_biz_id,omitempty"`
	Ipv4Ip    IPInfo            `json:"ip"`
	Ipv6Ip    IPInfo            `json:"ipv6"`
	Condition []SearchCondition `json:"condition"`
	Page      BasePage          `json:"page"`
}

// BasePage for paging query
type BasePage struct {
	Sort        string `json:"sort,omitempty" mapstructure:"sort"`
	Limit       int    `json:"limit,omitempty" mapstructure:"limit"`
	Start       int    `json:"start" mapstructure:"start"`
	EnableCount bool   `json:"enable_count,omitempty" mapstructure:"enable_count,omitempty"`
}

// SearchCondition TODO
// search condition
type SearchCondition struct {
	Fields    []string        `json:"fields"`
	Condition []ConditionItem `json:"condition"`
	ObjectID  string          `json:"bk_obj_id"`
}

// ConditionItem subcondition
type ConditionItem struct {
	Field    string      `json:"field,omitempty"`
	Operator string      `json:"operator,omitempty"`
	Value    interface{} `json:"value,omitempty"`
}

type TopoData struct {
	TopSets []*TopoSet `json:"child"`
}

type TopoSet struct {
	BkInstID   int32         `json:"bk_inst_id"`
	BkInstName string        `json:"bk_inst_name"`
	Modules    []*TopoModule `json:"child"`
}

type TopoModule struct {
	BkInstID   int32  `json:"bk_inst_id"`
	BkInstName string `json:"bk_inst_name"`
}

type HostSearchData struct {
	Count int64       `json:"count"`
	Info  []*HostInfo `json:"info"`
}

type HostInfo struct {
	BizInfo    []*BizInfo    `json:"biz"`
	HostInfo   *BkHost       `json:"host"`
	ModuleInfo []*ModuleInfo `json:"module"`
}

func (h *HostInfo) GetBizID() int32 {
	if len(h.BizInfo) == 0 {
		// 不存在则返回-1
		return -1
	}

	return h.BizInfo[0].BkBizID
}

type BizInfo struct {
	BkBizID int32 `json:"bk_biz_id"`
}

type ModuleInfo struct {
	BkModuleID   int32  `json:"bk_module_id"`
	BkModuleName string `json:"bk_module_name"`
}

type ServerListResp struct {
	Retcode int64          `json:"retcode"`
	Data    ServerListData `json:"data"`
}

type ServerListData struct {
	Columns []string              `json:"columns"`
	Items   []*ServerListDataItem `json:"items"`
	Total   int                   `json:"total"`
}

type ServerListDataItem struct {
	ID         int64  `json:"id"`
	RegionName string `json:"region_name"`
	Des        string `json:"des"`
	Name       string `json:"name"`
	IsEnable   int    `json:"is_enable"`
}

type TransferHostReq struct {
	BkModuleID                 int32      `json:"bk_module_id"`
	BkBizID                    int32      `json:"bk_biz_id"`
	Instances                  []Instance `json:"instances"`
	HostApplyConflictResolvers []string   `json:"host_apply_conflict_resolvers"`
}

type TransferHostReqV2 struct {
	BkHostIds       []int32        `json:"bk_host_ids"`
	IsRemoveFromAll bool           `json:"is_remove_from_all"`
	AddToModules    []int32        `json:"add_to_modules"`
	RemoveFromNode  RemoveFromNode `json:"remove_from_node"`
}

type RemoveFromNode struct {
	BkInstId int32  `json:"bk_inst_id"`
	BkObjId  string `json:"bk_obj_id"`
}

type MoveIdleReq struct {
	BkBizID   int32   `json:"bk_biz_id"`
	BkHostIDs []int32 `json:"bk_host_id"`
}

type ChangeStatesReq struct {
	BkState   string `json:"bk_state"`
	BkHostIDs string `json:"bk_host_id"`
}

type MoveResourceReq struct {
	BkBizID    int32   `json:"bk_biz_id"`
	BkHostIDs  []int32 `json:"bk_host_id"`
	BkModuleID int32   `json:"bk_module_id"`
}

type Instance struct {
	BkHostID            int32    `json:"bk_host_id"`
	ServiceInstanceName string   `json:"service_instance_name"`
	Processes           []string `json:"processes"`
}

type CreateSetReq struct {
	Sets []SetCreation `json:"sets"`
}

type SetCreation struct {
	SetTemplateID int32  `json:"set_template_id"`
	BkSetName     string `json:"bk_set_name"`
	BkParentID    int32  `json:"bk_parent_id"`
	BkBizID       int32  `json:"bk_biz_id"`
}

type SetRespData struct {
	Index   int32   `json:"index"`
	SetData SetData `json:"data"`
}

type SetData struct {
	BkBizID int32 `json:"bk_biz_id"`
	BkSetID int32 `json:"bk_set_id"`
}

const CCErrCoreServiceHostNotBelongBusiness = 1113002 // 主机不属于业务
