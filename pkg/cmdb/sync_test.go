package cmdb

import (
	"context"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
)

func TestCreate(t *testing.T) {
	entity1 := entity.HostResource{
		InnerIPAddress: []string{"************"},
		InstanceID:     "i-test0012",
		Description:    "test_desc",
		HostName:       "test-hostname-1",
		CPUOptions: entity.InstanceCPUOptions{
			CoreCount: 4,
		},
		Memory:       4096,
		ZoneID:       "test-zoneid-1",
		CreationTime: time.Now().Unix(),
	}
	entity1.ID = primitive.NewObjectID()

	entity2 := entity.HostResource{
		InnerIPAddress: []string{"************"},
		InstanceID:     "i-test002",
		Description:    "test_desc",
		HostName:       "test-hostname-1",
		CPUOptions: entity.InstanceCPUOptions{
			CoreCount: 4,
		},
		Memory:       4096,
		ZoneID:       "test-zoneid-1",
		CreationTime: time.Now().Unix(),
	}
	entity2.ID = primitive.NewObjectID()

	res, err := CreateCmdbHosts(logrus.StandardLogger(), []entity.HostResource{
		entity1, entity2,
	})

	t.Errorf("err is %v, res: %+v", err, res)
}

func TestUpdate(t *testing.T) {
	entity1 := entity.HostResource{
		InnerIPAddress: []string{"************"},
		InstanceID:     "i-test0012",
		Description:    "test_desc",
		HostName:       "test-hostname-2",
		CPUOptions: entity.InstanceCPUOptions{
			CoreCount: 4,
		},
		Memory:       4096,
		ZoneID:       "test-zoneid-1",
		CreationTime: time.Now().Unix(),
		BkCmdb: &entity.BkCmdbInfo{
			HostID: 263,
		},
	}
	entity1.ID = primitive.NewObjectID()

	entity2 := entity.HostResource{
		InnerIPAddress: []string{"************"},
		InstanceID:     "i-test002",
		Description:    "test_desc",
		HostName:       "test-hostname-4",
		CPUOptions: entity.InstanceCPUOptions{
			CoreCount: 4,
		},
		Memory:       4096,
		ZoneID:       "test-zoneid-1",
		CreationTime: time.Now().Unix(),
		BkCmdb: &entity.BkCmdbInfo{
			HostID: 264,
		},
	}
	entity2.ID = primitive.NewObjectID()

	res, err := UpdateCmdbHosts(logrus.StandardLogger(), []entity.HostResource{
		entity1, entity2,
	})

	t.Errorf("err is %v, res: %+v", err, res)
}

func TestFind(t *testing.T) {
	hosts, err := FindBkHostByInstanceIDs(logrus.StandardLogger(), nil, nil)
	t.Errorf("err is %v, res: %+v", err, hosts)

}

func TestCompareMySql(t *testing.T) {

	ctx := context.Background()

	regionID := "cn-shanghai"
	orderID := primitive.NewObjectID()
	pr := &common.InitProvider{
		RegionID: regionID,
		Logger:   logrus.StandardLogger(),
	}
	task := CompareTask{
		OrderID:      orderID.Hex(),
		Env:          "test",
		RegionID:     regionID,
		ResourceType: []string{"mysql"},
		InstanceID:   []string{},
	}
	t.Errorf("error is %v", task.DoCompare(ctx, pr))
}

func TestCompareRedis(t *testing.T) {

	ctx := context.Background()
	regionID := "cn-shanghai"
	orderID := primitive.NewObjectID()
	pr := &common.InitProvider{
		RegionID: regionID,
		Logger:   logrus.StandardLogger(),
	}
	task := CompareTask{
		OrderID:      orderID.Hex(),
		Env:          "test",
		RegionID:     regionID,
		ResourceType: []string{"redis"},
		InstanceID:   []string{},
	}
	t.Errorf("error is %v", task.DoCompare(ctx, pr))
}

func TestCMDBRDSCreation(t *testing.T) {
	ctx := context.Background()
	id, err := AddBkCmdbObj(ctx, ALIPolarDB, &RdsCreation{
		BkInstName:      "test1",
		CreateTime:      "test2",
		DBEngine:        "test3",
		DBEngineVersion: "test4",
		DBType:          "test5",
		InstanceClass:   "test6",
		InstanceID:      "test7",
		InstanceName:    "test8",
		InstanceStatus:  "test9",
		ProviderName:    "test10",
		RegionID:        "test11",
		ZoneID:          "test12",
		VpcID:           "test13",
	})
	t.Errorf("errror is %v, id is %d", err, id)
}

func TestCMDBRedisCreation(t *testing.T) {
	ctx := context.Background()
	id, err := AddBkCmdbObj(ctx, ALIKVStore, &RedisCreation{
		BkInstName:      "test1",
		InstanceID:      "test7",
		InstanceName:    "test8",
		ProviderName:    "test10",
		VpcID:           "test13",
		VSwitchID:       "test122",
		RegionID:        "test11",
		ZoneID:          "test12",
		CreateTime:      "test2",
		InstanceClass:   "test6",
		InstanceStatus:  "test9",
		DBEngine:        "test3",
		DBEngineVersion: "test4",
	})
	t.Errorf("errror is %v, id is %d", err, id)
}

func TestCMDBUpdateHost(t *testing.T) {
	ctx := context.Background()
	entities, err := models.HostResourceModel.GetByInstanceIDs(ctx, []string{"i-6wee5l8jn830i40v012v", "i-6wehogb6x3zg724xuigy"})
	if err != nil {
		t.Fatal(err)
	}

	ee := []entity.HostResource{}
	for _, e := range entities {
		ee = append(ee, *e)
	}
	res, err := UpdateCmdbHosts(logrus.StandardLogger(), ee)
	t.Errorf("err is %v, res: %+v", err, res)
}

func TestCMDBDeleteHost(t *testing.T) {

	ctx := context.Background()
	entities, err := models.HostResourceModel.GetByInstanceIDs(ctx, []string{"i-6wee5l8jn830i40v012v", "i-6wehogb6x3zg724xuigy"})
	if err != nil {
		t.Fatal(err)
	}

	instanceIDs := []string{}
	for _, e := range entities {
		instanceIDs = append(instanceIDs, e.InstanceID)
	}
	res, err := DeleteBkCmdbNoEnv(logrus.StandardLogger(), "host", instanceIDs)
	t.Errorf("err is %v, res: %+v", err, res)
}
