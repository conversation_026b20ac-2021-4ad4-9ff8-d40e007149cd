package cmdb

import (
	"encoding/json"
	"fmt"
	"slices"
	"strings"

	"github.com/tidwall/sjson"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// CompareTask -
type CompareTask struct {
	OrderID      string
	Env          string
	RegionID     string
	ResourceType []string
	InstanceID   []string
	CmdbObj      struct {
		Host     map[string]*AliEcs
		Mysql    map[string]*AliPorlardb
		Redis    map[string]*AliKvStore
		Database map[string]*AliDatabase
		LB       map[string]*AliLB
	}
	LocalObj struct {
		Host     map[string]*entity.HostResource
		Mysql    map[string]*entity.MysqlClusterResource
		Redis    map[string]*entity.RedisResource
		Database map[string]*entity.MysqlDatabaseResource
		LB       map[string]*entity.LoadBalancer
	}
	Result struct {
		Host  CompareAction `json:"host,omitempty"`
		Mysql CompareAction `json:"mysql,omitempty"`
		Redis CompareAction `json:"redis,omitempty"`
		LB    CompareAction `json:"lb,omitempty"`
	}
	IspID string
}

// CompareAction -
type CompareAction struct {
	Add    map[string]InstanceDetail `json:"add"`
	Remove map[string]InstanceDetail `json:"remove"`
	Update map[string]InstanceDetail `json:"update"`
}

// InstanceDetail -
type InstanceDetail struct {
	ID     string   `json:"id"`
	Name   string   `json:"name"`
	Reason []string `json:"reason,omitempty"`
	Solved bool     `json:"solved"`
}

type cmdbObjT interface {
	AliEcs | AliPorlardb | AliKvStore | AliDatabase | AliLB
}
type localObjT interface {
	entity.HostResource | entity.MysqlClusterResource | entity.RedisResource | entity.MysqlDatabaseResource | entity.LoadBalancer
}

var dbPageSize = uint64(50)

func (c *CompareTask) collectLocalHostData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.LocalObj.Host = map[string]*entity.HostResource{}
	dbPage := uint64(1)
	dbTotal := dbPageSize * 2
	customFilter := map[string]any{}
	if len(instanceIDs) != 0 {
		iInstanceID := []any{}
		for _, i := range instanceIDs {
			iInstanceID = append(iInstanceID, i)
		}
		customFilter["InstanceID"] = bson.M{"$in": iInstanceID}
	}
	for (dbPage-1)*dbPageSize < dbTotal {
		resp, total, err := models.HostResourceModel.Query(ctx, &schema.HostResourceQueryParams{
			HostResourceColumnParam: schema.HostResourceColumnParam{
				AgentEnv: cmdbEnv,
				RegionID: regionID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbPageSize,
			},
			OrderParams: schema.OrderParams{},
		}, customFilter)
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			c.LocalObj.Host[item.InstanceID] = item
		}
	}
	return nil
}
func (c *CompareTask) collectLocalMysqlData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.LocalObj.Mysql = map[string]*entity.MysqlClusterResource{}
	dbPage := uint64(1)
	dbTotal := dbPageSize * 2
	customFilter := map[string]any{}
	if len(instanceIDs) != 0 {
		iInstanceID := []any{}
		for _, i := range instanceIDs {
			iInstanceID = append(iInstanceID, i)
		}
		customFilter["DBClusterId"] = bson.M{"$in": iInstanceID}
	}
	for (dbPage-1)*dbPageSize < dbTotal {
		resp, total, err := models.MysqlClusterResourceModel.Query(ctx, &schema.MysqlClusterResourceQueryParams{
			MysqlClusterResourceColumnParam: schema.MysqlClusterResourceColumnParam{
				RegionID: regionID,
				IspID:    c.IspID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbPageSize,
			},
			OrderParams: schema.OrderParams{},
		}, customFilter)
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			c.LocalObj.Mysql[item.DBClusterID] = item
		}
	}
	return nil
}

func (c *CompareTask) collectLocalRedisData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.LocalObj.Redis = map[string]*entity.RedisResource{}
	dbPage := uint64(1)
	dbTotal := dbPageSize * 2
	customFilter := map[string]any{}
	if len(instanceIDs) != 0 {
		iInstanceID := []any{}
		for _, i := range instanceIDs {
			iInstanceID = append(iInstanceID, i)
		}
		customFilter["InstanceID"] = bson.M{"$in": iInstanceID}
	}
	for (dbPage-1)*dbPageSize < dbTotal {
		resp, total, err := models.RedisResourceModel.Query(ctx, &schema.RedisResourceQueryParams{
			RedisResourceColumnParam: schema.RedisResourceColumnParam{
				RegionID: regionID,
				IspID:    c.IspID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbPageSize,
			},
			OrderParams: schema.OrderParams{},
		}, customFilter)
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			c.LocalObj.Redis[item.InstanceID] = item
		}
	}
	return nil
}

func (c *CompareTask) collectLocalLBData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.LocalObj.LB = map[string]*entity.LoadBalancer{}
	dbPage := uint64(1)
	dbTotal := dbPageSize * 2
	customFilter := map[string]any{}
	if len(instanceIDs) != 0 {
		iInstanceID := []any{}
		for _, i := range instanceIDs {
			iInstanceID = append(iInstanceID, i)
		}
		customFilter["LoadBalancerID"] = bson.M{"$in": iInstanceID}
	}
	for (dbPage-1)*dbPageSize < dbTotal {
		resp, total, err := models.LoadBalancerModel.Query(ctx, &schema.LoadBalancerQueryParams{
			LoadBalancerColumnParam: schema.LoadBalancerColumnParam{
				RegionID: regionID,
				IspID:    c.IspID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbPageSize,
			},
			OrderParams: schema.OrderParams{},
		}, customFilter)
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			c.LocalObj.LB[item.LoadBalancerID] = item
		}
	}
	return nil
}

// CollectLocalData -
func (c *CompareTask) CollectLocalData(ctx context.Context) error {
	var err error
	if slices.Contains(c.ResourceType, "host") {
		err = c.collectLocalHostData(ctx, c.Env, c.RegionID, c.InstanceID...)
		if err != nil {
			return err
		}
	} else if slices.Contains(c.ResourceType, "mysql") {
		err = c.collectLocalMysqlData(ctx, c.Env, c.RegionID, c.InstanceID...)
		if err != nil {
			return err
		}
	} else if slices.Contains(c.ResourceType, "redis") {
		err = c.collectLocalRedisData(ctx, c.Env, c.RegionID, c.InstanceID...)
		if err != nil {
			return err
		}
	} else if slices.Contains(c.ResourceType, "lb") {
		return c.collectLocalLBData(ctx, c.Env, c.RegionID, c.InstanceID...)
	}
	return nil
}

// CollectCmdbData -
func (c *CompareTask) CollectCmdbData(ctx context.Context) error {
	var err error
	if slices.Contains(c.ResourceType, "host") {
		err = c.collectCmdbHostData(ctx, c.Env, c.RegionID, c.InstanceID...)
		if err != nil {
			return err
		}
	} else if slices.Contains(c.ResourceType, "mysql") {
		err = c.collectCmdbMysqlData(ctx, c.Env, c.RegionID, c.InstanceID...)
		if err != nil {
			return err
		}
	} else if slices.Contains(c.ResourceType, "redis") {
		err = c.collectCmdbRedisData(ctx, c.Env, c.RegionID, c.InstanceID...)
		if err != nil {
			return err
		}
	} else if slices.Contains(c.ResourceType, "lb") {
		return c.collectCmdbLBData(ctx, c.Env, c.RegionID, c.InstanceID...)
	}
	return nil
}
func (c *CompareTask) collectCmdbHostData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.CmdbObj.Host = map[string]*AliEcs{}
	dbPage := uint64(0)
	dbTotal := dbPageSize * 2
	customFilter := []string{}
	if len(instanceIDs) != 0 {
		iInstanceID := instanceIDs
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "InstanceId")
		condStr, _ = sjson.Set(condStr, "operator", "$in")
		condStr, _ = sjson.Set(condStr, "value", iInstanceID)
		customFilter = append(customFilter, condStr)
	}
	if regionID != "" {
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "RegionId")
		condStr, _ = sjson.Set(condStr, "operator", "$eq")
		condStr, _ = sjson.Set(condStr, "value", regionID)
		customFilter = append(customFilter, condStr)
	}
	for dbPage*dbPageSize < dbTotal {
		resp, result, err := ListBkCmdbObj(ctx, cmdbEnv, ALIEcs, customFilter, int32(dbPageSize), int32(dbPageSize*dbPage), "")
		if err != nil {
			return err
		}
		dbTotal = uint64(result.Data.Count)
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			obj := &AliEcs{}
			err := json.Unmarshal([]byte(item), obj)
			if err != nil {
				continue
			}
			c.CmdbObj.Host[obj.InstanceID] = obj
		}
	}
	return nil
}
func (c *CompareTask) collectCmdbMysqlData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.CmdbObj.Mysql = map[string]*AliPorlardb{}
	dbPage := uint64(0)
	dbTotal := dbPageSize * 2
	customFilter := []string{}
	if len(instanceIDs) != 0 {
		iInstanceID := instanceIDs
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "bk_inst_name")
		condStr, _ = sjson.Set(condStr, "operator", "$in")
		condStr, _ = sjson.Set(condStr, "value", iInstanceID)
		customFilter = append(customFilter, condStr)
	}
	if regionID != "" {
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "RegionID")
		condStr, _ = sjson.Set(condStr, "operator", "$eq")
		condStr, _ = sjson.Set(condStr, "value", regionID)
		customFilter = append(customFilter, condStr)
	}
	for dbPage*dbPageSize < dbTotal {
		resp, result, err := ListBkCmdbObj(ctx, cmdbEnv, ALIPolarDB, customFilter, int32(dbPageSize), int32(dbPageSize*dbPage), "")
		if err != nil {
			return err
		}
		dbTotal = uint64(result.Data.Count)
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			obj := &AliPorlardb{}
			err := json.Unmarshal([]byte(item), obj)
			if err != nil {
				continue
			}
			c.CmdbObj.Mysql[obj.BkInstName] = obj
		}
	}
	return nil
}
func (c *CompareTask) collectCmdbRedisData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.CmdbObj.Redis = map[string]*AliKvStore{}
	dbPage := uint64(0)
	dbTotal := dbPageSize * 2
	customFilter := []string{}
	if len(instanceIDs) != 0 {
		iInstanceID := instanceIDs
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "bk_inst_name")
		condStr, _ = sjson.Set(condStr, "operator", "$in")
		condStr, _ = sjson.Set(condStr, "value", iInstanceID)
		customFilter = append(customFilter, condStr)
	}
	if regionID != "" {
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "RegionID")
		condStr, _ = sjson.Set(condStr, "operator", "$eq")
		condStr, _ = sjson.Set(condStr, "value", regionID)
		customFilter = append(customFilter, condStr)
	}
	for dbPage*dbPageSize < dbTotal {
		resp, result, err := ListBkCmdbObj(ctx, cmdbEnv, ALIKVStore, customFilter, int32(dbPageSize), int32(dbPageSize*dbPage), "")
		if err != nil {
			return err
		}
		dbTotal = uint64(result.Data.Count)
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			obj := &AliKvStore{}
			err := json.Unmarshal([]byte(item), obj)
			if err != nil {
				continue
			}
			name, ok := (*obj)["bk_inst_name"].(string)
			if !ok {
				continue
			}
			c.CmdbObj.Redis[name] = obj
		}
	}
	return nil
}

func (c *CompareTask) collectCmdbLBData(ctx context.Context, cmdbEnv, regionID string, instanceIDs ...string) error {
	c.CmdbObj.LB = map[string]*AliLB{}
	dbPage := uint64(0)
	dbTotal := dbPageSize * 2
	customFilter := []string{}
	if len(instanceIDs) != 0 {
		iInstanceID := instanceIDs
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "bk_asset_id")
		condStr, _ = sjson.Set(condStr, "operator", "$in")
		condStr, _ = sjson.Set(condStr, "value", iInstanceID)
		customFilter = append(customFilter, condStr)
	}
	if regionID != "" {
		condStr := ""
		condStr, _ = sjson.Set(condStr, "field", "RegionID")
		condStr, _ = sjson.Set(condStr, "operator", "$eq")
		condStr, _ = sjson.Set(condStr, "value", regionID)
		customFilter = append(customFilter, condStr)
	}
	for dbPage*dbPageSize < dbTotal {
		resp, result, err := ListBkCmdbObj(ctx, cmdbEnv, ALILB, customFilter, int32(dbPageSize), int32(dbPageSize*dbPage), "")
		if err != nil {
			return err
		}
		dbTotal = uint64(result.Data.Count)
		dbPage++
		if len(resp) == 0 {
			continue
		}
		for _, item := range resp {
			obj := &AliLB{}
			err := json.Unmarshal([]byte(item), obj)
			if err != nil {
				continue
			}
			c.CmdbObj.LB[obj.BkAssetID] = obj
		}
	}
	return nil
}

// DoCompare -
func (c *CompareTask) DoCompare(ctx context.Context, pr *common.InitProvider) error {
	var err error
	// 获取数据，存入内存
	err = c.CollectLocalData(ctx)
	if err != nil {
		pr.Logger.Errorf("DoCompare.CollectCmdbData.error: %s", err.Error())
		return err
	}
	pr.Logger.Infof("DoCompare.CollectCmdbData.done, length:%d,%d,%d,%d", len(c.LocalObj.Host), len(c.LocalObj.Mysql), len(c.LocalObj.Redis), len(c.LocalObj.Database))
	err = c.CollectCmdbData(ctx)
	if err != nil {
		pr.Logger.Errorf("DoCompare.CollectCmdbData.error: %s", err.Error())
		return err
	}
	pr.Logger.Infof("DoCompare.CollectCmdbData.done, length:%d,%d,%d,%d", len(c.CmdbObj.Host), len(c.CmdbObj.Mysql), len(c.CmdbObj.Redis), len(c.CmdbObj.Database))
	// 判断存在性；兼判断数据一致性
	if slices.Contains(c.ResourceType, "host") {
		c.Result.Host = compare(ctx, c.LocalObj.Host, c.CmdbObj.Host)
	} else if slices.Contains(c.ResourceType, "mysql") {
		c.Result.Mysql = compare(ctx, c.LocalObj.Mysql, c.CmdbObj.Mysql)
	} else if slices.Contains(c.ResourceType, "redis") {
		c.Result.Redis = compare(ctx, c.LocalObj.Redis, c.CmdbObj.Redis)
	} else if slices.Contains(c.ResourceType, "lb") {
		c.Result.LB = compare(ctx, c.LocalObj.LB, c.CmdbObj.LB)
	}

	// 对cmdb待删除数据，判断assoc关系
	// 结果输出和写入
	resultBin, _ := json.Marshal(c.Result)
	_ = models.ResourceOrder.UpdateStatusDetail(ctx, c.OrderID, string(resultBin))
	pr.Logger.Infof("DoCompare.done, result: %s", string(resultBin))
	return nil
}

// left: local存在cmdb需添加；right：local不存在cmdb需删除；middle：两者不一致需更新cmdb
func compare[T cmdbObjT, S localObjT](ctx context.Context, localSet map[string]*S, cmdbSet map[string]*T) CompareAction {
	act := CompareAction{}
	act.Add = map[string]InstanceDetail{}
	act.Remove = map[string]InstanceDetail{}
	act.Update = map[string]InstanceDetail{}

	for id, objLocal := range localSet {
		objCmdb := cmdbSet[id]
		if objCmdb == nil {
			act.Add[id] = InstanceDetail{ID: id, Name: getObjName(objCmdb, objLocal)}
			continue
		}
		cmdbObjMap := map[string]any{}
		switch objLocalT := any(objLocal).(type) {
		case *entity.HostResource:
			cObj := convertHostEntity(*objLocalT)
			cmdbObjMap, _ = mapstruct.Struct2Map(cObj)
		case *entity.MysqlClusterResource:
			cObj := convertMysqlEntity(*objLocalT)
			cmdbObjMap, _ = mapstruct.Struct2Map(cObj)
		case *entity.RedisResource:
			cObj := convertRedisEntity(*objLocalT)
			cmdbObjMap, _ = mapstruct.Struct2Map(cObj)
		case *entity.LoadBalancer:
			cObj := convertLBEntity(*objLocalT)
			cmdbObjMap, _ = mapstruct.Struct2Map(cObj)
		}
		localObjMap, _ := mapstruct.Struct2Map(objCmdb)
		if same, reason := isMapSame(localObjMap, cmdbObjMap); !same {
			act.Update[id] = InstanceDetail{ID: id, Name: fmt.Sprint(localObjMap["InstanceName"]), Reason: reason}
		}
	}
	for id := range cmdbSet {
		objCmdb := cmdbSet[id]
		if localSet[id] == nil {
			act.Remove[id] = InstanceDetail{ID: id, Name: getObjName(objCmdb, localSet[id])}
			continue
		}
	}
	return act
}

func isMapSame(l, c map[string]any) (bool, []string) {
	reason := []string{}
	for k := range l {
		if c[k] == nil {
			continue
		}
		// case-insensitively compare
		if !strings.EqualFold(fmt.Sprint(l[k]), fmt.Sprint(c[k])) {
			reason = append(reason, fmt.Sprintf("field: %s; cloudman: %s, cmdb: %s", k, fmt.Sprint(l[k]), fmt.Sprint(c[k])))
		}
	}
	if len(reason) > 0 {
		return false, reason
	}
	return true, nil
}

func getObjName[T cmdbObjT, S localObjT](objCmdb *T, objLocal *S) string {
	if objLocal != nil {
		switch objLocalT := any(objLocal).(type) {
		case *entity.HostResource:
			return objLocalT.InstanceName
		case *entity.MysqlClusterResource:
			return objLocalT.DBClusterDescription
		case *entity.RedisResource:
			return objLocalT.InstanceName
		}
	}
	if objCmdb != nil {
		switch objCmdbT := any(objCmdb).(type) {
		case *AliEcs:
			return objCmdbT.InstanceName
		case *AliPorlardb:
			return objCmdbT.Dbclusterdescription
		case *AliKvStore:
			name, ok := (*objCmdbT)["InstanceName"].(string)
			if ok {
				return name
			}
		}
	}
	return ""
}
