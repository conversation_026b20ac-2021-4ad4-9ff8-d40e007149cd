package cmdb

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

var (
	ErrHostNotInBiz = errors.New("host is not in biz")
)

// CreateCmdbHosts 新增CMDB主机
func CreateCmdbHosts(logger *logrus.Logger, data []entity.HostResource) (*BatchResult, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return nil, fmt.Errorf("AddCmdbHostsNap.url_host.empty")
	}

	hostInfo := map[string]*BkHost{}
	for index, e := range data {
		bkHost := conevertEntityToHost(&e)
		// 创建时需要去掉BkHostID
		bkHost.BKHostID = nil
		hostInfo[fmt.Sprintf("%d", index)] = bkHost
	}
	payload := map[string]interface{}{
		"host_info":      hostInfo,
		"bk_supplier_id": 0, // 默认值
		"bk_biz_id":      1, // 资源池
	}
	d, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("CreateCmdbHosts.req.Marshal.error, err: %s", err.Error())
	}
	url := fmt.Sprintf("%s/api/v3/hosts/add", hostBase)
	logger.Infof("CreateCmdbHosts.http.req.url: %s", url)
	logger.Infof("CreateCmdbHosts.http.req.dump: %s", string(d))
	respBin, respCode, err := utils.HTTPPost(context.Background(), url, CMDBHeader(), d, 20*time.Second)
	if err != nil {
		return nil, fmt.Errorf("CreateCmdbHosts.http.post.error, err: %s", err.Error())
	}
	logger.Infof("CreateCmdbHosts.http.resp.dump: %s", string(respBin))
	if respCode != 200 {
		return nil, fmt.Errorf("CreateCmdbHosts.http.error, respCode: %d", respCode)
	}
	respObj := CommonResponse{}
	err = json.Unmarshal(respBin, &respObj)
	if err != nil {
		return nil, fmt.Errorf("CreateCmdbHosts.resp.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BKErrorCode != 0 {
		return nil, fmt.Errorf("CreateCmdbHosts.resp.error, BKErrorCode: %d", respObj.BKErrorCode)
	}
	var batchResult BatchResult
	err = json.Unmarshal(respObj.Data, &batchResult)
	if err != nil {
		return nil, fmt.Errorf("CreateCmdbHosts.data.Unmarshal.error, err: %s", err.Error())
	}

	return &batchResult, nil
}

func UpdateCmdbHosts(logger *logrus.Logger, data []entity.HostResource) (*BatchResult, error) {
	logger = utils.CmdbLogger(logger)
	cmdbURL := cfg.GetCmdbConfig().BkCmdbURL
	if cmdbURL == "" {
		return nil, fmt.Errorf("UpdateCmdbHosts.url_host.empty")
	}

	hostInfo := map[string]*BkHost{}
	for index, e := range data {
		hostInfo[fmt.Sprintf("%d", index)] = conevertEntityToHost(&e)
	}
	payload := map[string]interface{}{
		"host_info": hostInfo,
	}
	d, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("UpdateCmdbHosts.req.Marshal.error, err: %s", err.Error())
	}
	url := fmt.Sprintf("%s/api/v3/hosts/update", cmdbURL)
	logger.Infof("UpdateCmdbHosts.http.req.url: %s", url)
	logger.Infof("UpdateCmdbHosts.http.req.dump: %s", string(d))
	respBin, respCode, err := utils.HTTPPut(context.Background(), url, CMDBHeader(), d, 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("UpdateCmdbHosts.http.post.error, err: %s", err.Error())
	}
	logger.Infof("UpdateCmdbHosts.http.resp.dump: %s", string(respBin))
	if respCode != 200 {
		return nil, fmt.Errorf("UpdateCmdbHosts.http.error, respCode: %d", respCode)
	}
	respObj := CommonResponse{}
	err = json.Unmarshal(respBin, &respObj)
	if err != nil {
		return nil, fmt.Errorf("UpdateCmdbHosts.resp.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BKErrorCode != 0 {
		return nil, fmt.Errorf("UpdateCmdbHosts.resp.error, BKErrorCode: %d", respObj.BKErrorCode)
	}
	var batchResult BatchResult
	err = json.Unmarshal(respObj.Data, &batchResult)
	if err != nil {
		return nil, fmt.Errorf("UpdateCmdbHosts.data.Unmarshal.error, err: %s", err.Error())
	}

	return &batchResult, nil
}

// PushBkCmdbHost -
func PushBkCmdbHost(logger *logrus.Logger, list []entity.HostResource) ([]*BatchResult, error) {
	sendObjTest := []AliEcs{}
	sendObjProd := []AliEcs{}
	for _, ori := range list {
		if utils.ResourceIsProd(ori.InstanceName) {
			sendObjProd = append(sendObjProd, convertHostEntity(ori))
		}
		if utils.ResourceIsTest(ori.InstanceName) {
			sendObjTest = append(sendObjTest, convertHostEntity(ori))
		}
	}
	var r1, r2 *BatchResult
	var err1, err2 error
	sendInBatch := 20
	sendObjTestBatch := []AliEcs{}
	sendObjProdBatch := []AliEcs{}
	for i, obj := range sendObjTest {
		sendObjTestBatch = append(sendObjTestBatch, obj)
		if i == len(sendObjTest)-1 || len(sendObjTestBatch) >= sendInBatch {
			bodyBinTest, _ := json.Marshal(sendObjTestBatch)
			r1, err1 = PushBkCmdb(logger, ALIEcs, false, bodyBinTest)
			if err1 != nil {
				logger.Warnf("PushBkCmdbHost.test.failed: %s", err1.Error())
			} else {
				logger.Infof("PushBkCmdbHost.test.done")
			}
			sendObjTestBatch = []AliEcs{}
		}
	}
	for i, obj := range sendObjProd {
		sendObjProdBatch = append(sendObjProdBatch, obj)
		if i == len(sendObjProd)-1 || len(sendObjProdBatch) >= sendInBatch {
			bodyBinProd, _ := json.Marshal(sendObjProdBatch)
			r2, err2 = PushBkCmdb(logger, ALIEcs, true, bodyBinProd)
			if err2 != nil {
				logger.Warnf("PushBkCmdbHost.prod.failed: %s", err2.Error())
			} else {
				logger.Infof("PushBkCmdbHost.prod.done")
			}
			sendObjProdBatch = []AliEcs{}
		}
	}
	if err1 == nil && err2 == nil {
		return []*BatchResult{r1, r2}, nil
	}
	return []*BatchResult{r1, r2}, fmt.Errorf("test: %v ,prod: %v", err1, err2)
}

// PushBkCmdbMysql -
func PushBkCmdbMysql(logger *logrus.Logger, list []entity.MysqlClusterResource) ([]*BatchResult, error) {
	sendObjTest := []AliPorlardb{}
	sendObjProd := []AliPorlardb{}
	for _, ori := range list {
		sendObjProd = append(sendObjProd, convertMysqlEntity(ori))
		sendObjTest = append(sendObjTest, convertMysqlEntity(ori))
	}
	var r1, r2 *BatchResult
	var err1, err2 error
	if len(sendObjTest) > 0 {
		bodyBinTest, _ := json.Marshal(sendObjTest)
		r1, err1 = PushBkCmdb(logger, ALIPolarDB, false, bodyBinTest)
		if err1 != nil {
			logger.Warnf("PushBkCmdbMysql.test.failed: %s", err1.Error())
		} else {
			logger.Infof("PushBkCmdbMysql.test.done")
		}
	}
	if len(sendObjProd) > 0 {
		bodyBinProd, _ := json.Marshal(sendObjProd)
		r2, err2 = PushBkCmdb(logger, ALIPolarDB, true, bodyBinProd)
		if err2 != nil {
			logger.Warnf("PushBkCmdbMysql.prod.failed: %s", err2.Error())
		} else {
			logger.Infof("PushBkCmdbMysql.prod.done")
		}
	}
	if err1 == nil && err2 == nil {
		return []*BatchResult{r1, r2}, nil
	}
	return []*BatchResult{r1, r2}, fmt.Errorf("test: %v ,prod: %v", err1, err2)
}

// PushBkCmdbMysqlDatabase -
func PushBkCmdbMysqlDatabase(logger *logrus.Logger, list []entity.MysqlDatabaseResource) ([]*BatchResult, error) {
	sendObjTest := []AliDatabase{}
	sendObjProd := []AliDatabase{}
	for _, ori := range list {
		sendObjProd = append(sendObjProd, convertDatabaseEntity(ori))
		sendObjTest = append(sendObjTest, convertDatabaseEntity(ori))
	}
	var r1, r2 *BatchResult
	var err1, err2 error
	if len(sendObjTest) > 0 {
		bodyBinTest, _ := json.Marshal(sendObjTest)
		r1, err1 = PushBkCmdb(logger, ALIDatabase, false, bodyBinTest)
		if err1 != nil {
			logger.Warnf("PushBkCmdbMysqlDatabase.test.failed: %s", err1.Error())
		} else {
			logger.Infof("PushBkCmdbMysqlDatabase.test.done")
		}
	}
	if len(sendObjProd) > 0 {
		bodyBinProd, _ := json.Marshal(sendObjProd)
		r2, err2 = PushBkCmdb(logger, ALIDatabase, true, bodyBinProd)
		if err2 != nil {
			logger.Warnf("PushBkCmdbMysqlDatabase.prod.failed: %s", err2.Error())
		} else {
			logger.Infof("PushBkCmdbMysqlDatabase.prod.done")
		}
	}
	if err1 == nil {
		for _, ori := range list {
			idA, errA := PushBkCmdbAssoc(logger, false, ALIDatabase, ori.ClusterID+"_"+ori.DBName, ALIPolarDB, ori.ClusterID, "polardb_database_belong_ali_polardb")
			idB, errB := PushBkCmdbAssoc(logger, false, ALIPolarDB, ori.ClusterID, ALIDatabase, ori.ClusterID+"_"+ori.DBName, "ali_polardb_belong_polardb_database")
			logger.Infof("PushBkCmdbMysqlDatabase.test.Assoc, idA: %v, errA: %v, idB: %v, errB: %v", idA, errA, idB, errB)
		}
	}
	if err2 == nil {
		for _, ori := range list {
			idA, errA := PushBkCmdbAssoc(logger, true, ALIDatabase, ori.ClusterID+"_"+ori.DBName, ALIPolarDB, ori.ClusterID, "polardb_database_belong_ali_polardb")
			idB, errB := PushBkCmdbAssoc(logger, true, ALIPolarDB, ori.ClusterID, ALIDatabase, ori.ClusterID+"_"+ori.DBName, "ali_polardb_belong_polardb_database")
			logger.Infof("PushBkCmdbMysqlDatabase.prod.Assoc, idA: %v, errA: %v, idB: %v, errB: %v", idA, errA, idB, errB)
		}
	}
	if err1 != nil || err2 != nil {
		return []*BatchResult{r1, r2}, fmt.Errorf("test: %v ,prod: %v", err1, err2)
	}
	return []*BatchResult{r1, r2}, nil
}

// PushBkCmdbRedis -
func PushBkCmdbRedis(logger *logrus.Logger, list []entity.RedisResource) ([]*BatchResult, error) {
	sendObjTest := []AliKvStore{}
	sendObjProd := []AliKvStore{}
	for _, ori := range list {
		sendObjProd = append(sendObjProd, convertRedisEntity(ori))
		sendObjTest = append(sendObjTest, convertRedisEntity(ori))
	}
	var r1, r2 *BatchResult
	var err1, err2 error
	if len(sendObjTest) > 0 {
		bodyBinTest, _ := json.Marshal(sendObjTest)
		r1, err1 = PushBkCmdb(logger, ALIKVStore, false, bodyBinTest)
		if err1 != nil {
			logger.Warnf("PushBkCmdbRedis.test.failed: %s", err1.Error())
		} else {
			logger.Infof("PushBkCmdbRedis.test.done")
		}
	}
	if len(sendObjProd) > 0 {
		bodyBinProd, _ := json.Marshal(sendObjProd)
		r2, err2 = PushBkCmdb(logger, ALIKVStore, true, bodyBinProd)
		if err2 != nil {
			logger.Warnf("PushBkCmdbRedis.prod.failed: %s", err2.Error())
		} else {
			logger.Infof("PushBkCmdbRedis.prod.done")
		}
	}
	if err1 == nil && err2 == nil {
		return []*BatchResult{r1, r2}, nil
	}
	return []*BatchResult{r1, r2}, fmt.Errorf("test: %v ,prod: %v", err1, err2)
}

// PushBkCmdb -
func PushBkCmdb(logger *logrus.Logger, instType string, isProd bool, data []byte) (*BatchResult, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbTestURL
	if isProd {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	}
	if hostBase == "" {
		return nil, fmt.Errorf("PushBkCmdb.url_host.empty, isProd: %v", isProd)

	}
	url := fmt.Sprintf("%s/insts/owner/0/object/%s/importByRaw", hostBase, instType)
	logger.Infof("PushBkCmdb.http.req.url: %s", url)
	logger.Infof("PushBkCmdb.http.req.dump: %s", string(data))
	respBin, respCode, err := utils.HTTPPost(context.Background(), url, JwtHeader(isProd), data, 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("PushBkCmdb.http.post.error, isProd: %v", isProd)
	}
	logger.Infof("PushBkCmdb.http.resp.dump: %s", string(respBin))
	if respCode != 200 {
		return nil, fmt.Errorf("PushBkCmdb.http.error, respCode: %d", respCode)
	}
	respObj := CommonResponse{}
	err = json.Unmarshal(respBin, &respObj)
	if err != nil {
		return nil, fmt.Errorf("PushBkCmdb.resp.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BKErrorCode != 0 {
		return nil, fmt.Errorf("PushBkCmdb.resp.error, BKErrorCode: %d", respObj.BKErrorCode)
	}
	var batchResult BatchResult
	err = json.Unmarshal(respObj.Data, &batchResult)
	if err != nil {
		return nil, fmt.Errorf("PushBkCmdb.data.Unmarshal.error, err: %s", err.Error())
	}

	return &batchResult, nil
}

// DeleteBkCmdbNoEnv -
func DeleteBkCmdbNoEnv(logger *logrus.Logger, instType string, data []string) ([]bool, error) {
	if instType == "host" {
		r, err := DeleteBkCmdbHost(logger, data)
		return []bool{r, r}, err
	}

	r1, err1 := DeleteBkCmdb(logger, instType, false, data)
	if err1 != nil {
		logger.Warnf("DeleteBkCmdbNoEnv.test.failed: %s", err1.Error())
	} else {
		logger.Infof("DeleteBkCmdbNoEnv.test.done")
	}
	r2, err2 := DeleteBkCmdb(logger, instType, true, data)
	if err2 != nil {
		logger.Warnf("DeleteBkCmdbNoEnv.prod.failed: %s", err2.Error())
	} else {
		logger.Infof("DeleteBkCmdbNoEnv.prod.done")
	}
	return []bool{r1, r2}, nil
}

func DeleteBkCmdbHost(logger *logrus.Logger, instanceIDs []string) (bool, error) {
	data, err := FindBkHostByInstanceIDs(logger, instanceIDs, []string{"biz"})
	if err != nil {
		return false, nil
	}
	const BkIdleHostResourceBizID = 1

	bkHostIDs := make([]string, 0)
	for _, info := range data.Info {
		bkHostID := tea.Int32Value(info.HostInfo.BKHostID)
		bkHostIDs = append(bkHostIDs, fmt.Sprintf("%d", bkHostID))
		if len(info.BizInfo) == 0 {
			continue
		}
		// 由于目前机器只属于一个项目，所以取第一个即可
		bizID := info.BizInfo[0].BkBizID
		if bizID == BkIdleHostResourceBizID {
			continue
		}
		err = MoveBizToBizIDIdle(logger, bkHostID, bizID)
		if err != nil {
			logger.Warnf("移动到业务空闲池失败, err: %v", err)
			continue
		}
		logger.Infof("移动机器到业务空闲池成功, ip: %s", info.HostInfo.BkHostname)
		err = MoveHostToIdleResource(logger, bkHostID, bizID)
		if err != nil {
			logger.Warnf("移动到主机池失败, err: %v", err)
			continue
		}
		logger.Infof("移动机器到主机池成功, ip: %s", info.HostInfo.BkHostname)
	}

	// 开始删除
	baseURL := cfg.GetCmdbConfig().BkCmdbURL
	deleteURL := fmt.Sprintf("%s/api/v3/hosts/batch", baseURL)
	deleteObj := bkCmdbHostDeleteRequest{
		BkHostIDs: strings.Join(bkHostIDs, ","),
	}
	deleteBodyBin, _ := json.Marshal(deleteObj)
	deleteRespBin, deleteRespCode, err := utils.HTTPDelete(context.Background(), deleteURL, CMDBHeader(), deleteBodyBin, 5*time.Second)
	if err != nil {
		return false, fmt.Errorf("DeleteBkCmdbHost.del.error, err: %s", err.Error())
	}
	logger.Infof("DeleteBkCmdbHost.del.dump: %s", string(deleteRespBin))
	if deleteRespCode != 200 {
		return false, fmt.Errorf("DeleteBkCmdbHost.del.error, respCode: %d", deleteRespCode)
	}
	respObj := CommonResponse{}
	err = json.Unmarshal(deleteRespBin, &respObj)
	if err != nil {
		return false, fmt.Errorf("DeleteBkCmdbHost.del.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BKErrorCode != 0 {
		return false, fmt.Errorf("DeleteBkCmdbHost.del.error, BKErrorCode: %d", respObj.BKErrorCode)
	}

	return respObj.Result, nil
}

// DeleteBkCmdb -
func DeleteBkCmdb(logger *logrus.Logger, instType string, isProd bool, instanceIDs []string) (bool, error) {
	logger = utils.CmdbLogger(logger)
	var err error
	// 开始根据instance_id查询cmdb_id
	baseURL := cfg.GetCmdbConfig().BkCmdbTestURL
	if isProd {
		baseURL = cfg.GetCmdbConfig().BkCmdbURL
	}
	ids, err := FindBkIDByBkName(logger, isProd, instType, instanceIDs)
	if err != nil {
		return false, fmt.Errorf("FindBkIDByBkName.error: %s", err.Error())
	}
	if len(ids) != len(instanceIDs) {
		logger.Warnf("DeleteBkCmdb.del.count_not_match, input: %d, found: %d", len(instanceIDs), len(ids))
	}
	logger.Infof("DeleteBkCmdb.del.ready, list: %v, isProd: %v", ids, isProd)
	// 开始删除
	deleteURL := fmt.Sprintf("%s/api/v3/%s/instance/object/%s", baseURL, "deletemany", instType)
	deleteObj := bkCmdbDeleteRequest{}
	for _, id := range ids {
		deleteObj.Delete.InstIDs = append(deleteObj.Delete.InstIDs, id)
	}
	deleteBodyBin, _ := json.Marshal(deleteObj)
	deleteRespBin, deleteRespCode, err := utils.HTTPDelete(context.Background(), deleteURL, JwtHeader(isProd), deleteBodyBin, 5*time.Second)
	if err != nil {
		return false, fmt.Errorf("DeleteBkCmdb.del.error, err: %s", err.Error())
	}
	logger.Infof("DeleteBkCmdb.del.dump: %s", string(deleteRespBin))
	if deleteRespCode != 200 {
		return false, fmt.Errorf("DeleteBkCmdb.del.error, respCode: %d", deleteRespCode)
	}
	respObj := CommonResponse{}
	err = json.Unmarshal(deleteRespBin, &respObj)
	if err != nil {
		return false, fmt.Errorf("DeleteBkCmdb.del.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BKErrorCode != 0 {
		return false, fmt.Errorf("DeleteBkCmdb.del.error, BKErrorCode: %d", respObj.BKErrorCode)
	}

	return respObj.Result, nil
}

// PushBkCmdbAssoc -
func PushBkCmdbAssoc(logger *logrus.Logger, isProd bool, instType string, instName string, targetType string, targetName string, relation string) (int, error) {
	logger = utils.CmdbLogger(logger)
	instIDs, err := FindBkIDByBkName(logger, isProd, instType, []string{instName})
	if err != nil {
		return -1, err
	}
	if len(instIDs) == 0 {
		return -1, fmt.Errorf("cannot find instName")
	}
	targetIDs, err := FindBkIDByBkName(logger, isProd, targetType, []string{targetName})
	if err != nil {
		return -1, err
	}
	if len(targetIDs) == 0 {
		return -1, fmt.Errorf("cannot find targetName")
	}
	instID := instIDs[instName]
	targetID := targetIDs[targetName]
	hostBase := cfg.GetCmdbConfig().BkCmdbTestURL
	if isProd {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	}
	if hostBase == "" {
		return -1, fmt.Errorf("PushBkCmdbAssoc.url_host.empty, isProd: %v", isProd)
	}
	queryResult, err := FindAssocByBkID(logger, isProd, AssocQueryReq{
		BaseObjType: instType,
		InstType:    instType,
		InstID:      instID,
		TargetType:  targetType,
		TargetID:    targetID,
		Relation:    relation,
	})
	if err != nil {
		return -1, err
	}
	if len(queryResult) > 0 {
		assocID := int(gjson.GetBytes(queryResult[0], "id").Int())
		return assocID, nil
	}

	createURL := fmt.Sprintf("%s/api/v3/create/instassociation", hostBase)
	createData := ""
	createData, _ = sjson.Set(createData, "bk_obj_asst_id", relation)
	createData, _ = sjson.Set(createData, "bk_inst_id", instID)
	createData, _ = sjson.Set(createData, "bk_asst_inst_id", targetID)
	logger.Infof("PushBkCmdbAssoc.create.req.dump: %s", createData)
	createRespBin, createRespCode, err := utils.HTTPPost(context.Background(), createURL, JwtHeader(isProd), []byte(createData), 5*time.Second)
	logger.Infof("PushBkCmdbAssoc.create.resp.dump: %s", string(createRespBin))
	if createRespCode != 200 {
		return -1, fmt.Errorf("PushBkCmdbAssoc.create.error, respCode: %d", createRespCode)
	}
	createRespObj := CommonResponse{}
	err = json.Unmarshal(createRespBin, &createRespObj)
	if err != nil {
		return -1, fmt.Errorf("PushBkCmdbAssoc.resp.Unmarshal.error, err: %s", err.Error())
	}
	if createRespObj.BKErrorCode != 0 {
		return -1, fmt.Errorf("PushBkCmdbAssoc.resp.error, BKErrorCode: %d", createRespObj.BKErrorCode)
	}
	assocID := int(gjson.GetBytes(createRespObj.Data, "id").Int())
	return assocID, nil
}

// FindAssocByBkID -
func FindAssocByBkID(logger *logrus.Logger, isProd bool, req AssocQueryReq) ([]json.RawMessage, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbTestURL
	if isProd {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	}
	if hostBase == "" {
		return nil, fmt.Errorf("FindAssocByBkID.url_host.empty, isProd: %v", isProd)
	}
	queryURL := fmt.Sprintf("%s/api/v3/find/instassociation", hostBase)
	queryData := ""
	queryData, _ = sjson.Set(queryData, "bk_obj_id", req.BaseObjType)
	if req.Relation != "" {
		queryData, _ = sjson.Set(queryData, "condition.bk_obj_asst_id", req.Relation)
	}
	if req.TargetType != "" {
		queryData, _ = sjson.Set(queryData, "condition.bk_asst_obj_id", req.TargetType)
	}
	if req.TargetID != 0 {
		queryData, _ = sjson.Set(queryData, "condition.bk_asst_inst_id", req.TargetID)
	}
	if req.InstType != "" {
		queryData, _ = sjson.Set(queryData, "condition.bk_obj_id", req.InstType)
	}
	if req.InstID != 0 {
		queryData, _ = sjson.Set(queryData, "condition.bk_inst_id", req.InstID)
	}
	logger.Infof("FindAssocByBkID.query.req.dump: %s", queryData)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), queryURL, JwtHeader(isProd), []byte(queryData), 5*time.Second)
	logger.Infof("FindAssocByBkID.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return nil, fmt.Errorf("FindAssocByBkID.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return nil, fmt.Errorf("FindAssocByBkID.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		return nil, fmt.Errorf("FindAssocByBkID.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}
	var queryResult []json.RawMessage
	err = json.Unmarshal(queryRespObj.Data, &queryResult)
	if err != nil {
		return nil, fmt.Errorf("FindAssocByBkID.data.Unmarshal.error, err: %s", err.Error())
	}
	return queryResult, nil
}

// FindBkIDByBkName -
func FindBkIDByBkName(logger *logrus.Logger, isProd bool, instType string, instNames []string) (map[string]int, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbTestURL
	if isProd {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	}
	if hostBase == "" {
		return nil, fmt.Errorf("FindBkIDByBkName.url_host.empty, isProd: %v", isProd)
	}
	queryURL := fmt.Sprintf("%s/api/v3/find/instassociation/object/%s", hostBase, instType)
	queryBody := ""
	queryBody, _ = sjson.Set(queryBody, fmt.Sprintf("condition.%s.0.field", instType), keyQueryMap[instType])
	queryBody, _ = sjson.Set(queryBody, fmt.Sprintf("condition.%s.0.operator", instType), "$in")
	queryBody, _ = sjson.Set(queryBody, fmt.Sprintf("condition.%s.0.value", instType), instNames)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), queryURL, JwtHeader(isProd), []byte(queryBody), 5*time.Second)
	logger.Infof("FindBkIDByBkName.query.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return nil, fmt.Errorf("FindBkIDByBkName.query.error, respCode: %d, error: %v", queryRespCode, err)
	}
	queryRespObj := bkCmdbQueryResult{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if queryRespObj.BkErrorCode != 0 {
		return nil, fmt.Errorf("FindBkIDByBkName.query.error, bk_error_code: %d", queryRespObj.BkErrorCode)
	}
	result := map[string]int{}
	if queryRespObj.Data.Count == 0 {
		logger.Warnf("FindBkIDByBkName.query.empty")
		return result, nil
	}
	if queryRespObj.Data.Count != len(instNames) {
		logger.Warnf("FindBkIDByBkName.query.result_count_mismatch, need: %d, got:%d", len(instNames), queryRespObj.Data.Count)
	}
	for _, i := range queryRespObj.Data.Info {
		name := gjson.GetBytes(i, keyQueryMap[instType]).String()
		id := gjson.GetBytes(i, "bk_inst_id").Int()
		result[name] = int(id)
	}
	return result, nil
}

// FindBkNameByBkID -
func FindBkNameByBkID(logger *logrus.Logger, isProd bool, instType string, instIDs []int) (map[int]string, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbTestURL
	if isProd {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	}
	if hostBase == "" {
		return nil, fmt.Errorf("FindBkNameByBkID.url_host.empty, isProd: %v", isProd)
	}
	queryURL := fmt.Sprintf("%s/api/v3/find/instassociation/object/%s", hostBase, instType)
	queryBody := ""
	queryBody, _ = sjson.Set(queryBody, fmt.Sprintf("condition.%s.0.field", instType), "bk_inst_id")
	queryBody, _ = sjson.Set(queryBody, fmt.Sprintf("condition.%s.0.operator", instType), "$in")
	queryBody, _ = sjson.Set(queryBody, fmt.Sprintf("condition.%s.0.value", instType), instIDs)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), queryURL, JwtHeader(isProd), []byte(queryBody), 5*time.Second)
	logger.Infof("FindBkNameByBkID.query.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return nil, fmt.Errorf("FindBkNameByBkID.query.error, respCode: %d, error: %v", queryRespCode, err)
	}
	queryRespObj := bkCmdbQueryResult{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if queryRespObj.BkErrorCode != 0 {
		return nil, fmt.Errorf("FindBkNameByBkID.query.error, bk_error_code: %d", queryRespObj.BkErrorCode)
	}
	if queryRespObj.Data.Count == 0 {
		return nil, fmt.Errorf("FindBkNameByBkID.query.empty")
	}
	if queryRespObj.Data.Count != len(instIDs) {
		return nil, fmt.Errorf("FindBkNameByBkID.query.result_count_mismatch, need: %d, got:%d", len(instIDs), queryRespObj.Data.Count)
	}
	result := map[int]string{}
	for _, i := range queryRespObj.Data.Info {
		name := gjson.GetBytes(i, keyQueryMap[instType]).String()
		id := gjson.GetBytes(i, "bk_inst_id").Int()
		result[int(id)] = name
	}
	return result, nil
}

var keyQueryMap = map[string]string{
	"ali_ecs":          "InstanceId",
	"ali_polardb":      "bk_inst_name",
	"ali_kvStore":      "bk_inst_name",
	"polardb_database": "bk_inst_name",
	"game_region":      "bk_inst_name",
	"yuanshen_process": "bk_inst_name",
}

// JwtHeader -
func JwtHeader(isProd bool) http.Header {
	token := ""
	if cfg.GetCmdbConfig().BkCmdbToken != "" {
		token = cfg.GetCmdbConfig().BkCmdbToken
	}
	h := http.Header{
		"Content-Type": []string{"application/json"},
	}
	if token != "" {
		h.Add("Authorization", token)
	}
	return h
}

func CMDBHeader() http.Header {
	token := ""
	if cfg.GetCmdbConfig().BkCmdbToken != "" {
		token = cfg.GetCmdbConfig().BkCmdbToken
	}
	h := http.Header{
		"Content-Type": []string{"application/json"},
	}
	if token != "" {
		h.Add("Authorization", token)
	}

	return h
}

func JSONHeader() http.Header {
	h := http.Header{
		"Content-Type": []string{"application/json"},
	}
	return h
}

// FindBkHostByInstanceIDs -
func FindBkHostByInstanceIDs(logger *logrus.Logger, instanceIDs []string, embed []string) (*HostSearchData, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return nil, fmt.Errorf("FindBkHostByInstanceIDs.url_host.empty")
	}
	queryURL := fmt.Sprintf("%s/api/v3/hosts/search", hostBase)
	maxPage := 999999 // tmp magic number

	hostCondition := SearchCondition{
		Fields: []string{
			"bk_host_id",
			"bk_host_innerip",
			"bk_host_name",
			"__bk_host_topology__",
			"bk_cloud_inst_id",
			"bk_state",
			"InstanceName",
			"bk_cloud_host_status",
		},
		Condition: []ConditionItem{},
		ObjectID:  "host",
	}
	if len(instanceIDs) > 0 {
		hostCondition.Condition = []ConditionItem{
			{
				Field:    "bk_cloud_inst_id",
				Operator: "$in",
				Value:    instanceIDs,
			},
		}
	}

	searchReq := &HostCommonSearch{
		AppID:  0,
		Ipv4Ip: IPInfo{},
		Ipv6Ip: IPInfo{},
		Condition: []SearchCondition{
			hostCondition,
		},
		Page: BasePage{
			Sort:  "bk_host_id",
			Limit: maxPage,
			Start: 0,
		},
	}

	for _, e := range embed {
		searchReq.Condition = append(searchReq.Condition, SearchCondition{
			Fields:    []string{},
			Condition: []ConditionItem{},
			ObjectID:  e,
		})
	}

	searchData, err := json.Marshal(searchReq)
	if err != nil {
		return nil, fmt.Errorf("FindBkHostByInstanceIDs.Marshal.error, err: %s", err.Error())
	}

	logger.Debugf("FindBkHostByInstanceIDs.query.req.dump: %s", searchData)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), queryURL, CMDBHeader(), []byte(searchData), 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("FindBkHostByInstanceIDs.query.req.error, err: %s", err.Error())
	}
	logger.Debugf("FindBkHostByInstanceIDs.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return nil, fmt.Errorf("FindBkHostByInstanceIDs.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return nil, fmt.Errorf("FindBkHostByInstanceIDs.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		return nil, fmt.Errorf("FindBkHostByInstanceIDs.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}
	queryResult := HostSearchData{}
	err = json.Unmarshal(queryRespObj.Data, &queryResult)
	if err != nil {
		return nil, fmt.Errorf("FindBkHostByInstanceIDs.data.Unmarshal.error, err: %v", err)
	}
	return &queryResult, nil
}

// FindCMDBTopoInstByBizID -
func FindCMDBTopoInstByBizID(logger *logrus.Logger, bizID int32) (*TopoData, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return nil, fmt.Errorf("FindCMDBTopoInstByBizID.url_host.empty")
	}
	queryURL := fmt.Sprintf("%s/api/v3/find/topoinst_with_statistics/biz/%d", hostBase, bizID)

	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), queryURL, CMDBHeader(), nil, 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("FindCMDBTopoInstByBizID.query.req.error, err: %s", err.Error())
	}
	logger.Debugf("FindCMDBTopoInstByBizID.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return nil, fmt.Errorf("FindCMDBTopoInstByBizID.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return nil, fmt.Errorf("FindCMDBTopoInstByBizID.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		return nil, fmt.Errorf("FindCMDBTopoInstByBizID.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}
	var queryResult []*TopoData
	err = json.Unmarshal(queryRespObj.Data, &queryResult)
	if err != nil {
		return nil, fmt.Errorf("FindCMDBTopoInstByBizID.data.Unmarshal.error, err: %s", err.Error())
	}
	return queryResult[0], nil
}

func GetServerListData(logger *logrus.Logger, isProd bool) ([]*ServerListDataItem, error) {
	logger = utils.CmdbLogger(logger)
	serverListURL := cfg.GetCmdbConfig().OtherRegion.ServerListURL
	token := cfg.GetCmdbConfig().OtherRegion.ServerListToken
	if isProd {
		serverListURL = cfg.GetCmdbConfig().ProdRegion.ServerListURL
		token = cfg.GetCmdbConfig().ProdRegion.ServerListToken
	}
	header := JSONHeader()
	header.Add("Authorization", "Bearer "+token)

	maxloop := 100
	currentNum := 0
	total := 0
	pageSize := 500
	resItem := make([]*ServerListDataItem, 0)
	for i := 1; i <= maxloop; i++ {
		query := map[string]string{
			"page":      fmt.Sprintf("%d", i),
			"page_size": fmt.Sprintf("%d", pageSize),
		}
		queryRespBin, queryRespCode, err := utils.HTTPGet(context.Background(), serverListURL, header, query, 5*time.Second)
		if err != nil {
			return nil, fmt.Errorf("GetServerListData.query.req.error, err: %s", err.Error())
		}
		logger.Debugf("GetServerListData.query.resp.dump: %s", string(queryRespBin))
		if queryRespCode != 200 {
			return nil, fmt.Errorf("GetServerListData.query.error, respCode: %d", queryRespCode)
		}
		queryRespObj := ServerListResp{}
		err = json.Unmarshal(queryRespBin, &queryRespObj)
		if err != nil {
			return nil, fmt.Errorf("GetServerListData.resp.Unmarshal.error, err: %s", err.Error())
		}
		if queryRespObj.Retcode != 0 {
			return nil, fmt.Errorf("GetServerListData.resp.error")
		}
		if total == 0 {
			total = queryRespObj.Data.Total
		}
		resItem = append(resItem, queryRespObj.Data.Items...)
		currentNum += pageSize
		if currentNum >= total {
			break
		}
	}

	return resItem, nil
}

// params := map[string]interface{}{
// 	"bk_host_ids":        hostIds,
// 	"add_to_modules":     moduleIds,
// 	"is_remove_from_all": false,
// 	"remove_from_node": map[string]interface{}{
// 		"bk_inst_id": bkBizId,
// 		"bk_obj_id":  "biz",
// 	},
// }
// // Create a Resty Client
// resp = new(BaseBKCmdbResponse[[]string])
// subPath := fmt.Sprintf("/api/v3/host/transfer_with_auto_clear_service_instance/bk_biz_id/%d", bkBizId)
// err = server.client.Post(subPath, params).Into(resp)

func TransferHost(logger *logrus.Logger, HostID int32, ModuleID int32, isProd bool) error {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return fmt.Errorf("TransferHost.url_host.empty")
	}
	bizID := cfg.GetCmdbConfig().OtherRegion.BizID
	if isProd {
		bizID = cfg.GetCmdbConfig().ProdRegion.BizID
	}
	url := fmt.Sprintf("%s/api/v3/host/transfer_with_auto_clear_service_instance/bk_biz_id/%d", hostBase, bizID)
	req := &TransferHostReqV2{
		BkHostIds:       []int32{HostID},
		IsRemoveFromAll: false,
		AddToModules:    []int32{ModuleID},
		RemoveFromNode: RemoveFromNode{
			BkInstId: bizID,
			BkObjId:  "biz",
		},
	}

	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("TransferHost.Marshal.error, err: %s", err.Error())
	}

	logger.Infof("TransferHost.query.req.dump: %s", data)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), url, CMDBHeader(), []byte(data), 5*time.Second)
	if err != nil {
		return fmt.Errorf("TransferHost.query.req.error, err: %s", err.Error())
	}
	logger.Infof("TransferHost.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return fmt.Errorf("TransferHost.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return fmt.Errorf("TransferHost.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		if queryRespObj.BKErrorCode == CCErrCoreServiceHostNotBelongBusiness {
			return ErrHostNotInBiz
		}
		return fmt.Errorf("TransferHost.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}
	return nil
}

func MoveHostToIdleResource(logger *logrus.Logger, hostID, bizID int32) error {
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return fmt.Errorf("MoveHostToIdleResource.url_host.empty")
	}
	const IdleResourceModuleID = 1
	url := fmt.Sprintf("%s/api/v3/hosts/modules/resource", hostBase)
	req := &MoveResourceReq{
		BkHostIDs:  []int32{hostID},
		BkBizID:    bizID,
		BkModuleID: IdleResourceModuleID,
	}
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("MoveHostToIdleResource.Marshal.error, err: %s", err.Error())
	}
	respBin, respCode, err := utils.HTTPPost(context.Background(), url, CMDBHeader(), data, 5*time.Second)
	if err != nil {
		return fmt.Errorf("MoveHostToIdleResource.post.req.error, err: %s", err.Error())
	}
	logger.Infof("MoveHostToIdleResource.post.resp.dump: %s", string(respBin))
	if respCode != 200 {
		return fmt.Errorf("MoveHostToIdleResource.post.error, respCode: %d", respCode)
	}
	respObj := CommonResponse{}
	err = json.Unmarshal(respBin, &respObj)
	if err != nil {
		return fmt.Errorf("MoveHostToIdleResource.resp.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BKErrorCode != 0 {
		return fmt.Errorf("MoveHostToIdleResource.resp.error, BKErrorCode: %d", respObj.BKErrorCode)
	}
	return nil
}

func MoveBizToBizIDIdle(logger *logrus.Logger, hostID, bizID int32) error {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return fmt.Errorf("MoveBizToBizIDIdle.url_host.empty")
	}
	url := fmt.Sprintf("%s/api/v3/hosts/modules/idle", hostBase)
	req := &MoveIdleReq{
		BkHostIDs: []int32{hostID},
		BkBizID:   bizID,
	}
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("MoveBizToBizIDIdle.Marshal.error, err: %s", err.Error())
	}
	logger.Infof("MoveBizToBizIDIdle.query.req.dump: %s", data)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), url, CMDBHeader(), data, 5*time.Second)
	if err != nil {
		return fmt.Errorf("MoveBizToBizIDIdle.query.req.error, err: %s", err.Error())
	}
	logger.Infof("MoveBizToBizIDIdle.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return fmt.Errorf("MoveBizToBizIDIdle.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return fmt.Errorf("MoveBizToBizIDIdle.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		return fmt.Errorf("MoveBizToBizIDIdle.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}
	return nil
}

func MoveMultipleHostToBizIDIdle(logger *logrus.Logger, hostIDs []int32, bizID int32) error {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return fmt.Errorf("MoveToBizIdle.url_host.empty")
	}
	url := fmt.Sprintf("%s/api/v3/hosts/modules/resource/idle", hostBase)
	req := &MoveIdleReq{
		BkHostIDs: hostIDs,
		BkBizID:   bizID,
	}
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("MoveIdleToBizIDIdle.Marshal.error, err: %s", err.Error())
	}
	logger.Infof("MoveIdleToBizIDIdle.query.req.dump: %s", data)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), url, CMDBHeader(), data, 5*time.Second)
	if err != nil {
		return fmt.Errorf("MoveIdleToBizIDIdle.query.req.error, err: %s", err.Error())
	}
	logger.Infof("MoveIdleToBizIDIdle.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return fmt.Errorf("MoveIdleToBizIDIdle.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return fmt.Errorf("MoveIdleToBizIDIdle.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		return fmt.Errorf("MoveIdleToBizIDIdle.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}
	return nil
}

func ChangeMulHostsStates(logger *logrus.Logger, hostIDs []int32, state string) error {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return fmt.Errorf("ChangeMulHostsStates.url_host.empty")
	}
	url := fmt.Sprintf("%s/api/v3/hosts/batch", hostBase)
	hostIDstrs := make([]string, 0)
	for _, hostID := range hostIDs {
		hostIDstrs = append(hostIDstrs, fmt.Sprintf("%d", hostID))
	}

	req := &ChangeStatesReq{
		BkHostIDs: strings.Join(hostIDstrs, ","),
		BkState:   state,
	}
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("ChangeMulHostsStates.Marshal.error, err: %s", err.Error())
	}
	logger.Infof("ChangeMulHostsStates.query.req.dump: %s", data)
	respBin, respCode, err := utils.HTTPPut(context.Background(), url, CMDBHeader(), data, 5*time.Second)
	if err != nil {
		return fmt.Errorf("ChangeMulHostsStates.query.req.error, err: %s", err.Error())
	}
	logger.Infof("ChangeMulHostsStates.query.resp.dump: %s", string(respBin))
	if respCode != 200 {
		return fmt.Errorf("ChangeMulHostsStates.query.error, respCode: %d", respCode)
	}
	respObj := CommonResponse{}
	err = json.Unmarshal(respBin, &respObj)
	if err != nil {
		return fmt.Errorf("ChangeMulHostsStates.resp.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BKErrorCode != 0 {
		return fmt.Errorf("ChangeMulHostsStates.resp.error, BKErrorCode: %d", respObj.BKErrorCode)
	}
	return nil
}

func MoveIdleToBizIDIdle(logger *logrus.Logger, hostID, bizID int32) error {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return fmt.Errorf("MoveToBizIdle.url_host.empty")
	}
	url := fmt.Sprintf("%s/api/v3/hosts/modules/resource/idle", hostBase)
	req := &MoveIdleReq{
		BkHostIDs: []int32{hostID},
		BkBizID:   bizID,
	}
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("MoveIdleToBizIDIdle.Marshal.error, err: %s", err.Error())
	}
	logger.Infof("MoveIdleToBizIDIdle.query.req.dump: %s", data)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), url, CMDBHeader(), data, 5*time.Second)
	if err != nil {
		return fmt.Errorf("MoveIdleToBizIDIdle.query.req.error, err: %s", err.Error())
	}
	logger.Infof("MoveIdleToBizIDIdle.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return fmt.Errorf("MoveIdleToBizIDIdle.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return fmt.Errorf("MoveIdleToBizIDIdle.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		return fmt.Errorf("MoveIdleToBizIDIdle.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}
	return nil
}

func MoveIdleToBizIdle(logger *logrus.Logger, hostID int32, isProd bool) error {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return fmt.Errorf("MoveIdleToBizIdle.url_host.empty")
	}

	bizID := cfg.GetCmdbConfig().OtherRegion.BizID
	if isProd {
		bizID = cfg.GetCmdbConfig().ProdRegion.BizID
	}
	return MoveIdleToBizIDIdle(logger, hostID, bizID)
}

func CreateSet(logger *logrus.Logger, setName string, isProd bool) (*SetData, error) {
	logger = utils.CmdbLogger(logger)
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	if hostBase == "" {
		return nil, fmt.Errorf("CreateSet.url_host.empty")
	}

	regionConfig := cfg.GetCmdbConfig().OtherRegion
	if isProd {
		regionConfig = cfg.GetCmdbConfig().ProdRegion
	}
	url := fmt.Sprintf("%s/api/v3/set/%d/batch", hostBase, regionConfig.BizID)
	req := &CreateSetReq{
		Sets: []SetCreation{
			{
				SetTemplateID: regionConfig.TemplateID,
				BkSetName:     setName,
				BkParentID:    regionConfig.BizID,
				BkBizID:       regionConfig.BizID,
			},
		},
	}

	data, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("CreateSet.Marshal.error, err: %s", err.Error())
	}

	logger.Infof("CreateSet.query.req.dump: %s", data)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), url, CMDBHeader(), data, 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("CreateSet.query.req.error, err: %s", err.Error())
	}
	logger.Infof("CreateSet.query.resp.dump: %s", string(queryRespBin))
	if queryRespCode != 200 {
		return nil, fmt.Errorf("CreateSet.query.error, respCode: %d", queryRespCode)
	}
	queryRespObj := CommonResponse{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return nil, fmt.Errorf("CreateSet.resp.Unmarshal.error, err: %s", err.Error())
	}
	if queryRespObj.BKErrorCode != 0 {
		return nil, fmt.Errorf("CreateSet.resp.error, BKErrorCode: %d", queryRespObj.BKErrorCode)
	}

	var queryResult []SetRespData
	err = json.Unmarshal(queryRespObj.Data, &queryResult)
	if err != nil {
		return nil, fmt.Errorf("CreateSet.data.Unmarshal.error, err: %s", err.Error())
	}
	return &queryResult[0].SetData, nil
}

// ListBkCmdbObj -
func ListBkCmdbObj(ctx context.Context, cmdbEnv, instType string, conds []string, limit, start int32, sort string) ([]string, BkCmdbQueryDataCountResult, error) {
	var err error
	hostBase := ""
	if cmdbEnv == "prod" {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	} else if cmdbEnv == "test" {
		hostBase = cfg.GetCmdbConfig().BkCmdbTestURL
	} else {
		return nil, BkCmdbQueryDataCountResult{}, fmt.Errorf("cmdbQuery.unknown_env")
	}
	queryURL := fmt.Sprintf("%s/api/v3/find/instassociation/object/%s", hostBase, instType)
	queryBody := ""
	for _, cond := range conds {
		queryBody, _ = sjson.SetRaw(queryBody, fmt.Sprintf("condition.%s.-1", instType), cond)
	}
	if limit == 0 {
		limit = 10
	}
	queryBody, _ = sjson.Set(queryBody, "page.limit", limit)
	queryBody, _ = sjson.Set(queryBody, "page.start", start)
	if sort != "" {
		queryBody, _ = sjson.Set(queryBody, "page.sort", sort)
	}
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), queryURL, JwtHeader(cmdbEnv == "prod"), []byte(queryBody), 5*time.Second)
	if err != nil {
		return nil, BkCmdbQueryDataCountResult{}, fmt.Errorf("cmdbQuery.http.error, err: %v", err)
	}
	if queryRespCode != 200 {
		return nil, BkCmdbQueryDataCountResult{}, fmt.Errorf("cmdbQuery.http.error, respCode: %d", queryRespCode)
	}
	logger.Infof("cmdbQuery.http.dump: %s", string(queryRespBin))
	queryRespObj := BkCmdbQueryDataCountResult{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return nil, BkCmdbQueryDataCountResult{}, fmt.Errorf("cmdbQuery.http.error: %s", err.Error())
	}
	respInfo := []string{}
	for _, i := range queryRespObj.Data.Info {
		respInfo = append(respInfo, string(i))
	}
	return respInfo, queryRespObj, nil
}

// AddBkCmdbObj 创建自定义资源
func AddBkCmdbObj(ctx context.Context, instType string, obj interface{}) (int, error) {
	var err error
	hostBase := cfg.GetCmdbConfig().BkCmdbURL
	url := fmt.Sprintf("%s/api/v3/create/instance/object/%s", hostBase, instType)
	creationBody, err := json.Marshal(obj)
	if err != nil {
		return -1, fmt.Errorf("cmdCreation.http.error, err: %v", err)
	}
	respBin, respCode, err := utils.HTTPPost(context.Background(), url, JwtHeader(true), []byte(creationBody), 5*time.Second)
	if err != nil {
		return -1, fmt.Errorf("cmdCreation.http.error, err: %v", err)
	}
	if respCode != 200 {
		return -1, fmt.Errorf("cmdCreation.http.error, respCode: %d", respCode)
	}
	logger.Infof("cmdCreation.http.dump: %s", string(respBin))
	respObj := BkCmdbCommonCreateDataResult{}
	err = json.Unmarshal(respBin, &respObj)
	if err != nil {
		return -1, fmt.Errorf("cmdCreation.http.error: %s", err.Error())
	}
	return respObj.Data.BkInstID, nil
}

// BkCmdbQueryDataCountResult -
type BkCmdbQueryDataCountResult struct {
	Result      bool   `json:"result"`
	BkErrorCode int    `json:"bk_error_code"`
	BKErrorMsg  string `json:"bk_error_msg"`
	Data        struct {
		Count int               `json:"count"`
		Info  []json.RawMessage `json:"info"`
	}
}

type BkCmdbCommonCreateDataResult struct {
	Result      bool              `json:"result"`
	BkErrorCode int               `json:"bk_error_code"`
	BKErrorMsg  string            `json:"bk_error_msg"`
	Data        CommonCreationObj `json:"data"`
}

type CommonCreationObj struct {
	BkInstID          int    `json:"bk_inst_id"`
	BkSupplierAccount string `json:"bk_supplier_account"`
	BkCreateTime      string `json:"bk_create_time"`
	BkLastTime        string `json:"bk_last_time"`
}

type RdsCreation struct {
	BkInstName      string `json:"bk_inst_name"`
	CreateTime      string `json:"CreateTime"`
	DBEngine        string `json:"DBEngine"`
	DBEngineVersion string `json:"DBEngineVersion"`
	DBType          string `json:"DBType"`
	InstanceClass   string `json:"InstanceClass"`
	InstanceID      string `json:"InstanceID"`
	InstanceName    string `json:"InstanceName"`
	InstanceStatus  string `json:"InstanceStatus"`
	ProviderName    string `json:"ProviderName"`
	RegionID        string `json:"RegionID"`
	ZoneID          string `json:"ZoneID"`
	VpcID           string `json:"VpcID"`
}

type RedisCreation struct {
	BkInstName      string `json:"bk_inst_name"`
	InstanceID      string `json:"InstanceID"`
	InstanceName    string `json:"InstanceName"`
	ProviderName    string `json:"ProviderName"`
	VpcID           string `json:"VpcID"`
	VSwitchID       string `json:"VSwitchID"`
	RegionID        string `json:"RegionID"`
	ZoneID          string `json:"ZoneID"`
	CreateTime      string `json:"CreateTime"`
	InstanceClass   string `json:"InstanceClass"`
	InstanceStatus  string `json:"InstanceStatus"`
	DBEngine        string `json:"DBEngine"`
	DBEngineVersion string `json:"DBEngineVersion"`
}
