package mapstruct

// MapStruct <string, any>
type MapStruct map[string]struct{}

// Set <string, empty>
func (m MapStruct) Set(s string) {
	m[s] = struct{}{}
}

// ToList to list
func (m MapStruct) ToList() []string {
	s := make([]string, 0)
	for k := range m {
		s = append(s, k)
	}

	return s
}

// MapAssign 合并map，注意此操作会修改原始对象
func MapAssign(target map[string]interface{}, source ...map[string]interface{}) {
	for _, raw := range source {
		for k, v := range raw {
			target[k] = v
		}
	}

	return
}
