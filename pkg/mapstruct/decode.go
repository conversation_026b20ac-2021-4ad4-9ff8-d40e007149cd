package mapstruct

import (
	"bytes"
	"encoding/json"
)

// MapStr map[string]interface{}
type MapStr map[string]interface{}

// Struct2Map 转换效率不高，避免在查询时使用，仅限于数据写入时使用
func Struct2Map(v interface{}) (map[string]interface{}, error) {
	buf, err := json.<PERSON>(v)
	if err != nil {
		return nil, err
	}
	data := make(map[string]interface{})
	if err := json.Unmarshal(buf, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// DecodeFromMapStr from mapStr
func DecodeFromMapStr(data interface{}, input MapStr) error {
	inputBytes, err := json.<PERSON>(input)
	if err != nil {
		return err
	}
	d := json.NewDecoder(bytes.NewReader(inputBytes))
	// d.UseNumber()
	err = d.Decode(data)
	return err
}
