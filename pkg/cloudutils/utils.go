package cloudutils

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// ModelInterface 为了解决循环依赖，使用interface来调用model层方法
type ModelInterface interface {
	FindExistsInstancename(ctx context.Context, hostnames []string) ([]string, error)
	FindRegexInstanceName(ctx context.Context, regex primitive.Regex) ([]string, error)
	Count(ctx context.Context, filter map[string]interface{}) (int64, error)
}

// Cloudutils ...
type Cloudutils struct {
	Model ModelInterface
}

// GetCloudutils ...
func GetCloudutils(model ModelInterface) *Cloudutils {
	return &Cloudutils{
		Model: model,
	}
}

// SysType 操作系统类型
type SysType = int

const (
	// Windows windows
	Windows SysType = iota
	// Linux linux
	Linux
)
const ruleReg = `\[\d{0,8}(,[0-8]?])`

/*
GenInstanceName 根据规则生成有序id
*rule rule生成规则: nameprefix-[begin_number,bits]name_suffix, begin_number: 起始值,默认为0,最大值为99999999; bits:位数,默认为6,最大为8位

	k8s-node-[0, 4].mhy.com: k8s-node-0000.mhy.com
	k8s-node-[99].mhy.com: k8s-node-000099.mhy.com
	rule不能包含空格;不能以./-开头/结尾;不能使用连续的./-;不能仅使用数字;windows长度2-15个字符;linux2-128个字符;且[begin_number,bits]只能有一个;
	[begin_number,bits]按规则生成查询mongo正则表达式,排序获取最大值?并保存符合规则生成的最大index号(减少数据库查询,不服用小于此编号)
	*count 待生成的名称数量

*ispID 云厂商ID
*unique 是否全局唯一
*sysType 操作系统类型
*/
func (c Cloudutils) GenInstanceName(ctx context.Context, rule string, count int, unique bool, sysType SysType) ([]string, error) {
	// 判断rule是否为生成计算方法,即是否包含"[]"
	// reg := '\[[0-9]{0,8}(\,[0-8]){0,1}\]'
	// regexp.MustCompile(`\[\d{0,8}(\\,[0-8]{0, 1})]`)
	if count == 0 {
		return []string{}, nil
	}
	if err := RuleCheck(rule, sysType); err != nil {
		return nil, err
	}
	r := regexp.MustCompile(ruleReg)
	matchList := r.FindStringSubmatch(rule)
	if len(matchList) == 0 || matchList == nil {
		// 不合法,生成满足要求的重复Hostname
		if unique && count > 1 {
			return nil, fmt.Errorf("创建多台机器不能使用唯一实例名称")
		}
		rHostname := repeatStringArr(rule, count)
		if count == 1 && unique {
			return c.filterExistInstanceName(ctx, rHostname)
		}
		return rHostname, nil
	}

	str := strings.Trim(matchList[0], "[")
	str = strings.Trim(str, "]")

	var beginstr, bitstr string
	for index, val := range strings.Split(str, ",") {
		if index == 0 {
			beginstr = val
		} else {
			bitstr = val
		}
	}
	begin, err := strconv.Atoi(beginstr)
	if err != nil {
		begin = 0
	}
	bits, err := strconv.Atoi(bitstr)
	if err != nil {
		bits = 6
	}

	if len(string(rune(begin))) > bits {
		return nil, fmt.Errorf("begin_number超过bits限制")
	}
	prefix := strings.Split(rule, "[")[0]
	rSuffix := strings.Split(rule, "]")
	suffix := rSuffix[len(rSuffix)-1]
	// 计算区间内最大长度
	maxIndex := utils.MaxUInt(bits)
	var Hosts []string
	for {
		if uint64(begin) > maxIndex {
			return nil, fmt.Errorf("超过给定的最大长度")
		}
		unFilterHost := genInstanceName(prefix, suffix, begin, bits, count)
		if !unique {
			return unFilterHost, nil
		}
		fHost, err := c.filterExistInstanceName(ctx, unFilterHost)
		if err != nil {
			return nil, err
		}
		Hosts = append(Hosts, fHost...)
		if len(Hosts) >= count {
			return Hosts, nil
		}
		begin += count
		count -= len(fHost)
	}
}

func genInstanceName(prefix, suffix string, begin, bits, count int) []string {
	var names []string
	for i := 0; i < count; i++ {
		names = append(names, fmt.Sprintf("%s%s%s", prefix, utils.StrLeftFill(strconv.Itoa(begin+i), "0", bits), suffix))
	}

	return names
}

func (c Cloudutils) filterExistInstanceName(ctx context.Context, hostnames []string) ([]string, error) {
	instanceNames, err := c.Model.FindExistsInstancename(ctx, hostnames)
	if err != nil {
		return nil, err
	}
	hostnameMap := utils.StrArrayToMap(hostnames)
	for _, i := range instanceNames {
		delete(hostnameMap, i)
	}

	return utils.Keys(hostnameMap), nil
}

// RuleCheck 主机名生成规格检查
func RuleCheck(rule string, sysType SysType) error {
	r := regexp.MustCompile(`(^(\s|-|\|/))|((\s|-|\|/)$)|([.\-]){2,}|([\\/])|\s|;`)
	matchList := r.FindStringSubmatch(rule)
	if len(matchList) > 0 {
		return fmt.Errorf("主机名生成规则不合法")
	}

	if len(rule) >= 128 || (sysType == Windows && len(rule) > 20) {
		return fmt.Errorf("主机名生成规则不合法")
	}

	// 名称中不得包含空格

	return nil
}

func repeatStringArr(s string, count int) []string {
	var l []string
	for i := 0; i < count; i++ {
		l = append(l, s)
	}
	return l
}

// CheckInstanceNameSeq 查找符合前后缀的实例名，获取序号字段的，最小未使用和最大已使用
func (c Cloudutils) CheckInstanceNameSeq(ctx context.Context, prefix string, suffix string) (int32, int32, error) {
	instanceNames, err := c.Model.FindRegexInstanceName(ctx, primitive.Regex{
		Pattern: fmt.Sprintf("%s.*%s", regexp.QuoteMeta(prefix), regexp.QuoteMeta(suffix)),
		Options: "",
	})
	if err != nil {
		return 0, 0, err
	}
	seqMap := map[int32]bool{}
	for _, i := range instanceNames {
		seq := strings.TrimSuffix(strings.TrimPrefix(i, prefix), suffix)
		seqNumber, err := strconv.ParseInt(seq, 10, 64)
		if err != nil {
			continue
		}
		seqMap[int32(seqNumber)] = true
	}
	minUnused, maxUnused := int32(1), int32(1)
	for minUnused <= 9999 {
		if seqMap[minUnused] {
			minUnused++
			continue
		}
		break
	}
	for s := range seqMap {
		if s >= maxUnused {
			maxUnused = s + 1
		}
	}
	return minUnused, maxUnused, nil
}
