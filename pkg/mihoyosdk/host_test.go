package mihoyosdk

import (
	"fmt"
	"testing"
)

func TestListVm(t *testing.T) {
	sdk := NewMihoyoComputeSdkWithAksk(
		"dbc2695363bb510309dcc37c1633b5a1cf3230b51cc8531a589d7e970b384762f13c3e1047a50b7a8b64adb8544f4d2d70088958edd7f7c9f29bac003eabf857",
		"a2f7ebca24b2ef8b3f2663ba9014b164e629b9a65925b7f5b436d5b11b5f49b47c30e572f9cc2bd4e184a2b5f6789345c13b0a6b1e7b657a9d19dcf5a58f7869",
		158,
		"shanghai",
	)
	res, err := sdk.ListVms(100, 1)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v", res)
}
