package mihoyosdk

// import (
// 	"encoding/json"
// 	"fmt"
// 	"io"
// 	"net/http"

// 	"github.com/sirupsen/logrus"
// )

// InstanceType 实例规格类型
type InstanceType struct {
	ID              string `json:"id"`
	Name            string `json:"name"`
	CPUAvailable    int64  `json:"cpu_available"`
	MemoryAvailable int64  `json:"memory_available"`
	GpuAvailable    int64  `json:"gpu_available"`
	CPUVendor       int64  `json:"cpu_vendor"`
	CPUModel        string `json:"cpu_model"`
	IsGpu           bool   `json:"is_gpu"`
	LocalDisk       bool   `json:"local_disk"`
}

// // FindInstanceType 获取规格类型
// func (m MihoyoComputeClient) FindInstanceType() ([]InstanceType, error) {
// 	url := fmt.Sprintf("%s/api/v1/instances", domain)
// 	req, _ := http.NewRequest("GET", url, nil)
// 	req.Header.Set("projectId", m.key.projectIDStr)
// 	req.Header.Set("Cookie", "_cookie="+m.GetTokenByCache())
// 	logrus.Infof("mihoyosdk.FindInstanceType.req=%+v", req)
// 	resp, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		return nil, err
// 	}
// 	respBody, _ := io.ReadAll(resp.Body)
// 	logrus.Infof("mihoyosdk.FindInstanceType.resp=%s", string(respBody))
// 	respObj := &struct {
// 		Instances []InstanceType `json:"instances"`
// 	}{}
// 	err = json.Unmarshal(respBody, respObj)
// 	if err != nil {
// 		return nil, err
// 	}
// 	permission, err := m.GetPermission()
// 	if err != nil {
// 		return nil, err
// 	}
// 	result := []InstanceType{}
// 	for _, instance := range respObj.Instances {
// 		authStatus, authExist := permission[instance.Name]
// 		if !authExist {
// 			result = append(result, instance)
// 		} else if authStatus {
// 			result = append(result, instance)
// 		}

// 	}
// 	return result, nil
// }

// // GetPermission 获取全局权限位
// func (m MihoyoComputeClient) GetPermission() (map[string]bool, error) {
// 	url := fmt.Sprintf("%s/api/v1/account/project/%s/projects/permissions/all?project_id=%s", domain, m.key.projectIDStr, m.key.projectIDStr)
// 	req, _ := http.NewRequest("GET", url, nil)
// 	req.Header.Set("projectId", m.key.projectIDStr)
// 	req.Header.Set("Cookie", "_cookie="+m.GetTokenByCache())
// 	logrus.Infof("mihoyosdk.GetPermission.req=%+v", req)
// 	resp, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		return nil, err
// 	}
// 	respBody, _ := io.ReadAll(resp.Body)
// 	logrus.Infof("mihoyosdk.GetPermission.resp=%s", string(respBody))
// 	respObj := &struct {
// 		Permissions []struct {
// 			ResourcesType string `json:"resources_type"`
// 			Status        bool   `json:"status"`
// 		} `json:"permissions"`
// 	}{}
// 	err = json.Unmarshal(respBody, respObj)
// 	if err != nil {
// 		return nil, err
// 	}
// 	result := map[string]bool{}
// 	for _, p := range respObj.Permissions {
// 		result[p.ResourcesType] = p.Status
// 	}
// 	return result, nil
// }
