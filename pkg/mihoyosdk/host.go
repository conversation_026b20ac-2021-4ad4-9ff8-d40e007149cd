package mihoyosdk

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
)

// Host 描述虚拟机实例
type Host struct {
	ID         string        `json:"id"`
	HostID     int64         `json:"host_id"`
	Name       string        `json:"name"`
	ProjectID  int64         `json:"project_id"`
	VMType     string        `json:"vm_type"`
	Status     string        `json:"status"`
	Resource   HostResource  `json:"resource"`
	Image      HostImageMini `json:"image"`
	Network    []HostNetwork `json:"network"`
	Volumes    []HostVolume  `json:"volumes"`
	DataCenter string        `json:"datacenter"`
	CreateTime string        `json:"create_time"`
}

// HostResource 描述虚拟机资源
type HostResource struct {
	CPU    int64 `json:"cpu"`
	Gpu    int64 `json:"gpu"`
	Memory int64 `json:"memory"`
}

// HostImageMini 描述虚拟机镜像-简化
type HostImageMini struct {
	ImageID   string `json:"id"`
	OsName    string `json:"os_name"`
	OsVersion string `json:"os_version"`
	OsType    string `json:"os_type"`
}

// HostNetwork 描述虚拟机网络
type HostNetwork struct {
	IP     string `json:"ip"`
	Mac    string `json:"mac"`
	Subnet string `json:"subnet"`
	Type   string `json:"type"`
}

// HostVolume 描述虚拟机磁盘
type HostVolume struct {
	Arch       string `json:"arch"`
	IsBootDisk bool   `json:"is_boot_disk"`
	Size       int64  `json:"size"`
	Type       string `json:"type"`
}

func (m MihoyoComputeClient) setCommonHeaderf(request *http.Request) error {
	var bodyBytes []byte
	var err error
	if request.Body != nil {
		bodyBytes, err = io.ReadAll(request.Body)
		if err != nil {
			logrus.Errorf("Failed to read request body")
			return err
		}
	}

	timeStr, signBase64, bodyHash, randomStr, err := GenerateClientParam(bodyBytes, m.ak, m.sk)
	if err != nil {
		return err
	}

	request.Header.Add("x-auth-accesskey", m.ak)
	request.Header.Add("x-auth-timestamp", tea.StringValue(timeStr))
	request.Header.Add("x-auth-signature", tea.StringValue(signBase64))
	request.Header.Add("x-auth-random-str", tea.StringValue(randomStr))
	request.Header.Add("x-auth-body-hash", tea.StringValue(bodyHash))
	return nil
}

// ListVms 虚拟机列表，分页接口，不支持过滤字段
func (m MihoyoComputeClient) ListVms(pageSize, pageNum int64) ([]*Host, error) {
	url := fmt.Sprintf("%s/api/v1/project/%d/vms", domain, m.projectID)
	req, _ := http.NewRequest("GET", url, nil)
	query := req.URL.Query()
	query.Set("page_size", fmt.Sprintf("%d", pageSize))
	query.Set("page_num", fmt.Sprintf("%d", pageNum))
	query.Set("region", m.regionID)
	req.URL.RawQuery = query.Encode()

	m.setCommonHeaderf(req)
	logrus.Infof("mihoyosdk.ListVms.req=%+v", req)
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	respBody, _ := io.ReadAll(resp.Body)
	logrus.Infof("mihoyosdk.ListVms.resp=%s", string(respBody))
	respObj := &struct {
		Vms   []*Host `json:"vms"`
		Count int     `json:"count"`
	}{}
	err = json.Unmarshal(respBody, respObj)
	if err != nil {
		return nil, err
	}
	return respObj.Vms, nil
}

// OperateVM 虚拟机操作
// 支持poweron,poweroff,shutdown,reboot四个指令
// shutdown仅对虚拟机发送指令，不保证成功，如失败需用poweroff
func (m MihoyoComputeClient) OperateVM(instanceID string, command string) (bool, error) {
	if command != "poweron" && command != "poweroff" && command != "reboot" && command != "shutdown" {
		return false, fmt.Errorf("OperateVM.UnsupportCommand:%s", command)
	}
	url := fmt.Sprintf("%s/api/v1/project/%d/vms/%s/%s", domain, m.projectID, instanceID, command)
	req, _ := http.NewRequest("PUT", url, nil)
	m.setCommonHeaderf(req)
	logrus.Infof("mihoyosdk.OperateVM.%s.req=%+v", command, req)
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return false, err
	}
	respBody, _ := io.ReadAll(resp.Body)
	logrus.Infof("mihoyosdk.OperateVM.%s.resp=%s", command, string(respBody))
	respObj := &struct {
		RequestID string `json:"request_id"`
		VMID      string `json:"vm_id"`
	}{}
	err = json.Unmarshal(respBody, respObj)
	if err != nil {
		return false, fmt.Errorf("json_unmarshal_failed, err:%s, raw:%s", err.Error(), string(respBody))
	}
	return respObj.VMID == instanceID, nil
}

// HostCreateReq 创建新实例请求体
type HostCreateReq struct {
	Name       string       `json:"name"`
	ProjectID  int64        `json:"project_id"`
	DataCenter string       `json:"datacenter"`
	VMType     string       `json:"vm_type"`
	Resource   HostResource `json:"resource"`
	ImageID    string       `json:"image_id"`
	Volumes    []HostVolume `json:"volumes"`
	Password   string       `json:"password"`
}

// // CreateVM 虚拟机创建
// func (m MihoyoComputeClient) CreateVM(r *HostCreateReq) (string, error) {
// 	passwordBase64 := base64.StdEncoding.EncodeToString([]byte(r.Password))
// 	r.Password = passwordBase64
// 	r.ProjectID = m.key.projectID
// 	reqBin, _ := json.Marshal(r)
// 	url := fmt.Sprintf("%s/api/v1/project/%s/vms", domain, m.key.projectIDStr)
// 	req, _ := http.NewRequest("POST", url, bytes.NewReader(reqBin))
// 	req.Header.Set("projectId", m.key.projectIDStr)
// 	req.Header.Set("Cookie", "_cookie="+m.GetTokenByCache())
// 	req.Header.Set("Content-Type", "application/json")
// 	logrus.Infof("mihoyosdk.CreateVM.req=%s", string(reqBin))
// 	resp, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		return "", err
// 	}
// 	respBody, _ := io.ReadAll(resp.Body)
// 	logrus.Infof("mihoyosdk.CreateVM.resp=%s", string(respBody))
// 	respObj := &struct {
// 		RequestID string `json:"request_id"`
// 		VMID      string `json:"vm_id"`
// 	}{}
// 	err = json.Unmarshal(respBody, respObj)
// 	if err != nil {
// 		return "", err
// 	}
// 	return respObj.VMID, nil
// }

// // DestroyVM 虚拟机销毁删除
// func (m MihoyoComputeClient) DestroyVM(instanceID string) error {
// 	url := fmt.Sprintf("%s/api/v1/project/%s/vms/%s", domain, m.key.projectIDStr, instanceID)
// 	req, _ := http.NewRequest("DELETE", url, nil)
// 	req.Header.Set("projectId", m.key.projectIDStr)
// 	req.Header.Set("Cookie", "_cookie="+m.GetTokenByCache())
// 	logrus.Infof("mihoyosdk.DestroyVM.req=%+v", req)
// 	resp, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		return err
// 	}
// 	respBody, _ := io.ReadAll(resp.Body)
// 	logrus.Infof("mihoyosdk.DestroyVM.resp=%s,%s", resp.Status, string(respBody))
// 	return nil
// }
