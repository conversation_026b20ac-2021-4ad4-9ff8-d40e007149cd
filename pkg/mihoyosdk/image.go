package mihoyosdk

// HostImage 实例镜像
type HostImage struct {
	ImageID          string `json:"image_id"`
	ImageName        string `json:"image_name"`
	ImageSize        int64  `json:"image_size"`
	ImageDescription string `json:"image_description"`
	ImagePath        string `json:"image_path"`
	OsName           string `json:"os_name"`
	OsVersion        string `json:"os_version"`
	OsType           string `json:"os_type"`
	Architecture     string `json:"architecture"`
	Status           string `json:"status"`
	Md5              string `json:"md5"`
	ImageType        string `json:"image_type"`
	CreateTime       string `json:"create_time"`
}

// // FindImages 获取镜像
// func (m MihoyoComputeClient) FindImages() ([]HostImage, error) {
// 	url := fmt.Sprintf("%s/api/v1/project/%s/images", domain, m.key.projectIDStr)
// 	req, _ := http.NewRequest("GET", url, nil)
// 	req.Header.Set("projectId", m.key.projectIDStr)
// 	req.Header.Set("Cookie", "_cookie="+m.GetTokenByCache())
// 	logrus.Infof("mihoyosdk.FindImages.req=%+v", req)
// 	resp, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		return nil, err
// 	}
// 	respBody, _ := io.ReadAll(resp.Body)
// 	logrus.Infof("mihoyosdk.FindImages.resp=%s", string(respBody))
// 	respObj := &struct {
// 		Images []HostImage `json:"images"`
// 	}{}
// 	err = json.Unmarshal(respBody, respObj)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return respObj.Images, nil
// }
