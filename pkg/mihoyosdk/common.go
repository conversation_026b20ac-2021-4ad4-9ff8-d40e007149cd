package mihoyosdk

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"hash"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

type HashFunc func() hash.Hash

var hashFunc HashFunc = sha256.New
var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

// 将字典排序后的请求体进行MD5散列值并转换成BASE64编码，用于加密body
func Md5AndBase64(str string) (string, error) {

	sortStr, err := sortJSON(str)
	if err != nil {
		logrus.Errorf("Md5AndBase64 err: %v", err)
		return "", err
	}
	data := []byte(sortStr)
	has := md5.Sum(data)
	md5str := fmt.Sprintf("%x", has)
	encodeData := base64.StdEncoding.EncodeToString([]byte(md5str))
	return encodeData, err
}

// 对body字典排序
func sortJSON(s string) (string, error) {
	if s == "" {
		return s, nil
	}
	var m map[string]interface{}
	err := json.Unmarshal([]byte(s), &m)
	if err != nil {
		logrus.Errorf("unmarshal error %v", err)
		return "", err
	}
	// 原理是json marshal会自动根据key 升序排列表
	bs, err := json.Marshal(m)
	if err != nil {
		logrus.Errorf("marshal error %v", err)
		return "", err
	}

	reader := bytes.NewReader(bs)
	decode := json.NewDecoder(reader)
	decode.UseNumber()
	err = decode.Decode(&m)
	if err != nil {
		logrus.Errorf("decode error %v", err)
		return "", err
	}

	// 重新做一次序列化，并禁用Html Escape
	buffer := bytes.NewBufferString("")
	encoder := json.NewEncoder(buffer)
	encoder.SetEscapeHTML(false)
	err = encoder.Encode(m)
	if err != nil {
		logrus.Errorf("encode error %v", err)
		return "", err
	}

	marshal := strings.TrimSpace(buffer.String())
	return marshal, nil
}

func HmacSum(key []byte, elems ...string) []byte {
	h := hmac.New(hashFunc, key)
	sort.Strings(elems)
	s := strings.Join(elems, "")
	h.Write([]byte(s))
	return h.Sum(nil)
}

// 生成随机字符串
func RandStringRunes(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

// EncodeToString 编码为16进制字符串
func EncodeToString(b []byte) string {
	return hex.EncodeToString(b)
}

// DecodeString 解码给定的16进制字符串得到MAC
func DecodeString(s string) ([]byte, error) {
	return hex.DecodeString(s)
}

func GenerateClientParam(body []byte, ak, sk string) (*string, *string, *string, *string, error) {
	ss := make([]string, 0, 4)
	randomStr := RandStringRunes(6)
	timeStr := strconv.FormatInt(time.Now().Unix(), 10)
	ss = append(ss, ak, randomStr, timeStr)

	var bodyHash string
	var err error
	if len(body) > 0 {
		bodyHash, err = Md5AndBase64(string(body))
		if err != nil {
			logrus.Errorf("md5 error %v", err)
			return nil, nil, nil, nil, err
		}
		ss = append(ss, bodyHash)
	}
	b := HmacSum([]byte(sk), ss...)
	signStr := EncodeToString(b)
	signBase64 := base64.StdEncoding.EncodeToString([]byte(signStr))

	return &timeStr, &signBase64, &bodyHash, &randomStr, nil
}
