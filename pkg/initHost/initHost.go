package inithost

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	httpclient "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/httpClient"
)

// HostParam 初始化主机参数
type HostParam struct {
	Zone    string `json:"zone"`
	Env     string `json:"env"`
	IP      string `json:"ip"`
	AgentID string `json:"agent_id"`
}

// PostHostParams 初始化主机参数
type PostHostParams struct {
	Token string      `json:"token"`
	Host  []HostParam `json:"host"`
}

// Do 执行初始化
func Do(initServer string, params []HostParam) error {
	var hosts []string
	for _, host := range params {
		hosts = append(hosts, host.IP)
	}
	token := fmt.Sprintf("%x", md5.Sum([]byte(strings.Join(hosts, ",")+"sadfafasfdgvdacxva")))
	// host, _ := json.Marshal(params)
	payloads := PostHostParams{
		Token: token,
		Host:  params,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()
	data, _ := json.Marshal(payloads)
	resp, err := httpclient.NewHttpclient().POSTCtx(ctx, initServer, map[string][]string{
		"Content-Type": {"application/json"},
	}, data)
	fmt.Println(string(resp))

	return err
}
