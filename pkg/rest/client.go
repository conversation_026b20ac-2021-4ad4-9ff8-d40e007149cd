package rest

import (
	"encoding/json"
	"fmt"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
)

type Client struct {
	client  *resty.Client
	BaseUri string
	Token   string
	Headers map[string]string
}

// Response 用于发送http请求后返回
type Response struct {
	*resty.Response
	Err error
}

func NewClient(baseUri string) *Client {
	client := resty.New()
	client.SetTimeout(time.Duration(5) * time.Second) // 请求超时时间（单位：秒）

	restCli := &Client{
		client:  client,
		BaseUri: baseUri,
		Headers: map[string]string{
			"Accept": "application/json",
		},
	}
	client.SetCloseConnection(true)

	return restCli
}
func (client Client) WithHeader(header, value string) *Client {
	client.client.SetHeader(header, value)
	return &client
}

func (client Client) WithToken(token string) *Client {
	client.client.SetAuthToken(token)
	client.Token = token
	return &client
}

func (client Client) WithBasicAuth(username, password string) *Client {
	client.client.SetBasicAuth(username, password)
	return &client
}

func (client Client) Ping() {
	timeout := 2 // 请求超时时间（单位：秒）

	r := resty.New()
	r.SetTimeout(time.Duration(timeout) * time.Second)

	resp, err := r.R().Get(client.BaseUri)
	logger.Info(string(resp.Body()))
	if err != nil {
		logger.Error("连接失败:", err)
	}
}

type Option func(request *resty.Request)

func WithHeader(header, value string) Option {
	return func(request *resty.Request) {
		request.SetHeader(header, value)
	}
}

func WithFormData(data map[string]string) Option {
	return func(request *resty.Request) {
		request.SetFormData(data)
	}
}

func (client Client) Verb(method, url string, body interface{}, params map[string]string, options ...Option) *Response {
	request := client.client.R() // 重新创建请求对象，开辟新的内存空间， 保证线程安全
	request.SetHeaders(client.Headers)
	request.SetAuthToken(client.Token)
	for _, option := range options {
		option(request)
	}
	bodyJson, err := json.Marshal(body)
	if err != nil {
		logger.Error("marshal body failed:", err)
		return &Response{
			nil,
			err,
		}
	}
	logger.Infof("request start----resty %s url: %s . requst body: %s , request params: %s, err: %v", method, url, string(bodyJson), params, err)

	// 表单模式不使用SetBody方法
	contentType := request.Header.Get("Content-Type")
	if contentType != "application/x-www-form-urlencoded" {
		request = request.SetBody(body)
	}
	if len(params) != 0 {
		request = request.SetQueryParams(params)
	}

	resp, err := request.
		Execute(method, url)
	if resp != nil && resp.IsError() {
		err = fmt.Errorf("request status code: %d status: %s response: %s", resp.StatusCode(), resp.Status(), resp.Body())
	}
	logger.Debugf("end----resty %s url: %s params:%v request: %s resp: %v", method, url, params, bodyJson, resp)

	return &Response{
		resp,
		err,
	}
}

func (client Client) Post(uri string, reqBody interface{}, options ...Option) (response *Response) {
	url := client.processUrl(uri)
	response = client.Verb(resty.MethodPost, url, reqBody, nil, options...)
	if response.Err != nil {
		logger.Error("request failed:", response.Err)
		return response
	}
	logger.Infof("resp.StatusCode(): %d resp.IsSuccess(): %v", response.StatusCode(), response.IsSuccess())

	return response
}

func (client Client) Get(uri string, reqBody map[string]string) (response *Response) {
	url := client.processUrl(uri)
	response = client.Verb(resty.MethodGet, url, nil, reqBody)
	if response.Err != nil {
		logger.Error("request failed:", response.Err)
		return response
	}
	logger.Infof("resp.StatusCode(): %d resp.IsSuccess(): %v", response.StatusCode(), response.IsSuccess())

	return
}

func (client Client) Put(uri string, reqBody interface{}) (response *Response) {
	url := client.processUrl(uri)
	response = client.Verb(resty.MethodPut, url, reqBody, nil)
	if response.Err != nil {
		logger.Error("request failed:", response.Err)
		return response
	}
	logger.Infof("resp.StatusCode(): %d resp.IsSuccess(): %v", response.StatusCode(), response.IsSuccess())

	return
}

func (client Client) Delete(uri string, reqBody interface{}) (response *Response) {
	url := client.processUrl(uri)
	response = client.Verb(resty.MethodDelete, url, reqBody, nil)
	if response.Err != nil {
		logger.Error("request failed:", response.Err)
		return response
	}
	logger.Infof("resp.StatusCode(): %d resp.IsSuccess(): %v", response.StatusCode(), response.IsSuccess())

	return
}

func (client Client) Patch(uri string, reqBody interface{}) (response *Response) {
	url := client.processUrl(uri)
	response = client.Verb(resty.MethodPatch, url, reqBody, nil)
	if response.Err != nil {
		logger.Error("request failed:", response.Err)
		return response
	}
	logger.Infof("resp.StatusCode(): %d resp.IsSuccess(): %v", response.StatusCode(), response.IsSuccess())

	return
}

// 此方法用于处理各种斜杠 问题，导致的请求不可用
func (client Client) processUrl(uri string) string {
	u, err := url.Parse(client.BaseUri)
	if err != nil {
		logger.Fatal(err)
	}
	u.Path = path.Join(u.Path, uri)
	url := u.String()

	// 如果uri末尾存在"/", 保留, 适配调用Confd平台相关uri
	if strings.HasSuffix(uri, "/") {
		url += "/"
	}

	//在 Go 1.19 或更高版本中使用url.JoinPath函数
	//s, err := url.JoinPath("http://foo", "bar.html")

	return url
}

func (r *Response) Into(obj interface{}) error {
	if r.Err != nil {
		return r.Err
	}

	if r != nil && len(r.Body()) != 0 {
		err := Unmarshal(r.Body(), obj)
		if nil != err {
			if r.StatusCode() >= 300 {
				return fmt.Errorf("http request err: %s", string(r.Body()))
			}
			logger.Errorf("invalid response body, unmarshal json failed, reply:%s, error:%s", string(r.Body()), err.Error())
			return fmt.Errorf("http response err: %v, raw data: %s", err, r.Body())
		}
	} else if r.StatusCode() >= 300 {
		return fmt.Errorf("http request failed: %s", r.Status())
	}
	return nil
}

func (r *Response) IntoStruct(obj interface{}) error {
	if nil != r.Err {
		return r.Err
	}
	if len(r.Body()) != 0 {
		err := json.Unmarshal(r.Body(), obj)
		if nil != err {
			if r.StatusCode() >= 300 {
				return fmt.Errorf("http request err: %s", string(r.Body()))
			}
			logger.Errorf("invalid response body, unmarshal json failed, reply:%s, error:%s", r.Body, err.Error())
			return fmt.Errorf("http response err: %v, raw data: %s", err, r.Body())
		}
	} else if r.StatusCode() >= 300 {
		return fmt.Errorf("http request failed: %s", r.Status())
	}
	return nil
}
