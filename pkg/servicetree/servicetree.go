package servicetree

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// AddEntityRequest ...
type AddEntityRequest struct {
	BasePath    string        `json:"base_path,omitempty"`
	Catalog     string        `json:"catalog"`
	SourceType  string        `json:"source_type"`
	ClusterName string        `json:"cluster_name"`
	Entity      []interface{} `json:"entity,omitempty"`
}

// TreeEntity ...
type TreeEntity struct {
	EntityID string            `json:"entity_id,omitempty"`
	Display  string            `json:"display,omitempty"`
	Catalog  string            `json:"catalog,omitempty"`
	Detail   string            `json:"detail,omitempty"`
	Owner    []string          `json:"owner,omitempty"`
	Tag      map[string]string `json:"tag,omitempty"`
	Search   string            `json:"search,omitempty"`
	Paths    []string          `json:"paths,omitempty"`
	RowPath  string            `json:"row_path,omitempty"`
}

// PushServiceTreeHost ...
func PushServiceTreeHost(ctx context.Context, logger *logrus.Logger, list []entity.HostResource) (string, error) {
	sysConf := cfg.GetSystemConfig()
	conf := cfg.GetServiceTreeConfig()
	cli, err := cfg.GetIAMCli(conf.IAMEnv)
	if err != nil {
		return "", err
	}
	m, ok := ctx.Value(constant.OrderCtxMeta).(map[string]string)
	orderUsername := ""
	if ok {
		value, ok2 := m["order_username"]
		if ok2 {
			orderUsername = value
		}
	}
	var token string
	if orderUsername != "" {
		tokenBody, err := cli.CreateToken(ctx, "privilege", orderUsername)
		if err != nil {
			return "", err
		}
		token = tokenBody.Token
	} else {
		token, err = cli.GetSelfToken(ctx)
		if err != nil {
			return "", err
		}
	}

	hostMap := map[string][]*entity.HostResource{}
	for k, host := range list {
		_, ok := hostMap[host.InitTreeNode]
		if !ok {
			hostMap[host.InitTreeNode] = []*entity.HostResource{}
		}
		hostMap[host.InitTreeNode] = append(hostMap[host.InitTreeNode], &list[k])
	}

	for path := range hostMap {
		hosts, err := models.HostModelToPb(ctx, hostMap[path])
		if err != nil {
			logger.Error(err.Error())
		}
		var entities []interface{}
		for _, host := range hosts {
			entities = append(entities, host)
		}

		req := AddEntityRequest{
			BasePath:    path,
			SourceType:  "cloudman",
			Catalog:     "host",
			ClusterName: sysConf.Cluster,
			Entity:      entities,
		}
		err = requestAddEntity(ctx, conf.ServiceTreePrefix, req, token)
		if err != nil {
			logger.Error(err.Error())
		} else {
			logger.Infof("service_tree.pushed, dump: %+v", req)
		}
	}

	return "", nil
}

// PushServiceTreeMysql ...
func PushServiceTreeMysql(ctx context.Context, logger *logrus.Logger, list []entity.MysqlClusterResource) (string, error) {
	sysConf := cfg.GetSystemConfig()
	conf := cfg.GetServiceTreeConfig()
	cli, err := cfg.GetIAMCli(conf.IAMEnv)
	if err != nil {
		return "", err
	}
	m, ok := ctx.Value(constant.OrderCtxMeta).(map[string]string)
	orderUsername := ""
	if ok {
		value, ok2 := m["order_username"]
		if ok2 {
			orderUsername = value
		}
	}
	var token string
	if orderUsername != "" {
		tokenBody, err := cli.CreateToken(ctx, "privilege", orderUsername)
		if err != nil {
			return "", err
		}
		token = tokenBody.Token
	} else {
		token, err = cli.GetSelfToken(ctx)
		if err != nil {
			return "", err
		}
	}

	mysqlMap := map[string][]*entity.MysqlClusterResource{}
	for k, mysql := range list {
		_, ok := mysqlMap[mysql.InitTreeNode]
		if !ok {
			mysqlMap[mysql.InitTreeNode] = []*entity.MysqlClusterResource{}
		}
		mysqlMap[mysql.InitTreeNode] = append(mysqlMap[mysql.InitTreeNode], &list[k])
	}

	for path := range mysqlMap {
		var entities []interface{}
		for _, mysql := range mysqlMap[path] {
			entities = append(entities, models.DBClusterEntityToPB(*mysql))
		}
		req := AddEntityRequest{
			BasePath:    path,
			SourceType:  "cloudman",
			Catalog:     "mysql",
			ClusterName: sysConf.Cluster,
			Entity:      entities,
		}
		err = requestAddEntity(ctx, conf.ServiceTreePrefix, req, token)
		if err != nil {
			logger.Error(err.Error())
		} else {
			logger.Infof("service_tree.pushed, dump: %+v", req)
		}
	}
	return "", nil
}

// PushServiceTreeRedis ...
func PushServiceTreeRedis(ctx context.Context, logger *logrus.Logger, list []entity.RedisResource) (string, error) {
	sysConf := cfg.GetSystemConfig()
	conf := cfg.GetServiceTreeConfig()
	cli, err := cfg.GetIAMCli(conf.IAMEnv)
	if err != nil {
		return "", err
	}
	m, ok := ctx.Value(constant.OrderCtxMeta).(map[string]string)
	orderUsername := ""
	if ok {
		value, ok2 := m["order_username"]
		if ok2 {
			orderUsername = value
		}
	}
	var token string
	if orderUsername != "" {
		tokenBody, err := cli.CreateToken(ctx, "privilege", orderUsername)
		if err != nil {
			return "", err
		}
		token = tokenBody.Token
	} else {
		token, err = cli.GetSelfToken(ctx)
		if err != nil {
			return "", err
		}
	}

	redisMap := map[string][]*entity.RedisResource{}
	for k, redis := range list {
		_, ok := redisMap[redis.InitTreeNode]
		if !ok {
			redisMap[redis.InitTreeNode] = []*entity.RedisResource{}
		}
		redisMap[redis.InitTreeNode] = append(redisMap[redis.InitTreeNode], &list[k])
	}

	for path := range redisMap {
		var entities []interface{}
		for _, redis := range redisMap[path] {
			detail := models.RedisEntityToPb(*redis)
			entities = append(entities, detail)
		}
		req := AddEntityRequest{
			BasePath:    path,
			SourceType:  "cloudman",
			Catalog:     "redis",
			ClusterName: sysConf.Cluster,
			Entity:      entities,
		}
		err = requestAddEntity(ctx, conf.ServiceTreePrefix, req, token)
		if err != nil {
			logger.Error(err.Error())
		} else {
			logger.Infof("service_tree.pushed, dump: %+v", req)
		}
	}
	return "", nil
}

func requestAddEntity(ctx context.Context, prefix string, req interface{}, token string) error {
	reqByte, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("pushservicetreehost.marshal.error:%s", err.Error())
	}
	resBody, code, err := utils.HTTPPost(ctx, prefix+"service-tree/event/add_entity", http.Header{"Authorization": []string{"Bearer " + token}}, reqByte, time.Second*10)
	if err != nil {
		return fmt.Errorf("pushservicetree.post.error:%s", err.Error())
	}
	if code != 200 {
		return fmt.Errorf("pushservicetree.post.errorcode:%d, %s", code, err.Error())
	}
	res := struct {
		Data *cloudman.GetTreeResponse `json:"data"`
	}{}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		return fmt.Errorf("pushservicetree.unmarshal.error:%s", err.Error())
	}
	return nil
}
