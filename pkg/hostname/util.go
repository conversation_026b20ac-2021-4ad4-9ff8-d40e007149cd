package hostname

import (
	"fmt"
	"strings"
	"unicode"
)

func GetHostNameEnv(hostname string) string {
	hostSegs := strings.Split(hostname, "-")
	if len(hostSegs) != 5 {
		return ""
	}
	return hostSegs[1]
}

func GetHostNameRegion(hostname string) string {
	hostSegs := strings.Split(hostname, "-")
	if len(hostSegs) != 5 {
		return ""
	}
	return fmt.Sprintf("%s_%s_%s", hostSegs[1], hostSegs[2], hostSegs[3])
}

func GetHostNameArea(hostname string) string {
	hostSegs := strings.Split(hostname, "-")
	if len(hostSegs) != 5 {
		return ""
	}
	return hostSegs[3]
}

func GetHostNameModule(hostname string) string {
	hostSegs := strings.Split(hostname, "-")
	if len(hostSegs) != 5 {
		return ""
	}
	moduleNum := hostSegs[4]
	module := strings.TrimRightFunc(moduleNum, func(r rune) bool {
		return unicode.IsNumber(r)
	})
	if module == "" {
		module = "all"
	}
	return module
}

func GetHostNameRegionModule(hostname string) string {
	return fmt.Sprintf("%s_%s", GetHostNameRegion(hostname), GetHostNameModule(hostname))
}

func GetHostNameNum(hostname string) string {
	hostSegs := strings.Split(hostname, "-")
	if len(hostSegs) != 5 {
		return ""
	}
	moduleNum := hostSegs[4]
	num := strings.TrimLeftFunc(moduleNum, func(r rune) bool {
		return unicode.IsLetter(r)
	})
	return num
}

func GetLbNameEnv(lbName string) string {
	lbSegs := strings.Split(lbName, "-")
	if len(lbSegs) != 6 {
		return ""
	}
	return lbSegs[1]
}

func GetLbNameRegion(lbName string) string {
	lbSegs := strings.Split(lbName, "-")
	if len(lbSegs) != 6 {
		return ""
	}
	return fmt.Sprintf("%s_%s_%s", lbSegs[1], lbSegs[2], lbSegs[3])
}

func GetLbNameArea(lbName string) string {
	lbSegs := strings.Split(lbName, "-")
	if len(lbSegs) != 6 {
		return ""
	}
	return lbSegs[3]
}

func GetLbNameModule(lbName string) string {
	lbSegs := strings.Split(lbName, "-")
	if len(lbSegs) != 6 {
		return ""
	}
	moduleNum := lbSegs[4]
	module := strings.TrimRightFunc(moduleNum, func(r rune) bool {
		return unicode.IsNumber(r)
	})
	if module == "" {
		module = "all"
	}
	return module
}

func GetLbNameRegionModule(lbName string) string {
	return fmt.Sprintf("%s_%s", GetLbNameRegion(lbName), GetLbNameModule(lbName))
}

func GetLbNameNum(lbName string) string {
	lbSegs := strings.Split(lbName, "-")
	if len(lbSegs) != 6 {
		return ""
	}
	moduleNum := lbSegs[4]
	num := strings.TrimLeftFunc(moduleNum, func(r rune) bool {
		return unicode.IsLetter(r)
	})
	return num
}
