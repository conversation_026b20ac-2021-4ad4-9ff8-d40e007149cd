package hotwheel

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

func (client *HotWheelClient) getHeader() http.Header {
	tokenHeader := http.Header{}
	tokenHeader.Add("Authorization", "Bearer "+client.Token)
	tokenHeader.Add("Content-Type", "application/json")
	return tokenHeader
}

func (client *HotWheelClient) StartTask(ctx context.Context, templateID int64, taskName string, params map[string]interface{}) (string, error) {
	url := fmt.Sprintf("%s/api/v1/taskflow/createAndExec", client.URI)

	constants, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	dataReq := &CreateTaskFromPipeIDReq{
		PipelineID: templateID,
		Name:       taskName,
		Desc:       "created by op-cloudman",
		Constants:  string(constants),
	}
	body, err := json.Marshal(dataReq)
	if err != nil {
		return "", err
	}

	resp, code, err := utils.HTTPPost(ctx, url, client.getHeader(), body, 5*time.Second)
	if err != nil {
		return "", err
	}
	if code != http.StatusOK {
		return "", fmt.Errorf("start task failed")
	}
	idResp := BaseResponse[IDReply]{}
	err = json.Unmarshal(resp, &idResp)
	if err != nil {
		return "", err
	}
	if idResp.Retcode != 0 {
		return "", fmt.Errorf("start task failed, error_msg: %s", idResp.Message)
	}
	return idResp.Data.Id, nil
}

func (client *HotWheelClient) GetTaskStatus(ctx context.Context, taskID string) (taskStatus TaskStatus, err error) {
	url := fmt.Sprintf("%s/api/v1/taskflow/getstatus", client.URI)

	queryMap := map[string]string{
		"id": taskID,
	}

	timeout := 10 * time.Second
	err = common.Retry(timeout, func() *common.RetryError {
		resp, code, err := utils.HTTPGet(ctx, url, client.getHeader(), queryMap, 5*time.Second)
		if err != nil {
			return common.RetryableError(err)
		}
		if code != http.StatusOK {
			return common.RetryableError(fmt.Errorf("get task status failed"))
		}
		statusResp := BaseResponse[TaskFlowStatus]{}
		err = json.Unmarshal(resp, &statusResp)
		if err != nil {
			return common.NonRetryableError(err)
		}
		if statusResp.Retcode != 0 {
			return common.RetryableError(fmt.Errorf("get task status failed, error_msg: %s", statusResp.Message))
		}
		taskStatus = statusResp.Data.Status
		return nil
	})
	if err != nil {
		return "", err
	}
	return taskStatus, nil
}

func (client *HotWheelClient) GetTaskStatusDetail(ctx context.Context, taskID string) (*TaskFlowStatusDetail, error) {
	url := fmt.Sprintf("%s/api/v1/taskflow/getstatus", client.URI)

	queryMap := map[string]string{
		"id": taskID,
	}

	timeout := 10 * time.Second
	detail := &TaskFlowStatusDetail{}
	err := common.Retry(timeout, func() *common.RetryError {
		resp, code, err := utils.HTTPGet(ctx, url, client.getHeader(), queryMap, 5*time.Second)
		if err != nil {
			return common.RetryableError(err)
		}
		if code != http.StatusOK {
			return common.RetryableError(fmt.Errorf("get task status failed"))
		}
		statusResp := BaseResponse[TaskFlowStatusDetail]{}
		err = json.Unmarshal(resp, &statusResp)
		if err != nil {
			return common.NonRetryableError(err)
		}
		if statusResp.Retcode != 0 {
			return common.RetryableError(fmt.Errorf("get task status failed, error_msg: %s", statusResp.Message))
		}
		detail = &statusResp.Data
		return nil
	})
	if err != nil {
		return nil, err
	}
	return detail, nil
}

func (client *HotWheelClient) GetPipelines(ctx context.Context, cateID int64) ([]*PipelineListData, error) {
	url := fmt.Sprintf("%s/api/v1/pipeline/list", client.URI)

	queryMap := map[string]string{
		"page":     "1",
		"pageSize": "9999",
		"common":   "0",
	}
	if cateID != 0 {
		queryMap["pipelineCateID"] = fmt.Sprintf("%d", cateID)
	}

	resp, code, err := utils.HTTPGet(ctx, url, client.getHeader(), queryMap, 5*time.Second)
	if err != nil {
		return nil, err
	}
	if code != http.StatusOK {
		return nil, fmt.Errorf("get pipelines failed, http_status: %d", code)
	}
	listResp := BaseResponse[PipelineList]{}
	err = json.Unmarshal(resp, &listResp)
	if err != nil {
		return nil, err
	}
	if listResp.Retcode != 0 {
		return nil, fmt.Errorf("get pipelines failed, error_msg: %s", listResp.Message)
	}
	return listResp.Data.List, nil
}

func (client HotWheelClient) RetryNode(ctx context.Context, req *RetryNodeReq) (*BaseResponse[MsgReply], error) {
	url := fmt.Sprintf("%s/api/v1/taskflow/retrynode", client.URI)
	body, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	resp, code, err := utils.HTTPPut(ctx, url, client.getHeader(), body, 5*time.Second)
	if err != nil {
		return nil, err
	}
	if code != http.StatusOK {
		return nil, fmt.Errorf("retry node failed, resp: %s", resp)
	}
	msgResp := BaseResponse[MsgReply]{}
	err = json.Unmarshal(resp, &msgResp)
	if err != nil {
		return nil, err
	}
	if msgResp.Retcode != 0 {
		return nil, fmt.Errorf("start task failed, error_msg: %s", msgResp.Message)
	}
	return &msgResp, nil
}
