package hotwheel

type CreateTaskFromPipeIDReq struct {
	Name       string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Desc       string `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`              // 描述
	PipelineID int64  `protobuf:"varint,3,opt,name=pipelineID,proto3" json:"pipelineID,omitempty"` // 关联流程id
	//LightProjectID int64  `protobuf:"varint,4,opt,name=lightProjectID,proto3" json:"lightProjectID,omitempty"` // 轻应用id
	//ScheduleID     int64  `protobuf:"varint,5,opt,name=scheduleID,proto3" json:"scheduleID,omitempty"`         // 执行方案id
	//GrayID         int64  `protobuf:"varint,6,opt,name=grayID,proto3" json:"grayID,omitempty"`                 // 灰度方案id
	Constants string `protobuf:"bytes,7,opt,name=constants,proto3" json:"constants,omitempty"` // 任务参数, map[string]interface{}格式转json string传入
	//CallbackURL    string `protobuf:"bytes,8,opt,name=callbackURL,proto3" json:"callbackURL,omitempty"`        // 回调地址,每次任务状态变更时触发post请求,发送任务状态信息
}

type BaseResponse[E any] struct {
	Retcode int    `json:"retcode,omitempty"`
	Message string `json:"message,omitempty"`
	Data    E      `json:"data,omitempty"`
	Msg     string `json:"msg,omitempty" redis:"msg"`
	Count   int    `json:"count,omitempty" redis:"count"`
}

type IDReply struct {
	Id  string `json:"id,omitempty"`
	Msg string `json:"msg,omitempty"`
}

type MsgReply struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

type TaskFlowStatus struct {
	Status TaskStatus `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"` // 流程状态
}
type TaskNode struct {
	Uid        string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`       // 任务节点唯一id 用于查看日志的ID
	NodeID     string `protobuf:"bytes,2,opt,name=nodeID,proto3" json:"nodeID,omitempty"` // 流程大json中节点id标识
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Status     string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`          // 节点状态
	Errmsg     string `protobuf:"bytes,5,opt,name=errmsg,proto3" json:"errmsg,omitempty"`          // 节点执行错误信息
	TaskFlowID string `protobuf:"varint,6,opt,name=taskFlowID,proto3" json:"taskFlowID,omitempty"` // 关联任务流id
	ExecNum    int32  `protobuf:"varint,7,opt,name=execNum,proto3" json:"execNum,omitempty"`       // 执行次数
	FailSkip   bool   `protobuf:"varint,8,opt,name=failSkip,proto3" json:"failSkip,omitempty"`     // 失败跳过标记
	Type       string `protobuf:"bytes,9,opt,name=type,proto3" json:"type,omitempty"`              // 节点类型
	StartTime  string `protobuf:"bytes,10,opt,name=startTime,proto3" json:"startTime,omitempty"`   // 执行开始时间
	EndTime    string `protobuf:"bytes,11,opt,name=endTime,proto3" json:"endTime,omitempty"`       // 执行结束时间
}
type TaskFlowStatusDetail struct {
	Id         string               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name       string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status     string               `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`                                                                                             // 流程状态
	StartTime  string               `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`                                                                                       // 开始时间
	EndTime    string               `protobuf:"bytes,5,opt,name=endTime,proto3" json:"endTime,omitempty"`                                                                                           // 结束时间
	Type       string               `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`                                                                                                 // 任务类型(单次/周期)
	Children   map[string]*TaskNode `protobuf:"bytes,7,rep,name=children,proto3" json:"children,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 流程节点执行状态
	CreateUser string               `protobuf:"bytes,8,opt,name=createUser,proto3" json:"createUser,omitempty"`
	ExecUser   string               `protobuf:"bytes,9,opt,name=execUser,proto3" json:"execUser,omitempty"`       // 执行人
	PipelineID string               `protobuf:"varint,10,opt,name=pipelineID,proto3" json:"pipelineID,omitempty"` // 关联流程id
	Varmapstr  string               `protobuf:"bytes,12,opt,name=varmapstr,proto3" json:"varmapstr,omitempty"`    // 变量参数json字符串
}

type TaskStatus string

const (
	TaskStatusSuccess TaskStatus = "SUCCESS"
	TaskStatusFailure TaskStatus = "FAILURE"
	TaskStatusRunning TaskStatus = "RUNNING"
	TaskStatusPending TaskStatus = "PENDING"
	TaskStatusRevoke  TaskStatus = "REVOKE"
	TaskStatusSkip    TaskStatus = "SKIP"
)

func (ts TaskStatus) IsFinish() bool {
	return ts == TaskStatusSuccess || ts == TaskStatusFailure || ts == TaskStatusRevoke || ts == TaskStatusSkip
}
func (ts TaskStatus) IsSuccess() bool {
	return ts == TaskStatusSuccess
}

type PipelineList struct {
	Count string              `json:"count"`
	List  []*PipelineListData `json:"list"`
}

type PipelineListData struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type PipelineParam struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type RetryNodeReq struct {
	Uid      string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`           // 节点uid
	Inputstr string `protobuf:"bytes,2,opt,name=inputstr,proto3" json:"inputstr,omitempty"` // 输入参数json字符串
	Argcopy  bool   `protobuf:"varint,3,opt,name=argcopy,proto3" json:"argcopy,omitempty"`  // 参数复制,传入inputstr为空值的参数自动填入第一次执行参数
}
