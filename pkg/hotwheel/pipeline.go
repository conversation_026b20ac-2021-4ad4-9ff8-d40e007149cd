package hotwheel

import (
	"context"
	"fmt"
	"strconv"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

type HotWheelClient struct {
	URI   string // 标准运维地址
	Token string // 请求hotwheel使用的token
}

func NewClient(uri, token string) *HotWheelClient {
	return &HotWheelClient{
		URI:   uri,
		Token: token,
	}
}

func (client *HotWheelClient) StartPipeline(ctx context.Context, pipelineID, taskName string, jumpserver *entity.Jumpserver, params []*PipelineParam) (string, error) {
	reqParams := map[string]interface{}{
		"${instance}": map[string]interface{}{
			"type": "manual",
			"value": []map[string]string{{
				"Host":    jumpserver.Host,
				"AgentID": jumpserver.AgentID,
			}},
		},
	}
	for _, p := range params {
		reqParams[fmt.Sprintf("${%s}", p.Key)] = p.Value
	}

	templateID, err := strconv.ParseInt(pipelineID, 0, 64)
	if err != nil {
		return "", err
	}
	return client.StartTask(ctx, templateID, taskName, reqParams)
}

func (client *HotWheelClient) RetryPipeline(ctx context.Context, taskID string) error {
	statusResp, err := client.GetTaskStatusDetail(ctx, taskID)
	if err != nil {
		return err
	}
	if statusResp.Status == "SUCCESS" {
		return fmt.Errorf("the pipeline cannot retry")
	}
	childUid := ""
	for _, child := range statusResp.Children {
		if child.Status != "FAILURE" {
			continue
		}
		childUid = child.Uid
		break
	}
	if childUid == "" {
		return fmt.Errorf("没有找到失败的节点")
	}

	_, err = client.RetryNode(ctx, &RetryNodeReq{
		Uid:     childUid,
		Argcopy: true,
	})
	return err
}
