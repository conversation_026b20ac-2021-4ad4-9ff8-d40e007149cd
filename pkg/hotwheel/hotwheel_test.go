package hotwheel

import (
	"context"
	"fmt"
	"testing"
)

func TestGetTaskStatus(t *testing.T) {
	client := HotWheelClient{
		URI:   "https://ops.juequling.com/backend/hotwheel/hotwheel",
		Token: "",
	}
	status, err := client.GetTaskStatus(context.Background(), "47680")
	fmt.Println(status, err)
}

func TestRetryPipeline(t *testing.T) {
	client := HotWheelClient{
		URI:   "https://ops-test.juequling.com/backend/hotwheel/hotwheel",
		Token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoibG9naW4iLCJjcmVhdGVfdXNlciI6InlpLnhpZSIsImV4cCI6MTc2NjExNDQzMiwiaWF0IjoxNzY1NTA5NjMyLCJuYmYiOjE3NjU1MDk2MzIsInN1YiI6InlpLnhpZSJ9.Jz6o4bpahJ0JGqR-YY3pHxWFZh2eTVyc3zrc2m3dF1Q",
	}
	err := client.RetryPipeline(context.Background(), "528317")
	fmt.Println(err)
}
