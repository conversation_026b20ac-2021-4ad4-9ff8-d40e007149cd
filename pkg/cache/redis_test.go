package cache

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLock(t *testing.T) {
	ctx := context.Background()
	lockTime := 5
	lockKey := "test-key1"
	err := TryLock(ctx, lockKey, lockTime)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	err = TryLock(ctx, lockKey, lockTime)
	assert.NotNil(t, err, "lock failed")
	err = UnLock(ctx, lockKey)
	if err != nil {
		t.<PERSON>al(err)
	}
	err = TryLock(ctx, lockKey, lockTime)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
}
