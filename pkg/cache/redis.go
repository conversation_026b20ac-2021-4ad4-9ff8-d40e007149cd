package cache

import (
	"context"
	"sync"
	"time"

	goredislib "github.com/go-redis/redis"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
)

var (
	// 全局Redis客户端实例
	redisClient *goredislib.Client
	clientOnce  sync.Once
)

// GetClient 获取redis client（单例模式）
func GetClient() *goredislib.Client {
	clientOnce.Do(func() {
		systemConfig := cfg.GetSystemConfig()
		redisClient = goredislib.NewClient(&goredislib.Options{
			Addr:         systemConfig.Redis.Addr,
			Password:     systemConfig.Redis.Password,
			PoolSize:     50,              // 连接池大小
			MinIdleConns: 10,              // 最小空闲连接数
			IdleTimeout:  5 * time.Minute, // 空闲连接超时
			MaxRetries:   3,               // 最大重试次数
		})
	})
	return redisClient
}

// CloseClient 关闭Redis连接
func CloseClient() error {
	if redisClient != nil {
		return redisClient.Close()
	}
	return nil
}

func TryLock(ctx context.Context, key string, expire int) error {
	client := GetClient()
	pool := goredis.NewPool(client)
	rs := redsync.New(pool)
	mutexLock := rs.NewMutex(key, redsync.WithExpiry(time.Duration(expire)*time.Second))
	return mutexLock.TryLock()
}

func UnLock(ctx context.Context, key string) error {
	client := GetClient()
	return client.Del(key).Err()
}
