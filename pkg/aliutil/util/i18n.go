package util

// I18n 国际化
type I18n struct {
	CN string
}

// RegionI18n aliyun各个区域的名称和代码,https://help.aliyun.com/document_detail/40654.html
var RegionI18n = map[string]I18n{
	// 中国
	"cn-qingdao":     {"华北 1（青岛）"},
	"cn-beijing":     {"华北 2（北京）"},
	"cn-zhangjiakou": {"华北 3（张家口）"},
	"cn-huhehaote":   {"华北 5（呼和浩特）"},
	"cn-wulanchabu":  {"华北 6（乌兰察布）"},
	"cn-hangzhou":    {"华东 1（杭州）"},
	"cn-shanghai":    {"华东 2（上海）"},
	"cn-shenzhen":    {"华南 1（深圳）"},
	"cn-heyuan":      {"华南 2（河源）"},
	"cn-guangzhou":   {"华南 3（广州）"},
	"cn-chengdu":     {"西南 1（成都）"},
	"cn-nanjing":     {"华东 5（南京）"},
	// 其他国家和地区
	"cn-hongkong":    {"中国（香港）"},
	"ap-southeast-1": {"新加坡（新加坡）"},
	"ap-southeast-2": {"澳大利亚（悉尼）"},
	"ap-southeast-3": {"马来西亚（吉隆坡）"},
	"ap-southeast-5": {"印度尼西亚（雅加达）"},
	"ap-southeast-6": {"菲律宾（马尼拉）"},
	"ap-south-1":     {"印度（孟买）"},
	"ap-northeast-1": {"日本（东京）"},
	"us-west-1":      {"美国（硅谷）"},
	"us-east-1":      {"美国（弗吉尼亚）"},
	"eu-central-1":   {"德国（法兰克福）"},
	"eu-west-1":      {"英国（伦敦）"},
	"me-east-1":      {"阿联酋（迪拜）"},
}

// GetRegionCN 当您使用没有区域的终端节点时，AWS将 Amazon EC2 请求路由到美国东部（弗吉尼亚北部）（us-east-1），该区域是 API 调用的默认区域
func GetRegionCN(regionID string) I18n {
	if region, ok := RegionI18n[regionID]; ok {
		return region
	}

	return I18n{"华东 2（上海）"}
}
