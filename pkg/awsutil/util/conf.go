package util

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
)

// GetConfig 获取AWS config
func GetConfig(ctx context.Context, regionID,accessKey,accessSecret string) (cfg aws.Config, err error) {
	return config.LoadDefaultConfig(ctx,config.WithRegion(regionID), config.WithCredentialsProvider(
		credentials.NewStaticCredentialsProvider( accessKey, accessSecret, "")))
}
