package util

import "fmt"

// I18n 国际化
type I18n struct {
	CN string
}

// RegionI18n aws各个区域的名称和代码,https://docs.aws.amazon.com/zh_cn/general/latest/gr/rande.html#view-service-endpoints
var RegionI18n = map[string]I18n{
	"us-east-2":      {"US East (Ohio)"},
	"us-east-1":      {"US East (N. Virginia)"},
	"us-west-1":      {"US West (N. California)"},
	"us-west-2":      {"US West (Oregon)"},
	"af-south-1":     {"Africa (Cape Town)"},
	"ap-east-1":      {"亚太地区（香港）"},
	"ap-south-1":     {"亚太地区（孟买）"},
	"ap-northeast-3": {"亚太地区（大阪）"},
	"ap-northeast-2": {"亚太地区（首尔）"},
	"ap-southeast-1": {"亚太地区（新加坡）"},
	"ap-southeast-2": {"亚太地区（悉尼）"},
	"ap-northeast-1": {"亚太地区（东京）"},
	"ca-central-1":   {"加拿大（中部）"},
	"cn-north-1":     {"中国（北京）"},
	"cn-northwest-1": {"中国（宁夏）"},
	"eu-central-1":   {"欧洲（法兰克福）"},
	"eu-west-1":      {"欧洲（爱尔兰）"},
	"eu-west-2":      {"欧洲（伦敦）"},
	"eu-south-1":     {"欧洲（米兰）"},
	"eu-west-3":      {"欧洲（巴黎）"},
	"eu-north-1":     {"欧洲（斯德哥尔摩）"},
	"me-south-1":     {"中东（巴林）"},
	"sa-east-1":      {"南美洲（圣保罗）"},
}

// GetRegionCN 当您使用没有区域的终端节点时，AWS将 Amazon EC2 请求路由到美国东部（弗吉尼亚北部）（us-east-1），该区域是 API 调用的默认区域
func GetRegionCN(regionID string) I18n {
	if region, ok := RegionI18n[regionID]; ok {
		return region
	}

	return I18n{fmt.Sprintf("未知地区(%s)", regionID)}
}
