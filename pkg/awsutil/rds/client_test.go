package rds

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/service/rds"
	"testing"
)

func TestMysqlClient(t *testing.T) {
	ctx := context.TODO()
	client, err := MysqlClient(ctx, "ap-southeast-1", "xxx", "xxxx")
	if err != nil {
		panic(err)
	}
	dbinst := "aws-pjsh-test-ardb-ap-dispat-instance-1"
	out, err := client.DescribeDBInstances(ctx, &rds.DescribeDBInstancesInput{
		DBInstanceIdentifier: &dbinst,
	})
	if err != nil {
		panic(err)
	}
	for _, inst := range out.DBInstances {
		fmt.Println(*inst.DBInstanceIdentifier, inst.DBParameterGroups)
		for _, g := range inst.DBParameterGroups {
			fmt.Println("   ", *g.DBParameterGroupName)
		}

	}

}
