package rds

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/service/rds"
	"github.com/aws/aws-sdk-go/aws"
)

// CreateClusterSnapshot 创建集群镜像
func CreateClusterSnapshot(rdsCli rds.Client) {
	// 可通过tag对标示备份名称/备份名称
	rdsCli.CreateDBClusterSnapshot(context.Background(), &rds.CreateDBClusterSnapshotInput{
		DBClusterIdentifier:         nil, // dbCluster名称
		DBClusterSnapshotIdentifier: nil, // dbCluster快照名称
		Tags:                        nil,
	})

	// 可根据tag查看是否备份结束
	rdsCli.DescribeDBClusterSnapshots(context.Background(), &rds.DescribeDBClusterSnapshotsInput{
		DBClusterIdentifier:         nil,
		DBClusterSnapshotIdentifier: nil,
		Filters:                     nil,
		IncludePublic:               aws.Bool(false),
		IncludeShared:               aws.<PERSON><PERSON>(false),
		Marker:                      nil,
		MaxRecords:                  nil,
		SnapshotType:                nil,
	})
}
