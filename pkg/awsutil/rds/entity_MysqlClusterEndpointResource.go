package rds

import "github.com/aws/aws-sdk-go-v2/service/rds/types"

//type MysqlClusterEndpointResource struct {
//	Model                 `bson:",inline"`
//	IsLock                bool                       `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
//	IspID                 string                     `bson:"isp_id" json:"isp_id,omitempty" field:"云厂商ID"`
//	IspType               string                     `bson:"isp_type" json:"isp_type,omitempty" field:"云厂商类型"`




//	DBEndpointID          string                     `bson:"DBEndpointId" json:"DBEndpointId,omitempty" xml:"DBEndpointId,omitempty" field:"连接地址ID"`
//	EndpointConfig        string                     `bson:"EndpointConfig" json:"EndpointConfig,omitempty" xml:"EndpointConfig,omitempty" field:"高级配置"`
//	DBEndpointDescription string                     `bson:"DBEndpointDescription" json:"DBEndpointDescription,omitempty" xml:"DBEndpointDescription,omitempty" field:"自定义集群地址名称"`
//	EndpointType          string                     `bson:"EndpointType" json:"EndpointType,omitempty" xml:"EndpointType,omitempty" field:"集群地址类型"`
//	AutoAddNewNodes       string                     `bson:"AutoAddNewNodes" json:"AutoAddNewNodes,omitempty" xml:"AutoAddNewNodes,omitempty" field:"是否自动加入默认集群地址"`
//}


// DBClusterIDFromEndpoint           string                     `bson:"DBClusterID" json:"DBClusterID,omitempty" xml:"DBClusterID,omitempty" field:"所属集群ID"`
func DBClusterIDFromEndpoint(ce types.DBClusterEndpoint) string {
	return *ce.DBClusterIdentifier
}

// NodeWithRoles         string                     `bson:"NodeWithRoles" json:"NodeWithRoles,omitempty" xml:"NodeWithRoles,omitempty" field:"角色名称"`
func NodeWithRoles(ce types.DBClusterEndpoint) string {
	return ""
}


//	Nodes                 string                     `bson:"Nodes" json:"Nodes,omitempty" xml:"Nodes,omitempty" field:"节点列表"`

// ReadWriteMode         string                     `bson:"ReadWriteMode" json:"ReadWriteMode,omitempty" xml:"ReadWriteMode,omitempty" field:"读写模式"`
func ReadWriteMode(ce types.DBClusterEndpoint) string {
	return *ce.EndpointType
}


//	AddressItems          []MysqlClusterAddressItems `bson:"AddressItems" json:"AddressItems,omitempty" xml:"AddressItems,omitempty" type:"Repeated" field:"集群item"`