package rds

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	//"github.com/aws/aws-sdk-go-v2/service/rds"
	"github.com/aws/aws-sdk-go-v2/service/rds/types"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

//type MysqlClusterResource struct {
//	Model                `bson:",inline"`
//	IsLock               bool                `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
//	CreateTime           string              `bson:"CreateTime" json:"CreateTime,omitempty" xml:"CreateTime,omitempty" field:"创建时间"`
//	LockMode             string              `bson:"LockMode" json:"LockMode,omitempty" xml:"LockMode,omitempty" field:"集群的锁定状态"`
//	ResourceGroupID      string              `bson:"ResourceGroupId" json:"ResourceGroupId,omitempty" xml:"ResourceGroupId,omitempty" field:"资源组ID"`
//}

// ExpireTime           string              `bson:"ExpireTime" json:"ExpireTime,omitempty" xml:"ExpireTime,omitempty" field:"集群到期时间"`

// Expired              string              `bson:"Expired" json:"Expired,omitempty" xml:"Expired,omitempty" field:"集群是否到期"`
func Expired() string {
	return "false"
}

// DeletionLock         int32               `bson:"DeletionLock" json:"DeletionLock,omitempty" xml:"DeletionLock,omitempty" field:"集群删除的保护状态"`
func DeletionLock(c types.DBCluster) int32 {
	if *c.DeletionProtection {
		return 1
	}
	return 0
}

// DBType DBType
func DBType() string {
	return "MySQL"
}

// Tags                 MysqlClusterTags    `bson:"Tags" json:"Tags,omitempty" xml:"Tags,omitempty" type:"Struct" field:"标签集合"`
func Tags(c types.DBCluster) entity.MysqlClusterTags {
	var tags []entity.Tag
	for _, tag := range c.TagList {
		tags = append(tags, entity.Tag{
			TagKey:   *tag.Key,
			TagValue: *tag.Value,
		})
	}
	return entity.MysqlClusterTags{
		Tag: tags,
	}
}

// StorageUsed          int64               `bson:"StorageUsed" json:"StorageUsed,omitempty" xml:"StorageUsed,omitempty" field:"存储用量"`
func StorageUsed(c types.DBCluster) int64 {
	//// The current capacity of an Aurora Serverless DB cluster. The capacity is 0
	//// (zero) when the cluster is paused. For more information about Aurora Serverless,
	//// see Using Amazon Aurora Serverless
	//// (https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/aurora-serverless.html)
	//// in the Amazon Aurora User Guide.
	//Capacity *int32

	//// For all database engines except Amazon Aurora, AllocatedStorage specifies the
	//// allocated storage size in gibibytes (GiB). For Aurora, AllocatedStorage always
	//// returns 1, because Aurora DB cluster storage size isn't fixed, but instead
	//// automatically adjusts as needed.
	//AllocatedStorage *int32

	return -1
}

// VpcID                string              `bson:"VpcId" json:"VpcId,omitempty" xml:"VpcId,omitempty" field:"专有网络ID"`
func VpcID(cluster types.DBCluster) string {
	if cluster.DBSubnetGroup == nil {
		return ""
	}
	return *cluster.DBSubnetGroup
}

// DBClusterNetworkType -
func DBClusterNetworkType() string {
	return constant.NetworkTypeVPC
}

// DBNodeNumber         int32               `bson:"DBNodeNumber" json:"DBNodeNumber,omitempty" xml:"DBNodeNumber,omitempty" field:"节点数量"`
func DBNodeNumber(cluster types.DBCluster) int32 {
	return int32(len(cluster.DBClusterMembers))
}

// DBNodeClass          string              `bson:"DBNodeClass" json:"DBNodeClass,omitempty" xml:"DBNodeClass,omitempty" field:"节点规格"`
func DBNodeClass(inst types.DBInstance) string {
	if inst.DBInstanceClass == nil {
		return ""
	}
	return *inst.DBInstanceClass
}

// DBInstanceRole DBInstanceRole
func DBInstanceRole(member types.DBClusterMember) string {
	if aws.ToBool(member.IsClusterWriter) {
		return "Writer"
	}
	return "Reader"
}

// DBNode DBNode
func DBNode(inst types.DBInstance, member types.DBClusterMember, regionID string) entity.MysqlClusterDBNode {
	return entity.MysqlClusterDBNode{
		DBNodeClass: *inst.DBInstanceClass,
		ZoneID:      *inst.AvailabilityZone,
		DBNodeRole:  DBInstanceRole(member),
		DBNodeID:    *inst.DBInstanceIdentifier,
		RegionID:    regionID,
	}
}

//	DBNodes              MysqlClusterDBNodes `bson:"DBNodes" json:"DBNodes,omitempty" xml:"DBNodes,omitempty" type:"Struct" field:"集群节点详情"`

// PayType              string              `bson:"PayType" json:"PayType,omitempty" xml:"PayType,omitempty" field:"付费类型"`
func PayType() string {
	return constant.PayOndemand
}

// DBClusterID          string              `bson:"DBClusterId" json:"DBClusterId,omitempty" xml:"DBClusterId,omitempty" field:"集群ID" validate:"required" indexed:"1"`
func DBClusterID(cluster types.DBCluster) string {
	if cluster.DBClusterIdentifier == nil {
		return ""
	}
	return *cluster.DBClusterIdentifier
}

// DBClusterStatus      string              `bson:"DBClusterStatus" json:"DBClusterStatus,omitempty" xml:"DBClusterStatus,omitempty" field:"集群状态"`
func DBClusterStatus(cluster types.DBCluster) string {
	if cluster.Status == nil {
		return ""
	}
	return *cluster.Status
}

// DBVersion            string              `bson:"DBVersion" json:"DBVersion,omitempty" xml:"DBVersion,omitempty" field:"数据库版本" validate:"required"`
func DBVersion(cluster types.DBCluster) string {
	if cluster.EngineVersion == nil {
		return ""
	}
	return *cluster.EngineVersion
}

// DBEndpoint -
func DBEndpoint(cluster types.DBCluster) string {
	if cluster.Endpoint == nil {
		return ""
	}
	return *cluster.Endpoint
}

// DBReaderEndpoint -
func DBReaderEndpoint(cluster types.DBCluster) string {
	if cluster.ReaderEndpoint == nil {
		return ""
	}
	return *cluster.ReaderEndpoint
}

// DBPort -
func DBPort(cluster types.DBCluster) int32 {
	if cluster.Port == nil {
		return 0
	}
	return *cluster.Port
}

// DBClusterDescription string              `bson:"DBClusterDescription" json:"DBClusterDescription,omitempty" xml:"DBClusterDescription,omitempty" field:"集群描述"`
func DBClusterDescription(cluster types.DBCluster) string {
	if cluster.DBClusterIdentifier == nil {
		return ""
	}
	return *cluster.DBClusterIdentifier
}

// Engine               string              `bson:"Engine" json:"Engine,omitempty" xml:"Engine,omitempty" field:"集群引擎" validate:"required"`
func Engine(cluster types.DBCluster) string {
	if cluster.Engine == nil {
		return ""
	}
	return *cluster.Engine
}

// DBVpcSecurityGroups ...
func DBVpcSecurityGroups(cluster types.DBCluster) []string {
	var sgs []string
	for _, mem := range cluster.VpcSecurityGroups {
		sgs = append(sgs, aws.ToString(mem.VpcSecurityGroupId))
	}
	return sgs
}
