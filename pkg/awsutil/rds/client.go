package rds

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/service/rds"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/util"
)

// MysqlClient MysqlClient
func MysqlClient(ctx context.Context, regionID, accessKey, accessSecret string) (*rds.Client, error) {
	cfg, err := util.GetConfig(ctx, regionID, accessKey, accessSecret)
	if err != nil {
		return nil, err
	}
	return rds.NewFromConfig(cfg), nil
}
