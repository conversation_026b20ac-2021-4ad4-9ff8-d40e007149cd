package redis

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/service/elasticache"
)

// CreateBackup 创建aws-elasticache快照
func CreateBackup(cacheCli *elasticache.Client) {
	cacheCli.CreateSnapshot(context.Background(), &elasticache.CreateSnapshotInput{
		SnapshotName:       nil,
		CacheClusterId:     nil,
		KmsKeyId:           nil,
		ReplicationGroupId: nil,
		Tags:               nil,
	})

	cacheCli.DescribeSnapshots(context.Background(), &elasticache.DescribeSnapshotsInput{
		CacheClusterId:      nil,
		Marker:              nil,
		MaxRecords:          nil,
		ReplicationGroupId:  nil,
		ShowNodeGroupConfig: nil,
		SnapshotName:        nil,
		SnapshotSource:      nil,
	})
}
