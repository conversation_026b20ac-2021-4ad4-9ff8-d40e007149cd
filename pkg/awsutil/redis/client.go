package redis

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/util"
)

// Client RedisClient
func Client(ctx context.Context, regionID, accessKey, accessSecret string) (*elasticache.Client, error) {
	cfg, err := util.GetConfig(ctx, regionID, accessKey, accessSecret)
	if err != nil {
		return nil, err
	}
	return elasticache.NewFromConfig(cfg), nil
}
