package redis

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	"testing"
)

func TestClient(t *testing.T) {
	ctx := context.TODO()
	c, err := Client(ctx, "ap-southeast-1", "xxxx", "xxxx")
	if err != nil {
		panic(err)
	}
	flag := true
	co, err := c.DescribeCacheClusters(ctx, &elasticache.DescribeCacheClustersInput{ShowCacheNodeInfo: &flag})
	if err != nil {
		panic(err)
	}
	fmt.Println("Clusters", len(co.CacheClusters))
	for _, clu := range co.CacheClusters {
		if clu.ReplicationGroupId != nil {
			continue
		}
		fmt.Println("ARN", *clu.ARN)
		fmt.Println(*clu.NumCacheNodes)
		fmt.Println(len(clu.CacheNodes))
		fmt.Println(clu.ConfigurationEndpoint)
		fmt.Println(*clu.CacheNodes[0].Endpoint.Address)

	}
	//out, err := c.DescribeReplicationGroups(ctx, &elasticache.DescribeReplicationGroupsInput{})
	//if err != nil {
	//	panic(err)
	//}
	//fmt.Println(len(out.ReplicationGroups))
}
