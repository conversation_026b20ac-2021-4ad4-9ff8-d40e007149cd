package redis

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	"github.com/aws/aws-sdk-go-v2/service/elasticache/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// GetRedisResource ...
func GetRedisResource(ctx context.Context, c types.CacheCluster, ispID, ispType, zoneID string) (*entity.RedisResource, error) {
	var sgIPs []string
	for _, sg := range c.SecurityGroups {
		sgIPs = append(sgIPs, aws.StringValue(sg.SecurityGroupId))
	}
	return &entity.RedisResource{
		ReplacateID:      *c.CacheClusterId,
		ResourceGroupID:  *c.CacheClusterId,
		InstanceID:       *c.Cache<PERSON>lusterId,
		InstanceName:     *c.CacheClusterId,
		InstanceStatus:   *c.CacheClusterStatus,
		Capacity:         Capacity(*c.CacheNodeType),
		Bandwidth:        Bandwidth(*c.CacheNodeType),
		IspID:            ispID,
		IspType:          ispType,
		RegionID:         zoneID,
		ZoneID:           zoneID,
		ChargeType:       ChargeType(),
		NetworkType:      NetworkType(),
		ConnectionDomain: *c.CacheNodes[0].Endpoint.Address,
		Port:             int64(aws.Int32Value(c.CacheNodes[0].Endpoint.Port)),
		SecurityGroupIds: entity.InstanceSecurityGroupIds{SecurityGroupID: sgIPs},
		CreateTime:       c.CacheClusterCreateTime.Format("2006-01-02T15:04:05Z"),
	}, nil
}

// GetRedisArchitectureType 获取redis架构类型
func GetRedisArchitectureType(clusterModel *bool) string {
	if aws.BoolValue(clusterModel) {
		return "cluster"
	}

	return "standard"
}

// GetRedisResourceSlice GetRedisResourceSlice
func GetRedisResourceSlice(ctx context.Context, rg types.ReplicationGroup, ispID, ispType, zoneID string, logging *logrus.Logger) ([]*entity.RedisResource, error) {
	replicateID := *rg.ReplicationGroupId
	var rrs []*entity.RedisResource
	var connectionDomain *string
	var port int32
	if rg.ConfigurationEndpoint != nil {
		connectionDomain = rg.ConfigurationEndpoint.Address
		port = aws.Int32Value(rg.ConfigurationEndpoint.Port)
	} else {
		logging.Errorf("Redis (%s) Configuraiton Endpoint Port is nil\n", replicateID)
	}
	m := new(entity.RedisResource)
	m.ConnectionDomain = aws.StringValue(connectionDomain)
	m.InstanceType = aws.StringValue(rg.Engine)
	m.Port = int64(port)
	m.ReplacateID = replicateID
	m.ResourceGroupID = replicateID
	m.InstanceID = replicateID
	m.InstanceName = replicateID
	m.InstanceStatus = aws.StringValue(rg.Status)
	m.Capacity = Capacity(*rg.CacheNodeType)
	m.Bandwidth = Bandwidth(*rg.CacheNodeType)
	m.InstanceClass = aws.StringValue(rg.CacheNodeType)
	m.IspID = ispID
	m.IspType = ispType
	m.RegionID = zoneID
	m.ZoneID = zoneID
	m.ChargeType = ChargeType()
	m.NetworkType = NetworkType()
	m.ArchitectureType = GetRedisArchitectureType(rg.ClusterEnabled)
	m.CreateTime = rg.ReplicationGroupCreateTime.Format("2006-01-02T15:04:05Z")
	if len(rg.NodeGroups) > 0 {
		ng := rg.NodeGroups[0]
		if ng.PrimaryEndpoint != nil {
			m.ConnectionDomain = aws.StringValue(ng.PrimaryEndpoint.Address)
			m.Port = int64(aws.Int32Value(ng.PrimaryEndpoint.Port))
		}
	}
	rrs = append(rrs, m)
	return rrs, nil
	// vpcID, err := VpcID(ctx, client, cc)
	// if err!=nil {
	//	return nil, err
	// }
	// m.VpcID = vpcID
	//
	// tags, err := Tags(ctx, client, cc)
	// if err!=nil {
	//	return nil, err
	// }
	// m.Tags = tags
}

// ReplacateID string `bson:"ReplacateId" json:"ReplacateId,omitempty" xml:"ReplacateId,omitempty" field:"多活实例的逻辑ID"`
func ReplacateID(cc types.CacheCluster) string {
	return *cc.ReplicationGroupId
}

// SearchKey string `bson:"SearchKey" json:"SearchKey,omitempty" xml:"SearchKey,omitempty" field:"搜索关键字"`
func SearchKey() string {
	return "" // todo: 确定用途
}

// ConnectionDomainAndPort ConnectionDomainAndPort
func ConnectionDomainAndPort(cc types.CacheCluster) (string, int64, error) {
	if len(cc.CacheNodes) != 1 {
		return "", 0, fmt.Errorf("CacheCluster（%s）数量不为1，不合法", *cc.CacheClusterId)
	}

	return *cc.CacheNodes[0].Endpoint.Address, int64(aws.Int32Value(cc.CacheNodes[0].Endpoint.Port)), nil
}

// ConnectionDomain ConnectionDomain
func ConnectionDomain(cc types.CacheCluster) string {
	if cc.ConfigurationEndpoint != nil {
		fmt.Println("ConfigurationEndpoint:", *cc.ConfigurationEndpoint)
	}

	for _, m := range cc.CacheNodes {
		fmt.Println("Node:", *m.CacheNodeId, ", Endpoint:", *m.Endpoint.Address)
	}
	return ""
}

// UserName string            `bson:"UserName" json:"UserName,omitempty" xml:"UserName,omitempty" field:"连接用户名"`
func UserName() string {
	return "" // todo:讨论评估
}

// RegionID            string            `bson:"RegionID" json:"RegionID,omitempty" xml:"RegionID,omitempty" field:"地域ID"`
func RegionID(regionID string) string {
	return regionID
}

// Capacity            int64             `bson:"Capacity" json:"Capacity,omitempty" xml:"Capacity,omitempty" field:"实例容量"`
func Capacity(cacheNodeType string) int64 {
	return constant.CacheMemMap[cacheNodeType]
}

// InstanceClass       string            `bson:"InstanceClass" json:"InstanceClass,omitempty" xml:"InstanceClass,omitempty" field:"实例的规格"`
func InstanceClass(cc types.CacheCluster) string {
	return *cc.CacheNodeType
}

// InstanceType        string            `bson:"InstanceType" json:"InstanceType,omitempty" xml:"InstanceType,omitempty" field:"实例类型"`
func InstanceType(cc types.CacheCluster) string {
	return *cc.Engine
}

// ArchitectureType    string            `bson:"ArchitectureType" json:"ArchitectureType,omitempty" xml:"ArchitectureType,omitempty" field:"架构类型"`
func ArchitectureType() string {
	return "" // aws redis cluster模式划分：ReplicationGroup.ClusterEnable区分
}

//	QPS                 int64             `bson:"QPS" json:"QPS,omitempty" xml:"QPS,omitempty" field:"每秒请求数"`

// Bandwidth           int64             `bson:"Bandwidth" json:"Bandwidth,omitempty" xml:"Bandwidth,omitempty" field:"实例带宽"`
func Bandwidth(nodeType string) int64 {
	return constant.CacheBandwidthMap[nodeType]
}

// Connections         int64             `bson:"Connections" json:"Connections,omitempty" xml:"Connections,omitempty" field:"连接数限制"`

// ZoneID              string            `bson:"ZoneID" json:"ZoneID,omitempty" xml:"ZoneID,omitempty" field:"可用区ID"`
func ZoneID(cc types.CacheCluster) string {
	return *cc.PreferredAvailabilityZone
}

// Config              string            `bson:"Config" json:"Config,omitempty" xml:"Config,omitempty" field:"实例参数设置"`
func Config() string {
	return "" // 能取到，但不值得，待讨论  CacheParameterGroupName ==》 client.DescribeCacheParameters() ==> Parameter list
}

// ChargeType          string            `bson:"ChargeType" json:"ChargeType,omitempty" xml:"ChargeType,omitempty" field:"付费类型"`
func ChargeType() string {
	return constant.PostPaid
}

// NetworkType         string            `bson:"NetworkType" json:"NetworkType,omitempty" xml:"NetworkType,omitempty" field:"网络类型"`
func NetworkType() string {
	return constant.NetworkTypeVPC
}

// VpcID               string            `bson:"VpcId" json:"VpcId,omitempty" xml:"VpcId,omitempty" field:"专有网络ID"`
func VpcID(ctx context.Context, client *elasticache.Client, cc types.CacheCluster) (string, error) {
	out, err := client.DescribeCacheSubnetGroups(ctx, &elasticache.DescribeCacheSubnetGroupsInput{
		CacheSubnetGroupName: cc.CacheSubnetGroupName,
	})
	if err != nil {
		return "", fmt.Errorf("查询CacheSubnetGroup（%s）报错：%v", *cc.CacheSubnetGroupName, err)
	}
	if len(out.CacheSubnetGroups) != 1 {
		return "", fmt.Errorf("查询CacheSubnetGroup（%s）查询结果不为一，不合法", *cc.CacheSubnetGroupName)
	}
	return *out.CacheSubnetGroups[0].VpcId, nil
}

// VSwitchID           string            `bson:"VSwitchId" json:"VSwitchId,omitempty" xml:"VSwitchId,omitempty" field:"虚拟交换机ID"`
// PrivateIP           string            `bson:"PrivateIp" json:"PrivateIp,omitempty" xml:"PrivateIp,omitempty" field:"内网IP地址"`

//	CreateTime          string            `bson:"CreateTime" json:"CreateTime,omitempty" xml:"CreateTime,omitempty" field:"创建时间"`
//	EndTime             string            `bson:"EndTime" json:"EndTime,omitempty" xml:"EndTime,omitempty" field:"到期时间"`

//	HasRenewChangeOrder bool              `bson:"HasRenewChangeOrder" json:"HasRenewChangeOrder,omitempty" xml:"HasRenewChangeOrder,omitempty" field:"是否有未生效订单"`
//	IsRds               bool              `bson:"IsRds" json:"IsRds,omitempty" xml:"IsRds,omitempty" field:"是否属RDS管控"`
//	NodeType            string            `bson:"NodeType" json:"NodeType,omitempty" xml:"NodeType,omitempty" field:"节点类型"`
//	PackageType         string            `bson:"PackageType" json:"PackageType,omitempty" xml:"PackageType,omitempty" field:"套餐类型"`

// EngineVersion       string            `bson:"EngineVersion" json:"EngineVersion,omitempty" xml:"EngineVersion,omitempty" field:"引擎版本"`
func EngineVersion(cc types.CacheCluster) string {
	return *cc.EngineVersion
}

//	DestroyTime         string            `bson:"DestroyTime" json:"DestroyTime,omitempty" xml:"DestroyTime,omitempty" field:"销毁时间"`
//	ConnectionMode      string            `bson:"ConnectionMode" json:"ConnectionMode,omitempty" xml:"ConnectionMode,omitempty" field:"访问模式"`

//	ResourceGroupID     string            `bson:"ResourceGroupId" json:"ResourceGroupId,omitempty" xml:"ResourceGroupId,omitempty" field:"资源组ID"`

// ShardCount          int32             `bson:"ShardCount" json:"ShardCount,omitempty" xml:"ShardCount,omitempty" field:"数据节点数量"`
func ShardCount(cc types.CacheCluster) int32 {
	return *cc.NumCacheNodes
}

// Tags                RedisInstanceTags `bson:"Tags" json:"Tags,omitempty" xml:"Tags,omitempty" type:"Struct" field:"标签集合"`
func Tags(ctx context.Context, client *elasticache.Client, cc types.CacheCluster) (entity.RedisInstanceTags, error) {
	tagOut, err := client.ListTagsForResource(ctx, &elasticache.ListTagsForResourceInput{ResourceName: cc.ARN})
	if err != nil {
		return entity.RedisInstanceTags{}, err
	}
	tagSlice := []entity.RedisInstanceTag{}
	for _, tag := range tagOut.TagList {
		tagSlice = append(tagSlice, entity.RedisInstanceTag{
			Key:   *tag.Key,
			Value: *tag.Value,
		})
	}
	return entity.RedisInstanceTags{
		Tag: tagSlice,
	}, nil
}
