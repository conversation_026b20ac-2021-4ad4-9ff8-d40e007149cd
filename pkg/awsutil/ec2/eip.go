package ec2

import (
	"context"
	"errors"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/cloudwatch"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
)

// GetEipLimit 获取EIP限额
func GetEipLimit(ctx context.Context, region string, accountID string) (int, error) {
	var limit int

	out := &ec2.DescribeAccountAttributesOutput{}
	err := agentsdk.SyncCall(ctx, region, "host", "DescribeAccountAttributes", accountID, []interface{}{"", &ec2.DescribeAccountAttributesInput{
		AttributeNames: []types.AccountAttributeName{
			"vpc-max-elastic-ips",
		},
	}}, out)
	if err != nil {
		logrus.Error("AWS DescribeAccountAttributes ERROR:", err)
		return limit, err
	}

	switch {
	case len(out.AccountAttributes) == 0:
		msg := "AWS 未查找到vpc-max-elastic-ips"
		logrus.Error(msg)
		return limit, errors.New(msg)
	case len(out.AccountAttributes) > 1:
		msg := "AWS vpc-max-elastic-ips限额数量大于1，不合法"
		logrus.Error(msg)
		return limit, errors.New(msg)
	default:
		for _, v := range out.AccountAttributes[0].AttributeValues {
			if v.AttributeValue == nil {
				msg := "AWS vpc-max-elastic-ips限额未配置，不合法"
				logrus.Error(msg)
				return limit, errors.New(msg)
			}
			limitStr := *v.AttributeValue
			val, err := strconv.Atoi(limitStr)
			if err != nil {
				msg := "AWS vpc-max-elastic-ip配额不是整数，不合法"
				logrus.Error(msg)
				return limit, errors.New(msg)
			}
			limit = val
		}
		return limit, nil
	}

}

// ListEIP 列举EIP
func ListEIP(ctx context.Context, region string, accountID string) (int, []types.Address, error) {
	var num int

	out := &ec2.DescribeAddressesOutput{}
	err := agentsdk.SyncCall(ctx, region, "host", "DescribeAddresses", accountID, []interface{}{"", &ec2.DescribeAddressesInput{}}, out)
	if err != nil {
		logrus.Error("List EIP Error:", err)
		return num, nil, err
	}

	num = len(out.Addresses)
	return num, nil, nil
}

type IpamPool struct {
	IpamPoolId      string   `json:"ipam_pool_id"`
	State           string   `json:"state"`
	Cidrs           []string `json:"cidrs"`
	TotalCount      int      `json:"total_count"`
	AssignedPercent float64  `json:"assigned_percent"`
}

func ListIpamPools(ctx context.Context, region string, accountID string) ([]*IpamPool, error) {
	out := &ec2.DescribeIpamPoolsOutput{}
	err := agentsdk.SyncCall(ctx, region, "host", "DescribeIpamPools", accountID, []interface{}{"", &ec2.DescribeIpamPoolsInput{}}, out)
	if err != nil {
		logrus.Error("List IpamPools Error:", err)
		return nil, err
	}

	pools := make([]*IpamPool, 0)
	for _, pool := range out.IpamPools {
		cidrs, err := GetIpamCidrs(ctx, region, accountID, aws.StringValue(pool.IpamPoolId))
		if err != nil {
			continue
		}
		scopeArn := aws.StringValue(pool.IpamScopeArn)
		scopeId := scopeArn[strings.LastIndex(scopeArn, "/")+1:]
		percent, err := GetIpamPoolAssignedPercent(ctx, region, accountID, aws.StringValue(pool.IpamPoolId), scopeId)
		if err != nil {
			continue
		}

		total := 0
		for _, cidr := range cidrs {
			count, err := getCidrCount(cidr)
			if err != nil {
				continue
			}
			total += count
		}
		pools = append(pools, &IpamPool{
			IpamPoolId:      aws.StringValue(pool.IpamPoolId),
			State:           string(pool.State),
			Cidrs:           cidrs,
			TotalCount:      total,
			AssignedPercent: percent,
		})
	}

	return pools, nil
}

func getCidrCount(cidr string) (int, error) {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return 0, err
	}

	ones, bits := ipnet.Mask.Size()
	return 1 << (uint(bits) - uint(ones)), nil
}

func GetIpamCidrs(ctx context.Context, region string, accountID string, ipamPoolId string) ([]string, error) {
	out := &ec2.GetIpamPoolCidrsOutput{}
	err := agentsdk.SyncCall(ctx, region, "host", "GetIpamPoolCidrs", accountID, []interface{}{"", &ec2.GetIpamPoolCidrsInput{
		IpamPoolId: aws.String(ipamPoolId),
	}}, out)
	if err != nil {
		logrus.Error("Get IpamPoolCidrs Error:", err)
		return nil, err
	}
	cidrs := make([]string, 0)
	for _, cidr := range out.IpamPoolCidrs {
		if cidr.State != types.IpamPoolCidrStateProvisioned {
			continue
		}
		cidrs = append(cidrs, aws.StringValue(cidr.Cidr))
	}
	return cidrs, nil
}

func GetIpamPoolAssignedPercent(ctx context.Context, region string, accountID string, ipamPoolId string, scopeId string) (float64, error) {
	startTime := time.Now().Add(-30 * time.Minute)
	endTime := time.Now()
	input := &cloudwatch.GetMetricDataInput{
		StartTime: &startTime,
		EndTime:   &endTime,
		MetricDataQueries: []*cloudwatch.MetricDataQuery{
			{
				MetricStat: &cloudwatch.MetricStat{
					Metric: &cloudwatch.Metric{
						Namespace:  aws.String("AWS/IPAM"),
						MetricName: aws.String("PercentAssigned"),
						Dimensions: []*cloudwatch.Dimension{
							{
								Name:  aws.String("PoolID"),
								Value: aws.String(ipamPoolId),
							},
							{
								Name:  aws.String("AddressFamily"),
								Value: aws.String("IPv4"),
							},
							{
								Name:  aws.String("Locale"),
								Value: aws.String(region),
							},
							{
								Name:  aws.String("ScopeID"),
								Value: aws.String(scopeId),
							},
						},
					},
					Stat:   aws.String("Average"),
					Unit:   aws.String("Percent"),
					Period: aws.Int64(300),
				},
				Id: aws.String("m1"),
			},
		},
	}
	resp := &cloudwatch.GetMetricDataOutput{}
	err := agentsdk.SyncCall(ctx, region, "cloudwatch", "GetMetricData", accountID, []interface{}{input}, resp)
	if err != nil {
		logrus.Error("GetMetricData Error:", err)
		return 0, err
	}

	if len(resp.MetricDataResults) == 0 {
		return 0, nil
	}

	res := resp.MetricDataResults[0]
	if len(res.Values) == 0 {
		return 0, nil
	}

	// 第一个值为最新的值
	return aws.Float64Value(res.Values[0]), nil
}

// AssignEIP 分配EIP
func AssignEIP(ctx context.Context, region string, accountID string, instID *string, ipamPoolId *string) error {
	out := &ec2.AllocateAddressOutput{}
	input := &ec2.AllocateAddressInput{
		IpamPoolId: ipamPoolId,
	}

	err := agentsdk.SyncCall(ctx, region, "host", "AllocateAddress", accountID, []interface{}{"", input}, out)
	if err != nil {
		msg := fmt.Sprintf("AWS AllocateAddress Error: %s", err)
		logrus.Error(msg)
		return err
	}

	iOut := &ec2.AssociateAddressOutput{}
	err = agentsdk.SyncCall(ctx, region, "host", "AssociateAddress", accountID, []interface{}{"", &ec2.AssociateAddressInput{
		AllocationId: out.AllocationId,
		InstanceId:   instID,
	}}, iOut)
	if err != nil {
		msg := fmt.Sprintf("AWS AssociateAddress Error: %s", err)
		logrus.Error(msg)
		return err
	}
	return nil
}
