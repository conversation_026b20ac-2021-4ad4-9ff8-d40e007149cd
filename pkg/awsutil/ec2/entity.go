package ec2

import (
	"fmt"

	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// HostResource string, instTypeInfo types.InstanceTypeInfo, image types.Image, zone types.AvailabilityZone
func HostResource(inst types.Instance) *entity.HostResource {
	fmt.Println(Status(inst), ", ", *inst.State)
	return &entity.HostResource{
		InstanceID:          InstanceID(inst),
		InstanceType:        InstanceType(inst),
		InstanceName:        InstanceName(inst),
		HostName:            HostName(inst),
		ZoneID:              *inst.Placement.AvailabilityZone,
		Status:              Status(inst),
		ImageID:             ImageID(inst),
		SecurityGroupIds:    SecurityGroupIds(inst),
		KeyPairName:         KeyPairName(inst),
		CPU:                 CPU(inst),
		CPUOptions:          CPUOption(inst),
		InstanceNetworkType: InstanceNetworkType(inst),
		NetworkInterfaces:   NetworkInterfaces(inst),
		PublicIPAddress:     PublicIPAddress(inst),
		InnerIPAddress:      InnerIPAddress(inst),
		VpcAttributes:       VpcAttributes(inst),
		StartTime:           StartTime(inst),
		CreationTime:        CreationTime(inst),
		// Tags:                Tags(inst),
		// RegionID: regionID,
		// EipAddress: EipAddress(),
		// Memory:     Memory(instTypeInfo),
	}
	// hr.OSType,hr.OSNameEn,hr.OSName = OsInfo(image)
}

// InstanceID InstanceID
func InstanceID(inst types.Instance) string {
	return *inst.InstanceId
}

const instanceNameTagKey = "Name"

func nameTagValue(inst types.Instance) string {
	name := "undefined"
	for _, tag := range inst.Tags {
		if *tag.Key == instanceNameTagKey {
			name = *tag.Value
			break
		}
	}
	return name
}

// InstanceName InstanceName
func InstanceName(inst types.Instance) string { // HostName = InstanceName
	return nameTagValue(inst)
}

// HostName HostName
func HostName(inst types.Instance) string {
	return nameTagValue(inst)
}

// RegionID string `bson:"RegionID" json:"RegionID,omitempty" xml:"RegionID,omitempty" field:"实例所属地域ID"`
// Region ID

// ZoneID ZoneID
func ZoneID(zone types.AvailabilityZone) string {
	return *zone.ZoneId
}

// Status Status
func Status(inst types.Instance) string {
	return formatStatus(inst.State)
}
func formatStatus(state *types.InstanceState) string {
	// Todo: 定义状态转化标准
	switch state.Name {
	case types.InstanceStateNameRunning:
		return constant.ResourceRunning
	case types.InstanceStateNamePending:
		return constant.ResourcePending
	default:
		return constant.ResourceStopped
	}
}

// ImageID ImageID
func ImageID(inst types.Instance) string {
	return *inst.ImageId
}

// InstanceType 实例类型、规格
func InstanceType(inst types.Instance) string {
	return string(inst.InstanceType)
}

// SecurityGroupIds 安全组
func SecurityGroupIds(inst types.Instance) entity.InstanceSecurityGroupIds {
	var sgIDSlice []string
	for _, sg := range inst.SecurityGroups {
		sgIDSlice = append(sgIDSlice, *sg.GroupId)
	}
	return entity.InstanceSecurityGroupIds{
		SecurityGroupID: sgIDSlice,
	}
}

// KeyPairName string `bson:"KeyPairName" json:"KeyPairName,omitempty" xml:"KeyPairName,omitempty" field:"密钥对名称"`
func KeyPairName(inst types.Instance) string {
	if inst.KeyName == nil {
		return ""
	}
	return *inst.KeyName
}

// CPU int32 `bson:"Cpu" json:"Cpu,omitempty" xml:"Cpu,omitempty" field:"vCPU数" validate:"required"`
func CPU(inst types.Instance) int32 {
	coreCount := *inst.CpuOptions.CoreCount
	threadPerCore := *inst.CpuOptions.ThreadsPerCore
	return coreCount * threadPerCore
}

// CPUOption InstanceCPUOptions `bson:"CpuOptions" json:"CpuOptions,omitempty" xml:"CpuOptions,omitempty" type:"Struct" field:"CPU配置详情"`
func CPUOption(inst types.Instance) entity.InstanceCPUOptions {
	return entity.InstanceCPUOptions{
		CoreCount:      *inst.CpuOptions.CoreCount,
		ThreadsPerCore: *inst.CpuOptions.ThreadsPerCore,
	}
}

// Memory int32 `bson:"Memory" json:"Memory,omitempty" xml:"Memory,omitempty" field:"内存大小(MiB)" validate:"required"`
func Memory(instType types.InstanceTypeInfo) int32 {
	val := *instType.MemoryInfo.SizeInMiB
	return int32(val)
}

// InstanceNetworkType string `bson:"InstanceNetworkType" json:"InstanceNetworkType,omitempty" xml:"InstanceNetworkType,omitempty" field:"实例网络类型"`
func InstanceNetworkType(inst types.Instance) string {
	// 经确认，AWS EC2的网络类型均为VPC
	return constant.NetworkTypeVPC
}

// NetworkInterfaces InstanceNetworkInterfaces `bson:"NetworkInterfaces" json:"NetworkInterfaces,omitempty" xml:"NetworkInterfaces,omitempty" type:"Struct" field:"实例弹性网卡属性"`
func NetworkInterfaces(inst types.Instance) entity.InstanceNetworkInterfaces {
	var ms []entity.InstanceNetworkInterface
	for _, nt := range inst.NetworkInterfaces {
		var ipv6Sets []entity.InterfaceIpv6SetsIpv6Set
		for _, v := range nt.Ipv6Addresses {
			ipv6Sets = append(ipv6Sets, entity.InterfaceIpv6SetsIpv6Set{Ipv6Address: *v.Ipv6Address})
		}
		var ipSets []entity.InterfacePrivateIPSetsPrivateIPSet
		for _, v := range nt.PrivateIpAddresses {
			ipSets = append(ipSets, entity.InterfacePrivateIPSetsPrivateIPSet{
				Primary:          *v.Primary,
				PrivateIPAddress: *v.PrivateIpAddress,
			})
		}
		ms = append(ms, entity.InstanceNetworkInterface{
			Type:               *nt.InterfaceType,
			MacAddress:         *nt.MacAddress,
			NetworkInterfaceID: *nt.NetworkInterfaceId,
			Ipv6Sets: entity.InterfaceIpv6Sets{
				Ipv6Set: ipv6Sets,
			},
			PrimaryIPAddress: *nt.PrivateIpAddress,
			PrivateIPSets: entity.InterfacePrivateIPSets{
				PrivateIPSet: ipSets,
			},
		})
	}
	return entity.InstanceNetworkInterfaces{
		NetworkInterface: ms,
	}
}

// OSNameEn string `bson:"OSNameEn" json:"OSNameEn,omitempty" xml:"OSNameEn,omitempty" field:"操作系统名称(en)"`
// OSType string `bson:"OSType" json:"OSType,omitempty" xml:"OSType,omitempty" field:"实例操作系统类型"`
// OSName string `bson:"OSName" json:"OSName,omitempty" xml:"OSName,omitempty" field:"操作系统名称" validate:"required"`

// OsInfo OsInfo
func OsInfo(image types.Image) (typ, name, desc string) {
	typ = *image.PlatformDetails
	name = *image.Name // OSNameEn
	if image.Description != nil {
		desc = *image.Description // OSName
	}
	return
}

// EipAddress InstanceEipAddress `bson:"EipAddress" json:"EipAddress,omitempty" xml:"EipAddress,omitempty" type:"Struct" field:"弹性公网IP列表"`
func EipAddress() {
	// todo: 目前aws还没有eip，需要进一步调研
}

// PublicIPAddress []string `bson:"PublicIpAddress" json:"PublicIpAddress,omitempty" xml:"PublicIpAddress,omitempty" field_type:"SliceString" type:"Repeated" field:"公网IP列表" indexed:"1"`
func PublicIPAddress(inst types.Instance) []string {
	if inst.PublicIpAddress == nil {
		return []string{}
	}
	return []string{*inst.PublicIpAddress}
}

// InnerIPAddress []string `bson:"InnerIpAddress" json:"InnerIpAddress,omitempty" xml:"InnerIpAddress,omitempty" field_type:"SliceString" type:"Repeated" field:"内网IP列表" validate:"required" indexed:"1"` // 内网ip,阿里云经典网络内网ip取值为inneripaddress,vpc网络为vpc-PrivateIPAddress,同步时将自动合并该选项
func InnerIPAddress(inst types.Instance) []string {
	if inst.PrivateIpAddress == nil {
		return []string{}
	}
	return []string{*inst.PrivateIpAddress}
}

// VPC
// VlanID string `bson:"VlanId" json:"VlanId,omitempty" xml:"VlanId,omitempty" field:"实例的VLAN-ID"`

// VpcAttributes InstanceVpcAttributes`bson:"VpcAttributes" json:"VpcAttributes,omitempty" xml:"VpcAttributes,omitempty" type:"Struct" field:"专有网络VPC属性"`
func VpcAttributes(inst types.Instance) entity.InstanceVpcAttributes {
	if inst.VpcId == nil {
		return entity.InstanceVpcAttributes{}
	}
	return entity.InstanceVpcAttributes{
		VpcID: *inst.VpcId,
	}
}

// Tags InstanceTags `bson:"Tags" json:"Tags,omitempty" xml:"Tags,omitempty" type:"Struct" field:"标签集合"`
func Tags(inst types.Instance) entity.InstanceTags {
	tagSlice := []entity.Tag{}
	for _, tag := range inst.Tags {
		tagSlice = append(tagSlice, entity.Tag{
			TagKey:   *tag.Key,
			TagValue: *tag.Value,
		})
	}
	return entity.InstanceTags{
		Tag: tagSlice,
	}
}

// LocalStorageCapacity int64 `bson:"LocalStorageCapacity" json:"LocalStorageCapacity,omitempty" xml:"LocalStorageCapacity,omitempty" field:"实例挂载的本地存储容量"`
// LocalStorageAmount int32 `bson:"LocalStorageAmount" json:"LocalStorageAmount,omitempty" xml:"LocalStorageAmount,omitempty" field:"实例挂载的本地存储数量"`
// func LocalStorage(inst types.Instance) {
//	inst.RootDeviceType
//	inst.RootDeviceName
//	inst.RamdiskId
//	for _, device := range inst.BlockDeviceMappings {
//		if *inst.RootDeviceName == *device.DeviceName {
//			device.Ebs.VolumeId
//		}
//	}
// }

// StartTime                  string                             `bson:"StartTime" json:"StartTime,omitempty" xml:"StartTime,omitempty" field:"实例启动时间"`
func StartTime(inst types.Instance) string {
	return inst.LaunchTime.String()
}

// CreationTime aws接口无法直接返回创建时间，这里取所有绑定存储设备中AttachTime最早的时间
func CreationTime(inst types.Instance) int64 {
	minTime := int64(0)
	for _, d := range inst.BlockDeviceMappings {
		t := d.Ebs.AttachTime.Unix()
		if minTime == 0 {
			minTime = t
		}
		if t < minTime {
			minTime = t
		}
	}
	return minTime
}

/*
// HostResource 主机资源
type HostResource struct {
	//Model                     `bson:",inline"`
	HostAgent                  `bson:",inline"`
	AllowChangeData            bool                               `bson:"allow_change_data" json:"allow_change_data,omitempty"` // 是否允许agent修正数据
	IsLock                     bool                               `bson:"is_lock" json:"is_lock,omitempty" field:"是否锁定"`
	DedicatedHostAttribute     InstanceDedicatedHostAttribute     `bson:"DedicatedHostAttribute" json:"DedicatedHostAttribute,omitempty" xml:"DedicatedHostAttribute,omitempty" type:"Struct"`
	OperationLocks             InstanceOperationLocks             `bson:"OperationLocks" json:"OperationLocks,omitempty" xml:"OperationLocks,omitempty" type:"Struct"`
	SaleCycle                  string                             `bson:"SaleCycle" json:"SaleCycle,omitempty" xml:"SaleCycle,omitempty" field:"实例计费周期"`
	SpotStrategy               string                             `bson:"SpotStrategy" json:"SpotStrategy,omitempty" xml:"SpotStrategy,omitempty" field:"抢占策略"`
	DeviceAvailable            bool                               `bson:"DeviceAvailable" json:"DeviceAvailable,omitempty" xml:"DeviceAvailable,omitempty" field:"是否可以挂载数据盘"`
	SpotDuration               int32                              `bson:"SpotDuration" json:"SpotDuration,omitempty" xml:"SpotDuration,omitempty" field:"实例的保留时长"`
	SpotPriceLimit             float32                            `bson:"SpotPriceLimit" json:"SpotPriceLimit,omitempty" xml:"SpotPriceLimit,omitempty" field:"每小时最高价格"`
	DeploymentSetGroupNo       int32                              `bson:"DeploymentSetGroupNo" json:"DeploymentSetGroupNo,omitempty" xml:"DeploymentSetGroupNo,omitempty" field:"实例部署位置"`
	ClusterID                  string                             `bson:"ClusterId" json:"ClusterId,omitempty" xml:"ClusterId,omitempty" field:"实例所在的集群ID"`
	DedicatedInstanceAttribute InstanceDedicatedInstanceAttribute `bson:"DedicatedInstanceAttribute" json:"DedicatedInstanceAttribute,omitempty" xml:"DedicatedInstanceAttribute,omitempty" type:"Struct"`
	GPUSpec                    string                             `bson:"GPUSpec" json:"GPUSpec,omitempty" xml:"GPUSpec,omitempty" field:"实例GPU类型"`
	StoppedMode                string                             `bson:"StoppedMode" json:"StoppedMode,omitempty" xml:"StoppedMode,omitempty" field:"实例停机后是否继续收费"`
	GPUAmount                  int32                              `bson:"GPUAmount" json:"GPUAmount,omitempty" xml:"GPUAmount,omitempty" field:"实例附带的GPU数量"`
	InstanceChargeType         string                             `bson:"InstanceChargeType" json:"InstanceChargeType,omitempty" xml:"InstanceChargeType,omitempty" field:"实例计费方式"`
	InternetChargeType         string                             `bson:"InternetChargeType" json:"InternetChargeType,omitempty" xml:"InternetChargeType,omitempty" field:"网络计费类型"`
	Recyclable                 bool                               `bson:"Recyclable" json:"Recyclable,omitempty" xml:"Recyclable,omitempty" field:"实例是否可以回收"`
	CreditSpecification        string                             `bson:"CreditSpecification" json:"CreditSpecification,omitempty" xml:"CreditSpecification,omitempty" field:"实例运行模式"`
	DeploymentSetID            string                             `bson:"DeploymentSetId" json:"DeploymentSetId,omitempty" xml:"DeploymentSetId,omitempty" field:"部署集ID"`
	KeyPairName                string                             `bson:"KeyPairName" json:"KeyPairName,omitempty" xml:"KeyPairName,omitempty" field:"密钥对名称"`
	Description                string                             `bson:"Description" json:"Description" xml:"Description,omitempty" field:"实例描述" indexed:"1"`
	HpcClusterID               string                             `bson:"HpcClusterId" json:"HpcClusterId,omitempty" xml:"HpcClusterId,omitempty" field:"实例所属的HPC集群ID"`
	DeletionProtection         bool                               `bson:"DeletionProtection" json:"DeletionProtection,omitempty" xml:"DeletionProtection,omitempty" field:"开启实例释放保护"`
	EcsCapacityReservationAttr InstanceEcsCapacityReservationAttr `bson:"EcsCapacityReservationAttr" json:"EcsCapacityReservationAttr,omitempty" xml:"EcsCapacityReservationAttr,omitempty" type:"Struct" field:"预留容量参数"`
	InternetMaxBandwidthIn     int32                              `bson:"InternetMaxBandwidthIn" json:"InternetMaxBandwidthIn,omitempty" xml:"InternetMaxBandwidthIn,omitempty" field:"公网入带宽最大值(M/s)"`
	InternetMaxBandwidthOut    int32                              `bson:"InternetMaxBandwidthOut" json:"InternetMaxBandwidthOut,omitempty" xml:"InternetMaxBandwidthOut,omitempty" field:"公网出带宽最大值(M/s)"`
	IoOptimized                bool                               `bson:"IoOptimized" json:"IoOptimized,omitempty" xml:"IoOptimized,omitempty" field:"是否为I/O优化型实例"`
	RdmaIPAddress              []string                           `bson:"RdmaIpAddress" json:"RdmaIpAddress,omitempty" xml:"RdmaIpAddress,omitempty" field_type:"SliceString" type:"Repeated" field:"RdmaIpAddress"`
	ResourceGroupID            string                             `bson:"ResourceGroupId" json:"ResourceGroupId,omitempty" xml:"ResourceGroupId,omitempty" field:"实例所属的企业资源组ID"`
	InstanceTypeFamily         string                             `bson:"InstanceTypeFamily" json:"InstanceTypeFamily,omitempty" xml:"InstanceTypeFamily,omitempty" field:"实例规格族"`
}
*/
