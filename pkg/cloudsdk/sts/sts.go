package sts

import (
	"errors"
	"fmt"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/kms"

	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// STS interface
type STS interface {
	GetSTSToken(ctx context.Context, ck ClientKey, resourceType, action, arn string) (bool, AssumeRoleOutput, error)
	GetPolicy(ctx context.Context, resourceType, methodName string) (string, error)
}

// AssumeRoleOutput ...
type AssumeRoleOutput struct {
	Token           string
	Expiration      time.Time
	AccessKeyID     string
	AccessKeySecret string
}

// Identity ...
type Identity struct {
	Arn string
}

// ClientKey ...
type C<PERSON><PERSON><PERSON> struct {
	Region          string
	AccessKeyID     string
	AccessKeySecret string
}

// TimeNeverExpire ...
var TimeNeverExpire = time.Date(2099, 1, 1, 0, 0, 0, 0, time.Local)

// GetPolicy ...
func GetPolicy(ctx context.Context, resourceType, methodName string, account *entity.Account) (string, error) {
	if account.HighLevelPermission == false {
		return "original", nil
	}

	var stsClient STS
	if account.AType == "aliyun" {
		stsClient = GetAliSTS()
	} else if account.AType == "aws" {
		stsClient = GetAwsSTS()
	} else if account.AType == "mihoyo" {
		stsClient = GetMihoyoSTS()
	} else if account.AType == "jumpserver" {
		stsClient = GetJumpserverSTS()
	} else {
		return "original AccessKey", nil
	}

	return stsClient.GetPolicy(ctx, resourceType, methodName)
}

// GetSTSTokenFromMethod ...
func GetSTSTokenFromMethod(ctx context.Context, region, resourceType, methodName string, account *entity.Account) (entity.STSResult, error) {
	if account.HighLevelPermission == false {
		decryptedAccessKey, err := utils.DecryptStrByAes(account.AccessKey, constant.AccountSecret)
		if err != nil {
			return entity.STSResult{}, fmt.Errorf("GetSTSTokenFromMethod.decryptAccessKey.failed, error: %s", err.Error())
		}
		decryptedAccessSecret, err := utils.DecryptStrByAes(account.AccessSecret, constant.AccountSecret)
		if err != nil {
			return entity.STSResult{}, fmt.Errorf("GetSTSTokenFromMethod.decryptAccessSecret.failed, error: %s", err.Error())
		}
		if account.KMSName == "" {
			return entity.STSResult{
				Token:      "",
				Expiration: TimeNeverExpire.Unix(),
				Ak:         decryptedAccessKey,
				Sk:         decryptedAccessSecret,
				IsCache:    true,
			}, nil
		}
		kmsClient, ok := kms.Clients["cloudman"]
		if !ok {
			return entity.STSResult{}, errors.New("cannot get kms client: 'cloudman'")
		}
		ak, sk, err := kmsClient.GetRealAK(account.KMSName, decryptedAccessKey, decryptedAccessSecret)
		if err != nil {
			return entity.STSResult{}, err
		}
		return entity.STSResult{
			Expiration: TimeNeverExpire.Unix(),
			Ak:         ak,
			Sk:         sk,
			IsCache:    true,
		}, nil
	}

	var stsClient STS
	if account.AType == "aliyun" {
		stsClient = GetAliSTS()
	} else if account.AType == "aws" {
		stsClient = GetAwsSTS()
	} else if account.AType == "mihoyo" {
		stsClient = GetMihoyoSTS()
	} else if account.AType == "jumpserver" {
		stsClient = GetJumpserverSTS()
	} else {
		return entity.STSResult{
			Token:      "",
			Ak:         "",
			Sk:         "",
			Expiration: **********,
			IsCache:    true,
		}, nil
	}

	isCache, output, err := stsClient.GetSTSToken(ctx, ClientKey{Region: region, AccessKeyID: account.AccessKey, AccessKeySecret: account.AccessSecret}, resourceType, methodName, account.Arn)
	return entity.STSResult{
		Token:      output.Token,
		Ak:         output.AccessKeyID,
		Sk:         output.AccessKeySecret,
		Expiration: output.Expiration.Unix(),
		IsCache:    isCache,
	}, err
}

// GetSTSTokenFromTask ...
func GetSTSTokenFromTask(ctx context.Context, apply entity.STSApply) (entity.STSResult, error) {
	account, err := models.AccountModel.Get(ctx, apply.AccountID, true)
	if err != nil {
		return entity.STSResult{}, err
	}
	if account.HighLevelPermission == false {
		return entity.STSResult{
			Token:      "",
			Expiration: TimeNeverExpire.Unix(),
			Ak:         account.AccessKey,
			Sk:         account.AccessSecret,
			IsCache:    true,
		}, nil
	}

	var stsClient STS
	if account.AType == "aliyun" {
		stsClient = GetAliSTS()
	} else if account.AType == "aws" {
		stsClient = GetAwsSTS()
	}

	isCache, output, err := stsClient.GetSTSToken(ctx, ClientKey{Region: apply.Region, AccessKeyID: account.AccessKey, AccessKeySecret: account.AccessSecret}, apply.ResourceType, apply.MethodName, account.Arn)
	return entity.STSResult{
		Token:      output.Token,
		Ak:         output.AccessKeyID,
		Sk:         output.AccessKeySecret,
		Expiration: output.Expiration.Unix(),
		IsCache:    isCache,
	}, err
}
