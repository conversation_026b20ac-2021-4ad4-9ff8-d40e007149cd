package sts

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	aliError "github.com/aliyun/alibaba-cloud-sdk-go/sdk/errors"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/ram"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/sts"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
)

var aliSTS *AliSTS
var aliOnce sync.Once

var aliResouceServiceMap = map[string]string{
	"host":  "ecs",
	"mysql": "rds",
	"redis": "kvstore",
}

// AliSTS ...
type AliSTS struct {
	tokenMap sync.Map
}

// AliPolicy ...
type AliPolicy struct {
	Statement []AliStatement `json:"Statement"`
	Version   string         `json:"Version,omitempty"`
}

// AliStatement ...
type AliStatement struct {
	Action    string        `json:"Action"`
	Effect    string        `json:"Effect"`
	Resource  string        `json:"Resource,omitempty"`
	Principal *AliPrincipal `json:"Principal,omitempty"`
	Condition string        `json:"Condition,omitempty"`
}

// AliPrincipal ...
type AliPrincipal struct {
	RAM []string `json:"RAM"`
}

// NewAliSTSClient ...
func NewAliSTSClient(ck ClientKey) (*sts.Client, error) {
	return sts.NewClientWithAccessKey(ck.Region, ck.AccessKeyID, ck.AccessKeySecret)
}

// GetAliSTS ...
func GetAliSTS() *AliSTS {
	aliOnce.Do(func() {
		aliSTS = &AliSTS{
			tokenMap: sync.Map{},
		}
	})
	return aliSTS
}

// GetPolicy ...
func (a *AliSTS) GetPolicy(ctx context.Context, resourceType, methodName string) (string, error) {
	service, ok := aliResouceServiceMap[resourceType]
	if !ok {
		return "", errors.New("service of this resource type not found")
	}

	statement := AliStatement{
		Action:    fmt.Sprintf("%s:%s", service, methodName),
		Effect:    "Allow",
		Resource:  "*",
		Principal: nil,
	}
	policy := AliPolicy{
		Statement: []AliStatement{statement},
	}
	policyRaw, _ := json.Marshal(policy)
	return string(policyRaw), nil
}

// AssumeRole ...
func (a *AliSTS) AssumeRole(ctx context.Context, ck ClientKey, service, action, arn string) (AssumeRoleOutput, error) {
	cli, err := NewAliSTSClient(ck)
	if err != nil {
		return AssumeRoleOutput{}, err
	}

	req := sts.CreateAssumeRoleRequest()
	req.Scheme = "https"
	req.RoleArn = arn
	username := permission.GetUsername(ctx)
	if username == "" {
		username = "inner"
	}
	req.RoleSessionName = username
	statement := AliStatement{
		Action:    fmt.Sprintf("%s:%s", service, action),
		Effect:    "Allow",
		Resource:  "*",
		Principal: nil,
	}

	policy := AliPolicy{
		Statement: []AliStatement{statement},
	}
	policyRaw, _ := json.Marshal(policy)
	req.Policy = string(policyRaw)
	resp, err := cli.AssumeRole(req)
	if err != nil {
		return AssumeRoleOutput{}, err
	}
	expireTime, err := time.Parse("2006-01-02T15:04:05Z", resp.Credentials.Expiration)
	if err != nil {
		return AssumeRoleOutput{}, err
	}
	return AssumeRoleOutput{
		Token:           resp.Credentials.SecurityToken,
		Expiration:      expireTime,
		AccessKeyID:     resp.Credentials.AccessKeyId,
		AccessKeySecret: resp.Credentials.AccessKeySecret,
	}, nil
}

// GetSTSToken ...
func (a *AliSTS) GetSTSToken(ctx context.Context, ck ClientKey, resourceType, action, arn string) (bool, AssumeRoleOutput, error) {
	service, ok := aliResouceServiceMap[resourceType]
	if !ok {
		return false, AssumeRoleOutput{}, errors.New("service of this resource type not found")
	}
	key := fmt.Sprintf("%s:%s", service, action)
	val, ok := a.tokenMap.Load(key)
	output, ok2 := val.(AssumeRoleOutput)
	if ok && ok2 && !time.Now().Add(time.Minute*3).After(output.Expiration) {
		return true, output, nil
	}
	newOutput, err := a.AssumeRole(ctx, ck, service, action, arn)
	if err != nil {
		return false, AssumeRoleOutput{}, err
	}
	a.tokenMap.Store(key, newOutput)
	return false, newOutput, nil
}

// GetCallerIdentity ...
func (a *AliSTS) GetCallerIdentity(ck ClientKey) (Identity, error) {
	cli, err := NewAliSTSClient(ck)
	if err != nil {
		return Identity{}, err
	}
	request := sts.CreateGetCallerIdentityRequest()
	request.Scheme = "https"
	resp, err := cli.GetCallerIdentity(request)
	if err != nil {
		return Identity{}, err
	}
	return Identity{Arn: resp.Arn}, nil
}

// CreateRole ...
func (a *AliSTS) CreateRole(ck ClientKey) (Identity, error) {
	identity, err := a.GetCallerIdentity(ck)
	if err != nil {
		return Identity{}, err
	}

	cli, err := ram.NewClientWithAccessKey(ck.Region, ck.AccessKeyID, ck.AccessKeySecret)
	if err != nil {
		return Identity{}, err
	}

	getReq := ram.CreateGetRoleRequest()
	getReq.Scheme = "https"
	getReq.RoleName = "cloudman-admin-role"
	role, err := cli.GetRole(getReq)
	if err == nil {
		return Identity{Arn: role.Role.Arn}, nil
	}
	aliErr := err.(aliError.Error)
	if aliErr.ErrorCode() == "NoPermission" {
		return Identity{}, errors.New("无法获取角色, 请授予该用户AliyunRAMReadOnlyAccess策略")
	}

	principal := AliPrincipal{RAM: []string{identity.Arn}}
	rolePolicyStatement := AliStatement{Action: "sts:AssumeRole", Effect: "Allow", Principal: &principal}
	policy := AliPolicy{Statement: []AliStatement{rolePolicyStatement}, Version: "1"}
	policyRaw, _ := json.Marshal(policy)
	req := ram.CreateCreateRoleRequest()
	req.Scheme = "https"
	req.RoleName = "cloudman-admin-role"
	req.AssumeRolePolicyDocument = string(policyRaw)

	resp, err := cli.CreateRole(req)
	if err != nil {
		return Identity{}, errors.New("创建角色失败,请授予该RAM用户'AliyunRAMFullAccess'策略，或去控制台自行创建名为'cloudman-admin-role'的角色并授予'AdministratorAccess'策略")
	}

	attachReq := ram.CreateAttachPolicyToRoleRequest()
	attachReq.Scheme = "https"
	attachReq.PolicyName = "AdministratorAccess"
	attachReq.PolicyType = "System"
	attachReq.RoleName = resp.Role.RoleName
	_, err = cli.AttachPolicyToRole(attachReq)
	if err != nil {
		return Identity{}, errors.New("授予角色策略失败,请赋予该RAM用户'AliyunRAMFullAccess'策略，或去控制台自行创建名为'cloudman-admin-role'的角色并赋予'AdministratorAccess'策略")
	}
	return Identity{Arn: resp.Role.Arn}, nil
}
