package sts

import (
	"context"
	"sync"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mihoyosdk"
)

var mihoyoSTS *MihoyoSTS
var mihoyoOnce sync.Once

// MihoyoSTS ...
type MihoyoSTS struct {
	tokenMap map[string]*mihoyosdk.MihoyoComputeClient
	m        sync.RWMutex
}

// GetMihoyoSTS ...
func GetMihoyoSTS() *MihoyoSTS {
	mihoyoOnce.Do(func() {
		mihoyoSTS = &MihoyoSTS{
			tokenMap: map[string]*mihoyosdk.MihoyoComputeClient{},
		}
	})
	return mihoyoSTS
}

// GetPolicy ...
func (a *MihoyoSTS) GetPolicy(ctx context.Context, resourceType, methodName string) (string, error) {
	return "original AccessKey", nil
}

// GetSTSToken ...
func (a *MihoyoSTS) GetSTSToken(ctx context.Context, ck ClientKey, resourceType, action, arn string) (bool, AssumeRoleOutput, error) {
	return true, AssumeRoleOutput{}, nil
}
