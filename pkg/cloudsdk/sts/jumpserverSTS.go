package sts

import (
	"context"
	"sync"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

var jumpserverSTS *JumpserverSTS
var jumpserverOnce sync.Once

// JumpserverSTS ...
type JumpserverSTS struct {
}

// GetJumpserverSTS ...
func GetJumpserverSTS() *JumpserverSTS {
	jumpserverOnce.Do(func() {
		jumpserverSTS = &JumpserverSTS{}
	})
	return jumpserverSTS
}

// GetPolicy ...
func (a *JumpserverSTS) GetPolicy(ctx context.Context, resourceType, methodName string) (string, error) {
	return "original AccessKey", nil
}

// GetSTSToken ...
func (a *JumpserverSTS) GetSTSToken(ctx context.Context, ck ClientKey, resourceType, action, arn string) (bool, AssumeRoleOutput, error) {
	decryptedAccessKey, err := utils.DecryptStrByAes(ck.AccessKeyID, constant.AccountSecret)
	if err != nil {
		return false, AssumeRoleOutput{}, err
	}
	decryptedAccessSecret, err := utils.DecryptStrByAes(ck.AccessKeySecret, constant.AccountSecret)
	if err != nil {
		return false, AssumeRoleOutput{}, err
	}
	return true, AssumeRoleOutput{
		Token:           "",
		Expiration:      time.Unix(0, 0),
		AccessKeyID:     decryptedAccessKey,
		AccessKeySecret: decryptedAccessSecret,
	}, nil
}
