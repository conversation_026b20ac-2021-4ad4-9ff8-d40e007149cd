package sts

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/iam"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/util"
)

var awsSTS *AwsSTS
var awsOnce sync.Once

var awsResouceServiceMap = map[string]string{
	"host":  "ec2",
	"mysql": "rds",
	"redis": "elasticache",
}

// AwsSTS ...
type AwsSTS struct {
	tokenMap sync.Map
}

// AwsPolicy ...
type AwsPolicy struct {
	Version   string         `json:"Version"`
	Statement []AwsStatement `json:"Statement"`
}

// AwsStatement ...
type AwsStatement struct {
	Action    string        `json:"Action"`
	Effect    string        `json:"Effect"`
	Resource  string        `json:"Resource,omitempty"`
	Principal *AwsPrincipal `json:"Principal,omitempty"`
}

// AwsPrincipal ...
type AwsPrincipal struct {
	AWS string `json:"AWS"`
}

// NewAwsSTSClient ...
func NewAwsSTSClient(ctx context.Context, ck ClientKey) (*sts.Client, error) {
	cfg, err := util.GetConfig(ctx, ck.Region, ck.AccessKeyID, ck.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	return sts.NewFromConfig(cfg), nil
}

// NewAwsIAMClient ...
func NewAwsIAMClient(ctx context.Context, ck ClientKey) (*iam.Client, error) {
	cfg, err := util.GetConfig(ctx, ck.Region, ck.AccessKeyID, ck.AccessKeySecret)
	if err != nil {
		return nil, err
	}
	return iam.NewFromConfig(cfg), nil
}

// GetAwsSTS ...
func GetAwsSTS() *AwsSTS {
	awsOnce.Do(func() {
		awsSTS = &AwsSTS{
			tokenMap: sync.Map{},
		}
	})
	return awsSTS
}

// GetPolicy ...
func (a *AwsSTS) GetPolicy(ctx context.Context, resourceType, methodName string) (string, error) {
	service, ok := awsResouceServiceMap[resourceType]
	if !ok {
		return "", errors.New("service of this resource type not found")
	}

	statement := AwsStatement{
		Action:   fmt.Sprintf("%s:%s", service, methodName),
		Effect:   "Allow",
		Resource: "*",
	}

	policy := AwsPolicy{
		Version:   "2012-10-17",
		Statement: []AwsStatement{statement},
	}
	policyRaw, _ := json.Marshal(policy)
	return string(policyRaw), nil
}

// AssumeRole ...
func (a *AwsSTS) AssumeRole(ctx context.Context, ck ClientKey, service, action, arn string) (AssumeRoleOutput, error) {
	cli, err := NewAwsSTSClient(ctx, ck)
	if err != nil {
		return AssumeRoleOutput{}, err
	}

	statement := AwsStatement{
		Action:   fmt.Sprintf("%s:%s", service, action),
		Effect:   "Allow",
		Resource: "*",
	}

	policy := AwsPolicy{
		Version:   "2012-10-17",
		Statement: []AwsStatement{statement},
	}
	policyRaw, _ := json.Marshal(policy)

	username := permission.GetUsername(ctx)
	if username == "" {
		username = "inner"
	}
	resp, err := cli.AssumeRole(ctx, &sts.AssumeRoleInput{
		RoleArn:         aws.String(arn),
		RoleSessionName: aws.String(username),
		Policy:          aws.String(string(policyRaw)),
		DurationSeconds: aws.Int32(43200),
	})
	if err != nil {
		return AssumeRoleOutput{}, err
	}

	return AssumeRoleOutput{
		Token:           aws.ToString(resp.Credentials.SessionToken),
		Expiration:      aws.ToTime(resp.Credentials.Expiration),
		AccessKeyID:     aws.ToString(resp.Credentials.AccessKeyId),
		AccessKeySecret: aws.ToString(resp.Credentials.SecretAccessKey),
	}, nil
}

// GetSTSToken ...
func (a *AwsSTS) GetSTSToken(ctx context.Context, ck ClientKey, resourceType, action, arn string) (bool, AssumeRoleOutput, error) {
	service, ok := awsResouceServiceMap[resourceType]
	if !ok {
		return false, AssumeRoleOutput{}, errors.New("service of this resource type not found")
	}
	key := fmt.Sprintf("%s:%s", service, action)
	val, ok := a.tokenMap.Load(key)
	output, ok2 := val.(AssumeRoleOutput)
	if ok && ok2 && !time.Now().Add(time.Minute*3).After(output.Expiration) {
		return true, output, nil
	}
	newOutput, err := a.AssumeRole(ctx, ck, service, action, arn)
	if err != nil {
		return false, AssumeRoleOutput{}, err
	}
	a.tokenMap.Store(key, newOutput)
	return false, newOutput, nil
}

// GetCallerIdentity ...
func (a *AwsSTS) GetCallerIdentity(ctx context.Context, ck ClientKey) (Identity, error) {
	cli, err := NewAwsSTSClient(ctx, ck)
	if err != nil {
		return Identity{}, err
	}
	resp, err := cli.GetCallerIdentity(ctx, &sts.GetCallerIdentityInput{})
	if err != nil {
		return Identity{}, err
	}
	return Identity{Arn: aws.ToString(resp.Arn)}, nil
}

// CreateRole ...
func (a *AwsSTS) CreateRole(ctx context.Context, ck ClientKey) (Identity, error) {
	identity, err := a.GetCallerIdentity(ctx, ck)
	if err != nil {
		return Identity{}, err
	}

	iamCli, err := NewAwsIAMClient(ctx, ck)
	if err != nil {
		return Identity{}, err
	}
	role, err := iamCli.GetRole(ctx, &iam.GetRoleInput{RoleName: aws.String("cloudman-admin-role")})
	if err == nil {
		return Identity{Arn: aws.ToString(role.Role.Arn)}, nil
	}
	if strings.Contains(err.Error(), "StatusCode: 403") {
		return Identity{}, errors.New("无法获取角色, 请授予该用户IAMReadOnlyAccess策略")
	}

	principal := AwsPrincipal{AWS: identity.Arn}
	rolePolicyStatement := AwsStatement{Action: "sts:AssumeRole", Effect: "Allow", Principal: &principal}
	policy := AwsPolicy{Statement: []AwsStatement{rolePolicyStatement}, Version: "2012-10-17"}
	policyRaw, _ := json.Marshal(policy)
	resp, err := iamCli.CreateRole(ctx, &iam.CreateRoleInput{
		AssumeRolePolicyDocument: aws.String(string(policyRaw)),
		RoleName:                 aws.String("cloudman-admin-role"),
		MaxSessionDuration:       aws.Int32(43200),
	})
	if err != nil {
		return Identity{}, errors.New("创建角色失败,请授予该IAM用户'IAMFullAccess'策略，或去控制台自行创建名为'cloudman-admin-role'的角色并授予'AdministratorAccess'策略")
	}

	_, err = iamCli.AttachRolePolicy(ctx, &iam.AttachRolePolicyInput{RoleName: resp.Role.RoleName, PolicyArn: aws.String("arn:aws:iam::aws:policy/AdministratorAccess")})
	if err != nil {
		return Identity{}, errors.New("授予角色策略失败,请授予该IAM用户'IAMFullAccess'策略，或去控制台自行创建名为'cloudman-admin-role'的角色并授予'AdministratorAccess'策略")
	}
	return Identity{Arn: aws.ToString(resp.Role.Arn)}, nil
}
