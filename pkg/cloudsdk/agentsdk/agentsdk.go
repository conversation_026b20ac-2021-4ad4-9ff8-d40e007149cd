package agentsdk

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent_runner/task"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/sts"
)

// SyncCall 同步调用
// 本方法假定了只有第一个回参是有效的
func SyncCall(ctx context.Context, region, resourceType, methodName, accountID string, reqPayload []interface{}, respStruct ...interface{}) error {
	account, err := models.AccountModel.Get(ctx, accountID, true)
	if err != nil {
		return err
	}

	policy, err := sts.GetPolicy(ctx, resourceType, methodName, account)
	if err != nil {
		return err
	}

	stsRes, err := sts.GetSTSTokenFromMethod(ctx, region, resourceType, methodName, account)
	if err != nil {
		return err
	}

	return doSyncCall(ctx, region, resourceType, methodName, account.AType, accountID, policy, stsRes, account.UseAgent, reqPayload, respStruct...)
}

// SyncCallWithAKSK ...
// 本方法接收的sk是原始未加密的sk
func SyncCallWithAKSK(ctx context.Context, region, resourceType, methodName, ispType, ak, sk string, useAgent bool, reqPayload []interface{}, respStruct ...interface{}) error {
	return doSyncCall(ctx, region, resourceType, methodName, ispType, "", "original", entity.STSResult{Ak: ak, Sk: sk, Expiration: sts.TimeNeverExpire.Unix(), IsCache: true}, useAgent, reqPayload, respStruct...)
}

func doSyncCall(ctx context.Context, region, resourceType, methodName, ispType, accountID, policy string, stsResult entity.STSResult, useAgent bool, reqPayload []interface{}, respStruct ...interface{}) error {
	runner, err := task.NewTask(ctx, accountID, ispType, resourceType, methodName,
		entity.STSKey{STSApply: entity.STSApply{ResourceType: resourceType, MethodName: methodName, AccountID: accountID, Region: region, Policy: policy}, STSResult: stsResult},
		time.Minute, isRiskyMethod(ispType, resourceType, methodName))
	if err != nil {
		return err
	}
	// 等待mongodb副本同步
	time.Sleep(15 * time.Millisecond)
	var reqPayloadStr []string
	for _, v := range reqPayload {
		tmp, _ := json.Marshal(v)
		reqPayloadStr = append(reqPayloadStr, string(tmp))
	}
	resp, businessErr, callErr := runner.SyncCall(ctx, useAgent, reqPayloadStr)
	if callErr != nil {
		return callErr
	}
	if businessErr != nil {
		return businessErr
	}
	if len(resp) < len(respStruct) {
		return fmt.Errorf("response array less then required respStruct")
	}
	for i := range respStruct {
		if err := json.Unmarshal([]byte(resp[i]), respStruct[i]); err != nil {
			return err
		}
	}
	return nil
}

// 保存方法类型的map，若为true则表示该方法为操作类型,其余则为只读类型
var methodTypeMap = map[string]bool{
	"aliyun.host.AddTags":                         true,
	"aliyun.host.RemoveTags":                      true,
	"aliyun.host.RunInstances":                    true,
	"aliyun.host.StartInstances":                  true,
	"aliyun.host.StopInstances":                   true,
	"aliyun.host.RebootInstances":                 true,
	"aliyun.host.DeleteInstances":                 true,
	"aliyun.host.ModifyInstanceChargeType":        true,
	"aliyun.mysql.CreateBackup":                   true,
	"aliyun.mysql.CreateDBCluster":                true,
	"aliyun.mysql.CreateAccount":                  true,
	"aliyun.mysql.ModifyDBClusterAccessWhitelist": true,
	"aliyun.redis.CreateInstance":                 true,
	"aliyun.redis.CreateBackup":                   true,
	"aliyun.redis.DeleteInstance":                 true,
	"aliyun.redis.TransformInstanceChargeType":    true,
	"aws.host.CreateTags":                         true,
	"aws.host.RunInstances":                       true,
	"aws.host.StartInstances":                     true,
	"aws.host.StopInstances":                      true,
	"aws.host.RebootInstances":                    true,
	"aws.host.DeleteInstances":                    true,
	"aws.mysql.CreateDBCluster":                   true,
	"aws.mysql.CreateDBInstance":                  true,
	"aws.mysql.CreateDBClusterSnapshot":           true,
	"aws.mysql.ModifyDBCluster":                   true,
	"aws.mysql.DeleteDBCluster":                   true,
	"aws.mysql.DeleteDBInstance":                  true,
	"aws.redis.CreateSnapshot":                    true,
	"aws.redis.CreateReplicationGroup":            true,
	"aws.redis.DeleteReplicationGroup":            true,
	"mihoyo.host.CreateVM":                        true,
	"mihoyo.host.DestroyVM":                       true,
	"mihoyo.host.OperateVM":                       true,
}

func isRiskyMethod(ispType, resourceType, methodName string) int32 {
	result, ok := methodTypeMap[strings.Join([]string{ispType, resourceType, methodName}, ".")]
	if !ok {
		return 0
	}
	if result {
		return 1
	}
	return 0
}
