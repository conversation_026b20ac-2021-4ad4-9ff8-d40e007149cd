package host

import (
	"context"
	"encoding/json"
	"strconv"

	ecs******** "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
)

// AliHost 阿里云主机SDK
type AliHost struct {
	regionID  string
	accountID string
}

// DescribePrice 查询ecs主机价格
func (a *AliHost) DescribePrice(ctx context.Context, resourceType string, input []byte) (*cloudman.SalePrice, error) {
	var hostData *ecs********.RunInstancesRequest
	err := json.Unmarshal(input, &hostData)
	if err != nil {
		return nil, err
	}
	var dataDisk []*ecs********.DescribePriceRequestDataDisk
	for _, v := range hostData.DataDisk {
		dataDisk = append(dataDisk, &ecs********.DescribePriceRequestDataDisk{
			Category:         v.Category,
			Size:             v.Size,
			PerformanceLevel: v.PerformanceLevel,
		})
	}
	systemDiskSize, _ := strconv.Atoi(tea.StringValue(hostData.SystemDisk.Size))
	if err != nil {
		return nil, err
	}

	req := &ecs********.DescribePriceRequest{
		Amount: hostData.Amount,
		SystemDisk: &ecs********.DescribePriceRequestSystemDisk{
			Category:         hostData.SystemDisk.Category,
			Size:             tea.Int32(int32(systemDiskSize)),
			PerformanceLevel: hostData.SystemDisk.PerformanceLevel,
		},
		DataDisk:                dataDisk,
		ImageId:                 hostData.ImageId,
		InstanceType:            hostData.InstanceType,
		InternetChargeType:      hostData.InternetChargeType,
		InternetMaxBandwidthOut: hostData.InternetMaxBandwidthOut,
		Period:                  hostData.Period,
		PriceUnit:               hostData.PeriodUnit,
		RegionId:                tea.String(a.regionID),
		ResourceType:            tea.String(resourceType), // 目标资源类型
		// ZoneId:                  nil,
		// InstanceNetworkType:     nil,
		// IoOptimized:             nil,
	}
	res := &ecs********.DescribePriceResponse{}
	err = agentsdk.SyncCall(ctx, a.regionID, "host", "DescribePrice", a.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	return &cloudman.SalePrice{Price: tea.Float32Value(res.Body.PriceInfo.Price.TradePrice)}, nil
}

// DescribeAutoSnapshotPolicyEx 获取自动磁盘策略
func (a *AliHost) DescribeAutoSnapshotPolicyEx(ctx context.Context, input *common.PageCommon) (*cloudman.AutoSnapshotPolicyEXResp, error) {
	// TODO implement me
	describeAutoSnapshotPolicyExReq := &ecs********.DescribeAutoSnapshotPolicyExRequest{
		RegionId:   tea.String(a.regionID),
		PageNumber: tea.Int32(input.GetPage()),
		PageSize:   tea.Int32(input.GetSize()),
	}

	res := &ecs********.DescribeAutoSnapshotPolicyExResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeAutoSnapshotPolicyEx", a.accountID, []interface{}{describeAutoSnapshotPolicyExReq}, res)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.AutoSnapshotPolicyEXInfo

	for _, v := range res.Body.AutoSnapshotPolicies.AutoSnapshotPolicy {
		list = append(list, &cloudman.AutoSnapshotPolicyEXInfo{
			Status:         tea.StringValue(v.Status),
			Name:           tea.StringValue(v.AutoSnapshotPolicyName),
			TimePoints:     tea.StringValue(v.TimePoints),
			Id:             tea.StringValue(v.AutoSnapshotPolicyId),
			RepeatWeekDays: tea.StringValue(v.RepeatWeekdays),
		})
	}

	return &cloudman.AutoSnapshotPolicyEXResp{List: list, Total: tea.Int32Value(res.Body.TotalCount)}, nil
}

// GetAliHost 获取AliHost
func GetAliHost(ctx context.Context, regionID, accountID string) (*AliHost, error) {
	return &AliHost{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

// DescribeSecurityGroups ...
func (a *AliHost) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	describeSecurityGroupsRequest := &ecs********.DescribeSecurityGroupsRequest{
		RegionId:   tea.String(a.regionID),
		MaxResults: tea.Int32(100),
		VpcId:      tea.String(input.VpcID),
	}

	maxLoop := 100
	secGroups := make([]*ecs********.DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroup, 0)
	for i := 0; i <= maxLoop; i++ {
		res := &ecs********.DescribeSecurityGroupsResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeSecurityGroups", a.accountID, []interface{}{describeSecurityGroupsRequest}, res)
		if err != nil {
			return nil, err
		}
		secGroups = append(secGroups, res.Body.SecurityGroups.SecurityGroup...)
		if res.Body.NextToken == nil || *res.Body.NextToken == "" {
			break
		}
		describeSecurityGroupsRequest.SetNextToken(*res.Body.NextToken)
	}

	var list []*cloudman.SecurityGroupInfo
	for _, v := range secGroups {
		list = append(list, &cloudman.SecurityGroupInfo{
			Description:       tea.StringValue(v.Description),
			VpcId:             tea.StringValue(v.VpcId),
			SecurityGroupId:   tea.StringValue(v.SecurityGroupId),
			SecurityGroupName: tea.StringValue(v.SecurityGroupName),
			SecurityGroupType: tea.StringValue(v.SecurityGroupType),
		})
	}

	return &cloudman.SecurityGroupResp{List: list}, nil
}

// DescribeSubnets DescribeSubnets
func (a *AliHost) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	req := &ecs********.DescribeVSwitchesRequest{
		VpcId:    tea.String(input.VpcID),
		ZoneId:   tea.String(input.ZoneID),
		RegionId: tea.String(a.regionID),
		PageSize: tea.Int32(50),
	}
	res := &ecs********.DescribeVSwitchesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeVSwitches", a.accountID, []interface{}{req}, res)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.VSwitchInfo
	for _, v := range res.Body.VSwitches.VSwitch {
		list = append(list, &cloudman.VSwitchInfo{
			VSwitchName: tea.StringValue(v.VSwitchName),
			Status:      tea.StringValue(v.Status),
			VSwitchId:   tea.StringValue(v.VSwitchId),
		})
	}

	return &cloudman.VSwitchResp{List: list}, nil
}

// DescribeNetworks DescribeNetworks
func (a *AliHost) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	describeVpcsRequest := &ecs********.DescribeVpcsRequest{
		RegionId:   tea.String(a.regionID),
		PageNumber: tea.Int32(1),
		PageSize:   tea.Int32(10), // 允许传入的最大值为50
	}
	// 复制代码运行请自行打印 API 的返回值
	res := &ecs********.DescribeVpcsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeVpcs", a.accountID, []interface{}{describeVpcsRequest}, res)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.NetworkInfo
	for _, v := range res.Body.Vpcs.Vpc {
		list = append(list, &cloudman.NetworkInfo{
			VpcId:       tea.StringValue(v.VpcId),
			Name:        tea.StringValue(v.VpcName),
			Status:      tea.StringValue(v.Status),
			Description: tea.StringValue(v.Description),
		})
	}

	return &cloudman.NetworkResp{List: list}, nil
}

// DescribeInstanceTypes DescribeInstanceTypesDescribeInstanceTypes
func (a *AliHost) DescribeInstanceTypes(ctx context.Context, input *common.DescribeInstanceTypesInput) (result *cloudman.InstanceTypesResp, err error) {
	var list []*cloudman.InstanceTypeResp
	if input.ImageID != "" {
		list, err = a.DescribeInstanceTypesByImageID(ctx, input.ImageID)
		if err != nil {
			return nil, err
		}
	} else {
		request := &ecs********.DescribeInstanceTypesRequest{}
		res := &ecs********.DescribeInstanceTypesResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstanceTypes", a.accountID, []interface{}{request}, res)
		if err != nil {
			return nil, err
		}
		for _, v := range res.Body.InstanceTypes.InstanceType {
			list = append(list, &cloudman.InstanceTypeResp{
				Value:              "",
				Label:              "",
				MemorySize:         tea.Float32Value(v.MemorySize),
				CPUCoreCount:       tea.Int32Value(v.CpuCoreCount),
				InstanceTypeFamily: tea.StringValue(v.InstanceTypeFamily),
				InstanceTypeID:     tea.StringValue(v.InstanceTypeId),
			})
		}
	}

	availableRes, err := a.DescribeAvailableResource(ctx, "InstanceType", a.regionID, input.ZoneID)
	if err != nil {
		return nil, err
	}

	instanceMap := map[string]*ecs********.DescribeAvailableResourceResponseBodyAvailableZonesAvailableZoneAvailableResourcesAvailableResourceSupportedResourcesSupportedResource{}
	for _, zone := range availableRes.Body.AvailableZones.AvailableZone {
		for _, availableResource := range zone.AvailableResources.AvailableResource {
			if tea.StringValue(availableResource.Type) == "InstanceType" {
				for _, instance := range availableResource.SupportedResources.SupportedResource {
					instanceMap[tea.StringValue(instance.Value)] = instance
				}
			}
		}
	}

	var retList []*cloudman.InstanceTypeResp
	for k := range list {
		instance, ok := instanceMap[list[k].InstanceTypeID]
		if ok {
			list[k].Status = tea.StringValue(instance.Status)
			retList = append(retList, list[k])
		}
	}

	return &cloudman.InstanceTypesResp{
		List: retList,
	}, nil
}

// DescribeAvailableResource ...
func (a *AliHost) DescribeAvailableResource(ctx context.Context, destinationResource string, regionID string, zoneID string) (*ecs********.DescribeAvailableResourceResponse, error) {
	res := &ecs********.DescribeAvailableResourceResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeAvailableResource", a.accountID, []interface{}{&ecs********.DescribeAvailableResourceRequest{
		DestinationResource: tea.String(destinationResource),
		RegionId:            tea.String(regionID),
		ZoneId:              tea.String(zoneID),
	}}, res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// DescribeInstanceTypesByImageID 通过镜像id筛选实例规格
func (a *AliHost) DescribeInstanceTypesByImageID(ctx context.Context, imageID string) ([]*cloudman.InstanceTypeResp, error) {
	res := &ecs********.DescribeImageSupportInstanceTypesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeImageSupportInstanceTypes", a.accountID, []interface{}{&ecs********.DescribeImageSupportInstanceTypesRequest{
		ImageId:  tea.String(imageID),
		RegionId: tea.String(a.regionID),
	}}, res)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.InstanceTypeResp
	for _, v := range res.Body.InstanceTypes.InstanceType {
		list = append(list, &cloudman.InstanceTypeResp{
			Value:              "",
			Label:              "",
			MemorySize:         tea.Float32Value(v.MemorySize),
			CPUCoreCount:       tea.Int32Value(v.CpuCoreCount),
			InstanceTypeFamily: tea.StringValue(v.InstanceTypeFamily),
			InstanceTypeID:     tea.StringValue(v.InstanceTypeId),
		})
	}
	return list, nil
}

// DescribeVolumeTypes DescribeVolumeTypes
func (a *AliHost) DescribeVolumeTypes(ctx context.Context, input *common.DescribeVolumeTypesInput) (*cloudman.VolumeTypesResp, error) {
	options := []*cloudman.ValueLabelResp{{
		Value: "cloud_essd",
		Label: "ESSD云盘",
	}, {
		Value: "cloud_efficiency",
		Label: "高效云盘",
	}}
	return &cloudman.VolumeTypesResp{SystemDisk: &cloudman.VolumeType{
		CateGoryOptions: options,
		MinSize:         40,
		MaxSize:         16000,
		MinCount:        1,
		MaxCount:        1,
	}, DataDisk: &cloudman.VolumeType{
		CateGoryOptions: options,
		MinSize:         40,
		MaxSize:         16000,
		MinCount:        0,
		MaxCount:        4,
	}}, nil
}

// DescribeKeyPairs DescribeKeyPairs
func (a *AliHost) DescribeKeyPairs(ctx context.Context, input *common.DescribeKeyPairsInput) (*cloudman.KeyPairResp, error) {
	res := &ecs********.DescribeKeyPairsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeKeyPairs", a.accountID, []interface{}{&ecs********.DescribeKeyPairsRequest{
		RegionId:    tea.String(a.regionID),
		KeyPairName: tea.String(input.KeyName),
	}}, res)
	if err != nil {
		return nil, err
	}

	var list []string
	for _, v := range res.Body.KeyPairs.KeyPair {
		list = append(list, tea.StringValue(v.KeyPairName))
	}

	return &cloudman.KeyPairResp{List: list}, nil
}

// DescribeImages DescribeImages
func (a *AliHost) DescribeImages(ctx context.Context, input *common.DescribeImagesInput) (*cloudman.ImagesResp, error) {
	const pageSize = 100
	var pageNum int32 = 1
	req := &ecs********.DescribeImagesRequest{
		RegionId:             &a.regionID,
		Status:               tea.String("Available"),
		IsSupportIoOptimized: tea.Bool(true),
		IsSupportCloudinit:   tea.Bool(true),
		PageNumber:           tea.Int32(pageNum),
		PageSize:             tea.Int32(pageSize),
		IsPublic:             tea.Bool(input.IsPublic),
		Architecture:         tea.String("x86_64"),
	}

	if input.OsType != "" {
		req.OSType = tea.String(input.OsType)
	}

	if input.ImageName != "" {
		req.ImageName = tea.String(input.ImageName)
	}

	var images []*ecs********.DescribeImagesResponseBodyImagesImage
	for {
		pageNum++
		resp := &ecs********.DescribeImagesResponse{}
		err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeImages", a.accountID, []interface{}{req}, resp)
		if err != nil {
			return nil, err
		}
		images = append(images, resp.Body.Images.Image...)
		if len(resp.Body.Images.Image) == pageSize {
			req.PageNumber = tea.Int32(pageNum)
			continue
		}
		break
	}

	var li []*cloudman.ImageResp
	for _, v := range images {
		li = append(li, &cloudman.ImageResp{
			OsName:    tea.StringValue(v.OSName),
			ImageName: tea.StringValue(v.ImageName),
			ImageId:   tea.StringValue(v.ImageId),
			OsType:    tea.StringValue(v.OSType),
			IsPublic:  tea.BoolValue(v.IsPublic),
			Size:      tea.Int32Value(v.Size),
		})
	}

	return &cloudman.ImagesResp{List: li}, nil
}

// DescribeChargeType 获取计费类型
func (a *AliHost) DescribeChargeType(ctx context.Context) (*cloudman.ChargeTypesResp, error) {
	return &cloudman.ChargeTypesResp{
		List: []*cloudman.ValueLabelResp{{
			Value: "PrePaid",
			Label: "包年包月",
		}, {
			Value: "PostPaid",
			Label: "按量计费",
		}},
		Source: cloudman.Datasource_raw,
	}, nil
}
