package host

import (
	"context"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// Host 定义主机的方法
type Host interface {
	DescribeImages(ctx context.Context, input *common.DescribeImagesInput) (*cloudman.ImagesResp, error)
	DescribeInstanceTypes(ctx context.Context, input *common.DescribeInstanceTypesInput) (*cloudman.InstanceTypesResp, error)
	DescribeKeyPairs(ctx context.Context, input *common.DescribeKeyPairsInput) (*cloudman.KeyPairResp, error)
	DescribeVolumeTypes(ctx context.Context, input *common.DescribeVolumeTypesInput) (*cloudman.VolumeTypesResp, error)
	DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error)
	DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error)
	DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error)
	DescribeChargeType(ctx context.Context) (*cloudman.ChargeTypesResp, error)
	DescribeAutoSnapshotPolicyEx(ctx context.Context, input *common.PageCommon) (*cloudman.AutoSnapshotPolicyEXResp, error)
	DescribePrice(ctx context.Context, resType string, input []byte) (*cloudman.SalePrice, error)
}

// GetHost 获取主机SDK
func GetHost(ctx context.Context, ispType, regionID, accountID string) (Host, error) {
	switch ispType {
	case constant.IspAws:
		return GetAwsHost(ctx, regionID, accountID)
	case constant.IspAliyun:
		return GetAliHost(ctx, regionID, accountID)
	case constant.IspMihoyo:
		return GetMihoyoHost(ctx, regionID, accountID)
	default:
		return nil, fmt.Errorf("云厂商（%s）暂不支持", ispType)
	}
}

// DescribeImagesInput 描述Image查询条件
type DescribeImagesInput struct {
	OsType    string
	ImageName string
	IsPublic  bool
}

// GetOSType 获取系统类型
func (d DescribeImagesInput) GetOSType() string {
	if d.OsType == "windows" {
		return "Windows"
	}

	return "Linux/UNIX"
}

// DescribeInstanceTypesInput 描述InstanceType查询条件
type DescribeInstanceTypesInput struct {
	ImageID string // 镜像id
}

// DescribeKeyPairsInput DescribeKeyPairsInput
type DescribeKeyPairsInput struct {
	KeyName string
}

// DescribeVolumeTypesInput DescribeVolumeTypesInput
type DescribeVolumeTypesInput struct {
	ImageID string
}

// PageCommon 通用分页
type PageCommon struct {
	Page int32
	Size int32
}

// GetPage 获取页码
func (p PageCommon) GetPage() int32 {
	if p.Page != 0 {
		return p.Page
	}

	return 1
}

// GetSize 获取分页大小
func (p PageCommon) GetSize() int32 {
	if p.Size != 0 {
		return p.Page
	}

	return 30
}
