package host

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mihoyosdk"
)

// GetMihoyoHost 获取mhy-host类型的查询操作类
func GetMihoyoHost(ctx context.Context, regionID, accountID string) (*MihoyoHost, error) {
	return &MihoyoHost{
		accountID: accountID,
	}, nil
}

// MihoyoHost mhy-host查询类操作接口实现类
type MihoyoHost struct {
	accountID string
}

// DescribeImages 获取镜像
func (m MihoyoHost) DescribeImages(ctx context.Context, input *common.DescribeImagesInput) (*cloudman.ImagesResp, error) {
	imageList := []mihoyosdk.HostImage{}
	err := agentsdk.SyncCall(ctx, "", "host", "FindImages", m.accountID, []interface{}{}, &imageList)
	if err != nil {
		logrus.Warnf("mihoyoHost.DescribeImages.error:%s", err.Error())
		return nil, err
	}
	var li []*cloudman.ImageResp
	for _, image := range imageList {
		fullImageName := image.ImageName
		if image.ImageDescription != "" {
			fullImageName += " [" + image.ImageDescription + "]"
		}
		if input.OsType != "" && strings.ToLower(input.OsType) != strings.ToLower(image.OsType) {
			// 平台接口不支持按照os_type筛选image和分页，此处本地过滤
			continue
		}
		if input.ImageName != "" && strings.ToLower(input.ImageName) != strings.ToLower(image.ImageName) {
			continue
		}
		li = append(li, &cloudman.ImageResp{
			OsName:    image.OsName,
			ImageName: fullImageName,
			ImageId:   image.ImageID,
			OsType:    image.OsType,
			IsPublic:  true,
		})
	}

	return &cloudman.ImagesResp{List: li}, nil
}

// DescribeInstanceTypes 获取实例类型
func (m MihoyoHost) DescribeInstanceTypes(ctx context.Context, input *common.DescribeInstanceTypesInput) (*cloudman.InstanceTypesResp, error) {
	instanceResult := []mihoyosdk.InstanceType{}
	err := agentsdk.SyncCall(ctx, "", "host", "FindInstanceType", m.accountID, []interface{}{}, &instanceResult)
	if err != nil {
		logrus.Warnf("mihoyoHost.DescribeInstanceTypes.error:%s", err.Error())
		return nil, err
	}
	cpuEnum := []int64{1, 2, 4, 8, 12, 16, 20, 24, 32, 48, 64}
	memMulti := []int64{1, 2, 3, 4, 6}
	var li []*cloudman.InstanceTypeResp
	for _, vmType := range instanceResult {
		// 循环cpu枚举和mem倍率枚举，并判断是否超过平台限制
		for _, cpu := range cpuEnum {
			if cpu > vmType.CPUAvailable {
				continue
			}
			for _, memM := range memMulti {
				if cpu*memM > vmType.MemoryAvailable {
					continue
				}
				li = append(li, &cloudman.InstanceTypeResp{
					InstanceTypeFamily: vmType.Name,
					InstanceTypeID:     fmt.Sprintf("%s - %dC%dG", vmType.Name, cpu, cpu*memM),
					CPUCoreCount:       int32(cpu),
					MemorySize:         float32(cpu * memM),
				})
			}
		}
	}
	return &cloudman.InstanceTypesResp{List: li}, nil
}

// DescribeKeyPairs 获取密钥对
func (m MihoyoHost) DescribeKeyPairs(ctx context.Context, input *common.DescribeKeyPairsInput) (*cloudman.KeyPairResp, error) {
	return &cloudman.KeyPairResp{List: []string{}}, nil
}

// DescribeVolumeTypes 获取磁盘类型
func (m MihoyoHost) DescribeVolumeTypes(ctx context.Context, input *common.DescribeVolumeTypesInput) (*cloudman.VolumeTypesResp, error) {
	options := []*cloudman.ValueLabelResp{{
		Value: "default_disk_type",
		Label: "默认磁盘类型",
	}}
	return &cloudman.VolumeTypesResp{SystemDisk: &cloudman.VolumeType{
		CateGoryOptions: options,
		MinSize:         40,
		MaxSize:         40,
		MinCount:        1,
		MaxCount:        1,
	}, DataDisk: &cloudman.VolumeType{
		CateGoryOptions: options,
		MinSize:         20,
		MaxSize:         8000,
		MinCount:        1,
		MaxCount:        1,
	}}, nil
}

// DescribeNetworks 获取网络
func (m MihoyoHost) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	return &cloudman.NetworkResp{List: []*cloudman.NetworkInfo{
		{
			VpcId: "default_vpc",
			Name:  "默认网络",
		},
	}}, nil
}

// DescribeSubnets 获取子网交换机
func (m MihoyoHost) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	return &cloudman.VSwitchResp{List: []*cloudman.VSwitchInfo{
		{
			VSwitchId:   "default_switch",
			VSwitchName: "默认交换机",
		},
	}}, nil
}

// DescribeSecurityGroups 获取安全组
func (m MihoyoHost) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	return &cloudman.SecurityGroupResp{List: []*cloudman.SecurityGroupInfo{
		{
			SecurityGroupId:   "default_security_group",
			SecurityGroupName: "默认安全组",
		},
	}}, nil
}

// DescribeChargeType 获取计费模式
func (m MihoyoHost) DescribeChargeType(ctx context.Context) (*cloudman.ChargeTypesResp, error) {
	return &cloudman.ChargeTypesResp{
		List: []*cloudman.ValueLabelResp{{
			Value: "default_charge_type",
			Label: "默认计费模式",
		}},
		Source: cloudman.Datasource_raw,
	}, nil
}

// DescribeAutoSnapshotPolicyEx 获取备份规则
func (m MihoyoHost) DescribeAutoSnapshotPolicyEx(ctx context.Context, input *common.PageCommon) (*cloudman.AutoSnapshotPolicyEXResp, error) {
	return &cloudman.AutoSnapshotPolicyEXResp{List: []*cloudman.AutoSnapshotPolicyEXInfo{}}, nil
}

// DescribePrice 获取报价
func (m MihoyoHost) DescribePrice(ctx context.Context, resType string, input []byte) (*cloudman.SalePrice, error) {
	return &cloudman.SalePrice{Price: 0}, nil
}
