package host

import (
	"context"
	"fmt"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
)

// AwsHost AwsHost
type AwsHost struct {
	regionID  string
	accountID string
}

// DescribePrice 获取订单价格
func (a *AwsHost) DescribePrice(ctx context.Context, resType string, input []byte) (*cloudman.SalePrice, error) {
	return &cloudman.SalePrice{Price: 0}, nil
}

// GetAwsHost GetAwsHost
func GetAwsHost(ctx context.Context, regionID, accountID string) (*AwsHost, error) {
	return &AwsHost{regionID: regionID, accountID: accountID}, nil
}

// FilterName
var (
	vpcIDFilter = "vpc-id"
)

// DescribeAutoSnapshotPolicyEx 磁盘自动快照策略
func (a *AwsHost) DescribeAutoSnapshotPolicyEx(ctx context.Context, input *common.PageCommon) (*cloudman.AutoSnapshotPolicyEXResp, error) {
	return &cloudman.AutoSnapshotPolicyEXResp{List: nil}, nil
}

// DescribeSecurityGroups DescribeSecurityGroups
func (a *AwsHost) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	var filters []types.Filter
	if input.VpcID != "" {
		filters = []types.Filter{
			{Name: &vpcIDFilter, Values: []string{input.VpcID}},
		}
	}
	reqInput := &ec2.DescribeSecurityGroupsInput{
		Filters: filters,
	}

	out := &ec2.DescribeSecurityGroupsOutput{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeSecurityGroups", a.accountID, []interface{}{"", reqInput}, out)
	if err != nil {
		return nil, err
	}

	var li []*cloudman.SecurityGroupInfo
	for _, sg := range out.SecurityGroups {
		li = append(li, &cloudman.SecurityGroupInfo{
			SecurityGroupId:   *sg.GroupId,
			SecurityGroupName: *sg.GroupName,
			VpcId:             *sg.VpcId,
			Description:       *sg.Description,
		})
	}
	return &cloudman.SecurityGroupResp{List: li}, nil
}

// DescribeSubnets DescribeSubnets
func (a *AwsHost) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	reqInput := &ec2.DescribeSubnetsInput{
		Filters: []types.Filter{
			{Name: &vpcIDFilter, Values: []string{input.VpcID}},
			{Name: aws.String("availability-zone-id"), Values: []string{input.ZoneID}},
		},
	}

	out := &ec2.DescribeSubnetsOutput{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeSubnets", a.accountID, []interface{}{"", reqInput}, out)
	if err != nil {
		return nil, err
	}

	var li []*cloudman.VSwitchInfo
	for _, subnet := range out.Subnets {
		name := *subnet.SubnetId
		for _, t := range subnet.Tags {
			if *t.Key == "Name" {
				name = *t.Value
			}
		}
		fmt.Println("Name=", name)
		li = append(li, &cloudman.VSwitchInfo{
			VSwitchName: name,
			Status:      string(subnet.State),
			VSwitchId:   *subnet.SubnetId,
		})
	}
	return &cloudman.VSwitchResp{
		List: li,
	}, nil
}

// DescribeNetworks DescribeNetworks
func (a *AwsHost) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	reqInput := &ec2.DescribeVpcsInput{}
	out := &ec2.DescribeVpcsOutput{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeVpcs", a.accountID, []interface{}{"", reqInput}, out)
	if err != nil {
		return nil, err
	}

	var li []*cloudman.NetworkInfo
	for _, v := range out.Vpcs {
		name := *v.VpcId
		for _, t := range v.Tags {
			if *t.Key == "Name" {
				name = *t.Value
			}
		}
		li = append(li, &cloudman.NetworkInfo{
			VpcId:  *v.VpcId,
			Name:   name,
			Status: string(v.State),
			// Description: *v.DhcpOptionsId,
		})
	}
	return &cloudman.NetworkResp{List: li}, nil
}

// DescribeVolumeTypes DescribeVolumeTypes
func (a *AwsHost) DescribeVolumeTypes(ctx context.Context, input *common.DescribeVolumeTypesInput) (*cloudman.VolumeTypesResp, error) {
	// 找不到获取ebs类型api，故硬编码常用类型
	options := []*cloudman.ValueLabelResp{{
		Value: "gp2",
		Label: "通用型SSD(gp2)",
	}, {
		Value: "gp3",
		Label: "通用型SSD(gp3)",
	}}
	return &cloudman.VolumeTypesResp{
		SystemDisk: &cloudman.VolumeType{
			CateGoryOptions: options,
			MinSize:         40,
			MaxSize:         16000,
			MinCount:        1,
			MaxCount:        1,
		},
		DataDisk: &cloudman.VolumeType{
			CateGoryOptions: options,
			MinSize:         40,
			MaxSize:         16000,
			MinCount:        0,
			MaxCount:        4,
		},
	}, nil
}

// DescribeKeyPairs DescribeKeyPairs
func (a *AwsHost) DescribeKeyPairs(ctx context.Context, input *common.DescribeKeyPairsInput) (*cloudman.KeyPairResp, error) {
	keyNames := []string{input.KeyName}
	if input.KeyName == "*" {
		keyNames = []string{}
	}
	out := &ec2.DescribeKeyPairsOutput{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeKeyPairs", a.accountID, []interface{}{"", &ec2.DescribeKeyPairsInput{KeyNames: keyNames}}, out)
	if err != nil {
		return nil, err
	}

	var keyNameSlice []string
	for _, kp := range out.KeyPairs {
		keyNameSlice = append(keyNameSlice, *kp.KeyName)
	}

	return &cloudman.KeyPairResp{List: keyNameSlice}, nil
}

// DescribeImages DescribeImages
func (a *AwsHost) DescribeImages(ctx context.Context, input *common.DescribeImagesInput) (*cloudman.ImagesResp, error) {
	// Windows/"Linux/UNIX"
	filter := []types.Filter{
		{Name: aws.String("is-public"), Values: []string{fmt.Sprintf("%v", input.IsPublic)}}, // 是否为公共镜像
		// {Name: aws.String("is-public"), Values: []string{fmt.Sprintf("%v", false)}}, // 是否为公共镜像
		{Name: aws.String("platform-details"), Values: []string{input.GetOSType()}}, // 镜像所属平台
		{Name: aws.String("architecture"), Values: []string{"x86_64"}},              // 镜像所属架构
	}
	if input.ImageName != "" {
		// AMI-name
		filter = append(filter, types.Filter{
			Name:   aws.String("name"),
			Values: []string{input.ImageName},
		})
	}
	result := &ec2.DescribeImagesOutput{}
	err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeImages", a.accountID, []interface{}{"", &ec2.DescribeImagesInput{
		Filters: filter,
	}}, result)
	if err != nil {
		return nil, err
	}

	// 获取zest配置中imageOwner的镜像
	awsOption, err := cfg.GetAwsOptionConf()
	if err != nil {
		return nil, err
	}
	if len(awsOption.ImageOwners) != 0 {
		//filterField := "image-id"
		ownersResult := &ec2.DescribeImagesOutput{}
		err = agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeImages", a.accountID, []interface{}{"", &ec2.DescribeImagesInput{
			Owners: awsOption.ImageOwners,
			Filters: []types.Filter{{
				Name:   aws.String("name"),
				Values: []string{input.ImageName},
			}},
		}}, ownersResult)
		if err != nil {
			return nil, err
		}
		result.Images = append(result.Images, ownersResult.Images...)
	}

	li := make([]*cloudman.ImageResp, 0)
	for _, v := range result.Images {
		name := aws.ToString(v.Name)
		for _, v := range v.Tags {
			if aws.ToString(v.Key) == "Name" {
				name = aws.ToString(v.Value)
			}
		}
		size := int32(0)
		if len(v.BlockDeviceMappings) > 0 {
			size = aws.ToInt32(v.BlockDeviceMappings[0].Ebs.VolumeSize)
		}
		li = append(li, &cloudman.ImageResp{
			OsName:    name,
			ImageName: aws.ToString(v.Name),
			ImageId:   aws.ToString(v.ImageId),
			OsType:    aws.ToString(v.PlatformDetails),
			IsPublic:  aws.ToBool(v.Public),
			Size:      size,
		})
	}
	return &cloudman.ImagesResp{List: li}, nil
}

// DescribeInstanceTypes DescribeInstanceTypes
func (a *AwsHost) DescribeInstanceTypes(ctx context.Context, input *common.DescribeInstanceTypesInput) (*cloudman.InstanceTypesResp, error) {
	var token *string
	var li []*cloudman.InstanceTypeResp
	for {
		result := &ec2.DescribeInstanceTypesOutput{}
		typeFilter := []types.Filter{{Name: aws.String("processor-info.supported-architecture"), Values: []string{"x86_64"}}}
		err := agentsdk.SyncCall(ctx, a.regionID, "host", "DescribeInstanceTypes", a.accountID, []interface{}{"", &ec2.DescribeInstanceTypesInput{
			DryRun:        nil,
			Filters:       typeFilter,
			InstanceTypes: nil,
			MaxResults:    tea.Int32(100),
			NextToken:     token,
		}}, result)
		if err != nil {
			return nil, err
		}

		for _, v := range result.InstanceTypes {
			li = append(li, &cloudman.InstanceTypeResp{
				Value:              "",
				Label:              "",
				MemorySize:         float32(tea.Int64Value(v.MemoryInfo.SizeInMiB) / 1024),
				CPUCoreCount:       tea.Int32Value(v.VCpuInfo.DefaultVCpus),
				InstanceTypeFamily: string(v.InstanceType),
				InstanceTypeID:     string(v.InstanceType),
			})
		}
		if result.NextToken == nil {
			break
		}
		token = result.NextToken
	}
	return &cloudman.InstanceTypesResp{List: li}, nil
}

// DescribeChargeType 获取计费类型
func (a *AwsHost) DescribeChargeType(ctx context.Context) (*cloudman.ChargeTypesResp, error) {
	// awsOption, err := cfg.GetAwsOptionConf()
	// if err != nil {
	// 	fmt.Printf("从Zes获取同步参数报错:%v\n", err)
	// 	return nil, err
	// }
	//
	// var li []*cloudman.ValueLabelResp
	// for _, kv := range awsOption.ChargeTypes {
	// 	li = append(li, &cloudman.ValueLabelResp{
	// 		Value: kv.Value,
	// 		Label: kv.Label,
	// 	})
	// }
	return &cloudman.ChargeTypesResp{List: []*cloudman.ValueLabelResp{
		{
			Label: "按量付费",
			Value: "PostPaid",
		},
	}}, nil
}
