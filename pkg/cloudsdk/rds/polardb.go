package rds

import (
	"context"
	"fmt"
	"strings"

	polardb******** "github.com/alibabacloud-go/polardb-********/v6/client"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/host"
)

// AliPolarDB polarDB
type AliPolarDB struct {
	regionID  string
	accountID string
}

// GetDBTypes 获取db类型
func (a AliPolarDB) GetDBTypes(ctx context.Context, input *AvailableDBTypesInput) (*cloudman.DBTypeResp, error) {
	return &cloudman.DBTypeResp{
		List: []*cloudman.DBType{
			{
				Engine: "Mysql",
				EngineVersion: []*cloudman.EngineVersion{{
					Version: "8.0",
					CategoryList: []*cloudman.DBCategory{
						{
							Category:        "basic",
							ClassCode:       engineClasses["mysql80|basic"],
							StorageTypeList: []string{"PSL5"},
						},
						{
							Category:        "HighAvailability",
							ClassCode:       engineClasses["mysql80|normal"],
							StorageTypeList: []string{"PSL5", "PSL4"},
						},
					},
				}, {
					Version: "5.7",
					CategoryList: []*cloudman.DBCategory{
						{
							Category:        "basic",
							ClassCode:       engineClasses["mysql57|basic"],
							StorageTypeList: []string{"PSL5"},
						},
						{
							Category:        "HighAvailability",
							ClassCode:       engineClasses["mysql57|normal"],
							StorageTypeList: []string{"PSL5"},
						},
					},
				}, {
					Version: "5.6",
					CategoryList: []*cloudman.DBCategory{
						{
							Category:        "basic",
							ClassCode:       engineClasses["mysql55|basic"],
							StorageTypeList: []string{"PSL5"},
						},
						{
							Category:        "HighAvailability",
							ClassCode:       engineClasses["mysql56|normal"],
							StorageTypeList: []string{"PSL5"},
						},
					},
				}},
			},
		},
	}, nil
}

func buildFilterName(engine, version string) string {
	engine = strings.ToLower(engine)
	version = strings.Join(strings.Split(version, "."), "")

	return fmt.Sprintf("%s%s", engine, version)
}

func buildClasses(classes []string) []*cloudman.DBClasses {
	var list []*cloudman.DBClasses
	for _, class := range classes {
		if result, ok := classesMap[class]; ok {

			list = append(list, &cloudman.DBClasses{
				Id:     class,
				Cpu:    strings.Split(result, "核")[0],
				Memory: strings.Split(result, "核")[1],
				Group:  strings.Split(result, "GB")[1],
			})
		}
	}
	return list
}

// GetAvailableClasses 获取数据库可用实例规格
func (a AliPolarDB) GetAvailableClasses(ctx context.Context, params *AvailableClassesInput) (*cloudman.DBClassesResp, error) {
	filterName := buildFilterName(params.Engine, params.EngineVersion)
	var list []*cloudman.DBClasses
	for k, v := range engineClasses {
		if strings.HasPrefix(k, filterName) {
			list = append(list, buildClasses(v)...)
		}
	}

	return &cloudman.DBClassesResp{List: list}, nil
}

// ListAvailableZone 获取可用区
func (a AliPolarDB) ListAvailableZone(ctx context.Context, params *AvailableZoneInput) (*cloudman.DBZoneResp, error) {
	req := &polardb********.DescribeRegionsRequest{}
	resp := &polardb********.DescribeRegionsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "DescribeRegions", a.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}

	output := &cloudman.DBZoneResp{
		List: []*cloudman.AvailableZone{},
	}
	for _, region := range resp.Body.Regions.Region {
		if *region.RegionId != a.regionID {
			continue
		}
		for _, zone := range region.Zones.Zone {
			output.List = append(output.List, &cloudman.AvailableZone{
				RegionId: *region.RegionId,
				ZoneId:   *zone.ZoneId,
				ZoneName: *zone.ZoneId,
			})
		}
	}
	return output, nil
}

// DescribeNetworks 获取VPC
func (a AliPolarDB) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeNetworks(ctx, input)
}

// DescribeSubnets 获取子网
func (a AliPolarDB) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeSubnets(ctx, input)
}

// DescribeSecurityGroups 获取安全组
func (a AliPolarDB) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeSecurityGroups(ctx, input)
}

func (a AliPolarDB) DescribeParameterGroups(ctx context.Context, params *common.DescribeParameterGroupsInput) (*cloudman.DBParamsGroupsResp, error) {
	req := &polardb********.DescribeParameterGroupsRequest{}
	req.SetDBType(params.EngineName)
	req.SetDBVersion(params.EngineVersion)
	req.SetRegionId(a.regionID)

	resp := &polardb********.DescribeParameterGroupsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "DescribeParameterGroups", a.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}

	output := &cloudman.DBParamsGroupsResp{
		List: []*cloudman.DBParamsGroup{},
	}
	for _, p := range resp.Body.ParameterGroups {
		output.List = append(output.List, &cloudman.DBParamsGroup{
			Id:   *p.ParameterGroupId,
			Name: *p.ParameterGroupName,
			Desc: *p.ParameterGroupDesc,
		})
	}
	return output, nil
}

func (a AliPolarDB) ListAllGlobalSecurityIPGroup(ctx context.Context) (*polardb********.DescribeGlobalSecurityIPGroupResponse, error) {
	req := polardb********.DescribeGlobalSecurityIPGroupRequest{}

	req.SetRegionId(a.regionID)
	res := &polardb********.DescribeGlobalSecurityIPGroupResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "DescribeGlobalSecurityIPGroup", a.accountID, []interface{}{&req}, res)
	return res, err
}

func (a AliPolarDB) DescribeIPWhiteList(ctx context.Context, params *common.DescribeNetworksInput) (*cloudman.IPWhiteListResp, error) {
	res, err := a.ListAllGlobalSecurityIPGroup(ctx)
	if err != nil {
		return nil, err
	}

	output := &cloudman.IPWhiteListResp{
		List: []*cloudman.IPWhiteListInfo{},
	}

	for _, ipGroup := range res.Body.GlobalSecurityIPGroup {
		output.List = append(output.List, &cloudman.IPWhiteListInfo{
			TemplateId:   *ipGroup.GlobalSecurityGroupId,
			TemplateName: *ipGroup.GlobalIgName,
		})
	}
	return output, nil
}

// 实例规格描述
var classesMap = map[string]string{"polar.mysql.mmx4.large": "4核16 GB（独享）", "polar.mysql.mmx4.xlarge": "8核32 GB（独享）", "polar.mysql.mmx8.xlarge": "8核64 GB（独享）", "polar.mysql.mmx8.2xlarge": "16核128 GB（独享）", "polar.mysql.mmx8.4xlarge": "32核256 GB（独享）", "polar.mysql.mmx8.8xlarge": "64核512 GB（独享）", "polar.mysql.mmx8.12xlarge": "88核710 GB（独享）", "polar.mysql.mmg2.large": "4核8 GB（通用）", "polar.mysql.mmg4.large": "4核16 GB（通用）", "polar.mysql.mmg2.xlarge": "8核16 GB（通用）", "polar.mysql.mmg4.xlarge": "8核32 GB（通用）", "polar.mysql.mmg4.2xlarge": "16核64 GB（通用）", "polar.mysql.mmg4.4xlarge": "32核128 GB（通用）", "polar.mysql.ax4.large": "4核16 GB（独享）", "polar.mysql.ax4.xlarge": "8核32 GB（独享）", "polar.mysql.ax8.xlarge": "8核64 GB（独享）", "polar.mysql.ax8.2xlarge": "16核128 GB（独享）", "polar.mysql.ax8.4xlarge": "32核256 GB（独享）", "polar.mysql.ax8.8xlarge": "64核512 GB（独享）", "polar.mysql.ax8.12xlarge": "88核710 GB（独享）", "polar.mysql.ag2.large": "4核8 GB（通用）", "polar.mysql.ag4.large": "4核16 GB（通用）", "polar.mysql.ag2.xlarge": "8核16 GB（通用）", "polar.mysql.ag4.xlarge": "8核32 GB（通用）", "polar.mysql.ag4.2xlarge": "16核64 GB（通用）", "polar.mysql.ag4.4xlarge": "32核128 GB（通用）", "polar.mysql.x8.medium": "2 核 16 GB（独享）", "polar.mysql.x4.large": "4 核 16 GB（独享）", "polar.mysql.x8.large": "4 核 32 GB（独享）", "polar.mysql.x4.xlarge": "8 核 32 GB（独享）", "polar.mysql.x8.xlarge": "8 核 64 GB（独享）", "polar.mysql.x4.2xlarge": "16核64 GB（独享）", "polar.mysql.x8.2xlarge": "16 核 128 GB（独享）", "polar.mysql.x4.4xlarge": "32核128 GB（独享）", "polar.mysql.x8.4xlarge": "32 核 256 GB（独享）", "polar.mysql.x8.8xlarge": "64 核 512 GB（独享）", "polar.mysql.x8.12xlarge": "88 核 710 GB（独占物理机）", "polar.mysql.x4.medium": "2 核 8 GB（独享）", "polar.pg.x4.large": "4 核 16 GB（独享）", "polar.pg.x4.xlarge": "8 核 32 GB（独享）", "polar.pg.x8.xlarge": "8 核 64 GB（独享）", "polar.pg.x8.2xlarge": "16 核 128 GB（独享）", "polar.pg.x8.4xlarge": "32 核 256 GB（独享）", "polar.pg.x8.8xlarge": "64 核512 GB（独享）", "polar.pg.x8.12xlarge": "88 核 710 GB（独享）", "polar.pg.x4.medium": "2 核 8 GB（独享）", "polar.o.x4.large": "4 核 16 GB（独享）", "polar.o.x4.xlarge": "8 核 32 GB（独享）", "polar.o.x8.xlarge": "8 核 64 GB（独享）", "polar.o.x8.2xlarge": "16 核 128 GB（独享）", "polar.o.x8.4xlarge": "32 核 256 GB（独享）", "polar.o.x8.8xlarge": "64 核512 GB（独享）", "polar.o.x8.12xlarge": "88 核 710 GB（独享）", "polar.o.x4.medium": "2 核 8 GB（独享）", "polar.mysql.x4.large.3": "4 核 16 GB（独享）", "polar.mysql.x8.12xlarge.3": "88 核 710 GB（独享）", "polar.mysql.s2.large": "4 核 8 GB（突发性能）", "polar.mysql.a4.large": "4核 16GB（独享）", "polar.mysql.a4.xlarge": "8核 32GB（独享）", "polar.mysql.a8.xlarge": "8核 64GB（独享）", "polar.mysql.a8.2xlarge": "16核 128GB（独享）", "polar.mysql.a8.4xlarge": "32核 256GB（独享）", "polar.mysql.a8.8xlarge": "64核 512GB（独享）", "polar.mysql.a8.12xlarge": "88核 710GB（独享）", "polar.mysql.g2.large": "4 核 8GB（通用）", "polar.mysql.g2.medium": "2 核 4GB（通用）", "polar.mysql.g4.medium": "2 核 8GB（通用）", "polar.mysql.g4.large": "4 核 16GB（通用）", "polar.mysql.g2.xlarge": "8 核 16GB（通用）", "polar.mysql.g4.xlarge": "8 核 32GB（通用）", "polar.mysql.g4.2xlarge": "16核 64GB（通用）", "polar.mysql.g6.2xlarge": "16核 96GB（通用）", "polar.mysql.g8.2xlarge": "16核 128GB（通用）", "polar.mysql.g4.4xlarge": "32核 128GB（通用）"}

// 引擎支持的系列
var engineClasses = map[string][]string{
	"mysql57|normal": {
		"polar.mysql.g2.large",
		"polar.mysql.g2.medium",
		"polar.mysql.g4.medium",
		"polar.mysql.g4.large",
		"polar.mysql.g2.xlarge",
		"polar.mysql.g4.xlarge",
		"polar.mysql.g4.2xlarge",
		"polar.mysql.g6.2xlarge",
		"polar.mysql.g8.2xlarge",
		"polar.mysql.g4.4xlarge",
		"polar.mysql.x8.medium",
		"polar.mysql.x4.large",
		"polar.mysql.x8.large",
		"polar.mysql.x4.xlarge",
		"polar.mysql.x8.xlarge",
		"polar.mysql.x4.2xlarge",
		"polar.mysql.x8.2xlarge",
		"polar.mysql.x4.4xlarge",
		"polar.mysql.x8.4xlarge",
		"polar.mysql.x8.8xlarge",
		"polar.mysql.x8.12xlarge",
		"polar.mysql.x4.medium",
	},
	"mysql57|basic": {
		"polar.mysql.s2.large",
	},
	"mysql56|normal": {
		"polar.mysql.x8.medium",
		"polar.mysql.x4.large",
		"polar.mysql.x8.large",
		"polar.mysql.x4.xlarge",
		"polar.mysql.x8.xlarge",
		"polar.mysql.x4.2xlarge",
		"polar.mysql.x8.2xlarge",
		"polar.mysql.x4.4xlarge",
		"polar.mysql.x8.4xlarge",
		"polar.mysql.x8.8xlarge",
		"polar.mysql.x8.12xlarge",
		"polar.mysql.x4.medium",
		"polar.mysql.g2.large",
		"polar.mysql.g2.medium",
		"polar.mysql.g4.medium",
		"polar.mysql.g4.large",
		"polar.mysql.g2.xlarge",
		"polar.mysql.g4.xlarge",
		"polar.mysql.g4.2xlarge",
		"polar.mysql.g6.2xlarge",
		"polar.mysql.g8.2xlarge",
		"polar.mysql.g4.4xlarge",
	},
	"mysql56|basic": {
		"polar.mysql.s2.large",
	},
	"mysql80|normal": {
		"polar.mysql.x8.medium",
		"polar.mysql.x4.large",
		"polar.mysql.x8.large",
		"polar.mysql.x4.xlarge",
		"polar.mysql.x8.xlarge",
		"polar.mysql.x4.2xlarge",
		"polar.mysql.x8.2xlarge",
		"polar.mysql.x4.4xlarge",
		"polar.mysql.x8.4xlarge",
		"polar.mysql.x8.8xlarge",
		"polar.mysql.x8.12xlarge",
		"polar.mysql.x4.medium",
		"polar.mysql.g2.large",
		"polar.mysql.g2.medium",
		"polar.mysql.g4.medium",
		"polar.mysql.g4.large",
		"polar.mysql.g2.xlarge",
		"polar.mysql.g4.xlarge",
		"polar.mysql.g4.2xlarge",
		"polar.mysql.g6.2xlarge",
		"polar.mysql.g8.2xlarge",
		"polar.mysql.g4.4xlarge",
	},
	"mysql80|basic": {
		"polar.mysql.s2.large",
	},
}

// GetAliPolarDB init-polarDB
func GetAliPolarDB(ctx context.Context, regionID, accountID string) (*AliPolarDB, error) {
	return &AliPolarDB{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}
