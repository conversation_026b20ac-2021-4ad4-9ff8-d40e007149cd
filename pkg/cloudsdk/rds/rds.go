package rds

import (
	"context"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// RDS inter
type RDS interface {
	GetDBTypes(ctx context.Context, input *AvailableDBTypesInput) (*cloudman.DBTypeResp, error)
	// GetAvailableClasses 获取实例规格
	GetAvailableClasses(ctx context.Context, params *AvailableClassesInput) (*cloudman.DBClassesResp, error)
	// ListAvailableZone 获取可用区
	ListAvailableZone(ctx context.Context, params *AvailableZoneInput) (*cloudman.DBZoneResp, error)
	DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error)
	DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error)
	DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error)

	DescribeParameterGroups(ctx context.Context, params *common.DescribeParameterGroupsInput) (*cloudman.DBParamsGroupsResp, error)
	DescribeIPWhiteList(ctx context.Context, params *common.DescribeNetworksInput) (*cloudman.IPWhiteListResp, error)
}

// GetRDS 获取RDS-SDK
func GetRDS(ctx context.Context, ispType, regionID, accountID string) (RDS, error) {
	switch ispType {
	case constant.IspAws:
		return GetAWSRds(ctx, regionID, accountID)
	case constant.IspAliyun:
		return GetAliPolarDB(ctx, regionID, accountID)
	default:
		return nil, fmt.Errorf("云厂商（%s）暂不支持", ispType)
	}
}

// AvailableClassesInput 获取实例可用区Input
type AvailableClassesInput struct {
	Engine             string
	EngineVersion      string
	InstanceChargeType string
}

// AvailableZoneInput 获取可用区
type AvailableZoneInput struct {
	RegionID string
}

// AvailableDBTypesInput 获取db类型input
type AvailableDBTypesInput struct {
	Engine        string
	EngineVersion string
	ZoneID        string // 可用区ID
	// RegionID      string
}
