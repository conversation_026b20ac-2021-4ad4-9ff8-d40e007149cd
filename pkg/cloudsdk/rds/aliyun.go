package rds

import (
	"context"
	"fmt"
	"strings"

	rds******** "github.com/alibabacloud-go/rds-********/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/host"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// AliRDS RDS只支持polarDB
type AliRDS struct {
	regionID  string
	accountID string
}

// GetDBTypes 获取数据库类型
func (a AliRDS) GetDBTypes(ctx context.Context, params *AvailableDBTypesInput) (*cloudman.DBTypeResp, error) {
	// TODO implement me
	request := &rds********.DescribeAvailableZonesRequest{
		Engine:        tea.String(params.Engine),
		EngineVersion: tea.String(params.EngineVersion),
		RegionId:      tea.String(a.regionID),
		ZoneId:        tea.String(params.ZoneID),
	}
	resp := &rds********.DescribeAvailableZonesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "DescribeAvailableZones", a.accountID, []interface{}{request}, resp)
	if err != nil || len(resp.Body.AvailableZones) == 0 {
		return nil, fmt.Errorf("error: 遇到错误(%v)或者可用区为空", err)
	}

	var list []*cloudman.DBType
	for _, v := range resp.Body.AvailableZones[0].SupportedEngines {
		var versions []*cloudman.EngineVersion
		for _, ver := range v.SupportedEngineVersions {
			var categoryList []*cloudman.DBCategory
			for _, category := range ver.SupportedCategorys {
				categoryList = append(categoryList, &cloudman.DBCategory{
					Category: tea.StringValue(category.Category),
					StorageTypeList: func() []string {
						var l []string
						for _, st := range category.SupportedStorageTypes {
							l = append(l, tea.StringValue(st.StorageType))
						}
						return l
					}(),
				})
			}
			versions = append(versions, &cloudman.EngineVersion{
				Version:      tea.StringValue(ver.Version),
				CategoryList: categoryList,
			})
		}

		list = append(list, &cloudman.DBType{
			Engine:        tea.StringValue(v.Engine),
			EngineVersion: versions,
		})
	}

	return &cloudman.DBTypeResp{List: list}, nil
}

// ListAvailableZone 获取可用区
func (a AliRDS) ListAvailableZone(ctx context.Context, params *AvailableZoneInput) (*cloudman.DBZoneResp, error) {
	resp := &rds********.DescribeRegionsResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "DescribeRegions", a.accountID, []interface{}{&rds********.DescribeRegionsRequest{
		AcceptLanguage: tea.String("zh-CN"),
	}}, resp)
	if err != nil {
		return nil, err
	}
	var list []*cloudman.AvailableZone
	for _, v := range resp.Body.Regions.RDSRegion {
		list = append(list, &cloudman.AvailableZone{
			RegionId: tea.StringValue(v.RegionId),
			ZoneId:   tea.StringValue(v.ZoneId),
			ZoneName: tea.StringValue(v.ZoneName),
		})
	}

	return &cloudman.DBZoneResp{List: list}, nil
}

// DescribeNetworks 获取VPC
func (a AliRDS) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeNetworks(ctx, input)
}

// DescribeSubnets 获取子网
func (a AliRDS) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeSubnets(ctx, input)
}

// DescribeSecurityGroups 获取安全组
func (a AliRDS) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeSecurityGroups(ctx, input)
}

// GetAliRDS 获取AliRDS
func GetAliRDS(ctx context.Context, regionID, accountID string) (*AliRDS, error) {
	return &AliRDS{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

// GetAvailableClasses 获取可用实例列表
func (a AliRDS) GetAvailableClasses(ctx context.Context, params *AvailableClassesInput) (*cloudman.DBClassesResp, error) {
	req := rds********.ListClassesRequest{
		OrderType: tea.String("BUY"),
	}
	if params.InstanceChargeType == constant.PrePaid {
		req.CommodityCode = tea.String("rds") // 主实例包年包月
	} else {
		req.CommodityCode = tea.String("bards") // 主实例按量付费
	}
	resp := &rds********.ListClassesResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "ListClasses", a.accountID, []interface{}{req}, resp)
	if err != nil {
		return nil, err
	}
	var list []*cloudman.DBClasses
	for _, v := range resp.Body.Items {
		if strings.Contains(tea.StringValue(v.ClassCode), strings.ToLower(params.Engine)) {
			list = append(list, &cloudman.DBClasses{
				Id:     tea.StringValue(v.ClassCode),
				Cpu:    tea.StringValue(v.Cpu),
				Memory: tea.StringValue(v.MemoryClass),
				Group:  tea.StringValue(v.ClassGroup),
			})
		}
	}
	return &cloudman.DBClassesResp{
		List: list,
	}, nil
	// resp.AvailableZones[0].SupportedEngines.SupportedEngine[0].SupportedEngineVersions.SupportedEngineVersion
}
