package rds

import (
	"context"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"github.com/aws/aws-sdk-go-v2/service/ec2"
	ec2Types "github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go-v2/service/rds"
	rdsTypes "github.com/aws/aws-sdk-go-v2/service/rds/types"
	"github.com/aws/aws-sdk-go/aws"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/host"
)

// AWSRds aws-rds(仅支持Aurora)
type AWSRds struct {
	regionID  string
	accountID string
}

// GetDBTypes 获取db类型
func (A AWSRds) GetDBTypes(ctx context.Context, input *AvailableDBTypesInput) (*cloudman.DBTypeResp, error) {
	resp := &rds.DescribeDBEngineVersionsOutput{}
	err := agentsdk.SyncCall(ctx, A.regionID, "mysql", "DescribeDBEngineVersions", A.accountID, []interface{}{"", &rds.DescribeDBEngineVersionsInput{
		Engine:  aws.String("aurora-mysql"),
		Filters: nil,
	}}, resp)
	if err != nil {
		return nil, err
	}

	dbType := map[string][]*cloudman.EngineVersion{}
	engineDescribe := map[string]struct{}{}
	for _, v := range resp.DBEngineVersions {
		engineName := aws.StringValue(v.Engine)
		version := aws.StringValue(v.EngineVersion)
		if _, exists := engineDescribe[version]; exists {
			continue
		}
		engineDescribe[version] = struct{}{}
		if l, ok := dbType[engineName]; ok {
			dbType[engineName] = append(l, &cloudman.EngineVersion{
				Version:      version,
				CategoryList: nil,
			})
		} else {
			dbType[engineName] = []*cloudman.EngineVersion{{
				Version:      version,
				CategoryList: nil,
			}}
		}
	}

	var list []*cloudman.DBType
	for k, v := range dbType {
		list = append(list, &cloudman.DBType{
			Engine:        k,
			EngineVersion: v,
		})
	}

	return &cloudman.DBTypeResp{List: list}, nil
	// aws rds describe-orderable-db-instance-options --engine engine --engine-version version \
	//    --query "*[].{DBInstanceClass:DBInstanceClass,StorageType:StorageType}|[?StorageType=='gp2']|[].{DBInstanceClass:DBInstanceClass}" \
	//    --output text \
	//    --region region
}

// GetAvailableClasses 获取可用实例类型
func (A AWSRds) GetAvailableClasses(ctx context.Context, params *AvailableClassesInput) (*cloudman.DBClassesResp, error) {
	resp := &rds.DescribeOrderableDBInstanceOptionsOutput{}
	err := agentsdk.SyncCall(ctx, A.regionID, "mysql", "DescribeOrderableDBInstanceOptions", A.accountID, []interface{}{"", &rds.DescribeOrderableDBInstanceOptionsInput{
		Engine:        aws.String(params.Engine),
		EngineVersion: aws.String(params.EngineVersion),
	}}, resp)
	if err != nil {
		return nil, err
	}

	var dbInstances []string
	var DBClasses []*cloudman.DBClasses
	for _, v := range resp.OrderableDBInstanceOptions {
		// 先获取实例类型,再获取实例描述
		id := aws.StringValue(v.DBInstanceClass)
		// 这里因为数据较少,直接用数组判断去重
		if slices.Contains(dbInstances, id) {
			continue
		}
		dbInstances = append(dbInstances, id)
		DBClasses = append(DBClasses, &cloudman.DBClasses{
			Id: id,
		})
	}

	instanceMap, err := A.GetInstanceClasses(ctx, dbInstances)
	if err != nil {
		return nil, err
	}

	for _, v := range DBClasses {
		if res, ok := instanceMap[v.Id]; ok {
			v.Cpu = fmt.Sprintf("%d", aws.Int32Value(res.VCpuInfo.DefaultVCpus))
			v.Memory = fmt.Sprintf("%sGbi", strconv.FormatInt(aws.Int64Value(res.MemoryInfo.SizeInMiB)/1024, 10))
			v.Group = string(res.InstanceType)
		}
	}

	return &cloudman.DBClassesResp{List: DBClasses}, err
}

// GetInstanceClasses 获取指定实例规格
func (A AWSRds) GetInstanceClasses(ctx context.Context, instances []string) (map[string]ec2Types.InstanceTypeInfo, error) {
	// instances = ["db.t4g.medium"]
	// 批量查询, 如db.t4g.large传递查询参数为t4g*, 以加快查询速度
	// 对instances与合并
	//cli, err := awsec2.Client(ctx, A.regionID, A.accessKey, A.accessSecret)
	//if err != nil {
	//	return nil, err
	//}

	var filterInstance []string
	filterMapInstance := map[string]struct{}{}
	for _, instance := range instances {
		if resp := strings.Split(instance, "."); len(resp) >= 2 {
			// filterInstance = append(filterInstance, resp[1])
			mKey := fmt.Sprintf("%s*", resp[1])
			if _, ok := filterMapInstance[mKey]; !ok {
				filterMapInstance[mKey] = struct{}{}
				filterInstance = append(filterInstance, mKey)
			}
		}
	}

	var token *string
	var instanceTypeInfo []ec2Types.InstanceTypeInfo
	for {
		out := &ec2.DescribeInstanceTypesOutput{}
		err := agentsdk.SyncCall(ctx, A.regionID, "host", "DescribeInstanceTypes", A.accountID, []interface{}{"", &ec2.DescribeInstanceTypesInput{
			Filters: []ec2Types.Filter{
				{
					Name:   aws.String("instance-type"),
					Values: filterInstance,
				},
			},
			NextToken: token,
		}}, out)
		if err != nil {
			return nil, err
		}
		instanceTypeInfo = append(instanceTypeInfo, out.InstanceTypes...)
		token = out.NextToken
		if token == nil {
			break
		}
	}

	m := map[string]ec2Types.InstanceTypeInfo{} // 根据后两位规格生成map加快查询
	for _, lMap := range instanceTypeInfo {
		mKey := fmt.Sprintf("db.%s", string(lMap.InstanceType))
		m[mKey] = lMap
	}

	return m, nil
}

// ListAvailableZone 获取可用区
func (A AWSRds) ListAvailableZone(ctx context.Context, params *AvailableZoneInput) (*cloudman.DBZoneResp, error) {
	// resp, err := A.rdsClient.DescribeDBEngineVersions(ctx, &rds.DescribeDBEngineVersionsInput{
	// 	Engine:  aws.String("aurora-mysql"),
	// 	Filters: nil,
	// })
	// if err != nil {
	// 	return nil, err
	// }
	res := &ec2.DescribeAvailabilityZonesOutput{}
	err := agentsdk.SyncCall(ctx, A.regionID, "host", "DescribeAvailabilityZones", A.accountID, []interface{}{"", &ec2.DescribeAvailabilityZonesInput{}}, res)
	if err != nil {
		return nil, err
	}
	list := []*cloudman.AvailableZone{}
	for _, v := range res.AvailabilityZones {
		list = append(list, &cloudman.AvailableZone{
			ZoneId:   aws.StringValue(v.ZoneId),
			ZoneName: aws.StringValue(v.ZoneName),
		})
	}

	return &cloudman.DBZoneResp{List: list}, nil
}

// DescribeNetworks 获取vpc-network
func (A AWSRds) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	h, err := host.GetAliHost(ctx, A.regionID, A.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeNetworks(ctx, input)
}

// DescribeSubnets 获取子网
func (a AWSRds) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	res := &rds.DescribeDBSubnetGroupsOutput{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "DescribeDBSubnetGroups", a.accountID, []interface{}{"", &rds.DescribeDBSubnetGroupsInput{
		DBSubnetGroupName: nil,
		Filters: []rdsTypes.Filter{
			{
				Name:   aws.String("vpc-id"),
				Values: []string{input.VpcID},
			},
		},
	}}, res)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.VSwitchInfo
	for _, v := range res.DBSubnetGroups {
		if aws.StringValue(v.VpcId) == input.VpcID {
			list = append(list, &cloudman.VSwitchInfo{
				Status:      aws.StringValue(v.SubnetGroupStatus),
				VSwitchId:   aws.StringValue(v.DBSubnetGroupName),
				VSwitchName: aws.StringValue(v.DBSubnetGroupDescription),
			})
		}
	}

	return &cloudman.VSwitchResp{List: list}, nil
}

// DescribeSecurityGroups 获取安全组
func (A AWSRds) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	h, err := host.GetAwsHost(ctx, A.regionID, A.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeSecurityGroups(ctx, input)
}

// GetAWSRds init-rds
func GetAWSRds(ctx context.Context, regionID, accountID string) (*AWSRds, error) {
	return &AWSRds{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}

func (a AWSRds) DescribeParameterGroups(ctx context.Context, params *common.DescribeParameterGroupsInput) (*cloudman.DBParamsGroupsResp, error) {
	res := &rds.DescribeDBClusterParameterGroupsOutput{}
	err := agentsdk.SyncCall(ctx, a.regionID, "mysql", "DescribeDBClusterParameterGroups", a.accountID, []interface{}{"", &rds.DescribeDBClusterParameterGroupsInput{}}, res)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.DBParamsGroup
	for _, v := range res.DBClusterParameterGroups {
		list = append(list, &cloudman.DBParamsGroup{
			Id:   aws.StringValue(v.DBClusterParameterGroupName),
			Name: aws.StringValue(v.DBClusterParameterGroupName),
			Desc: aws.StringValue(v.Description),
		})
	}

	return &cloudman.DBParamsGroupsResp{
		List: list,
	}, nil
}

func (a AWSRds) DescribeIPWhiteList(ctx context.Context, params *common.DescribeNetworksInput) (*cloudman.IPWhiteListResp, error) {
	return &cloudman.IPWhiteListResp{}, nil
}
