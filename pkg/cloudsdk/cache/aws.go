package cache

import (
	"context"
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/host"
)

// AWSEsCache aws-cache
type AWSEsCache struct {
	regionID  string
	accountID string
}

// DescribeParamsGroups 参数组
func (A AWSEsCache) DescribeParamsGroups(ctx context.Context, input *common.DescribeParameterGroupsInput) (*cloudman.CacheParamsGroupsResp, error) {
	var cacheParameterGroupName *string = nil
	if input.GroupName != "" {
		cacheParameterGroupName = aws.String(input.GroupName)
	}
	resp := &elasticache.DescribeCacheParameterGroupsOutput{}
	err := agentsdk.SyncCall(ctx, A.regionID, "redis", "DescribeCacheParameterGroups", A.accountID, []interface{}{"", &elasticache.DescribeCacheParameterGroupsInput{
		CacheParameterGroupName: cacheParameterGroupName,
	}}, resp)
	if err != nil {
		return nil, err
	}

	// version需剔除小版本
	compVersion := strings.Split(input.EngineVersion, ".")
	version := input.EngineVersion
	if len(compVersion) >= 2 {
		version = compVersion[0]
	}
	cacheParameterGroupFamily := fmt.Sprintf("%s%s", strings.ToLower(input.EngineName), version)
	list := make([]*cloudman.CacheParamsGroup, 0)
	for _, group := range resp.CacheParameterGroups {
		if strings.HasPrefix(aws.ToString(group.CacheParameterGroupFamily), cacheParameterGroupFamily) {
			list = append(list, &cloudman.CacheParamsGroup{
				Name: aws.ToString(group.CacheParameterGroupName),
				Desc: aws.ToString(group.Description),
			})
		}
	}

	return &cloudman.CacheParamsGroupsResp{List: list}, err
}

// GetDBTypes 获取引擎类型及版本
func (A AWSEsCache) GetDBTypes(ctx context.Context) (*cloudman.DBTypeResp, error) {
	resp := &elasticache.DescribeCacheEngineVersionsOutput{}
	err := agentsdk.SyncCall(ctx, A.regionID, "redis", "DescribeCacheEngineVersions", A.accountID, []interface{}{"", &elasticache.DescribeCacheEngineVersionsInput{
		Engine: aws.String("Redis"),
	}}, resp)
	if err != nil {
		return nil, err
	}
	var engineVersions []*cloudman.EngineVersion
	for _, v := range resp.CacheEngineVersions {
		engineVersions = append(engineVersions, &cloudman.EngineVersion{
			Version:      aws.ToString(v.EngineVersion),
			CategoryList: nil,
		})
	}

	return &cloudman.DBTypeResp{List: []*cloudman.DBType{
		{
			Engine:        "Redis",
			EngineVersion: engineVersions,
		},
	}}, nil
}

// GetAvailableClasses 获取可用实例类型
func (A AWSEsCache) GetAvailableClasses(ctx context.Context, input *common.AvailableClassesInput) (*cloudman.CacheClassesResp, error) {
	return &cloudman.CacheClassesResp{List: []*cloudman.CacheClasses{
		{Id: "cache.r7g.large", Memory: "13.07 GiB"},
		{Id: "cache.r7g.xlarge", Memory: "26.32 GiB"},
		{Id: "cache.r7g.2xlarge", Memory: "52.82 GiB"},
		{Id: "cache.r7g.4xlarge", Memory: "105.81 GiB"},
		{Id: "cache.r7g.8xlarge", Memory: "209.55 GiB"},
		{Id: "cache.r7g.12xlarge", Memory: "317.77 GiB"},
		{Id: "cache.r7g.16xlarge", Memory: "419.09 GiB"},
		{Id: "cache.m7g.large", Memory: "6.38 GiB"},
		{Id: "cache.m7g.xlarge", Memory: "12.93 GiB"},
		{Id: "cache.m7g.2xlarge", Memory: "26.04 GiB"},
		{Id: "cache.m7g.4xlarge", Memory: "52.26 GiB"},
		{Id: "cache.m7g.8xlarge", Memory: "103.68 GiB"},
		{Id: "cache.m7g.12xlarge", Memory: "157.12 GiB"},
		{Id: "cache.m7g.16xlarge", Memory: "209.55 GiB"},
		{Id: "cache.r6g.large", Memory: "13.07 GiB"},
		{Id: "cache.r6g.xlarge", Memory: "26.32 GiB"},
		{Id: "cache.r6g.2xlarge", Memory: "52.82 GiB"},
		{Id: "cache.r6g.4xlarge", Memory: "105.81 GiB"},
		{Id: "cache.r6g.8xlarge", Memory: "209.55 GiB"},
		{Id: "cache.r6g.12xlarge", Memory: "317.77 GiB"},
		{Id: "cache.r6g.16xlarge", Memory: "419.09 GiB"},
		{Id: "cache.m6g.large", Memory: "6.38 GiB"},
		{Id: "cache.m6g.xlarge", Memory: "12.93 GiB"},
		{Id: "cache.m6g.2xlarge", Memory: "26.04 GiB"},
		{Id: "cache.m6g.4xlarge", Memory: "52.26 GiB"},
		{Id: "cache.m6g.8xlarge", Memory: "103.68 GiB"},
		{Id: "cache.m6g.12xlarge", Memory: "157.12 GiB"},
		{Id: "cache.m6g.16xlarge", Memory: "209.55 GiB"},
		{Id: "cache.t4g.micro", Memory: "0.5 GiB"},
		{Id: "cache.t4g.small", Memory: "1.37 GiB"},
		{Id: "cache.t4g.medium", Memory: "3.09 GiB"},
		{Id: "cache.r5.large", Memory: "13.07 GiB"},
		{Id: "cache.r5.xlarge", Memory: "26.32 GiB"},
		{Id: "cache.r5.2xlarge", Memory: "52.82 GiB"},
		{Id: "cache.r5.4xlarge", Memory: "105.81 GiB"},
		{Id: "cache.r5.12xlarge", Memory: "317.77 GiB"},
		{Id: "cache.r5.24xlarge", Memory: "635.61 GiB"},
		{Id: "cache.m5.large", Memory: "6.38 GiB"},
		{Id: "cache.m5.xlarge", Memory: "12.93 GiB"},
		{Id: "cache.m5.2xlarge", Memory: "26.04 GiB"},
		{Id: "cache.m5.4xlarge", Memory: "52.26 GiB"},
		{Id: "cache.m5.12xlarge", Memory: "157.12 GiB"},
		{Id: "cache.m5.24xlarge", Memory: "314.32 GiB"},
		{Id: "cache.r4.large", Memory: "12.3 GiB"},
		{Id: "cache.r4.xlarge", Memory: "25.05 GiB"},
		{Id: "cache.r4.2xlarge", Memory: "50.47 GiB"},
		{Id: "cache.r4.4xlarge", Memory: "101.38 GiB"},
		{Id: "cache.r4.8xlarge", Memory: "203.26 GiB"},
		{Id: "cache.r4.16xlarge", Memory: "407 GiB"},
		{Id: "cache.m4.large", Memory: "6 GiB"},
		{Id: "cache.m4.xlarge", Memory: "14 GiB"},
		{Id: "cache.m4.2xlarge", Memory: "30 GiB"},
		{Id: "cache.m4.4xlarge", Memory: "61 GiB"},
		{Id: "cache.m4.10xlarge", Memory: "155 GiB"},
		{Id: "cache.t3.micro", Memory: "0.5 GiB"},
		{Id: "cache.t3.small", Memory: "1.37 GiB"},
		{Id: "cache.t3.medium", Memory: "3.09 GiB"},
		{Id: "cache.t2.micro", Memory: "0.5 GiB"},
		{Id: "cache.t2.small", Memory: "1.5 GiB"},
		{Id: "cache.t2.medium", Memory: "3.22 GiB"},
	}}, nil
}

// ListAvailableZone 获取可用区
func (A AWSEsCache) ListAvailableZone(ctx context.Context, input *common.DescribeZoneInput) (*cloudman.DBZoneResp, error) {
	// resp, err := A.client.DescribeCacheSubnetGroups(ctx, &elasticache.DescribeCacheSubnetGroupsInput{})
	// if err != nil {
	// 	return nil, err
	// }
	// if err != nil {
	// 	return nil, err
	// }
	// var list []*cloudman.AvailableZone
	// for _, v := range resp.CacheSubnetGroups {
	//
	// }
	return &cloudman.DBZoneResp{List: []*cloudman.AvailableZone{}}, nil
}

// DescribeNetworks 获取VPC
func (A AWSEsCache) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	return &cloudman.NetworkResp{List: []*cloudman.NetworkInfo{}}, nil
}

// DescribeSubnets 获取子网
func (A AWSEsCache) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	resp := &elasticache.DescribeCacheSubnetGroupsOutput{}
	err := agentsdk.SyncCall(ctx, A.regionID, "redis", "DescribeCacheSubnetGroups", A.accountID, []interface{}{"", &elasticache.DescribeCacheSubnetGroupsInput{}}, resp)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.VSwitchInfo
	for _, v := range resp.CacheSubnetGroups {
		// for _, vSwitch := range v.Subnets {
		list = append(list, &cloudman.VSwitchInfo{
			Status:      "",
			VSwitchId:   aws.ToString(v.CacheSubnetGroupName),
			VSwitchName: aws.ToString(v.CacheSubnetGroupName),
		})
		// }
	}

	return &cloudman.VSwitchResp{List: list}, nil
}

// DescribeSecurityGroups 获取安全组
func (A AWSEsCache) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	h, err := host.GetAwsHost(ctx, A.regionID, A.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeSecurityGroups(ctx, input)
}

func (A AWSEsCache) describeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	resp := &elasticache.DescribeCacheSecurityGroupsOutput{}
	err := agentsdk.SyncCall(ctx, A.regionID, "redis", "DescribeCacheSecurityGroups", A.accountID, []interface{}{"", &elasticache.DescribeCacheSecurityGroupsInput{}}, resp)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.SecurityGroupInfo
	for _, v := range resp.CacheSecurityGroups {
		list = append(list, &cloudman.SecurityGroupInfo{
			SecurityGroupName: aws.ToString(v.CacheSecurityGroupName),
		})
	}

	return &cloudman.SecurityGroupResp{List: list}, nil
}

// GetAWSEsCache 获取aws-elastiCache
func GetAWSEsCache(ctx context.Context, regionID, accountID string) (*AWSEsCache, error) {
	return &AWSEsCache{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}
