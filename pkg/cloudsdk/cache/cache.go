package cache

import (
	"context"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// Cache 获取Cache-inter
type Cache interface {
	GetDBTypes(ctx context.Context) (*cloudman.DBTypeResp, error)
	// GetAvailableClasses 获取实例规格
	GetAvailableClasses(ctx context.Context, input *common.AvailableClassesInput) (*cloudman.CacheClassesResp, error)
	// ListAvailableZone 获取可用区
	ListAvailableZone(ctx context.Context, input *common.DescribeZoneInput) (*cloudman.DBZoneResp, error)
	DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error)
	DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error)
	DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error)
	DescribeParamsGroups(ctx context.Context, input *common.DescribeParameterGroupsInput) (*cloudman.CacheParamsGroupsResp, error)
}

// GetCache 获取Cache-SDK
func GetCache(ctx context.Context, ispType, regionID, accountID string) (Cache, error) {
	switch ispType {
	case constant.IspAws:
		return GetAWSEsCache(ctx, regionID, accountID)
	case constant.IspAliyun:
		return GetAliKvStore(ctx, regionID, accountID)
	default:
		return nil, fmt.Errorf("云厂商（%s）暂不支持", ispType)
	}
}
