package cache

import (
	"context"
	"fmt"

	kvStore******** "github.com/alibabacloud-go/r-kvstore-********/v7/client"
	"github.com/alibabacloud-go/tea/tea"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/host"
)

// AliKvStore AliKvStore-cache
type AliKvStore struct {
	regionID  string
	accountID string
}

// DescribeParamsGroups 参数组
func (a AliKvStore) DescribeParamsGroups(ctx context.Context, input *common.DescribeParameterGroupsInput) (*cloudman.CacheParamsGroupsResp, error) {
	return &cloudman.CacheParamsGroupsResp{List: []*cloudman.CacheParamsGroup{}}, nil
}

// GetDBTypes 获取数据库类型
func (a AliKvStore) GetDBTypes(ctx context.Context) (*cloudman.DBTypeResp, error) {
	resp := &kvStore********.DescribeAvailableResourceResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "redis", "DescribeAvailableResource", a.accountID, []interface{}{&kvStore********.DescribeAvailableResourceRequest{
		AcceptLanguage:     tea.String("zh-CN"),
		Engine:             tea.String("Redis"),
		InstanceChargeType: tea.String("PrePaid"),
		OrderType:          tea.String("BUY"),
		ProductType:        tea.String("Local"),
		RegionId:           tea.String(a.regionID),
		ZoneId:             nil,
	}}, resp)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.DBType
	for _, engine := range resp.Body.AvailableZones.AvailableZone[0].SupportedEngines.SupportedEngine {
		var engineVersion []*cloudman.EngineVersion
		for _, version := range engine.SupportedEditionTypes.SupportedEditionType[0].SupportedSeriesTypes.SupportedSeriesType[0].SupportedEngineVersions.SupportedEngineVersion {
			var category []*cloudman.DBCategory
			for _, resources := range version.SupportedArchitectureTypes.SupportedArchitectureType {
				category = append(category, &cloudman.DBCategory{
					Category:  tea.StringValue(resources.Architecture),
					ClassCode: []string{},
				})
			}
			engineVersion = append(engineVersion, &cloudman.EngineVersion{
				Version:      tea.StringValue(version.Version),
				CategoryList: category,
			})
		}
		list = append(list, &cloudman.DBType{
			Engine:        "Redis",
			EngineVersion: engineVersion,
		})
	}

	return &cloudman.DBTypeResp{List: list}, nil
}

// GetAvailableClasses 获取可用实例列表
func (a AliKvStore) GetAvailableClasses(ctx context.Context, input *common.AvailableClassesInput) (*cloudman.CacheClassesResp, error) {
	// 参数引擎版本5.0, 是否为集群
	resp := &kvStore********.DescribeAvailableResourceResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "redis", "DescribeAvailableResource", a.accountID, []interface{}{&kvStore********.DescribeAvailableResourceRequest{
		AcceptLanguage:     tea.String("zh-CN"),
		Engine:             tea.String(input.EngineName),
		InstanceChargeType: tea.String("PrePaid"),
		OrderType:          tea.String("BUY"),
		ProductType:        tea.String("Local"),
		RegionId:           tea.String(a.regionID),
		ZoneId:             tea.String(input.ZoneID),
	}}, resp)
	if err != nil {
		return nil, err
	}

	var list []*cloudman.CacheClasses
	for _, zone := range resp.Body.AvailableZones.AvailableZone {
		zoneID := tea.StringValue(zone.ZoneId)
		for _, engine := range zone.SupportedEngines.SupportedEngine {
			if tea.StringValue(engine.Engine) != "redis" {
				continue
			}
			for _, editionType := range engine.SupportedEditionTypes.SupportedEditionType {
				// if tea.StringValue(editionType.EditionType) != "Community" {
				// 	continue
				// }
				for _, seriesType := range editionType.SupportedSeriesTypes.SupportedSeriesType {
					for _, version := range seriesType.SupportedEngineVersions.SupportedEngineVersion {
						if tea.StringValue(version.Version) != input.EngineVersion {
							continue
						}
						for _, architectureType := range version.SupportedArchitectureTypes.SupportedArchitectureType {
							architecture := tea.StringValue(architectureType.Architecture)
							if architecture == "rwsplit" {
								continue
							}
							for _, supportedShard := range architectureType.SupportedShardNumbers.SupportedShardNumber {
								shardNum := tea.StringValue(supportedShard.ShardNumber)
								for _, supportedNodeType := range supportedShard.SupportedNodeTypes.SupportedNodeType {
									nodeType := tea.StringValue(supportedNodeType.SupportedNodeType)
									for _, availableResource := range supportedNodeType.AvailableResources.AvailableResource {
										capacity := tea.Int64Value(availableResource.Capacity)
										if capacity == 0 {
											continue
										}
										memory := fmt.Sprintf("%dGB", capacity/1024)
										if capacity < 1024 {
											memory = fmt.Sprintf("%dMB", capacity)
										}
										list = append(list, &cloudman.CacheClasses{
											Id:       tea.StringValue(availableResource.InstanceClass),
											Remark:   tea.StringValue(availableResource.InstanceClassRemark),
											ShardNum: shardNum,
											Memory:   memory,
											Group:    architecture,
											ZoneId:   zoneID,
											NodeType: nodeType,
										})
									}
								}
							}
						}
					}
				}
			}
		}
	}

	return &cloudman.CacheClassesResp{List: list}, nil
}

// ListAvailableZone 获取可用区
func (a AliKvStore) ListAvailableZone(ctx context.Context, input *common.DescribeZoneInput) (*cloudman.DBZoneResp, error) {
	resp := &kvStore********.DescribeAvailableResourceResponse{}
	err := agentsdk.SyncCall(ctx, a.regionID, "redis", "DescribeAvailableResource", a.accountID, []interface{}{&kvStore********.DescribeAvailableResourceRequest{
		AcceptLanguage:     tea.String("zh-CN"),
		Engine:             tea.String("Redis"),
		InstanceChargeType: tea.String("PrePaid"),
		OrderType:          tea.String("BUY"),
		ProductType:        tea.String("Local"),
		RegionId:           tea.String(a.regionID),
	}}, resp)
	if err != nil {
		return nil, err
	}
	var list []*cloudman.AvailableZone
	for _, availableZone := range resp.Body.AvailableZones.AvailableZone {
		list = append(list, &cloudman.AvailableZone{
			ZoneId:   tea.StringValue(availableZone.ZoneId),
			ZoneName: tea.StringValue(availableZone.ZoneName),
		})
	}

	return &cloudman.DBZoneResp{List: list}, nil
}

// DescribeNetworks 获取VPC
func (a AliKvStore) DescribeNetworks(ctx context.Context, input *common.DescribeNetworksInput) (*cloudman.NetworkResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeNetworks(ctx, input)
}

// DescribeSubnets 获取子网
func (a AliKvStore) DescribeSubnets(ctx context.Context, input *common.DescribeSubnetsInput) (*cloudman.VSwitchResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}
	return h.DescribeSubnets(ctx, input)
}

// DescribeSecurityGroups 获取安全组
func (a AliKvStore) DescribeSecurityGroups(ctx context.Context, input *common.DescribeSecurityGroupInput) (*cloudman.SecurityGroupResp, error) {
	h, err := host.GetAliHost(ctx, a.regionID, a.accountID)
	if err != nil {
		return nil, err
	}

	return h.DescribeSecurityGroups(ctx, input)
}

// GetAliKvStore 获取阿里云kvStore
func GetAliKvStore(ctx context.Context, regionID, accountID string) (*AliKvStore, error) {
	return &AliKvStore{
		regionID:  regionID,
		accountID: accountID,
	}, nil
}
