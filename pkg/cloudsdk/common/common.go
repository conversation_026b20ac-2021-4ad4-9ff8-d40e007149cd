package common

// DescribeNetworksInput DescribeNetworksInput
type DescribeNetworksInput struct {
}

// DescribeSubnetsInput DescribeSubnetsInput
type DescribeSubnetsInput struct {
	VpcID  string
	ZoneID string
}

// DescribeSecurityGroupInput DescribeSecurityGroupInput
type DescribeSecurityGroupInput struct {
	Size  int32
	VpcID string
}

// DescribeParameterGroupsInput 获取参数组列表
type DescribeParameterGroupsInput struct {
	GroupName     string
	EngineName    string
	EngineVersion string
}

// DescribeIPWhiteListInput 获取ip白名单模版列表请求
type DescribeIPWhiteListInput struct {
	GroupName     string
	EngineName    string
	EngineVersion string
}

// DescribeZoneInput 获取可用区
type DescribeZoneInput struct {
	SubnetName string // 子网名称
	ChargeType string // 付费模式
}

// DescribeInstanceTypesInput 描述InstanceType查询条件
type DescribeInstanceTypesInput struct {
	ImageID string // 镜像id
	ZoneID  string
}

// DescribeKeyPairsInput DescribeKeyPairsInput
type DescribeKeyPairsInput struct {
	KeyName string
}

// DescribeVolumeTypesInput DescribeVolumeTypesInput
type DescribeVolumeTypesInput struct {
	ImageID string
}

// DescribeImagesInput 描述Image查询条件
type DescribeImagesInput struct {
	OsType    string
	ImageName string
	IsPublic  bool
}

// GetOSType 获取系统类型
func (d DescribeImagesInput) GetOSType() string {
	if d.OsType == "windows" {
		return "Windows"
	}

	return "Linux/UNIX"
}

// PageCommon 通用分页
type PageCommon struct {
	Page int32
	Size int32
}

// GetPage 获取页码
func (p PageCommon) GetPage() int32 {
	if p.Page != 0 {
		return p.Page
	}

	return 1
}

// GetSize 获取分页大小
func (p PageCommon) GetSize() int32 {
	if p.Size != 0 {
		return p.Page
	}

	return 30
}

// AvailableClassesInput 可用实例参数
type AvailableClassesInput struct {
	EngineName    string // 引擎名称
	EngineVersion string // 引擎版本
	Architecture  string // 架构类型
	ChargeType    string // 付费模式,aws不支持
	ZoneID        string // 可用区
	ProductType   string // 产品类型
}

// GetEngineName 获取引擎名称
func (a AvailableClassesInput) GetEngineName() string {
	if a.EngineName == "" {
		return "Redis"
	}
	return a.EngineName
}

// PriceInput 主机价格查询表单
type PriceInput struct {
	ResourceType    string // 资源类型
	ImageID         string // 镜像id
	InstanceType    string // 实例规格
	ChargeType      string // 付费类型
	InternetMaxBand int32  // 外网流量
	SystemDisk      Disk   // 系统磁盘设置
	DataDisk        []Disk // 数据盘设置
	Amount          int32  // 数量
	Period          int32  // 计费时长
	PriceUnit       string // 计费周期
}

// Disk 主机磁盘
type Disk struct {
	Size             int32  // 容量
	Category         string // 类型
	PerformanceLevel string // 性能等级
}
