package lb

import (
	"context"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// LB inter
type LB interface {
	DescribeZones(ctx context.Context) (*cloudman.DescribeZonesRes, error)
	// ListAllALBAcls(ctx context.Context) (*cloudman.ListAClsRes, error)
	ListCertificates(ctx context.Context) (*cloudman.ListCertificatesRes, error)
}

// GetLB 获取LB-SDK
func GetLB(ctx context.Context, ispType, regionID, accountID string) (LB, error) {
	switch ispType {
	case constant.IspAws:
		return GetAwsLB(ctx, regionID, accountID)
		// return nil, fmt.Errorf("云厂商（%s）暂不支持", ispType)
	case constant.IspAliyun:
		return GetAliLB(ctx, regionID, accountID)
	default:
		return nil, fmt.Errorf("云厂商（%s）暂不支持", ispType)
	}
}
