package task

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// Runner 任务执行控制器
type Runner struct {
	TaskID string `json:"task_id"`
}

// NewTask 创建新的agent_task
func NewTask(ctx context.Context, accountID, ispName string, resourceType string, methodName string, key entity.STSKey, timeout time.Duration, risky int32) (*Runner, error) {
	key.STSResult.Ak, _ = utils.EncryptStrByAes(key.STSResult.Ak, constant.AccountSecret)
	key.STSResult.Sk, _ = utils.EncryptStrByAes(key.STSResult.Sk, constant.AccountSecret)
	key.STSResult.Token, _ = utils.EncryptStrByAes(key.STSResult.Token, constant.AccountSecret)
	atEntity := &entity.AgentTask{
		AccountID:    accountID,
		IspName:      ispName,
		ResourceType: resourceType,
		MethodName:   methodName,
		Status:       StatusCreate,
		Key:          key,
		Operator:     permission.GetUsername(ctx),
		Extra: entity.AgentTaskExtra{
			Timeout: int64(timeout),
			Risky:   risky,
		},
	}
	taskID, err := models.AgentTaskModel.Create(ctx, atEntity)
	return &Runner{
		TaskID: taskID,
	}, err
}

// SyncCall 执行同步调用
// outarg1:全量结果数组；outarg2:出参中的第一个error类型参数转string；outarg3：调用过程中内部错误
func (a *Runner) SyncCall(ctx context.Context, useAgent bool, reqPayload []string) ([]string, error, error) {
	var err error
	//if useAgent {
	//	err = a.call(ctx, RunTypeSync, reqPayload)
	//}
	err = a.callLocal(ctx, RunTypeLocal, reqPayload)

	if err != nil {
		return nil, nil, err
	}
	// 等待mongodb副本同步
	time.Sleep(15 * time.Millisecond)
	at, err := models.AgentTaskModel.FindPK(ctx, a.TaskID)
	if err != nil {
		return nil, nil, err
	}
	if at.ErrMsg != "" {
		return nil, nil, fmt.Errorf("internal error: %s", at.ErrMsg)
	}
	respPayload := make([]string, 0)
	err = json.Unmarshal([]byte(at.ResponseDump), &respPayload)
	if err != nil {
		logrus.Warnf("SyncCall.resp.unmarshal.error: %s", err.Error())
		return nil, nil, err
	}
	var businessError error
	if at.Extra.BusinessErrMsg != "" {
		feErr := ""
		feErr, _ = sjson.Set(feErr, "fe_dialog", true)
		feErr, _ = sjson.Set(feErr, "isp_name", at.IspName)
		feErr, _ = sjson.Set(feErr, "method_name", at.MethodName)
		feErr, _ = sjson.Set(feErr, "err_msg", at.Extra.BusinessErrMsg)
		businessError = fmt.Errorf(feErr)
	}
	return respPayload, businessError, nil
}

// AsyncCallback 回调函数类型-WIP
type AsyncCallback func(ctx context.Context, client *Runner, respPayload []string)

func (a *Runner) callLocal(ctx context.Context, runType int32, reqPayload []string) error {
	// 获取数据库信息并更新
	at, err := models.AgentTaskModel.FindPK(ctx, a.TaskID)
	if err != nil {
		logrus.Warnf("TaskCallLocal.invalid_taskid: %s", err.Error())
		return err
	}
	at.Status = StatusPending
	at.RunType = runType
	reqBin, _ := json.Marshal(reqPayload)
	at.RequestDump = string(reqBin)
	at.Extra.SubmitTime = time.Now().UnixNano()
	at.Extra.ServerHost = "local"
	at.Extra.ServerAddr = "127.0.0.1"
	at.Extra.AgentID = "local"
	at.Extra.AgentIP = "127.0.0.1"
	err = models.AgentTaskModel.CreateOrUpdate(ctx, at)
	if err != nil {
		logrus.Warnf("TaskCallLocal.update_db.failed: %s", err.Error())
		return err
	}
	// 开始构造反射基础对象
	var c interface{}
	key := at.Key
	key.STSResult.Ak, _ = utils.DecryptStrByAes(key.STSResult.Ak, constant.AccountSecret)
	key.STSResult.Sk, _ = utils.DecryptStrByAes(key.STSResult.Sk, constant.AccountSecret)
	key.STSResult.Token, _ = utils.DecryptStrByAes(key.STSResult.Token, constant.AccountSecret)
	if at.IspName == "aliyun" && at.ResourceType == "host" {
		c, err = aliyunHostClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "vpc" {
		c, err = aliyunVpcClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "hostV3" {
		c, err = aliyunHostV3Client(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "mysql" {
		c, err = aliyunMysqlClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "redis" {
		c, err = aliyunRedisClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "slb" {
		c, err = aliyunSLBClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "alb" {
		c, err = aliyunALBClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "cas" {
		c, err = aliyunCASClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "ram" {
		c, err = aliyunRamClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "resource" {
		c, err = aliyunResourceClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "ddosbgp" {
		c, err = aliyunDdosBGPClient(key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "dbs" {
		c, err = aliyunDBSClient(key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aliyun" && at.ResourceType == "common_mysql" {
		c, err = aliyunCommonMysqlClient(key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aws" && at.ResourceType == "host" {
		c, err = awsHostClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aws" && at.ResourceType == "mysql" {
		c, err = awsMysqlClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aws" && at.ResourceType == "redis" {
		c, err = awsRedisClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aws" && at.ResourceType == "lb" {
		c, err = awsLBClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aws" && at.ResourceType == "acm" {
		c, err = awsACMClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aws" && at.ResourceType == "ssm" {
		c, err = awsSSMClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "aws" && at.ResourceType == "cloudwatch" {
		c, err = awsCloudWatchClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else if at.IspName == "mihoyo" && at.ResourceType == "host" {
		c, err = mihoyoHostClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token, at.AccountID)
	} else if at.IspName == "jumpserver" && at.ResourceType == "host" {
		c, err = jumpserverHostClient(ctx, key.STSApply.Region, key.STSResult.Ak, key.STSResult.Sk, key.STSResult.Token)
	} else {
		at.ErrMsg = fmt.Sprintf("TaskCallLocal.init_client.unknown, isp=(%s) resource_type=(%s)", at.IspName, at.ResourceType)
		at.Status = StatusFailed
		_ = models.AgentTaskModel.CreateOrUpdate(ctx, at)
		return fmt.Errorf("TaskCallLocal.init_client.unknown, isp=(%s) resource_type=(%s)", at.IspName, at.ResourceType)
	}
	if err != nil {
		at.ErrMsg = fmt.Sprintf("TaskCallLocal.init_client.error, %s", err)
		at.Status = StatusFailed
		_ = models.AgentTaskModel.CreateOrUpdate(ctx, at)
		return fmt.Errorf("TaskCallLocal.init_client.error, %w", err)
	}
	// 调用反射执行
	at.Extra.StartTime = time.Now().UnixNano()
	out, bErr, err := execute(ctx, c, at.MethodName, reqPayload)
	at.Extra.EndTime = time.Now().UnixNano()
	if err != nil {
		at.ErrMsg = err.Error()
		at.Status = StatusFailed
		_ = models.AgentTaskModel.CreateOrUpdate(ctx, at)
		logrus.WithField("task_id", a.TaskID).Warnf("TaskCallLocal.execute.error: %s", err.Error())
		return fmt.Errorf("TaskCallLocal.execute.error, %w", err)
	}
	at.Status = StatusSuccess
	respBin, _ := json.Marshal(out)
	at.ResponseDump = string(respBin)
	at.Extra.CallbackTime = time.Now().UnixNano()
	at.Extra.BusinessErrMsg = bErr
	err = models.AgentTaskModel.CreateOrUpdate(ctx, at)
	if err != nil {
		logrus.WithField("task_id", a.TaskID).Warnf("TaskCallLocal.write_resp.error: %s", err.Error())
	}
	logrus.WithField("task_id", a.TaskID).Infof("TaskCallLocal.done")
	return nil
}
