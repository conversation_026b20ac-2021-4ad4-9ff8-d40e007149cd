package task

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	slb20140515 "github.com/alibabacloud-go/slb-20140515/v4/client"

	alb20200616 "github.com/alibabacloud-go/alb-20200616/v2/client"
	cas20200407 "github.com/alibabacloud-go/cas-20200407/v2/client"
	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	openapiv2 "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dbs20210101 "github.com/alibabacloud-go/dbs-20210101/v3/client"
	ddosbgp20180720 "github.com/alibabacloud-go/ddosbgp-20180720/v3/client"
	ecs20140526 "github.com/alibabacloud-go/ecs-20140526/v2/client"
	ecs20140526v3 "github.com/alibabacloud-go/ecs-20140526/v3/client"
	polardb20170801 "github.com/alibabacloud-go/polardb-20170801/v6/client"
	r_kvstore20150101 "github.com/alibabacloud-go/r-kvstore-20150101/v7/client"
	ram20150501 "github.com/alibabacloud-go/ram-20150501/v2/client"
	resourcemanager20200331 "github.com/alibabacloud-go/resourcemanager-20200331/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	vpc20160428 "github.com/alibabacloud-go/vpc-20160428/v6/client"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	"github.com/aws/aws-sdk-go-v2/service/rds"
	"github.com/aws/aws-sdk-go/aws"
	credentials_old "github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/acm"
	"github.com/aws/aws-sdk-go/service/cloudwatch"
	"github.com/aws/aws-sdk-go/service/elbv2"
	"github.com/aws/aws-sdk-go/service/ssm"
	"github.com/sirupsen/logrus"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jumpserversdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mihoyosdk"
)

func execute(ctx context.Context, c interface{}, m string, in []string) (out []string, businessErrMsg string, outErr error) {
	defer func() {
		if panicErr := recover(); panicErr != nil {
			logrus.Errorf("panic recovered: %v", panicErr)
			outErr = fmt.Errorf("panic: %v", panicErr)
		}
	}()
	clientValue := reflect.ValueOf(c)
	mObj := clientValue.MethodByName(m)
	if !mObj.IsValid() {
		return nil, businessErrMsg, fmt.Errorf("invalid method %s", m)
	}
	mNumIn := mObj.Type().NumIn()
	if !mObj.Type().IsVariadic() && len(in) != mNumIn {
		// 最后一个参数为...的情况，不验证参数数量
		return nil, businessErrMsg, fmt.Errorf("input arguments num: expected %d, got %d", mNumIn, len(in))
	}
	if mObj.Type().IsVariadic() && mObj.Type().In(mNumIn-1).Kind() != reflect.Slice {
		// 可变函数时最后一个入参必须是slice
		return nil, businessErrMsg, fmt.Errorf("last input argument must be slice on variadic func, got %v", mObj.Type().In(mNumIn-1).Kind())
	}
	mIn := make([]reflect.Value, 0)
	for i := 0; i < len(in); i++ {
		var inType reflect.Type
		if mObj.Type().IsVariadic() && i >= mNumIn-1 {
			inType = mObj.Type().In(mNumIn - 1).Elem()
		} else {
			inType = mObj.Type().In(i)
		}
		if inType.String() == "context.Context" {
			mIn = append(mIn, reflect.ValueOf(ctx))
			continue
		}
		deptrFlag := false
		var v interface{}
		switch inType.Kind() {
		case reflect.Func, reflect.Chan:
			return nil, businessErrMsg, fmt.Errorf("unsupport arg type: %s", inType.Kind())
		case reflect.Ptr:
			// 类型为指针的，要从指针向下挖一层到指向类型，然后再包成指针送到json库
			v = reflect.New(inType.Elem()).Interface()
			deptrFlag = false
		default:
			// 类型为非指针的，直接包成指针送到json库，然后解这层指针
			v = reflect.New(inType).Interface()
			deptrFlag = true
		}

		jerr := json.Unmarshal([]byte(in[i]), v)
		if jerr != nil {
			logrus.Errorf(jerr.Error())
			return nil, businessErrMsg, jerr
		}
		if deptrFlag {
			mIn = append(mIn, reflect.ValueOf(v).Elem())
		} else {
			mIn = append(mIn, reflect.ValueOf(v))
		}
	}

	mOut := mObj.Call(mIn)
	result := make([]string, 0)
	for _, out := range mOut {
		if out.Kind() == reflect.Ptr ||
			out.Kind() == reflect.Map ||
			out.Kind() == reflect.Slice ||
			out.Kind() == reflect.Interface {
			if out.IsNil() {
				result = append(result, `null`)
				continue
			}
		}
		if out.Type().String() == "error" {
			var e error
			e = out.Interface().(error)
			result = append(result, e.Error())
			businessErrMsg = e.Error()
			continue
		}
		jb, _ := json.Marshal(out.Interface())
		result = append(result, string(jb))
	}
	return result, businessErrMsg, nil
}

func aliyunHostClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*ecs20140526.Client, error) {
	cfg := &openapi.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := ecs20140526.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunVpcClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*vpc20160428.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := vpc20160428.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunHostV3Client(ctx context.Context, regionID, accessKey, accessSecret, token string) (*ecs20140526v3.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := ecs20140526v3.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunMysqlClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*polardb20170801.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	// 这里us-east-1 sdk的endpoint有问题，需要hardcode一下
	if regionID == "us-east-1" {
		cfg.SetEndpoint("polardb.us-east-1.aliyuncs.com")
	}

	cli, err := polardb20170801.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunRedisClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*r_kvstore20150101.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := r_kvstore20150101.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunSLBClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*slb20140515.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := slb20140515.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunALBClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*alb20200616.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := alb20200616.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunCASClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*cas20200407.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := cas20200407.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunRamClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*ram20150501.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := ram20150501.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunResourceClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*resourcemanager20200331.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := resourcemanager20200331.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunDdosBGPClient(regionID, accessKey, accessSecret, token string) (*ddosbgp20180720.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := ddosbgp20180720.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunDBSClient(regionID, accessKey, accessSecret, token string) (*dbs20210101.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
	}

	cli, err := dbs20210101.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func aliyunCommonMysqlClient(regionID, accessKey, accessSecret, token string) (*openapiv2.Client, error) {
	cfg := &openapiv2.Config{
		AccessKeyId:     tea.String(accessKey),
		AccessKeySecret: tea.String(accessSecret),
		RegionId:        tea.String(regionID),
		SecurityToken:   tea.String(token),
		Endpoint:        tea.String("polardb.aliyuncs.com"),
	}
	if regionID == "eu-central-1" || regionID == "us-east-1" || regionID == "ap-northeast-1" || regionID == "ap-southeast-1" {
		cfg.Endpoint = tea.String(fmt.Sprintf("polardb.%s.aliyuncs.com", regionID))
	}

	cli, err := openapiv2.NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

func awsHostClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*ec2.Client, error) {
	cfg, err := config.LoadDefaultConfig(
		ctx,
		config.WithRegion(regionID),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, accessSecret, token)),
	)
	if err != nil {
		return nil, fmt.Errorf("创建AWS EC2 Client报错：%v", err)
	}
	return ec2.NewFromConfig(cfg), nil
}

func awsMysqlClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*rds.Client, error) {
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(regionID),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, accessSecret, token)),
	)
	if err != nil {
		return nil, fmt.Errorf("创建AWS RDS Client报错：%v", err)
	}
	return rds.NewFromConfig(cfg), nil
}

func awsRedisClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*elasticache.Client, error) {
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(regionID),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, accessSecret, token)),
	)
	if err != nil {
		return nil, fmt.Errorf("创建AWS elasticache Client报错：%v", err)
	}
	return elasticache.NewFromConfig(cfg), nil
}

func awsLBClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*elbv2.ELBV2, error) {
	sess := session.Must(session.NewSession(&aws.Config{Region: aws.String(regionID), Credentials: credentials_old.NewStaticCredentials(accessKey, accessSecret, token)}))
	return elbv2.New(sess), nil
}

func awsSSMClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*ssm.SSM, error) {
	sess := session.Must(session.NewSession(&aws.Config{Region: aws.String(regionID), Credentials: credentials_old.NewStaticCredentials(accessKey, accessSecret, token)}))
	cli := ssm.New(sess)
	return cli, nil
}

func awsACMClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*acm.ACM, error) {
	sess := session.Must(session.NewSession(&aws.Config{Region: aws.String(regionID), Credentials: credentials_old.NewStaticCredentials(accessKey, accessSecret, token)}))
	cli := acm.New(sess)
	return cli, nil
}

func awsCloudWatchClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*cloudwatch.CloudWatch, error) {
	sess := session.Must(session.NewSession(&aws.Config{Region: aws.String(regionID), Credentials: credentials_old.NewStaticCredentials(accessKey, accessSecret, token)}))
	cli := cloudwatch.New(sess)
	return cli, nil
}

func mihoyoHostClient(ctx context.Context, regionID, accessKey, accessSecret, token string, accountId string) (*mihoyosdk.MihoyoComputeClient, error) {
	// 需要获取project_id
	account, err := models.AccountModel.Get(ctx, accountId)
	if err != nil {
		return nil, err
	}
	cli := mihoyosdk.NewMihoyoComputeSdkWithAksk(accessKey, accessSecret, account.ProjectID, regionID)
	return cli, nil
}

func jumpserverHostClient(ctx context.Context, regionID, accessKey, accessSecret, token string) (*jumpserversdk.JumpserverClient, error) {
	cli := jumpserversdk.NewJumpserverSdk(accessKey, accessSecret)
	return cli, nil
}
