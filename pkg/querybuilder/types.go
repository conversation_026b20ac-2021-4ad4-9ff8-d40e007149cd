/*
 * <PERSON>cent is pleased to support the open source community by making 蓝鲸 available.
 * Copyright (C) 2017-2018 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except
 * in compliance with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions and
 * limitations under the License.
 */

package querybuilder

import (
	"fmt"
	"regexp"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// Rule 规则接口
type Rule interface {
	GetDeep() int
	Validate() (string, error)
	ToMgo() (mgoFilter map[string]interface{}, errKey string, err error)
	Match(matcher Matcher) bool
}

// Condition *************** define condition ************************
type Condition string

// Validate 验证合法
func (c Condition) Validate() error {
	if c == ConditionAnd || c == ConditionOr {
		return nil
	}
	return fmt.Errorf("unexpected condition: %s", c)
}

// ToMgo to mongo
func (c Condition) ToMgo() (mgoOperator string, err error) {
	switch c {
	case ConditionOr:
		return constant.MongoOR, nil
	case ConditionAnd:
		return constant.MongoAND, nil
	default:
		return "", fmt.Errorf("unexpected operator %s", c)
	}
}

var (
	// ConditionAnd and
	ConditionAnd = Condition("AND")
	// ConditionOr or
	ConditionOr = Condition("OR")
)

// Operator *************** define operator ************************
type Operator string

var (
	// OperatorEqual 相等
	OperatorEqual = Operator("equal")
	// OperatorNotEqual 不相等
	OperatorNotEqual = Operator("not_equal")

	// OperatorIn set-operator
	// OperatorIn in
	OperatorIn = Operator("in")
	// OperatorNotIn not in
	OperatorNotIn = Operator("not_in")

	// OperatorLess numeric compare
	// OperatorLess 小于
	OperatorLess = Operator("less")
	// OperatorLessOrEqual 小于或等于
	OperatorLessOrEqual = Operator("less_or_equal")
	// OperatorGreater greater
	OperatorGreater = Operator("greater")
	// OperatorGreaterOrEqual greater/eq
	OperatorGreaterOrEqual = Operator("greater_or_equal")

	// OperatorDatetimeLess datetime operate
	// OperatorDatetimeLess 时间小于
	OperatorDatetimeLess = Operator("datetime_less")
	// OperatorDatetimeLessOrEqual datetime_less_or_equal
	OperatorDatetimeLessOrEqual = Operator("datetime_less_or_equal")
	// OperatorDatetimeGreater datetime_greater
	OperatorDatetimeGreater = Operator("datetime_greater")
	// OperatorDatetimeGreaterOrEqual datetime_greater_or_equal
	OperatorDatetimeGreaterOrEqual = Operator("datetime_greater_or_equal")

	// OperatorBeginsWith string operator
	// OperatorBeginsWith begins_with
	OperatorBeginsWith = Operator("begins_with")
	// OperatorNotBeginsWith not_begins_with
	OperatorNotBeginsWith = Operator("not_begins_with")
	// OperatorContains contains
	OperatorContains = Operator("contains")
	// OperatorNotContains not_contains
	OperatorNotContains = Operator("not_contains")
	// OperatorsEndsWith ends_with
	OperatorsEndsWith = Operator("ends_with")
	// OperatorNotEndsWith not_ends_with
	OperatorNotEndsWith = Operator("not_ends_with")

	// OperatorIsEmpty array operator
	// OperatorIsEmpty is_empty
	OperatorIsEmpty = Operator("is_empty")
	// OperatorIsNotEmpty is_not_empty
	OperatorIsNotEmpty = Operator("is_not_empty")

	// OperatorIsNull  null check
	OperatorIsNull = Operator("is_null")
	// OperatorIsNotNull is_not_null
	OperatorIsNotNull = Operator("is_not_null")

	// OperatorExist exist check
	OperatorExist = Operator("exist")
	// OperatorNotExist not_exist
	OperatorNotExist = Operator("not_exist")
)

// SupportOperators 支持操作列表
var SupportOperators = map[Operator]bool{
	OperatorEqual:    true,
	OperatorNotEqual: true,

	OperatorIn:    true,
	OperatorNotIn: true,

	OperatorLess:           true,
	OperatorLessOrEqual:    true,
	OperatorGreater:        true,
	OperatorGreaterOrEqual: true,

	OperatorDatetimeLess:           false,
	OperatorDatetimeLessOrEqual:    false,
	OperatorDatetimeGreater:        false,
	OperatorDatetimeGreaterOrEqual: false,

	OperatorBeginsWith:    true,
	OperatorNotBeginsWith: true,
	OperatorContains:      true,
	OperatorNotContains:   true,
	OperatorsEndsWith:     true,
	OperatorNotEndsWith:   true,

	OperatorIsEmpty:    true,
	OperatorIsNotEmpty: true,

	OperatorIsNull:    true,
	OperatorIsNotNull: true,

	OperatorExist:    true,
	OperatorNotExist: true,
}

// Validate validate
func (op Operator) Validate() error {
	if support, ok := SupportOperators[op]; !support || !ok {
		return fmt.Errorf("unsupported operator: %s", op)
	}
	return nil
}

// AtomRule *************** define rule ************************
type AtomRule struct {
	Field    string      `json:"field"`
	Operator Operator    `json:"operator"`
	Value    interface{} `json:"value"`
}

// GetDeep deep
func (r AtomRule) GetDeep() int {
	return int(1)
}

// Validate validate
func (r AtomRule) Validate() (string, error) {
	if err := r.Operator.Validate(); err != nil {
		return "operator", err
	}
	if err := r.validateField(); err != nil {
		return "field", err
	}
	if err := r.validateValue(); err != nil {
		return "value", err
	}
	return "", nil
}

// Matcher match
type Matcher func(r AtomRule) bool

// Match match
func (r AtomRule) Match(matcher Matcher) bool {
	return matcher(r)
}

var (
	// ValidFieldPattern TODO: should we support dot field separator here?
	// ValidFieldPattern pattern
	ValidFieldPattern = regexp.MustCompile(`^[a-zA-Z0-9][\d\w\-_.]*$`)
)

func (r AtomRule) validateField() error {
	if !ValidFieldPattern.MatchString(r.Field) {
		return fmt.Errorf("invalid field: %s", r.Field)
	}
	return nil
}

func (r AtomRule) validateValue() error {
	switch r.Operator {
	case OperatorEqual, OperatorNotEqual:
		return validateBasicType(r.Value)
	case OperatorIn, OperatorNotIn:
		return validateSliceOfBasicType(r.Value, true)
	case OperatorLess, OperatorLessOrEqual, OperatorGreater, OperatorGreaterOrEqual:
		return validateNumericType(r.Value)
	case OperatorDatetimeLess, OperatorDatetimeLessOrEqual, OperatorDatetimeGreater, OperatorDatetimeGreaterOrEqual:
		return validateDatetimeStringType(r.Value)
	case OperatorBeginsWith, OperatorNotBeginsWith, OperatorContains, OperatorNotContains, OperatorsEndsWith, OperatorNotEndsWith:
		return validateNotEmptyStringType(r.Value)
	case OperatorIsEmpty, OperatorIsNotEmpty:
		return nil
	case OperatorIsNull, OperatorIsNotNull:
		return nil
	case OperatorExist, OperatorNotExist:
		return nil
	default:
		return fmt.Errorf("unsupported operator: %s", r.Operator)
	}
}

// ToMysql generate mysql filter from rule
func (r AtomRule) ToMysql() (mgoFiler map[string]interface{}, key string, err error) {
	return nil, "", err
}

// ToMgo generate mongo filter from rule
func (r AtomRule) ToMgo() (mgoFiler map[string]interface{}, key string, err error) {
	if key, err := r.Validate(); err != nil {
		return nil, key, fmt.Errorf("validate failed, key: %s, err: %s", key, err)
	}

	filter := make(map[string]interface{})
	switch r.Operator {
	case OperatorEqual:
		filter[r.Field] = map[string]interface{}{
			constant.MongoEQ: r.Value,
		}
	case OperatorNotEqual:
		filter[r.Field] = map[string]interface{}{
			constant.MongoNE: r.Value,
		}
	case OperatorIn:
		filter[r.Field] = map[string]interface{}{
			constant.MongoIN: r.Value,
		}
	case OperatorNotIn:
		filter[r.Field] = map[string]interface{}{
			constant.MongoNIN: r.Value,
		}
	case OperatorLess:
		filter[r.Field] = map[string]interface{}{
			constant.MongoLT: r.Value,
		}
	case OperatorLessOrEqual:
		filter[r.Field] = map[string]interface{}{
			constant.MongoLTE: r.Value,
		}
	case OperatorGreater:
		filter[r.Field] = map[string]interface{}{
			constant.MongoGT: r.Value,
		}
	case OperatorGreaterOrEqual:
		filter[r.Field] = map[string]interface{}{
			constant.MongoGTE: r.Value,
		}
	case OperatorDatetimeLess:
		t, err := time.Parse(time.RFC3339, r.Value.(string))
		if err != nil {
			return nil, "value", err
		}
		filter[r.Field] = map[string]interface{}{
			constant.MongoLT: t,
		}
	case OperatorDatetimeLessOrEqual:
		t, err := time.Parse(time.RFC3339, r.Value.(string))
		if err != nil {
			return nil, "value", err
		}
		filter[r.Field] = map[string]interface{}{
			constant.MongoLTE: t,
		}
	case OperatorDatetimeGreater:
		t, err := time.Parse(time.RFC3339, r.Value.(string))
		if err != nil {
			return nil, "value", err
		}
		filter[r.Field] = map[string]interface{}{
			constant.MongoGT: t,
		}
	case OperatorDatetimeGreaterOrEqual:
		t, err := time.Parse(time.RFC3339, r.Value.(string))
		if err != nil {
			return nil, "value", err
		}
		filter[r.Field] = map[string]interface{}{
			constant.MongoGTE: t,
		}
	case OperatorBeginsWith:
		filter[r.Field] = map[string]interface{}{
			constant.MongoLIKE: fmt.Sprintf("^%s", r.Value),
		}
	case OperatorNotBeginsWith:
		filter[r.Field] = map[string]interface{}{
			constant.MongoNot: map[string]interface{}{constant.MongoLIKE: fmt.Sprintf("^%s", r.Value)},
		}
	case OperatorContains:
		filter[r.Field] = map[string]interface{}{
			constant.MongoLIKE:    fmt.Sprintf("%s", r.Value),
			constant.MongoOPTIONS: "i",
		}
	case OperatorNotContains:
		filter[r.Field] = map[string]interface{}{
			constant.MongoNot: map[string]interface{}{constant.MongoLIKE: fmt.Sprintf("%s", r.Value)},
		}
	case OperatorsEndsWith:
		filter[r.Field] = map[string]interface{}{
			constant.MongoLIKE: fmt.Sprintf("%s$", r.Value),
		}
	case OperatorNotEndsWith:
		filter[r.Field] = map[string]interface{}{
			constant.MongoNot: map[string]interface{}{constant.MongoLIKE: fmt.Sprintf("%s$", r.Value)},
		}
	case OperatorIsEmpty:
		// array empty
		filter[r.Field] = map[string]interface{}{
			constant.MongoEQ: make([]interface{}, 0),
		}
	case OperatorIsNotEmpty:
		// array not empty
		filter[r.Field] = map[string]interface{}{
			constant.MongoNE: make([]interface{}, 0),
		}
	case OperatorIsNull:
		filter[r.Field] = map[string]interface{}{
			constant.MongoEQ: nil,
		}
	case OperatorIsNotNull:
		filter[r.Field] = map[string]interface{}{
			constant.MongoNE: nil,
		}
	case OperatorExist:
		filter[r.Field] = map[string]interface{}{
			constant.MongoExists: true,
		}
	case OperatorNotExist:
		filter[r.Field] = map[string]interface{}{
			constant.MongoExists: false,
		}
	default:
		return nil, "operator", fmt.Errorf("unsupported operator: %s", r.Operator)
	}
	return filter, "", nil
}

// CombinedRule *************** define query ************************
type CombinedRule struct {
	Condition Condition `json:"condition"`
	Rules     []Rule    `json:"rules"`
}

var (
	// MaxDeep 嵌套层级的深度按树的高度计算，查询条件最大深度为3即最多嵌套2层
	MaxDeep = 3
)

// GetDeep deep
func (r CombinedRule) GetDeep() int {
	maxChildDeep := 1
	for _, child := range r.Rules {
		childDeep := child.GetDeep()
		if childDeep > maxChildDeep {
			maxChildDeep = childDeep
		}
	}
	return maxChildDeep + 1
}

// Validate validate
func (r CombinedRule) Validate() (string, error) {
	if err := r.Condition.Validate(); err != nil {
		return "condition", err
	}
	if r.Rules == nil || len(r.Rules) == 0 {
		return "rules", fmt.Errorf("combined rules shouldn't be empty")
	}
	for idx, rule := range r.Rules {
		if key, err := rule.Validate(); err != nil {
			return fmt.Sprintf("rules[%d].%s", idx, key), err
		}
	}
	return "", nil
}

// ToMgo to-mongodb
func (r CombinedRule) ToMgo() (mgoFilter map[string]interface{}, key string, err error) {
	if err := r.Condition.Validate(); err != nil {
		return nil, "condition", err
	}
	if r.Rules == nil || len(r.Rules) == 0 {
		return nil, "rules", fmt.Errorf("combined rules shouldn't be empty")
	}
	filters := make([]map[string]interface{}, 0)
	for idx, rule := range r.Rules {
		filter, key, err := rule.ToMgo()
		if err != nil {
			return nil, fmt.Sprintf("rules[%d].%s", idx, key), err
		}
		filters = append(filters, filter)
	}
	mgoOperator, err := r.Condition.ToMgo()
	if err != nil {
		return nil, "condition", err
	}
	mgoFilter = map[string]interface{}{
		mgoOperator: filters,
	}
	return mgoFilter, "", nil
}

// Match match
func (r CombinedRule) Match(matcher Matcher) bool {
	if len(r.Rules) == 0 {
		return true
	}

	switch r.Condition {
	case ConditionAnd:
		for _, rule := range r.Rules {
			if rule.Match(matcher) == false {
				return false
			}
		}
		return true
	case ConditionOr:
		for _, rule := range r.Rules {
			if rule.Match(matcher) == true {
				return true
			}
		}
		return false
	default:
		panic(fmt.Sprintf("unexpected condition %s", r.Condition))
	}
}
