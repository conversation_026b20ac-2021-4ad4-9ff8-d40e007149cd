package permission

import (
	"context"
	"strings"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
)

// Inner ...
const (
	Inner     = "inner"
	InnerTrue = "true"
)

// CheckPermissionFromCtx ...
func CheckPermissionFromCtx(ctx context.Context, actionID, instanceID string) (bool, error) {
	return CheckPermission(ctx, actionID, instanceID, GetUsername(ctx))
}

// CheckPermission ...
func CheckPermission(ctx context.Context, actionID, instanceID, username string) (bool, error) {
	if cfg.GetSystemConfig().Env == "local" {
		return true, nil
	}
	inner, _ := ctx.Value(Inner).(string)
	if inner == InnerTrue {
		return true, nil
	}
	return cfg.IAMCliMap["cur"].HasPermissionWithErr(ctx, cfg.IAMServiceID, actionID, instanceID, username)
}

// IsAdminFromCtx ...
func IsAdminFromCtx(ctx context.Context) bool {
	return IsAdmin(ctx, GetUsername(ctx))
}

// IsAdmin ...
func IsAdmin(ctx context.Context, username string) bool {
	hasPerm, err := CheckPermission(ctx, "admin", "", username)
	if err != nil {
		return false
	}
	return hasPerm
}

// HasViewFromCtx ...
func HasViewFromCtx(ctx context.Context) bool {
	return HasView(ctx, GetUsername(ctx))
}

// HasView ...
func HasView(ctx context.Context, username string) bool {
	hasPerm, err := CheckPermission(ctx, "view", "", username)
	if err != nil {
		return false
	}
	return hasPerm
}

// GetUserUnionInstancesFromCtx ...
func GetUserUnionInstancesFromCtx(ctx context.Context, actions ...string) (bool, []string) {
	return GetUserUnionInstances(ctx, GetUsername(ctx), actions...)
}

// GetUserUnionInstances ...
func GetUserUnionInstances(ctx context.Context, username string, actions ...string) (bool, []string) {
	if cfg.GetSystemConfig().Env == "local" {
		return true, nil
	}
	var instancesList [][]string
	for _, v := range actions {
		permInstances, err := cfg.IAMCliMap["cur"].GetPermissionInstance(ctx, "op-cloudman-takumi", v, username)
		if err != nil {
			continue
		}
		allInstances := permInstances.AllowAllInstances
		instances := permInstances.Instances
		if allInstances {
			return true, []string{}
		}
		for k := range instances {
			splitedInstance := strings.Split(instances[k], ":")
			if len(splitedInstance) == 2 {
				instances[k] = splitedInstance[1]
			}
		}
		instancesList = append(instancesList, instances)
	}
	return false, UnionInstances([]string{}, instancesList...)
}

// UnionInstances ...
func UnionInstances(mainSet []string, sets ...[]string) []string {
	m := map[string]bool{}
	for _, v := range mainSet {
		m[v] = true
	}
	for _, set := range sets {
		for _, v := range set {
			m[v] = true
		}
	}
	var ret []string
	for k := range m {
		ret = append(ret, k)
	}
	return ret
}

// GetUsername 从rpc meta中获取用户名
func GetUsername(ctx context.Context) string {
	if ctx.Value("token") != nil {
		return ctx.Value("username").(string)
	}
	if cfg.GetSystemConfig().Env == "local" && cfg.IAMCliMap["cur"] == nil {
		return "local_user"
	}

	return ""
}
