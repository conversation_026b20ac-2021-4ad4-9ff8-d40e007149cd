package logger

import "fmt"

// getMessage format with Sprint, Sprintf, or neither.
func getMessage(template string, fmtArgs []interface{}) string {
	if len(fmtArgs) == 0 {
		return template
	}

	if template != "" {
		return fmt.Sprintf(template, fmtArgs...)
	}

	if len(fmtArgs) == 1 {
		if str, ok := fmtArgs[0].(string); ok {
			return str
		}
	}
	return fmt.Sprint(fmtArgs...)
}

func Debug(args ...interface{}) {
	Logger.Debug(getMessage("", args))
}

func Debugf(template string, args ...interface{}) {
	Logger.Debug(getMessage(template, args))
}

func Info(args ...interface{}) {
	Logger.Info(getMessage("", args))
}

func Println(args ...interface{}) {
	Logger.Info(getMessage("", args))
}

func Infof(template string, args ...interface{}) {
	Logger.Info(getMessage(template, args))
}

func Warn(args ...interface{}) {
	Logger.Warn(getMessage("", args))
}

func Warnf(template string, args ...interface{}) {
	Logger.Warn(getMessage(template, args))
}

func Error(args ...interface{}) {
	Logger.Error(getMessage("", args))

}

func Errorf(template string, args ...interface{}) {
	Logger.Error(getMessage(template, args))

}

func DPanic(args ...interface{}) {
	Logger.DPanic(getMessage("", args))
}

func DPanicf(template string, args ...interface{}) {
	Logger.DPanic(getMessage(template, args))

}

func Panic(args ...interface{}) {
	Logger.Panic(getMessage("", args))
}

func Panicf(template string, args ...interface{}) {
	Logger.Panic(getMessage(template, args))

}

func Fatal(args ...interface{}) {
	Logger.Fatal(getMessage("", args))

}

func Fatalf(template string, args ...interface{}) {
	Logger.Fatal(getMessage(template, args))
}
