package response

import (
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
)

type CommonResponse struct {
	Data    interface{} `json:"data"`
	Message string      `json:"message"`
	Retcode int         `json:"retcode"`
}

const SuccessCode int = 0

func Error(c *app.RequestContext, err error, retcode int) {
	c.J<PERSON>(consts.StatusOK, CommonResponse{
		Message: err.Error(),
		Retcode: retcode,
	})
}

func Data(c *app.RequestContext, data interface{}) {
	c.JSO<PERSON>(consts.StatusOK, CommonResponse{
		Data:    data,
		Message: "OK",
		Retcode: SuccessCode,
	})
}

func NoData(c *app.RequestContext) {
	c.JSON(consts.StatusOK, CommonResponse{
		Message: "OK",
		Retcode: SuccessCode,
	})
}
