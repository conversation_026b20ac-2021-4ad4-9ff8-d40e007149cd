// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: domain.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DomainEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DomainId         int32          `protobuf:"varint,1,opt,name=DomainId,proto3" form:"DomainId" json:"DomainId" query:"DomainId"`
	Name             string         `protobuf:"bytes,2,opt,name=Name,proto3" form:"Name" json:"Name" query:"Name"`
	Status           string         `protobuf:"bytes,3,opt,name=Status,proto3" form:"Status" json:"Status" query:"Status"`
	TTL              int32          `protobuf:"varint,4,opt,name=TTL,proto3" form:"TTL" json:"TTL" query:"TTL"`
	CNAMESpeedup     string         `protobuf:"bytes,5,opt,name=CNAMESpeedup,proto3" form:"CNAMESpeedup" json:"CNAMESpeedup" query:"CNAMESpeedup"`
	DNSStatus        string         `protobuf:"bytes,6,opt,name=DNSStatus,proto3" form:"DNSStatus" json:"DNSStatus" query:"DNSStatus"`
	Grade            string         `protobuf:"bytes,7,opt,name=Grade,proto3" form:"Grade" json:"Grade" query:"Grade"`
	GroupId          int32          `protobuf:"varint,8,opt,name=GroupId,proto3" form:"GroupId" json:"GroupId" query:"GroupId"`
	SearchEnginePush string         `protobuf:"bytes,9,opt,name=SearchEnginePush,proto3" form:"SearchEnginePush" json:"SearchEnginePush" query:"SearchEnginePush"`
	Remark           string         `protobuf:"bytes,10,opt,name=Remark,proto3" form:"Remark" json:"Remark" query:"Remark"`
	Punycode         string         `protobuf:"bytes,11,opt,name=Punycode,proto3" form:"Punycode" json:"Punycode" query:"Punycode"`
	EffectiveDNS     []string       `protobuf:"bytes,12,rep,name=EffectiveDNS,proto3" form:"EffectiveDNS" json:"EffectiveDNS" query:"EffectiveDNS"`
	GradeLevel       int32          `protobuf:"varint,13,opt,name=GradeLevel,proto3" form:"GradeLevel" json:"GradeLevel" query:"GradeLevel"`
	GradeTitle       string         `protobuf:"bytes,14,opt,name=GradeTitle,proto3" form:"GradeTitle" json:"GradeTitle" query:"GradeTitle"`
	IsVip            string         `protobuf:"bytes,15,opt,name=IsVip,proto3" form:"IsVip" json:"IsVip" query:"IsVip"`
	VipStartAt       string         `protobuf:"bytes,16,opt,name=VipStartAt,proto3" form:"VipStartAt" json:"VipStartAt" query:"VipStartAt"`
	VipEndAt         string         `protobuf:"bytes,17,opt,name=VipEndAt,proto3" form:"VipEndAt" json:"VipEndAt" query:"VipEndAt"`
	VipAutoRenew     string         `protobuf:"bytes,18,opt,name=VipAutoRenew,proto3" form:"VipAutoRenew" json:"VipAutoRenew" query:"VipAutoRenew"`
	RecordCount      int32          `protobuf:"varint,19,opt,name=RecordCount,proto3" form:"RecordCount" json:"RecordCount" query:"RecordCount"`
	CreatedOn        string         `protobuf:"bytes,20,opt,name=CreatedOn,proto3" form:"CreatedOn" json:"CreatedOn" query:"CreatedOn"`
	UpdatedOn        string         `protobuf:"bytes,21,opt,name=UpdatedOn,proto3" form:"UpdatedOn" json:"UpdatedOn" query:"UpdatedOn"`
	Owner            string         `protobuf:"bytes,22,opt,name=Owner,proto3" form:"Owner" json:"Owner" query:"Owner"`
	TagList          []*ResourceTag `protobuf:"bytes,23,rep,name=TagList,proto3" form:"TagList" json:"TagList" query:"TagList"`
	IspId            string         `protobuf:"bytes,24,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType          string         `protobuf:"bytes,25,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName          string         `protobuf:"bytes,26,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
	RecordList       []*Record      `protobuf:"bytes,27,rep,name=record_list,json=recordList,proto3" form:"record_list" json:"record_list" query:"record_list"`
	RegionId         string         `protobuf:"bytes,28,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *DomainEntity) Reset() {
	*x = DomainEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domain_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DomainEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainEntity) ProtoMessage() {}

func (x *DomainEntity) ProtoReflect() protoreflect.Message {
	mi := &file_domain_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainEntity.ProtoReflect.Descriptor instead.
func (*DomainEntity) Descriptor() ([]byte, []int) {
	return file_domain_proto_rawDescGZIP(), []int{0}
}

func (x *DomainEntity) GetDomainId() int32 {
	if x != nil {
		return x.DomainId
	}
	return 0
}

func (x *DomainEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DomainEntity) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DomainEntity) GetTTL() int32 {
	if x != nil {
		return x.TTL
	}
	return 0
}

func (x *DomainEntity) GetCNAMESpeedup() string {
	if x != nil {
		return x.CNAMESpeedup
	}
	return ""
}

func (x *DomainEntity) GetDNSStatus() string {
	if x != nil {
		return x.DNSStatus
	}
	return ""
}

func (x *DomainEntity) GetGrade() string {
	if x != nil {
		return x.Grade
	}
	return ""
}

func (x *DomainEntity) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *DomainEntity) GetSearchEnginePush() string {
	if x != nil {
		return x.SearchEnginePush
	}
	return ""
}

func (x *DomainEntity) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *DomainEntity) GetPunycode() string {
	if x != nil {
		return x.Punycode
	}
	return ""
}

func (x *DomainEntity) GetEffectiveDNS() []string {
	if x != nil {
		return x.EffectiveDNS
	}
	return nil
}

func (x *DomainEntity) GetGradeLevel() int32 {
	if x != nil {
		return x.GradeLevel
	}
	return 0
}

func (x *DomainEntity) GetGradeTitle() string {
	if x != nil {
		return x.GradeTitle
	}
	return ""
}

func (x *DomainEntity) GetIsVip() string {
	if x != nil {
		return x.IsVip
	}
	return ""
}

func (x *DomainEntity) GetVipStartAt() string {
	if x != nil {
		return x.VipStartAt
	}
	return ""
}

func (x *DomainEntity) GetVipEndAt() string {
	if x != nil {
		return x.VipEndAt
	}
	return ""
}

func (x *DomainEntity) GetVipAutoRenew() string {
	if x != nil {
		return x.VipAutoRenew
	}
	return ""
}

func (x *DomainEntity) GetRecordCount() int32 {
	if x != nil {
		return x.RecordCount
	}
	return 0
}

func (x *DomainEntity) GetCreatedOn() string {
	if x != nil {
		return x.CreatedOn
	}
	return ""
}

func (x *DomainEntity) GetUpdatedOn() string {
	if x != nil {
		return x.UpdatedOn
	}
	return ""
}

func (x *DomainEntity) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *DomainEntity) GetTagList() []*ResourceTag {
	if x != nil {
		return x.TagList
	}
	return nil
}

func (x *DomainEntity) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *DomainEntity) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *DomainEntity) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

func (x *DomainEntity) GetRecordList() []*Record {
	if x != nil {
		return x.RecordList
	}
	return nil
}

func (x *DomainEntity) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type Record struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordId      uint64 `protobuf:"varint,1,opt,name=RecordId,proto3" form:"RecordId" json:"RecordId" query:"RecordId"`
	Value         string `protobuf:"bytes,2,opt,name=Value,proto3" form:"Value" json:"Value" query:"Value"`
	Status        string `protobuf:"bytes,3,opt,name=Status,proto3" form:"Status" json:"Status" query:"Status"`
	UpdatedOn     string `protobuf:"bytes,4,opt,name=UpdatedOn,proto3" form:"UpdatedOn" json:"UpdatedOn" query:"UpdatedOn"`
	Name          string `protobuf:"bytes,5,opt,name=Name,proto3" form:"Name" json:"Name" query:"Name"`
	Line          string `protobuf:"bytes,6,opt,name=Line,proto3" form:"Line" json:"Line" query:"Line"`
	LineId        string `protobuf:"bytes,7,opt,name=LineId,proto3" form:"LineId" json:"LineId" query:"LineId"`
	Type          string `protobuf:"bytes,8,opt,name=Type,proto3" form:"Type" json:"Type" query:"Type"`
	Weight        uint64 `protobuf:"varint,9,opt,name=Weight,proto3" form:"Weight" json:"Weight" query:"Weight"`
	MonitorStatus string `protobuf:"bytes,10,opt,name=MonitorStatus,proto3" form:"MonitorStatus" json:"MonitorStatus" query:"MonitorStatus"`
	Remark        string `protobuf:"bytes,11,opt,name=Remark,proto3" form:"Remark" json:"Remark" query:"Remark"`
	TTL           uint64 `protobuf:"varint,12,opt,name=TTL,proto3" form:"TTL" json:"TTL" query:"TTL"`
	MX            uint64 `protobuf:"varint,13,opt,name=MX,proto3" form:"MX" json:"MX" query:"MX"`
	DefaultNS     bool   `protobuf:"varint,14,opt,name=DefaultNS,proto3" form:"DefaultNS" json:"DefaultNS" query:"DefaultNS"`
}

func (x *Record) Reset() {
	*x = Record{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domain_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Record) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Record) ProtoMessage() {}

func (x *Record) ProtoReflect() protoreflect.Message {
	mi := &file_domain_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Record.ProtoReflect.Descriptor instead.
func (*Record) Descriptor() ([]byte, []int) {
	return file_domain_proto_rawDescGZIP(), []int{1}
}

func (x *Record) GetRecordId() uint64 {
	if x != nil {
		return x.RecordId
	}
	return 0
}

func (x *Record) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Record) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Record) GetUpdatedOn() string {
	if x != nil {
		return x.UpdatedOn
	}
	return ""
}

func (x *Record) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Record) GetLine() string {
	if x != nil {
		return x.Line
	}
	return ""
}

func (x *Record) GetLineId() string {
	if x != nil {
		return x.LineId
	}
	return ""
}

func (x *Record) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Record) GetWeight() uint64 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Record) GetMonitorStatus() string {
	if x != nil {
		return x.MonitorStatus
	}
	return ""
}

func (x *Record) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *Record) GetTTL() uint64 {
	if x != nil {
		return x.TTL
	}
	return 0
}

func (x *Record) GetMX() uint64 {
	if x != nil {
		return x.MX
	}
	return 0
}

func (x *Record) GetDefaultNS() bool {
	if x != nil {
		return x.DefaultNS
	}
	return false
}

type DomainAddressOperationLocks struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LockReason string `protobuf:"bytes,1,opt,name=lock_reason,json=lockReason,proto3" form:"lock_reason" json:"lock_reason" query:"lock_reason"`
}

func (x *DomainAddressOperationLocks) Reset() {
	*x = DomainAddressOperationLocks{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domain_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DomainAddressOperationLocks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainAddressOperationLocks) ProtoMessage() {}

func (x *DomainAddressOperationLocks) ProtoReflect() protoreflect.Message {
	mi := &file_domain_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainAddressOperationLocks.ProtoReflect.Descriptor instead.
func (*DomainAddressOperationLocks) Descriptor() ([]byte, []int) {
	return file_domain_proto_rawDescGZIP(), []int{2}
}

func (x *DomainAddressOperationLocks) GetLockReason() string {
	if x != nil {
		return x.LockReason
	}
	return ""
}

type DescribeDomainReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp         string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId    string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	SearchKey   string `protobuf:"bytes,5,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue string `protobuf:"bytes,6,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
}

func (x *DescribeDomainReq) Reset() {
	*x = DescribeDomainReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domain_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDomainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDomainReq) ProtoMessage() {}

func (x *DescribeDomainReq) ProtoReflect() protoreflect.Message {
	mi := &file_domain_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDomainReq.ProtoReflect.Descriptor instead.
func (*DescribeDomainReq) Descriptor() ([]byte, []int) {
	return file_domain_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeDomainReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeDomainReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeDomainReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeDomainReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeDomainReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *DescribeDomainReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

type DescribeDomainRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32           `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*DomainEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeDomainRes) Reset() {
	*x = DescribeDomainRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domain_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDomainRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDomainRes) ProtoMessage() {}

func (x *DescribeDomainRes) ProtoReflect() protoreflect.Message {
	mi := &file_domain_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDomainRes.ProtoReflect.Descriptor instead.
func (*DescribeDomainRes) Descriptor() ([]byte, []int) {
	return file_domain_proto_rawDescGZIP(), []int{4}
}

func (x *DescribeDomainRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeDomainRes) GetList() []*DomainEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type DescribeDomainRecordsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	RecordValue string `protobuf:"bytes,3,opt,name=record_value,json=recordValue,proto3" form:"record_value" json:"record_value" query:"record_value"`
}

func (x *DescribeDomainRecordsReq) Reset() {
	*x = DescribeDomainRecordsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domain_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDomainRecordsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDomainRecordsReq) ProtoMessage() {}

func (x *DescribeDomainRecordsReq) ProtoReflect() protoreflect.Message {
	mi := &file_domain_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDomainRecordsReq.ProtoReflect.Descriptor instead.
func (*DescribeDomainRecordsReq) Descriptor() ([]byte, []int) {
	return file_domain_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeDomainRecordsReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeDomainRecordsReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeDomainRecordsReq) GetRecordValue() string {
	if x != nil {
		return x.RecordValue
	}
	return ""
}

type CreateDomainReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *CreateDomainReq) Reset() {
	*x = CreateDomainReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_domain_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDomainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDomainReq) ProtoMessage() {}

func (x *CreateDomainReq) ProtoReflect() protoreflect.Message {
	mi := &file_domain_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDomainReq.ProtoReflect.Descriptor instead.
func (*CreateDomainReq) Descriptor() ([]byte, []int) {
	return file_domain_proto_rawDescGZIP(), []int{6}
}

func (x *CreateDomainReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_domain_proto protoreflect.FileDescriptor

var file_domain_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x11, 0x72, 0x65, 0x73, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x06, 0x0a, 0x0c,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x54, 0x4c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x54, 0x54, 0x4c, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x4e, 0x41, 0x4d, 0x45, 0x53,
	0x70, 0x65, 0x65, 0x64, 0x75, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x4e,
	0x41, 0x4d, 0x45, 0x53, 0x70, 0x65, 0x65, 0x64, 0x75, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x4e,
	0x53, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x44,
	0x4e, 0x53, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x47, 0x72, 0x61, 0x64,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x50, 0x75, 0x73, 0x68, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1a, 0x0a, 0x08,
	0x50, 0x75, 0x6e, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x50, 0x75, 0x6e, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x4e, 0x53, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x4e, 0x53, 0x12, 0x1e, 0x0a, 0x0a,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x47, 0x72, 0x61, 0x64, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x47, 0x72, 0x61, 0x64, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x49, 0x73, 0x56, 0x69, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x73, 0x56,
	0x69, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x56, 0x69, 0x70, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x56, 0x69, 0x70, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x41, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x56, 0x69, 0x70, 0x45, 0x6e, 0x64, 0x41, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x56, 0x69, 0x70, 0x45, 0x6e, 0x64, 0x41, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x56, 0x69, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x56, 0x69, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x6e,
	0x65, 0x77, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4f,
	0x6e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x4f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x6e, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x6e,
	0x12, 0x14, 0x0a, 0x05, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x07, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x52,
	0x07, 0x54, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xda, 0x02, 0x0a, 0x06, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x4c, 0x69, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4c, 0x69, 0x6e, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x54, 0x54, 0x4c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x03, 0x54, 0x54, 0x4c, 0x12, 0x0e, 0x0a, 0x02, 0x4d, 0x58, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x02, 0x4d, 0x58, 0x12, 0x1c, 0x0a, 0x09, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4e,
	0x53, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x4e, 0x53, 0x22, 0x3e, 0x0a, 0x1b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x63, 0x6b,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0xac, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69,
	0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x55, 0x0a, 0x11, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x65, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x25, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69,
	0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c,
	0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_domain_proto_rawDescOnce sync.Once
	file_domain_proto_rawDescData = file_domain_proto_rawDesc
)

func file_domain_proto_rawDescGZIP() []byte {
	file_domain_proto_rawDescOnce.Do(func() {
		file_domain_proto_rawDescData = protoimpl.X.CompressGZIP(file_domain_proto_rawDescData)
	})
	return file_domain_proto_rawDescData
}

var file_domain_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_domain_proto_goTypes = []interface{}{
	(*DomainEntity)(nil),                // 0: cloudman.DomainEntity
	(*Record)(nil),                      // 1: cloudman.Record
	(*DomainAddressOperationLocks)(nil), // 2: cloudman.DomainAddressOperationLocks
	(*DescribeDomainReq)(nil),           // 3: cloudman.DescribeDomainReq
	(*DescribeDomainRes)(nil),           // 4: cloudman.DescribeDomainRes
	(*DescribeDomainRecordsReq)(nil),    // 5: cloudman.DescribeDomainRecordsReq
	(*CreateDomainReq)(nil),             // 6: cloudman.CreateDomainReq
	(*ResourceTag)(nil),                 // 7: cloudman.resource_tag
}
var file_domain_proto_depIdxs = []int32{
	7, // 0: cloudman.DomainEntity.TagList:type_name -> cloudman.resource_tag
	1, // 1: cloudman.DomainEntity.record_list:type_name -> cloudman.Record
	0, // 2: cloudman.DescribeDomainRes.list:type_name -> cloudman.DomainEntity
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_domain_proto_init() }
func file_domain_proto_init() {
	if File_domain_proto != nil {
		return
	}
	file_resTemplate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_domain_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DomainEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domain_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Record); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domain_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DomainAddressOperationLocks); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domain_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDomainReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domain_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDomainRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domain_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDomainRecordsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_domain_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDomainReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_domain_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_domain_proto_goTypes,
		DependencyIndexes: file_domain_proto_depIdxs,
		MessageInfos:      file_domain_proto_msgTypes,
	}.Build()
	File_domain_proto = out.File
	file_domain_proto_rawDesc = nil
	file_domain_proto_goTypes = nil
	file_domain_proto_depIdxs = nil
}
