// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: servicetree.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TreeNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path       string      `protobuf:"bytes,1,opt,name=path,proto3" form:"path" json:"path" query:"path"`
	Owner      []string    `protobuf:"bytes,2,rep,name=owner,proto3" form:"owner" json:"owner" query:"owner"`
	OwnerAdmin []string    `protobuf:"bytes,3,rep,name=owner_admin,json=ownerAdmin,proto3" form:"owner_admin" json:"owner_admin" query:"owner_admin"`
	List       []*TreeNode `protobuf:"bytes,4,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Leaf       bool        `protobuf:"varint,5,opt,name=leaf,proto3" form:"leaf" json:"leaf" query:"leaf"`
	Sort       int32       `protobuf:"varint,6,opt,name=sort,proto3" form:"sort" json:"sort" query:"sort"`
	PathName   string      `protobuf:"bytes,11,opt,name=path_name,json=pathName,proto3" form:"path_name" json:"path_name" query:"path_name"`
	Display    string      `protobuf:"bytes,12,opt,name=display,proto3" form:"display" json:"display" query:"display"`
	Detail     string      `protobuf:"bytes,13,opt,name=detail,proto3" form:"detail" json:"detail" query:"detail"`
}

func (x *TreeNode) Reset() {
	*x = TreeNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreeNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreeNode) ProtoMessage() {}

func (x *TreeNode) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreeNode.ProtoReflect.Descriptor instead.
func (*TreeNode) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{0}
}

func (x *TreeNode) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *TreeNode) GetOwner() []string {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *TreeNode) GetOwnerAdmin() []string {
	if x != nil {
		return x.OwnerAdmin
	}
	return nil
}

func (x *TreeNode) GetList() []*TreeNode {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TreeNode) GetLeaf() bool {
	if x != nil {
		return x.Leaf
	}
	return false
}

func (x *TreeNode) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *TreeNode) GetPathName() string {
	if x != nil {
		return x.PathName
	}
	return ""
}

func (x *TreeNode) GetDisplay() string {
	if x != nil {
		return x.Display
	}
	return ""
}

func (x *TreeNode) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

type TreeEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntityId string            `protobuf:"bytes,1,opt,name=entity_id,json=entityId,proto3" form:"entity_id" json:"entity_id" query:"entity_id"`
	Display  string            `protobuf:"bytes,2,opt,name=display,proto3" form:"display" json:"display" query:"display"`
	Catalog  string            `protobuf:"bytes,3,opt,name=catalog,proto3" form:"catalog" json:"catalog" query:"catalog"`
	Detail   string            `protobuf:"bytes,4,opt,name=detail,proto3" form:"detail" json:"detail" query:"detail"`
	Owner    []string          `protobuf:"bytes,5,rep,name=owner,proto3" form:"owner" json:"owner" query:"owner"`
	Tag      map[string]string `protobuf:"bytes,6,rep,name=tag,proto3" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" form:"tag" json:"tag" query:"tag"`
	Paths    []string          `protobuf:"bytes,11,rep,name=paths,proto3" form:"paths" json:"paths" query:"paths"`
	RowPath  string            `protobuf:"bytes,12,opt,name=row_path,json=rowPath,proto3" form:"row_path" json:"row_path" query:"row_path"`
}

func (x *TreeEntity) Reset() {
	*x = TreeEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TreeEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TreeEntity) ProtoMessage() {}

func (x *TreeEntity) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TreeEntity.ProtoReflect.Descriptor instead.
func (*TreeEntity) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{1}
}

func (x *TreeEntity) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *TreeEntity) GetDisplay() string {
	if x != nil {
		return x.Display
	}
	return ""
}

func (x *TreeEntity) GetCatalog() string {
	if x != nil {
		return x.Catalog
	}
	return ""
}

func (x *TreeEntity) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *TreeEntity) GetOwner() []string {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *TreeEntity) GetTag() map[string]string {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *TreeEntity) GetPaths() []string {
	if x != nil {
		return x.Paths
	}
	return nil
}

func (x *TreeEntity) GetRowPath() string {
	if x != nil {
		return x.RowPath
	}
	return ""
}

type GetTreeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TreeNode `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *GetTreeResponse) Reset() {
	*x = GetTreeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTreeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTreeResponse) ProtoMessage() {}

func (x *GetTreeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTreeResponse.ProtoReflect.Descriptor instead.
func (*GetTreeResponse) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{2}
}

func (x *GetTreeResponse) GetList() []*TreeNode {
	if x != nil {
		return x.List
	}
	return nil
}

type GetEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*TreeEntity `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Count int32         `protobuf:"varint,2,opt,name=count,proto3" form:"count" json:"count" query:"count"`
}

func (x *GetEntityResponse) Reset() {
	*x = GetEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityResponse) ProtoMessage() {}

func (x *GetEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityResponse.ProtoReflect.Descriptor instead.
func (*GetEntityResponse) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{3}
}

func (x *GetEntityResponse) GetList() []*TreeEntity {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetEntityResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type NodeEntityCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PathCountMap map[string]int32 `protobuf:"bytes,1,rep,name=path_count_map,json=pathCountMap,proto3" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3" form:"path_count_map" json:"path_count_map" query:"path_count_map"`
}

func (x *NodeEntityCountResponse) Reset() {
	*x = NodeEntityCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeEntityCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeEntityCountResponse) ProtoMessage() {}

func (x *NodeEntityCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeEntityCountResponse.ProtoReflect.Descriptor instead.
func (*NodeEntityCountResponse) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{4}
}

func (x *NodeEntityCountResponse) GetPathCountMap() map[string]int32 {
	if x != nil {
		return x.PathCountMap
	}
	return nil
}

type AddNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path    string   `protobuf:"bytes,1,opt,name=path,proto3" form:"path" json:"path" query:"path"`
	Display string   `protobuf:"bytes,11,opt,name=display,proto3" form:"display" json:"display" query:"display"`
	Detail  string   `protobuf:"bytes,12,opt,name=detail,proto3" form:"detail" json:"detail" query:"detail"`
	IsLeaf  bool     `protobuf:"varint,13,opt,name=is_leaf,json=isLeaf,proto3" form:"is_leaf" json:"is_leaf" query:"is_leaf"`
	Sort    int32    `protobuf:"varint,14,opt,name=sort,proto3" form:"sort" json:"sort" query:"sort"`
	Owner   []string `protobuf:"bytes,21,rep,name=owner,proto3" form:"owner" json:"owner" query:"owner"`
}

func (x *AddNodeRequest) Reset() {
	*x = AddNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddNodeRequest) ProtoMessage() {}

func (x *AddNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddNodeRequest.ProtoReflect.Descriptor instead.
func (*AddNodeRequest) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{5}
}

func (x *AddNodeRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *AddNodeRequest) GetDisplay() string {
	if x != nil {
		return x.Display
	}
	return ""
}

func (x *AddNodeRequest) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *AddNodeRequest) GetIsLeaf() bool {
	if x != nil {
		return x.IsLeaf
	}
	return false
}

func (x *AddNodeRequest) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *AddNodeRequest) GetOwner() []string {
	if x != nil {
		return x.Owner
	}
	return nil
}

type AddNodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddNodeResponse) Reset() {
	*x = AddNodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddNodeResponse) ProtoMessage() {}

func (x *AddNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddNodeResponse.ProtoReflect.Descriptor instead.
func (*AddNodeResponse) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{6}
}

type VerifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchKey string   `protobuf:"bytes,1,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	List      []string `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *VerifyRequest) Reset() {
	*x = VerifyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyRequest) ProtoMessage() {}

func (x *VerifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyRequest.ProtoReflect.Descriptor instead.
func (*VerifyRequest) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{7}
}

func (x *VerifyRequest) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *VerifyRequest) GetList() []string {
	if x != nil {
		return x.List
	}
	return nil
}

type VerifyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pass []string `protobuf:"bytes,1,rep,name=pass,proto3" form:"pass" json:"pass" query:"pass"`
	Deny []string `protobuf:"bytes,2,rep,name=deny,proto3" form:"deny" json:"deny" query:"deny"`
}

func (x *VerifyResponse) Reset() {
	*x = VerifyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_servicetree_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyResponse) ProtoMessage() {}

func (x *VerifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_servicetree_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyResponse.ProtoReflect.Descriptor instead.
func (*VerifyResponse) Descriptor() ([]byte, []int) {
	return file_servicetree_proto_rawDescGZIP(), []int{8}
}

func (x *VerifyResponse) GetPass() []string {
	if x != nil {
		return x.Pass
	}
	return nil
}

func (x *VerifyResponse) GetDeny() []string {
	if x != nil {
		return x.Deny
	}
	return nil
}

var File_servicetree_proto protoreflect.FileDescriptor

var file_servicetree_proto_rawDesc = []byte{
	0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x74, 0x72, 0x65, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x22, 0xf4, 0x01,
	0x0a, 0x08, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x14,
	0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54,
	0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6c, 0x65, 0x61, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x6c, 0x65, 0x61,
	0x66, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x74, 0x68, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x22, 0xa5, 0x02, 0x0a, 0x0a, 0x54, 0x72, 0x65, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x12, 0x2f, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x72, 0x65, 0x65, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x54, 0x61, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03,
	0x74, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x74, 0x68, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x77,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x77,
	0x50, 0x61, 0x74, 0x68, 0x1a, 0x36, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x39, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x26, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x53, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x72, 0x65, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb5, 0x01, 0x0a,
	0x17, 0x4e, 0x6f, 0x64, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x0e, 0x70, 0x61, 0x74, 0x68,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x50, 0x61, 0x74, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x70, 0x61, 0x74, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4d, 0x61, 0x70, 0x1a, 0x3f, 0x0a, 0x11, 0x50, 0x61, 0x74, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x99, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x6c, 0x65, 0x61, 0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x69, 0x73, 0x4c, 0x65, 0x61, 0x66, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x22, 0x11, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x42, 0x0a, 0x0d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x38, 0x0a, 0x0e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x73, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x65, 0x6e, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x6e,
	0x79, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68,
	0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f,
	0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_servicetree_proto_rawDescOnce sync.Once
	file_servicetree_proto_rawDescData = file_servicetree_proto_rawDesc
)

func file_servicetree_proto_rawDescGZIP() []byte {
	file_servicetree_proto_rawDescOnce.Do(func() {
		file_servicetree_proto_rawDescData = protoimpl.X.CompressGZIP(file_servicetree_proto_rawDescData)
	})
	return file_servicetree_proto_rawDescData
}

var file_servicetree_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_servicetree_proto_goTypes = []interface{}{
	(*TreeNode)(nil),                // 0: cloudman.TreeNode
	(*TreeEntity)(nil),              // 1: cloudman.TreeEntity
	(*GetTreeResponse)(nil),         // 2: cloudman.GetTreeResponse
	(*GetEntityResponse)(nil),       // 3: cloudman.GetEntityResponse
	(*NodeEntityCountResponse)(nil), // 4: cloudman.NodeEntityCountResponse
	(*AddNodeRequest)(nil),          // 5: cloudman.AddNodeRequest
	(*AddNodeResponse)(nil),         // 6: cloudman.AddNodeResponse
	(*VerifyRequest)(nil),           // 7: cloudman.VerifyRequest
	(*VerifyResponse)(nil),          // 8: cloudman.VerifyResponse
	nil,                             // 9: cloudman.TreeEntity.TagEntry
	nil,                             // 10: cloudman.NodeEntityCountResponse.PathCountMapEntry
}
var file_servicetree_proto_depIdxs = []int32{
	0,  // 0: cloudman.TreeNode.list:type_name -> cloudman.TreeNode
	9,  // 1: cloudman.TreeEntity.tag:type_name -> cloudman.TreeEntity.TagEntry
	0,  // 2: cloudman.GetTreeResponse.list:type_name -> cloudman.TreeNode
	1,  // 3: cloudman.GetEntityResponse.list:type_name -> cloudman.TreeEntity
	10, // 4: cloudman.NodeEntityCountResponse.path_count_map:type_name -> cloudman.NodeEntityCountResponse.PathCountMapEntry
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_servicetree_proto_init() }
func file_servicetree_proto_init() {
	if File_servicetree_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_servicetree_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreeNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TreeEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTreeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeEntityCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddNodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_servicetree_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_servicetree_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_servicetree_proto_goTypes,
		DependencyIndexes: file_servicetree_proto_depIdxs,
		MessageInfos:      file_servicetree_proto_msgTypes,
	}.Build()
	File_servicetree_proto = out.File
	file_servicetree_proto_rawDesc = nil
	file_servicetree_proto_goTypes = nil
	file_servicetree_proto_depIdxs = nil
}
