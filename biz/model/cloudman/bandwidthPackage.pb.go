// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: bandwidthPackage.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DescribeBandwidthPackageCbwpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId  string `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
}

func (x *DescribeBandwidthPackageCbwpReq) Reset() {
	*x = DescribeBandwidthPackageCbwpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bandwidthPackage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBandwidthPackageCbwpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBandwidthPackageCbwpReq) ProtoMessage() {}

func (x *DescribeBandwidthPackageCbwpReq) ProtoReflect() protoreflect.Message {
	mi := &file_bandwidthPackage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBandwidthPackageCbwpReq.ProtoReflect.Descriptor instead.
func (*DescribeBandwidthPackageCbwpReq) Descriptor() ([]byte, []int) {
	return file_bandwidthPackage_proto_rawDescGZIP(), []int{0}
}

func (x *DescribeBandwidthPackageCbwpReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeBandwidthPackageCbwpReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type DescribeBandwidthPackageCbwpRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                   `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*BandwidthPackageCbwp `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeBandwidthPackageCbwpRes) Reset() {
	*x = DescribeBandwidthPackageCbwpRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bandwidthPackage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBandwidthPackageCbwpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBandwidthPackageCbwpRes) ProtoMessage() {}

func (x *DescribeBandwidthPackageCbwpRes) ProtoReflect() protoreflect.Message {
	mi := &file_bandwidthPackage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBandwidthPackageCbwpRes.ProtoReflect.Descriptor instead.
func (*DescribeBandwidthPackageCbwpRes) Descriptor() ([]byte, []int) {
	return file_bandwidthPackage_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeBandwidthPackageCbwpRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeBandwidthPackageCbwpRes) GetList() []*BandwidthPackageCbwp {
	if x != nil {
		return x.List
	}
	return nil
}

type BandwidthPackageCbwp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	Bandwidth  string `protobuf:"bytes,2,opt,name=Bandwidth,proto3" form:"Bandwidth" json:"Bandwidth" query:"Bandwidth"`
	UsedCount  int32  `protobuf:"varint,3,opt,name=used_count,json=usedCount,proto3" form:"used_count" json:"used_count" query:"used_count"`
	Name       string `protobuf:"bytes,4,opt,name=Name,proto3" form:"Name" json:"Name" query:"Name"`
}

func (x *BandwidthPackageCbwp) Reset() {
	*x = BandwidthPackageCbwp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bandwidthPackage_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BandwidthPackageCbwp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BandwidthPackageCbwp) ProtoMessage() {}

func (x *BandwidthPackageCbwp) ProtoReflect() protoreflect.Message {
	mi := &file_bandwidthPackage_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BandwidthPackageCbwp.ProtoReflect.Descriptor instead.
func (*BandwidthPackageCbwp) Descriptor() ([]byte, []int) {
	return file_bandwidthPackage_proto_rawDescGZIP(), []int{2}
}

func (x *BandwidthPackageCbwp) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *BandwidthPackageCbwp) GetBandwidth() string {
	if x != nil {
		return x.Bandwidth
	}
	return ""
}

func (x *BandwidthPackageCbwp) GetUsedCount() int32 {
	if x != nil {
		return x.UsedCount
	}
	return 0
}

func (x *BandwidthPackageCbwp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_bandwidthPackage_proto protoreflect.FileDescriptor

var file_bandwidthPackage_proto_rawDesc = []byte{
	0x0a, 0x16, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x22, 0x5d, 0x0a, 0x1f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x61,
	0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x62,
	0x77, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x6b, 0x0a, 0x1f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x61, 0x6e,
	0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x62, 0x77,
	0x70, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x43, 0x62, 0x77, 0x70, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x88,
	0x01, 0x0a, 0x14, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x43, 0x62, 0x77, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x61, 0x6e, 0x64,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x42, 0x61, 0x6e,
	0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x75, 0x73, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61,
	0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bandwidthPackage_proto_rawDescOnce sync.Once
	file_bandwidthPackage_proto_rawDescData = file_bandwidthPackage_proto_rawDesc
)

func file_bandwidthPackage_proto_rawDescGZIP() []byte {
	file_bandwidthPackage_proto_rawDescOnce.Do(func() {
		file_bandwidthPackage_proto_rawDescData = protoimpl.X.CompressGZIP(file_bandwidthPackage_proto_rawDescData)
	})
	return file_bandwidthPackage_proto_rawDescData
}

var file_bandwidthPackage_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_bandwidthPackage_proto_goTypes = []interface{}{
	(*DescribeBandwidthPackageCbwpReq)(nil), // 0: cloudman.DescribeBandwidthPackageCbwpReq
	(*DescribeBandwidthPackageCbwpRes)(nil), // 1: cloudman.DescribeBandwidthPackageCbwpRes
	(*BandwidthPackageCbwp)(nil),            // 2: cloudman.BandwidthPackageCbwp
}
var file_bandwidthPackage_proto_depIdxs = []int32{
	2, // 0: cloudman.DescribeBandwidthPackageCbwpRes.list:type_name -> cloudman.BandwidthPackageCbwp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_bandwidthPackage_proto_init() }
func file_bandwidthPackage_proto_init() {
	if File_bandwidthPackage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bandwidthPackage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBandwidthPackageCbwpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bandwidthPackage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBandwidthPackageCbwpRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bandwidthPackage_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BandwidthPackageCbwp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bandwidthPackage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_bandwidthPackage_proto_goTypes,
		DependencyIndexes: file_bandwidthPackage_proto_depIdxs,
		MessageInfos:      file_bandwidthPackage_proto_msgTypes,
	}.Build()
	File_bandwidthPackage_proto = out.File
	file_bandwidthPackage_proto_rawDesc = nil
	file_bandwidthPackage_proto_goTypes = nil
	file_bandwidthPackage_proto_depIdxs = nil
}
