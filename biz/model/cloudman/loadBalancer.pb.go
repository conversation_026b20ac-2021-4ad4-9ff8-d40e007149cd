// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: loadBalancer.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoadBalancerEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID                           string                       `protobuf:"bytes,1,opt,name=ID,proto3" form:"ID" json:"ID" query:"ID"`
	Address                      string                       `protobuf:"bytes,2,opt,name=Address,proto3" form:"Address" json:"Address" query:"Address"`
	AddressIPVersion             string                       `protobuf:"bytes,3,opt,name=AddressIPVersion,proto3" form:"AddressIPVersion" json:"AddressIPVersion" query:"AddressIPVersion"`
	AddressType                  string                       `protobuf:"bytes,4,opt,name=AddressType,proto3" form:"AddressType" json:"AddressType" query:"AddressType"`
	AutoReleaseTime              int64                        `protobuf:"varint,5,opt,name=AutoReleaseTime,proto3" form:"AutoReleaseTime" json:"AutoReleaseTime" query:"AutoReleaseTime"`
	BackendServers               []*LoadBalancerBackendServer `protobuf:"bytes,6,rep,name=BackendServers,proto3" form:"BackendServers" json:"BackendServers" query:"BackendServers"`
	Bandwidth                    int32                        `protobuf:"varint,7,opt,name=Bandwidth,proto3" form:"Bandwidth" json:"Bandwidth" query:"Bandwidth"`
	CreateTime                   string                       `protobuf:"bytes,8,opt,name=CreateTime,proto3" form:"CreateTime" json:"CreateTime" query:"CreateTime"`
	CreateTimeStamp              int64                        `protobuf:"varint,9,opt,name=CreateTimeStamp,proto3" form:"CreateTimeStamp" json:"CreateTimeStamp" query:"CreateTimeStamp"`
	DeleteProtection             string                       `protobuf:"bytes,10,opt,name=DeleteProtection,proto3" form:"DeleteProtection" json:"DeleteProtection" query:"DeleteProtection"`
	EndTime                      string                       `protobuf:"bytes,11,opt,name=EndTime,proto3" form:"EndTime" json:"EndTime" query:"EndTime"`
	EndTimeStamp                 int64                        `protobuf:"varint,12,opt,name=EndTimeStamp,proto3" form:"EndTimeStamp" json:"EndTimeStamp" query:"EndTimeStamp"`
	InstanceChargeType           string                       `protobuf:"bytes,13,opt,name=InstanceChargeType,proto3" form:"InstanceChargeType" json:"InstanceChargeType" query:"InstanceChargeType"`
	InternetChargeType           string                       `protobuf:"bytes,14,opt,name=InternetChargeType,proto3" form:"InternetChargeType" json:"InternetChargeType" query:"InternetChargeType"`
	Listeners                    []*LoadBalancerListener      `protobuf:"bytes,15,rep,name=Listeners,proto3" form:"Listeners" json:"Listeners" query:"Listeners"`
	LoadBalancerID               string                       `protobuf:"bytes,18,opt,name=LoadBalancerID,proto3" form:"LoadBalancerID" json:"LoadBalancerID" query:"LoadBalancerID"`
	LoadBalancerName             string                       `protobuf:"bytes,19,opt,name=LoadBalancerName,proto3" form:"LoadBalancerName" json:"LoadBalancerName" query:"LoadBalancerName"`
	LoadBalancerSpec             string                       `protobuf:"bytes,20,opt,name=LoadBalancerSpec,proto3" form:"LoadBalancerSpec" json:"LoadBalancerSpec" query:"LoadBalancerSpec"`
	LoadBalancerStatus           string                       `protobuf:"bytes,21,opt,name=LoadBalancerStatus,proto3" form:"LoadBalancerStatus" json:"LoadBalancerStatus" query:"LoadBalancerStatus"`
	MasterZoneID                 string                       `protobuf:"bytes,22,opt,name=MasterZoneID,proto3" form:"MasterZoneID" json:"MasterZoneID" query:"MasterZoneID"`
	ModificationProtectionReason string                       `protobuf:"bytes,23,opt,name=ModificationProtectionReason,proto3" form:"ModificationProtectionReason" json:"ModificationProtectionReason" query:"ModificationProtectionReason"`
	ModificationProtectionStatus string                       `protobuf:"bytes,24,opt,name=ModificationProtectionStatus,proto3" form:"ModificationProtectionStatus" json:"ModificationProtectionStatus" query:"ModificationProtectionStatus"`
	NetworkType                  string                       `protobuf:"bytes,25,opt,name=NetworkType,proto3" form:"NetworkType" json:"NetworkType" query:"NetworkType"`
	PayType                      string                       `protobuf:"bytes,26,opt,name=PayType,proto3" form:"PayType" json:"PayType" query:"PayType"`
	RegionID                     string                       `protobuf:"bytes,27,opt,name=RegionID,proto3" form:"RegionID" json:"RegionID" query:"RegionID"`
	RegionIDAlias                string                       `protobuf:"bytes,28,opt,name=RegionIDAlias,proto3" form:"RegionIDAlias" json:"RegionIDAlias" query:"RegionIDAlias"`
	RenewalCycUnit               string                       `protobuf:"bytes,29,opt,name=RenewalCycUnit,proto3" form:"RenewalCycUnit" json:"RenewalCycUnit" query:"RenewalCycUnit"`
	RenewalDuration              int32                        `protobuf:"varint,30,opt,name=RenewalDuration,proto3" form:"RenewalDuration" json:"RenewalDuration" query:"RenewalDuration"`
	RenewalStatus                string                       `protobuf:"bytes,31,opt,name=RenewalStatus,proto3" form:"RenewalStatus" json:"RenewalStatus" query:"RenewalStatus"`
	ResourceGroupID              string                       `protobuf:"bytes,33,opt,name=ResourceGroupID,proto3" form:"ResourceGroupID" json:"ResourceGroupID" query:"ResourceGroupID"`
	SlaveZoneID                  string                       `protobuf:"bytes,34,opt,name=SlaveZoneID,proto3" form:"SlaveZoneID" json:"SlaveZoneID" query:"SlaveZoneID"`
	Tags                         []*ResourceTag               `protobuf:"bytes,35,rep,name=Tags,proto3" form:"Tags" json:"Tags" query:"Tags"`
	VSwitchID                    string                       `protobuf:"bytes,36,opt,name=VSwitchID,proto3" form:"VSwitchID" json:"VSwitchID" query:"VSwitchID"`
	VpcID                        string                       `protobuf:"bytes,37,opt,name=VpcID,proto3" form:"VpcID" json:"VpcID" query:"VpcID"`
	IspId                        string                       `protobuf:"bytes,38,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType                      string                       `protobuf:"bytes,39,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName                      string                       `protobuf:"bytes,40,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
	LoadBalancerType             string                       `protobuf:"bytes,41,opt,name=LoadBalancerType,proto3" form:"LoadBalancerType" json:"LoadBalancerType" query:"LoadBalancerType"`
	NeedCleanup                  bool                         `protobuf:"varint,42,opt,name=NeedCleanup,proto3" form:"NeedCleanup" json:"NeedCleanup" query:"NeedCleanup"`
	SecurityGroupIds             []string                     `protobuf:"bytes,43,rep,name=SecurityGroupIds,proto3" form:"SecurityGroupIds" json:"SecurityGroupIds" query:"SecurityGroupIds"`
	Ipv6AddressType              string                       `protobuf:"bytes,44,opt,name=Ipv6AddressType,proto3" form:"Ipv6AddressType" json:"Ipv6AddressType" query:"Ipv6AddressType"`
}

func (x *LoadBalancerEntity) Reset() {
	*x = LoadBalancerEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerEntity) ProtoMessage() {}

func (x *LoadBalancerEntity) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerEntity.ProtoReflect.Descriptor instead.
func (*LoadBalancerEntity) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{0}
}

func (x *LoadBalancerEntity) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *LoadBalancerEntity) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *LoadBalancerEntity) GetAddressIPVersion() string {
	if x != nil {
		return x.AddressIPVersion
	}
	return ""
}

func (x *LoadBalancerEntity) GetAddressType() string {
	if x != nil {
		return x.AddressType
	}
	return ""
}

func (x *LoadBalancerEntity) GetAutoReleaseTime() int64 {
	if x != nil {
		return x.AutoReleaseTime
	}
	return 0
}

func (x *LoadBalancerEntity) GetBackendServers() []*LoadBalancerBackendServer {
	if x != nil {
		return x.BackendServers
	}
	return nil
}

func (x *LoadBalancerEntity) GetBandwidth() int32 {
	if x != nil {
		return x.Bandwidth
	}
	return 0
}

func (x *LoadBalancerEntity) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *LoadBalancerEntity) GetCreateTimeStamp() int64 {
	if x != nil {
		return x.CreateTimeStamp
	}
	return 0
}

func (x *LoadBalancerEntity) GetDeleteProtection() string {
	if x != nil {
		return x.DeleteProtection
	}
	return ""
}

func (x *LoadBalancerEntity) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *LoadBalancerEntity) GetEndTimeStamp() int64 {
	if x != nil {
		return x.EndTimeStamp
	}
	return 0
}

func (x *LoadBalancerEntity) GetInstanceChargeType() string {
	if x != nil {
		return x.InstanceChargeType
	}
	return ""
}

func (x *LoadBalancerEntity) GetInternetChargeType() string {
	if x != nil {
		return x.InternetChargeType
	}
	return ""
}

func (x *LoadBalancerEntity) GetListeners() []*LoadBalancerListener {
	if x != nil {
		return x.Listeners
	}
	return nil
}

func (x *LoadBalancerEntity) GetLoadBalancerID() string {
	if x != nil {
		return x.LoadBalancerID
	}
	return ""
}

func (x *LoadBalancerEntity) GetLoadBalancerName() string {
	if x != nil {
		return x.LoadBalancerName
	}
	return ""
}

func (x *LoadBalancerEntity) GetLoadBalancerSpec() string {
	if x != nil {
		return x.LoadBalancerSpec
	}
	return ""
}

func (x *LoadBalancerEntity) GetLoadBalancerStatus() string {
	if x != nil {
		return x.LoadBalancerStatus
	}
	return ""
}

func (x *LoadBalancerEntity) GetMasterZoneID() string {
	if x != nil {
		return x.MasterZoneID
	}
	return ""
}

func (x *LoadBalancerEntity) GetModificationProtectionReason() string {
	if x != nil {
		return x.ModificationProtectionReason
	}
	return ""
}

func (x *LoadBalancerEntity) GetModificationProtectionStatus() string {
	if x != nil {
		return x.ModificationProtectionStatus
	}
	return ""
}

func (x *LoadBalancerEntity) GetNetworkType() string {
	if x != nil {
		return x.NetworkType
	}
	return ""
}

func (x *LoadBalancerEntity) GetPayType() string {
	if x != nil {
		return x.PayType
	}
	return ""
}

func (x *LoadBalancerEntity) GetRegionID() string {
	if x != nil {
		return x.RegionID
	}
	return ""
}

func (x *LoadBalancerEntity) GetRegionIDAlias() string {
	if x != nil {
		return x.RegionIDAlias
	}
	return ""
}

func (x *LoadBalancerEntity) GetRenewalCycUnit() string {
	if x != nil {
		return x.RenewalCycUnit
	}
	return ""
}

func (x *LoadBalancerEntity) GetRenewalDuration() int32 {
	if x != nil {
		return x.RenewalDuration
	}
	return 0
}

func (x *LoadBalancerEntity) GetRenewalStatus() string {
	if x != nil {
		return x.RenewalStatus
	}
	return ""
}

func (x *LoadBalancerEntity) GetResourceGroupID() string {
	if x != nil {
		return x.ResourceGroupID
	}
	return ""
}

func (x *LoadBalancerEntity) GetSlaveZoneID() string {
	if x != nil {
		return x.SlaveZoneID
	}
	return ""
}

func (x *LoadBalancerEntity) GetTags() []*ResourceTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *LoadBalancerEntity) GetVSwitchID() string {
	if x != nil {
		return x.VSwitchID
	}
	return ""
}

func (x *LoadBalancerEntity) GetVpcID() string {
	if x != nil {
		return x.VpcID
	}
	return ""
}

func (x *LoadBalancerEntity) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *LoadBalancerEntity) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *LoadBalancerEntity) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

func (x *LoadBalancerEntity) GetLoadBalancerType() string {
	if x != nil {
		return x.LoadBalancerType
	}
	return ""
}

func (x *LoadBalancerEntity) GetNeedCleanup() bool {
	if x != nil {
		return x.NeedCleanup
	}
	return false
}

func (x *LoadBalancerEntity) GetSecurityGroupIds() []string {
	if x != nil {
		return x.SecurityGroupIds
	}
	return nil
}

func (x *LoadBalancerEntity) GetIpv6AddressType() string {
	if x != nil {
		return x.Ipv6AddressType
	}
	return ""
}

type LoadBalancerBackendServer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=Description,proto3" form:"Description" json:"Description" query:"Description"`
	ServerID    string `protobuf:"bytes,2,opt,name=ServerID,proto3" form:"ServerID" json:"ServerID" query:"ServerID"`
	ServerIP    string `protobuf:"bytes,3,opt,name=ServerIP,proto3" form:"ServerIP" json:"ServerIP" query:"ServerIP"`
	Type        string `protobuf:"bytes,4,opt,name=Type,proto3" form:"Type" json:"Type" query:"Type"`
	Weight      int32  `protobuf:"varint,5,opt,name=Weight,proto3" form:"Weight" json:"Weight" query:"Weight"`
}

func (x *LoadBalancerBackendServer) Reset() {
	*x = LoadBalancerBackendServer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerBackendServer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerBackendServer) ProtoMessage() {}

func (x *LoadBalancerBackendServer) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerBackendServer.ProtoReflect.Descriptor instead.
func (*LoadBalancerBackendServer) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{1}
}

func (x *LoadBalancerBackendServer) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LoadBalancerBackendServer) GetServerID() string {
	if x != nil {
		return x.ServerID
	}
	return ""
}

func (x *LoadBalancerBackendServer) GetServerIP() string {
	if x != nil {
		return x.ServerIP
	}
	return ""
}

func (x *LoadBalancerBackendServer) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *LoadBalancerBackendServer) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

type LoadBalancerListener struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AclId             string `protobuf:"bytes,1,opt,name=AclId,proto3" form:"AclId" json:"AclId" query:"AclId"`
	AclStatus         string `protobuf:"bytes,2,opt,name=AclStatus,proto3" form:"AclStatus" json:"AclStatus" query:"AclStatus"`
	AclType           string `protobuf:"bytes,3,opt,name=AclType,proto3" form:"AclType" json:"AclType" query:"AclType"`
	BackendServerPort int32  `protobuf:"varint,4,opt,name=BackendServerPort,proto3" form:"BackendServerPort" json:"BackendServerPort" query:"BackendServerPort"`
	Bandwidth         int32  `protobuf:"varint,5,opt,name=Bandwidth,proto3" form:"Bandwidth" json:"Bandwidth" query:"Bandwidth"`
	Description       string `protobuf:"bytes,6,opt,name=Description,proto3" form:"Description" json:"Description" query:"Description"`
	ListenerPort      int32  `protobuf:"varint,7,opt,name=ListenerPort,proto3" form:"ListenerPort" json:"ListenerPort" query:"ListenerPort"`
	ListenerProtocol  string `protobuf:"bytes,8,opt,name=ListenerProtocol,proto3" form:"ListenerProtocol" json:"ListenerProtocol" query:"ListenerProtocol"`
	LoadBalancerId    string `protobuf:"bytes,9,opt,name=LoadBalancerId,proto3" form:"LoadBalancerId" json:"LoadBalancerId" query:"LoadBalancerId"`
	Scheduler         string `protobuf:"bytes,10,opt,name=Scheduler,proto3" form:"Scheduler" json:"Scheduler" query:"Scheduler"`
	Status            string `protobuf:"bytes,11,opt,name=Status,proto3" form:"Status" json:"Status" query:"Status"`
	VServerGroupId    string `protobuf:"bytes,12,opt,name=VServerGroupId,proto3" form:"VServerGroupId" json:"VServerGroupId" query:"VServerGroupId"`
}

func (x *LoadBalancerListener) Reset() {
	*x = LoadBalancerListener{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerListener) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerListener) ProtoMessage() {}

func (x *LoadBalancerListener) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerListener.ProtoReflect.Descriptor instead.
func (*LoadBalancerListener) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{2}
}

func (x *LoadBalancerListener) GetAclId() string {
	if x != nil {
		return x.AclId
	}
	return ""
}

func (x *LoadBalancerListener) GetAclStatus() string {
	if x != nil {
		return x.AclStatus
	}
	return ""
}

func (x *LoadBalancerListener) GetAclType() string {
	if x != nil {
		return x.AclType
	}
	return ""
}

func (x *LoadBalancerListener) GetBackendServerPort() int32 {
	if x != nil {
		return x.BackendServerPort
	}
	return 0
}

func (x *LoadBalancerListener) GetBandwidth() int32 {
	if x != nil {
		return x.Bandwidth
	}
	return 0
}

func (x *LoadBalancerListener) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LoadBalancerListener) GetListenerPort() int32 {
	if x != nil {
		return x.ListenerPort
	}
	return 0
}

func (x *LoadBalancerListener) GetListenerProtocol() string {
	if x != nil {
		return x.ListenerProtocol
	}
	return ""
}

func (x *LoadBalancerListener) GetLoadBalancerId() string {
	if x != nil {
		return x.LoadBalancerId
	}
	return ""
}

func (x *LoadBalancerListener) GetScheduler() string {
	if x != nil {
		return x.Scheduler
	}
	return ""
}

func (x *LoadBalancerListener) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *LoadBalancerListener) GetVServerGroupId() string {
	if x != nil {
		return x.VServerGroupId
	}
	return ""
}

type LoadBalancerListenerPortsAndProtocal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ListenerPort     int32  `protobuf:"varint,1,opt,name=ListenerPort,proto3" form:"ListenerPort" json:"ListenerPort" query:"ListenerPort"`
	ListenerProtocal string `protobuf:"bytes,2,opt,name=ListenerProtocal,proto3" form:"ListenerProtocal" json:"ListenerProtocal" query:"ListenerProtocal"`
}

func (x *LoadBalancerListenerPortsAndProtocal) Reset() {
	*x = LoadBalancerListenerPortsAndProtocal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerListenerPortsAndProtocal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerListenerPortsAndProtocal) ProtoMessage() {}

func (x *LoadBalancerListenerPortsAndProtocal) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerListenerPortsAndProtocal.ProtoReflect.Descriptor instead.
func (*LoadBalancerListenerPortsAndProtocal) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{3}
}

func (x *LoadBalancerListenerPortsAndProtocal) GetListenerPort() int32 {
	if x != nil {
		return x.ListenerPort
	}
	return 0
}

func (x *LoadBalancerListenerPortsAndProtocal) GetListenerProtocal() string {
	if x != nil {
		return x.ListenerProtocal
	}
	return ""
}

type LoadBalancerListenerPortAndProtocol struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description      string `protobuf:"bytes,1,opt,name=Description,proto3" form:"Description" json:"Description" query:"Description"`
	ForwardPort      int32  `protobuf:"varint,2,opt,name=ForwardPort,proto3" form:"ForwardPort" json:"ForwardPort" query:"ForwardPort"`
	ListenerForward  string `protobuf:"bytes,3,opt,name=ListenerForward,proto3" form:"ListenerForward" json:"ListenerForward" query:"ListenerForward"`
	ListenerPort     int32  `protobuf:"varint,4,opt,name=ListenerPort,proto3" form:"ListenerPort" json:"ListenerPort" query:"ListenerPort"`
	ListenerProtocol string `protobuf:"bytes,5,opt,name=ListenerProtocol,proto3" form:"ListenerProtocol" json:"ListenerProtocol" query:"ListenerProtocol"`
}

func (x *LoadBalancerListenerPortAndProtocol) Reset() {
	*x = LoadBalancerListenerPortAndProtocol{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerListenerPortAndProtocol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerListenerPortAndProtocol) ProtoMessage() {}

func (x *LoadBalancerListenerPortAndProtocol) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerListenerPortAndProtocol.ProtoReflect.Descriptor instead.
func (*LoadBalancerListenerPortAndProtocol) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{4}
}

func (x *LoadBalancerListenerPortAndProtocol) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LoadBalancerListenerPortAndProtocol) GetForwardPort() int32 {
	if x != nil {
		return x.ForwardPort
	}
	return 0
}

func (x *LoadBalancerListenerPortAndProtocol) GetListenerForward() string {
	if x != nil {
		return x.ListenerForward
	}
	return ""
}

func (x *LoadBalancerListenerPortAndProtocol) GetListenerPort() int32 {
	if x != nil {
		return x.ListenerPort
	}
	return 0
}

func (x *LoadBalancerListenerPortAndProtocol) GetListenerProtocol() string {
	if x != nil {
		return x.ListenerProtocol
	}
	return ""
}

type DescribeLoadBalancerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp         string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId    string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	SearchKey   string `protobuf:"bytes,5,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue string `protobuf:"bytes,6,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
	Address     string `protobuf:"bytes,7,opt,name=address,proto3" form:"address" json:"address" query:"address"`
	LbType      string `protobuf:"bytes,8,opt,name=lb_type,json=lbType,proto3" form:"lb_type" json:"lb_type" query:"lb_type"`
	NeedCleanup bool   `protobuf:"varint,9,opt,name=need_cleanup,json=needCleanup,proto3" form:"need_cleanup" json:"need_cleanup" query:"need_cleanup"`
}

func (x *DescribeLoadBalancerReq) Reset() {
	*x = DescribeLoadBalancerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeLoadBalancerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeLoadBalancerReq) ProtoMessage() {}

func (x *DescribeLoadBalancerReq) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeLoadBalancerReq.ProtoReflect.Descriptor instead.
func (*DescribeLoadBalancerReq) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeLoadBalancerReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeLoadBalancerReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeLoadBalancerReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeLoadBalancerReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeLoadBalancerReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *DescribeLoadBalancerReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

func (x *DescribeLoadBalancerReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *DescribeLoadBalancerReq) GetLbType() string {
	if x != nil {
		return x.LbType
	}
	return ""
}

func (x *DescribeLoadBalancerReq) GetNeedCleanup() bool {
	if x != nil {
		return x.NeedCleanup
	}
	return false
}

type DescribeLoadBalancerRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                 `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*LoadBalancerEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeLoadBalancerRes) Reset() {
	*x = DescribeLoadBalancerRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeLoadBalancerRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeLoadBalancerRes) ProtoMessage() {}

func (x *DescribeLoadBalancerRes) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeLoadBalancerRes.ProtoReflect.Descriptor instead.
func (*DescribeLoadBalancerRes) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{6}
}

func (x *DescribeLoadBalancerRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeLoadBalancerRes) GetList() []*LoadBalancerEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type DescribeLoadBalancerDetailRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoadBalancerName string          `protobuf:"bytes,1,opt,name=LoadBalancerName,proto3" form:"LoadBalancerName" json:"LoadBalancerName" query:"LoadBalancerName"`
	LoadBalancerID   string          `protobuf:"bytes,2,opt,name=LoadBalancerID,proto3" form:"LoadBalancerID" json:"LoadBalancerID" query:"LoadBalancerID"`
	VpcID            string          `protobuf:"bytes,3,opt,name=VpcID,proto3" form:"VpcID" json:"VpcID" query:"VpcID"`
	IspId            string          `protobuf:"bytes,4,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType          string          `protobuf:"bytes,5,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName          string          `protobuf:"bytes,6,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
	LoadBalancerType string          `protobuf:"bytes,7,opt,name=LoadBalancerType,proto3" form:"LoadBalancerType" json:"LoadBalancerType" query:"LoadBalancerType"`
	Address          string          `protobuf:"bytes,8,opt,name=Address,proto3" form:"Address" json:"Address" query:"Address"`
	RegionID         string          `protobuf:"bytes,9,opt,name=RegionID,proto3" form:"RegionID" json:"RegionID" query:"RegionID"`
	Listeners        []*ALBListener  `protobuf:"bytes,10,rep,name=Listeners,proto3" form:"Listeners" json:"Listeners" query:"Listeners"`
	ZoneMappings     []*ZoneMappings `protobuf:"bytes,11,rep,name=ZoneMappings,proto3" form:"ZoneMappings" json:"ZoneMappings" query:"ZoneMappings"`
}

func (x *DescribeLoadBalancerDetailRes) Reset() {
	*x = DescribeLoadBalancerDetailRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeLoadBalancerDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeLoadBalancerDetailRes) ProtoMessage() {}

func (x *DescribeLoadBalancerDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeLoadBalancerDetailRes.ProtoReflect.Descriptor instead.
func (*DescribeLoadBalancerDetailRes) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{7}
}

func (x *DescribeLoadBalancerDetailRes) GetLoadBalancerName() string {
	if x != nil {
		return x.LoadBalancerName
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetLoadBalancerID() string {
	if x != nil {
		return x.LoadBalancerID
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetVpcID() string {
	if x != nil {
		return x.VpcID
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetLoadBalancerType() string {
	if x != nil {
		return x.LoadBalancerType
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetRegionID() string {
	if x != nil {
		return x.RegionID
	}
	return ""
}

func (x *DescribeLoadBalancerDetailRes) GetListeners() []*ALBListener {
	if x != nil {
		return x.Listeners
	}
	return nil
}

func (x *DescribeLoadBalancerDetailRes) GetZoneMappings() []*ZoneMappings {
	if x != nil {
		return x.ZoneMappings
	}
	return nil
}

type ZoneMappings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoadBalancerAddresses []*LoadBalancerAddresses `protobuf:"bytes,1,rep,name=LoadBalancerAddresses,proto3" form:"LoadBalancerAddresses" json:"LoadBalancerAddresses" query:"LoadBalancerAddresses"`
	ZoneId                string                   `protobuf:"bytes,2,opt,name=ZoneId,proto3" form:"ZoneId" json:"ZoneId" query:"ZoneId"`
	VSwitchId             string                   `protobuf:"bytes,3,opt,name=VSwitchId,proto3" form:"VSwitchId" json:"VSwitchId" query:"VSwitchId"`
}

func (x *ZoneMappings) Reset() {
	*x = ZoneMappings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ZoneMappings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZoneMappings) ProtoMessage() {}

func (x *ZoneMappings) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZoneMappings.ProtoReflect.Descriptor instead.
func (*ZoneMappings) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{8}
}

func (x *ZoneMappings) GetLoadBalancerAddresses() []*LoadBalancerAddresses {
	if x != nil {
		return x.LoadBalancerAddresses
	}
	return nil
}

func (x *ZoneMappings) GetZoneId() string {
	if x != nil {
		return x.ZoneId
	}
	return ""
}

func (x *ZoneMappings) GetVSwitchId() string {
	if x != nil {
		return x.VSwitchId
	}
	return ""
}

type LoadBalancerAddresses struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address         string `protobuf:"bytes,1,opt,name=Address,proto3" form:"Address" json:"Address" query:"Address"`
	Ipv6Address     string `protobuf:"bytes,2,opt,name=Ipv6Address,proto3" form:"Ipv6Address" json:"Ipv6Address" query:"Ipv6Address"`
	IntranetAddress string `protobuf:"bytes,3,opt,name=IntranetAddress,proto3" form:"IntranetAddress" json:"IntranetAddress" query:"IntranetAddress"`
}

func (x *LoadBalancerAddresses) Reset() {
	*x = LoadBalancerAddresses{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerAddresses) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerAddresses) ProtoMessage() {}

func (x *LoadBalancerAddresses) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerAddresses.ProtoReflect.Descriptor instead.
func (*LoadBalancerAddresses) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{9}
}

func (x *LoadBalancerAddresses) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *LoadBalancerAddresses) GetIpv6Address() string {
	if x != nil {
		return x.Ipv6Address
	}
	return ""
}

func (x *LoadBalancerAddresses) GetIntranetAddress() string {
	if x != nil {
		return x.IntranetAddress
	}
	return ""
}

type ALBListener struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ListenerID           string `protobuf:"bytes,1,opt,name=ListenerID,proto3" form:"ListenerID" json:"ListenerID" query:"ListenerID"`
	ListenerProtocol     string `protobuf:"bytes,2,opt,name=ListenerProtocol,proto3" form:"ListenerProtocol" json:"ListenerProtocol" query:"ListenerProtocol"`
	ListenerPort         int32  `protobuf:"varint,3,opt,name=ListenerPort,proto3" form:"ListenerPort" json:"ListenerPort" query:"ListenerPort"`
	DefaultServerGroupID string `protobuf:"bytes,4,opt,name=DefaultServerGroupID,proto3" form:"DefaultServerGroupID" json:"DefaultServerGroupID" query:"DefaultServerGroupID"`
	IdleTimeout          int32  `protobuf:"varint,5,opt,name=IdleTimeout,proto3" form:"IdleTimeout" json:"IdleTimeout" query:"IdleTimeout"`
	RequestTimeout       int32  `protobuf:"varint,6,opt,name=RequestTimeout,proto3" form:"RequestTimeout" json:"RequestTimeout" query:"RequestTimeout"`
	GzipEnabled          bool   `protobuf:"varint,7,opt,name=GzipEnabled,proto3" form:"GzipEnabled" json:"GzipEnabled" query:"GzipEnabled"`
	ListenerStatus       string `protobuf:"bytes,8,opt,name=ListenerStatus,proto3" form:"ListenerStatus" json:"ListenerStatus" query:"ListenerStatus"`
	ListenerDescription  string `protobuf:"bytes,9,opt,name=ListenerDescription,proto3" form:"ListenerDescription" json:"ListenerDescription" query:"ListenerDescription"`
}

func (x *ALBListener) Reset() {
	*x = ALBListener{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ALBListener) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ALBListener) ProtoMessage() {}

func (x *ALBListener) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ALBListener.ProtoReflect.Descriptor instead.
func (*ALBListener) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{10}
}

func (x *ALBListener) GetListenerID() string {
	if x != nil {
		return x.ListenerID
	}
	return ""
}

func (x *ALBListener) GetListenerProtocol() string {
	if x != nil {
		return x.ListenerProtocol
	}
	return ""
}

func (x *ALBListener) GetListenerPort() int32 {
	if x != nil {
		return x.ListenerPort
	}
	return 0
}

func (x *ALBListener) GetDefaultServerGroupID() string {
	if x != nil {
		return x.DefaultServerGroupID
	}
	return ""
}

func (x *ALBListener) GetIdleTimeout() int32 {
	if x != nil {
		return x.IdleTimeout
	}
	return 0
}

func (x *ALBListener) GetRequestTimeout() int32 {
	if x != nil {
		return x.RequestTimeout
	}
	return 0
}

func (x *ALBListener) GetGzipEnabled() bool {
	if x != nil {
		return x.GzipEnabled
	}
	return false
}

func (x *ALBListener) GetListenerStatus() string {
	if x != nil {
		return x.ListenerStatus
	}
	return ""
}

func (x *ALBListener) GetListenerDescription() string {
	if x != nil {
		return x.ListenerDescription
	}
	return ""
}

type DescribeLoadBalancerServerGroupRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerGroupID     string                 `protobuf:"bytes,1,opt,name=ServerGroupID,proto3" form:"ServerGroupID" json:"ServerGroupID" query:"ServerGroupID"`
	ServerGroupName   string                 `protobuf:"bytes,2,opt,name=ServerGroupName,proto3" form:"ServerGroupName" json:"ServerGroupName" query:"ServerGroupName"`
	Scheduler         string                 `protobuf:"bytes,3,opt,name=Scheduler,proto3" form:"Scheduler" json:"Scheduler" query:"Scheduler"`
	CreateTime        string                 `protobuf:"bytes,4,opt,name=CreateTime,proto3" form:"CreateTime" json:"CreateTime" query:"CreateTime"`
	Servers           []*LBServerGroupServer `protobuf:"bytes,5,rep,name=Servers,proto3" form:"Servers" json:"Servers" query:"Servers"`
	ServerGroupType   string                 `protobuf:"bytes,6,opt,name=ServerGroupType,proto3" form:"ServerGroupType" json:"ServerGroupType" query:"ServerGroupType"`
	ServerGroupStatus string                 `protobuf:"bytes,7,opt,name=ServerGroupStatus,proto3" form:"ServerGroupStatus" json:"ServerGroupStatus" query:"ServerGroupStatus"`
	Protocol          string                 `protobuf:"bytes,8,opt,name=Protocol,proto3" form:"Protocol" json:"Protocol" query:"Protocol"`
}

func (x *DescribeLoadBalancerServerGroupRes) Reset() {
	*x = DescribeLoadBalancerServerGroupRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeLoadBalancerServerGroupRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeLoadBalancerServerGroupRes) ProtoMessage() {}

func (x *DescribeLoadBalancerServerGroupRes) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeLoadBalancerServerGroupRes.ProtoReflect.Descriptor instead.
func (*DescribeLoadBalancerServerGroupRes) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{11}
}

func (x *DescribeLoadBalancerServerGroupRes) GetServerGroupID() string {
	if x != nil {
		return x.ServerGroupID
	}
	return ""
}

func (x *DescribeLoadBalancerServerGroupRes) GetServerGroupName() string {
	if x != nil {
		return x.ServerGroupName
	}
	return ""
}

func (x *DescribeLoadBalancerServerGroupRes) GetScheduler() string {
	if x != nil {
		return x.Scheduler
	}
	return ""
}

func (x *DescribeLoadBalancerServerGroupRes) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *DescribeLoadBalancerServerGroupRes) GetServers() []*LBServerGroupServer {
	if x != nil {
		return x.Servers
	}
	return nil
}

func (x *DescribeLoadBalancerServerGroupRes) GetServerGroupType() string {
	if x != nil {
		return x.ServerGroupType
	}
	return ""
}

func (x *DescribeLoadBalancerServerGroupRes) GetServerGroupStatus() string {
	if x != nil {
		return x.ServerGroupStatus
	}
	return ""
}

func (x *DescribeLoadBalancerServerGroupRes) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

type LBServerGroupServer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerID string `protobuf:"bytes,1,opt,name=ServerID,proto3" form:"ServerID" json:"ServerID" query:"ServerID"`
	ServerIP string `protobuf:"bytes,2,opt,name=ServerIP,proto3" form:"ServerIP" json:"ServerIP" query:"ServerIP"`
	Type     string `protobuf:"bytes,3,opt,name=Type,proto3" form:"Type" json:"Type" query:"Type"`
	Weight   int32  `protobuf:"varint,4,opt,name=Weight,proto3" form:"Weight" json:"Weight" query:"Weight"`
	Port     int32  `protobuf:"varint,5,opt,name=Port,proto3" form:"Port" json:"Port" query:"Port"`
}

func (x *LBServerGroupServer) Reset() {
	*x = LBServerGroupServer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LBServerGroupServer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LBServerGroupServer) ProtoMessage() {}

func (x *LBServerGroupServer) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LBServerGroupServer.ProtoReflect.Descriptor instead.
func (*LBServerGroupServer) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{12}
}

func (x *LBServerGroupServer) GetServerID() string {
	if x != nil {
		return x.ServerID
	}
	return ""
}

func (x *LBServerGroupServer) GetServerIP() string {
	if x != nil {
		return x.ServerIP
	}
	return ""
}

func (x *LBServerGroupServer) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *LBServerGroupServer) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *LBServerGroupServer) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

type DescribeLoadBalancerByInstancesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page         uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size         uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	InstanceType string   `protobuf:"bytes,3,opt,name=instanceType,proto3" form:"instanceType" json:"instanceType" query:"instanceType"`
	InstanceIds  []string `protobuf:"bytes,4,rep,name=instance_ids,json=instanceIds,proto3" form:"instance_ids" json:"instance_ids" query:"instance_ids"`
}

func (x *DescribeLoadBalancerByInstancesReq) Reset() {
	*x = DescribeLoadBalancerByInstancesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeLoadBalancerByInstancesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeLoadBalancerByInstancesReq) ProtoMessage() {}

func (x *DescribeLoadBalancerByInstancesReq) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeLoadBalancerByInstancesReq.ProtoReflect.Descriptor instead.
func (*DescribeLoadBalancerByInstancesReq) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{13}
}

func (x *DescribeLoadBalancerByInstancesReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeLoadBalancerByInstancesReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeLoadBalancerByInstancesReq) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *DescribeLoadBalancerByInstancesReq) GetInstanceIds() []string {
	if x != nil {
		return x.InstanceIds
	}
	return nil
}

type DescribeZonesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isp      string `protobuf:"bytes,1,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *DescribeZonesReq) Reset() {
	*x = DescribeZonesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeZonesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeZonesReq) ProtoMessage() {}

func (x *DescribeZonesReq) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeZonesReq.ProtoReflect.Descriptor instead.
func (*DescribeZonesReq) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{14}
}

func (x *DescribeZonesReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeZonesReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type DescribeZonesRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32      `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*ALBZone `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeZonesRes) Reset() {
	*x = DescribeZonesRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeZonesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeZonesRes) ProtoMessage() {}

func (x *DescribeZonesRes) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeZonesRes.ProtoReflect.Descriptor instead.
func (*DescribeZonesRes) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{15}
}

func (x *DescribeZonesRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeZonesRes) GetList() []*ALBZone {
	if x != nil {
		return x.List
	}
	return nil
}

type ALBZone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalName string `protobuf:"bytes,1,opt,name=local_name,json=localName,proto3" form:"local_name" json:"local_name" query:"local_name"`
	ZoneId    string `protobuf:"bytes,2,opt,name=zone_id,json=zoneId,proto3" form:"zone_id" json:"zone_id" query:"zone_id"`
}

func (x *ALBZone) Reset() {
	*x = ALBZone{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ALBZone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ALBZone) ProtoMessage() {}

func (x *ALBZone) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ALBZone.ProtoReflect.Descriptor instead.
func (*ALBZone) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{16}
}

func (x *ALBZone) GetLocalName() string {
	if x != nil {
		return x.LocalName
	}
	return ""
}

func (x *ALBZone) GetZoneId() string {
	if x != nil {
		return x.ZoneId
	}
	return ""
}

type ListACLsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isp      string `protobuf:"bytes,1,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *ListACLsReq) Reset() {
	*x = ListACLsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListACLsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListACLsReq) ProtoMessage() {}

func (x *ListACLsReq) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListACLsReq.ProtoReflect.Descriptor instead.
func (*ListACLsReq) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{17}
}

func (x *ListACLsReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *ListACLsReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type ListAClsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32     `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*ALBAcl `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *ListAClsRes) Reset() {
	*x = ListAClsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAClsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAClsRes) ProtoMessage() {}

func (x *ListAClsRes) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAClsRes.ProtoReflect.Descriptor instead.
func (*ListAClsRes) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{18}
}

func (x *ListAClsRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListAClsRes) GetList() []*ALBAcl {
	if x != nil {
		return x.List
	}
	return nil
}

type ALBAcl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AclId   string `protobuf:"bytes,1,opt,name=acl_id,json=aclId,proto3" form:"acl_id" json:"acl_id" query:"acl_id"`
	AclName string `protobuf:"bytes,2,opt,name=acl_name,json=aclName,proto3" form:"acl_name" json:"acl_name" query:"acl_name"`
}

func (x *ALBAcl) Reset() {
	*x = ALBAcl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ALBAcl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ALBAcl) ProtoMessage() {}

func (x *ALBAcl) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ALBAcl.ProtoReflect.Descriptor instead.
func (*ALBAcl) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{19}
}

func (x *ALBAcl) GetAclId() string {
	if x != nil {
		return x.AclId
	}
	return ""
}

func (x *ALBAcl) GetAclName() string {
	if x != nil {
		return x.AclName
	}
	return ""
}

type ListCertificatesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isp      string `protobuf:"bytes,1,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *ListCertificatesReq) Reset() {
	*x = ListCertificatesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCertificatesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCertificatesReq) ProtoMessage() {}

func (x *ListCertificatesReq) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCertificatesReq.ProtoReflect.Descriptor instead.
func (*ListCertificatesReq) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{20}
}

func (x *ListCertificatesReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *ListCertificatesReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type ListCertificatesRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32          `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*Certificate `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *ListCertificatesRes) Reset() {
	*x = ListCertificatesRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCertificatesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCertificatesRes) ProtoMessage() {}

func (x *ListCertificatesRes) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCertificatesRes.ProtoReflect.Descriptor instead.
func (*ListCertificatesRes) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{21}
}

func (x *ListCertificatesRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListCertificatesRes) GetList() []*Certificate {
	if x != nil {
		return x.List
	}
	return nil
}

type Certificate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CertIdentifier string `protobuf:"bytes,1,opt,name=cert_identifier,json=certIdentifier,proto3" form:"cert_identifier" json:"cert_identifier" query:"cert_identifier"`
	CertName       string `protobuf:"bytes,2,opt,name=cert_name,json=certName,proto3" form:"cert_name" json:"cert_name" query:"cert_name"`
	CommonName     string `protobuf:"bytes,3,opt,name=common_name,json=commonName,proto3" form:"common_name" json:"common_name" query:"common_name"`
	Domain         string `protobuf:"bytes,4,opt,name=domain,proto3" form:"domain" json:"domain" query:"domain"`
	AfterDate      int64  `protobuf:"varint,5,opt,name=after_date,json=afterDate,proto3" form:"after_date" json:"after_date" query:"after_date"`
	BeforeDate     int64  `protobuf:"varint,6,opt,name=before_date,json=beforeDate,proto3" form:"before_date" json:"before_date" query:"before_date"`
}

func (x *Certificate) Reset() {
	*x = Certificate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Certificate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Certificate) ProtoMessage() {}

func (x *Certificate) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Certificate.ProtoReflect.Descriptor instead.
func (*Certificate) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{22}
}

func (x *Certificate) GetCertIdentifier() string {
	if x != nil {
		return x.CertIdentifier
	}
	return ""
}

func (x *Certificate) GetCertName() string {
	if x != nil {
		return x.CertName
	}
	return ""
}

func (x *Certificate) GetCommonName() string {
	if x != nil {
		return x.CommonName
	}
	return ""
}

func (x *Certificate) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *Certificate) GetAfterDate() int64 {
	if x != nil {
		return x.AfterDate
	}
	return 0
}

func (x *Certificate) GetBeforeDate() int64 {
	if x != nil {
		return x.BeforeDate
	}
	return 0
}

type LoadBalancerIDsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
}

func (x *LoadBalancerIDsReq) Reset() {
	*x = LoadBalancerIDsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_loadBalancer_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadBalancerIDsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerIDsReq) ProtoMessage() {}

func (x *LoadBalancerIDsReq) ProtoReflect() protoreflect.Message {
	mi := &file_loadBalancer_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerIDsReq.ProtoReflect.Descriptor instead.
func (*LoadBalancerIDsReq) Descriptor() ([]byte, []int) {
	return file_loadBalancer_proto_rawDescGZIP(), []int{23}
}

func (x *LoadBalancerIDsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_loadBalancer_proto protoreflect.FileDescriptor

var file_loadBalancer_proto_rawDesc = []byte{
	0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x11,
	0x72, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xe2, 0x0c, 0x0a, 0x12, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x50, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x50, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x41, 0x75, 0x74, 0x6f, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0e, 0x42, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x0e, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x61, 0x6e, 0x64, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x42, 0x61, 0x6e, 0x64,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x2a, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x45, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x45, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2e, 0x0a, 0x12, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x4c, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x09, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x49, 0x44, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x49, 0x44, 0x12,
	0x2a, 0x0a, 0x10, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x4c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x2e, 0x0a, 0x12, 0x4c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x4d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x44, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d,
	0x61, 0x73, 0x74, 0x65, 0x72, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x44, 0x12, 0x42, 0x0a, 0x1c, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1c, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x42, 0x0a, 0x1c, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x50, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x24, 0x0a, 0x0d, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x41, 0x6c, 0x69, 0x61,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x43, 0x79, 0x63, 0x55,
	0x6e, 0x69, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x6e, 0x65, 0x77,
	0x61, 0x6c, 0x43, 0x79, 0x63, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x52, 0x65, 0x6e,
	0x65, 0x77, 0x61, 0x6c, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x52, 0x65, 0x6e, 0x65,
	0x77, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x6c, 0x61, 0x76, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x49, 0x44, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x53, 0x6c, 0x61, 0x76, 0x65, 0x5a,
	0x6f, 0x6e, 0x65, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x04, 0x54, 0x61, 0x67, 0x73, 0x18, 0x23, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x52, 0x04, 0x54, 0x61, 0x67,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x44, 0x18, 0x24,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x44, 0x12,
	0x14, 0x0a, 0x05, 0x56, 0x70, 0x63, 0x49, 0x44, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x56, 0x70, 0x63, 0x49, 0x44, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x4e, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x18, 0x2a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x4e, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70,
	0x12, 0x2a, 0x0a, 0x10, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x73, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x19, 0x4c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x50, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x50, 0x12, 0x12,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0xa8, 0x03, 0x0a, 0x14, 0x4c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x63, 0x6c, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x41, 0x63, 0x6c, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x63, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x63,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x63, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2c, 0x0a, 0x11, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x42, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x20, 0x0a,
	0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x22, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12,
	0x26, 0x0a, 0x0e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a,
	0x0e, 0x56, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x56, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x24, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72,
	0x74, 0x73, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x61, 0x6c, 0x12, 0x22, 0x0a,
	0x0c, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72,
	0x74, 0x12, 0x2a, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x61, 0x6c, 0x22, 0xe3, 0x01,
	0x0a, 0x23, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x41, 0x6e, 0x64, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x6f, 0x72, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x6f,
	0x72, 0x77, 0x61, 0x72, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x4c, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x77,
	0x61, 0x72, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50,
	0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x22, 0x88, 0x02, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e,
	0x65, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x6e, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x22, 0x61,
	0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xa9, 0x03, 0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x72, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x70, 0x63, 0x49, 0x44,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x56, 0x70, 0x63, 0x49, 0x44, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x73, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x33, 0x0a, 0x09,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41, 0x4c, 0x42, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72,
	0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x0c, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x9b, 0x01,
	0x0a, 0x0c, 0x5a, 0x6f, 0x6e, 0x65, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x55,
	0x0a, 0x15, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x52, 0x15,
	0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x15, 0x4c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x28, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x49, 0x6e, 0x74, 0x72, 0x61,
	0x6e, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xf7, 0x02, 0x0a, 0x0b, 0x41,
	0x4c, 0x42, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x10, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x22, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x32, 0x0a, 0x14, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x12, 0x20,
	0x0a, 0x0b, 0x49, 0x64, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x49, 0x64, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x12, 0x26, 0x0a, 0x0e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x47, 0x7a, 0x69, 0x70,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x47,
	0x7a, 0x69, 0x70, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x30, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xdf, 0x02, 0x0a, 0x22, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x44, 0x12, 0x28, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x42, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x07, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x8d, 0x01, 0x0a, 0x13, 0x4c, 0x42, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x1a,
	0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x49, 0x50, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x49, 0x50, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x50, 0x6f, 0x72, 0x74, 0x22, 0x93, 0x01, 0x0a, 0x22, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x42,
	0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x41, 0x0a, 0x10,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69,
	0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22,
	0x4f, 0x0a, 0x10, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x41, 0x4c, 0x42, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x41, 0x0a, 0x07, 0x41, 0x4c, 0x42, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x7a, 0x6f,
	0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x7a, 0x6f, 0x6e,
	0x65, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x4c, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x49, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x3a, 0x0a, 0x06,
	0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x63, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x63, 0x6c, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x44, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73,
	0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x56,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x29, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xcc, 0x01, 0x0a, 0x0b, 0x43, 0x65, 0x72, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x65, 0x72, 0x74, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x63, 0x65, 0x72, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x65, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x65, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x22, 0x26, 0x0a, 0x12, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x72, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x42, 0x3b, 0x5a,
	0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_loadBalancer_proto_rawDescOnce sync.Once
	file_loadBalancer_proto_rawDescData = file_loadBalancer_proto_rawDesc
)

func file_loadBalancer_proto_rawDescGZIP() []byte {
	file_loadBalancer_proto_rawDescOnce.Do(func() {
		file_loadBalancer_proto_rawDescData = protoimpl.X.CompressGZIP(file_loadBalancer_proto_rawDescData)
	})
	return file_loadBalancer_proto_rawDescData
}

var file_loadBalancer_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_loadBalancer_proto_goTypes = []interface{}{
	(*LoadBalancerEntity)(nil),                   // 0: cloudman.LoadBalancerEntity
	(*LoadBalancerBackendServer)(nil),            // 1: cloudman.LoadBalancerBackendServer
	(*LoadBalancerListener)(nil),                 // 2: cloudman.LoadBalancerListener
	(*LoadBalancerListenerPortsAndProtocal)(nil), // 3: cloudman.LoadBalancerListenerPortsAndProtocal
	(*LoadBalancerListenerPortAndProtocol)(nil),  // 4: cloudman.LoadBalancerListenerPortAndProtocol
	(*DescribeLoadBalancerReq)(nil),              // 5: cloudman.DescribeLoadBalancerReq
	(*DescribeLoadBalancerRes)(nil),              // 6: cloudman.DescribeLoadBalancerRes
	(*DescribeLoadBalancerDetailRes)(nil),        // 7: cloudman.DescribeLoadBalancerDetailRes
	(*ZoneMappings)(nil),                         // 8: cloudman.ZoneMappings
	(*LoadBalancerAddresses)(nil),                // 9: cloudman.LoadBalancerAddresses
	(*ALBListener)(nil),                          // 10: cloudman.ALBListener
	(*DescribeLoadBalancerServerGroupRes)(nil),   // 11: cloudman.DescribeLoadBalancerServerGroupRes
	(*LBServerGroupServer)(nil),                  // 12: cloudman.LBServerGroupServer
	(*DescribeLoadBalancerByInstancesReq)(nil),   // 13: cloudman.DescribeLoadBalancerByInstancesReq
	(*DescribeZonesReq)(nil),                     // 14: cloudman.DescribeZonesReq
	(*DescribeZonesRes)(nil),                     // 15: cloudman.DescribeZonesRes
	(*ALBZone)(nil),                              // 16: cloudman.ALBZone
	(*ListACLsReq)(nil),                          // 17: cloudman.ListACLsReq
	(*ListAClsRes)(nil),                          // 18: cloudman.ListAClsRes
	(*ALBAcl)(nil),                               // 19: cloudman.ALBAcl
	(*ListCertificatesReq)(nil),                  // 20: cloudman.ListCertificatesReq
	(*ListCertificatesRes)(nil),                  // 21: cloudman.ListCertificatesRes
	(*Certificate)(nil),                          // 22: cloudman.Certificate
	(*LoadBalancerIDsReq)(nil),                   // 23: cloudman.LoadBalancerIDsReq
	(*ResourceTag)(nil),                          // 24: cloudman.resource_tag
}
var file_loadBalancer_proto_depIdxs = []int32{
	1,  // 0: cloudman.LoadBalancerEntity.BackendServers:type_name -> cloudman.LoadBalancerBackendServer
	2,  // 1: cloudman.LoadBalancerEntity.Listeners:type_name -> cloudman.LoadBalancerListener
	24, // 2: cloudman.LoadBalancerEntity.Tags:type_name -> cloudman.resource_tag
	0,  // 3: cloudman.DescribeLoadBalancerRes.list:type_name -> cloudman.LoadBalancerEntity
	10, // 4: cloudman.DescribeLoadBalancerDetailRes.Listeners:type_name -> cloudman.ALBListener
	8,  // 5: cloudman.DescribeLoadBalancerDetailRes.ZoneMappings:type_name -> cloudman.ZoneMappings
	9,  // 6: cloudman.ZoneMappings.LoadBalancerAddresses:type_name -> cloudman.LoadBalancerAddresses
	12, // 7: cloudman.DescribeLoadBalancerServerGroupRes.Servers:type_name -> cloudman.LBServerGroupServer
	16, // 8: cloudman.DescribeZonesRes.list:type_name -> cloudman.ALBZone
	19, // 9: cloudman.ListAClsRes.list:type_name -> cloudman.ALBAcl
	22, // 10: cloudman.ListCertificatesRes.list:type_name -> cloudman.Certificate
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_loadBalancer_proto_init() }
func file_loadBalancer_proto_init() {
	if File_loadBalancer_proto != nil {
		return
	}
	file_resTemplate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_loadBalancer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerBackendServer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerListener); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerListenerPortsAndProtocal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerListenerPortAndProtocol); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeLoadBalancerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeLoadBalancerRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeLoadBalancerDetailRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ZoneMappings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerAddresses); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ALBListener); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeLoadBalancerServerGroupRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LBServerGroupServer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeLoadBalancerByInstancesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeZonesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeZonesRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ALBZone); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListACLsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAClsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ALBAcl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCertificatesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCertificatesRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Certificate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_loadBalancer_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadBalancerIDsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_loadBalancer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_loadBalancer_proto_goTypes,
		DependencyIndexes: file_loadBalancer_proto_depIdxs,
		MessageInfos:      file_loadBalancer_proto_msgTypes,
	}.Build()
	File_loadBalancer_proto = out.File
	file_loadBalancer_proto_rawDesc = nil
	file_loadBalancer_proto_goTypes = nil
	file_loadBalancer_proto_depIdxs = nil
}
