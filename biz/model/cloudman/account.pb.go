// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: account.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AccountParamsType int32

const (
	AccountParamsType_host  AccountParamsType = 0
	AccountParamsType_mysql AccountParamsType = 1
	AccountParamsType_redis AccountParamsType = 2
)

// Enum value maps for AccountParamsType.
var (
	AccountParamsType_name = map[int32]string{
		0: "host",
		1: "mysql",
		2: "redis",
	}
	AccountParamsType_value = map[string]int32{
		"host":  0,
		"mysql": 1,
		"redis": 2,
	}
)

func (x AccountParamsType) Enum() *AccountParamsType {
	p := new(AccountParamsType)
	*p = x
	return p
}

func (x AccountParamsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountParamsType) Descriptor() protoreflect.EnumDescriptor {
	return file_account_proto_enumTypes[0].Descriptor()
}

func (AccountParamsType) Type() protoreflect.EnumType {
	return &file_account_proto_enumTypes[0]
}

func (x AccountParamsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountParamsType.Descriptor instead.
func (AccountParamsType) EnumDescriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{0}
}

type Status int32

const (
	Status_enabled  Status = 0
	Status_disabled Status = 1
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "enabled",
		1: "disabled",
	}
	Status_value = map[string]int32{
		"enabled":  0,
		"disabled": 1,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_account_proto_enumTypes[1].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_account_proto_enumTypes[1]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{1}
}

type PingResult_Res int32

const (
	PingResult_PongSKFromDB  PingResult_Res = 0
	PingResult_NoReply       PingResult_Res = 1
	PingResult_PongSKFromReq PingResult_Res = 2
)

// Enum value maps for PingResult_Res.
var (
	PingResult_Res_name = map[int32]string{
		0: "PongSKFromDB",
		1: "NoReply",
		2: "PongSKFromReq",
	}
	PingResult_Res_value = map[string]int32{
		"PongSKFromDB":  0,
		"NoReply":       1,
		"PongSKFromReq": 2,
	}
)

func (x PingResult_Res) Enum() *PingResult_Res {
	p := new(PingResult_Res)
	*p = x
	return p
}

func (x PingResult_Res) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PingResult_Res) Descriptor() protoreflect.EnumDescriptor {
	return file_account_proto_enumTypes[2].Descriptor()
}

func (PingResult_Res) Type() protoreflect.EnumType {
	return &file_account_proto_enumTypes[2]
}

func (x PingResult_Res) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PingResult_Res.Descriptor instead.
func (PingResult_Res) EnumDescriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{6, 0}
}

type AccountId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
}

func (x *AccountId) Reset() {
	*x = AccountId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountId) ProtoMessage() {}

func (x *AccountId) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountId.ProtoReflect.Descriptor instead.
func (*AccountId) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{0}
}

func (x *AccountId) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type AccountQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size     uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Statue   string   `protobuf:"bytes,5,opt,name=statue,proto3" form:"statue" json:"statue" query:"statue"`
	Name     string   `protobuf:"bytes,6,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Type     string   `protobuf:"bytes,7,opt,name=type,proto3" form:"type" json:"type" query:"type"`
}

func (x *AccountQueryReq) Reset() {
	*x = AccountQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountQueryReq) ProtoMessage() {}

func (x *AccountQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountQueryReq.ProtoReflect.Descriptor instead.
func (*AccountQueryReq) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{1}
}

func (x *AccountQueryReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AccountQueryReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AccountQueryReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *AccountQueryReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *AccountQueryReq) GetStatue() string {
	if x != nil {
		return x.Statue
	}
	return ""
}

func (x *AccountQueryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountQueryReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type AccountListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32           `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*AccountDetail `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *AccountListRes) Reset() {
	*x = AccountListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountListRes) ProtoMessage() {}

func (x *AccountListRes) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountListRes.ProtoReflect.Descriptor instead.
func (*AccountListRes) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{2}
}

func (x *AccountListRes) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AccountListRes) GetList() []*AccountDetail {
	if x != nil {
		return x.List
	}
	return nil
}

type CreateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              int32     `protobuf:"varint,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Name                string    `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	AType               string    `protobuf:"bytes,3,opt,name=a_type,json=aType,proto3" form:"a_type" json:"a_type" query:"a_type"`
	AccessKey           string    `protobuf:"bytes,4,opt,name=access_key,json=accessKey,proto3" form:"access_key" json:"access_key" query:"access_key"`
	AccessSecret        string    `protobuf:"bytes,5,opt,name=access_secret,json=accessSecret,proto3" form:"access_secret" json:"access_secret" query:"access_secret"`
	RegionIds           []string  `protobuf:"bytes,6,rep,name=region_ids,json=regionIds,proto3" form:"region_ids" json:"region_ids" query:"region_ids"`
	UserIds             []*UserId `protobuf:"bytes,7,rep,name=user_ids,json=userIds,proto3" form:"user_ids" json:"user_ids" query:"user_ids"`
	IndexId             uint32    `protobuf:"varint,8,opt,name=index_id,json=indexId,proto3" form:"index_id" json:"index_id" query:"index_id"`
	HighLevelPermission bool      `protobuf:"varint,9,opt,name=high_level_permission,json=highLevelPermission,proto3" form:"high_level_permission" json:"high_level_permission" query:"high_level_permission"`
	UseAgent            bool      `protobuf:"varint,10,opt,name=use_agent,json=useAgent,proto3" form:"use_agent" json:"use_agent" query:"use_agent"`
	Arn                 string    `protobuf:"bytes,11,opt,name=arn,proto3" form:"arn" json:"arn" query:"arn"`
	Host                string    `protobuf:"bytes,12,opt,name=host,proto3" form:"host" json:"host" query:"host"`
	KmsName             string    `protobuf:"bytes,13,opt,name=kms_name,json=kmsName,proto3" form:"kms_name" json:"kms_name" query:"kms_name"`
	ProjectId           int32     `protobuf:"varint,14,opt,name=project_id,json=projectId,proto3" form:"project_id" json:"project_id" query:"project_id"`
}

func (x *CreateAccountRequest) Reset() {
	*x = CreateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest) ProtoMessage() {}

func (x *CreateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAccountRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateAccountRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateAccountRequest) GetAType() string {
	if x != nil {
		return x.AType
	}
	return ""
}

func (x *CreateAccountRequest) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *CreateAccountRequest) GetAccessSecret() string {
	if x != nil {
		return x.AccessSecret
	}
	return ""
}

func (x *CreateAccountRequest) GetRegionIds() []string {
	if x != nil {
		return x.RegionIds
	}
	return nil
}

func (x *CreateAccountRequest) GetUserIds() []*UserId {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *CreateAccountRequest) GetIndexId() uint32 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *CreateAccountRequest) GetHighLevelPermission() bool {
	if x != nil {
		return x.HighLevelPermission
	}
	return false
}

func (x *CreateAccountRequest) GetUseAgent() bool {
	if x != nil {
		return x.UseAgent
	}
	return false
}

func (x *CreateAccountRequest) GetArn() string {
	if x != nil {
		return x.Arn
	}
	return ""
}

func (x *CreateAccountRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *CreateAccountRequest) GetKmsName() string {
	if x != nil {
		return x.KmsName
	}
	return ""
}

func (x *CreateAccountRequest) GetProjectId() int32 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type UpdateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  string    `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
	Status              int32     `protobuf:"varint,2,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Name                string    `protobuf:"bytes,3,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	AType               string    `protobuf:"bytes,4,opt,name=a_type,json=aType,proto3" form:"a_type" json:"a_type" query:"a_type"`
	AccessKey           string    `protobuf:"bytes,5,opt,name=access_key,json=accessKey,proto3" form:"access_key" json:"access_key" query:"access_key"`
	AccessSecret        string    `protobuf:"bytes,6,opt,name=access_secret,json=accessSecret,proto3" form:"access_secret" json:"access_secret" query:"access_secret"`
	RegionIds           []string  `protobuf:"bytes,7,rep,name=region_ids,json=regionIds,proto3" form:"region_ids" json:"region_ids" query:"region_ids"`
	UserIds             []*UserId `protobuf:"bytes,8,rep,name=user_ids,json=userIds,proto3" form:"user_ids" json:"user_ids" query:"user_ids"`
	IndexId             uint32    `protobuf:"varint,9,opt,name=index_id,json=indexId,proto3" form:"index_id" json:"index_id" query:"index_id"`
	HighLevelPermission bool      `protobuf:"varint,10,opt,name=high_level_permission,json=highLevelPermission,proto3" form:"high_level_permission" json:"high_level_permission" query:"high_level_permission"`
	UseAgent            bool      `protobuf:"varint,11,opt,name=use_agent,json=useAgent,proto3" form:"use_agent" json:"use_agent" query:"use_agent"`
	Host                string    `protobuf:"bytes,12,opt,name=host,proto3" form:"host" json:"host" query:"host"`
	KmsName             string    `protobuf:"bytes,13,opt,name=kms_name,json=kmsName,proto3" form:"kms_name" json:"kms_name" query:"kms_name"`
	ProjectId           int32     `protobuf:"varint,14,opt,name=project_id,json=projectId,proto3" form:"project_id" json:"project_id" query:"project_id"`
}

func (x *UpdateAccountRequest) Reset() {
	*x = UpdateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountRequest) ProtoMessage() {}

func (x *UpdateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountRequest.ProtoReflect.Descriptor instead.
func (*UpdateAccountRequest) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAccountRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateAccountRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateAccountRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateAccountRequest) GetAType() string {
	if x != nil {
		return x.AType
	}
	return ""
}

func (x *UpdateAccountRequest) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *UpdateAccountRequest) GetAccessSecret() string {
	if x != nil {
		return x.AccessSecret
	}
	return ""
}

func (x *UpdateAccountRequest) GetRegionIds() []string {
	if x != nil {
		return x.RegionIds
	}
	return nil
}

func (x *UpdateAccountRequest) GetUserIds() []*UserId {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *UpdateAccountRequest) GetIndexId() uint32 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *UpdateAccountRequest) GetHighLevelPermission() bool {
	if x != nil {
		return x.HighLevelPermission
	}
	return false
}

func (x *UpdateAccountRequest) GetUseAgent() bool {
	if x != nil {
		return x.UseAgent
	}
	return false
}

func (x *UpdateAccountRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *UpdateAccountRequest) GetKmsName() string {
	if x != nil {
		return x.KmsName
	}
	return ""
}

func (x *UpdateAccountRequest) GetProjectId() int32 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type AccountDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              uint32    `protobuf:"varint,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Name                string    `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	AType               string    `protobuf:"bytes,3,opt,name=a_type,json=aType,proto3" form:"a_type" json:"a_type" query:"a_type"`
	AccessKey           string    `protobuf:"bytes,4,opt,name=access_key,json=accessKey,proto3" form:"access_key" json:"access_key" query:"access_key"`
	AccessSecret        string    `protobuf:"bytes,5,opt,name=access_secret,json=accessSecret,proto3" form:"access_secret" json:"access_secret" query:"access_secret"`
	Region              []*Region `protobuf:"bytes,7,rep,name=region,proto3" form:"region" json:"region" query:"region"`
	Id                  string    `protobuf:"bytes,8,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	PingStatus          int32     `protobuf:"varint,9,opt,name=ping_status,json=pingStatus,proto3" form:"ping_status" json:"ping_status" query:"ping_status"`
	UserIds             []*UserId `protobuf:"bytes,10,rep,name=user_ids,json=userIds,proto3" form:"user_ids" json:"user_ids" query:"user_ids"`
	IndexId             uint32    `protobuf:"varint,11,opt,name=index_id,json=indexId,proto3" form:"index_id" json:"index_id" query:"index_id"`
	HighLevelPermission bool      `protobuf:"varint,12,opt,name=high_level_permission,json=highLevelPermission,proto3" form:"high_level_permission" json:"high_level_permission" query:"high_level_permission"`
	UseAgent            bool      `protobuf:"varint,13,opt,name=use_agent,json=useAgent,proto3" form:"use_agent" json:"use_agent" query:"use_agent"`
	AgentStatus         int32     `protobuf:"varint,14,opt,name=agent_status,json=agentStatus,proto3" form:"agent_status" json:"agent_status" query:"agent_status"`
	UpdateUser          string    `protobuf:"bytes,15,opt,name=update_user,json=updateUser,proto3" form:"update_user" json:"update_user" query:"update_user"`
	UpdatedTime         int64     `protobuf:"varint,16,opt,name=updated_time,json=updatedTime,proto3" form:"updated_time" json:"updated_time" query:"updated_time"`
	Host                string    `protobuf:"bytes,17,opt,name=host,proto3" form:"host" json:"host" query:"host"`
	KmsName             string    `protobuf:"bytes,18,opt,name=kms_name,json=kmsName,proto3" form:"kms_name" json:"kms_name" query:"kms_name"`
	ProjectId           int32     `protobuf:"varint,19,opt,name=project_id,json=projectId,proto3" form:"project_id" json:"project_id" query:"project_id"`
}

func (x *AccountDetail) Reset() {
	*x = AccountDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDetail) ProtoMessage() {}

func (x *AccountDetail) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDetail.ProtoReflect.Descriptor instead.
func (*AccountDetail) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{5}
}

func (x *AccountDetail) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AccountDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountDetail) GetAType() string {
	if x != nil {
		return x.AType
	}
	return ""
}

func (x *AccountDetail) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *AccountDetail) GetAccessSecret() string {
	if x != nil {
		return x.AccessSecret
	}
	return ""
}

func (x *AccountDetail) GetRegion() []*Region {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *AccountDetail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AccountDetail) GetPingStatus() int32 {
	if x != nil {
		return x.PingStatus
	}
	return 0
}

func (x *AccountDetail) GetUserIds() []*UserId {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *AccountDetail) GetIndexId() uint32 {
	if x != nil {
		return x.IndexId
	}
	return 0
}

func (x *AccountDetail) GetHighLevelPermission() bool {
	if x != nil {
		return x.HighLevelPermission
	}
	return false
}

func (x *AccountDetail) GetUseAgent() bool {
	if x != nil {
		return x.UseAgent
	}
	return false
}

func (x *AccountDetail) GetAgentStatus() int32 {
	if x != nil {
		return x.AgentStatus
	}
	return 0
}

func (x *AccountDetail) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

func (x *AccountDetail) GetUpdatedTime() int64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *AccountDetail) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *AccountDetail) GetKmsName() string {
	if x != nil {
		return x.KmsName
	}
	return ""
}

func (x *AccountDetail) GetProjectId() int32 {
	if x != nil {
		return x.ProjectId
	}
	return 0
}

type PingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result PingResult_Res `protobuf:"varint,1,opt,name=result,proto3,enum=cloudman.PingResult_Res" form:"result" json:"result" query:"result"`
}

func (x *PingResult) Reset() {
	*x = PingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingResult) ProtoMessage() {}

func (x *PingResult) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingResult.ProtoReflect.Descriptor instead.
func (*PingResult) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{6}
}

func (x *PingResult) GetResult() PingResult_Res {
	if x != nil {
		return x.Result
	}
	return PingResult_PongSKFromDB
}

type UpdateAccountStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
	Status int64  `protobuf:"varint,2,opt,name=status,proto3" form:"status" json:"status" query:"status"`
}

func (x *UpdateAccountStatusReq) Reset() {
	*x = UpdateAccountStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountStatusReq) ProtoMessage() {}

func (x *UpdateAccountStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateAccountStatusReq) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateAccountStatusReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateAccountStatusReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type RegionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Regions []*Region `protobuf:"bytes,1,rep,name=regions,proto3" form:"regions" json:"regions" query:"regions"`
}

func (x *RegionResult) Reset() {
	*x = RegionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionResult) ProtoMessage() {}

func (x *RegionResult) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionResult.ProtoReflect.Descriptor instead.
func (*RegionResult) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{8}
}

func (x *RegionResult) GetRegions() []*Region {
	if x != nil {
		return x.Regions
	}
	return nil
}

type Region struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalName string `protobuf:"bytes,1,opt,name=local_name,json=localName,proto3" form:"local_name" json:"local_name" query:"local_name"`
	RegionId  string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IspType   string `protobuf:"bytes,3,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	Id        string `protobuf:"bytes,4,opt,name=id,proto3" form:"id" json:"id" query:"id"`
}

func (x *Region) Reset() {
	*x = Region{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Region) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Region) ProtoMessage() {}

func (x *Region) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Region.ProtoReflect.Descriptor instead.
func (*Region) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{9}
}

func (x *Region) GetLocalName() string {
	if x != nil {
		return x.LocalName
	}
	return ""
}

func (x *Region) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *Region) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *Region) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UserId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
}

func (x *UserId) Reset() {
	*x = UserId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserId) ProtoMessage() {}

func (x *UserId) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserId.ProtoReflect.Descriptor instead.
func (*UserId) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{10}
}

func (x *UserId) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ListAccountParamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page          uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size          uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering      []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords      string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	ParamType     string   `protobuf:"bytes,5,opt,name=paramType,proto3" form:"paramType" json:"paramType" query:"paramType"`
	BindRegionId  string   `protobuf:"bytes,6,opt,name=bind_region_id,json=bindRegionId,proto3" form:"bind_region_id" json:"bind_region_id" query:"bind_region_id"`
	BindAccountId string   `protobuf:"bytes,7,opt,name=bind_account_id,json=bindAccountId,proto3" json:"bind_account_id" path:"accountId"`
}

func (x *ListAccountParamReq) Reset() {
	*x = ListAccountParamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountParamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountParamReq) ProtoMessage() {}

func (x *ListAccountParamReq) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountParamReq.ProtoReflect.Descriptor instead.
func (*ListAccountParamReq) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{11}
}

func (x *ListAccountParamReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAccountParamReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListAccountParamReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *ListAccountParamReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *ListAccountParamReq) GetParamType() string {
	if x != nil {
		return x.ParamType
	}
	return ""
}

func (x *ListAccountParamReq) GetBindRegionId() string {
	if x != nil {
		return x.BindRegionId
	}
	return ""
}

func (x *ListAccountParamReq) GetBindAccountId() string {
	if x != nil {
		return x.BindAccountId
	}
	return ""
}

type ListAccountParamResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32              `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*AccountParamInfo `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *ListAccountParamResp) Reset() {
	*x = ListAccountParamResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccountParamResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccountParamResp) ProtoMessage() {}

func (x *ListAccountParamResp) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccountParamResp.ProtoReflect.Descriptor instead.
func (*ListAccountParamResp) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{12}
}

func (x *ListAccountParamResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListAccountParamResp) GetList() []*AccountParamInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type AccountParamInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
	ParamType     AccountParamsType `protobuf:"varint,2,opt,name=param_type,json=paramType,proto3,enum=cloudman.AccountParamsType" form:"param_type" json:"param_type" query:"param_type"`
	BindRegionId  string            `protobuf:"bytes,3,opt,name=bind_region_id,json=bindRegionId,proto3" form:"bind_region_id" json:"bind_region_id" query:"bind_region_id"`
	Params        string            `protobuf:"bytes,4,opt,name=params,proto3" form:"params" json:"params" query:"params"`
	BindAccountId string            `protobuf:"bytes,5,opt,name=bind_account_id,json=bindAccountId,proto3" json:"bind_account_id" path:"accountId"`
	Status        Status            `protobuf:"varint,6,opt,name=status,proto3,enum=cloudman.Status" form:"status" json:"status" query:"status"`
	UpdateTime    int32             `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" form:"update_time" json:"update_time" query:"update_time"`
	UpdateUser    string            `protobuf:"bytes,8,opt,name=update_user,json=updateUser,proto3" form:"update_user" json:"update_user" query:"update_user"`
	CreateTime    int32             `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" form:"create_time" json:"create_time" query:"create_time"`
	CreateUser    string            `protobuf:"bytes,10,opt,name=create_user,json=createUser,proto3" form:"create_user" json:"create_user" query:"create_user"`
}

func (x *AccountParamInfo) Reset() {
	*x = AccountParamInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountParamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountParamInfo) ProtoMessage() {}

func (x *AccountParamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountParamInfo.ProtoReflect.Descriptor instead.
func (*AccountParamInfo) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{13}
}

func (x *AccountParamInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AccountParamInfo) GetParamType() AccountParamsType {
	if x != nil {
		return x.ParamType
	}
	return AccountParamsType_host
}

func (x *AccountParamInfo) GetBindRegionId() string {
	if x != nil {
		return x.BindRegionId
	}
	return ""
}

func (x *AccountParamInfo) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

func (x *AccountParamInfo) GetBindAccountId() string {
	if x != nil {
		return x.BindAccountId
	}
	return ""
}

func (x *AccountParamInfo) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_enabled
}

func (x *AccountParamInfo) GetUpdateTime() int32 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *AccountParamInfo) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

func (x *AccountParamInfo) GetCreateTime() int32 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *AccountParamInfo) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

type SysConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnableWorkOrder      bool `protobuf:"varint,1,opt,name=enable_work_order,json=enableWorkOrder,proto3" form:"enable_work_order" json:"enable_work_order" query:"enable_work_order"`
	ShowTagValue         bool `protobuf:"varint,2,opt,name=show_tag_value,json=showTagValue,proto3" form:"show_tag_value" json:"show_tag_value" query:"show_tag_value"`
	AutoRefreshOpsStatus bool `protobuf:"varint,3,opt,name=auto_refresh_ops_status,json=autoRefreshOpsStatus,proto3" form:"auto_refresh_ops_status" json:"auto_refresh_ops_status" query:"auto_refresh_ops_status"`
}

func (x *SysConfig) Reset() {
	*x = SysConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_account_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SysConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SysConfig) ProtoMessage() {}

func (x *SysConfig) ProtoReflect() protoreflect.Message {
	mi := &file_account_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SysConfig.ProtoReflect.Descriptor instead.
func (*SysConfig) Descriptor() ([]byte, []int) {
	return file_account_proto_rawDescGZIP(), []int{14}
}

func (x *SysConfig) GetEnableWorkOrder() bool {
	if x != nil {
		return x.EnableWorkOrder
	}
	return false
}

func (x *SysConfig) GetShowTagValue() bool {
	if x != nil {
		return x.ShowTagValue
	}
	return false
}

func (x *SysConfig) GetAutoRefreshOpsStatus() bool {
	if x != nil {
		return x.AutoRefreshOpsStatus
	}
	return false
}

var File_account_proto protoreflect.FileDescriptor

var file_account_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x22, 0xb1, 0x01, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x53, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2b, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb6, 0x03, 0x0a, 0x14, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x52, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x68, 0x69, 0x67, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73, 0x65, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x72, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x61, 0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x6d, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x6d, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x22, 0xbc, 0x03, 0x0a, 0x14, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x52,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x13, 0x68, 0x69, 0x67, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x6d, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x6d, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x22, 0xc0, 0x04, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12,
	0x32, 0x0a, 0x15, 0x68, 0x69, 0x67, 0x68, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13,
	0x68, 0x69, 0x67, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x75, 0x73, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6b,
	0x6d, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b,
	0x6d, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x70,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x37, 0x0a, 0x03, 0x52, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x0c,
	0x50, 0x6f, 0x6e, 0x67, 0x53, 0x4b, 0x46, 0x72, 0x6f, 0x6d, 0x44, 0x42, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x4e, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x50,
	0x6f, 0x6e, 0x67, 0x53, 0x4b, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x10, 0x02, 0x22, 0x48,
	0x0a, 0x16, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x3a, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a, 0x0a, 0x07, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x6f, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x19, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xf0, 0x01, 0x0a, 0x13, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0f,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xd2, 0xbb, 0x18, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x5c, 0x0a, 0x14, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x89, 0x03, 0x0a, 0x10, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a,
	0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x09, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x69,
	0x6e, 0x64, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x35, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0d, 0xd2, 0xbb, 0x18, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x22, 0x94, 0x01,
	0x0a, 0x09, 0x53, 0x79, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x68, 0x6f, 0x77, 0x5f,
	0x74, 0x61, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x61, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x35, 0x0a,
	0x17, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x6f, 0x70,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14,
	0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x4f, 0x70, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2a, 0x33, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x68, 0x6f, 0x73,
	0x74, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x10, 0x01, 0x12, 0x09,
	0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x10, 0x02, 0x2a, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x01, 0x42, 0x3b,
	0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_account_proto_rawDescOnce sync.Once
	file_account_proto_rawDescData = file_account_proto_rawDesc
)

func file_account_proto_rawDescGZIP() []byte {
	file_account_proto_rawDescOnce.Do(func() {
		file_account_proto_rawDescData = protoimpl.X.CompressGZIP(file_account_proto_rawDescData)
	})
	return file_account_proto_rawDescData
}

var file_account_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_account_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_account_proto_goTypes = []interface{}{
	(AccountParamsType)(0),         // 0: cloudman.accountParamsType
	(Status)(0),                    // 1: cloudman.status
	(PingResult_Res)(0),            // 2: cloudman.pingResult.Res
	(*AccountId)(nil),              // 3: cloudman.accountId
	(*AccountQueryReq)(nil),        // 4: cloudman.accountQueryReq
	(*AccountListRes)(nil),         // 5: cloudman.accountListRes
	(*CreateAccountRequest)(nil),   // 6: cloudman.createAccountRequest
	(*UpdateAccountRequest)(nil),   // 7: cloudman.updateAccountRequest
	(*AccountDetail)(nil),          // 8: cloudman.accountDetail
	(*PingResult)(nil),             // 9: cloudman.pingResult
	(*UpdateAccountStatusReq)(nil), // 10: cloudman.updateAccountStatusReq
	(*RegionResult)(nil),           // 11: cloudman.regionResult
	(*Region)(nil),                 // 12: cloudman.region
	(*UserId)(nil),                 // 13: cloudman.user_id
	(*ListAccountParamReq)(nil),    // 14: cloudman.listAccountParamReq
	(*ListAccountParamResp)(nil),   // 15: cloudman.listAccountParamResp
	(*AccountParamInfo)(nil),       // 16: cloudman.accountParamInfo
	(*SysConfig)(nil),              // 17: cloudman.SysConfig
}
var file_account_proto_depIdxs = []int32{
	8,  // 0: cloudman.accountListRes.list:type_name -> cloudman.accountDetail
	13, // 1: cloudman.createAccountRequest.user_ids:type_name -> cloudman.user_id
	13, // 2: cloudman.updateAccountRequest.user_ids:type_name -> cloudman.user_id
	12, // 3: cloudman.accountDetail.region:type_name -> cloudman.region
	13, // 4: cloudman.accountDetail.user_ids:type_name -> cloudman.user_id
	2,  // 5: cloudman.pingResult.result:type_name -> cloudman.pingResult.Res
	12, // 6: cloudman.regionResult.regions:type_name -> cloudman.region
	16, // 7: cloudman.listAccountParamResp.list:type_name -> cloudman.accountParamInfo
	0,  // 8: cloudman.accountParamInfo.param_type:type_name -> cloudman.accountParamsType
	1,  // 9: cloudman.accountParamInfo.status:type_name -> cloudman.status
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_account_proto_init() }
func file_account_proto_init() {
	if File_account_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_account_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Region); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountParamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccountParamResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountParamInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_account_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SysConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_account_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_account_proto_goTypes,
		DependencyIndexes: file_account_proto_depIdxs,
		EnumInfos:         file_account_proto_enumTypes,
		MessageInfos:      file_account_proto_msgTypes,
	}.Build()
	File_account_proto = out.File
	file_account_proto_rawDesc = nil
	file_account_proto_goTypes = nil
	file_account_proto_depIdxs = nil
}
