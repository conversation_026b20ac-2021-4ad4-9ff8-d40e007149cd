// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: dashBoard.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DashBoardAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DashBoardAccountRequest) Reset() {
	*x = DashBoardAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardAccountRequest) ProtoMessage() {}

func (x *DashBoardAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardAccountRequest.ProtoReflect.Descriptor instead.
func (*DashBoardAccountRequest) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{0}
}

type DashBoardAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DashBoardAccountDetail `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DashBoardAccountResponse) Reset() {
	*x = DashBoardAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardAccountResponse) ProtoMessage() {}

func (x *DashBoardAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardAccountResponse.ProtoReflect.Descriptor instead.
func (*DashBoardAccountResponse) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{1}
}

func (x *DashBoardAccountResponse) GetList() []*DashBoardAccountDetail {
	if x != nil {
		return x.List
	}
	return nil
}

type DashBoardAccountDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string                    `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	AccountName string                    `protobuf:"bytes,2,opt,name=account_name,json=accountName,proto3" form:"account_name" json:"account_name" query:"account_name"`
	AccountType string                    `protobuf:"bytes,3,opt,name=account_type,json=accountType,proto3" form:"account_type" json:"account_type" query:"account_type"`
	Regions     []*DashBoardAccountRegion `protobuf:"bytes,4,rep,name=regions,proto3" form:"regions" json:"regions" query:"regions"`
}

func (x *DashBoardAccountDetail) Reset() {
	*x = DashBoardAccountDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardAccountDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardAccountDetail) ProtoMessage() {}

func (x *DashBoardAccountDetail) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardAccountDetail.ProtoReflect.Descriptor instead.
func (*DashBoardAccountDetail) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{2}
}

func (x *DashBoardAccountDetail) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DashBoardAccountDetail) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *DashBoardAccountDetail) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *DashBoardAccountDetail) GetRegions() []*DashBoardAccountRegion {
	if x != nil {
		return x.Regions
	}
	return nil
}

type DashBoardAccountRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId   string `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	RegionName string `protobuf:"bytes,2,opt,name=region_name,json=regionName,proto3" form:"region_name" json:"region_name" query:"region_name"`
}

func (x *DashBoardAccountRegion) Reset() {
	*x = DashBoardAccountRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardAccountRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardAccountRegion) ProtoMessage() {}

func (x *DashBoardAccountRegion) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardAccountRegion.ProtoReflect.Descriptor instead.
func (*DashBoardAccountRegion) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{3}
}

func (x *DashBoardAccountRegion) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DashBoardAccountRegion) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

type DashBoardUserAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DashBoardUserAuthRequest) Reset() {
	*x = DashBoardUserAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardUserAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardUserAuthRequest) ProtoMessage() {}

func (x *DashBoardUserAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardUserAuthRequest.ProtoReflect.Descriptor instead.
func (*DashBoardUserAuthRequest) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{4}
}

type DashBoardUserAuthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Auth string `protobuf:"bytes,1,opt,name=auth,proto3" form:"auth" json:"auth" query:"auth"`
}

func (x *DashBoardUserAuthResponse) Reset() {
	*x = DashBoardUserAuthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashBoardUserAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashBoardUserAuthResponse) ProtoMessage() {}

func (x *DashBoardUserAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashBoardUserAuthResponse.ProtoReflect.Descriptor instead.
func (*DashBoardUserAuthResponse) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{5}
}

func (x *DashBoardUserAuthResponse) GetAuth() string {
	if x != nil {
		return x.Auth
	}
	return ""
}

type DashboardIfWorkOrderUsedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsUsed bool `protobuf:"varint,1,opt,name=isUsed,proto3" form:"isUsed" json:"isUsed" query:"isUsed"`
}

func (x *DashboardIfWorkOrderUsedResponse) Reset() {
	*x = DashboardIfWorkOrderUsedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardIfWorkOrderUsedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardIfWorkOrderUsedResponse) ProtoMessage() {}

func (x *DashboardIfWorkOrderUsedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardIfWorkOrderUsedResponse.ProtoReflect.Descriptor instead.
func (*DashboardIfWorkOrderUsedResponse) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{6}
}

func (x *DashboardIfWorkOrderUsedResponse) GetIsUsed() bool {
	if x != nil {
		return x.IsUsed
	}
	return false
}

type DashboardBackendVersionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BuildVersion string `protobuf:"bytes,1,opt,name=buildVersion,proto3" form:"buildVersion" json:"buildVersion" query:"buildVersion"`
	BuildCommit  string `protobuf:"bytes,2,opt,name=buildCommit,proto3" form:"buildCommit" json:"buildCommit" query:"buildCommit"`
	BuildDate    string `protobuf:"bytes,3,opt,name=buildDate,proto3" form:"buildDate" json:"buildDate" query:"buildDate"`
}

func (x *DashboardBackendVersionResponse) Reset() {
	*x = DashboardBackendVersionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardBackendVersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardBackendVersionResponse) ProtoMessage() {}

func (x *DashboardBackendVersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardBackendVersionResponse.ProtoReflect.Descriptor instead.
func (*DashboardBackendVersionResponse) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{7}
}

func (x *DashboardBackendVersionResponse) GetBuildVersion() string {
	if x != nil {
		return x.BuildVersion
	}
	return ""
}

func (x *DashboardBackendVersionResponse) GetBuildCommit() string {
	if x != nil {
		return x.BuildCommit
	}
	return ""
}

func (x *DashboardBackendVersionResponse) GetBuildDate() string {
	if x != nil {
		return x.BuildDate
	}
	return ""
}

type DashboardHostBriefReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string   `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionIds []string `protobuf:"bytes,2,rep,name=region_ids,json=regionIds,proto3" form:"region_ids" json:"region_ids" query:"region_ids"`
}

func (x *DashboardHostBriefReq) Reset() {
	*x = DashboardHostBriefReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHostBriefReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHostBriefReq) ProtoMessage() {}

func (x *DashboardHostBriefReq) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHostBriefReq.ProtoReflect.Descriptor instead.
func (*DashboardHostBriefReq) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{8}
}

func (x *DashboardHostBriefReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DashboardHostBriefReq) GetRegionIds() []string {
	if x != nil {
		return x.RegionIds
	}
	return nil
}

type DashboardHostBriefResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DashboardHostBrief `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DashboardHostBriefResp) Reset() {
	*x = DashboardHostBriefResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHostBriefResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHostBriefResp) ProtoMessage() {}

func (x *DashboardHostBriefResp) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHostBriefResp.ProtoReflect.Descriptor instead.
func (*DashboardHostBriefResp) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{9}
}

func (x *DashboardHostBriefResp) GetList() []*DashboardHostBrief {
	if x != nil {
		return x.List
	}
	return nil
}

type DashboardHostBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId        string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionId         string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	HostTotal        int32  `protobuf:"varint,11,opt,name=host_total,json=hostTotal,proto3" form:"host_total" json:"host_total" query:"host_total"`
	HostRunning      int32  `protobuf:"varint,12,opt,name=host_running,json=hostRunning,proto3" form:"host_running" json:"host_running" query:"host_running"`
	HostAgentRunning int32  `protobuf:"varint,13,opt,name=host_agent_running,json=hostAgentRunning,proto3" form:"host_agent_running" json:"host_agent_running" query:"host_agent_running"`
	Host_7DayCreated int32  `protobuf:"varint,21,opt,name=host_7day_created,json=host7dayCreated,proto3" form:"host_7day_created" json:"host_7day_created" query:"host_7day_created"`
	Host_7DayDeleted int32  `protobuf:"varint,22,opt,name=host_7day_deleted,json=host7dayDeleted,proto3" form:"host_7day_deleted" json:"host_7day_deleted" query:"host_7day_deleted"`
	Host_7DayExpire  int32  `protobuf:"varint,23,opt,name=host_7day_expire,json=host7dayExpire,proto3" form:"host_7day_expire" json:"host_7day_expire" query:"host_7day_expire"`
	HostNeedCleanup  int32  `protobuf:"varint,31,opt,name=host_need_cleanup,json=hostNeedCleanup,proto3" form:"host_need_cleanup" json:"host_need_cleanup" query:"host_need_cleanup"`
	HostInRecycle    int32  `protobuf:"varint,32,opt,name=host_in_recycle,json=hostInRecycle,proto3" form:"host_in_recycle" json:"host_in_recycle" query:"host_in_recycle"`
}

func (x *DashboardHostBrief) Reset() {
	*x = DashboardHostBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHostBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHostBrief) ProtoMessage() {}

func (x *DashboardHostBrief) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHostBrief.ProtoReflect.Descriptor instead.
func (*DashboardHostBrief) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{10}
}

func (x *DashboardHostBrief) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DashboardHostBrief) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DashboardHostBrief) GetHostTotal() int32 {
	if x != nil {
		return x.HostTotal
	}
	return 0
}

func (x *DashboardHostBrief) GetHostRunning() int32 {
	if x != nil {
		return x.HostRunning
	}
	return 0
}

func (x *DashboardHostBrief) GetHostAgentRunning() int32 {
	if x != nil {
		return x.HostAgentRunning
	}
	return 0
}

func (x *DashboardHostBrief) GetHost_7DayCreated() int32 {
	if x != nil {
		return x.Host_7DayCreated
	}
	return 0
}

func (x *DashboardHostBrief) GetHost_7DayDeleted() int32 {
	if x != nil {
		return x.Host_7DayDeleted
	}
	return 0
}

func (x *DashboardHostBrief) GetHost_7DayExpire() int32 {
	if x != nil {
		return x.Host_7DayExpire
	}
	return 0
}

func (x *DashboardHostBrief) GetHostNeedCleanup() int32 {
	if x != nil {
		return x.HostNeedCleanup
	}
	return 0
}

func (x *DashboardHostBrief) GetHostInRecycle() int32 {
	if x != nil {
		return x.HostInRecycle
	}
	return 0
}

type DashboardResBriefReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string   `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionIds []string `protobuf:"bytes,2,rep,name=region_ids,json=regionIds,proto3" form:"region_ids" json:"region_ids" query:"region_ids"`
	ResType   string   `protobuf:"bytes,3,opt,name=res_type,json=resType,proto3" form:"res_type" json:"res_type" query:"res_type"`
}

func (x *DashboardResBriefReq) Reset() {
	*x = DashboardResBriefReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResBriefReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResBriefReq) ProtoMessage() {}

func (x *DashboardResBriefReq) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResBriefReq.ProtoReflect.Descriptor instead.
func (*DashboardResBriefReq) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{11}
}

func (x *DashboardResBriefReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DashboardResBriefReq) GetRegionIds() []string {
	if x != nil {
		return x.RegionIds
	}
	return nil
}

func (x *DashboardResBriefReq) GetResType() string {
	if x != nil {
		return x.ResType
	}
	return ""
}

type DashboardResBriefResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DashboardResBrief `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DashboardResBriefResp) Reset() {
	*x = DashboardResBriefResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResBriefResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResBriefResp) ProtoMessage() {}

func (x *DashboardResBriefResp) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResBriefResp.ProtoReflect.Descriptor instead.
func (*DashboardResBriefResp) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{12}
}

func (x *DashboardResBriefResp) GetList() []*DashboardResBrief {
	if x != nil {
		return x.List
	}
	return nil
}

type DashboardResBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId       string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionId        string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	ResTotal        int32  `protobuf:"varint,11,opt,name=res_total,json=resTotal,proto3" form:"res_total" json:"res_total" query:"res_total"`
	ResRunning      int32  `protobuf:"varint,12,opt,name=res_running,json=resRunning,proto3" form:"res_running" json:"res_running" query:"res_running"`
	ResAgentRunning int32  `protobuf:"varint,13,opt,name=res_agent_running,json=resAgentRunning,proto3" form:"res_agent_running" json:"res_agent_running" query:"res_agent_running"`
	Res_7DayCreated int32  `protobuf:"varint,21,opt,name=res_7day_created,json=res7dayCreated,proto3" form:"res_7day_created" json:"res_7day_created" query:"res_7day_created"`
	Res_7DayDeleted int32  `protobuf:"varint,22,opt,name=res_7day_deleted,json=res7dayDeleted,proto3" form:"res_7day_deleted" json:"res_7day_deleted" query:"res_7day_deleted"`
	Res_7DayExpire  int32  `protobuf:"varint,23,opt,name=res_7day_expire,json=res7dayExpire,proto3" form:"res_7day_expire" json:"res_7day_expire" query:"res_7day_expire"`
	ResNeedCleanup  int32  `protobuf:"varint,31,opt,name=res_need_cleanup,json=resNeedCleanup,proto3" form:"res_need_cleanup" json:"res_need_cleanup" query:"res_need_cleanup"`
	ResInRecycle    int32  `protobuf:"varint,32,opt,name=res_in_recycle,json=resInRecycle,proto3" form:"res_in_recycle" json:"res_in_recycle" query:"res_in_recycle"`
}

func (x *DashboardResBrief) Reset() {
	*x = DashboardResBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResBrief) ProtoMessage() {}

func (x *DashboardResBrief) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResBrief.ProtoReflect.Descriptor instead.
func (*DashboardResBrief) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{13}
}

func (x *DashboardResBrief) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DashboardResBrief) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DashboardResBrief) GetResTotal() int32 {
	if x != nil {
		return x.ResTotal
	}
	return 0
}

func (x *DashboardResBrief) GetResRunning() int32 {
	if x != nil {
		return x.ResRunning
	}
	return 0
}

func (x *DashboardResBrief) GetResAgentRunning() int32 {
	if x != nil {
		return x.ResAgentRunning
	}
	return 0
}

func (x *DashboardResBrief) GetRes_7DayCreated() int32 {
	if x != nil {
		return x.Res_7DayCreated
	}
	return 0
}

func (x *DashboardResBrief) GetRes_7DayDeleted() int32 {
	if x != nil {
		return x.Res_7DayDeleted
	}
	return 0
}

func (x *DashboardResBrief) GetRes_7DayExpire() int32 {
	if x != nil {
		return x.Res_7DayExpire
	}
	return 0
}

func (x *DashboardResBrief) GetResNeedCleanup() int32 {
	if x != nil {
		return x.ResNeedCleanup
	}
	return 0
}

func (x *DashboardResBrief) GetResInRecycle() int32 {
	if x != nil {
		return x.ResInRecycle
	}
	return 0
}

type DashboardHostDailyCountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayBefore int32  `protobuf:"varint,1,opt,name=day_before,json=dayBefore,proto3" form:"day_before" json:"day_before" query:"day_before"`
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionId  string `protobuf:"bytes,3,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *DashboardHostDailyCountReq) Reset() {
	*x = DashboardHostDailyCountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHostDailyCountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHostDailyCountReq) ProtoMessage() {}

func (x *DashboardHostDailyCountReq) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHostDailyCountReq.ProtoReflect.Descriptor instead.
func (*DashboardHostDailyCountReq) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{14}
}

func (x *DashboardHostDailyCountReq) GetDayBefore() int32 {
	if x != nil {
		return x.DayBefore
	}
	return 0
}

func (x *DashboardHostDailyCountReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DashboardHostDailyCountReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type DashboardHostDailyCountResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DashboardHostDailyCount `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DashboardHostDailyCountResp) Reset() {
	*x = DashboardHostDailyCountResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHostDailyCountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHostDailyCountResp) ProtoMessage() {}

func (x *DashboardHostDailyCountResp) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHostDailyCountResp.ProtoReflect.Descriptor instead.
func (*DashboardHostDailyCountResp) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{15}
}

func (x *DashboardHostDailyCountResp) GetList() []*DashboardHostDailyCount {
	if x != nil {
		return x.List
	}
	return nil
}

type DashboardHostDailyCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayName      string `protobuf:"bytes,1,opt,name=day_name,json=dayName,proto3" form:"day_name" json:"day_name" query:"day_name"`
	StateCount   int32  `protobuf:"varint,2,opt,name=state_count,json=stateCount,proto3" form:"state_count" json:"state_count" query:"state_count"`
	CreatedCount int32  `protobuf:"varint,3,opt,name=created_count,json=createdCount,proto3" form:"created_count" json:"created_count" query:"created_count"`
	DeletedCount int32  `protobuf:"varint,4,opt,name=deleted_count,json=deletedCount,proto3" form:"deleted_count" json:"deleted_count" query:"deleted_count"`
}

func (x *DashboardHostDailyCount) Reset() {
	*x = DashboardHostDailyCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHostDailyCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHostDailyCount) ProtoMessage() {}

func (x *DashboardHostDailyCount) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHostDailyCount.ProtoReflect.Descriptor instead.
func (*DashboardHostDailyCount) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{16}
}

func (x *DashboardHostDailyCount) GetDayName() string {
	if x != nil {
		return x.DayName
	}
	return ""
}

func (x *DashboardHostDailyCount) GetStateCount() int32 {
	if x != nil {
		return x.StateCount
	}
	return 0
}

func (x *DashboardHostDailyCount) GetCreatedCount() int32 {
	if x != nil {
		return x.CreatedCount
	}
	return 0
}

func (x *DashboardHostDailyCount) GetDeletedCount() int32 {
	if x != nil {
		return x.DeletedCount
	}
	return 0
}

type DashboardResDailyCountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionId  string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	ResType   string `protobuf:"bytes,3,opt,name=res_type,json=resType,proto3" form:"res_type" json:"res_type" query:"res_type"`
	Start     int32  `protobuf:"varint,11,opt,name=start,proto3" form:"start" json:"start" query:"start"`
	End       int32  `protobuf:"varint,12,opt,name=end,proto3" form:"end" json:"end" query:"end"`
	DayBefore int32  `protobuf:"varint,21,opt,name=day_before,json=dayBefore,proto3" form:"day_before" json:"day_before" query:"day_before"`
}

func (x *DashboardResDailyCountReq) Reset() {
	*x = DashboardResDailyCountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResDailyCountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResDailyCountReq) ProtoMessage() {}

func (x *DashboardResDailyCountReq) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResDailyCountReq.ProtoReflect.Descriptor instead.
func (*DashboardResDailyCountReq) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{17}
}

func (x *DashboardResDailyCountReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DashboardResDailyCountReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DashboardResDailyCountReq) GetResType() string {
	if x != nil {
		return x.ResType
	}
	return ""
}

func (x *DashboardResDailyCountReq) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *DashboardResDailyCountReq) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

func (x *DashboardResDailyCountReq) GetDayBefore() int32 {
	if x != nil {
		return x.DayBefore
	}
	return 0
}

type DashboardResDailyCountResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*DashboardResDailyCount `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DashboardResDailyCountResp) Reset() {
	*x = DashboardResDailyCountResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResDailyCountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResDailyCountResp) ProtoMessage() {}

func (x *DashboardResDailyCountResp) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResDailyCountResp.ProtoReflect.Descriptor instead.
func (*DashboardResDailyCountResp) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{18}
}

func (x *DashboardResDailyCountResp) GetList() []*DashboardResDailyCount {
	if x != nil {
		return x.List
	}
	return nil
}

type DashboardResDailyCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayName      string `protobuf:"bytes,1,opt,name=day_name,json=dayName,proto3" form:"day_name" json:"day_name" query:"day_name"`
	StateCount   int32  `protobuf:"varint,2,opt,name=state_count,json=stateCount,proto3" form:"state_count" json:"state_count" query:"state_count"`
	CreatedCount int32  `protobuf:"varint,3,opt,name=created_count,json=createdCount,proto3" form:"created_count" json:"created_count" query:"created_count"`
	DeletedCount int32  `protobuf:"varint,4,opt,name=deleted_count,json=deletedCount,proto3" form:"deleted_count" json:"deleted_count" query:"deleted_count"`
}

func (x *DashboardResDailyCount) Reset() {
	*x = DashboardResDailyCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResDailyCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResDailyCount) ProtoMessage() {}

func (x *DashboardResDailyCount) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResDailyCount.ProtoReflect.Descriptor instead.
func (*DashboardResDailyCount) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{19}
}

func (x *DashboardResDailyCount) GetDayName() string {
	if x != nil {
		return x.DayName
	}
	return ""
}

func (x *DashboardResDailyCount) GetStateCount() int32 {
	if x != nil {
		return x.StateCount
	}
	return 0
}

func (x *DashboardResDailyCount) GetCreatedCount() int32 {
	if x != nil {
		return x.CreatedCount
	}
	return 0
}

func (x *DashboardResDailyCount) GetDeletedCount() int32 {
	if x != nil {
		return x.DeletedCount
	}
	return 0
}

type AutoFillSnapshotReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayBefore int32  `protobuf:"varint,1,opt,name=day_before,json=dayBefore,proto3" form:"day_before" json:"day_before" query:"day_before"`
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
}

func (x *AutoFillSnapshotReq) Reset() {
	*x = AutoFillSnapshotReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoFillSnapshotReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoFillSnapshotReq) ProtoMessage() {}

func (x *AutoFillSnapshotReq) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoFillSnapshotReq.ProtoReflect.Descriptor instead.
func (*AutoFillSnapshotReq) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{20}
}

func (x *AutoFillSnapshotReq) GetDayBefore() int32 {
	if x != nil {
		return x.DayBefore
	}
	return 0
}

func (x *AutoFillSnapshotReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type Snapshot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayName    string `protobuf:"bytes,1,opt,name=day_name,json=dayName,proto3" form:"day_name" json:"day_name" query:"day_name"`
	AccountId  string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionId   string `protobuf:"bytes,3,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	ResType    string `protobuf:"bytes,4,opt,name=res_type,json=resType,proto3" form:"res_type" json:"res_type" query:"res_type"`
	TotalCount int32  `protobuf:"varint,5,opt,name=total_count,json=totalCount,proto3" form:"total_count" json:"total_count" query:"total_count"`
	Action     string `protobuf:"bytes,6,opt,name=action,proto3" form:"action" json:"action" query:"action"`
}

func (x *Snapshot) Reset() {
	*x = Snapshot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Snapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Snapshot) ProtoMessage() {}

func (x *Snapshot) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Snapshot.ProtoReflect.Descriptor instead.
func (*Snapshot) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{21}
}

func (x *Snapshot) GetDayName() string {
	if x != nil {
		return x.DayName
	}
	return ""
}

func (x *Snapshot) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Snapshot) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *Snapshot) GetResType() string {
	if x != nil {
		return x.ResType
	}
	return ""
}

func (x *Snapshot) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *Snapshot) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type FillSnapshotReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*Snapshot `protobuf:"bytes,1,rep,name=data,proto3" form:"data" json:"data" query:"data"`
}

func (x *FillSnapshotReq) Reset() {
	*x = FillSnapshotReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FillSnapshotReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FillSnapshotReq) ProtoMessage() {}

func (x *FillSnapshotReq) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FillSnapshotReq.ProtoReflect.Descriptor instead.
func (*FillSnapshotReq) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{22}
}

func (x *FillSnapshotReq) GetData() []*Snapshot {
	if x != nil {
		return x.Data
	}
	return nil
}

type FillSnapshotRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Failures []*Snapshot `protobuf:"bytes,1,rep,name=failures,proto3" form:"failures" json:"failures" query:"failures"`
}

func (x *FillSnapshotRes) Reset() {
	*x = FillSnapshotRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_dashBoard_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FillSnapshotRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FillSnapshotRes) ProtoMessage() {}

func (x *FillSnapshotRes) ProtoReflect() protoreflect.Message {
	mi := &file_dashBoard_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FillSnapshotRes.ProtoReflect.Descriptor instead.
func (*FillSnapshotRes) Descriptor() ([]byte, []int) {
	return file_dashBoard_proto_rawDescGZIP(), []int{23}
}

func (x *FillSnapshotRes) GetFailures() []*Snapshot {
	if x != nil {
		return x.Failures
	}
	return nil
}

var File_dashBoard_proto protoreflect.FileDescriptor

var file_dashBoard_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x22, 0x19, 0x0a, 0x17, 0x64,
	0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x50, 0x0a, 0x18, 0x64, 0x61, 0x73, 0x68, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb9, 0x01, 0x0a, 0x16, 0x64, 0x61, 0x73,
	0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x56, 0x0a, 0x16, 0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x1a, 0x0a, 0x18,
	0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2f, 0x0a, 0x19, 0x64, 0x61, 0x73, 0x68,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x22, 0x3a, 0x0a, 0x20, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x55, 0x73, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x69, 0x73, 0x55, 0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69,
	0x73, 0x55, 0x73, 0x65, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x1f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x75, 0x69,
	0x6c, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x44, 0x61, 0x74, 0x65, 0x22, 0x55, 0x0a,
	0x15, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x42, 0x72,
	0x69, 0x65, 0x66, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x22, 0x4a, 0x0a, 0x16, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x48, 0x6f, 0x73, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x65, 0x73, 0x70, 0x12, 0x30,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x48, 0x6f, 0x73, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x96, 0x03, 0x0a, 0x12, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f,
	0x73, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x52, 0x75,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x10, 0x68, 0x6f, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x37, 0x64, 0x61, 0x79,
	0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x68, 0x6f, 0x73, 0x74, 0x37, 0x64, 0x61, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x37, 0x64, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x68, 0x6f, 0x73, 0x74,
	0x37, 0x64, 0x61, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x68,
	0x6f, 0x73, 0x74, 0x5f, 0x37, 0x64, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x68, 0x6f, 0x73, 0x74, 0x37, 0x64, 0x61, 0x79, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x65,
	0x65, 0x64, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75,
	0x70, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x68, 0x6f, 0x73, 0x74,
	0x49, 0x6e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x22, 0x6f, 0x0a, 0x14, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x72, 0x65, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x48, 0x0a, 0x15, 0x64, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x22, 0x85, 0x03, 0x0a, 0x11, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x42, 0x72, 0x69, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x65, 0x73, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x52, 0x75, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x72, 0x65, 0x73, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x5f, 0x37, 0x64, 0x61, 0x79, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x37,
	0x64, 0x61, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65,
	0x73, 0x5f, 0x37, 0x64, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x37, 0x64, 0x61, 0x79, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x5f, 0x37, 0x64, 0x61, 0x79,
	0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72,
	0x65, 0x73, 0x37, 0x64, 0x61, 0x79, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x72, 0x65, 0x73, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x4e, 0x65, 0x65, 0x64, 0x43,
	0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x5f, 0x69, 0x6e,
	0x5f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x49, 0x6e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x22, 0x77, 0x0a, 0x1a,
	0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61,
	0x79, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x64, 0x61, 0x79, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x1b, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x35, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x9f, 0x01, 0x0a, 0x17,
	0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb9, 0x01,
	0x0a, 0x19, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61,
	0x79, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x64, 0x61, 0x79, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x22, 0x52, 0x0a, 0x1a, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x9e, 0x01,
	0x0a, 0x16, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x69, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x53,
	0x0a, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x46, 0x69, 0x6c, 0x6c, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x79, 0x5f, 0x62, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64, 0x61, 0x79, 0x42, 0x65,
	0x66, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0xb5, 0x01, 0x0a, 0x08, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x64, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x0f, 0x66,
	0x69, 0x6c, 0x6c, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x12, 0x26,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x41, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x6c, 0x53, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x08, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52,
	0x08, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61,
	0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dashBoard_proto_rawDescOnce sync.Once
	file_dashBoard_proto_rawDescData = file_dashBoard_proto_rawDesc
)

func file_dashBoard_proto_rawDescGZIP() []byte {
	file_dashBoard_proto_rawDescOnce.Do(func() {
		file_dashBoard_proto_rawDescData = protoimpl.X.CompressGZIP(file_dashBoard_proto_rawDescData)
	})
	return file_dashBoard_proto_rawDescData
}

var file_dashBoard_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_dashBoard_proto_goTypes = []interface{}{
	(*DashBoardAccountRequest)(nil),          // 0: cloudman.dashBoardAccountRequest
	(*DashBoardAccountResponse)(nil),         // 1: cloudman.dashBoardAccountResponse
	(*DashBoardAccountDetail)(nil),           // 2: cloudman.dashBoardAccountDetail
	(*DashBoardAccountRegion)(nil),           // 3: cloudman.dashBoardAccountRegion
	(*DashBoardUserAuthRequest)(nil),         // 4: cloudman.dashBoardUserAuthRequest
	(*DashBoardUserAuthResponse)(nil),        // 5: cloudman.dashBoardUserAuthResponse
	(*DashboardIfWorkOrderUsedResponse)(nil), // 6: cloudman.dashboardIfWorkOrderUsedResponse
	(*DashboardBackendVersionResponse)(nil),  // 7: cloudman.dashboardBackendVersionResponse
	(*DashboardHostBriefReq)(nil),            // 8: cloudman.dashboardHostBriefReq
	(*DashboardHostBriefResp)(nil),           // 9: cloudman.dashboardHostBriefResp
	(*DashboardHostBrief)(nil),               // 10: cloudman.dashboardHostBrief
	(*DashboardResBriefReq)(nil),             // 11: cloudman.dashboardResBriefReq
	(*DashboardResBriefResp)(nil),            // 12: cloudman.dashboardResBriefResp
	(*DashboardResBrief)(nil),                // 13: cloudman.dashboardResBrief
	(*DashboardHostDailyCountReq)(nil),       // 14: cloudman.dashboardHostDailyCountReq
	(*DashboardHostDailyCountResp)(nil),      // 15: cloudman.dashboardHostDailyCountResp
	(*DashboardHostDailyCount)(nil),          // 16: cloudman.dashboardHostDailyCount
	(*DashboardResDailyCountReq)(nil),        // 17: cloudman.dashboardResDailyCountReq
	(*DashboardResDailyCountResp)(nil),       // 18: cloudman.dashboardResDailyCountResp
	(*DashboardResDailyCount)(nil),           // 19: cloudman.dashboardResDailyCount
	(*AutoFillSnapshotReq)(nil),              // 20: cloudman.autoFillSnapshotReq
	(*Snapshot)(nil),                         // 21: cloudman.snapshot
	(*FillSnapshotReq)(nil),                  // 22: cloudman.fillSnapshotReq
	(*FillSnapshotRes)(nil),                  // 23: cloudman.fillSnapshotRes
}
var file_dashBoard_proto_depIdxs = []int32{
	2,  // 0: cloudman.dashBoardAccountResponse.list:type_name -> cloudman.dashBoardAccountDetail
	3,  // 1: cloudman.dashBoardAccountDetail.regions:type_name -> cloudman.dashBoardAccountRegion
	10, // 2: cloudman.dashboardHostBriefResp.list:type_name -> cloudman.dashboardHostBrief
	13, // 3: cloudman.dashboardResBriefResp.list:type_name -> cloudman.dashboardResBrief
	16, // 4: cloudman.dashboardHostDailyCountResp.list:type_name -> cloudman.dashboardHostDailyCount
	19, // 5: cloudman.dashboardResDailyCountResp.list:type_name -> cloudman.dashboardResDailyCount
	21, // 6: cloudman.fillSnapshotReq.data:type_name -> cloudman.snapshot
	21, // 7: cloudman.fillSnapshotRes.failures:type_name -> cloudman.snapshot
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_dashBoard_proto_init() }
func file_dashBoard_proto_init() {
	if File_dashBoard_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_dashBoard_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardAccountDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardAccountRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardUserAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashBoardUserAuthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardIfWorkOrderUsedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardBackendVersionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHostBriefReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHostBriefResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHostBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResBriefReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResBriefResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHostDailyCountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHostDailyCountResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHostDailyCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResDailyCountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResDailyCountResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResDailyCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoFillSnapshotReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Snapshot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FillSnapshotReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_dashBoard_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FillSnapshotRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dashBoard_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dashBoard_proto_goTypes,
		DependencyIndexes: file_dashBoard_proto_depIdxs,
		MessageInfos:      file_dashBoard_proto_msgTypes,
	}.Build()
	File_dashBoard_proto = out.File
	file_dashBoard_proto_rawDesc = nil
	file_dashBoard_proto_goTypes = nil
	file_dashBoard_proto_depIdxs = nil
}
