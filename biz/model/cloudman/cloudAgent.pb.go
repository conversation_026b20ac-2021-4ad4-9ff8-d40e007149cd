// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: cloudAgent.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CloudAgentQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size      uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering  []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords  string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Statue    string   `protobuf:"bytes,5,opt,name=statue,proto3" form:"statue" json:"statue" query:"statue"`
	AccountId string   `protobuf:"bytes,6,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	InnerIp   string   `protobuf:"bytes,7,opt,name=inner_ip,json=innerIp,proto3" form:"inner_ip" json:"inner_ip" query:"inner_ip"`
}

func (x *CloudAgentQueryReq) Reset() {
	*x = CloudAgentQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudAgentQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudAgentQueryReq) ProtoMessage() {}

func (x *CloudAgentQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudAgentQueryReq.ProtoReflect.Descriptor instead.
func (*CloudAgentQueryReq) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{0}
}

func (x *CloudAgentQueryReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *CloudAgentQueryReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *CloudAgentQueryReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *CloudAgentQueryReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *CloudAgentQueryReq) GetStatue() string {
	if x != nil {
		return x.Statue
	}
	return ""
}

func (x *CloudAgentQueryReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CloudAgentQueryReq) GetInnerIp() string {
	if x != nil {
		return x.InnerIp
	}
	return ""
}

type CloudAgentListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32              `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*CloudAgentDetail `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *CloudAgentListRes) Reset() {
	*x = CloudAgentListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudAgentListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudAgentListRes) ProtoMessage() {}

func (x *CloudAgentListRes) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudAgentListRes.ProtoReflect.Descriptor instead.
func (*CloudAgentListRes) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{1}
}

func (x *CloudAgentListRes) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *CloudAgentListRes) GetList() []*CloudAgentDetail {
	if x != nil {
		return x.List
	}
	return nil
}

type CloudAgentDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	AccountId   string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	InnerIp     string `protobuf:"bytes,3,opt,name=inner_ip,json=innerIp,proto3" form:"inner_ip" json:"inner_ip" query:"inner_ip"`
	Status      int32  `protobuf:"varint,4,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Disabled    bool   `protobuf:"varint,5,opt,name=disabled,proto3" form:"disabled" json:"disabled" query:"disabled"`
	CreatedTime uint64 `protobuf:"varint,6,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	UpdatedTime uint64 `protobuf:"varint,7,opt,name=updated_time,json=updatedTime,proto3" form:"updated_time" json:"updated_time" query:"updated_time"`
	CreateUser  string `protobuf:"bytes,8,opt,name=create_user,json=createUser,proto3" form:"create_user" json:"create_user" query:"create_user"`
	UpdateUser  string `protobuf:"bytes,9,opt,name=update_user,json=updateUser,proto3" form:"update_user" json:"update_user" query:"update_user"`
	AgentId     string `protobuf:"bytes,10,opt,name=agent_id,json=agentId,proto3" form:"agent_id" json:"agent_id" query:"agent_id"`
}

func (x *CloudAgentDetail) Reset() {
	*x = CloudAgentDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudAgentDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudAgentDetail) ProtoMessage() {}

func (x *CloudAgentDetail) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudAgentDetail.ProtoReflect.Descriptor instead.
func (*CloudAgentDetail) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{2}
}

func (x *CloudAgentDetail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CloudAgentDetail) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CloudAgentDetail) GetInnerIp() string {
	if x != nil {
		return x.InnerIp
	}
	return ""
}

func (x *CloudAgentDetail) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CloudAgentDetail) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *CloudAgentDetail) GetCreatedTime() uint64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *CloudAgentDetail) GetUpdatedTime() uint64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *CloudAgentDetail) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *CloudAgentDetail) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

func (x *CloudAgentDetail) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

type CreateCloudAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	InnerIp   string `protobuf:"bytes,2,opt,name=inner_ip,json=innerIp,proto3" form:"inner_ip" json:"inner_ip" query:"inner_ip"`
}

func (x *CreateCloudAgentReq) Reset() {
	*x = CreateCloudAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCloudAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCloudAgentReq) ProtoMessage() {}

func (x *CreateCloudAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCloudAgentReq.ProtoReflect.Descriptor instead.
func (*CreateCloudAgentReq) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{3}
}

func (x *CreateCloudAgentReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CreateCloudAgentReq) GetInnerIp() string {
	if x != nil {
		return x.InnerIp
	}
	return ""
}

type UpdateCloudAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Disabled bool   `protobuf:"varint,2,opt,name=disabled,proto3" form:"disabled" json:"disabled" query:"disabled"`
}

func (x *UpdateCloudAgentReq) Reset() {
	*x = UpdateCloudAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCloudAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCloudAgentReq) ProtoMessage() {}

func (x *UpdateCloudAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCloudAgentReq.ProtoReflect.Descriptor instead.
func (*UpdateCloudAgentReq) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateCloudAgentReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateCloudAgentReq) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

type GetInstallScriptResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Script string `protobuf:"bytes,1,opt,name=script,proto3" form:"script" json:"script" query:"script"`
}

func (x *GetInstallScriptResp) Reset() {
	*x = GetInstallScriptResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInstallScriptResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInstallScriptResp) ProtoMessage() {}

func (x *GetInstallScriptResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInstallScriptResp.ProtoReflect.Descriptor instead.
func (*GetInstallScriptResp) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{5}
}

func (x *GetInstallScriptResp) GetScript() string {
	if x != nil {
		return x.Script
	}
	return ""
}

type IDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id" query:"id"`
}

func (x *IDReq) Reset() {
	*x = IDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IDReq) ProtoMessage() {}

func (x *IDReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IDReq.ProtoReflect.Descriptor instead.
func (*IDReq) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{6}
}

func (x *IDReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CloudAgentTaskListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page         uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size         uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering     []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	AccountId    string   `protobuf:"bytes,11,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	Risky        string   `protobuf:"bytes,12,opt,name=risky,proto3" form:"risky" json:"risky" query:"risky"`
	Result       string   `protobuf:"bytes,13,opt,name=result,proto3" form:"result" json:"result" query:"result"`
	ResourceType string   `protobuf:"bytes,14,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	SearchKey    string   `protobuf:"bytes,21,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue  string   `protobuf:"bytes,22,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
	Start        int32    `protobuf:"varint,31,opt,name=start,proto3" form:"start" json:"start" query:"start"`
	End          int32    `protobuf:"varint,32,opt,name=end,proto3" form:"end" json:"end" query:"end"`
}

func (x *CloudAgentTaskListReq) Reset() {
	*x = CloudAgentTaskListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudAgentTaskListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudAgentTaskListReq) ProtoMessage() {}

func (x *CloudAgentTaskListReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudAgentTaskListReq.ProtoReflect.Descriptor instead.
func (*CloudAgentTaskListReq) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{7}
}

func (x *CloudAgentTaskListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *CloudAgentTaskListReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *CloudAgentTaskListReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *CloudAgentTaskListReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CloudAgentTaskListReq) GetRisky() string {
	if x != nil {
		return x.Risky
	}
	return ""
}

func (x *CloudAgentTaskListReq) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *CloudAgentTaskListReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CloudAgentTaskListReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *CloudAgentTaskListReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

func (x *CloudAgentTaskListReq) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *CloudAgentTaskListReq) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

type CloudAgentTaskListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*CloudAgentTaskBrief `protobuf:"bytes,1,rep,name=data,proto3" form:"data" json:"data" query:"data"`
	Count int32                  `protobuf:"varint,2,opt,name=count,proto3" form:"count" json:"count" query:"count"`
}

func (x *CloudAgentTaskListResp) Reset() {
	*x = CloudAgentTaskListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudAgentTaskListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudAgentTaskListResp) ProtoMessage() {}

func (x *CloudAgentTaskListResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudAgentTaskListResp.ProtoReflect.Descriptor instead.
func (*CloudAgentTaskListResp) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{8}
}

func (x *CloudAgentTaskListResp) GetData() []*CloudAgentTaskBrief {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *CloudAgentTaskListResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type CloudAgentTaskBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	AccountId      string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionName     string `protobuf:"bytes,3,opt,name=region_name,json=regionName,proto3" form:"region_name" json:"region_name" query:"region_name"`
	ResourceType   string `protobuf:"bytes,11,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	MethodName     string `protobuf:"bytes,12,opt,name=method_name,json=methodName,proto3" form:"method_name" json:"method_name" query:"method_name"`
	RunType        int32  `protobuf:"varint,13,opt,name=run_type,json=runType,proto3" form:"run_type" json:"run_type" query:"run_type"`
	Risky          int32  `protobuf:"varint,14,opt,name=risky,proto3" form:"risky" json:"risky" query:"risky"`
	Operator       string `protobuf:"bytes,15,opt,name=operator,proto3" form:"operator" json:"operator" query:"operator"`
	SubmitTime     int64  `protobuf:"varint,21,opt,name=submit_time,json=submitTime,proto3" form:"submit_time" json:"submit_time" query:"submit_time"`
	Result         int32  `protobuf:"varint,25,opt,name=result,proto3" form:"result" json:"result" query:"result"`
	BusinessResult bool   `protobuf:"varint,28,opt,name=business_result,json=businessResult,proto3" form:"business_result" json:"business_result" query:"business_result"`
	AgentIp        string `protobuf:"bytes,35,opt,name=agent_ip,json=agentIp,proto3" form:"agent_ip" json:"agent_ip" query:"agent_ip"`
}

func (x *CloudAgentTaskBrief) Reset() {
	*x = CloudAgentTaskBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudAgentTaskBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudAgentTaskBrief) ProtoMessage() {}

func (x *CloudAgentTaskBrief) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudAgentTaskBrief.ProtoReflect.Descriptor instead.
func (*CloudAgentTaskBrief) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{9}
}

func (x *CloudAgentTaskBrief) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CloudAgentTaskBrief) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CloudAgentTaskBrief) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *CloudAgentTaskBrief) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CloudAgentTaskBrief) GetMethodName() string {
	if x != nil {
		return x.MethodName
	}
	return ""
}

func (x *CloudAgentTaskBrief) GetRunType() int32 {
	if x != nil {
		return x.RunType
	}
	return 0
}

func (x *CloudAgentTaskBrief) GetRisky() int32 {
	if x != nil {
		return x.Risky
	}
	return 0
}

func (x *CloudAgentTaskBrief) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *CloudAgentTaskBrief) GetSubmitTime() int64 {
	if x != nil {
		return x.SubmitTime
	}
	return 0
}

func (x *CloudAgentTaskBrief) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *CloudAgentTaskBrief) GetBusinessResult() bool {
	if x != nil {
		return x.BusinessResult
	}
	return false
}

func (x *CloudAgentTaskBrief) GetAgentIp() string {
	if x != nil {
		return x.AgentIp
	}
	return ""
}

type CloudAgentTaskDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	AccountId        string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	RegionName       string `protobuf:"bytes,3,opt,name=region_name,json=regionName,proto3" form:"region_name" json:"region_name" query:"region_name"`
	ResourceType     string `protobuf:"bytes,11,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	MethodName       string `protobuf:"bytes,12,opt,name=method_name,json=methodName,proto3" form:"method_name" json:"method_name" query:"method_name"`
	RunType          int32  `protobuf:"varint,13,opt,name=run_type,json=runType,proto3" form:"run_type" json:"run_type" query:"run_type"`
	Risky            int32  `protobuf:"varint,14,opt,name=risky,proto3" form:"risky" json:"risky" query:"risky"`
	Operator         string `protobuf:"bytes,15,opt,name=operator,proto3" form:"operator" json:"operator" query:"operator"`
	SubmitTime       int64  `protobuf:"varint,21,opt,name=submit_time,json=submitTime,proto3" form:"submit_time" json:"submit_time" query:"submit_time"`
	StartTime        int64  `protobuf:"varint,22,opt,name=start_time,json=startTime,proto3" form:"start_time" json:"start_time" query:"start_time"`
	EndTime          int64  `protobuf:"varint,23,opt,name=end_time,json=endTime,proto3" form:"end_time" json:"end_time" query:"end_time"`
	CallbackTime     int64  `protobuf:"varint,24,opt,name=callback_time,json=callbackTime,proto3" form:"callback_time" json:"callback_time" query:"callback_time"`
	Result           int32  `protobuf:"varint,25,opt,name=result,proto3" form:"result" json:"result" query:"result"`
	ErrorMsg         string `protobuf:"bytes,26,opt,name=error_msg,json=errorMsg,proto3" form:"error_msg" json:"error_msg" query:"error_msg"`
	BusinessErrorMsg string `protobuf:"bytes,27,opt,name=business_error_msg,json=businessErrorMsg,proto3" form:"business_error_msg" json:"business_error_msg" query:"business_error_msg"`
	BusinessResult   bool   `protobuf:"varint,28,opt,name=business_result,json=businessResult,proto3" form:"business_result" json:"business_result" query:"business_result"`
	RequestDump      string `protobuf:"bytes,31,opt,name=request_dump,json=requestDump,proto3" form:"request_dump" json:"request_dump" query:"request_dump"`
	ResponseDump     string `protobuf:"bytes,32,opt,name=response_dump,json=responseDump,proto3" form:"response_dump" json:"response_dump" query:"response_dump"`
	RequestSchema    string `protobuf:"bytes,33,opt,name=request_schema,json=requestSchema,proto3" form:"request_schema" json:"request_schema" query:"request_schema"`
	ResponseSchema   string `protobuf:"bytes,34,opt,name=response_schema,json=responseSchema,proto3" form:"response_schema" json:"response_schema" query:"response_schema"`
	AgentIp          string `protobuf:"bytes,35,opt,name=agent_ip,json=agentIp,proto3" form:"agent_ip" json:"agent_ip" query:"agent_ip"`
	AgentId          string `protobuf:"bytes,36,opt,name=agent_id,json=agentId,proto3" form:"agent_id" json:"agent_id" query:"agent_id"`
	AccessKey        string `protobuf:"bytes,41,opt,name=access_key,json=accessKey,proto3" form:"access_key" json:"access_key" query:"access_key"`
	AccessSecret     string `protobuf:"bytes,42,opt,name=access_secret,json=accessSecret,proto3" form:"access_secret" json:"access_secret" query:"access_secret"`
	AccessToken      string `protobuf:"bytes,43,opt,name=access_token,json=accessToken,proto3" form:"access_token" json:"access_token" query:"access_token"`
	KeyExpireTime    int64  `protobuf:"varint,44,opt,name=key_expire_time,json=keyExpireTime,proto3" form:"key_expire_time" json:"key_expire_time" query:"key_expire_time"`
	KeyPolicy        string `protobuf:"bytes,45,opt,name=key_policy,json=keyPolicy,proto3" form:"key_policy" json:"key_policy" query:"key_policy"`
}

func (x *CloudAgentTaskDetailResp) Reset() {
	*x = CloudAgentTaskDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloudAgentTaskDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudAgentTaskDetailResp) ProtoMessage() {}

func (x *CloudAgentTaskDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudAgentTaskDetailResp.ProtoReflect.Descriptor instead.
func (*CloudAgentTaskDetailResp) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{10}
}

func (x *CloudAgentTaskDetailResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetMethodName() string {
	if x != nil {
		return x.MethodName
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetRunType() int32 {
	if x != nil {
		return x.RunType
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetRisky() int32 {
	if x != nil {
		return x.Risky
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetSubmitTime() int64 {
	if x != nil {
		return x.SubmitTime
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetCallbackTime() int64 {
	if x != nil {
		return x.CallbackTime
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetBusinessErrorMsg() string {
	if x != nil {
		return x.BusinessErrorMsg
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetBusinessResult() bool {
	if x != nil {
		return x.BusinessResult
	}
	return false
}

func (x *CloudAgentTaskDetailResp) GetRequestDump() string {
	if x != nil {
		return x.RequestDump
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetResponseDump() string {
	if x != nil {
		return x.ResponseDump
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetRequestSchema() string {
	if x != nil {
		return x.RequestSchema
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetResponseSchema() string {
	if x != nil {
		return x.ResponseSchema
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetAgentIp() string {
	if x != nil {
		return x.AgentIp
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetAccessSecret() string {
	if x != nil {
		return x.AccessSecret
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *CloudAgentTaskDetailResp) GetKeyExpireTime() int64 {
	if x != nil {
		return x.KeyExpireTime
	}
	return 0
}

func (x *CloudAgentTaskDetailResp) GetKeyPolicy() string {
	if x != nil {
		return x.KeyPolicy
	}
	return ""
}

type ResChangelistReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page         uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size         uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering     []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	AccountId    string   `protobuf:"bytes,11,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	ResourceType string   `protobuf:"bytes,12,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	InstanceId   string   `protobuf:"bytes,13,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	InstanceName string   `protobuf:"bytes,14,opt,name=instance_name,json=instanceName,proto3" form:"instance_name" json:"instance_name" query:"instance_name"`
	Field        string   `protobuf:"bytes,15,opt,name=field,proto3" form:"field" json:"field" query:"field"`
	Start        int32    `protobuf:"varint,21,opt,name=start,proto3" form:"start" json:"start" query:"start"`
	End          int32    `protobuf:"varint,22,opt,name=end,proto3" form:"end" json:"end" query:"end"`
}

func (x *ResChangelistReq) Reset() {
	*x = ResChangelistReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResChangelistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResChangelistReq) ProtoMessage() {}

func (x *ResChangelistReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResChangelistReq.ProtoReflect.Descriptor instead.
func (*ResChangelistReq) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{11}
}

func (x *ResChangelistReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ResChangelistReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ResChangelistReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *ResChangelistReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ResChangelistReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ResChangelistReq) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *ResChangelistReq) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

func (x *ResChangelistReq) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ResChangelistReq) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *ResChangelistReq) GetEnd() int32 {
	if x != nil {
		return x.End
	}
	return 0
}

type ResChangelistResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data  []*ResChangelist `protobuf:"bytes,1,rep,name=data,proto3" form:"data" json:"data" query:"data"`
	Count int32            `protobuf:"varint,2,opt,name=count,proto3" form:"count" json:"count" query:"count"`
}

func (x *ResChangelistResp) Reset() {
	*x = ResChangelistResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResChangelistResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResChangelistResp) ProtoMessage() {}

func (x *ResChangelistResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResChangelistResp.ProtoReflect.Descriptor instead.
func (*ResChangelistResp) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{12}
}

func (x *ResChangelistResp) GetData() []*ResChangelist {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ResChangelistResp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ResChangelist struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	CreatedTime  int32  `protobuf:"varint,2,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	AccountId    string `protobuf:"bytes,11,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	ResourceType string `protobuf:"bytes,12,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	InstanceId   string `protobuf:"bytes,13,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	InstanceName string `protobuf:"bytes,14,opt,name=instance_name,json=instanceName,proto3" form:"instance_name" json:"instance_name" query:"instance_name"`
	Field        string `protobuf:"bytes,15,opt,name=field,proto3" form:"field" json:"field" query:"field"`
	Before       string `protobuf:"bytes,21,opt,name=before,proto3" form:"before" json:"before" query:"before"`
	After        string `protobuf:"bytes,22,opt,name=after,proto3" form:"after" json:"after" query:"after"`
}

func (x *ResChangelist) Reset() {
	*x = ResChangelist{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudAgent_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResChangelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResChangelist) ProtoMessage() {}

func (x *ResChangelist) ProtoReflect() protoreflect.Message {
	mi := &file_cloudAgent_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResChangelist.ProtoReflect.Descriptor instead.
func (*ResChangelist) Descriptor() ([]byte, []int) {
	return file_cloudAgent_proto_rawDescGZIP(), []int{13}
}

func (x *ResChangelist) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResChangelist) GetCreatedTime() int32 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *ResChangelist) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ResChangelist) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ResChangelist) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *ResChangelist) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

func (x *ResChangelist) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ResChangelist) GetBefore() string {
	if x != nil {
		return x.Before
	}
	return ""
}

func (x *ResChangelist) GetAfter() string {
	if x != nil {
		return x.After
	}
	return ""
}

var File_cloudAgent_proto protoreflect.FileDescriptor

var file_cloudAgent_proto_rawDesc = []byte{
	0x0a, 0x10, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x09, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc6, 0x01, 0x0a, 0x12, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x70,
	0x22, 0x59, 0x0a, 0x11, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb3, 0x02, 0x0a, 0x10,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x4f, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x49, 0x70, 0x22, 0x41, 0x0a, 0x13, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x2e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x22, 0x25, 0x0a, 0x05, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x1c,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xb2, 0xbb, 0x18, 0x02,
	0x69, 0x64, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0xb7, 0x02, 0x0a,
	0x15, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x69, 0x73,
	0x6b, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x61, 0x0a, 0x16, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xf5, 0x02, 0x0a, 0x13, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x72, 0x69, 0x65,
	0x66, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x75, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x70, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x70, 0x22, 0x85, 0x07, 0x0a, 0x18, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x75, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x72, 0x69, 0x73, 0x6b, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x73, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x75, 0x6d, 0x70, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x75, 0x6d, 0x70,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64, 0x75, 0x6d,
	0x70, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x44, 0x75, 0x6d, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x70, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x70,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6b, 0x65, 0x79, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6b, 0x65, 0x79,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6b, 0x65,
	0x79, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6b, 0x65, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x9e, 0x02, 0x0a, 0x10, 0x72, 0x65,
	0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x56, 0x0a, 0x11, 0x72, 0x65,
	0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x2b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x90, 0x02, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x66, 0x74, 0x65, 0x72, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74,
	0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d,
	0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f,
	0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cloudAgent_proto_rawDescOnce sync.Once
	file_cloudAgent_proto_rawDescData = file_cloudAgent_proto_rawDesc
)

func file_cloudAgent_proto_rawDescGZIP() []byte {
	file_cloudAgent_proto_rawDescOnce.Do(func() {
		file_cloudAgent_proto_rawDescData = protoimpl.X.CompressGZIP(file_cloudAgent_proto_rawDescData)
	})
	return file_cloudAgent_proto_rawDescData
}

var file_cloudAgent_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_cloudAgent_proto_goTypes = []interface{}{
	(*CloudAgentQueryReq)(nil),       // 0: cloudman.cloudAgentQueryReq
	(*CloudAgentListRes)(nil),        // 1: cloudman.cloudAgentListRes
	(*CloudAgentDetail)(nil),         // 2: cloudman.cloudAgentDetail
	(*CreateCloudAgentReq)(nil),      // 3: cloudman.createCloudAgentReq
	(*UpdateCloudAgentReq)(nil),      // 4: cloudman.updateCloudAgentReq
	(*GetInstallScriptResp)(nil),     // 5: cloudman.GetInstallScriptResp
	(*IDReq)(nil),                    // 6: cloudman.IDReq
	(*CloudAgentTaskListReq)(nil),    // 7: cloudman.cloudAgentTaskListReq
	(*CloudAgentTaskListResp)(nil),   // 8: cloudman.cloudAgentTaskListResp
	(*CloudAgentTaskBrief)(nil),      // 9: cloudman.cloudAgentTaskBrief
	(*CloudAgentTaskDetailResp)(nil), // 10: cloudman.cloudAgentTaskDetailResp
	(*ResChangelistReq)(nil),         // 11: cloudman.resChangelistReq
	(*ResChangelistResp)(nil),        // 12: cloudman.resChangelistResp
	(*ResChangelist)(nil),            // 13: cloudman.resChangelist
}
var file_cloudAgent_proto_depIdxs = []int32{
	2,  // 0: cloudman.cloudAgentListRes.list:type_name -> cloudman.cloudAgentDetail
	9,  // 1: cloudman.cloudAgentTaskListResp.data:type_name -> cloudman.cloudAgentTaskBrief
	13, // 2: cloudman.resChangelistResp.data:type_name -> cloudman.resChangelist
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_cloudAgent_proto_init() }
func file_cloudAgent_proto_init() {
	if File_cloudAgent_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cloudAgent_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudAgentQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudAgentListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudAgentDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCloudAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCloudAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInstallScriptResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudAgentTaskListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudAgentTaskListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudAgentTaskBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloudAgentTaskDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResChangelistReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResChangelistResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudAgent_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResChangelist); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cloudAgent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cloudAgent_proto_goTypes,
		DependencyIndexes: file_cloudAgent_proto_depIdxs,
		MessageInfos:      file_cloudAgent_proto_msgTypes,
	}.Build()
	File_cloudAgent_proto = out.File
	file_cloudAgent_proto_rawDesc = nil
	file_cloudAgent_proto_goTypes = nil
	file_cloudAgent_proto_depIdxs = nil
}
