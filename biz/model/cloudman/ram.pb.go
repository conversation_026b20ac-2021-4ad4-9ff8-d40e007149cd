// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: ram.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DescribeRAMPolicyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp         string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId    string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	Name        string `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Desc        string `protobuf:"bytes,6,opt,name=desc,proto3" form:"desc" json:"desc" query:"desc"`
	NeedCleanup bool   `protobuf:"varint,7,opt,name=need_cleanup,json=needCleanup,proto3" form:"need_cleanup" json:"need_cleanup" query:"need_cleanup"`
}

func (x *DescribeRAMPolicyReq) Reset() {
	*x = DescribeRAMPolicyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRAMPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRAMPolicyReq) ProtoMessage() {}

func (x *DescribeRAMPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRAMPolicyReq.ProtoReflect.Descriptor instead.
func (*DescribeRAMPolicyReq) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{0}
}

func (x *DescribeRAMPolicyReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeRAMPolicyReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeRAMPolicyReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeRAMPolicyReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeRAMPolicyReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribeRAMPolicyReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DescribeRAMPolicyReq) GetNeedCleanup() bool {
	if x != nil {
		return x.NeedCleanup
	}
	return false
}

type DescribeRAMPolicyResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32        `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*RAMPolicy `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeRAMPolicyResp) Reset() {
	*x = DescribeRAMPolicyResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRAMPolicyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRAMPolicyResp) ProtoMessage() {}

func (x *DescribeRAMPolicyResp) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRAMPolicyResp.ProtoReflect.Descriptor instead.
func (*DescribeRAMPolicyResp) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeRAMPolicyResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeRAMPolicyResp) GetList() []*RAMPolicy {
	if x != nil {
		return x.List
	}
	return nil
}

type RAMPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string        `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name               string        `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Description        string        `protobuf:"bytes,3,opt,name=description,proto3" form:"description" json:"description" query:"description"`
	AttachUsers        []*AttachUser `protobuf:"bytes,4,rep,name=attach_users,json=attachUsers,proto3" form:"attach_users" json:"attach_users" query:"attach_users"`
	PolicyDocument     string        `protobuf:"bytes,5,opt,name=policy_document,json=policyDocument,proto3" form:"policy_document" json:"policy_document" query:"policy_document"`
	CreateTime         string        `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" form:"create_time" json:"create_time" query:"create_time"`
	UpdateTime         string        `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" form:"update_time" json:"update_time" query:"update_time"`
	RelatedIpGroups    []string      `protobuf:"bytes,8,rep,name=related_ip_groups,json=relatedIpGroups,proto3" form:"related_ip_groups" json:"related_ip_groups" query:"related_ip_groups"`
	RelatedIpGroupsIds []string      `protobuf:"bytes,9,rep,name=related_ip_groups_ids,json=relatedIpGroupsIds,proto3" form:"related_ip_groups_ids" json:"related_ip_groups_ids" query:"related_ip_groups_ids"`
	RegionId           string        `protobuf:"bytes,10,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IspId              string        `protobuf:"bytes,11,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType            string        `protobuf:"bytes,12,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName            string        `protobuf:"bytes,13,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
}

func (x *RAMPolicy) Reset() {
	*x = RAMPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RAMPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RAMPolicy) ProtoMessage() {}

func (x *RAMPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RAMPolicy.ProtoReflect.Descriptor instead.
func (*RAMPolicy) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{2}
}

func (x *RAMPolicy) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RAMPolicy) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RAMPolicy) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RAMPolicy) GetAttachUsers() []*AttachUser {
	if x != nil {
		return x.AttachUsers
	}
	return nil
}

func (x *RAMPolicy) GetPolicyDocument() string {
	if x != nil {
		return x.PolicyDocument
	}
	return ""
}

func (x *RAMPolicy) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *RAMPolicy) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *RAMPolicy) GetRelatedIpGroups() []string {
	if x != nil {
		return x.RelatedIpGroups
	}
	return nil
}

func (x *RAMPolicy) GetRelatedIpGroupsIds() []string {
	if x != nil {
		return x.RelatedIpGroupsIds
	}
	return nil
}

func (x *RAMPolicy) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *RAMPolicy) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *RAMPolicy) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *RAMPolicy) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

type AttachUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" form:"display_name" json:"display_name" query:"display_name"`
	UserId      string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" form:"user_id" json:"user_id" query:"user_id"`
	UserName    string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" form:"user_name" json:"user_name" query:"user_name"`
	AttachDate  string `protobuf:"bytes,4,opt,name=attach_date,json=attachDate,proto3" form:"attach_date" json:"attach_date" query:"attach_date"`
}

func (x *AttachUser) Reset() {
	*x = AttachUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttachUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttachUser) ProtoMessage() {}

func (x *AttachUser) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttachUser.ProtoReflect.Descriptor instead.
func (*AttachUser) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{3}
}

func (x *AttachUser) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *AttachUser) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AttachUser) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *AttachUser) GetAttachDate() string {
	if x != nil {
		return x.AttachDate
	}
	return ""
}

type ModifyRAMPolicySourceIPGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
	IpGroupIds []string `protobuf:"bytes,2,rep,name=ip_group_ids,json=ipGroupIds,proto3" form:"ip_group_ids" json:"ip_group_ids" query:"ip_group_ids"`
}

func (x *ModifyRAMPolicySourceIPGroupReq) Reset() {
	*x = ModifyRAMPolicySourceIPGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyRAMPolicySourceIPGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyRAMPolicySourceIPGroupReq) ProtoMessage() {}

func (x *ModifyRAMPolicySourceIPGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyRAMPolicySourceIPGroupReq.ProtoReflect.Descriptor instead.
func (*ModifyRAMPolicySourceIPGroupReq) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{4}
}

func (x *ModifyRAMPolicySourceIPGroupReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModifyRAMPolicySourceIPGroupReq) GetIpGroupIds() []string {
	if x != nil {
		return x.IpGroupIds
	}
	return nil
}

type PreviewRAMPolicyDeployReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids   []string `protobuf:"bytes,1,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
	IsAll bool     `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" form:"is_all" json:"is_all" query:"is_all"`
}

func (x *PreviewRAMPolicyDeployReq) Reset() {
	*x = PreviewRAMPolicyDeployReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRAMPolicyDeployReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRAMPolicyDeployReq) ProtoMessage() {}

func (x *PreviewRAMPolicyDeployReq) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRAMPolicyDeployReq.ProtoReflect.Descriptor instead.
func (*PreviewRAMPolicyDeployReq) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{5}
}

func (x *PreviewRAMPolicyDeployReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PreviewRAMPolicyDeployReq) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

type PreviewRAMPolicyDeployResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*PreviewRamPolicyDeployRes `protobuf:"bytes,1,rep,name=results,proto3" form:"results" json:"results" query:"results"`
}

func (x *PreviewRAMPolicyDeployResp) Reset() {
	*x = PreviewRAMPolicyDeployResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRAMPolicyDeployResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRAMPolicyDeployResp) ProtoMessage() {}

func (x *PreviewRAMPolicyDeployResp) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRAMPolicyDeployResp.ProtoReflect.Descriptor instead.
func (*PreviewRAMPolicyDeployResp) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{6}
}

func (x *PreviewRAMPolicyDeployResp) GetResults() []*PreviewRamPolicyDeployRes {
	if x != nil {
		return x.Results
	}
	return nil
}

type PreviewRamPolicyDeployRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string        `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name              string        `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Description       string        `protobuf:"bytes,3,opt,name=description,proto3" form:"description" json:"description" query:"description"`
	AttachUsers       []*AttachUser `protobuf:"bytes,4,rep,name=attach_users,json=attachUsers,proto3" form:"attach_users" json:"attach_users" query:"attach_users"`
	AddIps            []string      `protobuf:"bytes,5,rep,name=add_ips,json=addIps,proto3" form:"add_ips" json:"add_ips" query:"add_ips"`
	RemoveIps         []string      `protobuf:"bytes,6,rep,name=remove_ips,json=removeIps,proto3" form:"remove_ips" json:"remove_ips" query:"remove_ips"`
	OldPolicyDocument string        `protobuf:"bytes,7,opt,name=oldPolicyDocument,proto3" form:"oldPolicyDocument" json:"oldPolicyDocument" query:"oldPolicyDocument"`
	NewPolicyDocument string        `protobuf:"bytes,8,opt,name=newPolicyDocument,proto3" form:"newPolicyDocument" json:"newPolicyDocument" query:"newPolicyDocument"`
}

func (x *PreviewRamPolicyDeployRes) Reset() {
	*x = PreviewRamPolicyDeployRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewRamPolicyDeployRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewRamPolicyDeployRes) ProtoMessage() {}

func (x *PreviewRamPolicyDeployRes) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewRamPolicyDeployRes.ProtoReflect.Descriptor instead.
func (*PreviewRamPolicyDeployRes) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{7}
}

func (x *PreviewRamPolicyDeployRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PreviewRamPolicyDeployRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PreviewRamPolicyDeployRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PreviewRamPolicyDeployRes) GetAttachUsers() []*AttachUser {
	if x != nil {
		return x.AttachUsers
	}
	return nil
}

func (x *PreviewRamPolicyDeployRes) GetAddIps() []string {
	if x != nil {
		return x.AddIps
	}
	return nil
}

func (x *PreviewRamPolicyDeployRes) GetRemoveIps() []string {
	if x != nil {
		return x.RemoveIps
	}
	return nil
}

func (x *PreviewRamPolicyDeployRes) GetOldPolicyDocument() string {
	if x != nil {
		return x.OldPolicyDocument
	}
	return ""
}

func (x *PreviewRamPolicyDeployRes) GetNewPolicyDocument() string {
	if x != nil {
		return x.NewPolicyDocument
	}
	return ""
}

type RAMPolicyDeployReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids   []string `protobuf:"bytes,1,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
	IsAll bool     `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" form:"is_all" json:"is_all" query:"is_all"`
}

func (x *RAMPolicyDeployReq) Reset() {
	*x = RAMPolicyDeployReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RAMPolicyDeployReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RAMPolicyDeployReq) ProtoMessage() {}

func (x *RAMPolicyDeployReq) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RAMPolicyDeployReq.ProtoReflect.Descriptor instead.
func (*RAMPolicyDeployReq) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{8}
}

func (x *RAMPolicyDeployReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *RAMPolicyDeployReq) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

type SyncRamPolicyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId string `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IspId    string `protobuf:"bytes,2,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
}

func (x *SyncRamPolicyReq) Reset() {
	*x = SyncRamPolicyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncRamPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRamPolicyReq) ProtoMessage() {}

func (x *SyncRamPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRamPolicyReq.ProtoReflect.Descriptor instead.
func (*SyncRamPolicyReq) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{9}
}

func (x *SyncRamPolicyReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *SyncRamPolicyReq) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

type SyncNewRAMPolicyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Names    []string `protobuf:"bytes,1,rep,name=names,proto3" form:"names" json:"names" query:"names"`
	RegionId string   `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IspId    string   `protobuf:"bytes,3,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
}

func (x *SyncNewRAMPolicyReq) Reset() {
	*x = SyncNewRAMPolicyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncNewRAMPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncNewRAMPolicyReq) ProtoMessage() {}

func (x *SyncNewRAMPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncNewRAMPolicyReq.ProtoReflect.Descriptor instead.
func (*SyncNewRAMPolicyReq) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{10}
}

func (x *SyncNewRAMPolicyReq) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *SyncNewRAMPolicyReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *SyncNewRAMPolicyReq) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

type GetCloudRamPolicyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId string `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IspId    string `protobuf:"bytes,2,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
}

func (x *GetCloudRamPolicyReq) Reset() {
	*x = GetCloudRamPolicyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCloudRamPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCloudRamPolicyReq) ProtoMessage() {}

func (x *GetCloudRamPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCloudRamPolicyReq.ProtoReflect.Descriptor instead.
func (*GetCloudRamPolicyReq) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{11}
}

func (x *GetCloudRamPolicyReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *GetCloudRamPolicyReq) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

type GetCloudRamPolicyResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetCloudRamPolicyRes `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *GetCloudRamPolicyResp) Reset() {
	*x = GetCloudRamPolicyResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCloudRamPolicyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCloudRamPolicyResp) ProtoMessage() {}

func (x *GetCloudRamPolicyResp) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCloudRamPolicyResp.ProtoReflect.Descriptor instead.
func (*GetCloudRamPolicyResp) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{12}
}

func (x *GetCloudRamPolicyResp) GetList() []*GetCloudRamPolicyRes {
	if x != nil {
		return x.List
	}
	return nil
}

type GetCloudRamPolicyRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" form:"description" json:"description" query:"description"`
}

func (x *GetCloudRamPolicyRes) Reset() {
	*x = GetCloudRamPolicyRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ram_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCloudRamPolicyRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCloudRamPolicyRes) ProtoMessage() {}

func (x *GetCloudRamPolicyRes) ProtoReflect() protoreflect.Message {
	mi := &file_ram_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCloudRamPolicyRes.ProtoReflect.Descriptor instead.
func (*GetCloudRamPolicyRes) Descriptor() ([]byte, []int) {
	return file_ram_proto_rawDescGZIP(), []int{13}
}

func (x *GetCloudRamPolicyRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetCloudRamPolicyRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

var File_ram_proto protoreflect.FileDescriptor

var file_ram_proto_rawDesc = []byte{
	0x0a, 0x09, 0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xb8, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x69, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x65, 0x64,
	0x5f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x6e, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x22, 0x56, 0x0a, 0x15, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0xbe, 0x03, 0x0a, 0x09, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x70, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x49,
	0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x69, 0x70, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x49,
	0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x0a, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x44, 0x61, 0x74, 0x65, 0x22, 0x5b, 0x0a,
	0x1f, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xd2, 0xbb,
	0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x70, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x69, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x22, 0x44, 0x0a, 0x19, 0x50, 0x72,
	0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f,
	0x61, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c,
	0x22, 0x5b, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x41, 0x4d, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3d,
	0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x52, 0x65, 0x73, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xae, 0x02,
	0x0a, 0x19, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0b, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x64,
	0x49, 0x70, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x69, 0x70,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x49,
	0x70, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x6f, 0x6c, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f,
	0x6c, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x2c, 0x0a, 0x11, 0x6e, 0x65, 0x77, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e, 0x65, 0x77,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x3d,
	0x0a, 0x12, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x22, 0x46, 0x0a,
	0x10, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x73, 0x70, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x4e, 0x65, 0x77,
	0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70,
	0x49, 0x64, 0x22, 0x4b, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x61,
	0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x61, 0x6d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0x4c, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x61, 0x6d, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x3b, 0x5a,
	0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_ram_proto_rawDescOnce sync.Once
	file_ram_proto_rawDescData = file_ram_proto_rawDesc
)

func file_ram_proto_rawDescGZIP() []byte {
	file_ram_proto_rawDescOnce.Do(func() {
		file_ram_proto_rawDescData = protoimpl.X.CompressGZIP(file_ram_proto_rawDescData)
	})
	return file_ram_proto_rawDescData
}

var file_ram_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_ram_proto_goTypes = []interface{}{
	(*DescribeRAMPolicyReq)(nil),            // 0: cloudman.DescribeRAMPolicyReq
	(*DescribeRAMPolicyResp)(nil),           // 1: cloudman.DescribeRAMPolicyResp
	(*RAMPolicy)(nil),                       // 2: cloudman.RAMPolicy
	(*AttachUser)(nil),                      // 3: cloudman.AttachUser
	(*ModifyRAMPolicySourceIPGroupReq)(nil), // 4: cloudman.ModifyRAMPolicySourceIPGroupReq
	(*PreviewRAMPolicyDeployReq)(nil),       // 5: cloudman.PreviewRAMPolicyDeployReq
	(*PreviewRAMPolicyDeployResp)(nil),      // 6: cloudman.PreviewRAMPolicyDeployResp
	(*PreviewRamPolicyDeployRes)(nil),       // 7: cloudman.PreviewRamPolicyDeployRes
	(*RAMPolicyDeployReq)(nil),              // 8: cloudman.RAMPolicyDeployReq
	(*SyncRamPolicyReq)(nil),                // 9: cloudman.SyncRamPolicyReq
	(*SyncNewRAMPolicyReq)(nil),             // 10: cloudman.SyncNewRAMPolicyReq
	(*GetCloudRamPolicyReq)(nil),            // 11: cloudman.GetCloudRamPolicyReq
	(*GetCloudRamPolicyResp)(nil),           // 12: cloudman.GetCloudRamPolicyResp
	(*GetCloudRamPolicyRes)(nil),            // 13: cloudman.GetCloudRamPolicyRes
}
var file_ram_proto_depIdxs = []int32{
	2,  // 0: cloudman.DescribeRAMPolicyResp.list:type_name -> cloudman.RAMPolicy
	3,  // 1: cloudman.RAMPolicy.attach_users:type_name -> cloudman.AttachUser
	7,  // 2: cloudman.PreviewRAMPolicyDeployResp.results:type_name -> cloudman.PreviewRamPolicyDeployRes
	3,  // 3: cloudman.PreviewRamPolicyDeployRes.attach_users:type_name -> cloudman.AttachUser
	13, // 4: cloudman.GetCloudRamPolicyResp.list:type_name -> cloudman.GetCloudRamPolicyRes
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_ram_proto_init() }
func file_ram_proto_init() {
	if File_ram_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ram_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRAMPolicyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRAMPolicyResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RAMPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttachUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyRAMPolicySourceIPGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRAMPolicyDeployReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRAMPolicyDeployResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewRamPolicyDeployRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RAMPolicyDeployReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncRamPolicyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncNewRAMPolicyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCloudRamPolicyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCloudRamPolicyResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ram_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCloudRamPolicyRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ram_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ram_proto_goTypes,
		DependencyIndexes: file_ram_proto_depIdxs,
		MessageInfos:      file_ram_proto_msgTypes,
	}.Build()
	File_ram_proto = out.File
	file_ram_proto_rawDesc = nil
	file_ram_proto_goTypes = nil
	file_ram_proto_depIdxs = nil
}
