// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: ip.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IPEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Type             string `protobuf:"bytes,2,opt,name=type,proto3" form:"type" json:"type" query:"type"`                                             // 区分网段还是IP地址
	CreateType       string `protobuf:"bytes,3,opt,name=create_type,json=createType,proto3" form:"create_type" json:"create_type" query:"create_type"` // "sync" or "custom"
	Address          string `protobuf:"bytes,4,opt,name=address,proto3" form:"address" json:"address" query:"address"`
	BindInstanceType string `protobuf:"bytes,5,opt,name=bind_instance_type,json=bindInstanceType,proto3" form:"bind_instance_type" json:"bind_instance_type" query:"bind_instance_type"`
	InstanceId       string `protobuf:"bytes,6,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	Desc             string `protobuf:"bytes,7,opt,name=desc,proto3" form:"desc" json:"desc" query:"desc"`
	IspId            string `protobuf:"bytes,8,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`           // 云厂商ID
	IspType          string `protobuf:"bytes,9,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"` // 云厂商类型
	IspName          string `protobuf:"bytes,10,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
	RegionId         string `protobuf:"bytes,11,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	CreatedTime      string `protobuf:"bytes,12,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	UpdatedTime      string `protobuf:"bytes,13,opt,name=updated_time,json=updatedTime,proto3" form:"updated_time" json:"updated_time" query:"updated_time"`
}

func (x *IPEntity) Reset() {
	*x = IPEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPEntity) ProtoMessage() {}

func (x *IPEntity) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPEntity.ProtoReflect.Descriptor instead.
func (*IPEntity) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{0}
}

func (x *IPEntity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IPEntity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *IPEntity) GetCreateType() string {
	if x != nil {
		return x.CreateType
	}
	return ""
}

func (x *IPEntity) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *IPEntity) GetBindInstanceType() string {
	if x != nil {
		return x.BindInstanceType
	}
	return ""
}

func (x *IPEntity) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *IPEntity) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *IPEntity) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *IPEntity) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *IPEntity) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

func (x *IPEntity) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *IPEntity) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *IPEntity) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

type IPAddressOperationLocks struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LockReason string `protobuf:"bytes,1,opt,name=lock_reason,json=lockReason,proto3" form:"lock_reason" json:"lock_reason" query:"lock_reason"`
}

func (x *IPAddressOperationLocks) Reset() {
	*x = IPAddressOperationLocks{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPAddressOperationLocks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPAddressOperationLocks) ProtoMessage() {}

func (x *IPAddressOperationLocks) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPAddressOperationLocks.ProtoReflect.Descriptor instead.
func (*IPAddressOperationLocks) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{1}
}

func (x *IPAddressOperationLocks) GetLockReason() string {
	if x != nil {
		return x.LockReason
	}
	return ""
}

type DescribeIPReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp         string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId    string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	SearchKey   string `protobuf:"bytes,5,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue string `protobuf:"bytes,6,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
	Address     string `protobuf:"bytes,7,opt,name=address,proto3" form:"address" json:"address" query:"address"`
}

func (x *DescribeIPReq) Reset() {
	*x = DescribeIPReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIPReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIPReq) ProtoMessage() {}

func (x *DescribeIPReq) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIPReq.ProtoReflect.Descriptor instead.
func (*DescribeIPReq) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeIPReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeIPReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeIPReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeIPReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeIPReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *DescribeIPReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

func (x *DescribeIPReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type DescribeIPRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32       `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*IPEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeIPRes) Reset() {
	*x = DescribeIPRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIPRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIPRes) ProtoMessage() {}

func (x *DescribeIPRes) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIPRes.ProtoReflect.Descriptor instead.
func (*DescribeIPRes) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeIPRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeIPRes) GetList() []*IPEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type IPMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address string      `protobuf:"bytes,1,opt,name=address,proto3" form:"address" json:"address" query:"address"`
	List    []*IPEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *IPMap) Reset() {
	*x = IPMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPMap) ProtoMessage() {}

func (x *IPMap) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPMap.ProtoReflect.Descriptor instead.
func (*IPMap) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{4}
}

func (x *IPMap) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *IPMap) GetList() []*IPEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type DescribeByIPsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addresses        []string `protobuf:"bytes,1,rep,name=addresses,proto3" form:"addresses" json:"addresses" query:"addresses"`
	BindInstanceType string   `protobuf:"bytes,2,opt,name=bind_instance_type,json=bindInstanceType,proto3" form:"bind_instance_type" json:"bind_instance_type" query:"bind_instance_type"`
}

func (x *DescribeByIPsReq) Reset() {
	*x = DescribeByIPsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeByIPsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeByIPsReq) ProtoMessage() {}

func (x *DescribeByIPsReq) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeByIPsReq.ProtoReflect.Descriptor instead.
func (*DescribeByIPsReq) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeByIPsReq) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *DescribeByIPsReq) GetBindInstanceType() string {
	if x != nil {
		return x.BindInstanceType
	}
	return ""
}

type DescribeByIPsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpMap []*IPMap `protobuf:"bytes,1,rep,name=ipMap,proto3" form:"ipMap" json:"ipMap" query:"ipMap"`
}

func (x *DescribeByIPsRes) Reset() {
	*x = DescribeByIPsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeByIPsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeByIPsRes) ProtoMessage() {}

func (x *DescribeByIPsRes) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeByIPsRes.ProtoReflect.Descriptor instead.
func (*DescribeByIPsRes) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{6}
}

func (x *DescribeByIPsRes) GetIpMap() []*IPMap {
	if x != nil {
		return x.IpMap
	}
	return nil
}

type DescribeIPGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Name        string `protobuf:"bytes,3,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Description string `protobuf:"bytes,4,opt,name=description,proto3" form:"description" json:"description" query:"description"`
}

func (x *DescribeIPGroupReq) Reset() {
	*x = DescribeIPGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIPGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIPGroupReq) ProtoMessage() {}

func (x *DescribeIPGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIPGroupReq.ProtoReflect.Descriptor instead.
func (*DescribeIPGroupReq) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{7}
}

func (x *DescribeIPGroupReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeIPGroupReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeIPGroupReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribeIPGroupReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type DescribeIPGroupResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32      `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*IPGroup `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeIPGroupResp) Reset() {
	*x = DescribeIPGroupResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIPGroupResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIPGroupResp) ProtoMessage() {}

func (x *DescribeIPGroupResp) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIPGroupResp.ProtoReflect.Descriptor instead.
func (*DescribeIPGroupResp) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{8}
}

func (x *DescribeIPGroupResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeIPGroupResp) GetList() []*IPGroup {
	if x != nil {
		return x.List
	}
	return nil
}

type IPGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string   `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name        string   `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Description string   `protobuf:"bytes,3,opt,name=description,proto3" form:"description" json:"description" query:"description"`
	CreatedTime string   `protobuf:"bytes,4,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	UpdatedTime string   `protobuf:"bytes,5,opt,name=updated_time,json=updatedTime,proto3" form:"updated_time" json:"updated_time" query:"updated_time"`
	CreateUser  string   `protobuf:"bytes,6,opt,name=create_user,json=createUser,proto3" form:"create_user" json:"create_user" query:"create_user"`
	UpdateUser  string   `protobuf:"bytes,7,opt,name=update_user,json=updateUser,proto3" form:"update_user" json:"update_user" query:"update_user"`
	Ips         []string `protobuf:"bytes,8,rep,name=ips,proto3" form:"ips" json:"ips" query:"ips"`
	IpIds       []string `protobuf:"bytes,9,rep,name=ip_ids,json=ipIds,proto3" form:"ip_ids" json:"ip_ids" query:"ip_ids"`
}

func (x *IPGroup) Reset() {
	*x = IPGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPGroup) ProtoMessage() {}

func (x *IPGroup) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPGroup.ProtoReflect.Descriptor instead.
func (*IPGroup) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{9}
}

func (x *IPGroup) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IPGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IPGroup) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *IPGroup) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *IPGroup) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

func (x *IPGroup) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *IPGroup) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

func (x *IPGroup) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *IPGroup) GetIpIds() []string {
	if x != nil {
		return x.IpIds
	}
	return nil
}

type CreateIPGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string   `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Description string   `protobuf:"bytes,2,opt,name=description,proto3" form:"description" json:"description" query:"description"`
	IpIds       []string `protobuf:"bytes,3,rep,name=ip_ids,json=ipIds,proto3" form:"ip_ids" json:"ip_ids" query:"ip_ids"`
}

func (x *CreateIPGroupReq) Reset() {
	*x = CreateIPGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateIPGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIPGroupReq) ProtoMessage() {}

func (x *CreateIPGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIPGroupReq.ProtoReflect.Descriptor instead.
func (*CreateIPGroupReq) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{10}
}

func (x *CreateIPGroupReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateIPGroupReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateIPGroupReq) GetIpIds() []string {
	if x != nil {
		return x.IpIds
	}
	return nil
}

type ModifyIPGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string   `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Description string   `protobuf:"bytes,2,opt,name=description,proto3" form:"description" json:"description" query:"description"`
	IpIds       []string `protobuf:"bytes,3,rep,name=ip_ids,json=ipIds,proto3" form:"ip_ids" json:"ip_ids" query:"ip_ids"`
}

func (x *ModifyIPGroupReq) Reset() {
	*x = ModifyIPGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyIPGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyIPGroupReq) ProtoMessage() {}

func (x *ModifyIPGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyIPGroupReq.ProtoReflect.Descriptor instead.
func (*ModifyIPGroupReq) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{11}
}

func (x *ModifyIPGroupReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModifyIPGroupReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ModifyIPGroupReq) GetIpIds() []string {
	if x != nil {
		return x.IpIds
	}
	return nil
}

type DeleteIPGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
}

func (x *DeleteIPGroupReq) Reset() {
	*x = DeleteIPGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteIPGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIPGroupReq) ProtoMessage() {}

func (x *DeleteIPGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIPGroupReq.ProtoReflect.Descriptor instead.
func (*DeleteIPGroupReq) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteIPGroupReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteIPReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
}

func (x *DeleteIPReq) Reset() {
	*x = DeleteIPReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ip_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteIPReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIPReq) ProtoMessage() {}

func (x *DeleteIPReq) ProtoReflect() protoreflect.Message {
	mi := &file_ip_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIPReq.ProtoReflect.Descriptor instead.
func (*DeleteIPReq) Descriptor() ([]byte, []int) {
	return file_ip_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteIPReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_ip_proto protoreflect.FileDescriptor

var file_ip_proto_rawDesc = []byte{
	0x0a, 0x08, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x22, 0xfc, 0x02, 0x0a, 0x08, 0x49, 0x50, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x2c, 0x0a, 0x12, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x69,
	0x6e, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x3a, 0x0a, 0x17, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0xc2, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0x4d, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x49, 0x50, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x50, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x49, 0x0a, 0x05, 0x49, 0x50, 0x4d, 0x61, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x49, 0x50, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x5e,
	0x0a, 0x10, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x79, 0x49, 0x50, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x12, 0x2c, 0x0a, 0x12, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x69,
	0x6e, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x39,
	0x0a, 0x10, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x79, 0x49, 0x50, 0x73, 0x52,
	0x65, 0x73, 0x12, 0x25, 0x0a, 0x05, 0x69, 0x70, 0x4d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x50, 0x4d,
	0x61, 0x70, 0x52, 0x05, 0x69, 0x70, 0x4d, 0x61, 0x70, 0x22, 0x72, 0x0a, 0x12, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x0a,
	0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x80, 0x02, 0x0a, 0x07, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x70, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x70, 0x49, 0x64, 0x73, 0x22, 0x5f, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x70, 0x49, 0x64, 0x73, 0x22, 0x5b, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49,
	0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x69, 0x70, 0x49,
	0x64, 0x73, 0x22, 0x22, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1d, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x50, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74,
	0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d,
	0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f,
	0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ip_proto_rawDescOnce sync.Once
	file_ip_proto_rawDescData = file_ip_proto_rawDesc
)

func file_ip_proto_rawDescGZIP() []byte {
	file_ip_proto_rawDescOnce.Do(func() {
		file_ip_proto_rawDescData = protoimpl.X.CompressGZIP(file_ip_proto_rawDescData)
	})
	return file_ip_proto_rawDescData
}

var file_ip_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_ip_proto_goTypes = []interface{}{
	(*IPEntity)(nil),                // 0: cloudman.IPEntity
	(*IPAddressOperationLocks)(nil), // 1: cloudman.IPAddressOperationLocks
	(*DescribeIPReq)(nil),           // 2: cloudman.DescribeIPReq
	(*DescribeIPRes)(nil),           // 3: cloudman.DescribeIPRes
	(*IPMap)(nil),                   // 4: cloudman.IPMap
	(*DescribeByIPsReq)(nil),        // 5: cloudman.DescribeByIPsReq
	(*DescribeByIPsRes)(nil),        // 6: cloudman.DescribeByIPsRes
	(*DescribeIPGroupReq)(nil),      // 7: cloudman.DescribeIPGroupReq
	(*DescribeIPGroupResp)(nil),     // 8: cloudman.DescribeIPGroupResp
	(*IPGroup)(nil),                 // 9: cloudman.IPGroup
	(*CreateIPGroupReq)(nil),        // 10: cloudman.CreateIPGroupReq
	(*ModifyIPGroupReq)(nil),        // 11: cloudman.ModifyIPGroupReq
	(*DeleteIPGroupReq)(nil),        // 12: cloudman.DeleteIPGroupReq
	(*DeleteIPReq)(nil),             // 13: cloudman.DeleteIPReq
}
var file_ip_proto_depIdxs = []int32{
	0, // 0: cloudman.DescribeIPRes.list:type_name -> cloudman.IPEntity
	0, // 1: cloudman.IPMap.list:type_name -> cloudman.IPEntity
	4, // 2: cloudman.DescribeByIPsRes.ipMap:type_name -> cloudman.IPMap
	9, // 3: cloudman.DescribeIPGroupResp.list:type_name -> cloudman.IPGroup
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_ip_proto_init() }
func file_ip_proto_init() {
	if File_ip_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ip_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPAddressOperationLocks); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIPReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIPRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeByIPsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeByIPsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIPGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIPGroupResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateIPGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyIPGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteIPGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ip_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteIPReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ip_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ip_proto_goTypes,
		DependencyIndexes: file_ip_proto_depIdxs,
		MessageInfos:      file_ip_proto_msgTypes,
	}.Build()
	File_ip_proto = out.File
	file_ip_proto_rawDesc = nil
	file_ip_proto_goTypes = nil
	file_ip_proto_depIdxs = nil
}
