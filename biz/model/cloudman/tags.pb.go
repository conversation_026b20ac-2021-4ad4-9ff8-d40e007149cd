// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: tags.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TagQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size     uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Name     string   `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *TagQueryReq) Reset() {
	*x = TagQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tags_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagQueryReq) ProtoMessage() {}

func (x *TagQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_tags_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagQueryReq.ProtoReflect.Descriptor instead.
func (*TagQueryReq) Descriptor() ([]byte, []int) {
	return file_tags_proto_rawDescGZIP(), []int{0}
}

func (x *TagQueryReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TagQueryReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *TagQueryReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *TagQueryReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *TagQueryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TagListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*TagInfo `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total uint32     `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *TagListResp) Reset() {
	*x = TagListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tags_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagListResp) ProtoMessage() {}

func (x *TagListResp) ProtoReflect() protoreflect.Message {
	mi := &file_tags_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagListResp.ProtoReflect.Descriptor instead.
func (*TagListResp) Descriptor() ([]byte, []int) {
	return file_tags_proto_rawDescGZIP(), []int{1}
}

func (x *TagListResp) GetList() []*TagInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TagListResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type TagCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Key   string `protobuf:"bytes,2,opt,name=key,proto3" form:"key" json:"key" query:"key"`
	Value string `protobuf:"bytes,3,opt,name=value,proto3" form:"value" json:"value" query:"value"`
	Desc  string `protobuf:"bytes,4,opt,name=desc,proto3" form:"desc" json:"desc" query:"desc"`
	Type  string `protobuf:"bytes,5,opt,name=type,proto3" form:"type" json:"type" query:"type"`
}

func (x *TagCreateReq) Reset() {
	*x = TagCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tags_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagCreateReq) ProtoMessage() {}

func (x *TagCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_tags_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagCreateReq.ProtoReflect.Descriptor instead.
func (*TagCreateReq) Descriptor() ([]byte, []int) {
	return file_tags_proto_rawDescGZIP(), []int{2}
}

func (x *TagCreateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TagCreateReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *TagCreateReq) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *TagCreateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *TagCreateReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type TagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Key   string `protobuf:"bytes,2,opt,name=key,proto3" form:"key" json:"key" query:"key"`
	Value string `protobuf:"bytes,3,opt,name=value,proto3" form:"value" json:"value" query:"value"`
	Desc  string `protobuf:"bytes,4,opt,name=desc,proto3" form:"desc" json:"desc" query:"desc"`
	Type  string `protobuf:"bytes,5,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	Kv    string `protobuf:"bytes,6,opt,name=kv,proto3" form:"kv" json:"kv" query:"kv"`
}

func (x *TagInfo) Reset() {
	*x = TagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_tags_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfo) ProtoMessage() {}

func (x *TagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_tags_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfo.ProtoReflect.Descriptor instead.
func (*TagInfo) Descriptor() ([]byte, []int) {
	return file_tags_proto_rawDescGZIP(), []int{3}
}

func (x *TagInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TagInfo) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *TagInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *TagInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *TagInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TagInfo) GetKv() string {
	if x != nil {
		return x.Kv
	}
	return ""
}

var File_tags_proto protoreflect.FileDescriptor

var file_tags_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x61, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x22, 0x81, 0x01, 0x0a, 0x0b, 0x74, 0x61, 0x67, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4a, 0x0a, 0x0b, 0x74, 0x61,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x74, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x6e, 0x0a, 0x0c, 0x74, 0x61, 0x67, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x79, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6b, 0x76, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6b,
	0x76, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68,
	0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f,
	0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_tags_proto_rawDescOnce sync.Once
	file_tags_proto_rawDescData = file_tags_proto_rawDesc
)

func file_tags_proto_rawDescGZIP() []byte {
	file_tags_proto_rawDescOnce.Do(func() {
		file_tags_proto_rawDescData = protoimpl.X.CompressGZIP(file_tags_proto_rawDescData)
	})
	return file_tags_proto_rawDescData
}

var file_tags_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_tags_proto_goTypes = []interface{}{
	(*TagQueryReq)(nil),  // 0: cloudman.tagQueryReq
	(*TagListResp)(nil),  // 1: cloudman.tagListResp
	(*TagCreateReq)(nil), // 2: cloudman.tagCreateReq
	(*TagInfo)(nil),      // 3: cloudman.tagInfo
}
var file_tags_proto_depIdxs = []int32{
	3, // 0: cloudman.tagListResp.list:type_name -> cloudman.tagInfo
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_tags_proto_init() }
func file_tags_proto_init() {
	if File_tags_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_tags_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tags_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tags_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_tags_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_tags_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_tags_proto_goTypes,
		DependencyIndexes: file_tags_proto_depIdxs,
		MessageInfos:      file_tags_proto_msgTypes,
	}.Build()
	File_tags_proto = out.File
	file_tags_proto_rawDesc = nil
	file_tags_proto_goTypes = nil
	file_tags_proto_depIdxs = nil
}
