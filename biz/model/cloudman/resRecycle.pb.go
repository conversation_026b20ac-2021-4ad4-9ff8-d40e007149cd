// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: resRecycle.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RecycleQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page         uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size         uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering     []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords     string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Name         string   `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Type         string   `protobuf:"bytes,6,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	InstanceId   string   `protobuf:"bytes,7,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	InstanceName string   `protobuf:"bytes,8,opt,name=instance_name,json=instanceName,proto3" form:"instance_name" json:"instance_name" query:"instance_name"`
	InstanceType string   `protobuf:"bytes,9,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
	IspId        string   `protobuf:"bytes,10,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	RegionId     string   `protobuf:"bytes,11,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	CreateUser   string   `protobuf:"bytes,12,opt,name=create_user,json=createUser,proto3" form:"create_user" json:"create_user" query:"create_user"`
	Status       string   `protobuf:"bytes,13,opt,name=status,proto3" form:"status" json:"status" query:"status"`
}

func (x *RecycleQueryReq) Reset() {
	*x = RecycleQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecycleQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleQueryReq) ProtoMessage() {}

func (x *RecycleQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleQueryReq.ProtoReflect.Descriptor instead.
func (*RecycleQueryReq) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{0}
}

func (x *RecycleQueryReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RecycleQueryReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *RecycleQueryReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *RecycleQueryReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *RecycleQueryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecycleQueryReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RecycleQueryReq) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *RecycleQueryReq) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

func (x *RecycleQueryReq) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *RecycleQueryReq) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *RecycleQueryReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *RecycleQueryReq) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *RecycleQueryReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type RecycleListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*RecycleShort `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total uint32          `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *RecycleListResp) Reset() {
	*x = RecycleListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecycleListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleListResp) ProtoMessage() {}

func (x *RecycleListResp) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleListResp.ProtoReflect.Descriptor instead.
func (*RecycleListResp) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{1}
}

func (x *RecycleListResp) GetList() []*RecycleShort {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *RecycleListResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type RecycleShort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string   `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name           string   `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Type           string   `protobuf:"bytes,3,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	Isp            string   `protobuf:"bytes,4,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	Status         int32    `protobuf:"varint,5,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	CreatedTime    int64    `protobuf:"varint,6,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	DestroyTime    int64    `protobuf:"varint,7,opt,name=destroy_time,json=destroyTime,proto3" form:"destroy_time" json:"destroy_time" query:"destroy_time"`
	UpdateUser     string   `protobuf:"bytes,8,opt,name=update_user,json=updateUser,proto3" form:"update_user" json:"update_user" query:"update_user"`
	InstanceId     string   `protobuf:"bytes,9,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	InstanceType   string   `protobuf:"bytes,10,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
	CreateUser     string   `protobuf:"bytes,11,opt,name=create_user,json=createUser,proto3" form:"create_user" json:"create_user" query:"create_user"`
	InnerIpAddress []string `protobuf:"bytes,12,rep,name=inner_ip_address,json=innerIpAddress,proto3" form:"inner_ip_address" json:"inner_ip_address" query:"inner_ip_address"`
}

func (x *RecycleShort) Reset() {
	*x = RecycleShort{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecycleShort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleShort) ProtoMessage() {}

func (x *RecycleShort) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleShort.ProtoReflect.Descriptor instead.
func (*RecycleShort) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{2}
}

func (x *RecycleShort) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecycleShort) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecycleShort) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RecycleShort) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *RecycleShort) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RecycleShort) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *RecycleShort) GetDestroyTime() int64 {
	if x != nil {
		return x.DestroyTime
	}
	return 0
}

func (x *RecycleShort) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

func (x *RecycleShort) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *RecycleShort) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *RecycleShort) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *RecycleShort) GetInnerIpAddress() []string {
	if x != nil {
		return x.InnerIpAddress
	}
	return nil
}

type RecycleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Type        string `protobuf:"bytes,3,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	Isp         string `protobuf:"bytes,4,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	Status      uint32 `protobuf:"varint,5,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	CreatedTime int64  `protobuf:"varint,6,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	DestroyTime int64  `protobuf:"varint,7,opt,name=destroy_time,json=destroyTime,proto3" form:"destroy_time" json:"destroy_time" query:"destroy_time"`
	UpdateUser  string `protobuf:"bytes,8,opt,name=update_user,json=updateUser,proto3" form:"update_user" json:"update_user" query:"update_user"`
}

func (x *RecycleInfo) Reset() {
	*x = RecycleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecycleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleInfo) ProtoMessage() {}

func (x *RecycleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleInfo.ProtoReflect.Descriptor instead.
func (*RecycleInfo) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{3}
}

func (x *RecycleInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecycleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecycleInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RecycleInfo) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *RecycleInfo) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RecycleInfo) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *RecycleInfo) GetDestroyTime() int64 {
	if x != nil {
		return x.DestroyTime
	}
	return 0
}

func (x *RecycleInfo) GetUpdateUser() string {
	if x != nil {
		return x.UpdateUser
	}
	return ""
}

type RecyclePolicyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceType string `protobuf:"bytes,1,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
	Name         string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Page         uint64 `protobuf:"varint,3,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size         uint64 `protobuf:"varint,4,opt,name=size,proto3" form:"size" json:"size" query:"size"`
}

func (x *RecyclePolicyReq) Reset() {
	*x = RecyclePolicyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclePolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclePolicyReq) ProtoMessage() {}

func (x *RecyclePolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclePolicyReq.ProtoReflect.Descriptor instead.
func (*RecyclePolicyReq) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{4}
}

func (x *RecyclePolicyReq) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *RecyclePolicyReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecyclePolicyReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RecyclePolicyReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type RecyclePolicyResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*RecyclePolicy `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total int32            `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *RecyclePolicyResp) Reset() {
	*x = RecyclePolicyResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclePolicyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclePolicyResp) ProtoMessage() {}

func (x *RecyclePolicyResp) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclePolicyResp.ProtoReflect.Descriptor instead.
func (*RecyclePolicyResp) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{5}
}

func (x *RecyclePolicyResp) GetList() []*RecyclePolicy {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *RecyclePolicyResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type RecyclePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name         string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	InstanceType string `protobuf:"bytes,3,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
	Enable       bool   `protobuf:"varint,4,opt,name=enable,proto3" form:"enable" json:"enable" query:"enable"`
	PolicyInfo   string `protobuf:"bytes,5,opt,name=policy_info,json=policyInfo,proto3" form:"policy_info" json:"policy_info" query:"policy_info"`
	ReleaseTime  int32  `protobuf:"varint,6,opt,name=release_time,json=releaseTime,proto3" form:"release_time" json:"release_time" query:"release_time"` // 释放时间
}

func (x *RecyclePolicy) Reset() {
	*x = RecyclePolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclePolicy) ProtoMessage() {}

func (x *RecyclePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclePolicy.ProtoReflect.Descriptor instead.
func (*RecyclePolicy) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{6}
}

func (x *RecyclePolicy) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecyclePolicy) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecyclePolicy) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *RecyclePolicy) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *RecyclePolicy) GetPolicyInfo() string {
	if x != nil {
		return x.PolicyInfo
	}
	return ""
}

func (x *RecyclePolicy) GetReleaseTime() int32 {
	if x != nil {
		return x.ReleaseTime
	}
	return 0
}

type RecyclePolicyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Related string `protobuf:"bytes,1,opt,name=related,proto3" form:"related" json:"related" query:"related"` // 条件间关系
	List    string `protobuf:"bytes,2,opt,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *RecyclePolicyInfo) Reset() {
	*x = RecyclePolicyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclePolicyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclePolicyInfo) ProtoMessage() {}

func (x *RecyclePolicyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclePolicyInfo.ProtoReflect.Descriptor instead.
func (*RecyclePolicyInfo) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{7}
}

func (x *RecyclePolicyInfo) GetRelated() string {
	if x != nil {
		return x.Related
	}
	return ""
}

func (x *RecyclePolicyInfo) GetList() string {
	if x != nil {
		return x.List
	}
	return ""
}

type RecyclePolicyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key     string `protobuf:"bytes,1,opt,name=key,proto3" form:"key" json:"key" query:"key"`
	Related string `protobuf:"bytes,2,opt,name=related,proto3" form:"related" json:"related" query:"related"`
	// google.protobuf.Value value = 3;
	Value string `protobuf:"bytes,3,opt,name=value,proto3" form:"value" json:"value" query:"value"`
}

func (x *RecyclePolicyList) Reset() {
	*x = RecyclePolicyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecyclePolicyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecyclePolicyList) ProtoMessage() {}

func (x *RecyclePolicyList) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecyclePolicyList.ProtoReflect.Descriptor instead.
func (*RecyclePolicyList) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{8}
}

func (x *RecyclePolicyList) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *RecyclePolicyList) GetRelated() string {
	if x != nil {
		return x.Related
	}
	return ""
}

func (x *RecyclePolicyList) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type PolicyOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	InstanceType string `protobuf:"bytes,2,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
}

func (x *PolicyOption) Reset() {
	*x = PolicyOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyOption) ProtoMessage() {}

func (x *PolicyOption) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyOption.ProtoReflect.Descriptor instead.
func (*PolicyOption) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{9}
}

func (x *PolicyOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PolicyOption) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

type RecoverReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id" query:"id"`
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" form:"reason" json:"reason" query:"reason"`
}

func (x *RecoverReq) Reset() {
	*x = RecoverReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoverReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverReq) ProtoMessage() {}

func (x *RecoverReq) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverReq.ProtoReflect.Descriptor instead.
func (*RecoverReq) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{10}
}

func (x *RecoverReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecoverReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type BatchRecoverReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids    []string `protobuf:"bytes,1,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
	Reason string   `protobuf:"bytes,2,opt,name=reason,proto3" form:"reason" json:"reason" query:"reason"`
}

func (x *BatchRecoverReq) Reset() {
	*x = BatchRecoverReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRecoverReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRecoverReq) ProtoMessage() {}

func (x *BatchRecoverReq) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRecoverReq.ProtoReflect.Descriptor instead.
func (*BatchRecoverReq) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{11}
}

func (x *BatchRecoverReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchRecoverReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type RecycleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id" query:"id"`
	IsForce bool   `protobuf:"varint,2,opt,name=is_force,json=isForce,proto3" form:"is_force" json:"is_force" query:"is_force"`
}

func (x *RecycleReq) Reset() {
	*x = RecycleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resRecycle_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecycleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecycleReq) ProtoMessage() {}

func (x *RecycleReq) ProtoReflect() protoreflect.Message {
	mi := &file_resRecycle_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecycleReq.ProtoReflect.Descriptor instead.
func (*RecycleReq) Descriptor() ([]byte, []int) {
	return file_resRecycle_proto_rawDescGZIP(), []int{12}
}

func (x *RecycleReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecycleReq) GetIsForce() bool {
	if x != nil {
		return x.IsForce
	}
	return false
}

var File_resRecycle_proto protoreflect.FileDescriptor

var file_resRecycle_proto_rawDesc = []byte{
	0x0a, 0x10, 0x72, 0x65, 0x73, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x09, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf1, 0x02, 0x0a, 0x0f, 0x72, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x53, 0x0a, 0x0f, 0x72,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2a,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x53,
	0x68, 0x6f, 0x72, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xe8, 0x02, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x53, 0x68, 0x6f, 0x72,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x74, 0x72, 0x6f,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x74, 0x72, 0x6f, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x6e,
	0x65, 0x72, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xd6, 0x01, 0x0a, 0x0b,
	0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x22, 0x73, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x56, 0x0a, 0x11, 0x72, 0x65, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2b,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x22, 0xb4, 0x01, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x41, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x55, 0x0a, 0x11, 0x52,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x47, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x42, 0x0a, 0x0a, 0x52,
	0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xb2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0xd2, 0xbb, 0x18,
	0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0x3b, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x45, 0x0a, 0x0a,
	0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xb2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0xd2, 0xbb,
	0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x6f,
	0x72, 0x63, 0x65, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d,
	0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70,
	0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69,
	0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_resRecycle_proto_rawDescOnce sync.Once
	file_resRecycle_proto_rawDescData = file_resRecycle_proto_rawDesc
)

func file_resRecycle_proto_rawDescGZIP() []byte {
	file_resRecycle_proto_rawDescOnce.Do(func() {
		file_resRecycle_proto_rawDescData = protoimpl.X.CompressGZIP(file_resRecycle_proto_rawDescData)
	})
	return file_resRecycle_proto_rawDescData
}

var file_resRecycle_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_resRecycle_proto_goTypes = []interface{}{
	(*RecycleQueryReq)(nil),   // 0: cloudman.recycleQueryReq
	(*RecycleListResp)(nil),   // 1: cloudman.recycleListResp
	(*RecycleShort)(nil),      // 2: cloudman.recycleShort
	(*RecycleInfo)(nil),       // 3: cloudman.recycleInfo
	(*RecyclePolicyReq)(nil),  // 4: cloudman.recyclePolicyReq
	(*RecyclePolicyResp)(nil), // 5: cloudman.recyclePolicyResp
	(*RecyclePolicy)(nil),     // 6: cloudman.recyclePolicy
	(*RecyclePolicyInfo)(nil), // 7: cloudman.RecyclePolicyInfo
	(*RecyclePolicyList)(nil), // 8: cloudman.RecyclePolicyList
	(*PolicyOption)(nil),      // 9: cloudman.policyOption
	(*RecoverReq)(nil),        // 10: cloudman.RecoverReq
	(*BatchRecoverReq)(nil),   // 11: cloudman.BatchRecoverReq
	(*RecycleReq)(nil),        // 12: cloudman.RecycleReq
}
var file_resRecycle_proto_depIdxs = []int32{
	2, // 0: cloudman.recycleListResp.list:type_name -> cloudman.recycleShort
	6, // 1: cloudman.recyclePolicyResp.list:type_name -> cloudman.recyclePolicy
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_resRecycle_proto_init() }
func file_resRecycle_proto_init() {
	if File_resRecycle_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_resRecycle_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecycleQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecycleListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecycleShort); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecycleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecyclePolicyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecyclePolicyResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecyclePolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecyclePolicyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecyclePolicyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecoverReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRecoverReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resRecycle_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecycleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_resRecycle_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_resRecycle_proto_goTypes,
		DependencyIndexes: file_resRecycle_proto_depIdxs,
		MessageInfos:      file_resRecycle_proto_msgTypes,
	}.Build()
	File_resRecycle_proto = out.File
	file_resRecycle_proto_rawDesc = nil
	file_resRecycle_proto_goTypes = nil
	file_resRecycle_proto_depIdxs = nil
}
