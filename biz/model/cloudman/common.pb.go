// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: common.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConditionType int32

const (
	ConditionType_AND ConditionType = 0
	ConditionType_OR  ConditionType = 1
)

// Enum value maps for ConditionType.
var (
	ConditionType_name = map[int32]string{
		0: "AND",
		1: "OR",
	}
	ConditionType_value = map[string]int32{
		"AND": 0,
		"OR":  1,
	}
)

func (x ConditionType) Enum() *ConditionType {
	p := new(ConditionType)
	*p = x
	return p
}

func (x ConditionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConditionType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[0].Descriptor()
}

func (ConditionType) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[0]
}

func (x ConditionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConditionType.Descriptor instead.
func (ConditionType) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

type QueryOperator int32

const (
	QueryOperator_equal                     QueryOperator = 0
	QueryOperator_not_equal                 QueryOperator = 1
	QueryOperator_in                        QueryOperator = 2
	QueryOperator_not_in                    QueryOperator = 3
	QueryOperator_less                      QueryOperator = 4
	QueryOperator_less_or_equal             QueryOperator = 5
	QueryOperator_greater                   QueryOperator = 6
	QueryOperator_greater_or_equal          QueryOperator = 7
	QueryOperator_datetime_less             QueryOperator = 8
	QueryOperator_datetime_less_or_equal    QueryOperator = 9
	QueryOperator_datetime_greater          QueryOperator = 10
	QueryOperator_datetime_greater_or_equal QueryOperator = 11
	QueryOperator_begins_with               QueryOperator = 12
	QueryOperator_not_begins_with           QueryOperator = 13
	QueryOperator_contains                  QueryOperator = 14
	QueryOperator_not_contains              QueryOperator = 15
	QueryOperator_ends_with                 QueryOperator = 16
	QueryOperator_not_ends_with             QueryOperator = 17
	QueryOperator_is_empty                  QueryOperator = 18
	QueryOperator_is_not_empty              QueryOperator = 19
	QueryOperator_is_null                   QueryOperator = 20
	QueryOperator_is_not_null               QueryOperator = 21
	QueryOperator_exist                     QueryOperator = 22
	QueryOperator_not_exist                 QueryOperator = 23
)

// Enum value maps for QueryOperator.
var (
	QueryOperator_name = map[int32]string{
		0:  "equal",
		1:  "not_equal",
		2:  "in",
		3:  "not_in",
		4:  "less",
		5:  "less_or_equal",
		6:  "greater",
		7:  "greater_or_equal",
		8:  "datetime_less",
		9:  "datetime_less_or_equal",
		10: "datetime_greater",
		11: "datetime_greater_or_equal",
		12: "begins_with",
		13: "not_begins_with",
		14: "contains",
		15: "not_contains",
		16: "ends_with",
		17: "not_ends_with",
		18: "is_empty",
		19: "is_not_empty",
		20: "is_null",
		21: "is_not_null",
		22: "exist",
		23: "not_exist",
	}
	QueryOperator_value = map[string]int32{
		"equal":                     0,
		"not_equal":                 1,
		"in":                        2,
		"not_in":                    3,
		"less":                      4,
		"less_or_equal":             5,
		"greater":                   6,
		"greater_or_equal":          7,
		"datetime_less":             8,
		"datetime_less_or_equal":    9,
		"datetime_greater":          10,
		"datetime_greater_or_equal": 11,
		"begins_with":               12,
		"not_begins_with":           13,
		"contains":                  14,
		"not_contains":              15,
		"ends_with":                 16,
		"not_ends_with":             17,
		"is_empty":                  18,
		"is_not_empty":              19,
		"is_null":                   20,
		"is_not_null":               21,
		"exist":                     22,
		"not_exist":                 23,
	}
)

func (x QueryOperator) Enum() *QueryOperator {
	p := new(QueryOperator)
	*p = x
	return p
}

func (x QueryOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QueryOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[1].Descriptor()
}

func (QueryOperator) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[1]
}

func (x QueryOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QueryOperator.Descriptor instead.
func (QueryOperator) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

type Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" form:"message" json:"message" query:"message"`
}

func (x *Result) Reset() {
	*x = Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Result) ProtoMessage() {}

func (x *Result) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Result.ProtoReflect.Descriptor instead.
func (*Result) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *Result) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ResultOK struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ok bool `protobuf:"varint,1,opt,name=ok,proto3" form:"ok" json:"ok" query:"ok"`
}

func (x *ResultOK) Reset() {
	*x = ResultOK{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResultOK) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultOK) ProtoMessage() {}

func (x *ResultOK) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultOK.ProtoReflect.Descriptor instead.
func (*ResultOK) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *ResultOK) GetOk() bool {
	if x != nil {
		return x.Ok
	}
	return false
}

type BatchResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errors         []string `protobuf:"bytes,1,rep,name=Errors,proto3" form:"Errors" json:"Errors" query:"Errors"`
	Success        []string `protobuf:"bytes,2,rep,name=Success,proto3" form:"Success" json:"Success" query:"Success"`
	SuccessCreated []string `protobuf:"bytes,3,rep,name=SuccessCreated,proto3" form:"SuccessCreated" json:"SuccessCreated" query:"SuccessCreated"`
	SuccessUpdated []string `protobuf:"bytes,4,rep,name=SuccessUpdated,proto3" form:"SuccessUpdated" json:"SuccessUpdated" query:"SuccessUpdated"`
	UpdateErrors   []string `protobuf:"bytes,5,rep,name=UpdateErrors,proto3" form:"UpdateErrors" json:"UpdateErrors" query:"UpdateErrors"`
}

func (x *BatchResult) Reset() {
	*x = BatchResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchResult) ProtoMessage() {}

func (x *BatchResult) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchResult.ProtoReflect.Descriptor instead.
func (*BatchResult) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

func (x *BatchResult) GetErrors() []string {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *BatchResult) GetSuccess() []string {
	if x != nil {
		return x.Success
	}
	return nil
}

func (x *BatchResult) GetSuccessCreated() []string {
	if x != nil {
		return x.SuccessCreated
	}
	return nil
}

func (x *BatchResult) GetSuccessUpdated() []string {
	if x != nil {
		return x.SuccessUpdated
	}
	return nil
}

func (x *BatchResult) GetUpdateErrors() []string {
	if x != nil {
		return x.UpdateErrors
	}
	return nil
}

type ObjectID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
}

func (x *ObjectID) Reset() {
	*x = ObjectID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectID) ProtoMessage() {}

func (x *ObjectID) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectID.ProtoReflect.Descriptor instead.
func (*ObjectID) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{3}
}

func (x *ObjectID) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ObjectIDs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []string `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *ObjectIDs) Reset() {
	*x = ObjectIDs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectIDs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectIDs) ProtoMessage() {}

func (x *ObjectIDs) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectIDs.ProtoReflect.Descriptor instead.
func (*ObjectIDs) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{4}
}

func (x *ObjectIDs) GetList() []string {
	if x != nil {
		return x.List
	}
	return nil
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{5}
}

type OptionResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*FieldOption `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *OptionResp) Reset() {
	*x = OptionResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OptionResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptionResp) ProtoMessage() {}

func (x *OptionResp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptionResp.ProtoReflect.Descriptor instead.
func (*OptionResp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{6}
}

func (x *OptionResp) GetList() []*FieldOption {
	if x != nil {
		return x.List
	}
	return nil
}

type FieldOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Type     string `protobuf:"bytes,2,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	Value    string `protobuf:"bytes,3,opt,name=value,proto3" form:"value" json:"value" query:"value"`
	Required bool   `protobuf:"varint,4,opt,name=required,proto3" form:"required" json:"required" query:"required"`
	Indexed  bool   `protobuf:"varint,5,opt,name=indexed,proto3" form:"indexed" json:"indexed" query:"indexed"`
	Extra    bool   `protobuf:"varint,6,opt,name=extra,proto3" form:"extra" json:"extra" query:"extra"`
}

func (x *FieldOption) Reset() {
	*x = FieldOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldOption) ProtoMessage() {}

func (x *FieldOption) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldOption.ProtoReflect.Descriptor instead.
func (*FieldOption) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{7}
}

func (x *FieldOption) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FieldOption) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *FieldOption) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *FieldOption) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *FieldOption) GetIndexed() bool {
	if x != nil {
		return x.Indexed
	}
	return false
}

func (x *FieldOption) GetExtra() bool {
	if x != nil {
		return x.Extra
	}
	return false
}

type AgentIDResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId string `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" form:"agent_id" json:"agent_id" query:"agent_id"`
}

func (x *AgentIDResp) Reset() {
	*x = AgentIDResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentIDResp) ProtoMessage() {}

func (x *AgentIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentIDResp.ProtoReflect.Descriptor instead.
func (*AgentIDResp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{8}
}

func (x *AgentIDResp) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

type QueryRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Field    string        `protobuf:"bytes,1,opt,name=field,proto3" form:"field" json:"field" query:"field"`
	Operator QueryOperator `protobuf:"varint,2,opt,name=operator,proto3,enum=cloudman.QueryOperator" form:"operator" json:"operator" query:"operator"`
	Value    string        `protobuf:"bytes,3,opt,name=value,proto3" form:"value" json:"value" query:"value"`
	Values   []string      `protobuf:"bytes,4,rep,name=values,proto3" form:"values" json:"values" query:"values"`
}

func (x *QueryRule) Reset() {
	*x = QueryRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRule) ProtoMessage() {}

func (x *QueryRule) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRule.ProtoReflect.Descriptor instead.
func (*QueryRule) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{9}
}

func (x *QueryRule) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *QueryRule) GetOperator() QueryOperator {
	if x != nil {
		return x.Operator
	}
	return QueryOperator_equal
}

func (x *QueryRule) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *QueryRule) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type CustomQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Condition ConditionType `protobuf:"varint,1,opt,name=condition,proto3,enum=cloudman.ConditionType" form:"condition" json:"condition" query:"condition"`
	Rules     []*QueryRule  `protobuf:"bytes,2,rep,name=rules,proto3" form:"rules" json:"rules" query:"rules"`
	Page      int64         `protobuf:"varint,3,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size      int64         `protobuf:"varint,4,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering  []string      `protobuf:"bytes,5,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
}

func (x *CustomQuery) Reset() {
	*x = CustomQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomQuery) ProtoMessage() {}

func (x *CustomQuery) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomQuery.ProtoReflect.Descriptor instead.
func (*CustomQuery) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{10}
}

func (x *CustomQuery) GetCondition() ConditionType {
	if x != nil {
		return x.Condition
	}
	return ConditionType_AND
}

func (x *CustomQuery) GetRules() []*QueryRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *CustomQuery) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *CustomQuery) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *CustomQuery) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

type TimeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ref   string `protobuf:"bytes,1,opt,name=ref,proto3" form:"ref" json:"ref" query:"ref"`
	Value int32  `protobuf:"varint,2,opt,name=value,proto3" form:"value" json:"value" query:"value"`
}

func (x *TimeFilter) Reset() {
	*x = TimeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeFilter) ProtoMessage() {}

func (x *TimeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeFilter.ProtoReflect.Descriptor instead.
func (*TimeFilter) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{11}
}

func (x *TimeFilter) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

func (x *TimeFilter) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type SearchColumn struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" form:"key" json:"key" query:"key"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" form:"value" json:"value" query:"value"`
}

func (x *SearchColumn) Reset() {
	*x = SearchColumn{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchColumn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchColumn) ProtoMessage() {}

func (x *SearchColumn) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchColumn.ProtoReflect.Descriptor instead.
func (*SearchColumn) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{12}
}

func (x *SearchColumn) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SearchColumn) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ExportXlsxResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File []byte `protobuf:"bytes,1,opt,name=file,proto3" form:"file" json:"file" query:"file"`
}

func (x *ExportXlsxResp) Reset() {
	*x = ExportXlsxResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportXlsxResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportXlsxResp) ProtoMessage() {}

func (x *ExportXlsxResp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportXlsxResp.ProtoReflect.Descriptor instead.
func (*ExportXlsxResp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{13}
}

func (x *ExportXlsxResp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

type ObjIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
}

func (x *ObjIDReq) Reset() {
	*x = ObjIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjIDReq) ProtoMessage() {}

func (x *ObjIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjIDReq.ProtoReflect.Descriptor instead.
func (*ObjIDReq) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{14}
}

func (x *ObjIDReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type OrderID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id" path:"order_id"`
}

func (x *OrderID) Reset() {
	*x = OrderID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderID) ProtoMessage() {}

func (x *OrderID) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderID.ProtoReflect.Descriptor instead.
func (*OrderID) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{15}
}

func (x *OrderID) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type Raw struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawBody []byte `protobuf:"bytes,2,opt,name=raw_body,json=rawBody,proto3" form:"raw_body" json:"raw_body" query:"raw_body"`
}

func (x *Raw) Reset() {
	*x = Raw{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Raw) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Raw) ProtoMessage() {}

func (x *Raw) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Raw.ProtoReflect.Descriptor instead.
func (*Raw) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{16}
}

func (x *Raw) GetRawBody() []byte {
	if x != nil {
		return x.RawBody
	}
	return nil
}

var File_common_proto protoreflect.FileDescriptor

var file_common_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x22, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x4f, 0x4b, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x02, 0x6f, 0x6b, 0x22, 0xb3, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x26, 0x0a,
	0x0e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x22, 0x22, 0x0a, 0x08, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1f, 0x0a,
	0x09, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x07,
	0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x37, 0x0a, 0x0a, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x97, 0x01, 0x0a, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x28, 0x0a, 0x0b, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xb3, 0x01, 0x0a, 0x0b,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x35, 0x0a, 0x09, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x22, 0x34, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x10, 0x0a, 0x03, 0x72, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x65,
	0x66, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x36, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x24, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x22, 0x0a, 0x08, 0x4f, 0x62, 0x6a, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xd2,
	0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x07, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x44, 0x12, 0x27, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xd2, 0xbb, 0x18, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x20, 0x0a,
	0x03, 0x52, 0x61, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x42, 0x6f, 0x64, 0x79, 0x2a,
	0x20, 0x0a, 0x0d, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10,
	0x01, 0x2a, 0xa1, 0x03, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x09, 0x0a, 0x05, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x06, 0x0a,
	0x02, 0x69, 0x6e, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x5f, 0x69, 0x6e, 0x10,
	0x03, 0x12, 0x08, 0x0a, 0x04, 0x6c, 0x65, 0x73, 0x73, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x6c,
	0x65, 0x73, 0x73, 0x5f, 0x6f, 0x72, 0x5f, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x10, 0x05, 0x12, 0x0b,
	0x0a, 0x07, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x67,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x5f, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x10,
	0x07, 0x12, 0x11, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6c, 0x65,
	0x73, 0x73, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x72, 0x5f, 0x65, 0x71, 0x75, 0x61, 0x6c, 0x10, 0x09,
	0x12, 0x14, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x67, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x72, 0x10, 0x0a, 0x12, 0x1d, 0x0a, 0x19, 0x64, 0x61, 0x74, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x67, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x5f, 0x65, 0x71,
	0x75, 0x61, 0x6c, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x73, 0x5f,
	0x77, 0x69, 0x74, 0x68, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x6e, 0x6f, 0x74, 0x5f, 0x62, 0x65,
	0x67, 0x69, 0x6e, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x10, 0x0d, 0x12, 0x0c, 0x0a, 0x08, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x10, 0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x6e, 0x6f, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x73, 0x10, 0x0f, 0x12, 0x0d, 0x0a, 0x09, 0x65,
	0x6e, 0x64, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x10, 0x10, 0x12, 0x11, 0x0a, 0x0d, 0x6e, 0x6f,
	0x74, 0x5f, 0x65, 0x6e, 0x64, 0x73, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x10, 0x11, 0x12, 0x0c, 0x0a,
	0x08, 0x69, 0x73, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x10, 0x12, 0x12, 0x10, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x10, 0x13, 0x12, 0x0b, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x6e, 0x75, 0x6c, 0x6c, 0x10, 0x14, 0x12, 0x0f, 0x0a, 0x0b, 0x69, 0x73,
	0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x6e, 0x75, 0x6c, 0x6c, 0x10, 0x15, 0x12, 0x09, 0x0a, 0x05, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x10, 0x16, 0x12, 0x0d, 0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x78,
	0x69, 0x73, 0x74, 0x10, 0x17, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74,
	0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d,
	0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f,
	0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData = file_common_proto_rawDesc
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_proto_rawDescData)
	})
	return file_common_proto_rawDescData
}

var file_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_common_proto_goTypes = []interface{}{
	(ConditionType)(0),     // 0: cloudman.ConditionType
	(QueryOperator)(0),     // 1: cloudman.QueryOperator
	(*Result)(nil),         // 2: cloudman.Result
	(*ResultOK)(nil),       // 3: cloudman.ResultOK
	(*BatchResult)(nil),    // 4: cloudman.BatchResult
	(*ObjectID)(nil),       // 5: cloudman.ObjectID
	(*ObjectIDs)(nil),      // 6: cloudman.ObjectIDs
	(*Empty)(nil),          // 7: cloudman.Empty
	(*OptionResp)(nil),     // 8: cloudman.OptionResp
	(*FieldOption)(nil),    // 9: cloudman.FieldOption
	(*AgentIDResp)(nil),    // 10: cloudman.AgentIDResp
	(*QueryRule)(nil),      // 11: cloudman.QueryRule
	(*CustomQuery)(nil),    // 12: cloudman.CustomQuery
	(*TimeFilter)(nil),     // 13: cloudman.timeFilter
	(*SearchColumn)(nil),   // 14: cloudman.searchColumn
	(*ExportXlsxResp)(nil), // 15: cloudman.ExportXlsxResp
	(*ObjIDReq)(nil),       // 16: cloudman.ObjIDReq
	(*OrderID)(nil),        // 17: cloudman.OrderID
	(*Raw)(nil),            // 18: cloudman.Raw
}
var file_common_proto_depIdxs = []int32{
	9,  // 0: cloudman.OptionResp.list:type_name -> cloudman.FieldOption
	1,  // 1: cloudman.QueryRule.operator:type_name -> cloudman.QueryOperator
	0,  // 2: cloudman.CustomQuery.condition:type_name -> cloudman.ConditionType
	11, // 3: cloudman.CustomQuery.rules:type_name -> cloudman.QueryRule
	4,  // [4:4] is the sub-list for method output_type
	4,  // [4:4] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResultOK); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectIDs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OptionResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentIDResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchColumn); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportXlsxResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Raw); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		EnumInfos:         file_common_proto_enumTypes,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_rawDesc = nil
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
