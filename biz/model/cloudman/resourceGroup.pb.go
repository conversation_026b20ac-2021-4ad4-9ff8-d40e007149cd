// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: resourceGroup.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GroupQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size     uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Name     string   `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *GroupQueryReq) Reset() {
	*x = GroupQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupQueryReq) ProtoMessage() {}

func (x *GroupQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupQueryReq.ProtoReflect.Descriptor instead.
func (*GroupQueryReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{0}
}

func (x *GroupQueryReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GroupQueryReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GroupQueryReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *GroupQueryReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *GroupQueryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GroupListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*GroupInfo `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total uint32       `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *GroupListResp) Reset() {
	*x = GroupListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupListResp) ProtoMessage() {}

func (x *GroupListResp) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupListResp.ProtoReflect.Descriptor instead.
func (*GroupListResp) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{1}
}

func (x *GroupListResp) GetList() []*GroupInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GroupListResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GroupCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" form:"desc" json:"desc" query:"desc"`
}

func (x *GroupCreateReq) Reset() {
	*x = GroupCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupCreateReq) ProtoMessage() {}

func (x *GroupCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupCreateReq.ProtoReflect.Descriptor instead.
func (*GroupCreateReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{2}
}

func (x *GroupCreateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GroupCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GroupCreateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type GroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" form:"desc" json:"desc" query:"desc"`
}

func (x *GroupInfo) Reset() {
	*x = GroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupInfo) ProtoMessage() {}

func (x *GroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupInfo.ProtoReflect.Descriptor instead.
func (*GroupInfo) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{3}
}

func (x *GroupInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GroupInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GroupInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type ResGroupPolicyQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size     uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Name     string   `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *ResGroupPolicyQueryReq) Reset() {
	*x = ResGroupPolicyQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResGroupPolicyQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResGroupPolicyQueryReq) ProtoMessage() {}

func (x *ResGroupPolicyQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResGroupPolicyQueryReq.ProtoReflect.Descriptor instead.
func (*ResGroupPolicyQueryReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{4}
}

func (x *ResGroupPolicyQueryReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ResGroupPolicyQueryReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ResGroupPolicyQueryReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *ResGroupPolicyQueryReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *ResGroupPolicyQueryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GroupPolicyInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
}

func (x *GroupPolicyInfoReq) Reset() {
	*x = GroupPolicyInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupPolicyInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupPolicyInfoReq) ProtoMessage() {}

func (x *GroupPolicyInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupPolicyInfoReq.ProtoReflect.Descriptor instead.
func (*GroupPolicyInfoReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{5}
}

func (x *GroupPolicyInfoReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GroupPolicyInfoReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

type ResGroupPolicyListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*ResGroupPolicyShort `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total uint32                 `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *ResGroupPolicyListResp) Reset() {
	*x = ResGroupPolicyListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResGroupPolicyListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResGroupPolicyListResp) ProtoMessage() {}

func (x *ResGroupPolicyListResp) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResGroupPolicyListResp.ProtoReflect.Descriptor instead.
func (*ResGroupPolicyListResp) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{6}
}

func (x *ResGroupPolicyListResp) GetList() []*ResGroupPolicyShort {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ResGroupPolicyListResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ResGroupPolicyShort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Desc string `protobuf:"bytes,3,opt,name=desc,proto3" form:"desc" json:"desc" query:"desc"`
}

func (x *ResGroupPolicyShort) Reset() {
	*x = ResGroupPolicyShort{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResGroupPolicyShort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResGroupPolicyShort) ProtoMessage() {}

func (x *ResGroupPolicyShort) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResGroupPolicyShort.ProtoReflect.Descriptor instead.
func (*ResGroupPolicyShort) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{7}
}

func (x *ResGroupPolicyShort) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResGroupPolicyShort) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResGroupPolicyShort) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type ResGroupPolicyCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// string id = 1;
	// string name = 2;
	// string desc = 3;
	ResourceType string `protobuf:"bytes,3,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	BindGroupId  string `protobuf:"bytes,4,opt,name=bind_group_id,json=bindGroupId,proto3" form:"bind_group_id" json:"bind_group_id" query:"bind_group_id"`
	Condition    string `protobuf:"bytes,5,opt,name=condition,proto3" form:"condition" json:"condition" query:"condition"`
}

func (x *ResGroupPolicyCreateReq) Reset() {
	*x = ResGroupPolicyCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResGroupPolicyCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResGroupPolicyCreateReq) ProtoMessage() {}

func (x *ResGroupPolicyCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResGroupPolicyCreateReq.ProtoReflect.Descriptor instead.
func (*ResGroupPolicyCreateReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{8}
}

func (x *ResGroupPolicyCreateReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ResGroupPolicyCreateReq) GetBindGroupId() string {
	if x != nil {
		return x.BindGroupId
	}
	return ""
}

func (x *ResGroupPolicyCreateReq) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

type GroupPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Related string             `protobuf:"bytes,1,opt,name=related,proto3" form:"related" json:"related" query:"related"` // 条件间关系
	List    []*GroupPolicyInfo `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *GroupPolicy) Reset() {
	*x = GroupPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupPolicy) ProtoMessage() {}

func (x *GroupPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupPolicy.ProtoReflect.Descriptor instead.
func (*GroupPolicy) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{9}
}

func (x *GroupPolicy) GetRelated() string {
	if x != nil {
		return x.Related
	}
	return ""
}

func (x *GroupPolicy) GetList() []*GroupPolicyInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type GroupPolicyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key     string `protobuf:"bytes,1,opt,name=key,proto3" form:"key" json:"key" query:"key"`
	Related string `protobuf:"bytes,2,opt,name=related,proto3" form:"related" json:"related" query:"related"`
	Value   string `protobuf:"bytes,3,opt,name=value,proto3" form:"value" json:"value" query:"value"`
}

func (x *GroupPolicyInfo) Reset() {
	*x = GroupPolicyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupPolicyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupPolicyInfo) ProtoMessage() {}

func (x *GroupPolicyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupPolicyInfo.ProtoReflect.Descriptor instead.
func (*GroupPolicyInfo) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{10}
}

func (x *GroupPolicyInfo) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *GroupPolicyInfo) GetRelated() string {
	if x != nil {
		return x.Related
	}
	return ""
}

func (x *GroupPolicyInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ResGroupPolicyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	BindGroupName string `protobuf:"bytes,2,opt,name=bind_group_name,json=bindGroupName,proto3" form:"bind_group_name" json:"bind_group_name" query:"bind_group_name"`
	BindGroupDesc string `protobuf:"bytes,3,opt,name=bind_group_desc,json=bindGroupDesc,proto3" form:"bind_group_desc" json:"bind_group_desc" query:"bind_group_desc"`
	BindGroupId   string `protobuf:"bytes,4,opt,name=bind_group_id,json=bindGroupId,proto3" form:"bind_group_id" json:"bind_group_id" query:"bind_group_id"`
	// groupPolicy group_conditions = 5;
	ResourceType string `protobuf:"bytes,5,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	Condition    string `protobuf:"bytes,6,opt,name=condition,proto3" form:"condition" json:"condition" query:"condition"`
}

func (x *ResGroupPolicyInfo) Reset() {
	*x = ResGroupPolicyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResGroupPolicyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResGroupPolicyInfo) ProtoMessage() {}

func (x *ResGroupPolicyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResGroupPolicyInfo.ProtoReflect.Descriptor instead.
func (*ResGroupPolicyInfo) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{11}
}

func (x *ResGroupPolicyInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResGroupPolicyInfo) GetBindGroupName() string {
	if x != nil {
		return x.BindGroupName
	}
	return ""
}

func (x *ResGroupPolicyInfo) GetBindGroupDesc() string {
	if x != nil {
		return x.BindGroupDesc
	}
	return ""
}

func (x *ResGroupPolicyInfo) GetBindGroupId() string {
	if x != nil {
		return x.BindGroupId
	}
	return ""
}

func (x *ResGroupPolicyInfo) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ResGroupPolicyInfo) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

type FindResReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page           uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size           uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	ResourceType   string   `protobuf:"bytes,3,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	BindResGroupId string   `protobuf:"bytes,4,opt,name=bind_res_group_id,json=bindResGroupId,proto3" form:"bind_res_group_id" json:"bind_res_group_id" query:"bind_res_group_id"`
	Fields         []string `protobuf:"bytes,5,rep,name=fields,proto3" form:"fields" json:"fields" query:"fields"`
	Condition      string   `protobuf:"bytes,6,opt,name=condition,proto3" form:"condition" json:"condition" query:"condition"`
	Ordering       []string `protobuf:"bytes,7,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
}

func (x *FindResReq) Reset() {
	*x = FindResReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindResReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindResReq) ProtoMessage() {}

func (x *FindResReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindResReq.ProtoReflect.Descriptor instead.
func (*FindResReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{12}
}

func (x *FindResReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FindResReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FindResReq) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *FindResReq) GetBindResGroupId() string {
	if x != nil {
		return x.BindResGroupId
	}
	return ""
}

func (x *FindResReq) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *FindResReq) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *FindResReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

type CountResReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BindResGroupId string `protobuf:"bytes,1,opt,name=bind_res_group_id,json=bindResGroupId,proto3" form:"bind_res_group_id" json:"bind_res_group_id" query:"bind_res_group_id"`
}

func (x *CountResReq) Reset() {
	*x = CountResReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountResReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountResReq) ProtoMessage() {}

func (x *CountResReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountResReq.ProtoReflect.Descriptor instead.
func (*CountResReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{13}
}

func (x *CountResReq) GetBindResGroupId() string {
	if x != nil {
		return x.BindResGroupId
	}
	return ""
}

type CountResResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostTotal  uint32 `protobuf:"varint,1,opt,name=host_total,json=hostTotal,proto3" form:"host_total" json:"host_total" query:"host_total"`
	MysqlTotal uint32 `protobuf:"varint,2,opt,name=mysql_total,json=mysqlTotal,proto3" form:"mysql_total" json:"mysql_total" query:"mysql_total"`
	RedisTotal uint32 `protobuf:"varint,3,opt,name=redis_total,json=redisTotal,proto3" form:"redis_total" json:"redis_total" query:"redis_total"`
}

func (x *CountResResp) Reset() {
	*x = CountResResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountResResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountResResp) ProtoMessage() {}

func (x *CountResResp) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountResResp.ProtoReflect.Descriptor instead.
func (*CountResResp) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{14}
}

func (x *CountResResp) GetHostTotal() uint32 {
	if x != nil {
		return x.HostTotal
	}
	return 0
}

func (x *CountResResp) GetMysqlTotal() uint32 {
	if x != nil {
		return x.MysqlTotal
	}
	return 0
}

func (x *CountResResp) GetRedisTotal() uint32 {
	if x != nil {
		return x.RedisTotal
	}
	return 0
}

type FindResResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostList  []*HostResDetail      `protobuf:"bytes,1,rep,name=host_list,json=hostList,proto3" form:"host_list" json:"host_list" query:"host_list"`
	MysqlList []*MysqlClusterDetail `protobuf:"bytes,2,rep,name=mysql_list,json=mysqlList,proto3" form:"mysql_list" json:"mysql_list" query:"mysql_list"`
	RedisList []*RedisDetail        `protobuf:"bytes,3,rep,name=redis_list,json=redisList,proto3" form:"redis_list" json:"redis_list" query:"redis_list"`
	Total     uint32                `protobuf:"varint,4,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *FindResResp) Reset() {
	*x = FindResResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindResResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindResResp) ProtoMessage() {}

func (x *FindResResp) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindResResp.ProtoReflect.Descriptor instead.
func (*FindResResp) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{15}
}

func (x *FindResResp) GetHostList() []*HostResDetail {
	if x != nil {
		return x.HostList
	}
	return nil
}

func (x *FindResResp) GetMysqlList() []*MysqlClusterDetail {
	if x != nil {
		return x.MysqlList
	}
	return nil
}

func (x *FindResResp) GetRedisList() []*RedisDetail {
	if x != nil {
		return x.RedisList
	}
	return nil
}

func (x *FindResResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ListCloudReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IspId    string `protobuf:"bytes,1,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	RegionId string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *ListCloudReq) Reset() {
	*x = ListCloudReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCloudReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCloudReq) ProtoMessage() {}

func (x *ListCloudReq) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCloudReq.ProtoReflect.Descriptor instead.
func (*ListCloudReq) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{16}
}

func (x *ListCloudReq) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *ListCloudReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type ListCloudResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*ListCloudData `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total int32            `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *ListCloudResp) Reset() {
	*x = ListCloudResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCloudResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCloudResp) ProtoMessage() {}

func (x *ListCloudResp) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCloudResp.ProtoReflect.Descriptor instead.
func (*ListCloudResp) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{17}
}

func (x *ListCloudResp) GetList() []*ListCloudData {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListCloudResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ListCloudData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" form:"display_name" json:"display_name" query:"display_name"`
	Id          string `protobuf:"bytes,2,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name        string `protobuf:"bytes,3,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *ListCloudData) Reset() {
	*x = ListCloudData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_resourceGroup_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCloudData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCloudData) ProtoMessage() {}

func (x *ListCloudData) ProtoReflect() protoreflect.Message {
	mi := &file_resourceGroup_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCloudData.ProtoReflect.Descriptor instead.
func (*ListCloudData) Descriptor() ([]byte, []int) {
	return file_resourceGroup_proto_rawDescGZIP(), []int{18}
}

func (x *ListCloudData) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ListCloudData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ListCloudData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_resourceGroup_proto protoreflect.FileDescriptor

var file_resourceGroup_proto_rawDesc = []byte{
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a,
	0x11, 0x72, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x01,
	0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x4e, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x22, 0x50, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x43, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x8c, 0x01, 0x0a, 0x16, 0x72,
	0x65, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x49, 0x0a, 0x12, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x61, 0x0a, 0x16, 0x72, 0x65, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x4d, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x80, 0x01, 0x0a, 0x17, 0x72, 0x65, 0x73, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x62, 0x69, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x56, 0x0a, 0x0b, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x53, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xdb, 0x01, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x62, 0x69, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x73, 0x63, 0x12, 0x22, 0x0a,
	0x0d, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x69, 0x6e, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd6, 0x01, 0x0a, 0x0a, 0x66, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x29, 0x0a, 0x11, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x69, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x38, 0x0a,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x11,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x68, 0x6f, 0x73,
	0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x6d, 0x79, 0x73,
	0x71, 0x6c, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x72, 0x65,
	0x64, 0x69, 0x73, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xcc, 0x01, 0x0a, 0x0b, 0x66, 0x69, 0x6e,
	0x64, 0x52, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x34, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3b,
	0x0a, 0x0a, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x79,
	0x73, 0x71, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x09, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x0a, 0x72,
	0x65, 0x64, 0x69, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x09, 0x72, 0x65, 0x64, 0x69, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x42, 0x0a, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x52, 0x0a, 0x0d, 0x6c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x56, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67,
	0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71,
	0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_resourceGroup_proto_rawDescOnce sync.Once
	file_resourceGroup_proto_rawDescData = file_resourceGroup_proto_rawDesc
)

func file_resourceGroup_proto_rawDescGZIP() []byte {
	file_resourceGroup_proto_rawDescOnce.Do(func() {
		file_resourceGroup_proto_rawDescData = protoimpl.X.CompressGZIP(file_resourceGroup_proto_rawDescData)
	})
	return file_resourceGroup_proto_rawDescData
}

var file_resourceGroup_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_resourceGroup_proto_goTypes = []interface{}{
	(*GroupQueryReq)(nil),           // 0: cloudman.groupQueryReq
	(*GroupListResp)(nil),           // 1: cloudman.groupListResp
	(*GroupCreateReq)(nil),          // 2: cloudman.groupCreateReq
	(*GroupInfo)(nil),               // 3: cloudman.groupInfo
	(*ResGroupPolicyQueryReq)(nil),  // 4: cloudman.resGroupPolicyQueryReq
	(*GroupPolicyInfoReq)(nil),      // 5: cloudman.groupPolicyInfoReq
	(*ResGroupPolicyListResp)(nil),  // 6: cloudman.resGroupPolicyListResp
	(*ResGroupPolicyShort)(nil),     // 7: cloudman.resGroupPolicyShort
	(*ResGroupPolicyCreateReq)(nil), // 8: cloudman.resGroupPolicyCreateReq
	(*GroupPolicy)(nil),             // 9: cloudman.groupPolicy
	(*GroupPolicyInfo)(nil),         // 10: cloudman.groupPolicyInfo
	(*ResGroupPolicyInfo)(nil),      // 11: cloudman.resGroupPolicyInfo
	(*FindResReq)(nil),              // 12: cloudman.findResReq
	(*CountResReq)(nil),             // 13: cloudman.countResReq
	(*CountResResp)(nil),            // 14: cloudman.countResResp
	(*FindResResp)(nil),             // 15: cloudman.findResResp
	(*ListCloudReq)(nil),            // 16: cloudman.listCloudReq
	(*ListCloudResp)(nil),           // 17: cloudman.listCloudResp
	(*ListCloudData)(nil),           // 18: cloudman.listCloudData
	(*HostResDetail)(nil),           // 19: cloudman.HostResDetail
	(*MysqlClusterDetail)(nil),      // 20: cloudman.MysqlClusterDetail
	(*RedisDetail)(nil),             // 21: cloudman.RedisDetail
}
var file_resourceGroup_proto_depIdxs = []int32{
	3,  // 0: cloudman.groupListResp.list:type_name -> cloudman.groupInfo
	7,  // 1: cloudman.resGroupPolicyListResp.list:type_name -> cloudman.resGroupPolicyShort
	10, // 2: cloudman.groupPolicy.list:type_name -> cloudman.groupPolicyInfo
	19, // 3: cloudman.findResResp.host_list:type_name -> cloudman.HostResDetail
	20, // 4: cloudman.findResResp.mysql_list:type_name -> cloudman.MysqlClusterDetail
	21, // 5: cloudman.findResResp.redis_list:type_name -> cloudman.RedisDetail
	18, // 6: cloudman.listCloudResp.list:type_name -> cloudman.listCloudData
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_resourceGroup_proto_init() }
func file_resourceGroup_proto_init() {
	if File_resourceGroup_proto != nil {
		return
	}
	file_resTemplate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_resourceGroup_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResGroupPolicyQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupPolicyInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResGroupPolicyListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResGroupPolicyShort); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResGroupPolicyCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupPolicyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResGroupPolicyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindResReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountResReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountResResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindResResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCloudReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCloudResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_resourceGroup_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCloudData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_resourceGroup_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_resourceGroup_proto_goTypes,
		DependencyIndexes: file_resourceGroup_proto_depIdxs,
		MessageInfos:      file_resourceGroup_proto_msgTypes,
	}.Build()
	File_resourceGroup_proto = out.File
	file_resourceGroup_proto_rawDesc = nil
	file_resourceGroup_proto_goTypes = nil
	file_resourceGroup_proto_depIdxs = nil
}
