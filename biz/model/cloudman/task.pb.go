// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: task.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size     uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Status   string   `protobuf:"bytes,5,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Name     string   `protobuf:"bytes,6,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *TaskListReq) Reset() {
	*x = TaskListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskListReq) ProtoMessage() {}

func (x *TaskListReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskListReq.ProtoReflect.Descriptor instead.
func (*TaskListReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{0}
}

func (x *TaskListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TaskListReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *TaskListReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *TaskListReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *TaskListReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TaskListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32        `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*TaskDetail `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *TaskListResp) Reset() {
	*x = TaskListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskListResp) ProtoMessage() {}

func (x *TaskListResp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskListResp.ProtoReflect.Descriptor instead.
func (*TaskListResp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{1}
}

func (x *TaskListResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *TaskListResp) GetList() []*TaskDetail {
	if x != nil {
		return x.List
	}
	return nil
}

type TaskDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountName        string `protobuf:"bytes,1,opt,name=account_name,json=accountName,proto3" form:"account_name" json:"account_name" query:"account_name"`
	Status             int32  `protobuf:"varint,2,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Name               string `protobuf:"bytes,3,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	AccountId          string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	BindZoneId         string `protobuf:"bytes,5,opt,name=bind_zone_id,json=bindZoneId,proto3" form:"bind_zone_id" json:"bind_zone_id" query:"bind_zone_id"`
	BindZoneName       string `protobuf:"bytes,6,opt,name=bind_zone_name,json=bindZoneName,proto3" form:"bind_zone_name" json:"bind_zone_name" query:"bind_zone_name"`
	BindZoneSourceId   string `protobuf:"bytes,18,opt,name=bind_zone_source_id,json=bindZoneSourceId,proto3" form:"bind_zone_source_id" json:"bind_zone_source_id" query:"bind_zone_source_id"`
	TaskType           string `protobuf:"bytes,7,opt,name=task_type,json=taskType,proto3" form:"task_type" json:"task_type" query:"task_type"`
	Policy             uint32 `protobuf:"varint,8,opt,name=policy,proto3" form:"policy" json:"policy" query:"policy"`
	Rate               uint32 `protobuf:"varint,9,opt,name=rate,proto3" form:"rate" json:"rate" query:"rate"`
	Timeout            uint32 `protobuf:"varint,10,opt,name=timeout,proto3" form:"timeout" json:"timeout" query:"timeout"`
	LatestRunId        string `protobuf:"bytes,11,opt,name=latest_run_id,json=latestRunId,proto3" form:"latest_run_id" json:"latest_run_id" query:"latest_run_id"`
	LatestRunStatus    int32  `protobuf:"varint,12,opt,name=latest_run_status,json=latestRunStatus,proto3" form:"latest_run_status" json:"latest_run_status" query:"latest_run_status"`
	LatestRunStartTime int64  `protobuf:"varint,13,opt,name=latest_run_start_time,json=latestRunStartTime,proto3" form:"latest_run_start_time" json:"latest_run_start_time" query:"latest_run_start_time"`
	LatestRunEndTime   int64  `protobuf:"varint,14,opt,name=latest_run_end_time,json=latestRunEndTime,proto3" form:"latest_run_end_time" json:"latest_run_end_time" query:"latest_run_end_time"`
	Id                 string `protobuf:"bytes,15,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Retry              uint32 `protobuf:"varint,16,opt,name=retry,proto3" form:"retry" json:"retry" query:"retry"`
	AccountType        string `protobuf:"bytes,17,opt,name=account_type,json=accountType,proto3" form:"account_type" json:"account_type" query:"account_type"`
}

func (x *TaskDetail) Reset() {
	*x = TaskDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDetail) ProtoMessage() {}

func (x *TaskDetail) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDetail.ProtoReflect.Descriptor instead.
func (*TaskDetail) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{2}
}

func (x *TaskDetail) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *TaskDetail) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TaskDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskDetail) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *TaskDetail) GetBindZoneId() string {
	if x != nil {
		return x.BindZoneId
	}
	return ""
}

func (x *TaskDetail) GetBindZoneName() string {
	if x != nil {
		return x.BindZoneName
	}
	return ""
}

func (x *TaskDetail) GetBindZoneSourceId() string {
	if x != nil {
		return x.BindZoneSourceId
	}
	return ""
}

func (x *TaskDetail) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *TaskDetail) GetPolicy() uint32 {
	if x != nil {
		return x.Policy
	}
	return 0
}

func (x *TaskDetail) GetRate() uint32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *TaskDetail) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *TaskDetail) GetLatestRunId() string {
	if x != nil {
		return x.LatestRunId
	}
	return ""
}

func (x *TaskDetail) GetLatestRunStatus() int32 {
	if x != nil {
		return x.LatestRunStatus
	}
	return 0
}

func (x *TaskDetail) GetLatestRunStartTime() int64 {
	if x != nil {
		return x.LatestRunStartTime
	}
	return 0
}

func (x *TaskDetail) GetLatestRunEndTime() int64 {
	if x != nil {
		return x.LatestRunEndTime
	}
	return 0
}

func (x *TaskDetail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TaskDetail) GetRetry() uint32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *TaskDetail) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

type CreateTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     int32  `protobuf:"varint,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	AccountId  string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	BindZoneId string `protobuf:"bytes,4,opt,name=bind_zone_id,json=bindZoneId,proto3" form:"bind_zone_id" json:"bind_zone_id" query:"bind_zone_id"`
	TaskType   string `protobuf:"bytes,5,opt,name=task_type,json=taskType,proto3" form:"task_type" json:"task_type" query:"task_type"`
	Policy     uint32 `protobuf:"varint,6,opt,name=policy,proto3" form:"policy" json:"policy" query:"policy"`
	Rate       uint32 `protobuf:"varint,7,opt,name=rate,proto3" form:"rate" json:"rate" query:"rate"`
	Timeout    uint32 `protobuf:"varint,8,opt,name=timeout,proto3" form:"timeout" json:"timeout" query:"timeout"`
	Retry      uint32 `protobuf:"varint,9,opt,name=retry,proto3" form:"retry" json:"retry" query:"retry"`
}

func (x *CreateTaskReq) Reset() {
	*x = CreateTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskReq) ProtoMessage() {}

func (x *CreateTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskReq.ProtoReflect.Descriptor instead.
func (*CreateTaskReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{3}
}

func (x *CreateTaskReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreateTaskReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTaskReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CreateTaskReq) GetBindZoneId() string {
	if x != nil {
		return x.BindZoneId
	}
	return ""
}

func (x *CreateTaskReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *CreateTaskReq) GetPolicy() uint32 {
	if x != nil {
		return x.Policy
	}
	return 0
}

func (x *CreateTaskReq) GetRate() uint32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *CreateTaskReq) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *CreateTaskReq) GetRetry() uint32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

type UpdateTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id" path:"id"`
	Status     int32  `protobuf:"varint,2,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Name       string `protobuf:"bytes,3,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	AccountId  string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	BindZoneId string `protobuf:"bytes,5,opt,name=bind_zone_id,json=bindZoneId,proto3" form:"bind_zone_id" json:"bind_zone_id" query:"bind_zone_id"`
	TaskType   string `protobuf:"bytes,6,opt,name=task_type,json=taskType,proto3" form:"task_type" json:"task_type" query:"task_type"`
	Policy     uint32 `protobuf:"varint,7,opt,name=policy,proto3" form:"policy" json:"policy" query:"policy"`
	Rate       uint32 `protobuf:"varint,8,opt,name=rate,proto3" form:"rate" json:"rate" query:"rate"`
	Timeout    uint32 `protobuf:"varint,9,opt,name=timeout,proto3" form:"timeout" json:"timeout" query:"timeout"`
	Retry      uint32 `protobuf:"varint,10,opt,name=retry,proto3" form:"retry" json:"retry" query:"retry"`
}

func (x *UpdateTaskReq) Reset() {
	*x = UpdateTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskReq) ProtoMessage() {}

func (x *UpdateTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskReq.ProtoReflect.Descriptor instead.
func (*UpdateTaskReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateTaskReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateTaskReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UpdateTaskReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateTaskReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *UpdateTaskReq) GetBindZoneId() string {
	if x != nil {
		return x.BindZoneId
	}
	return ""
}

func (x *UpdateTaskReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *UpdateTaskReq) GetPolicy() uint32 {
	if x != nil {
		return x.Policy
	}
	return 0
}

func (x *UpdateTaskReq) GetRate() uint32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *UpdateTaskReq) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *UpdateTaskReq) GetRetry() uint32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

type RunTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Oid string `protobuf:"bytes,1,opt,name=oid,proto3" json:"oid" path:"id"`
}

func (x *RunTaskReq) Reset() {
	*x = RunTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunTaskReq) ProtoMessage() {}

func (x *RunTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunTaskReq.ProtoReflect.Descriptor instead.
func (*RunTaskReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{5}
}

func (x *RunTaskReq) GetOid() string {
	if x != nil {
		return x.Oid
	}
	return ""
}

var File_task_proto protoreflect.FileDescriptor

var file_task_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x99, 0x01, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4e, 0x0a,
	0x0c, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xcf, 0x04,
	0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x69,
	0x6e, 0x64, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x13, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x62, 0x69, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x72,
	0x75, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x72,
	0x75, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x12, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x13, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xf5, 0x01, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x22, 0x8d, 0x02, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x5a, 0x6f, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x22, 0x26, 0x0a, 0x0a, 0x72, 0x75, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x03, 0x6f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18, 0x02, 0x69, 0x64, 0x52, 0x03, 0x6f, 0x69, 0x64, 0x42,
	0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79,
	0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70,
	0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_task_proto_rawDescOnce sync.Once
	file_task_proto_rawDescData = file_task_proto_rawDesc
)

func file_task_proto_rawDescGZIP() []byte {
	file_task_proto_rawDescOnce.Do(func() {
		file_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_task_proto_rawDescData)
	})
	return file_task_proto_rawDescData
}

var file_task_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_task_proto_goTypes = []interface{}{
	(*TaskListReq)(nil),   // 0: cloudman.taskListReq
	(*TaskListResp)(nil),  // 1: cloudman.taskListResp
	(*TaskDetail)(nil),    // 2: cloudman.taskDetail
	(*CreateTaskReq)(nil), // 3: cloudman.createTaskReq
	(*UpdateTaskReq)(nil), // 4: cloudman.updateTaskReq
	(*RunTaskReq)(nil),    // 5: cloudman.runTaskReq
}
var file_task_proto_depIdxs = []int32{
	2, // 0: cloudman.taskListResp.list:type_name -> cloudman.taskDetail
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_task_proto_init() }
func file_task_proto_init() {
	if File_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_proto_goTypes,
		DependencyIndexes: file_task_proto_depIdxs,
		MessageInfos:      file_task_proto_msgTypes,
	}.Build()
	File_task_proto = out.File
	file_task_proto_rawDesc = nil
	file_task_proto_goTypes = nil
	file_task_proto_depIdxs = nil
}
