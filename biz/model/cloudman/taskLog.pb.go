// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: taskLog.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskLogQueryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size     uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Name     string   `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *TaskLogQueryParams) Reset() {
	*x = TaskLogQueryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogQueryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogQueryParams) ProtoMessage() {}

func (x *TaskLogQueryParams) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogQueryParams.ProtoReflect.Descriptor instead.
func (*TaskLogQueryParams) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{0}
}

func (x *TaskLogQueryParams) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TaskLogQueryParams) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *TaskLogQueryParams) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *TaskLogQueryParams) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *TaskLogQueryParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type TaskLogListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total uint32           `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*TaskLogDetail `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *TaskLogListResponse) Reset() {
	*x = TaskLogListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogListResponse) ProtoMessage() {}

func (x *TaskLogListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogListResponse.ProtoReflect.Descriptor instead.
func (*TaskLogListResponse) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{1}
}

func (x *TaskLogListResponse) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *TaskLogListResponse) GetList() []*TaskLogDetail {
	if x != nil {
		return x.List
	}
	return nil
}

type TaskLogDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	SyncTaskId     string `protobuf:"bytes,2,opt,name=sync_task_id,json=syncTaskId,proto3" form:"sync_task_id" json:"sync_task_id" query:"sync_task_id"`
	AccountId      string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
	AccountName    string `protobuf:"bytes,4,opt,name=account_name,json=accountName,proto3" form:"account_name" json:"account_name" query:"account_name"`
	BindRegionId   string `protobuf:"bytes,5,opt,name=bind_region_id,json=bindRegionId,proto3" form:"bind_region_id" json:"bind_region_id" query:"bind_region_id"`
	BindRegionName string `protobuf:"bytes,6,opt,name=bind_region_name,json=bindRegionName,proto3" form:"bind_region_name" json:"bind_region_name" query:"bind_region_name"`
	TaskType       string `protobuf:"bytes,7,opt,name=task_type,json=taskType,proto3" form:"task_type" json:"task_type" query:"task_type"`
	Status         uint32 `protobuf:"varint,8,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	EndTime        int64  `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" form:"end_time" json:"end_time" query:"end_time"`
	CreatedTime    int64  `protobuf:"varint,10,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	Id             string `protobuf:"bytes,11,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	AccountType    string `protobuf:"bytes,12,opt,name=account_type,json=accountType,proto3" form:"account_type" json:"account_type" query:"account_type"`
	CreateUser     string `protobuf:"bytes,13,opt,name=create_user,json=createUser,proto3" form:"create_user" json:"create_user" query:"create_user"`
}

func (x *TaskLogDetail) Reset() {
	*x = TaskLogDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogDetail) ProtoMessage() {}

func (x *TaskLogDetail) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogDetail.ProtoReflect.Descriptor instead.
func (*TaskLogDetail) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{2}
}

func (x *TaskLogDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskLogDetail) GetSyncTaskId() string {
	if x != nil {
		return x.SyncTaskId
	}
	return ""
}

func (x *TaskLogDetail) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *TaskLogDetail) GetAccountName() string {
	if x != nil {
		return x.AccountName
	}
	return ""
}

func (x *TaskLogDetail) GetBindRegionId() string {
	if x != nil {
		return x.BindRegionId
	}
	return ""
}

func (x *TaskLogDetail) GetBindRegionName() string {
	if x != nil {
		return x.BindRegionName
	}
	return ""
}

func (x *TaskLogDetail) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *TaskLogDetail) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TaskLogDetail) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *TaskLogDetail) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *TaskLogDetail) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TaskLogDetail) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *TaskLogDetail) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

type OrderTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64      `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64      `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering    []string    `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords    string      `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Name        string      `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Isp         string      `protobuf:"bytes,6,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	Type        string      `protobuf:"bytes,7,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	Status      string      `protobuf:"bytes,8,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	CreatedTime *TimeFilter `protobuf:"bytes,9,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	Action      string      `protobuf:"bytes,10,opt,name=action,proto3" form:"action" json:"action" query:"action"`
	RegionId    string      `protobuf:"bytes,11,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	SearchKey   string      `protobuf:"bytes,12,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue string      `protobuf:"bytes,13,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
}

func (x *OrderTaskReq) Reset() {
	*x = OrderTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderTaskReq) ProtoMessage() {}

func (x *OrderTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderTaskReq.ProtoReflect.Descriptor instead.
func (*OrderTaskReq) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{3}
}

func (x *OrderTaskReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *OrderTaskReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *OrderTaskReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *OrderTaskReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *OrderTaskReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderTaskReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *OrderTaskReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OrderTaskReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *OrderTaskReq) GetCreatedTime() *TimeFilter {
	if x != nil {
		return x.CreatedTime
	}
	return nil
}

func (x *OrderTaskReq) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *OrderTaskReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *OrderTaskReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *OrderTaskReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

type OrderTaskListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                  `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*OrderTaskListResult `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *OrderTaskListResp) Reset() {
	*x = OrderTaskListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderTaskListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderTaskListResp) ProtoMessage() {}

func (x *OrderTaskListResp) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderTaskListResp.ProtoReflect.Descriptor instead.
func (*OrderTaskListResp) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{4}
}

func (x *OrderTaskListResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *OrderTaskListResp) GetList() []*OrderTaskListResult {
	if x != nil {
		return x.List
	}
	return nil
}

type OrderTaskListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	CreatedTime  int64  `protobuf:"varint,2,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	CreateUser   string `protobuf:"bytes,3,opt,name=create_user,json=createUser,proto3" form:"create_user" json:"create_user" query:"create_user"`
	Status       int32  `protobuf:"varint,4,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	IspName      string `protobuf:"bytes,5,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
	Type         string `protobuf:"bytes,6,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	Action       string `protobuf:"bytes,7,opt,name=action,proto3" form:"action" json:"action" query:"action"`
	BeforeInfo   string `protobuf:"bytes,8,opt,name=before_info,json=beforeInfo,proto3" form:"before_info" json:"before_info" query:"before_info"`
	AfterInfo    string `protobuf:"bytes,9,opt,name=after_info,json=afterInfo,proto3" form:"after_info" json:"after_info" query:"after_info"`
	ErrorMsg     string `protobuf:"bytes,10,opt,name=error_msg,json=errorMsg,proto3" form:"error_msg" json:"error_msg" query:"error_msg"`
	IspType      string `protobuf:"bytes,11,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	RegionName   string `protobuf:"bytes,12,opt,name=region_name,json=regionName,proto3" form:"region_name" json:"region_name" query:"region_name"`
	RegionId     string `protobuf:"bytes,13,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	StartTime    int64  `protobuf:"varint,14,opt,name=start_time,json=startTime,proto3" form:"start_time" json:"start_time" query:"start_time"`
	OverTime     int64  `protobuf:"varint,15,opt,name=over_time,json=overTime,proto3" form:"over_time" json:"over_time" query:"over_time"`
	RawData      string `protobuf:"bytes,16,opt,name=raw_data,json=rawData,proto3" form:"raw_data" json:"raw_data" query:"raw_data"`
	Reason       string `protobuf:"bytes,17,opt,name=reason,proto3" form:"reason" json:"reason" query:"reason"`
	StatusDetail string `protobuf:"bytes,18,opt,name=status_detail,json=statusDetail,proto3" form:"status_detail" json:"status_detail" query:"status_detail"`
	TicketUrl    string `protobuf:"bytes,19,opt,name=ticket_url,json=ticketUrl,proto3" form:"ticket_url" json:"ticket_url" query:"ticket_url"`
	IspId        string `protobuf:"bytes,20,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	TemplateId   string `protobuf:"bytes,21,opt,name=template_id,json=templateId,proto3" form:"template_id" json:"template_id" query:"template_id"`
	TemplateName string `protobuf:"bytes,22,opt,name=template_name,json=templateName,proto3" form:"template_name" json:"template_name" query:"template_name"`
}

func (x *OrderTaskListResult) Reset() {
	*x = OrderTaskListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderTaskListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderTaskListResult) ProtoMessage() {}

func (x *OrderTaskListResult) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderTaskListResult.ProtoReflect.Descriptor instead.
func (*OrderTaskListResult) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{5}
}

func (x *OrderTaskListResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrderTaskListResult) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *OrderTaskListResult) GetCreateUser() string {
	if x != nil {
		return x.CreateUser
	}
	return ""
}

func (x *OrderTaskListResult) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *OrderTaskListResult) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

func (x *OrderTaskListResult) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OrderTaskListResult) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *OrderTaskListResult) GetBeforeInfo() string {
	if x != nil {
		return x.BeforeInfo
	}
	return ""
}

func (x *OrderTaskListResult) GetAfterInfo() string {
	if x != nil {
		return x.AfterInfo
	}
	return ""
}

func (x *OrderTaskListResult) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

func (x *OrderTaskListResult) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *OrderTaskListResult) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *OrderTaskListResult) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *OrderTaskListResult) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *OrderTaskListResult) GetOverTime() int64 {
	if x != nil {
		return x.OverTime
	}
	return 0
}

func (x *OrderTaskListResult) GetRawData() string {
	if x != nil {
		return x.RawData
	}
	return ""
}

func (x *OrderTaskListResult) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *OrderTaskListResult) GetStatusDetail() string {
	if x != nil {
		return x.StatusDetail
	}
	return ""
}

func (x *OrderTaskListResult) GetTicketUrl() string {
	if x != nil {
		return x.TicketUrl
	}
	return ""
}

func (x *OrderTaskListResult) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *OrderTaskListResult) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *OrderTaskListResult) GetTemplateName() string {
	if x != nil {
		return x.TemplateName
	}
	return ""
}

type OrderLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start uint64 `protobuf:"varint,1,opt,name=start,proto3" form:"start" json:"start" query:"start"`
	Id    string `protobuf:"bytes,2,opt,name=id,proto3" json:"id" path:"id"`
}

func (x *OrderLogReq) Reset() {
	*x = OrderLogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderLogReq) ProtoMessage() {}

func (x *OrderLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderLogReq.ProtoReflect.Descriptor instead.
func (*OrderLogReq) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{6}
}

func (x *OrderLogReq) GetStart() uint64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *OrderLogReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type TaskLogResultList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*TaskLogResult `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *TaskLogResultList) Reset() {
	*x = TaskLogResultList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogResultList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogResultList) ProtoMessage() {}

func (x *TaskLogResultList) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogResultList.ProtoReflect.Descriptor instead.
func (*TaskLogResultList) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{7}
}

func (x *TaskLogResultList) GetList() []*TaskLogResult {
	if x != nil {
		return x.List
	}
	return nil
}

type TaskLogResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreatedTime int64  `protobuf:"varint,1,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	TaskLogId   string `protobuf:"bytes,2,opt,name=task_log_id,json=taskLogId,proto3" form:"task_log_id" json:"task_log_id" query:"task_log_id"`
	Stopped     bool   `protobuf:"varint,3,opt,name=stopped,proto3" form:"stopped" json:"stopped" query:"stopped"`
	TaskResult  string `protobuf:"bytes,4,opt,name=task_result,json=taskResult,proto3" form:"task_result" json:"task_result" query:"task_result"`
}

func (x *TaskLogResult) Reset() {
	*x = TaskLogResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_taskLog_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogResult) ProtoMessage() {}

func (x *TaskLogResult) ProtoReflect() protoreflect.Message {
	mi := &file_taskLog_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogResult.ProtoReflect.Descriptor instead.
func (*TaskLogResult) Descriptor() ([]byte, []int) {
	return file_taskLog_proto_rawDescGZIP(), []int{8}
}

func (x *TaskLogResult) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *TaskLogResult) GetTaskLogId() string {
	if x != nil {
		return x.TaskLogId
	}
	return ""
}

func (x *TaskLogResult) GetStopped() bool {
	if x != nil {
		return x.Stopped
	}
	return false
}

func (x *TaskLogResult) GetTaskResult() string {
	if x != nil {
		return x.TaskResult
	}
	return ""
}

var File_taskLog_proto protoreflect.FileDescriptor

var file_taskLog_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x09, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x88, 0x01, 0x0a, 0x12, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a,
	0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x58, 0x0a,
	0x13, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x9e, 0x03, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b,
	0x4c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0c, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x62, 0x69, 0x6e, 0x64, 0x5f,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x22, 0xf0, 0x02, 0x0a, 0x0c, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a,
	0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x73, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x69, 0x6d,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5c, 0x0a, 0x11, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8e, 0x05, 0x0a, 0x13, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x69, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x69, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x66, 0x74, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73,
	0x67, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x76, 0x65,
	0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x76,
	0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x73, 0x70, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3b, 0x0a, 0x0b, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12,
	0x16, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xd2, 0xbb, 0x18,
	0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x40, 0x0a, 0x11, 0x54, 0x61, 0x73, 0x6b, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x0d, 0x54, 0x61,
	0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x74, 0x6f, 0x70, 0x70, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61,
	0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_taskLog_proto_rawDescOnce sync.Once
	file_taskLog_proto_rawDescData = file_taskLog_proto_rawDesc
)

func file_taskLog_proto_rawDescGZIP() []byte {
	file_taskLog_proto_rawDescOnce.Do(func() {
		file_taskLog_proto_rawDescData = protoimpl.X.CompressGZIP(file_taskLog_proto_rawDescData)
	})
	return file_taskLog_proto_rawDescData
}

var file_taskLog_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_taskLog_proto_goTypes = []interface{}{
	(*TaskLogQueryParams)(nil),  // 0: cloudman.TaskLogQueryParams
	(*TaskLogListResponse)(nil), // 1: cloudman.TaskLogListResponse
	(*TaskLogDetail)(nil),       // 2: cloudman.TaskLogDetail
	(*OrderTaskReq)(nil),        // 3: cloudman.OrderTaskReq
	(*OrderTaskListResp)(nil),   // 4: cloudman.OrderTaskListResp
	(*OrderTaskListResult)(nil), // 5: cloudman.OrderTaskListResult
	(*OrderLogReq)(nil),         // 6: cloudman.OrderLogReq
	(*TaskLogResultList)(nil),   // 7: cloudman.TaskLogResultList
	(*TaskLogResult)(nil),       // 8: cloudman.TaskLogResult
	(*TimeFilter)(nil),          // 9: cloudman.timeFilter
}
var file_taskLog_proto_depIdxs = []int32{
	2, // 0: cloudman.TaskLogListResponse.list:type_name -> cloudman.TaskLogDetail
	9, // 1: cloudman.OrderTaskReq.created_time:type_name -> cloudman.timeFilter
	5, // 2: cloudman.OrderTaskListResp.list:type_name -> cloudman.OrderTaskListResult
	8, // 3: cloudman.TaskLogResultList.list:type_name -> cloudman.TaskLogResult
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_taskLog_proto_init() }
func file_taskLog_proto_init() {
	if File_taskLog_proto != nil {
		return
	}
	file_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_taskLog_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogQueryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderTaskListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderTaskListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderLogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogResultList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_taskLog_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_taskLog_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_taskLog_proto_goTypes,
		DependencyIndexes: file_taskLog_proto_depIdxs,
		MessageInfos:      file_taskLog_proto_msgTypes,
	}.Build()
	File_taskLog_proto = out.File
	file_taskLog_proto_rawDesc = nil
	file_taskLog_proto_goTypes = nil
	file_taskLog_proto_depIdxs = nil
}
