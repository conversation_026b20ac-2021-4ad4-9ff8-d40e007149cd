// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: cmdb.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CmdbQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Env      string   `protobuf:"bytes,1,opt,name=env,proto3" form:"env" json:"env" query:"env"`
	InstType string   `protobuf:"bytes,11,opt,name=inst_type,json=instType,proto3" form:"inst_type" json:"inst_type" query:"inst_type"`
	Conds    []string `protobuf:"bytes,12,rep,name=conds,proto3" form:"conds" json:"conds" query:"conds"`
	Limit    int32    `protobuf:"varint,21,opt,name=limit,proto3" form:"limit" json:"limit" query:"limit"`
	Start    int32    `protobuf:"varint,22,opt,name=start,proto3" form:"start" json:"start" query:"start"`
	Sort     string   `protobuf:"bytes,23,opt,name=sort,proto3" form:"sort" json:"sort" query:"sort"`
}

func (x *CmdbQueryRequest) Reset() {
	*x = CmdbQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbQueryRequest) ProtoMessage() {}

func (x *CmdbQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbQueryRequest.ProtoReflect.Descriptor instead.
func (*CmdbQueryRequest) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{0}
}

func (x *CmdbQueryRequest) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *CmdbQueryRequest) GetInstType() string {
	if x != nil {
		return x.InstType
	}
	return ""
}

func (x *CmdbQueryRequest) GetConds() []string {
	if x != nil {
		return x.Conds
	}
	return nil
}

func (x *CmdbQueryRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *CmdbQueryRequest) GetStart() int32 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *CmdbQueryRequest) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

type CmdbQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BkErrorCode int32    `protobuf:"varint,1,opt,name=bk_error_code,json=bkErrorCode,proto3" form:"bk_error_code" json:"bk_error_code" query:"bk_error_code"`
	BkErrorMsg  string   `protobuf:"bytes,2,opt,name=bk_error_msg,json=bkErrorMsg,proto3" form:"bk_error_msg" json:"bk_error_msg" query:"bk_error_msg"`
	Count       int32    `protobuf:"varint,11,opt,name=count,proto3" form:"count" json:"count" query:"count"`
	Info        []string `protobuf:"bytes,12,rep,name=info,proto3" form:"info" json:"info" query:"info"`
}

func (x *CmdbQueryResponse) Reset() {
	*x = CmdbQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbQueryResponse) ProtoMessage() {}

func (x *CmdbQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbQueryResponse.ProtoReflect.Descriptor instead.
func (*CmdbQueryResponse) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{1}
}

func (x *CmdbQueryResponse) GetBkErrorCode() int32 {
	if x != nil {
		return x.BkErrorCode
	}
	return 0
}

func (x *CmdbQueryResponse) GetBkErrorMsg() string {
	if x != nil {
		return x.BkErrorMsg
	}
	return ""
}

func (x *CmdbQueryResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *CmdbQueryResponse) GetInfo() []string {
	if x != nil {
		return x.Info
	}
	return nil
}

type CmdbQueryAssociateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Env  string `protobuf:"bytes,1,opt,name=env,proto3" form:"env" json:"env" query:"env"`
	Body string `protobuf:"bytes,11,opt,name=body,proto3" form:"body" json:"body" query:"body"`
}

func (x *CmdbQueryAssociateRequest) Reset() {
	*x = CmdbQueryAssociateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbQueryAssociateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbQueryAssociateRequest) ProtoMessage() {}

func (x *CmdbQueryAssociateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbQueryAssociateRequest.ProtoReflect.Descriptor instead.
func (*CmdbQueryAssociateRequest) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{2}
}

func (x *CmdbQueryAssociateRequest) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *CmdbQueryAssociateRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

type CmdbUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdateCount  int32    `protobuf:"varint,1,opt,name=update_count,json=updateCount,proto3" form:"update_count" json:"update_count" query:"update_count"`
	ResourceType string   `protobuf:"bytes,11,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	IspId        string   `protobuf:"bytes,12,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	RegionId     string   `protobuf:"bytes,13,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	ResourceId   []string `protobuf:"bytes,14,rep,name=resource_id,json=resourceId,proto3" form:"resource_id" json:"resource_id" query:"resource_id"`
	CustCond     string   `protobuf:"bytes,21,opt,name=cust_cond,json=custCond,proto3" form:"cust_cond" json:"cust_cond" query:"cust_cond"`
}

func (x *CmdbUpdateRequest) Reset() {
	*x = CmdbUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbUpdateRequest) ProtoMessage() {}

func (x *CmdbUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbUpdateRequest.ProtoReflect.Descriptor instead.
func (*CmdbUpdateRequest) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{3}
}

func (x *CmdbUpdateRequest) GetUpdateCount() int32 {
	if x != nil {
		return x.UpdateCount
	}
	return 0
}

func (x *CmdbUpdateRequest) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CmdbUpdateRequest) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *CmdbUpdateRequest) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *CmdbUpdateRequest) GetResourceId() []string {
	if x != nil {
		return x.ResourceId
	}
	return nil
}

func (x *CmdbUpdateRequest) GetCustCond() string {
	if x != nil {
		return x.CustCond
	}
	return ""
}

type CmdbUpdateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result string `protobuf:"bytes,1,opt,name=result,proto3" form:"result" json:"result" query:"result"`
}

func (x *CmdbUpdateResponse) Reset() {
	*x = CmdbUpdateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbUpdateResponse) ProtoMessage() {}

func (x *CmdbUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbUpdateResponse.ProtoReflect.Descriptor instead.
func (*CmdbUpdateResponse) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{4}
}

func (x *CmdbUpdateResponse) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

type CmdbDeleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Env      string  `protobuf:"bytes,1,opt,name=env,proto3" form:"env" json:"env" query:"env"`
	InstType string  `protobuf:"bytes,11,opt,name=inst_type,json=instType,proto3" form:"inst_type" json:"inst_type" query:"inst_type"`
	Ids      []int32 `protobuf:"varint,12,rep,packed,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
}

func (x *CmdbDeleteRequest) Reset() {
	*x = CmdbDeleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbDeleteRequest) ProtoMessage() {}

func (x *CmdbDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbDeleteRequest.ProtoReflect.Descriptor instead.
func (*CmdbDeleteRequest) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{5}
}

func (x *CmdbDeleteRequest) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *CmdbDeleteRequest) GetInstType() string {
	if x != nil {
		return x.InstType
	}
	return ""
}

func (x *CmdbDeleteRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type CmdbDeleteResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BkErrorCode int32  `protobuf:"varint,1,opt,name=bk_error_code,json=bkErrorCode,proto3" form:"bk_error_code" json:"bk_error_code" query:"bk_error_code"`
	BkErrorMsg  string `protobuf:"bytes,2,opt,name=bk_error_msg,json=bkErrorMsg,proto3" form:"bk_error_msg" json:"bk_error_msg" query:"bk_error_msg"`
}

func (x *CmdbDeleteResponse) Reset() {
	*x = CmdbDeleteResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbDeleteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbDeleteResponse) ProtoMessage() {}

func (x *CmdbDeleteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbDeleteResponse.ProtoReflect.Descriptor instead.
func (*CmdbDeleteResponse) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{6}
}

func (x *CmdbDeleteResponse) GetBkErrorCode() int32 {
	if x != nil {
		return x.BkErrorCode
	}
	return 0
}

func (x *CmdbDeleteResponse) GetBkErrorMsg() string {
	if x != nil {
		return x.BkErrorMsg
	}
	return ""
}

type CmdbPullInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType string   `protobuf:"bytes,1,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	RegionId     string   `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	Instances    []string `protobuf:"bytes,11,rep,name=instances,proto3" form:"instances" json:"instances" query:"instances"`
}

func (x *CmdbPullInfoRequest) Reset() {
	*x = CmdbPullInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbPullInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbPullInfoRequest) ProtoMessage() {}

func (x *CmdbPullInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbPullInfoRequest.ProtoReflect.Descriptor instead.
func (*CmdbPullInfoRequest) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{7}
}

func (x *CmdbPullInfoRequest) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CmdbPullInfoRequest) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *CmdbPullInfoRequest) GetInstances() []string {
	if x != nil {
		return x.Instances
	}
	return nil
}

type CmdbDiffRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType string   `protobuf:"bytes,1,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	Env          string   `protobuf:"bytes,2,opt,name=env,proto3" form:"env" json:"env" query:"env"`
	IspId        string   `protobuf:"bytes,11,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	RegionId     string   `protobuf:"bytes,12,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	Instances    []string `protobuf:"bytes,21,rep,name=instances,proto3" form:"instances" json:"instances" query:"instances"`
}

func (x *CmdbDiffRequest) Reset() {
	*x = CmdbDiffRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbDiffRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbDiffRequest) ProtoMessage() {}

func (x *CmdbDiffRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbDiffRequest.ProtoReflect.Descriptor instead.
func (*CmdbDiffRequest) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{8}
}

func (x *CmdbDiffRequest) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CmdbDiffRequest) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *CmdbDiffRequest) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *CmdbDiffRequest) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *CmdbDiffRequest) GetInstances() []string {
	if x != nil {
		return x.Instances
	}
	return nil
}

type CmdbDiffProcessRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" form:"order_id" json:"order_id" query:"order_id"`
	List    []*CmdbDiffProcessItem `protobuf:"bytes,11,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *CmdbDiffProcessRequest) Reset() {
	*x = CmdbDiffProcessRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbDiffProcessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbDiffProcessRequest) ProtoMessage() {}

func (x *CmdbDiffProcessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbDiffProcessRequest.ProtoReflect.Descriptor instead.
func (*CmdbDiffProcessRequest) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{9}
}

func (x *CmdbDiffProcessRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CmdbDiffProcessRequest) GetList() []*CmdbDiffProcessItem {
	if x != nil {
		return x.List
	}
	return nil
}

type CmdbDiffProcessItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType string `protobuf:"bytes,1,opt,name=resource_type,json=resourceType,proto3" form:"resource_type" json:"resource_type" query:"resource_type"`
	ActionType   string `protobuf:"bytes,2,opt,name=action_type,json=actionType,proto3" form:"action_type" json:"action_type" query:"action_type"`
	InstanceId   string `protobuf:"bytes,11,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
}

func (x *CmdbDiffProcessItem) Reset() {
	*x = CmdbDiffProcessItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbDiffProcessItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbDiffProcessItem) ProtoMessage() {}

func (x *CmdbDiffProcessItem) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbDiffProcessItem.ProtoReflect.Descriptor instead.
func (*CmdbDiffProcessItem) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{10}
}

func (x *CmdbDiffProcessItem) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CmdbDiffProcessItem) GetActionType() string {
	if x != nil {
		return x.ActionType
	}
	return ""
}

func (x *CmdbDiffProcessItem) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

type CmdbDiffDeleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DryRun bool `protobuf:"varint,1,opt,name=dry_run,json=dryRun,proto3" form:"dry_run" json:"dry_run" query:"dry_run"`
}

func (x *CmdbDiffDeleteReq) Reset() {
	*x = CmdbDiffDeleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmdb_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmdbDiffDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdbDiffDeleteReq) ProtoMessage() {}

func (x *CmdbDiffDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_cmdb_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdbDiffDeleteReq.ProtoReflect.Descriptor instead.
func (*CmdbDiffDeleteReq) Descriptor() ([]byte, []int) {
	return file_cmdb_proto_rawDescGZIP(), []int{11}
}

func (x *CmdbDiffDeleteReq) GetDryRun() bool {
	if x != nil {
		return x.DryRun
	}
	return false
}

var File_cmdb_proto protoreflect.FileDescriptor

var file_cmdb_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x63, 0x6d, 0x64, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x22, 0x97, 0x01, 0x0a, 0x10, 0x63, 0x6d, 0x64, 0x62, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x6f, 0x72, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74,
	0x22, 0x83, 0x01, 0x0a, 0x11, 0x63, 0x6d, 0x64, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x6b, 0x5f, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62,
	0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x6b,
	0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x62, 0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x41, 0x0a, 0x19, 0x63, 0x6d, 0x64, 0x62, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x22, 0xcd, 0x01, 0x0a, 0x11, 0x63, 0x6d,
	0x64, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x75, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x22, 0x2c, 0x0a, 0x12, 0x63, 0x6d, 0x64,
	0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x54, 0x0a, 0x11, 0x63, 0x6d, 0x64, 0x62, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x64, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x5a, 0x0a,
	0x12, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x62, 0x6b, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x6b, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x6b, 0x5f, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62,
	0x6b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x73, 0x67, 0x22, 0x75, 0x0a, 0x13, 0x63, 0x6d, 0x64,
	0x62, 0x50, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x22, 0x9a, 0x01, 0x0a, 0x0f, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x66, 0x0a,
	0x16, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62,
	0x44, 0x69, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x7c, 0x0a, 0x13, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66,
	0x66, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x22, 0x2c, 0x0a, 0x11, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x72, 0x79, 0x5f,
	0x72, 0x75, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x64, 0x72, 0x79, 0x52, 0x75,
	0x6e, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68,
	0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f,
	0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cmdb_proto_rawDescOnce sync.Once
	file_cmdb_proto_rawDescData = file_cmdb_proto_rawDesc
)

func file_cmdb_proto_rawDescGZIP() []byte {
	file_cmdb_proto_rawDescOnce.Do(func() {
		file_cmdb_proto_rawDescData = protoimpl.X.CompressGZIP(file_cmdb_proto_rawDescData)
	})
	return file_cmdb_proto_rawDescData
}

var file_cmdb_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_cmdb_proto_goTypes = []interface{}{
	(*CmdbQueryRequest)(nil),          // 0: cloudman.cmdbQueryRequest
	(*CmdbQueryResponse)(nil),         // 1: cloudman.cmdbQueryResponse
	(*CmdbQueryAssociateRequest)(nil), // 2: cloudman.cmdbQueryAssociateRequest
	(*CmdbUpdateRequest)(nil),         // 3: cloudman.cmdbUpdateRequest
	(*CmdbUpdateResponse)(nil),        // 4: cloudman.cmdbUpdateResponse
	(*CmdbDeleteRequest)(nil),         // 5: cloudman.cmdbDeleteRequest
	(*CmdbDeleteResponse)(nil),        // 6: cloudman.cmdbDeleteResponse
	(*CmdbPullInfoRequest)(nil),       // 7: cloudman.cmdbPullInfoRequest
	(*CmdbDiffRequest)(nil),           // 8: cloudman.cmdbDiffRequest
	(*CmdbDiffProcessRequest)(nil),    // 9: cloudman.cmdbDiffProcessRequest
	(*CmdbDiffProcessItem)(nil),       // 10: cloudman.cmdbDiffProcessItem
	(*CmdbDiffDeleteReq)(nil),         // 11: cloudman.cmdbDiffDeleteReq
}
var file_cmdb_proto_depIdxs = []int32{
	10, // 0: cloudman.cmdbDiffProcessRequest.list:type_name -> cloudman.cmdbDiffProcessItem
	1,  // [1:1] is the sub-list for method output_type
	1,  // [1:1] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_cmdb_proto_init() }
func file_cmdb_proto_init() {
	if File_cmdb_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cmdb_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbQueryAssociateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbUpdateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbDeleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbDeleteResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbPullInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbDiffRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbDiffProcessRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbDiffProcessItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmdb_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmdbDiffDeleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cmdb_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmdb_proto_goTypes,
		DependencyIndexes: file_cmdb_proto_depIdxs,
		MessageInfos:      file_cmdb_proto_msgTypes,
	}.Build()
	File_cmdb_proto = out.File
	file_cmdb_proto_rawDesc = nil
	file_cmdb_proto_goTypes = nil
	file_cmdb_proto_depIdxs = nil
}
