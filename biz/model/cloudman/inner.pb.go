// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: inner.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RunTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string `protobuf:"bytes,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
}

func (x *RunTaskResponse) Reset() {
	*x = RunTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_inner_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunTaskResponse) ProtoMessage() {}

func (x *RunTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_inner_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunTaskResponse.ProtoReflect.Descriptor instead.
func (*RunTaskResponse) Descriptor() ([]byte, []int) {
	return file_inner_proto_rawDescGZIP(), []int{0}
}

func (x *RunTaskResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_inner_proto protoreflect.FileDescriptor

var file_inner_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x22, 0x29, 0x0a, 0x0f, 0x72, 0x75, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69,
	0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73,
	0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_inner_proto_rawDescOnce sync.Once
	file_inner_proto_rawDescData = file_inner_proto_rawDesc
)

func file_inner_proto_rawDescGZIP() []byte {
	file_inner_proto_rawDescOnce.Do(func() {
		file_inner_proto_rawDescData = protoimpl.X.CompressGZIP(file_inner_proto_rawDescData)
	})
	return file_inner_proto_rawDescData
}

var file_inner_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_inner_proto_goTypes = []interface{}{
	(*RunTaskResponse)(nil), // 0: cloudman.runTaskResponse
}
var file_inner_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_inner_proto_init() }
func file_inner_proto_init() {
	if File_inner_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_inner_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_inner_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_inner_proto_goTypes,
		DependencyIndexes: file_inner_proto_depIdxs,
		MessageInfos:      file_inner_proto_msgTypes,
	}.Build()
	File_inner_proto = out.File
	file_inner_proto_rawDesc = nil
	file_inner_proto_goTypes = nil
	file_inner_proto_depIdxs = nil
}
