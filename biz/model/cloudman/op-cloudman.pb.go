// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: op-cloudman.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_op_cloudman_proto protoreflect.FileDescriptor

var file_op_cloudman_proto_rawDesc = []byte{
	0x0a, 0x11, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x0c, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x63, 0x6d, 0x64, 0x62,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0b, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x72, 0x65, 0x73, 0x52, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x72, 0x65, 0x73,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a,
	0x74, 0x61, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x74, 0x61, 0x73, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x74, 0x72, 0x65, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x09, 0x65, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x08, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x09, 0x72, 0x61, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x69, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x64, 0x64, 0x6f, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x09, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x6c, 0x0a, 0x10, 0x4f, 0x70, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x54, 0x61, 0x6b, 0x75, 0x6d, 0x69, 0x12, 0x58, 0x0a, 0x1a,
	0x4f, 0x70, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x54, 0x61, 0x6b, 0x75, 0x6d, 0x69,
	0x49, 0x6e, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x17, 0xca,
	0xc1, 0x18, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x69, 0x6e, 0x69, 0x74, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xa0, 0x0d, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x57, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x13, 0xca, 0xc1, 0x18, 0x0f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5e, 0x0a, 0x0e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x12, 0x19, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x22, 0x17, 0xca, 0xc1, 0x18, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x6c, 0x6c, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x56, 0x0a, 0x0d, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x13,
	0xd2, 0xc1, 0x18, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x6d, 0x0a, 0x17, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1a, 0xd2, 0xc1, 0x18, 0x16, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x2d, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x69, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1e, 0xda,
	0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x57, 0x0a,
	0x0f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x44, 0x1a, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x17, 0xca,
	0xc1, 0x18, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x5a, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x17, 0xda, 0xc1, 0x18, 0x13, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a,
	0x69, 0x64, 0x12, 0x4f, 0x0a, 0x0e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x73,
	0x74, 0x72, 0x6f, 0x79, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x17, 0xe2, 0xc1, 0x18, 0x13,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f,
	0x3a, 0x69, 0x64, 0x12, 0x6d, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x65,
	0x74, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a,
	0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0xca,
	0xc1, 0x18, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x62, 0x69, 0x6e, 0x64, 0x2d, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x12, 0x64, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x65, 0x73,
	0x74, 0x52, 0x61, 0x77, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x18,
	0xd2, 0xc1, 0x18, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2d, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x55, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x14, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x1c, 0xd2, 0xc1, 0x18, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x70, 0x69, 0x6e, 0x67, 0x12,
	0x51, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x12,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x44, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x1c, 0xd2, 0xc1, 0x18, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x73, 0x79,
	0x6e, 0x63, 0x12, 0x69, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x25, 0xd2, 0xc1, 0x18, 0x21, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x2f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x6d, 0x0a,
	0x10, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x29, 0xca, 0xc1, 0x18, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x64, 0x2f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x78, 0x0a, 0x10,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x12, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a,
	0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x25, 0xca, 0xc1, 0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x2f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x6d, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1a, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x29, 0xda, 0xc1, 0x18, 0x25,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f,
	0x3a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x2f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x62, 0x0a, 0x0f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x65, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x10, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x29,
	0xe2, 0xc1, 0x18, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x2f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x2f, 0x3a, 0x69, 0x64, 0x32, 0xf4, 0x02, 0x0a, 0x0a, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x7a, 0x0a, 0x12, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x21, 0xd2, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0f, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x27, 0xca, 0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f,
	0x3a, 0x69, 0x64, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x74, 0x0a, 0x17, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20,
	0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x73, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x32, 0x5e, 0x0a, 0x05, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x0f, 0x49, 0x6e, 0x6e,
	0x65, 0x72, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x41, 0x50, 0x49, 0x12, 0x14, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x75,
	0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x11, 0xd2,
	0xc1, 0x18, 0x0d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6e, 0x6e, 0x65, 0x72,
	0x32, 0xc0, 0x08, 0x0a, 0x07, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12, 0x61, 0x0a, 0x0b,
	0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x1c, 0xca, 0xc1, 0x18, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x12,
	0x5a, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x44, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65,
	0x63, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x20, 0xca, 0xc1, 0x18, 0x1c, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x6e, 0x0a, 0x0e, 0x52,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x14, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x28, 0xd2, 0xc1, 0x18, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2f,
	0x3a, 0x69, 0x64, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0c, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x13, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x73,
	0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x62, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x73,
	0x74, 0x72, 0x6f, 0x79, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x28, 0xda, 0xc1,
	0x18, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x64,
	0x65, 0x73, 0x74, 0x72, 0x6f, 0x79, 0x12, 0x65, 0x0a, 0x13, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x17, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x23, 0xd2, 0xc1, 0x18, 0x1f, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x68, 0x0a,
	0x10, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x27,
	0xca, 0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2d, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x74, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x23, 0xca, 0xc1, 0x18, 0x1f, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x69, 0x0a,
	0x13, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x27, 0xda, 0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x2d, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x61, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x79,
	0x63, 0x6c, 0x65, 0x44, 0x65, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x27, 0xe2, 0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x2d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x56, 0x0a, 0x10, 0x52,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x52, 0x75, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x1f, 0xd2, 0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x75, 0x6e, 0x2d, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x32, 0xd9, 0x04, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x61, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1a, 0xca, 0xc1,
	0x18, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x5d, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12,
	0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1a, 0xd2, 0xc1, 0x18,
	0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x5c, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x1a, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x1e, 0xca, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x61, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1e, 0xda, 0xc1, 0x18, 0x1a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x58, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x12, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x1e, 0xe2, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x3a,
	0x69, 0x64, 0x12, 0x6b, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x12, 0x16, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20, 0xca,
	0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x32,
	0x95, 0x0a, 0x0a, 0x09, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x7b, 0x0a,
	0x13, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1d, 0xca, 0xc1, 0x18,
	0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x7f, 0x0a, 0x14, 0x44, 0x61,
	0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75,
	0x74, 0x68, 0x12, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61,
	0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0xca, 0xc1, 0x18,
	0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x61, 0x75, 0x74, 0x68, 0x12, 0x81, 0x01, 0x0a, 0x1b,
	0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x49, 0x66, 0x57, 0x6f,
	0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x73, 0x65, 0x64, 0x12, 0x0f, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x2a, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x49, 0x66, 0x57, 0x6f, 0x72, 0x6b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x73, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0xca, 0xc1, 0x18, 0x21, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f,
	0x69, 0x66, 0x77, 0x6f, 0x72, 0x6b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x75, 0x73, 0x65, 0x64, 0x12,
	0x7e, 0x0a, 0x1a, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x42,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x29,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xca, 0xc1, 0x18, 0x20, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x7c, 0x0a, 0x15, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x48,
	0x6f, 0x73, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x73,
	0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f,
	0x73, 0x74, 0x42, 0x72, 0x69, 0x65, 0x66, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20, 0xd2, 0xc1, 0x18,
	0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x12, 0x78, 0x0a,
	0x14, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x42, 0x72, 0x69, 0x65, 0x66, 0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1f, 0xd2, 0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x72, 0x65,
	0x73, 0x5f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x12, 0x91, 0x01, 0x0a, 0x1a, 0x44, 0x61, 0x73, 0x68,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x24, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x26, 0xd2, 0xc1, 0x18, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x19,
	0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x43, 0x68, 0x61, 0x72, 0x74, 0x12, 0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0xd2, 0xc1, 0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x72, 0x65, 0x73, 0x5f,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x12, 0x76, 0x0a, 0x19, 0x44,
	0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x46, 0x69, 0x6c, 0x6c,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x46, 0x69, 0x6c, 0x6c, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x28, 0xd2, 0xc1, 0x18, 0x24, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x6c, 0x6c, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x12, 0x72, 0x0a, 0x15, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61, 0x72, 0x64,
	0x46, 0x69, 0x6c, 0x6c, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x12, 0x19, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x66, 0x69, 0x6c, 0x6c, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x66, 0x69, 0x6c, 0x6c, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52,
	0x65, 0x73, 0x22, 0x23, 0xd2, 0xc1, 0x18, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2f, 0x66, 0x69, 0x6c, 0x6c, 0x5f, 0x73,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x32, 0xf8, 0x03, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x77, 0x0a, 0x12, 0x52, 0x65,
	0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1c,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x25, 0xca, 0xc1,
	0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2f,
	0x3a, 0x69, 0x64, 0x12, 0x72, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x25, 0xda, 0xc1, 0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2d, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x7a, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x46, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x66, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x66, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x2f, 0xd2, 0xc1, 0x18, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2d, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x7d, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x2f, 0xca, 0xc1, 0x18, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2d, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x2d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x2f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x32, 0x89, 0x04, 0x0a, 0x04, 0x54, 0x61, 0x67, 0x73, 0x12, 0x53, 0x0a, 0x08, 0x54,
	0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x74, 0x61, 0x67, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x15,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x61, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x19, 0xca, 0xc1, 0x18, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x60, 0x0a, 0x0e, 0x54, 0x61, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x61,
	0x67, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x20, 0xca, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2f, 0x74, 0x61,
	0x67, 0x73, 0x12, 0x51, 0x0a, 0x0a, 0x54, 0x61, 0x67, 0x73, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x61, 0x67, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x19, 0xd2, 0xc1, 0x18, 0x15,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x74, 0x61, 0x67, 0x73, 0x12, 0x50, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x74, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x1d, 0xca, 0xc1, 0x18, 0x19, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74,
	0x61, 0x67, 0x73, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x55, 0x0a, 0x0a, 0x54, 0x61, 0x67, 0x73, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x74, 0x61, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x1d, 0xda, 0xc1, 0x18, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x73, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x4e,
	0x0a, 0x07, 0x54, 0x61, 0x67, 0x73, 0x44, 0x65, 0x6c, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x1d, 0xe2, 0xc1, 0x18, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x61, 0x67, 0x73, 0x2f, 0x3a, 0x69, 0x64, 0x32, 0x9e,
	0x23, 0x0a, 0x0f, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x60, 0x0a, 0x15, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x20, 0xca, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x61, 0x0a, 0x17, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12,
	0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x19, 0xd2, 0xc1,
	0x18, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x74, 0x0a, 0x20, 0x48, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x4f, 0x70, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0d, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x64, 0x73, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x4f, 0x70, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x24, 0xd2, 0xc1, 0x18, 0x20, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f,
	0x73, 0x74, 0x2d, 0x6f, 0x70, 0x73, 0x2d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5e, 0x0a,
	0x1b, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0d, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x64, 0x73, 0x1a, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1e, 0xd2,
	0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x60, 0x0a,
	0x1d, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0d,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x64, 0x73, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x1e, 0xe2, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6c, 0x6f, 0x63, 0x6b, 0x12,
	0x70, 0x0a, 0x19, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x12, 0x17, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c,
	0x73, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x6d, 0x0a, 0x19, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x12, 0x17,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x20,
	0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x6b, 0x0a, 0x1c, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x20, 0xd2, 0xc1, 0x18,
	0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x6d, 0x0a,
	0x1a, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x47, 0x65, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a,
	0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x22, 0xca, 0xc1, 0x18, 0x1e, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68,
	0x6f, 0x73, 0x74, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x79, 0x0a, 0x1e,
	0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47,
	0x65, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x44, 0x1a, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f,
	0x73, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27,
	0xca, 0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x3a,
	0x69, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x6b, 0x12, 0x72, 0x0a, 0x1c, 0x48, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x43, 0x50, 0x55,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0xd2, 0xc1, 0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d,
	0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x70, 0x75, 0x12, 0x72, 0x0a, 0x1c, 0x48,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65,
	0x74, 0x4d, 0x65, 0x6d, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0xd2, 0xc1, 0x18, 0x21, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68,
	0x6f, 0x73, 0x74, 0x2d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x6d, 0x65, 0x6d, 0x12,
	0x70, 0x0a, 0x1b, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x47, 0x65, 0x74, 0x49, 0x4f, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x14,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x24, 0xd2, 0xc1, 0x18,
	0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x69,
	0x6f, 0x12, 0x72, 0x0a, 0x1c, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25,
	0xd2, 0xc1, 0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x2f, 0x6e, 0x65, 0x74, 0x12, 0x74, 0x0a, 0x1d, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x6b, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x26, 0xd2, 0xc1, 0x18, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x64, 0x69, 0x73, 0x6b, 0x12, 0x78, 0x0a, 0x1f, 0x48,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65,
	0x74, 0x55, 0x50, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x14,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0xd2, 0xc1, 0x18,
	0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x75,
	0x70, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x70, 0x0a, 0x1b, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x47, 0x72, 0x61, 0x70, 0x68,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x56, 0x69, 0x65, 0x77, 0x22,
	0x26, 0xd2, 0xc1, 0x18, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x12, 0x78, 0x0a, 0x1d, 0x48, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x47, 0x61, 0x69,
	0x61, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x61, 0x69, 0x61, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x61, 0x69, 0x61,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x26, 0xd2, 0xc1, 0x18, 0x22, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x67, 0x61, 0x69,
	0x61, 0x12, 0x75, 0x0a, 0x1a, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x47, 0x61, 0x69, 0x61, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x61, 0x69, 0x61, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x47, 0x72, 0x61, 0x70, 0x68, 0x56, 0x69, 0x65, 0x77, 0x22, 0x2b, 0xd2, 0xc1, 0x18,
	0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x67,
	0x61, 0x69, 0x61, 0x2d, 0x76, 0x69, 0x65, 0x77, 0x12, 0x75, 0x0a, 0x16, 0x48, 0x6f, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x5a, 0x6f,
	0x6e, 0x65, 0x12, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x5a, 0x6f,
	0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x30, 0xca,
	0xc1, 0x18, 0x2c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73,
	0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12,
	0x7f, 0x0a, 0x1a, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x47, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x69, 0x72, 0x73, 0x12, 0x14, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x69, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4b,
	0x65, 0x79, 0x50, 0x61, 0x69, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x34, 0xca, 0xc1, 0x18, 0x30,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73, 0x74, 0x69, 0x6e,
	0x66, 0x6f, 0x2f, 0x6b, 0x65, 0x79, 0x70, 0x61, 0x69, 0x72, 0x73, 0x2f, 0x3a, 0x72, 0x69, 0x64,
	0x12, 0x96, 0x01, 0x0a, 0x1f, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x3a, 0xca,
	0xc1, 0x18, 0x36, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73,
	0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2d, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x7d, 0x0a, 0x19, 0x48, 0x6f, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x33, 0xca, 0xc1, 0x18, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x7d, 0x0a, 0x19, 0x48, 0x6f, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x56, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x33, 0xca, 0xc1, 0x18, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x2d, 0x68, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x76, 0x73, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x74, 0x0a, 0x18, 0x48, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2d,
	0xca, 0xc1, 0x18, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f,
	0x73, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x90, 0x01,
	0x0a, 0x1f, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x35, 0xca, 0xc1, 0x18, 0x31, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x66,
	0x6f, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2d, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x8d, 0x01, 0x0a, 0x1d, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x38, 0xca, 0xc1, 0x18, 0x34, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x2d, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x3a, 0x72, 0x69, 0x64,
	0x12, 0x8d, 0x01, 0x0a, 0x1d, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x38, 0xca, 0xc1, 0x18, 0x34, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x2d, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x3a, 0x69, 0x73, 0x70,
	0x12, 0xbb, 0x01, 0x0a, 0x2b, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x75, 0x74, 0x6f,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x45, 0x58,
	0x12, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41, 0x75, 0x74, 0x6f,
	0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x45, 0x58,
	0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41,
	0x75, 0x74, 0x6f, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x45, 0x58, 0x52, 0x65, 0x73, 0x70, 0x22, 0x45, 0xca, 0xc1, 0x18, 0x41, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2d, 0x68, 0x6f, 0x73, 0x74, 0x69, 0x6e, 0x66, 0x6f, 0x2f,
	0x61, 0x75, 0x74, 0x6f, 0x2d, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x2d, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x65, 0x78, 0x2f, 0x3a, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x12, 0x6e,
	0x0a, 0x1e, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x47, 0x65, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x4f, 0x53, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x53, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x22, 0x28, 0xca, 0xc1, 0x18, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2f,
	0x65, 0x78, 0x69, 0x73, 0x74, 0x5f, 0x6f, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x89,
	0x01, 0x0a, 0x22, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0xd2, 0xc1, 0x18, 0x2b, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x68, 0x6f, 0x73, 0x74, 0x2d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x2d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x65, 0x0a, 0x14, 0x48, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x1f, 0xd2, 0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2d, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x78, 0x0a, 0x18, 0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x1a, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x73,
	0x68, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x61, 0x73, 0x68, 0x42, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x23, 0xca, 0xc1, 0x18, 0x1f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73,
	0x74, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x6d, 0x0a, 0x1d, 0x48,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x26, 0xca, 0xc1, 0x18, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2f, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x70, 0x0a, 0x20, 0x48, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x79, 0x6e,
	0x63, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x1a,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1e, 0xd2, 0xc1,
	0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x7b, 0x0a, 0x20,
	0x48, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x25, 0xd2, 0xc1, 0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73, 0x74, 0x2f, 0x69, 0x70,
	0x76, 0x36, 0x2f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x83, 0x01, 0x0a, 0x22, 0x48, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x55, 0x6e, 0x61,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x6e, 0x61, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x49, 0x70, 0x76, 0x36, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x27, 0xd2, 0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x68, 0x6f, 0x73,
	0x74, 0x2f, 0x69, 0x70, 0x76, 0x36, 0x2f, 0x75, 0x6e, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x32,
	0xdc, 0x12, 0x0a, 0x05, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x12, 0x57, 0x0a, 0x0b, 0x4d, 0x79, 0x73,
	0x71, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x21, 0xca, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x69, 0x0a, 0x14, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x1a, 0xd2, 0xc1, 0x18, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x12, 0x55, 0x0a,
	0x11, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x0d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x64,
	0x73, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x1f, 0xd2, 0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d,
	0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x57, 0x0a, 0x13, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x55, 0x6e, 0x6c,
	0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0d, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x64, 0x73, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1f, 0xe2, 0xc1,
	0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x67, 0x0a,
	0x0f, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78,
	0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x21, 0xd2, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d,
	0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x64, 0x0a, 0x0f, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52,
	0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x21, 0xd2, 0xc1, 0x18, 0x1d, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x69, 0x0a, 0x10,
	0x4d, 0x79, 0x73, 0x71, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x44, 0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x4d, 0x79, 0x73, 0x71, 0x6c, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x22, 0x23, 0xca, 0xc1, 0x18, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x69,
	0x6e, 0x66, 0x6f, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x6f, 0x0a, 0x11, 0x4d, 0x79, 0x73, 0x71, 0x6c,
	0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x12, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x79, 0x73, 0x71,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x22, 0x2c, 0xca, 0xc1, 0x18, 0x28,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2d, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x70, 0x0a, 0x10, 0x4d, 0x79, 0x73, 0x71,
	0x6c, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x79, 0x73, 0x71,
	0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2b, 0xca,
	0xc1, 0x18, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2d, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x70, 0x0a, 0x12, 0x4d, 0x79,
	0x73, 0x71, 0x6c, 0x47, 0x65, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x73,
	0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x44, 0x1a, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2d, 0xca,
	0xc1, 0x18, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2d, 0x77,
	0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x6f, 0x0a, 0x11,
	0x4d, 0x79, 0x73, 0x71, 0x6c, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x22,
	0x2c, 0xca, 0xc1, 0x18, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x69, 0x6e, 0x66, 0x6f,
	0x2d, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x69, 0x0a,
	0x0f, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x47, 0x65, 0x74, 0x44, 0x42, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x44, 0x42, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0xca, 0xc1, 0x18,
	0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x72, 0x65, 0x66, 0x2f, 0x64, 0x62, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x7b, 0x0a, 0x1a, 0x4d, 0x79, 0x73, 0x71,
	0x6c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x44, 0x42, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x32, 0xca, 0xc1, 0x18, 0x2e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x72, 0x65,
	0x66, 0x2f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65,
	0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x76, 0x0a, 0x16, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x42, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12,
	0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x44, 0x42, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x2b, 0xca, 0xc1, 0x18, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x72, 0x65, 0x66,
	0x2f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x88, 0x01,
	0x0a, 0x15, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x36, 0xca, 0xc1, 0x18, 0x32, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2d, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x6f, 0x0a, 0x0f, 0x4d, 0x79, 0x73, 0x71,
	0x6c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2f, 0xca, 0xc1, 0x18, 0x2b, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d,
	0x79, 0x73, 0x71, 0x6c, 0x2d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x6f, 0x0a, 0x0f, 0x4d, 0x79, 0x73,
	0x71, 0x6c, 0x47, 0x65, 0x74, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x14, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2f, 0xca, 0xc1, 0x18, 0x2b, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x68, 0x0a, 0x17, 0x4d, 0x79,
	0x73, 0x71, 0x6c, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x1f, 0xd2, 0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2f,
	0x73, 0x79, 0x6e, 0x63, 0x12, 0x90, 0x01, 0x0a, 0x19, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x38, 0xd2,
	0xc1, 0x18, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x2d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x3a, 0x69, 0x73, 0x70, 0x5f,
	0x69, 0x64, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x85, 0x01, 0x0a, 0x18, 0x4d, 0x79, 0x73, 0x71,
	0x6c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x50, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x34, 0xd2, 0xc1, 0x18, 0x30, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x6d, 0x79, 0x73, 0x71, 0x6c, 0x2d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70,
	0x2d, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12,
	0x8c, 0x01, 0x0a, 0x19, 0x4d, 0x79, 0x73, 0x71, 0x6c, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x53, 0x65, 0x74, 0x54, 0x6f, 0x4f, 0x53, 0x53, 0x12, 0x21, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x53, 0x65, 0x74, 0x54, 0x6f, 0x4f, 0x53, 0x53, 0x52, 0x65, 0x71,
	0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x53, 0x65, 0x74, 0x54, 0x6f, 0x4f, 0x53, 0x53,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0xd2, 0xc1, 0x18, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6d, 0x79, 0x73, 0x71, 0x6c,
	0x2f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x32, 0xc4,
	0x0f, 0x0a, 0x05, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x57, 0x0a, 0x0b, 0x52, 0x65, 0x64, 0x69,
	0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x21,
	0xca, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x54, 0x0a, 0x0d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65,
	0x64, 0x69, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1a, 0xd2, 0xc1, 0x18,
	0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x12, 0x55, 0x0a, 0x11, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x4c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0d, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x64, 0x73, 0x1a, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1f, 0xd2,
	0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x57,
	0x0a, 0x13, 0x52, 0x65, 0x64, 0x69, 0x73, 0x55, 0x6e, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x49, 0x64, 0x73, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1f, 0xe2, 0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64,
	0x69, 0x73, 0x2d, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x67, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x73, 0x70, 0x22, 0x21, 0xd2,
	0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x64, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x58,
	0x6c, 0x73, 0x78, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x21, 0xd2, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x5b, 0x0a, 0x09, 0x52, 0x65, 0x64, 0x69, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x23,
	0xca, 0xc1, 0x18, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2f,
	0x3a, 0x69, 0x64, 0x12, 0x6c, 0x0a, 0x10, 0x52, 0x65, 0x64, 0x69, 0x73, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x17, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x22, 0x2b, 0xca, 0xc1, 0x18, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x3a, 0x69,
	0x64, 0x12, 0x70, 0x0a, 0x12, 0x52, 0x65, 0x64, 0x69, 0x73, 0x47, 0x65, 0x74, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x17, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2d, 0xca, 0xc1, 0x18, 0x29, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x2d, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2f,
	0x3a, 0x69, 0x64, 0x12, 0x69, 0x0a, 0x12, 0x52, 0x65, 0x64, 0x69, 0x73, 0x47, 0x65, 0x74, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x28, 0xca, 0xc1, 0x18, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x2d, 0x72, 0x65, 0x66, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x7b,
	0x0a, 0x1a, 0x52, 0x65, 0x64, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x13, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x42, 0x5a,
	0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x32, 0xca, 0xc1, 0x18, 0x2e, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65,
	0x64, 0x69, 0x73, 0x2d, 0x72, 0x65, 0x66, 0x2f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x7d, 0x0a, 0x19, 0x52,
	0x65, 0x64, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0xca,
	0xc1, 0x18, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d, 0x72, 0x65, 0x66, 0x2f, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x9a, 0x01, 0x0a, 0x19, 0x52,
	0x65, 0x64, 0x69, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x3c, 0xca, 0xc1, 0x18, 0x38, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x64, 0x69, 0x73, 0x2d, 0x72, 0x65, 0x66, 0x2f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x2d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x3a, 0x69, 0x73, 0x70, 0x5f,
	0x69, 0x64, 0x2f, 0x3a, 0x72, 0x69, 0x64, 0x12, 0x88, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x64, 0x69,
	0x73, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x36, 0xca, 0xc1, 0x18, 0x32,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x3a, 0x72,
	0x69, 0x64, 0x12, 0x6f, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x2f, 0xca, 0xc1, 0x18, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x3a,
	0x72, 0x69, 0x64, 0x12, 0x6f, 0x0a, 0x0f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x47, 0x65, 0x74, 0x56,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x2f, 0xca, 0xc1, 0x18, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2d,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x2f,
	0x3a, 0x72, 0x69, 0x64, 0x12, 0x68, 0x0a, 0x17, 0x52, 0x65, 0x64, 0x69, 0x73, 0x53, 0x79, 0x6e,
	0x63, 0x52, 0x65, 0x64, 0x69, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12,
	0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1f, 0xd2,
	0xc1, 0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x75,
	0x0a, 0x14, 0x52, 0x65, 0x64, 0x69, 0x73, 0x47, 0x65, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x1a, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0xca, 0xc1, 0x18, 0x27, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72,
	0x65, 0x64, 0x69, 0x73, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x2f, 0x3a, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x32, 0xe6, 0x18, 0x0a, 0x0d, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x83, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x12, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x22, 0x22, 0xd2, 0xc1, 0x18, 0x1e, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0xa3, 0x01,
	0x0a, 0x20, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x73, 0x12, 0x2d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x65, 0x73, 0x22, 0x2c, 0xd2, 0xc1, 0x18, 0x28, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x86, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x73, 0x22, 0x28, 0xd2, 0xc1, 0x18, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x87, 0x01, 0x0a,
	0x24, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x24, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x27, 0xda,
	0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x21, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x12, 0x21, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52,
	0x65, 0x73, 0x22, 0x2e, 0xca, 0xc1, 0x18, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x2d, 0x74, 0x61,
	0x67, 0x73, 0x12, 0xae, 0x01, 0x0a, 0x27, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x27,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73,
	0x22, 0x31, 0xca, 0xc1, 0x18, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x2d, 0x74, 0x61, 0x67, 0x73,
	0x2d, 0x73, 0x67, 0x12, 0x9f, 0x01, 0x0a, 0x29, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61,
	0x67, 0x12, 0x29, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x35,
	0xd2, 0xc1, 0x18, 0x31, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x2d, 0x74, 0x61, 0x67, 0x73, 0x2f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x83, 0x01, 0x0a, 0x21, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x21, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x29, 0xd2, 0xc1, 0x18, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x22,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x65,
	0x61, 0x6e, 0x75, 0x70, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x12, 0x29, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x2a, 0xd2, 0xc1, 0x18, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2f, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x12, 0x89, 0x01, 0x0a, 0x20,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x73, 0x70, 0x22, 0x29, 0xd2, 0xc1,
	0x18, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x96, 0x01, 0x0a, 0x24, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x24, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x2e, 0xd2, 0xc1, 0x18, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x9e, 0x01, 0x0a, 0x22, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73,
	0x78, 0x52, 0x65, 0x73, 0x70, 0x22, 0x3a, 0xd2, 0xc1, 0x18, 0x36, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x2d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x9e, 0x01, 0x0a, 0x29, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x29, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x34, 0xd2, 0xc1,
	0x18, 0x30, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x2d, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x24, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x24, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x27, 0xd2, 0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x12, 0x99, 0x01, 0x0a,
	0x21, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73,
	0x74, 0x73, 0x12, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2d, 0xca, 0xc1, 0x18, 0x29, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x95, 0x01, 0x0a, 0x1f, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x1f, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x50,
	0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x3f, 0xda, 0xc1, 0x18, 0x3b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x2f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x3a, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x12, 0x88, 0x01, 0x0a, 0x1c, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c,
	0x73, 0x12, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b,
	0xca, 0xc1, 0x18, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x2f, 0x61, 0x6c, 0x62, 0x5f, 0x61, 0x63, 0x6c, 0x73, 0x12, 0x8c, 0x01, 0x0a, 0x1e,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x64,
	0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1e,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x4c, 0x42,
	0x41, 0x63, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x38, 0xd2, 0xc1, 0x18, 0x34, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x2f, 0x61, 0x6c, 0x62, 0x5f, 0x61, 0x63, 0x6c, 0x2f, 0x3a, 0x61, 0x63,
	0x6c, 0x5f, 0x69, 0x64, 0x2f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x9f, 0x01, 0x0a, 0x21, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x45, 0xd2, 0xc1, 0x18, 0x41, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x61, 0x6c, 0x62, 0x5f, 0x61, 0x63,
	0x6c, 0x2f, 0x3a, 0x61, 0x63, 0x6c, 0x5f, 0x69, 0x64, 0x2f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x2f,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x2d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x95, 0x01, 0x0a,
	0x1e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4a, 0x6f,
	0x69, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x25, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x4f,
	0x72, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x27, 0xd2, 0xc1, 0x18,
	0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f,
	0x6a, 0x6f, 0x69, 0x6e, 0x12, 0x97, 0x01, 0x0a, 0x1f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x25, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x4f, 0x72, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a,
	0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x28, 0xd2, 0xc1, 0x18, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x6c, 0x65, 0x61, 0x76, 0x65, 0x32, 0xb5,
	0x07, 0x0a, 0x0c, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x12,
	0x7f, 0x0a, 0x14, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x42,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x6f,
	0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x52, 0x65, 0x73, 0x22, 0x21, 0xd2,
	0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72,
	0x12, 0x80, 0x01, 0x0a, 0x1a, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x22, 0x25, 0xca, 0xc1,
	0x18, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x2f,
	0x3a, 0x69, 0x64, 0x12, 0x95, 0x01, 0x0a, 0x1f, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x22, 0x33, 0xca, 0xc1, 0x18, 0x2f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6c, 0x6f, 0x61,
	0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x84, 0x01, 0x0a, 0x19,
	0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5a, 0x6f, 0x6e,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x22, 0x2f, 0xd2, 0xc1, 0x18, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x72, 0x2d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x12, 0x74, 0x0a, 0x14, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x4c, 0x73, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x4c, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x43, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x22, 0x2e, 0xd2, 0xc1, 0x18, 0x2a, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6c,
	0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x2d, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x61, 0x63, 0x6c, 0x73, 0x12, 0x8d, 0x01, 0x0a, 0x1c, 0x4c, 0x6f, 0x61,
	0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x65, 0x72,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x73, 0x12, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x22, 0x2f, 0xd2, 0xc1, 0x18, 0x2b, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6c, 0x6f,
	0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x2d, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2f, 0x63, 0x65, 0x72, 0x74, 0x73, 0x12, 0x7c, 0x0a, 0x1f, 0x4c, 0x6f, 0x61, 0x64,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x4c,
	0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x72, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x29, 0xd2, 0xc1, 0x18,
	0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x6c, 0x6f, 0x61, 0x64, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x2f, 0x63,
	0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x32, 0xc4, 0x02, 0x0a, 0x03, 0x45, 0x69, 0x70, 0x12, 0x5b,
	0x0a, 0x0b, 0x45, 0x69, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x18, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x45, 0x69, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x52, 0x65,
	0x73, 0x22, 0x18, 0xd2, 0xc1, 0x18, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x65, 0x69, 0x70, 0x12, 0x78, 0x0a, 0x12, 0x45,
	0x69, 0x70, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x66, 0x0a, 0x0f, 0x45, 0x69, 0x70, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x49, 0x70, 0x61, 0x6d, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x70, 0x61, 0x6d,
	0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x70, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x22, 0x1d,
	0xd2, 0xc1, 0x18, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x69, 0x70, 0x61, 0x6d, 0x32, 0x8d, 0x06,
	0x0a, 0x02, 0x49, 0x50, 0x12, 0x57, 0x0a, 0x0a, 0x49, 0x50, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49,
	0x50, 0x52, 0x65, 0x73, 0x22, 0x17, 0xd2, 0xc1, 0x18, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x69, 0x70, 0x12, 0x58, 0x0a,
	0x10, 0x49, 0x50, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x49,
	0x50, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x50, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1e, 0xd2, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x69, 0x70,
	0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x52, 0x0a, 0x0a, 0x49, 0x50, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x50, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1b,
	0xe2, 0xc1, 0x18, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x69, 0x70, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x66, 0x0a, 0x0f, 0x49,
	0x50, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x79, 0x49, 0x50, 0x73, 0x12, 0x1a,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x42, 0x79, 0x49, 0x50, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x79,
	0x49, 0x50, 0x73, 0x52, 0x65, 0x73, 0x22, 0x1b, 0xd2, 0xc1, 0x18, 0x17, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x69, 0x70, 0x2f,
	0x69, 0x70, 0x73, 0x12, 0x70, 0x0a, 0x11, 0x49, 0x50, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1e, 0xca, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x69, 0x70, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x5e, 0x0a, 0x0f, 0x49, 0x50, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1d, 0xd2, 0xc1, 0x18, 0x19, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x69, 0x70, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x62, 0x0a, 0x0f, 0x49, 0x50, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x21, 0xda, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x69, 0x70, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x62, 0x0a, 0x0f, 0x49, 0x50, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1a, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x21, 0xe2, 0xc1, 0x18, 0x1d,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x69, 0x70, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2f, 0x3a, 0x69, 0x64, 0x32, 0xbf, 0x06,
	0x0a, 0x03, 0x52, 0x41, 0x4d, 0x12, 0x72, 0x0a, 0x14, 0x52, 0x41, 0x4d, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1e, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x19,
	0xca, 0xc1, 0x18, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x85, 0x01, 0x0a, 0x1f, 0x52, 0x41,
	0x4d, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x50, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x29, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52,
	0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x50,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x25, 0xda, 0xc1, 0x18, 0x21,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x72, 0x61, 0x6d, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x69, 0x70, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x5f, 0x0a, 0x10, 0x52, 0x41, 0x4d, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x41, 0x4d, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x1d, 0xd2, 0xc1, 0x18, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x61, 0x6d, 0x2f, 0x73, 0x79,
	0x6e, 0x63, 0x12, 0x8f, 0x01, 0x0a, 0x19, 0x52, 0x41, 0x4d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x12, 0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27, 0xd2, 0xc1, 0x18,
	0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2f, 0x72, 0x61, 0x6d, 0x2f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x2f, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x12, 0x65, 0x0a, 0x12, 0x52, 0x41, 0x4d, 0x52, 0x41, 0x4d, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x12, 0x1c, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1f, 0xd2, 0xc1, 0x18, 0x1b,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2f, 0x72, 0x61, 0x6d, 0x2f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x12, 0x69, 0x0a, 0x13, 0x52,
	0x41, 0x4d, 0x53, 0x79, 0x6e, 0x63, 0x4e, 0x65, 0x77, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x79,
	0x6e, 0x63, 0x4e, 0x65, 0x77, 0x52, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x21, 0xd2, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x61, 0x6d, 0x2f, 0x6e, 0x65,
	0x77, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x77, 0x0a, 0x14, 0x52, 0x41, 0x4d, 0x47, 0x65, 0x74,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1e,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x52, 0x61, 0x6d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x1e, 0xca, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x61, 0x6d, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x32,
	0xb7, 0x03, 0x0a, 0x06, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x67, 0x0a, 0x0e, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x1b, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x1b, 0xd2, 0xc1, 0x18, 0x17, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x7d, 0x0a, 0x15, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x22, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x22, 0x23, 0xd2,
	0xc1, 0x18, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x12, 0x5e, 0x0a, 0x10, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x53, 0x79, 0x6e, 0x63,
	0x44, 0x6e, 0x73, 0x70, 0x6f, 0x64, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x27, 0xd2, 0xc1, 0x18, 0x23, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x2d, 0x64, 0x6e, 0x73, 0x70,
	0x6f, 0x64, 0x12, 0x65, 0x0a, 0x12, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x22, 0xd2, 0xc1, 0x18, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x32, 0xdd, 0x03, 0x0a, 0x04, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x4b, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x10, 0xca,
	0xc1, 0x18, 0x0c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x12,
	0x50, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x12,
	0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x75, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x14, 0xca, 0xc1, 0x18,
	0x10, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x3a, 0x69,
	0x64, 0x12, 0x49, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12,
	0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x10, 0xd2, 0xc1, 0x18, 0x0c,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x52, 0x0a, 0x0e,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x41, 0x50, 0x49, 0x12, 0x14,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x75, 0x6e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x18, 0xd2, 0xc1, 0x18, 0x14, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x72, 0x75, 0x6e,
	0x12, 0x4d, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x17,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x14, 0xda, 0xc1, 0x18, 0x10, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x3a, 0x69, 0x64, 0x12,
	0x48, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x12, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49,
	0x44, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x14, 0xe2, 0xc1, 0x18, 0x10, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x3a, 0x69, 0x64, 0x32, 0xd0, 0x01, 0x0a, 0x07, 0x54, 0x61,
	0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x12, 0x62, 0x0a, 0x12, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x12, 0x1c, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0xca, 0xc1, 0x18, 0x0b, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x67, 0x12, 0x61, 0x0a, 0x16, 0x54, 0x61, 0x73,
	0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x4c, 0x6f, 0x67, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x13, 0xca, 0xc1, 0x18, 0x0f, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x67, 0x2f, 0x3a, 0x69, 0x64, 0x32, 0x86, 0x02, 0x0a,
	0x06, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x0a, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x12, 0xca, 0xc1, 0x18, 0x0e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x0a, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22,
	0x16, 0xca, 0xc1, 0x18, 0x12, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x4c, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x1a,
	0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x12, 0xd2, 0xc1, 0x18, 0x0e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x32, 0xc6, 0x0b, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x64, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x54, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1d, 0xca, 0xc1,
	0x18, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x5f, 0x0a, 0x11, 0x52,
	0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x54,
	0x65, 0x6d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1d, 0xd2,
	0xc1, 0x18, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x63, 0x0a, 0x0f,
	0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x44, 0x1a, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x54, 0x65, 0x6d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x22, 0x21,
	0xca, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x3a, 0x69,
	0x64, 0x12, 0x63, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x21, 0xda, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x59, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x6c, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x10, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x21,
	0xe2, 0xc1, 0x18, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x3a, 0x69,
	0x64, 0x12, 0x6e, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x1b, 0xd2, 0xc1, 0x18, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x6e, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x1b, 0xd2, 0xc1, 0x18, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x75, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12,
	0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x62, 0x75, 0x6c,
	0x6b, 0x2d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x5c, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12,
	0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x75, 0x6e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1c, 0xd2, 0xc1, 0x18, 0x18, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72,
	0x75, 0x6e, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x6c, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x74,
	0x72, 0x79, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x75,
	0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x10,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x22, 0xd2, 0xc1, 0x18, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x72, 0x75, 0x6e, 0x2f, 0x3a, 0x69, 0x64, 0x2f, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x12, 0x6b, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x53, 0x61, 0x6c, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x54, 0x65,
	0x6d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x61, 0x6c, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x22,
	0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2d, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x95, 0x01, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x53, 0x65, 0x71, 0x12, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x53, 0x65, 0x71, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x65, 0x71, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2b, 0xd2, 0xc1,
	0x18, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x71, 0x12, 0xa2, 0x01, 0x0a, 0x22, 0x52, 0x65,
	0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x6e, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x24, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x57, 0x69, 0x74, 0x68, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x57, 0x69, 0x74, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2f, 0xd2,
	0xc1, 0x18, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2f, 0x67, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x32, 0xbd,
	0x02, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x64, 0x0a, 0x14, 0x52,
	0x65, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x17, 0xd2, 0xc1, 0x18, 0x13, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x6a, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x12, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x1a,
	0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x1f, 0xca, 0xc1,
	0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x75, 0x64, 0x69, 0x74, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2d, 0x6c, 0x6f, 0x67, 0x2f, 0x3a, 0x69, 0x64, 0x12, 0x5f, 0x0a,
	0x10, 0x52, 0x65, 0x73, 0x41, 0x75, 0x64, 0x69, 0x74, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x1b, 0xca, 0xc1, 0x18, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x61,
	0x75, 0x64, 0x69, 0x74, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x3a, 0x69, 0x64, 0x32, 0xef,
	0x12, 0x0a, 0x0c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12,
	0x62, 0x0a, 0x19, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x1c, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x22, 0x13,
	0xd2, 0xc1, 0x18, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x12, 0x6a, 0x0a, 0x1c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x47, 0x65, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x12, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x72, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x22, 0x1a, 0xd2, 0xc1, 0x18, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12,
	0x3f, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x1c,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x12, 0x57, 0x0a, 0x13, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x47, 0x65, 0x74, 0x74, 0x6f, 0x70, 0x6f, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x18, 0xca, 0xc1, 0x18, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2d, 0x74, 0x6f, 0x70, 0x6f, 0x12, 0x6d, 0x0a, 0x1a, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x47, 0x65, 0x74, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x54, 0x6f, 0x70, 0x6f, 0x12, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x54, 0x6f, 0x70, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x54, 0x6f, 0x70, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x18,
	0xd2, 0xc1, 0x18, 0x14, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x74, 0x6f, 0x70, 0x6f, 0x12, 0x5c, 0x0a, 0x17, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x22, 0x13, 0xca, 0xc1, 0x18, 0x0f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x43, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1a, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x12, 0x6c, 0x0a, 0x1a, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x6c, 0x0a, 0x1d, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x47,
	0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x1a, 0xd2, 0xc1, 0x18,
	0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x68, 0x0a, 0x1a, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x47, 0x65, 0x74, 0x47, 0x61, 0x74, 0x65, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x72, 0x65, 0x71, 0x22, 0x1a, 0xca, 0xc1, 0x18, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x64, 0x0a, 0x17, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x19, 0xd2, 0xc1,
	0x18, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x18, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x0d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52,
	0x61, 0x77, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x1a, 0xd2, 0xc1, 0x18, 0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x73, 0x0a, 0x1d, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32,
	0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6d, 0x0a, 0x17, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x67, 0x0a, 0x15, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x22, 0x19, 0xca, 0xc1, 0x18, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x60, 0x0a,
	0x18, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x61, 0x77,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x1e, 0xca, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x12,
	0x6e, 0x0a, 0x16, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x12, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x58, 0x6c, 0x73, 0x78, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20, 0xd2,
	0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x83, 0x01, 0x0a, 0x22, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x28, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x22, 0x22, 0xca, 0xc1, 0x18, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x7c, 0x0a, 0x24, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x10, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x22, 0xd2, 0xc1, 0x18, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x70, 0x0a, 0x1a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x52, 0x65, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x12, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x10,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x23, 0xd2, 0xc1, 0x18, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x67, 0x0a, 0x17, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x20, 0xd2, 0xc1,
	0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x6d,
	0x0a, 0x1a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x47, 0x65,
	0x74, 0x48, 0x6f, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x17, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1f, 0xd2, 0xc1,
	0x18, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x71, 0x0a,
	0x1c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x1e,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20,
	0xca, 0xc1, 0x18, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x32, 0xa6, 0x02, 0x0a, 0x06, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x4b, 0x0a, 0x0f, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x0f,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x79, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0x12, 0xca, 0xc1, 0x18, 0x0e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5a, 0x0a, 0x16, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x57, 0x65, 0x63, 0x6f, 0x6d, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x1e, 0xca, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x32, 0x2f, 0x77, 0x65, 0x63, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x12, 0x73, 0x0a, 0x22, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0f, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b, 0xca, 0xc1,
	0x18, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x69,
	0x6c, 0x79, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x32, 0x9d, 0x07, 0x0a, 0x04, 0x43, 0x6d,
	0x64, 0x62, 0x12, 0x5c, 0x0a, 0x09, 0x43, 0x6d, 0x64, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x16, 0xd2, 0xc1, 0x18, 0x12, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d, 0x64, 0x62, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x78, 0x0a, 0x12, 0x43, 0x6d, 0x64, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x12, 0x23, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d, 0x64, 0x62, 0x2f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x12, 0x6d, 0x0a, 0x10, 0x43, 0x6d,
	0x64, 0x62, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1b,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0xd2, 0xc1, 0x18, 0x1a, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d, 0x64, 0x62, 0x2f, 0x6d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x60, 0x0a, 0x0a, 0x43, 0x6d, 0x64,
	0x62, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x63, 0x6d, 0x64, 0x62, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x17, 0xd2, 0xc1, 0x18, 0x13, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f,
	0x63, 0x6d, 0x64, 0x62, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x5a, 0x0a, 0x0c, 0x43,
	0x6d, 0x64, 0x62, 0x50, 0x75, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x50, 0x75, 0x6c, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x1a, 0xd2, 0xc1, 0x18,
	0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d, 0x64, 0x62, 0x2f, 0x70, 0x75,
	0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x5a, 0x0a, 0x08, 0x43, 0x6d, 0x64, 0x62, 0x44,
	0x69, 0x66, 0x66, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63,
	0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x15, 0xd2, 0xc1,
	0x18, 0x11, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d, 0x64, 0x62, 0x2f, 0x64,
	0x69, 0x66, 0x66, 0x12, 0x5e, 0x0a, 0x0f, 0x43, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x18, 0xd2, 0xc1, 0x18, 0x14, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d, 0x64, 0x62, 0x2f, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x69, 0x0a, 0x0e, 0x43, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x63, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x1c, 0xd2, 0xc1, 0x18, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d,
	0x64, 0x62, 0x2f, 0x64, 0x69, 0x66, 0x66, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x69,
	0x0a, 0x0e, 0x43, 0x6d, 0x64, 0x62, 0x44, 0x69, 0x66, 0x66, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x63, 0x6d, 0x64, 0x62,
	0x44, 0x69, 0x66, 0x66, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1c, 0xd2, 0xc1, 0x18,
	0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6d, 0x64, 0x62, 0x2f, 0x64, 0x69,
	0x66, 0x66, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x32, 0xa7, 0x04, 0x0a, 0x0b, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x12, 0x60, 0x0a, 0x12, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65, 0x65, 0x12,
	0x0d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x61, 0x77, 0x1a, 0x19,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x65,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x74, 0x72,
	0x65, 0x65, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x72, 0x65, 0x65, 0x12, 0x66, 0x0a, 0x14, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x0d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52,
	0x61, 0x77, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x22, 0xd2, 0xc1, 0x18, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x74, 0x72, 0x65, 0x65, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x79, 0x0a, 0x1a, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72,
	0x65, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x0d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x61, 0x77,
	0x1a, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x29, 0xd2, 0xc1, 0x18, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x74, 0x72, 0x65, 0x65, 0x2f, 0x6e, 0x6f, 0x64,
	0x65, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x6b,
	0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x41, 0x64, 0x64,
	0x4e, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x41, 0x64, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0xd2, 0xc1, 0x18, 0x1c, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x74, 0x72,
	0x65, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x66, 0x0a, 0x11, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x72, 0x65, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x12, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x1e, 0xd2, 0xc1, 0x18, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x74, 0x72, 0x65, 0x65, 0x2f, 0x76, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x32, 0x78, 0x0a, 0x04, 0x44, 0x64, 0x6f, 0x73, 0x12, 0x70, 0x0a, 0x13, 0x44,
	0x64, 0x6f, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x64, 0x6f, 0x73, 0x42,
	0x67, 0x70, 0x12, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x64, 0x6f, 0x73, 0x42, 0x67, 0x70, 0x52, 0x65, 0x71,
	0x1a, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x44, 0x64, 0x6f, 0x73, 0x42, 0x67, 0x70, 0x52, 0x65, 0x73, 0x22, 0x1d,
	0xca, 0xc1, 0x18, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2f, 0x64, 0x64, 0x6f, 0x73, 0x2f, 0x62, 0x67, 0x70, 0x32, 0xb6, 0x01,
	0x0a, 0x10, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x1c, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43,
	0x62, 0x77, 0x70, 0x12, 0x29, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x43, 0x62, 0x77, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x29,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x43, 0x62, 0x77, 0x70, 0x52, 0x65, 0x73, 0x22, 0x2b, 0xca, 0xc1, 0x18, 0x27, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f,
	0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x2d, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x2f, 0x63, 0x62, 0x77, 0x70, 0x32, 0xa3, 0x01, 0x0a, 0x05, 0x44, 0x65, 0x62, 0x75, 0x67,
	0x12, 0x52, 0x0a, 0x12, 0x44, 0x65, 0x62, 0x75, 0x67, 0x46, 0x69, 0x78, 0x52, 0x65, 0x73, 0x54,
	0x61, 0x67, 0x42, 0x69, 0x6e, 0x64, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x19, 0xca, 0xc1, 0x18, 0x15, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x2f, 0x66, 0x69, 0x78,
	0x2d, 0x74, 0x61, 0x67, 0x12, 0x46, 0x0a, 0x09, 0x44, 0x65, 0x62, 0x75, 0x67, 0x54, 0x65, 0x73,
	0x74, 0x12, 0x0f, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x10, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x16, 0xca, 0xc1, 0x18, 0x12, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x31, 0x2f, 0x64, 0x65, 0x62, 0x75, 0x67, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x32, 0x9a, 0x01, 0x0a,
	0x0a, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x8b, 0x01, 0x0a, 0x21,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0x19,
	0xd2, 0xc1, 0x18, 0x15, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d,
	0x2f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x32, 0xfa, 0x02, 0x0a, 0x0c, 0x49, 0x6e,
	0x69, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x7a, 0x0a, 0x19, 0x49, 0x6e,
	0x69, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x65, 0x74, 0x4a, 0x75, 0x6d,
	0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x75, 0x6d, 0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47,
	0x65, 0x74, 0x4a, 0x75, 0x6d, 0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x24, 0xca, 0xc1, 0x18, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x2f, 0x6a, 0x75, 0x6d, 0x70,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x72, 0x0a, 0x17, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x18, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22, 0xca, 0xc1, 0x18, 0x1e, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x69,
	0x74, 0x2f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x7a, 0x0a, 0x18, 0x49, 0x6e,
	0x69, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65,
	0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27, 0xd2,
	0xc1, 0x18, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x2f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x2f, 0x70,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69,
	0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c,
	0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_op_cloudman_proto_goTypes = []interface{}{
	(*Empty)(nil),                               // 0: cloudman.Empty
	(*AccountQueryReq)(nil),                     // 1: cloudman.accountQueryReq
	(*CreateAccountRequest)(nil),                // 2: cloudman.createAccountRequest
	(*UpdateAccountRequest)(nil),                // 3: cloudman.updateAccountRequest
	(*UpdateAccountStatusReq)(nil),              // 4: cloudman.updateAccountStatusReq
	(*ObjectID)(nil),                            // 5: cloudman.ObjectID
	(*AccountParamInfo)(nil),                    // 6: cloudman.accountParamInfo
	(*ListAccountParamReq)(nil),                 // 7: cloudman.listAccountParamReq
	(*CloudAgentTaskListReq)(nil),               // 8: cloudman.cloudAgentTaskListReq
	(*IDReq)(nil),                               // 9: cloudman.IDReq
	(*ResChangelistReq)(nil),                    // 10: cloudman.resChangelistReq
	(*RunTaskReq)(nil),                          // 11: cloudman.runTaskReq
	(*RecycleQueryReq)(nil),                     // 12: cloudman.recycleQueryReq
	(*RecoverReq)(nil),                          // 13: cloudman.RecoverReq
	(*ObjectIDs)(nil),                           // 14: cloudman.ObjectIDs
	(*RecycleReq)(nil),                          // 15: cloudman.RecycleReq
	(*RecyclePolicy)(nil),                       // 16: cloudman.recyclePolicy
	(*RecyclePolicyReq)(nil),                    // 17: cloudman.recyclePolicyReq
	(*GroupQueryReq)(nil),                       // 18: cloudman.groupQueryReq
	(*GroupCreateReq)(nil),                      // 19: cloudman.groupCreateReq
	(*ListCloudReq)(nil),                        // 20: cloudman.listCloudReq
	(*DashBoardAccountRequest)(nil),             // 21: cloudman.dashBoardAccountRequest
	(*DashBoardUserAuthRequest)(nil),            // 22: cloudman.dashBoardUserAuthRequest
	(*DashboardHostBriefReq)(nil),               // 23: cloudman.dashboardHostBriefReq
	(*DashboardResBriefReq)(nil),                // 24: cloudman.dashboardResBriefReq
	(*DashboardHostDailyCountReq)(nil),          // 25: cloudman.dashboardHostDailyCountReq
	(*DashboardResDailyCountReq)(nil),           // 26: cloudman.dashboardResDailyCountReq
	(*AutoFillSnapshotReq)(nil),                 // 27: cloudman.autoFillSnapshotReq
	(*FillSnapshotReq)(nil),                     // 28: cloudman.fillSnapshotReq
	(*GroupPolicyInfoReq)(nil),                  // 29: cloudman.groupPolicyInfoReq
	(*ResGroupPolicyCreateReq)(nil),             // 30: cloudman.resGroupPolicyCreateReq
	(*FindResReq)(nil),                          // 31: cloudman.findResReq
	(*CountResReq)(nil),                         // 32: cloudman.countResReq
	(*TagQueryReq)(nil),                         // 33: cloudman.tagQueryReq
	(*TagCreateReq)(nil),                        // 34: cloudman.tagCreateReq
	(*HostResReq)(nil),                          // 35: cloudman.HostResReq
	(*Ids)(nil),                                 // 36: cloudman.Ids
	(*ExportXlsxReq)(nil),                       // 37: cloudman.ExportXlsxReq
	(*ImportXlsxReq)(nil),                       // 38: cloudman.ImportXlsxReq
	(*HostResDetail)(nil),                       // 39: cloudman.HostResDetail
	(*MonitorReq)(nil),                          // 40: cloudman.MonitorReq
	(*GaiaMonitReq)(nil),                        // 41: cloudman.GaiaMonitReq
	(*GaiaViewReq)(nil),                         // 42: cloudman.GaiaViewReq
	(*ZoneReq)(nil),                             // 43: cloudman.ZoneReq
	(*KeyPairReq)(nil),                          // 44: cloudman.KeyPairReq
	(*SecurityGroupReq)(nil),                    // 45: cloudman.SecurityGroupReq
	(*NetworkReq)(nil),                          // 46: cloudman.NetworkReq
	(*VSwitchReq)(nil),                          // 47: cloudman.VSwitchReq
	(*ImagesReq)(nil),                           // 48: cloudman.ImagesReq
	(*InstanceTypeReq)(nil),                     // 49: cloudman.InstanceTypeReq
	(*VolumeTypeReq)(nil),                       // 50: cloudman.VolumeTypeReq
	(*ChargeTypeReq)(nil),                       // 51: cloudman.ChargeTypeReq
	(*AutoSnapshotPolicyEXReq)(nil),             // 52: cloudman.AutoSnapshotPolicyEXReq
	(*UpdateMonitorStatusReq)(nil),              // 53: cloudman.UpdateMonitorStatusReq
	(*CustomQuery)(nil),                         // 54: cloudman.CustomQuery
	(*HostDashBoardReq)(nil),                    // 55: cloudman.HostDashBoardReq
	(*SyncInstancesReq)(nil),                    // 56: cloudman.SyncInstancesReq
	(*AssignIpv6AddressReq)(nil),                // 57: cloudman.AssignIpv6AddressReq
	(*UnassignIpv6AddressesReq)(nil),            // 58: cloudman.UnassignIpv6AddressesReq
	(*MysqlClusterReq)(nil),                     // 59: cloudman.MysqlClusterReq
	(*DBTypeReq)(nil),                           // 60: cloudman.DBTypeReq
	(*DBZoneReq)(nil),                           // 61: cloudman.DBZoneReq
	(*DBClassesReq)(nil),                        // 62: cloudman.DBClassesReq
	(*DBParamsGroupsReq)(nil),                   // 63: cloudman.DBParamsGroupsReq
	(*IPWhiteListReq)(nil),                      // 64: cloudman.IPWhiteListReq
	(*UploadBackupSetToOSSReq)(nil),             // 65: cloudman.UploadBackupSetToOSSReq
	(*RedisReq)(nil),                            // 66: cloudman.RedisReq
	(*CacheClassReq)(nil),                       // 67: cloudman.CacheClassReq
	(*CacheParamsGroupsReq)(nil),                // 68: cloudman.CacheParamsGroupsReq
	(*OrderID)(nil),                             // 69: cloudman.OrderID
	(*DescribeSecurityGroupReq)(nil),            // 70: cloudman.DescribeSecurityGroupReq
	(*DescribeSecurityGroupByInstancesReq)(nil), // 71: cloudman.DescribeSecurityGroupByInstancesReq
	(*DescribeRulesReq)(nil),                    // 72: cloudman.DescribeRulesReq
	(*UpdateSecurityGroupRuleReq)(nil),          // 73: cloudman.UpdateSecurityGroupRuleReq
	(*GetAccountRegionTagsReq)(nil),             // 74: cloudman.GetAccountRegionTagsReq
	(*GetCustomTagSecurityGroupsReq)(nil),       // 75: cloudman.GetCustomTagSecurityGroupsReq
	(*UpdateSecurityGroupCustomTagReq)(nil),     // 76: cloudman.UpdateSecurityGroupCustomTagReq
	(*UpdateSecurityGroupsReq)(nil),             // 77: cloudman.UpdateSecurityGroupsReq
	(*ExportSecurityGroupReq)(nil),              // 78: cloudman.ExportSecurityGroupReq
	(*ExportSecurityGroupRuleReq)(nil),          // 79: cloudman.ExportSecurityGroupRuleReq
	(*ExportRelateInstancesReq)(nil),            // 80: cloudman.ExportRelateInstancesReq
	(*BatchDeleteSecurityGroupRuleReq)(nil),     // 81: cloudman.BatchDeleteSecurityGroupRuleReq
	(*CreateSecurityGroupRuleReq)(nil),          // 82: cloudman.CreateSecurityGroupRuleReq
	(*DescribeIPWhitelistsReq)(nil),             // 83: cloudman.DescribeIPWhitelistsReq
	(*ModifyIPWhitelistsReq)(nil),               // 84: cloudman.ModifyIPWhitelistsReq
	(*DescribeALBAclsReq)(nil),                  // 85: cloudman.DescribeALBAclsReq
	(*AddALBAclsEntriesReq)(nil),                // 86: cloudman.AddALBAclsEntriesReq
	(*RemoveALBAclsEntriesReq)(nil),             // 87: cloudman.RemoveALBAclsEntriesReq
	(*JoinOrLeaveSecurityGroupReq)(nil),         // 88: cloudman.JoinOrLeaveSecurityGroupReq
	(*DescribeLoadBalancerReq)(nil),             // 89: cloudman.DescribeLoadBalancerReq
	(*ObjIDReq)(nil),                            // 90: cloudman.ObjIDReq
	(*DescribeZonesReq)(nil),                    // 91: cloudman.DescribeZonesReq
	(*ListACLsReq)(nil),                         // 92: cloudman.ListACLsReq
	(*ListCertificatesReq)(nil),                 // 93: cloudman.ListCertificatesReq
	(*LoadBalancerIDsReq)(nil),                  // 94: cloudman.LoadBalancerIDsReq
	(*DescribeEipReq)(nil),                      // 95: cloudman.DescribeEipReq
	(*DescribeEipSegmentReq)(nil),               // 96: cloudman.DescribeEipSegmentReq
	(*DescribeIpamReq)(nil),                     // 97: cloudman.DescribeIpamReq
	(*DescribeIPReq)(nil),                       // 98: cloudman.DescribeIPReq
	(*IPEntity)(nil),                            // 99: cloudman.IPEntity
	(*DeleteIPReq)(nil),                         // 100: cloudman.DeleteIPReq
	(*DescribeByIPsReq)(nil),                    // 101: cloudman.DescribeByIPsReq
	(*DescribeIPGroupReq)(nil),                  // 102: cloudman.DescribeIPGroupReq
	(*CreateIPGroupReq)(nil),                    // 103: cloudman.CreateIPGroupReq
	(*ModifyIPGroupReq)(nil),                    // 104: cloudman.ModifyIPGroupReq
	(*DeleteIPGroupReq)(nil),                    // 105: cloudman.DeleteIPGroupReq
	(*DescribeRAMPolicyReq)(nil),                // 106: cloudman.DescribeRAMPolicyReq
	(*ModifyRAMPolicySourceIPGroupReq)(nil),     // 107: cloudman.ModifyRAMPolicySourceIPGroupReq
	(*SyncRamPolicyReq)(nil),                    // 108: cloudman.SyncRamPolicyReq
	(*PreviewRAMPolicyDeployReq)(nil),           // 109: cloudman.PreviewRAMPolicyDeployReq
	(*RAMPolicyDeployReq)(nil),                  // 110: cloudman.RAMPolicyDeployReq
	(*SyncNewRAMPolicyReq)(nil),                 // 111: cloudman.SyncNewRAMPolicyReq
	(*GetCloudRamPolicyReq)(nil),                // 112: cloudman.GetCloudRamPolicyReq
	(*DescribeDomainReq)(nil),                   // 113: cloudman.DescribeDomainReq
	(*DescribeDomainRecordsReq)(nil),            // 114: cloudman.DescribeDomainRecordsReq
	(*CreateDomainReq)(nil),                     // 115: cloudman.CreateDomainReq
	(*TaskListReq)(nil),                         // 116: cloudman.taskListReq
	(*CreateTaskReq)(nil),                       // 117: cloudman.createTaskReq
	(*UpdateTaskReq)(nil),                       // 118: cloudman.updateTaskReq
	(*TaskLogQueryParams)(nil),                  // 119: cloudman.TaskLogQueryParams
	(*OrderLogReq)(nil),                         // 120: cloudman.OrderLogReq
	(*RegionQueryRequest)(nil),                  // 121: cloudman.regionQueryRequest
	(*RegionDetail)(nil),                        // 122: cloudman.regionDetail
	(*ResTemQueryReq)(nil),                      // 123: cloudman.resTemQueryReq
	(*ResTemCreateReq)(nil),                     // 124: cloudman.resTemCreateReq
	(*CreateResReq)(nil),                        // 125: cloudman.CreateResReq
	(*ResCommonReq)(nil),                        // 126: cloudman.ResCommonReq
	(*UpdateResReq)(nil),                        // 127: cloudman.UpdateResReq
	(*RunOrderReq)(nil),                         // 128: cloudman.RunOrderReq
	(*RunOrderRetryReq)(nil),                    // 129: cloudman.RunOrderRetryReq
	(*CheckInstanceNameSeqReq)(nil),             // 130: cloudman.CheckInstanceNameSeqReq
	(*GenInstanceNameWithRuleReq)(nil),          // 131: cloudman.GenInstanceNameWithRuleReq
	(*OrderTaskReq)(nil),                        // 132: cloudman.OrderTaskReq
	(*CreateGatewayReq)(nil),                    // 133: cloudman.create_gateway_req
	(*GatewayTopoReq)(nil),                      // 134: cloudman.gatewayTopoReq
	(*CreateAgentReq)(nil),                      // 135: cloudman.create_agent_req
	(*GatewayAddressReq)(nil),                   // 136: cloudman.gateway_address_req
	(*AgentCallbackReq)(nil),                    // 137: cloudman.agent_callback_req
	(*Raw)(nil),                                 // 138: cloudman.Raw
	(*ChangeAgentStatusReq)(nil),                // 139: cloudman.ChangeAgentStatusReq
	(*ListAgentReq)(nil),                        // 140: cloudman.list_agent_req
	(*AgentInstallConfig)(nil),                  // 141: cloudman.agent_install_config
	(*ReinstallAgentReq)(nil),                   // 142: cloudman.ReinstallAgentReq
	(*UpdateAgentReq)(nil),                      // 143: cloudman.UpdateAgentReq
	(*GetAgentReq)(nil),                         // 144: cloudman.get_agent_req
	(*CmdbQueryRequest)(nil),                    // 145: cloudman.cmdbQueryRequest
	(*CmdbQueryAssociateRequest)(nil),           // 146: cloudman.cmdbQueryAssociateRequest
	(*CmdbUpdateRequest)(nil),                   // 147: cloudman.cmdbUpdateRequest
	(*CmdbDeleteRequest)(nil),                   // 148: cloudman.cmdbDeleteRequest
	(*CmdbPullInfoRequest)(nil),                 // 149: cloudman.cmdbPullInfoRequest
	(*CmdbDiffRequest)(nil),                     // 150: cloudman.cmdbDiffRequest
	(*CmdbDiffProcessRequest)(nil),              // 151: cloudman.cmdbDiffProcessRequest
	(*CmdbDiffDeleteReq)(nil),                   // 152: cloudman.cmdbDiffDeleteReq
	(*AddNodeRequest)(nil),                      // 153: cloudman.AddNodeRequest
	(*VerifyRequest)(nil),                       // 154: cloudman.VerifyRequest
	(*DescribeDdosBgpReq)(nil),                  // 155: cloudman.DescribeDdosBgpReq
	(*DescribeBandwidthPackageCbwpReq)(nil),     // 156: cloudman.DescribeBandwidthPackageCbwpReq
	(*CheckInstancePermissionReq)(nil),          // 157: cloudman.CheckInstancePermissionReq
	(*GetJumpserverReq)(nil),                    // 158: cloudman.GetJumpserverReq
	(*GetPipelineReq)(nil),                      // 159: cloudman.GetPipelineReq
	(*PreviewParamReq)(nil),                     // 160: cloudman.PreviewParamReq
	(*Result)(nil),                              // 161: cloudman.Result
	(*AccountListRes)(nil),                      // 162: cloudman.accountListRes
	(*RegionResult)(nil),                        // 163: cloudman.regionResult
	(*AccountDetail)(nil),                       // 164: cloudman.accountDetail
	(*RegionListResponse)(nil),                  // 165: cloudman.regionListResponse
	(*PingResult)(nil),                          // 166: cloudman.pingResult
	(*ListAccountParamResp)(nil),                // 167: cloudman.listAccountParamResp
	(*CloudAgentTaskListResp)(nil),              // 168: cloudman.cloudAgentTaskListResp
	(*CloudAgentTaskDetailResp)(nil),            // 169: cloudman.cloudAgentTaskDetailResp
	(*ResChangelistResp)(nil),                   // 170: cloudman.resChangelistResp
	(*RunTaskResponse)(nil),                     // 171: cloudman.runTaskResponse
	(*RecycleListResp)(nil),                     // 172: cloudman.recycleListResp
	(*RecycleInfo)(nil),                         // 173: cloudman.recycleInfo
	(*CommonResourceResp)(nil),                  // 174: cloudman.CommonResourceResp
	(*RecyclePolicyResp)(nil),                   // 175: cloudman.recyclePolicyResp
	(*GroupListResp)(nil),                       // 176: cloudman.groupListResp
	(*GroupInfo)(nil),                           // 177: cloudman.groupInfo
	(*ListCloudResp)(nil),                       // 178: cloudman.listCloudResp
	(*DashBoardAccountResponse)(nil),            // 179: cloudman.dashBoardAccountResponse
	(*DashBoardUserAuthResponse)(nil),           // 180: cloudman.dashBoardUserAuthResponse
	(*DashboardIfWorkOrderUsedResponse)(nil),    // 181: cloudman.dashboardIfWorkOrderUsedResponse
	(*DashboardBackendVersionResponse)(nil),     // 182: cloudman.dashboardBackendVersionResponse
	(*DashboardHostBriefResp)(nil),              // 183: cloudman.dashboardHostBriefResp
	(*DashboardResBriefResp)(nil),               // 184: cloudman.dashboardResBriefResp
	(*DashboardHostDailyCountResp)(nil),         // 185: cloudman.dashboardHostDailyCountResp
	(*DashboardResDailyCountResp)(nil),          // 186: cloudman.dashboardResDailyCountResp
	(*FillSnapshotRes)(nil),                     // 187: cloudman.fillSnapshotRes
	(*ResGroupPolicyInfo)(nil),                  // 188: cloudman.resGroupPolicyInfo
	(*FindResResp)(nil),                         // 189: cloudman.findResResp
	(*CountResResp)(nil),                        // 190: cloudman.countResResp
	(*TagListResp)(nil),                         // 191: cloudman.tagListResp
	(*TagInfo)(nil),                             // 192: cloudman.tagInfo
	(*OptionResp)(nil),                          // 193: cloudman.OptionResp
	(*HostResResp)(nil),                         // 194: cloudman.HostResResp
	(*HostOpsStatusResp)(nil),                   // 195: cloudman.HostOpsStatusResp
	(*ExportXlsxResp)(nil),                      // 196: cloudman.ExportXlsxResp
	(*BatchResult)(nil),                         // 197: cloudman.BatchResult
	(*HostDiskInfoResp)(nil),                    // 198: cloudman.HostDiskInfoResp
	(*MonitorResp)(nil),                         // 199: cloudman.MonitorResp
	(*GraphView)(nil),                           // 200: cloudman.GraphView
	(*GaiaMonitResp)(nil),                       // 201: cloudman.GaiaMonitResp
	(*ZoneRespList)(nil),                        // 202: cloudman.ZoneRespList
	(*KeyPairResp)(nil),                         // 203: cloudman.KeyPairResp
	(*SecurityGroupResp)(nil),                   // 204: cloudman.SecurityGroupResp
	(*NetworkResp)(nil),                         // 205: cloudman.NetworkResp
	(*VSwitchResp)(nil),                         // 206: cloudman.VSwitchResp
	(*ImagesResp)(nil),                          // 207: cloudman.ImagesResp
	(*InstanceTypesResp)(nil),                   // 208: cloudman.InstanceTypesResp
	(*VolumeTypesResp)(nil),                     // 209: cloudman.VolumeTypesResp
	(*ChargeTypesResp)(nil),                     // 210: cloudman.ChargeTypesResp
	(*AutoSnapshotPolicyEXResp)(nil),            // 211: cloudman.AutoSnapshotPolicyEXResp
	(*OSNames)(nil),                             // 212: cloudman.OSNames
	(*HostDashBoardResp)(nil),                   // 213: cloudman.HostDashBoardResp
	(*MysqlClusterResp)(nil),                    // 214: cloudman.MysqlClusterResp
	(*MysqlClusterDetail)(nil),                  // 215: cloudman.MysqlClusterDetail
	(*MysqlDatabases)(nil),                      // 216: cloudman.MysqlDatabases
	(*MysqlAccountsInfo)(nil),                   // 217: cloudman.MysqlAccountsInfo
	(*WhitelistInfo)(nil),                       // 218: cloudman.WhitelistInfo
	(*MysqlEndpoints)(nil),                      // 219: cloudman.MysqlEndpoints
	(*DBTypeResp)(nil),                          // 220: cloudman.DBTypeResp
	(*DBZoneResp)(nil),                          // 221: cloudman.DBZoneResp
	(*DBClassesResp)(nil),                       // 222: cloudman.DBClassesResp
	(*DBParamsGroupsResp)(nil),                  // 223: cloudman.DBParamsGroupsResp
	(*IPWhiteListResp)(nil),                     // 224: cloudman.IPWhiteListResp
	(*UploadBackupSetToOSSResp)(nil),            // 225: cloudman.UploadBackupSetToOSSResp
	(*RedisResp)(nil),                           // 226: cloudman.RedisResp
	(*RedisDetail)(nil),                         // 227: cloudman.RedisDetail
	(*RedisAccounts)(nil),                       // 228: cloudman.RedisAccounts
	(*CacheClassesResp)(nil),                    // 229: cloudman.CacheClassesResp
	(*CacheParamsGroupsResp)(nil),               // 230: cloudman.CacheParamsGroupsResp
	(*GetBackupDetailResp)(nil),                 // 231: cloudman.GetBackupDetailResp
	(*DescribeSecurityGroupRes)(nil),            // 232: cloudman.DescribeSecurityGroupRes
	(*GetAccountRegionTagsRes)(nil),             // 233: cloudman.GetAccountRegionTagsRes
	(*GetCustomTagSecurityGroupsRes)(nil),       // 234: cloudman.GetCustomTagSecurityGroupsRes
	(*DescribeIPWhitelistsResp)(nil),            // 235: cloudman.DescribeIPWhitelistsResp
	(*DescribeALBAclsResp)(nil),                 // 236: cloudman.DescribeALBAclsResp
	(*ModifySecurityGroupResult)(nil),           // 237: cloudman.ModifySecurityGroupResult
	(*DescribeLoadBalancerRes)(nil),             // 238: cloudman.DescribeLoadBalancerRes
	(*DescribeLoadBalancerDetailRes)(nil),       // 239: cloudman.DescribeLoadBalancerDetailRes
	(*DescribeLoadBalancerServerGroupRes)(nil),  // 240: cloudman.DescribeLoadBalancerServerGroupRes
	(*DescribeZonesRes)(nil),                    // 241: cloudman.DescribeZonesRes
	(*ListAClsRes)(nil),                         // 242: cloudman.ListAClsRes
	(*ListCertificatesRes)(nil),                 // 243: cloudman.ListCertificatesRes
	(*DescribeEipRes)(nil),                      // 244: cloudman.DescribeEipRes
	(*DescribeEipSegmentRes)(nil),               // 245: cloudman.DescribeEipSegmentRes
	(*DescribeIpamRes)(nil),                     // 246: cloudman.DescribeIpamRes
	(*DescribeIPRes)(nil),                       // 247: cloudman.DescribeIPRes
	(*DescribeByIPsRes)(nil),                    // 248: cloudman.DescribeByIPsRes
	(*DescribeIPGroupResp)(nil),                 // 249: cloudman.DescribeIPGroupResp
	(*DescribeRAMPolicyResp)(nil),               // 250: cloudman.DescribeRAMPolicyResp
	(*PreviewRAMPolicyDeployResp)(nil),          // 251: cloudman.PreviewRAMPolicyDeployResp
	(*GetCloudRamPolicyResp)(nil),               // 252: cloudman.GetCloudRamPolicyResp
	(*DescribeDomainRes)(nil),                   // 253: cloudman.DescribeDomainRes
	(*TaskListResp)(nil),                        // 254: cloudman.taskListResp
	(*TaskDetail)(nil),                          // 255: cloudman.taskDetail
	(*TaskLogListResponse)(nil),                 // 256: cloudman.TaskLogListResponse
	(*TaskLogResultList)(nil),                   // 257: cloudman.TaskLogResultList
	(*ResTemListResp)(nil),                      // 258: cloudman.resTemListResp
	(*UpdateResourcesResp)(nil),                 // 259: cloudman.UpdateResourcesResp
	(*SalePrice)(nil),                           // 260: cloudman.SalePrice
	(*CheckInstanceNameSeqResp)(nil),            // 261: cloudman.CheckInstanceNameSeqResp
	(*GenInstanceNameWithRuleResp)(nil),         // 262: cloudman.GenInstanceNameWithRuleResp
	(*OrderTaskListResp)(nil),                   // 263: cloudman.OrderTaskListResp
	(*OrderTaskListResult)(nil),                 // 264: cloudman.OrderTaskListResult
	(*Script)(nil),                              // 265: cloudman.script
	(*TopoResult)(nil),                          // 266: cloudman.topo_result
	(*GatewayTopoResult)(nil),                   // 267: cloudman.gatewayTopoResult
	(*ListGatewayResp)(nil),                     // 268: cloudman.list_gateway_resp
	(*ListAgentResp)(nil),                       // 269: cloudman.list_agent_resp
	(*ListAgentInstallConfigResp)(nil),          // 270: cloudman.list_agent_install_config_resp
	(*AgentIDResp)(nil),                         // 271: cloudman.AgentIDResp
	(*ListAgentVersionResp)(nil),                // 272: cloudman.ListAgentVersionResp
	(*SysConfig)(nil),                           // 273: cloudman.SysConfig
	(*CmdbQueryResponse)(nil),                   // 274: cloudman.cmdbQueryResponse
	(*CmdbUpdateResponse)(nil),                  // 275: cloudman.cmdbUpdateResponse
	(*CmdbDeleteResponse)(nil),                  // 276: cloudman.cmdbDeleteResponse
	(*GetTreeResponse)(nil),                     // 277: cloudman.GetTreeResponse
	(*GetEntityResponse)(nil),                   // 278: cloudman.GetEntityResponse
	(*NodeEntityCountResponse)(nil),             // 279: cloudman.NodeEntityCountResponse
	(*AddNodeResponse)(nil),                     // 280: cloudman.AddNodeResponse
	(*VerifyResponse)(nil),                      // 281: cloudman.VerifyResponse
	(*DescribeDdosBgpRes)(nil),                  // 282: cloudman.DescribeDdosBgpRes
	(*DescribeBandwidthPackageCbwpRes)(nil),     // 283: cloudman.DescribeBandwidthPackageCbwpRes
	(*CheckInstancePermissionResp)(nil),         // 284: cloudman.CheckInstancePermissionResp
	(*GetJumpserverResp)(nil),                   // 285: cloudman.GetJumpserverResp
	(*GetPipelineResp)(nil),                     // 286: cloudman.GetPipelineResp
	(*PreviewParamResp)(nil),                    // 287: cloudman.PreviewParamResp
}
var file_op_cloudman_proto_depIdxs = []int32{
	0,   // 0: cloudman.OpCloudmanTakumi.OpCloudmanTakumiInitStatus:input_type -> cloudman.Empty
	1,   // 1: cloudman.Account.AccountList:input_type -> cloudman.accountQueryReq
	1,   // 2: cloudman.Account.AccountListAll:input_type -> cloudman.accountQueryReq
	2,   // 3: cloudman.Account.AccountCreate:input_type -> cloudman.createAccountRequest
	3,   // 4: cloudman.Account.AccountGetAccountRegion:input_type -> cloudman.updateAccountRequest
	4,   // 5: cloudman.Account.AccountUpdateStatus:input_type -> cloudman.updateAccountStatusReq
	5,   // 6: cloudman.Account.AccountRetrieve:input_type -> cloudman.ObjectID
	3,   // 7: cloudman.Account.AccountUpdate:input_type -> cloudman.updateAccountRequest
	5,   // 8: cloudman.Account.AccountDestroy:input_type -> cloudman.ObjectID
	5,   // 9: cloudman.Account.AccountGetBindRegion:input_type -> cloudman.ObjectID
	3,   // 10: cloudman.Account.AccountTestRawPing:input_type -> cloudman.updateAccountRequest
	5,   // 11: cloudman.Account.AccountPing:input_type -> cloudman.ObjectID
	5,   // 12: cloudman.Account.AccountSync:input_type -> cloudman.ObjectID
	6,   // 13: cloudman.Account.AccountCreateParam:input_type -> cloudman.accountParamInfo
	5,   // 14: cloudman.Account.AccountParamInfo:input_type -> cloudman.ObjectID
	7,   // 15: cloudman.Account.AccountListParam:input_type -> cloudman.listAccountParamReq
	6,   // 16: cloudman.Account.AccountUpdateParam:input_type -> cloudman.accountParamInfo
	5,   // 17: cloudman.Account.AccountDelParam:input_type -> cloudman.ObjectID
	8,   // 18: cloudman.CloudAgent.CloudAgentTaskList:input_type -> cloudman.cloudAgentTaskListReq
	9,   // 19: cloudman.CloudAgent.CloudAgentTaskDetail:input_type -> cloudman.IDReq
	10,  // 20: cloudman.CloudAgent.CloudAgentResChangelist:input_type -> cloudman.resChangelistReq
	11,  // 21: cloudman.Inner.InnerRunTaskAPI:input_type -> cloudman.runTaskReq
	12,  // 22: cloudman.Recycle.RecycleList:input_type -> cloudman.recycleQueryReq
	5,   // 23: cloudman.Recycle.RecycleInfo:input_type -> cloudman.ObjectID
	13,  // 24: cloudman.Recycle.RecycleRecover:input_type -> cloudman.RecoverReq
	14,  // 25: cloudman.Recycle.BatchRecover:input_type -> cloudman.ObjectIDs
	15,  // 26: cloudman.Recycle.RecycleDestroy:input_type -> cloudman.RecycleReq
	16,  // 27: cloudman.Recycle.RecycleCreatePolicy:input_type -> cloudman.recyclePolicy
	5,   // 28: cloudman.Recycle.RecycleGetPolicy:input_type -> cloudman.ObjectID
	17,  // 29: cloudman.Recycle.RecycleGetPolicyList:input_type -> cloudman.recyclePolicyReq
	16,  // 30: cloudman.Recycle.RecycleChangePolicy:input_type -> cloudman.recyclePolicy
	5,   // 31: cloudman.Recycle.RecycleDelPolicy:input_type -> cloudman.ObjectID
	0,   // 32: cloudman.Recycle.RecycleRunPolicy:input_type -> cloudman.Empty
	18,  // 33: cloudman.ResourceGroup.ResourceGroupList:input_type -> cloudman.groupQueryReq
	19,  // 34: cloudman.ResourceGroup.ResourceGroupCreate:input_type -> cloudman.groupCreateReq
	5,   // 35: cloudman.ResourceGroup.ResourceGroupInfo:input_type -> cloudman.ObjectID
	19,  // 36: cloudman.ResourceGroup.ResourceGroupUpdate:input_type -> cloudman.groupCreateReq
	5,   // 37: cloudman.ResourceGroup.ResourceGroupDel:input_type -> cloudman.ObjectID
	20,  // 38: cloudman.ResourceGroup.ResourceGroupListCloud:input_type -> cloudman.listCloudReq
	21,  // 39: cloudman.DashBoard.DashBoardGetAccount:input_type -> cloudman.dashBoardAccountRequest
	22,  // 40: cloudman.DashBoard.DashBoardGetUserAuth:input_type -> cloudman.dashBoardUserAuthRequest
	0,   // 41: cloudman.DashBoard.DashBoardGetIfWorkOrderUsed:input_type -> cloudman.Empty
	0,   // 42: cloudman.DashBoard.DashBoardGetBackendVersion:input_type -> cloudman.Empty
	23,  // 43: cloudman.DashBoard.DashBoardGetHostBrief:input_type -> cloudman.dashboardHostBriefReq
	24,  // 44: cloudman.DashBoard.DashBoardGetResBrief:input_type -> cloudman.dashboardResBriefReq
	25,  // 45: cloudman.DashBoard.DashBoardGetHostDailyChart:input_type -> cloudman.dashboardHostDailyCountReq
	26,  // 46: cloudman.DashBoard.DashBoardGetResDailyChart:input_type -> cloudman.dashboardResDailyCountReq
	27,  // 47: cloudman.DashBoard.DashBoardAutoFillSnapshot:input_type -> cloudman.autoFillSnapshotReq
	28,  // 48: cloudman.DashBoard.DashBoardFillSnapshot:input_type -> cloudman.fillSnapshotReq
	29,  // 49: cloudman.ResGroupPolicy.ResGroupPolicyInfo:input_type -> cloudman.groupPolicyInfoReq
	30,  // 50: cloudman.ResGroupPolicy.ResGroupPolicyUpdate:input_type -> cloudman.resGroupPolicyCreateReq
	31,  // 51: cloudman.ResGroupPolicy.ResGroupPolicyFindResource:input_type -> cloudman.findResReq
	32,  // 52: cloudman.ResGroupPolicy.ResGroupPolicyCountResource:input_type -> cloudman.countResReq
	33,  // 53: cloudman.Tags.TagsList:input_type -> cloudman.tagQueryReq
	33,  // 54: cloudman.Tags.TagsListSystem:input_type -> cloudman.tagQueryReq
	34,  // 55: cloudman.Tags.TagsCreate:input_type -> cloudman.tagCreateReq
	5,   // 56: cloudman.Tags.TagsInfo:input_type -> cloudman.ObjectID
	34,  // 57: cloudman.Tags.TagsUpdate:input_type -> cloudman.tagCreateReq
	5,   // 58: cloudman.Tags.TagsDel:input_type -> cloudman.ObjectID
	0,   // 59: cloudman.HostResTemplate.HostResTemplateOption:input_type -> cloudman.Empty
	35,  // 60: cloudman.HostResTemplate.HostResTemplateDescribe:input_type -> cloudman.HostResReq
	36,  // 61: cloudman.HostResTemplate.HostResTemplateDescribeOpsStatus:input_type -> cloudman.Ids
	36,  // 62: cloudman.HostResTemplate.HostResTemplateLockResource:input_type -> cloudman.Ids
	36,  // 63: cloudman.HostResTemplate.HostResTemplateUnlockResource:input_type -> cloudman.Ids
	37,  // 64: cloudman.HostResTemplate.HostResTemplateExportXlsx:input_type -> cloudman.ExportXlsxReq
	38,  // 65: cloudman.HostResTemplate.HostResTemplateImportXlsx:input_type -> cloudman.ImportXlsxReq
	39,  // 66: cloudman.HostResTemplate.HostResTemplateInputResource:input_type -> cloudman.HostResDetail
	5,   // 67: cloudman.HostResTemplate.HostResTemplateGetHostInfo:input_type -> cloudman.ObjectID
	5,   // 68: cloudman.HostResTemplate.HostResTemplateGetHostDiskInfo:input_type -> cloudman.ObjectID
	40,  // 69: cloudman.HostResTemplate.HostResTemplateGetCPUMonitor:input_type -> cloudman.MonitorReq
	40,  // 70: cloudman.HostResTemplate.HostResTemplateGetMemMonitor:input_type -> cloudman.MonitorReq
	40,  // 71: cloudman.HostResTemplate.HostResTemplateGetIOMonitor:input_type -> cloudman.MonitorReq
	40,  // 72: cloudman.HostResTemplate.HostResTemplateGetNetMonitor:input_type -> cloudman.MonitorReq
	40,  // 73: cloudman.HostResTemplate.HostResTemplateGetDiskMonitor:input_type -> cloudman.MonitorReq
	40,  // 74: cloudman.HostResTemplate.HostResTemplateGetUPTimeMonitor:input_type -> cloudman.MonitorReq
	40,  // 75: cloudman.HostResTemplate.HostResTemplateGetGraphView:input_type -> cloudman.MonitorReq
	41,  // 76: cloudman.HostResTemplate.HostResTemplateGetGaiaMonitor:input_type -> cloudman.GaiaMonitReq
	42,  // 77: cloudman.HostResTemplate.HostResTemplateGetGaiaView:input_type -> cloudman.GaiaViewReq
	43,  // 78: cloudman.HostResTemplate.HostResTemplateGetZone:input_type -> cloudman.ZoneReq
	44,  // 79: cloudman.HostResTemplate.HostResTemplateGetKeyPairs:input_type -> cloudman.KeyPairReq
	45,  // 80: cloudman.HostResTemplate.HostResTemplateGetSecurityGroup:input_type -> cloudman.SecurityGroupReq
	46,  // 81: cloudman.HostResTemplate.HostResTemplateGetNetwork:input_type -> cloudman.NetworkReq
	47,  // 82: cloudman.HostResTemplate.HostResTemplateGetVSwitch:input_type -> cloudman.VSwitchReq
	48,  // 83: cloudman.HostResTemplate.HostResTemplateGetImages:input_type -> cloudman.ImagesReq
	49,  // 84: cloudman.HostResTemplate.HostResTemplateGetInstanceTypes:input_type -> cloudman.InstanceTypeReq
	50,  // 85: cloudman.HostResTemplate.HostResTemplateGetVolumeTypes:input_type -> cloudman.VolumeTypeReq
	51,  // 86: cloudman.HostResTemplate.HostResTemplateListChargeType:input_type -> cloudman.ChargeTypeReq
	52,  // 87: cloudman.HostResTemplate.HostResTemplateDescribeAutoSnapshotPolicyEX:input_type -> cloudman.AutoSnapshotPolicyEXReq
	0,   // 88: cloudman.HostResTemplate.HostResTemplateGetExistOSNames:input_type -> cloudman.Empty
	53,  // 89: cloudman.HostResTemplate.HostResTemplateUpdateMonitorStatus:input_type -> cloudman.UpdateMonitorStatusReq
	54,  // 90: cloudman.HostResTemplate.HostResTemplateQuery:input_type -> cloudman.CustomQuery
	55,  // 91: cloudman.HostResTemplate.HostResTemplateDashboard:input_type -> cloudman.HostDashBoardReq
	5,   // 92: cloudman.HostResTemplate.HostResTemplateGetRemoteToken:input_type -> cloudman.ObjectID
	56,  // 93: cloudman.HostResTemplate.HostResTemplateSyncHostInstances:input_type -> cloudman.SyncInstancesReq
	57,  // 94: cloudman.HostResTemplate.HostResTemplateAssignIpv6Address:input_type -> cloudman.AssignIpv6AddressReq
	58,  // 95: cloudman.HostResTemplate.HostResTemplateUnassignIpv6Address:input_type -> cloudman.UnassignIpv6AddressesReq
	0,   // 96: cloudman.Mysql.MysqlOption:input_type -> cloudman.Empty
	59,  // 97: cloudman.Mysql.MysqlDescribeCluster:input_type -> cloudman.MysqlClusterReq
	36,  // 98: cloudman.Mysql.MysqlLockResource:input_type -> cloudman.Ids
	36,  // 99: cloudman.Mysql.MysqlUnlockResource:input_type -> cloudman.Ids
	37,  // 100: cloudman.Mysql.MysqlExportXlsx:input_type -> cloudman.ExportXlsxReq
	38,  // 101: cloudman.Mysql.MysqlImportXlsx:input_type -> cloudman.ImportXlsxReq
	5,   // 102: cloudman.Mysql.MysqlClusterInfo:input_type -> cloudman.ObjectID
	5,   // 103: cloudman.Mysql.MysqlGetDatabases:input_type -> cloudman.ObjectID
	5,   // 104: cloudman.Mysql.MysqlGetAccounts:input_type -> cloudman.ObjectID
	5,   // 105: cloudman.Mysql.MysqlGetWhitelists:input_type -> cloudman.ObjectID
	5,   // 106: cloudman.Mysql.MysqlGetEndpoints:input_type -> cloudman.ObjectID
	60,  // 107: cloudman.Mysql.MysqlGetDBTypes:input_type -> cloudman.DBTypeReq
	61,  // 108: cloudman.Mysql.MysqlDescribeAvailableZone:input_type -> cloudman.DBZoneReq
	62,  // 109: cloudman.Mysql.MysqlDescribeDBClasses:input_type -> cloudman.DBClassesReq
	45,  // 110: cloudman.Mysql.MysqlGetSecurityGroup:input_type -> cloudman.SecurityGroupReq
	46,  // 111: cloudman.Mysql.MysqlGetNetwork:input_type -> cloudman.NetworkReq
	47,  // 112: cloudman.Mysql.MysqlGetVSwitch:input_type -> cloudman.VSwitchReq
	56,  // 113: cloudman.Mysql.MysqlSyncMysqlInstances:input_type -> cloudman.SyncInstancesReq
	63,  // 114: cloudman.Mysql.MysqlDescribeParamsGroups:input_type -> cloudman.DBParamsGroupsReq
	64,  // 115: cloudman.Mysql.MysqlDescribeIPWhiteList:input_type -> cloudman.IPWhiteListReq
	65,  // 116: cloudman.Mysql.MysqlUploadBackupSetToOSS:input_type -> cloudman.UploadBackupSetToOSSReq
	0,   // 117: cloudman.Redis.RedisOption:input_type -> cloudman.Empty
	66,  // 118: cloudman.Redis.RedisDescribe:input_type -> cloudman.RedisReq
	36,  // 119: cloudman.Redis.RedisLockResource:input_type -> cloudman.Ids
	36,  // 120: cloudman.Redis.RedisUnlockResource:input_type -> cloudman.Ids
	37,  // 121: cloudman.Redis.RedisExportXlsx:input_type -> cloudman.ExportXlsxReq
	38,  // 122: cloudman.Redis.RedisImportXlsx:input_type -> cloudman.ImportXlsxReq
	5,   // 123: cloudman.Redis.RedisInfo:input_type -> cloudman.ObjectID
	5,   // 124: cloudman.Redis.RedisGetAccounts:input_type -> cloudman.ObjectID
	5,   // 125: cloudman.Redis.RedisGetWhitelists:input_type -> cloudman.ObjectID
	60,  // 126: cloudman.Redis.RedisGetCacheTypes:input_type -> cloudman.DBTypeReq
	61,  // 127: cloudman.Redis.RedisDescribeAvailableZone:input_type -> cloudman.DBZoneReq
	67,  // 128: cloudman.Redis.RedisDescribeCacheClasses:input_type -> cloudman.CacheClassReq
	68,  // 129: cloudman.Redis.RedisDescribeParamsGroups:input_type -> cloudman.CacheParamsGroupsReq
	45,  // 130: cloudman.Redis.RedisGetSecurityGroup:input_type -> cloudman.SecurityGroupReq
	46,  // 131: cloudman.Redis.RedisGetNetwork:input_type -> cloudman.NetworkReq
	47,  // 132: cloudman.Redis.RedisGetVSwitch:input_type -> cloudman.VSwitchReq
	56,  // 133: cloudman.Redis.RedisSyncRedisInstances:input_type -> cloudman.SyncInstancesReq
	69,  // 134: cloudman.Redis.RedisGetBackupDetail:input_type -> cloudman.OrderID
	70,  // 135: cloudman.SecurityGroup.SecurityGroupDescribe:input_type -> cloudman.DescribeSecurityGroupReq
	71,  // 136: cloudman.SecurityGroup.SecurityGroupDescribeByInstances:input_type -> cloudman.DescribeSecurityGroupByInstancesReq
	72,  // 137: cloudman.SecurityGroup.SecurityGroupDescribeRules:input_type -> cloudman.DescribeRulesReq
	73,  // 138: cloudman.SecurityGroup.SecurityGroupUpdateSecurityGroupRule:input_type -> cloudman.UpdateSecurityGroupRuleReq
	74,  // 139: cloudman.SecurityGroup.SecurityGroupGetAccountRegionTags:input_type -> cloudman.GetAccountRegionTagsReq
	75,  // 140: cloudman.SecurityGroup.SecurityGroupGetCustomTagSecurityGroups:input_type -> cloudman.GetCustomTagSecurityGroupsReq
	76,  // 141: cloudman.SecurityGroup.SecurityGroupUpdateSecurityGroupCustomTag:input_type -> cloudman.UpdateSecurityGroupCustomTagReq
	77,  // 142: cloudman.SecurityGroup.SecurityGroupUpdateSecurityGroups:input_type -> cloudman.UpdateSecurityGroupsReq
	76,  // 143: cloudman.SecurityGroup.SecurityGroupCleanupSecurityGroups:input_type -> cloudman.UpdateSecurityGroupCustomTagReq
	78,  // 144: cloudman.SecurityGroup.SecurityGroupExportSecurityGroup:input_type -> cloudman.ExportSecurityGroupReq
	79,  // 145: cloudman.SecurityGroup.SecurityGroupExportSecurityGroupRule:input_type -> cloudman.ExportSecurityGroupRuleReq
	80,  // 146: cloudman.SecurityGroup.SecurityGroupExportRelateInstances:input_type -> cloudman.ExportRelateInstancesReq
	81,  // 147: cloudman.SecurityGroup.SecurityGroupBatchDeleteSecurityGroupRule:input_type -> cloudman.BatchDeleteSecurityGroupRuleReq
	82,  // 148: cloudman.SecurityGroup.SecurityGroupCreateSecurityGroupRule:input_type -> cloudman.CreateSecurityGroupRuleReq
	83,  // 149: cloudman.SecurityGroup.SecurityGroupDescribeIPWhitelists:input_type -> cloudman.DescribeIPWhitelistsReq
	84,  // 150: cloudman.SecurityGroup.SecurityGroupModifyIPWhitelists:input_type -> cloudman.ModifyIPWhitelistsReq
	85,  // 151: cloudman.SecurityGroup.SecurityGroupDescribeALBAcls:input_type -> cloudman.DescribeALBAclsReq
	86,  // 152: cloudman.SecurityGroup.SecurityGroupAddALBAclsEntries:input_type -> cloudman.AddALBAclsEntriesReq
	87,  // 153: cloudman.SecurityGroup.SecurityGroupRemoveALBAclsEntries:input_type -> cloudman.RemoveALBAclsEntriesReq
	88,  // 154: cloudman.SecurityGroup.SecurityGroupJoinSecurityGroup:input_type -> cloudman.JoinOrLeaveSecurityGroupReq
	88,  // 155: cloudman.SecurityGroup.SecurityGroupLeaveSecurityGroup:input_type -> cloudman.JoinOrLeaveSecurityGroupReq
	89,  // 156: cloudman.LoadBalancer.LoadBalancerDescribe:input_type -> cloudman.DescribeLoadBalancerReq
	90,  // 157: cloudman.LoadBalancer.LoadBalancerDescribeDetail:input_type -> cloudman.ObjIDReq
	9,   // 158: cloudman.LoadBalancer.LoadBalancerDescribeServerGroup:input_type -> cloudman.IDReq
	91,  // 159: cloudman.LoadBalancer.LoadBalancerDescribeZones:input_type -> cloudman.DescribeZonesReq
	92,  // 160: cloudman.LoadBalancer.LoadBalancerListACLs:input_type -> cloudman.ListACLsReq
	93,  // 161: cloudman.LoadBalancer.LoadBalancerListCertificates:input_type -> cloudman.ListCertificatesReq
	94,  // 162: cloudman.LoadBalancer.LoadBalancerCleanupLoadBalancer:input_type -> cloudman.LoadBalancerIDsReq
	95,  // 163: cloudman.Eip.EipDescribe:input_type -> cloudman.DescribeEipReq
	96,  // 164: cloudman.Eip.EipDescribeSegment:input_type -> cloudman.DescribeEipSegmentReq
	97,  // 165: cloudman.Eip.EipDescribeIpam:input_type -> cloudman.DescribeIpamReq
	98,  // 166: cloudman.IP.IPDescribe:input_type -> cloudman.DescribeIPReq
	99,  // 167: cloudman.IP.IPCreateCustomIP:input_type -> cloudman.IPEntity
	100, // 168: cloudman.IP.IPDeleteIP:input_type -> cloudman.DeleteIPReq
	101, // 169: cloudman.IP.IPDescribeByIPs:input_type -> cloudman.DescribeByIPsReq
	102, // 170: cloudman.IP.IPDescribeIPGroup:input_type -> cloudman.DescribeIPGroupReq
	103, // 171: cloudman.IP.IPCreateIPGroup:input_type -> cloudman.CreateIPGroupReq
	104, // 172: cloudman.IP.IPModifyIPGroup:input_type -> cloudman.ModifyIPGroupReq
	105, // 173: cloudman.IP.IPDeleteIPGroup:input_type -> cloudman.DeleteIPGroupReq
	106, // 174: cloudman.RAM.RAMDescribeRAMPolicy:input_type -> cloudman.DescribeRAMPolicyReq
	107, // 175: cloudman.RAM.RAMModifyRAMPolicySourceIPGroup:input_type -> cloudman.ModifyRAMPolicySourceIPGroupReq
	108, // 176: cloudman.RAM.RAMSyncRAMPolicy:input_type -> cloudman.SyncRamPolicyReq
	109, // 177: cloudman.RAM.RAMPreviewRAMPolicyDeploy:input_type -> cloudman.PreviewRAMPolicyDeployReq
	110, // 178: cloudman.RAM.RAMRAMPolicyDeploy:input_type -> cloudman.RAMPolicyDeployReq
	111, // 179: cloudman.RAM.RAMSyncNewRAMPolicy:input_type -> cloudman.SyncNewRAMPolicyReq
	112, // 180: cloudman.RAM.RAMGetCloudRamPolicy:input_type -> cloudman.GetCloudRamPolicyReq
	113, // 181: cloudman.Domain.DomainDescribe:input_type -> cloudman.DescribeDomainReq
	114, // 182: cloudman.Domain.DomainDescribeRecords:input_type -> cloudman.DescribeDomainRecordsReq
	0,   // 183: cloudman.Domain.DomainSyncDnspod:input_type -> cloudman.Empty
	115, // 184: cloudman.Domain.DomainCreateDomain:input_type -> cloudman.CreateDomainReq
	116, // 185: cloudman.Task.TaskList:input_type -> cloudman.taskListReq
	11,  // 186: cloudman.Task.TaskRetrieve:input_type -> cloudman.runTaskReq
	117, // 187: cloudman.Task.TaskCreate:input_type -> cloudman.createTaskReq
	11,  // 188: cloudman.Task.TaskRunTaskAPI:input_type -> cloudman.runTaskReq
	118, // 189: cloudman.Task.TaskUpdate:input_type -> cloudman.updateTaskReq
	5,   // 190: cloudman.Task.TaskDelete:input_type -> cloudman.ObjectID
	119, // 191: cloudman.TaskLog.TaskLogListTaskLog:input_type -> cloudman.TaskLogQueryParams
	120, // 192: cloudman.TaskLog.TaskLogRetrieveTaskLog:input_type -> cloudman.OrderLogReq
	121, // 193: cloudman.Region.RegionList:input_type -> cloudman.regionQueryRequest
	5,   // 194: cloudman.Region.RegionInfo:input_type -> cloudman.ObjectID
	122, // 195: cloudman.Region.RegionCreate:input_type -> cloudman.regionDetail
	123, // 196: cloudman.ResTemplate.ResTemplateList:input_type -> cloudman.resTemQueryReq
	124, // 197: cloudman.ResTemplate.ResTemplateCreate:input_type -> cloudman.resTemCreateReq
	5,   // 198: cloudman.ResTemplate.ResTemplateInfo:input_type -> cloudman.ObjectID
	124, // 199: cloudman.ResTemplate.ResTemplateUpdate:input_type -> cloudman.resTemCreateReq
	5,   // 200: cloudman.ResTemplate.ResTemplateDel:input_type -> cloudman.ObjectID
	125, // 201: cloudman.ResTemplate.ResTemplateCreateResource:input_type -> cloudman.CreateResReq
	126, // 202: cloudman.ResTemplate.ResTemplateUpdateResource:input_type -> cloudman.ResCommonReq
	127, // 203: cloudman.ResTemplate.ResTemplateUpdateResources:input_type -> cloudman.UpdateResReq
	128, // 204: cloudman.ResTemplate.ResTemplateRunOrder:input_type -> cloudman.RunOrderReq
	129, // 205: cloudman.ResTemplate.ResTemplateRunOrderRetry:input_type -> cloudman.RunOrderRetryReq
	124, // 206: cloudman.ResTemplate.ResTemplateGetSalePrice:input_type -> cloudman.resTemCreateReq
	130, // 207: cloudman.ResTemplate.ResTemplateCheckInstanceNameSeq:input_type -> cloudman.CheckInstanceNameSeqReq
	131, // 208: cloudman.ResTemplate.ResTemplateGenInstanceNameWithRule:input_type -> cloudman.GenInstanceNameWithRuleReq
	132, // 209: cloudman.ResAudit.ResAuditGetOrderList:input_type -> cloudman.OrderTaskReq
	120, // 210: cloudman.ResAudit.ResAuditGetOrderLog:input_type -> cloudman.OrderLogReq
	9,   // 211: cloudman.ResAudit.ResAuditGetOrder:input_type -> cloudman.IDReq
	133, // 212: cloudman.CloudGateway.CloudGatewayCreateGateway:input_type -> cloudman.create_gateway_req
	133, // 213: cloudman.CloudGateway.CloudGatewayGetGatewayScript:input_type -> cloudman.create_gateway_req
	133, // 214: cloudman.CloudGateway.CreateAgent:input_type -> cloudman.create_gateway_req
	0,   // 215: cloudman.CloudGateway.CloudGatewayGettopo:input_type -> cloudman.Empty
	134, // 216: cloudman.CloudGateway.CloudGatewayGetGatewayTopo:input_type -> cloudman.gatewayTopoReq
	0,   // 217: cloudman.CloudGateway.CloudGatewayListGateway:input_type -> cloudman.Empty
	135, // 218: cloudman.CloudGateway.CreateAgentScript:input_type -> cloudman.create_agent_req
	135, // 219: cloudman.CloudGateway.CloudGatewayGetAgentScript:input_type -> cloudman.create_agent_req
	136, // 220: cloudman.CloudGateway.CloudGatewayInsertGateAddress:input_type -> cloudman.gateway_address_req
	0,   // 221: cloudman.CloudGateway.CloudGatewayGetGateAddress:input_type -> cloudman.Empty
	137, // 222: cloudman.CloudGateway.CloudGatewayChangeAgent:input_type -> cloudman.agent_callback_req
	138, // 223: cloudman.CloudGateway.CloudGatewayChangeAgents:input_type -> cloudman.Raw
	139, // 224: cloudman.CloudGateway.CloudGatewayChangeAgentStatus:input_type -> cloudman.ChangeAgentStatusReq
	139, // 225: cloudman.CloudGateway.CloudGatewayDeleteAgent:input_type -> cloudman.ChangeAgentStatusReq
	140, // 226: cloudman.CloudGateway.CloudGatewayListAgent:input_type -> cloudman.list_agent_req
	5,   // 227: cloudman.CloudGateway.CloudGatewayRawAgentInfo:input_type -> cloudman.ObjectID
	140, // 228: cloudman.CloudGateway.CloudGatewayExportXlsx:input_type -> cloudman.list_agent_req
	0,   // 229: cloudman.CloudGateway.CloudGatewayListAgentInstallConfig:input_type -> cloudman.Empty
	141, // 230: cloudman.CloudGateway.CloudGatewayUpdateAgentInstallConfig:input_type -> cloudman.agent_install_config
	142, // 231: cloudman.CloudGateway.CloudGatewayReinstallAgent:input_type -> cloudman.ReinstallAgentReq
	143, // 232: cloudman.CloudGateway.CloudGatewayUpdateAgent:input_type -> cloudman.UpdateAgentReq
	144, // 233: cloudman.CloudGateway.CloudGatewayGetHostAgentID:input_type -> cloudman.get_agent_req
	0,   // 234: cloudman.CloudGateway.CloudGatewayListAgentVersion:input_type -> cloudman.Empty
	0,   // 235: cloudman.System.SystemGetConfig:input_type -> cloudman.Empty
	0,   // 236: cloudman.System.SystemWecomDailyNotify:input_type -> cloudman.Empty
	0,   // 237: cloudman.System.SystemSecurityGroupRuleDailyNotify:input_type -> cloudman.Empty
	145, // 238: cloudman.Cmdb.CmdbQuery:input_type -> cloudman.cmdbQueryRequest
	146, // 239: cloudman.Cmdb.CmdbQueryAssociate:input_type -> cloudman.cmdbQueryAssociateRequest
	147, // 240: cloudman.Cmdb.CmdbManualUpdate:input_type -> cloudman.cmdbUpdateRequest
	148, // 241: cloudman.Cmdb.CmdbDelete:input_type -> cloudman.cmdbDeleteRequest
	149, // 242: cloudman.Cmdb.CmdbPullInfo:input_type -> cloudman.cmdbPullInfoRequest
	150, // 243: cloudman.Cmdb.CmdbDiff:input_type -> cloudman.cmdbDiffRequest
	151, // 244: cloudman.Cmdb.CmdbDiffProcess:input_type -> cloudman.cmdbDiffProcessRequest
	152, // 245: cloudman.Cmdb.CmdbDiffDelete:input_type -> cloudman.cmdbDiffDeleteReq
	152, // 246: cloudman.Cmdb.CmdbDiffUpdate:input_type -> cloudman.cmdbDiffDeleteReq
	138, // 247: cloudman.ServiceTree.ServiceTreeGetTree:input_type -> cloudman.Raw
	138, // 248: cloudman.ServiceTree.ServiceTreeGetEntity:input_type -> cloudman.Raw
	138, // 249: cloudman.ServiceTree.ServiceTreeNodeEntityCount:input_type -> cloudman.Raw
	153, // 250: cloudman.ServiceTree.ServiceTreeAddNode:input_type -> cloudman.AddNodeRequest
	154, // 251: cloudman.ServiceTree.ServiceTreeVerify:input_type -> cloudman.VerifyRequest
	155, // 252: cloudman.Ddos.DdosDescribeDdosBgp:input_type -> cloudman.DescribeDdosBgpReq
	156, // 253: cloudman.BandwidthPackage.BandwidthPackageDescribeCbwp:input_type -> cloudman.DescribeBandwidthPackageCbwpReq
	0,   // 254: cloudman.Debug.DebugFixResTagBind:input_type -> cloudman.Empty
	0,   // 255: cloudman.Debug.DebugTest:input_type -> cloudman.Empty
	157, // 256: cloudman.Permission.PermissionCheckInstancePermission:input_type -> cloudman.CheckInstancePermissionReq
	158, // 257: cloudman.InitResource.InitResourceGetJumpserver:input_type -> cloudman.GetJumpserverReq
	159, // 258: cloudman.InitResource.InitResourceGetPipeline:input_type -> cloudman.GetPipelineReq
	160, // 259: cloudman.InitResource.InitResourcePreviewParam:input_type -> cloudman.PreviewParamReq
	161, // 260: cloudman.OpCloudmanTakumi.OpCloudmanTakumiInitStatus:output_type -> cloudman.Result
	162, // 261: cloudman.Account.AccountList:output_type -> cloudman.accountListRes
	162, // 262: cloudman.Account.AccountListAll:output_type -> cloudman.accountListRes
	161, // 263: cloudman.Account.AccountCreate:output_type -> cloudman.Result
	163, // 264: cloudman.Account.AccountGetAccountRegion:output_type -> cloudman.regionResult
	161, // 265: cloudman.Account.AccountUpdateStatus:output_type -> cloudman.Result
	164, // 266: cloudman.Account.AccountRetrieve:output_type -> cloudman.accountDetail
	161, // 267: cloudman.Account.AccountUpdate:output_type -> cloudman.Result
	161, // 268: cloudman.Account.AccountDestroy:output_type -> cloudman.Result
	165, // 269: cloudman.Account.AccountGetBindRegion:output_type -> cloudman.regionListResponse
	166, // 270: cloudman.Account.AccountTestRawPing:output_type -> cloudman.pingResult
	166, // 271: cloudman.Account.AccountPing:output_type -> cloudman.pingResult
	161, // 272: cloudman.Account.AccountSync:output_type -> cloudman.Result
	161, // 273: cloudman.Account.AccountCreateParam:output_type -> cloudman.Result
	6,   // 274: cloudman.Account.AccountParamInfo:output_type -> cloudman.accountParamInfo
	167, // 275: cloudman.Account.AccountListParam:output_type -> cloudman.listAccountParamResp
	161, // 276: cloudman.Account.AccountUpdateParam:output_type -> cloudman.Result
	161, // 277: cloudman.Account.AccountDelParam:output_type -> cloudman.Result
	168, // 278: cloudman.CloudAgent.CloudAgentTaskList:output_type -> cloudman.cloudAgentTaskListResp
	169, // 279: cloudman.CloudAgent.CloudAgentTaskDetail:output_type -> cloudman.cloudAgentTaskDetailResp
	170, // 280: cloudman.CloudAgent.CloudAgentResChangelist:output_type -> cloudman.resChangelistResp
	171, // 281: cloudman.Inner.InnerRunTaskAPI:output_type -> cloudman.runTaskResponse
	172, // 282: cloudman.Recycle.RecycleList:output_type -> cloudman.recycleListResp
	173, // 283: cloudman.Recycle.RecycleInfo:output_type -> cloudman.recycleInfo
	174, // 284: cloudman.Recycle.RecycleRecover:output_type -> cloudman.CommonResourceResp
	161, // 285: cloudman.Recycle.BatchRecover:output_type -> cloudman.Result
	161, // 286: cloudman.Recycle.RecycleDestroy:output_type -> cloudman.Result
	161, // 287: cloudman.Recycle.RecycleCreatePolicy:output_type -> cloudman.Result
	16,  // 288: cloudman.Recycle.RecycleGetPolicy:output_type -> cloudman.recyclePolicy
	175, // 289: cloudman.Recycle.RecycleGetPolicyList:output_type -> cloudman.recyclePolicyResp
	161, // 290: cloudman.Recycle.RecycleChangePolicy:output_type -> cloudman.Result
	161, // 291: cloudman.Recycle.RecycleDelPolicy:output_type -> cloudman.Result
	161, // 292: cloudman.Recycle.RecycleRunPolicy:output_type -> cloudman.Result
	176, // 293: cloudman.ResourceGroup.ResourceGroupList:output_type -> cloudman.groupListResp
	161, // 294: cloudman.ResourceGroup.ResourceGroupCreate:output_type -> cloudman.Result
	177, // 295: cloudman.ResourceGroup.ResourceGroupInfo:output_type -> cloudman.groupInfo
	161, // 296: cloudman.ResourceGroup.ResourceGroupUpdate:output_type -> cloudman.Result
	161, // 297: cloudman.ResourceGroup.ResourceGroupDel:output_type -> cloudman.Result
	178, // 298: cloudman.ResourceGroup.ResourceGroupListCloud:output_type -> cloudman.listCloudResp
	179, // 299: cloudman.DashBoard.DashBoardGetAccount:output_type -> cloudman.dashBoardAccountResponse
	180, // 300: cloudman.DashBoard.DashBoardGetUserAuth:output_type -> cloudman.dashBoardUserAuthResponse
	181, // 301: cloudman.DashBoard.DashBoardGetIfWorkOrderUsed:output_type -> cloudman.dashboardIfWorkOrderUsedResponse
	182, // 302: cloudman.DashBoard.DashBoardGetBackendVersion:output_type -> cloudman.dashboardBackendVersionResponse
	183, // 303: cloudman.DashBoard.DashBoardGetHostBrief:output_type -> cloudman.dashboardHostBriefResp
	184, // 304: cloudman.DashBoard.DashBoardGetResBrief:output_type -> cloudman.dashboardResBriefResp
	185, // 305: cloudman.DashBoard.DashBoardGetHostDailyChart:output_type -> cloudman.dashboardHostDailyCountResp
	186, // 306: cloudman.DashBoard.DashBoardGetResDailyChart:output_type -> cloudman.dashboardResDailyCountResp
	161, // 307: cloudman.DashBoard.DashBoardAutoFillSnapshot:output_type -> cloudman.Result
	187, // 308: cloudman.DashBoard.DashBoardFillSnapshot:output_type -> cloudman.fillSnapshotRes
	188, // 309: cloudman.ResGroupPolicy.ResGroupPolicyInfo:output_type -> cloudman.resGroupPolicyInfo
	161, // 310: cloudman.ResGroupPolicy.ResGroupPolicyUpdate:output_type -> cloudman.Result
	189, // 311: cloudman.ResGroupPolicy.ResGroupPolicyFindResource:output_type -> cloudman.findResResp
	190, // 312: cloudman.ResGroupPolicy.ResGroupPolicyCountResource:output_type -> cloudman.countResResp
	191, // 313: cloudman.Tags.TagsList:output_type -> cloudman.tagListResp
	191, // 314: cloudman.Tags.TagsListSystem:output_type -> cloudman.tagListResp
	161, // 315: cloudman.Tags.TagsCreate:output_type -> cloudman.Result
	192, // 316: cloudman.Tags.TagsInfo:output_type -> cloudman.tagInfo
	161, // 317: cloudman.Tags.TagsUpdate:output_type -> cloudman.Result
	161, // 318: cloudman.Tags.TagsDel:output_type -> cloudman.Result
	193, // 319: cloudman.HostResTemplate.HostResTemplateOption:output_type -> cloudman.OptionResp
	194, // 320: cloudman.HostResTemplate.HostResTemplateDescribe:output_type -> cloudman.HostResResp
	195, // 321: cloudman.HostResTemplate.HostResTemplateDescribeOpsStatus:output_type -> cloudman.HostOpsStatusResp
	161, // 322: cloudman.HostResTemplate.HostResTemplateLockResource:output_type -> cloudman.Result
	161, // 323: cloudman.HostResTemplate.HostResTemplateUnlockResource:output_type -> cloudman.Result
	196, // 324: cloudman.HostResTemplate.HostResTemplateExportXlsx:output_type -> cloudman.ExportXlsxResp
	197, // 325: cloudman.HostResTemplate.HostResTemplateImportXlsx:output_type -> cloudman.BatchResult
	161, // 326: cloudman.HostResTemplate.HostResTemplateInputResource:output_type -> cloudman.Result
	39,  // 327: cloudman.HostResTemplate.HostResTemplateGetHostInfo:output_type -> cloudman.HostResDetail
	198, // 328: cloudman.HostResTemplate.HostResTemplateGetHostDiskInfo:output_type -> cloudman.HostDiskInfoResp
	199, // 329: cloudman.HostResTemplate.HostResTemplateGetCPUMonitor:output_type -> cloudman.MonitorResp
	199, // 330: cloudman.HostResTemplate.HostResTemplateGetMemMonitor:output_type -> cloudman.MonitorResp
	199, // 331: cloudman.HostResTemplate.HostResTemplateGetIOMonitor:output_type -> cloudman.MonitorResp
	199, // 332: cloudman.HostResTemplate.HostResTemplateGetNetMonitor:output_type -> cloudman.MonitorResp
	199, // 333: cloudman.HostResTemplate.HostResTemplateGetDiskMonitor:output_type -> cloudman.MonitorResp
	199, // 334: cloudman.HostResTemplate.HostResTemplateGetUPTimeMonitor:output_type -> cloudman.MonitorResp
	200, // 335: cloudman.HostResTemplate.HostResTemplateGetGraphView:output_type -> cloudman.GraphView
	201, // 336: cloudman.HostResTemplate.HostResTemplateGetGaiaMonitor:output_type -> cloudman.GaiaMonitResp
	200, // 337: cloudman.HostResTemplate.HostResTemplateGetGaiaView:output_type -> cloudman.GraphView
	202, // 338: cloudman.HostResTemplate.HostResTemplateGetZone:output_type -> cloudman.ZoneRespList
	203, // 339: cloudman.HostResTemplate.HostResTemplateGetKeyPairs:output_type -> cloudman.KeyPairResp
	204, // 340: cloudman.HostResTemplate.HostResTemplateGetSecurityGroup:output_type -> cloudman.SecurityGroupResp
	205, // 341: cloudman.HostResTemplate.HostResTemplateGetNetwork:output_type -> cloudman.NetworkResp
	206, // 342: cloudman.HostResTemplate.HostResTemplateGetVSwitch:output_type -> cloudman.VSwitchResp
	207, // 343: cloudman.HostResTemplate.HostResTemplateGetImages:output_type -> cloudman.ImagesResp
	208, // 344: cloudman.HostResTemplate.HostResTemplateGetInstanceTypes:output_type -> cloudman.InstanceTypesResp
	209, // 345: cloudman.HostResTemplate.HostResTemplateGetVolumeTypes:output_type -> cloudman.VolumeTypesResp
	210, // 346: cloudman.HostResTemplate.HostResTemplateListChargeType:output_type -> cloudman.ChargeTypesResp
	211, // 347: cloudman.HostResTemplate.HostResTemplateDescribeAutoSnapshotPolicyEX:output_type -> cloudman.AutoSnapshotPolicyEXResp
	212, // 348: cloudman.HostResTemplate.HostResTemplateGetExistOSNames:output_type -> cloudman.OSNames
	161, // 349: cloudman.HostResTemplate.HostResTemplateUpdateMonitorStatus:output_type -> cloudman.Result
	194, // 350: cloudman.HostResTemplate.HostResTemplateQuery:output_type -> cloudman.HostResResp
	213, // 351: cloudman.HostResTemplate.HostResTemplateDashboard:output_type -> cloudman.HostDashBoardResp
	161, // 352: cloudman.HostResTemplate.HostResTemplateGetRemoteToken:output_type -> cloudman.Result
	161, // 353: cloudman.HostResTemplate.HostResTemplateSyncHostInstances:output_type -> cloudman.Result
	161, // 354: cloudman.HostResTemplate.HostResTemplateAssignIpv6Address:output_type -> cloudman.Result
	161, // 355: cloudman.HostResTemplate.HostResTemplateUnassignIpv6Address:output_type -> cloudman.Result
	193, // 356: cloudman.Mysql.MysqlOption:output_type -> cloudman.OptionResp
	214, // 357: cloudman.Mysql.MysqlDescribeCluster:output_type -> cloudman.MysqlClusterResp
	161, // 358: cloudman.Mysql.MysqlLockResource:output_type -> cloudman.Result
	161, // 359: cloudman.Mysql.MysqlUnlockResource:output_type -> cloudman.Result
	196, // 360: cloudman.Mysql.MysqlExportXlsx:output_type -> cloudman.ExportXlsxResp
	197, // 361: cloudman.Mysql.MysqlImportXlsx:output_type -> cloudman.BatchResult
	215, // 362: cloudman.Mysql.MysqlClusterInfo:output_type -> cloudman.MysqlClusterDetail
	216, // 363: cloudman.Mysql.MysqlGetDatabases:output_type -> cloudman.MysqlDatabases
	217, // 364: cloudman.Mysql.MysqlGetAccounts:output_type -> cloudman.MysqlAccountsInfo
	218, // 365: cloudman.Mysql.MysqlGetWhitelists:output_type -> cloudman.WhitelistInfo
	219, // 366: cloudman.Mysql.MysqlGetEndpoints:output_type -> cloudman.MysqlEndpoints
	220, // 367: cloudman.Mysql.MysqlGetDBTypes:output_type -> cloudman.DBTypeResp
	221, // 368: cloudman.Mysql.MysqlDescribeAvailableZone:output_type -> cloudman.DBZoneResp
	222, // 369: cloudman.Mysql.MysqlDescribeDBClasses:output_type -> cloudman.DBClassesResp
	204, // 370: cloudman.Mysql.MysqlGetSecurityGroup:output_type -> cloudman.SecurityGroupResp
	205, // 371: cloudman.Mysql.MysqlGetNetwork:output_type -> cloudman.NetworkResp
	206, // 372: cloudman.Mysql.MysqlGetVSwitch:output_type -> cloudman.VSwitchResp
	161, // 373: cloudman.Mysql.MysqlSyncMysqlInstances:output_type -> cloudman.Result
	223, // 374: cloudman.Mysql.MysqlDescribeParamsGroups:output_type -> cloudman.DBParamsGroupsResp
	224, // 375: cloudman.Mysql.MysqlDescribeIPWhiteList:output_type -> cloudman.IPWhiteListResp
	225, // 376: cloudman.Mysql.MysqlUploadBackupSetToOSS:output_type -> cloudman.UploadBackupSetToOSSResp
	193, // 377: cloudman.Redis.RedisOption:output_type -> cloudman.OptionResp
	226, // 378: cloudman.Redis.RedisDescribe:output_type -> cloudman.RedisResp
	161, // 379: cloudman.Redis.RedisLockResource:output_type -> cloudman.Result
	161, // 380: cloudman.Redis.RedisUnlockResource:output_type -> cloudman.Result
	196, // 381: cloudman.Redis.RedisExportXlsx:output_type -> cloudman.ExportXlsxResp
	197, // 382: cloudman.Redis.RedisImportXlsx:output_type -> cloudman.BatchResult
	227, // 383: cloudman.Redis.RedisInfo:output_type -> cloudman.RedisDetail
	228, // 384: cloudman.Redis.RedisGetAccounts:output_type -> cloudman.RedisAccounts
	218, // 385: cloudman.Redis.RedisGetWhitelists:output_type -> cloudman.WhitelistInfo
	220, // 386: cloudman.Redis.RedisGetCacheTypes:output_type -> cloudman.DBTypeResp
	221, // 387: cloudman.Redis.RedisDescribeAvailableZone:output_type -> cloudman.DBZoneResp
	229, // 388: cloudman.Redis.RedisDescribeCacheClasses:output_type -> cloudman.CacheClassesResp
	230, // 389: cloudman.Redis.RedisDescribeParamsGroups:output_type -> cloudman.CacheParamsGroupsResp
	204, // 390: cloudman.Redis.RedisGetSecurityGroup:output_type -> cloudman.SecurityGroupResp
	205, // 391: cloudman.Redis.RedisGetNetwork:output_type -> cloudman.NetworkResp
	206, // 392: cloudman.Redis.RedisGetVSwitch:output_type -> cloudman.VSwitchResp
	161, // 393: cloudman.Redis.RedisSyncRedisInstances:output_type -> cloudman.Result
	231, // 394: cloudman.Redis.RedisGetBackupDetail:output_type -> cloudman.GetBackupDetailResp
	232, // 395: cloudman.SecurityGroup.SecurityGroupDescribe:output_type -> cloudman.DescribeSecurityGroupRes
	232, // 396: cloudman.SecurityGroup.SecurityGroupDescribeByInstances:output_type -> cloudman.DescribeSecurityGroupRes
	232, // 397: cloudman.SecurityGroup.SecurityGroupDescribeRules:output_type -> cloudman.DescribeSecurityGroupRes
	161, // 398: cloudman.SecurityGroup.SecurityGroupUpdateSecurityGroupRule:output_type -> cloudman.Result
	233, // 399: cloudman.SecurityGroup.SecurityGroupGetAccountRegionTags:output_type -> cloudman.GetAccountRegionTagsRes
	234, // 400: cloudman.SecurityGroup.SecurityGroupGetCustomTagSecurityGroups:output_type -> cloudman.GetCustomTagSecurityGroupsRes
	161, // 401: cloudman.SecurityGroup.SecurityGroupUpdateSecurityGroupCustomTag:output_type -> cloudman.Result
	161, // 402: cloudman.SecurityGroup.SecurityGroupUpdateSecurityGroups:output_type -> cloudman.Result
	161, // 403: cloudman.SecurityGroup.SecurityGroupCleanupSecurityGroups:output_type -> cloudman.Result
	196, // 404: cloudman.SecurityGroup.SecurityGroupExportSecurityGroup:output_type -> cloudman.ExportXlsxResp
	196, // 405: cloudman.SecurityGroup.SecurityGroupExportSecurityGroupRule:output_type -> cloudman.ExportXlsxResp
	196, // 406: cloudman.SecurityGroup.SecurityGroupExportRelateInstances:output_type -> cloudman.ExportXlsxResp
	161, // 407: cloudman.SecurityGroup.SecurityGroupBatchDeleteSecurityGroupRule:output_type -> cloudman.Result
	161, // 408: cloudman.SecurityGroup.SecurityGroupCreateSecurityGroupRule:output_type -> cloudman.Result
	235, // 409: cloudman.SecurityGroup.SecurityGroupDescribeIPWhitelists:output_type -> cloudman.DescribeIPWhitelistsResp
	161, // 410: cloudman.SecurityGroup.SecurityGroupModifyIPWhitelists:output_type -> cloudman.Result
	236, // 411: cloudman.SecurityGroup.SecurityGroupDescribeALBAcls:output_type -> cloudman.DescribeALBAclsResp
	161, // 412: cloudman.SecurityGroup.SecurityGroupAddALBAclsEntries:output_type -> cloudman.Result
	161, // 413: cloudman.SecurityGroup.SecurityGroupRemoveALBAclsEntries:output_type -> cloudman.Result
	237, // 414: cloudman.SecurityGroup.SecurityGroupJoinSecurityGroup:output_type -> cloudman.ModifySecurityGroupResult
	237, // 415: cloudman.SecurityGroup.SecurityGroupLeaveSecurityGroup:output_type -> cloudman.ModifySecurityGroupResult
	238, // 416: cloudman.LoadBalancer.LoadBalancerDescribe:output_type -> cloudman.DescribeLoadBalancerRes
	239, // 417: cloudman.LoadBalancer.LoadBalancerDescribeDetail:output_type -> cloudman.DescribeLoadBalancerDetailRes
	240, // 418: cloudman.LoadBalancer.LoadBalancerDescribeServerGroup:output_type -> cloudman.DescribeLoadBalancerServerGroupRes
	241, // 419: cloudman.LoadBalancer.LoadBalancerDescribeZones:output_type -> cloudman.DescribeZonesRes
	242, // 420: cloudman.LoadBalancer.LoadBalancerListACLs:output_type -> cloudman.ListAClsRes
	243, // 421: cloudman.LoadBalancer.LoadBalancerListCertificates:output_type -> cloudman.ListCertificatesRes
	161, // 422: cloudman.LoadBalancer.LoadBalancerCleanupLoadBalancer:output_type -> cloudman.Result
	244, // 423: cloudman.Eip.EipDescribe:output_type -> cloudman.DescribeEipRes
	245, // 424: cloudman.Eip.EipDescribeSegment:output_type -> cloudman.DescribeEipSegmentRes
	246, // 425: cloudman.Eip.EipDescribeIpam:output_type -> cloudman.DescribeIpamRes
	247, // 426: cloudman.IP.IPDescribe:output_type -> cloudman.DescribeIPRes
	161, // 427: cloudman.IP.IPCreateCustomIP:output_type -> cloudman.Result
	161, // 428: cloudman.IP.IPDeleteIP:output_type -> cloudman.Result
	248, // 429: cloudman.IP.IPDescribeByIPs:output_type -> cloudman.DescribeByIPsRes
	249, // 430: cloudman.IP.IPDescribeIPGroup:output_type -> cloudman.DescribeIPGroupResp
	161, // 431: cloudman.IP.IPCreateIPGroup:output_type -> cloudman.Result
	161, // 432: cloudman.IP.IPModifyIPGroup:output_type -> cloudman.Result
	161, // 433: cloudman.IP.IPDeleteIPGroup:output_type -> cloudman.Result
	250, // 434: cloudman.RAM.RAMDescribeRAMPolicy:output_type -> cloudman.DescribeRAMPolicyResp
	161, // 435: cloudman.RAM.RAMModifyRAMPolicySourceIPGroup:output_type -> cloudman.Result
	161, // 436: cloudman.RAM.RAMSyncRAMPolicy:output_type -> cloudman.Result
	251, // 437: cloudman.RAM.RAMPreviewRAMPolicyDeploy:output_type -> cloudman.PreviewRAMPolicyDeployResp
	161, // 438: cloudman.RAM.RAMRAMPolicyDeploy:output_type -> cloudman.Result
	161, // 439: cloudman.RAM.RAMSyncNewRAMPolicy:output_type -> cloudman.Result
	252, // 440: cloudman.RAM.RAMGetCloudRamPolicy:output_type -> cloudman.GetCloudRamPolicyResp
	253, // 441: cloudman.Domain.DomainDescribe:output_type -> cloudman.DescribeDomainRes
	253, // 442: cloudman.Domain.DomainDescribeRecords:output_type -> cloudman.DescribeDomainRes
	161, // 443: cloudman.Domain.DomainSyncDnspod:output_type -> cloudman.Result
	161, // 444: cloudman.Domain.DomainCreateDomain:output_type -> cloudman.Result
	254, // 445: cloudman.Task.TaskList:output_type -> cloudman.taskListResp
	255, // 446: cloudman.Task.TaskRetrieve:output_type -> cloudman.taskDetail
	161, // 447: cloudman.Task.TaskCreate:output_type -> cloudman.Result
	161, // 448: cloudman.Task.TaskRunTaskAPI:output_type -> cloudman.Result
	161, // 449: cloudman.Task.TaskUpdate:output_type -> cloudman.Result
	161, // 450: cloudman.Task.TaskDelete:output_type -> cloudman.Result
	256, // 451: cloudman.TaskLog.TaskLogListTaskLog:output_type -> cloudman.TaskLogListResponse
	257, // 452: cloudman.TaskLog.TaskLogRetrieveTaskLog:output_type -> cloudman.TaskLogResultList
	165, // 453: cloudman.Region.RegionList:output_type -> cloudman.regionListResponse
	122, // 454: cloudman.Region.RegionInfo:output_type -> cloudman.regionDetail
	161, // 455: cloudman.Region.RegionCreate:output_type -> cloudman.Result
	258, // 456: cloudman.ResTemplate.ResTemplateList:output_type -> cloudman.resTemListResp
	161, // 457: cloudman.ResTemplate.ResTemplateCreate:output_type -> cloudman.Result
	124, // 458: cloudman.ResTemplate.ResTemplateInfo:output_type -> cloudman.resTemCreateReq
	161, // 459: cloudman.ResTemplate.ResTemplateUpdate:output_type -> cloudman.Result
	161, // 460: cloudman.ResTemplate.ResTemplateDel:output_type -> cloudman.Result
	174, // 461: cloudman.ResTemplate.ResTemplateCreateResource:output_type -> cloudman.CommonResourceResp
	174, // 462: cloudman.ResTemplate.ResTemplateUpdateResource:output_type -> cloudman.CommonResourceResp
	259, // 463: cloudman.ResTemplate.ResTemplateUpdateResources:output_type -> cloudman.UpdateResourcesResp
	161, // 464: cloudman.ResTemplate.ResTemplateRunOrder:output_type -> cloudman.Result
	161, // 465: cloudman.ResTemplate.ResTemplateRunOrderRetry:output_type -> cloudman.Result
	260, // 466: cloudman.ResTemplate.ResTemplateGetSalePrice:output_type -> cloudman.SalePrice
	261, // 467: cloudman.ResTemplate.ResTemplateCheckInstanceNameSeq:output_type -> cloudman.CheckInstanceNameSeqResp
	262, // 468: cloudman.ResTemplate.ResTemplateGenInstanceNameWithRule:output_type -> cloudman.GenInstanceNameWithRuleResp
	263, // 469: cloudman.ResAudit.ResAuditGetOrderList:output_type -> cloudman.OrderTaskListResp
	257, // 470: cloudman.ResAudit.ResAuditGetOrderLog:output_type -> cloudman.TaskLogResultList
	264, // 471: cloudman.ResAudit.ResAuditGetOrder:output_type -> cloudman.OrderTaskListResult
	5,   // 472: cloudman.CloudGateway.CloudGatewayCreateGateway:output_type -> cloudman.ObjectID
	265, // 473: cloudman.CloudGateway.CloudGatewayGetGatewayScript:output_type -> cloudman.script
	5,   // 474: cloudman.CloudGateway.CreateAgent:output_type -> cloudman.ObjectID
	266, // 475: cloudman.CloudGateway.CloudGatewayGettopo:output_type -> cloudman.topo_result
	267, // 476: cloudman.CloudGateway.CloudGatewayGetGatewayTopo:output_type -> cloudman.gatewayTopoResult
	268, // 477: cloudman.CloudGateway.CloudGatewayListGateway:output_type -> cloudman.list_gateway_resp
	5,   // 478: cloudman.CloudGateway.CreateAgentScript:output_type -> cloudman.ObjectID
	265, // 479: cloudman.CloudGateway.CloudGatewayGetAgentScript:output_type -> cloudman.script
	161, // 480: cloudman.CloudGateway.CloudGatewayInsertGateAddress:output_type -> cloudman.Result
	136, // 481: cloudman.CloudGateway.CloudGatewayGetGateAddress:output_type -> cloudman.gateway_address_req
	161, // 482: cloudman.CloudGateway.CloudGatewayChangeAgent:output_type -> cloudman.Result
	161, // 483: cloudman.CloudGateway.CloudGatewayChangeAgents:output_type -> cloudman.Result
	161, // 484: cloudman.CloudGateway.CloudGatewayChangeAgentStatus:output_type -> cloudman.Result
	161, // 485: cloudman.CloudGateway.CloudGatewayDeleteAgent:output_type -> cloudman.Result
	269, // 486: cloudman.CloudGateway.CloudGatewayListAgent:output_type -> cloudman.list_agent_resp
	161, // 487: cloudman.CloudGateway.CloudGatewayRawAgentInfo:output_type -> cloudman.Result
	196, // 488: cloudman.CloudGateway.CloudGatewayExportXlsx:output_type -> cloudman.ExportXlsxResp
	270, // 489: cloudman.CloudGateway.CloudGatewayListAgentInstallConfig:output_type -> cloudman.list_agent_install_config_resp
	161, // 490: cloudman.CloudGateway.CloudGatewayUpdateAgentInstallConfig:output_type -> cloudman.Result
	161, // 491: cloudman.CloudGateway.CloudGatewayReinstallAgent:output_type -> cloudman.Result
	161, // 492: cloudman.CloudGateway.CloudGatewayUpdateAgent:output_type -> cloudman.Result
	271, // 493: cloudman.CloudGateway.CloudGatewayGetHostAgentID:output_type -> cloudman.AgentIDResp
	272, // 494: cloudman.CloudGateway.CloudGatewayListAgentVersion:output_type -> cloudman.ListAgentVersionResp
	273, // 495: cloudman.System.SystemGetConfig:output_type -> cloudman.SysConfig
	0,   // 496: cloudman.System.SystemWecomDailyNotify:output_type -> cloudman.Empty
	0,   // 497: cloudman.System.SystemSecurityGroupRuleDailyNotify:output_type -> cloudman.Empty
	274, // 498: cloudman.Cmdb.CmdbQuery:output_type -> cloudman.cmdbQueryResponse
	274, // 499: cloudman.Cmdb.CmdbQueryAssociate:output_type -> cloudman.cmdbQueryResponse
	275, // 500: cloudman.Cmdb.CmdbManualUpdate:output_type -> cloudman.cmdbUpdateResponse
	276, // 501: cloudman.Cmdb.CmdbDelete:output_type -> cloudman.cmdbDeleteResponse
	0,   // 502: cloudman.Cmdb.CmdbPullInfo:output_type -> cloudman.Empty
	174, // 503: cloudman.Cmdb.CmdbDiff:output_type -> cloudman.CommonResourceResp
	0,   // 504: cloudman.Cmdb.CmdbDiffProcess:output_type -> cloudman.Empty
	174, // 505: cloudman.Cmdb.CmdbDiffDelete:output_type -> cloudman.CommonResourceResp
	174, // 506: cloudman.Cmdb.CmdbDiffUpdate:output_type -> cloudman.CommonResourceResp
	277, // 507: cloudman.ServiceTree.ServiceTreeGetTree:output_type -> cloudman.GetTreeResponse
	278, // 508: cloudman.ServiceTree.ServiceTreeGetEntity:output_type -> cloudman.GetEntityResponse
	279, // 509: cloudman.ServiceTree.ServiceTreeNodeEntityCount:output_type -> cloudman.NodeEntityCountResponse
	280, // 510: cloudman.ServiceTree.ServiceTreeAddNode:output_type -> cloudman.AddNodeResponse
	281, // 511: cloudman.ServiceTree.ServiceTreeVerify:output_type -> cloudman.VerifyResponse
	282, // 512: cloudman.Ddos.DdosDescribeDdosBgp:output_type -> cloudman.DescribeDdosBgpRes
	283, // 513: cloudman.BandwidthPackage.BandwidthPackageDescribeCbwp:output_type -> cloudman.DescribeBandwidthPackageCbwpRes
	161, // 514: cloudman.Debug.DebugFixResTagBind:output_type -> cloudman.Result
	161, // 515: cloudman.Debug.DebugTest:output_type -> cloudman.Result
	284, // 516: cloudman.Permission.PermissionCheckInstancePermission:output_type -> cloudman.CheckInstancePermissionResp
	285, // 517: cloudman.InitResource.InitResourceGetJumpserver:output_type -> cloudman.GetJumpserverResp
	286, // 518: cloudman.InitResource.InitResourceGetPipeline:output_type -> cloudman.GetPipelineResp
	287, // 519: cloudman.InitResource.InitResourcePreviewParam:output_type -> cloudman.PreviewParamResp
	260, // [260:520] is the sub-list for method output_type
	0,   // [0:260] is the sub-list for method input_type
	0,   // [0:0] is the sub-list for extension type_name
	0,   // [0:0] is the sub-list for extension extendee
	0,   // [0:0] is the sub-list for field type_name
}

func init() { file_op_cloudman_proto_init() }
func file_op_cloudman_proto_init() {
	if File_op_cloudman_proto != nil {
		return
	}
	file_common_proto_init()
	file_account_proto_init()
	file_cmdb_proto_init()
	file_dashBoard_proto_init()
	file_inner_proto_init()
	file_resourceGroup_proto_init()
	file_resRecycle_proto_init()
	file_resTemplate_proto_init()
	file_tags_proto_init()
	file_task_proto_init()
	file_taskLog_proto_init()
	file_region_proto_init()
	file_cloudGateway_proto_init()
	file_cloudAgent_proto_init()
	file_permission_proto_init()
	file_servicetree_proto_init()
	file_securityGroup_proto_init()
	file_loadBalancer_proto_init()
	file_eip_proto_init()
	file_domain_proto_init()
	file_ip_proto_init()
	file_ram_proto_init()
	file_initResource_proto_init()
	file_ddos_proto_init()
	file_bandwidthPackage_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_op_cloudman_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   32,
		},
		GoTypes:           file_op_cloudman_proto_goTypes,
		DependencyIndexes: file_op_cloudman_proto_depIdxs,
	}.Build()
	File_op_cloudman_proto = out.File
	file_op_cloudman_proto_rawDesc = nil
	file_op_cloudman_proto_goTypes = nil
	file_op_cloudman_proto_depIdxs = nil
}
