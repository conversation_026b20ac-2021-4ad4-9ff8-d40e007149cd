// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: cloudGateway.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateGatewayReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Isp         string `protobuf:"bytes,2,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	Region      string `protobuf:"bytes,3,opt,name=region,proto3" form:"region" json:"region" query:"region"`
	SshHostId   string `protobuf:"bytes,4,opt,name=ssh_host_id,json=sshHostId,proto3" form:"ssh_host_id" json:"ssh_host_id" query:"ssh_host_id"`
	SshPort     uint32 `protobuf:"varint,5,opt,name=ssh_port,json=sshPort,proto3" form:"ssh_port" json:"ssh_port" query:"ssh_port"`
	AuthType    string `protobuf:"bytes,6,opt,name=auth_type,json=authType,proto3" form:"auth_type" json:"auth_type" query:"auth_type"`
	SshUsername string `protobuf:"bytes,7,opt,name=ssh_username,json=sshUsername,proto3" form:"ssh_username" json:"ssh_username" query:"ssh_username"`
	SshPassword string `protobuf:"bytes,8,opt,name=ssh_password,json=sshPassword,proto3" form:"ssh_password" json:"ssh_password" query:"ssh_password"`
	SshPem      string `protobuf:"bytes,9,opt,name=ssh_pem,json=sshPem,proto3" form:"ssh_pem" json:"ssh_pem" query:"ssh_pem"`
}

func (x *CreateGatewayReq) Reset() {
	*x = CreateGatewayReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateGatewayReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGatewayReq) ProtoMessage() {}

func (x *CreateGatewayReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGatewayReq.ProtoReflect.Descriptor instead.
func (*CreateGatewayReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{0}
}

func (x *CreateGatewayReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateGatewayReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *CreateGatewayReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateGatewayReq) GetSshHostId() string {
	if x != nil {
		return x.SshHostId
	}
	return ""
}

func (x *CreateGatewayReq) GetSshPort() uint32 {
	if x != nil {
		return x.SshPort
	}
	return 0
}

func (x *CreateGatewayReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *CreateGatewayReq) GetSshUsername() string {
	if x != nil {
		return x.SshUsername
	}
	return ""
}

func (x *CreateGatewayReq) GetSshPassword() string {
	if x != nil {
		return x.SshPassword
	}
	return ""
}

func (x *CreateGatewayReq) GetSshPem() string {
	if x != nil {
		return x.SshPem
	}
	return ""
}

type ListGatewayResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GatewayResp `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *ListGatewayResp) Reset() {
	*x = ListGatewayResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGatewayResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGatewayResp) ProtoMessage() {}

func (x *ListGatewayResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGatewayResp.ProtoReflect.Descriptor instead.
func (*ListGatewayResp) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{1}
}

func (x *ListGatewayResp) GetList() []*GatewayResp {
	if x != nil {
		return x.List
	}
	return nil
}

type GatewayResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   string `protobuf:"bytes,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Hostname string `protobuf:"bytes,2,opt,name=hostname,proto3" form:"hostname" json:"hostname" query:"hostname"`
	Address  string `protobuf:"bytes,3,opt,name=address,proto3" form:"address" json:"address" query:"address"`
	Name     string `protobuf:"bytes,4,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *GatewayResp) Reset() {
	*x = GatewayResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayResp) ProtoMessage() {}

func (x *GatewayResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayResp.ProtoReflect.Descriptor instead.
func (*GatewayResp) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{2}
}

func (x *GatewayResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GatewayResp) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *GatewayResp) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GatewayResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Script struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Script string `protobuf:"bytes,1,opt,name=script,proto3" form:"script" json:"script" query:"script"`
}

func (x *Script) Reset() {
	*x = Script{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Script) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Script) ProtoMessage() {}

func (x *Script) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Script.ProtoReflect.Descriptor instead.
func (*Script) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{3}
}

func (x *Script) GetScript() string {
	if x != nil {
		return x.Script
	}
	return ""
}

type GatewayAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PublicProtoc   string `protobuf:"bytes,1,opt,name=public_protoc,json=publicProtoc,proto3" form:"public_protoc" json:"public_protoc" query:"public_protoc"`
	PublicAddress  string `protobuf:"bytes,2,opt,name=public_address,json=publicAddress,proto3" form:"public_address" json:"public_address" query:"public_address"`
	PrivateProtoc  string `protobuf:"bytes,3,opt,name=private_protoc,json=privateProtoc,proto3" form:"private_protoc" json:"private_protoc" query:"private_protoc"`
	PrivateAddress string `protobuf:"bytes,4,opt,name=private_address,json=privateAddress,proto3" form:"private_address" json:"private_address" query:"private_address"`
}

func (x *GatewayAddressReq) Reset() {
	*x = GatewayAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayAddressReq) ProtoMessage() {}

func (x *GatewayAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayAddressReq.ProtoReflect.Descriptor instead.
func (*GatewayAddressReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{4}
}

func (x *GatewayAddressReq) GetPublicProtoc() string {
	if x != nil {
		return x.PublicProtoc
	}
	return ""
}

func (x *GatewayAddressReq) GetPublicAddress() string {
	if x != nil {
		return x.PublicAddress
	}
	return ""
}

func (x *GatewayAddressReq) GetPrivateProtoc() string {
	if x != nil {
		return x.PrivateProtoc
	}
	return ""
}

func (x *GatewayAddressReq) GetPrivateAddress() string {
	if x != nil {
		return x.PrivateAddress
	}
	return ""
}

// 云管任务agent详情
type AgentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         int32  `protobuf:"varint,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`                                                         // 连接状态
	LostTotal      uint32 `protobuf:"varint,2,opt,name=lost_total,json=lostTotal,proto3" form:"lost_total" json:"lost_total" query:"lost_total"`                          // 掉线统计
	AgentId        string `protobuf:"bytes,3,opt,name=agent_id,json=agentId,proto3" form:"agent_id" json:"agent_id" query:"agent_id"`                                     // agent-id
	RegionId       string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`                                // 地域id
	ConnectGate    string `protobuf:"bytes,5,opt,name=connect_gate,json=connectGate,proto3" form:"connect_gate" json:"connect_gate" query:"connect_gate"`                 // 来源网关
	OsName         string `protobuf:"bytes,6,opt,name=os_name,json=osName,proto3" form:"os_name" json:"os_name" query:"os_name"`                                          // 系统类型
	NodeType       string `protobuf:"bytes,7,opt,name=node_type,json=nodeType,proto3" form:"node_type" json:"node_type" query:"node_type"`                                // agent类型
	UpdateTime     int64  `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" form:"update_time" json:"update_time" query:"update_time"`                     // 最后一次状态变化时间
	LatestIp       string `protobuf:"bytes,9,opt,name=latest_ip,json=latestIp,proto3" form:"latest_ip" json:"latest_ip" query:"latest_ip"`                                // 最后一次上报ip
	LatestHostname string `protobuf:"bytes,10,opt,name=latest_hostname,json=latestHostname,proto3" form:"latest_hostname" json:"latest_hostname" query:"latest_hostname"` // 最后一次上报主机名
	ProxyId        string `protobuf:"bytes,11,opt,name=proxy_id,json=proxyId,proto3" form:"proxy_id" json:"proxy_id" query:"proxy_id"`
}

func (x *AgentResp) Reset() {
	*x = AgentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentResp) ProtoMessage() {}

func (x *AgentResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentResp.ProtoReflect.Descriptor instead.
func (*AgentResp) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{5}
}

func (x *AgentResp) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AgentResp) GetLostTotal() uint32 {
	if x != nil {
		return x.LostTotal
	}
	return 0
}

func (x *AgentResp) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *AgentResp) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *AgentResp) GetConnectGate() string {
	if x != nil {
		return x.ConnectGate
	}
	return ""
}

func (x *AgentResp) GetOsName() string {
	if x != nil {
		return x.OsName
	}
	return ""
}

func (x *AgentResp) GetNodeType() string {
	if x != nil {
		return x.NodeType
	}
	return ""
}

func (x *AgentResp) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *AgentResp) GetLatestIp() string {
	if x != nil {
		return x.LatestIp
	}
	return ""
}

func (x *AgentResp) GetLatestHostname() string {
	if x != nil {
		return x.LatestHostname
	}
	return ""
}

func (x *AgentResp) GetProxyId() string {
	if x != nil {
		return x.ProxyId
	}
	return ""
}

type TopoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gateway []*GatewayResp `protobuf:"bytes,1,rep,name=gateway,proto3" form:"gateway" json:"gateway" query:"gateway"`
	Agent   []*AgentResp   `protobuf:"bytes,2,rep,name=agent,proto3" form:"agent" json:"agent" query:"agent"`
}

func (x *TopoResult) Reset() {
	*x = TopoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopoResult) ProtoMessage() {}

func (x *TopoResult) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopoResult.ProtoReflect.Descriptor instead.
func (*TopoResult) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{6}
}

func (x *TopoResult) GetGateway() []*GatewayResp {
	if x != nil {
		return x.Gateway
	}
	return nil
}

func (x *TopoResult) GetAgent() []*AgentResp {
	if x != nil {
		return x.Agent
	}
	return nil
}

type GatewayTopoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ordering    []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords    string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	Name        string   `protobuf:"bytes,5,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Type        string   `protobuf:"bytes,6,opt,name=type,proto3" form:"type" json:"type" query:"type"`
	Page        uint64   `protobuf:"varint,7,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64   `protobuf:"varint,8,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	SearchKey   string   `protobuf:"bytes,13,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue string   `protobuf:"bytes,14,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
}

func (x *GatewayTopoReq) Reset() {
	*x = GatewayTopoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayTopoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayTopoReq) ProtoMessage() {}

func (x *GatewayTopoReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayTopoReq.ProtoReflect.Descriptor instead.
func (*GatewayTopoReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{7}
}

func (x *GatewayTopoReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *GatewayTopoReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *GatewayTopoReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GatewayTopoReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GatewayTopoReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GatewayTopoReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GatewayTopoReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *GatewayTopoReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

type Topo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProxyList  []*AgentCallbackReq `protobuf:"bytes,1,rep,name=proxy_list,json=proxyList,proto3" form:"proxy_list" json:"proxy_list" query:"proxy_list"`
	NormalList []*AgentCallbackReq `protobuf:"bytes,2,rep,name=normal_list,json=normalList,proto3" form:"normal_list" json:"normal_list" query:"normal_list"`
}

func (x *Topo) Reset() {
	*x = Topo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Topo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Topo) ProtoMessage() {}

func (x *Topo) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Topo.ProtoReflect.Descriptor instead.
func (*Topo) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{8}
}

func (x *Topo) GetProxyList() []*AgentCallbackReq {
	if x != nil {
		return x.ProxyList
	}
	return nil
}

func (x *Topo) GetNormalList() []*AgentCallbackReq {
	if x != nil {
		return x.NormalList
	}
	return nil
}

type GatewayTopoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProdTopo  *Topo `protobuf:"bytes,1,opt,name=prod_topo,json=prodTopo,proto3" form:"prod_topo" json:"prod_topo" query:"prod_topo"`
	TestTopo  *Topo `protobuf:"bytes,2,opt,name=test_topo,json=testTopo,proto3" form:"test_topo" json:"test_topo" query:"test_topo"`
	NoEnvTopo *Topo `protobuf:"bytes,3,opt,name=no_env_topo,json=noEnvTopo,proto3" form:"no_env_topo" json:"no_env_topo" query:"no_env_topo"`
}

func (x *GatewayTopoResult) Reset() {
	*x = GatewayTopoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GatewayTopoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayTopoResult) ProtoMessage() {}

func (x *GatewayTopoResult) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayTopoResult.ProtoReflect.Descriptor instead.
func (*GatewayTopoResult) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{9}
}

func (x *GatewayTopoResult) GetProdTopo() *Topo {
	if x != nil {
		return x.ProdTopo
	}
	return nil
}

func (x *GatewayTopoResult) GetTestTopo() *Topo {
	if x != nil {
		return x.TestTopo
	}
	return nil
}

func (x *GatewayTopoResult) GetNoEnvTopo() *Topo {
	if x != nil {
		return x.NoEnvTopo
	}
	return nil
}

type CreateAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isp         string `protobuf:"bytes,1,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`                                                       // 服务提供商id
	Region      string `protobuf:"bytes,2,opt,name=region,proto3" form:"region" json:"region" query:"region"`                                           // 连接地域
	SshHostId   string `protobuf:"bytes,3,opt,name=ssh_host_id,json=sshHostId,proto3" form:"ssh_host_id" json:"ssh_host_id" query:"ssh_host_id"`        // ssh主机id
	SshPort     uint32 `protobuf:"varint,4,opt,name=ssh_port,json=sshPort,proto3" form:"ssh_port" json:"ssh_port" query:"ssh_port"`                     // ssh端口
	AuthType    string `protobuf:"bytes,5,opt,name=auth_type,json=authType,proto3" form:"auth_type" json:"auth_type" query:"auth_type"`                 // ssh认证类型
	SshUsername string `protobuf:"bytes,6,opt,name=ssh_username,json=sshUsername,proto3" form:"ssh_username" json:"ssh_username" query:"ssh_username"`  // ssh username
	SshPassword string `protobuf:"bytes,7,opt,name=ssh_password,json=sshPassword,proto3" form:"ssh_password" json:"ssh_password" query:"ssh_password"`  // ssh密码
	SshPem      string `protobuf:"bytes,8,opt,name=ssh_pem,json=sshPem,proto3" form:"ssh_pem" json:"ssh_pem" query:"ssh_pem"`                           // ssh证书内容
	NodeType    string `protobuf:"bytes,9,opt,name=node_type,json=nodeType,proto3" form:"node_type" json:"node_type" query:"node_type"`                 // 节点类型
	NetworkType string `protobuf:"bytes,10,opt,name=network_type,json=networkType,proto3" form:"network_type" json:"network_type" query:"network_type"` // 网络类型
}

func (x *CreateAgentReq) Reset() {
	*x = CreateAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAgentReq) ProtoMessage() {}

func (x *CreateAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAgentReq.ProtoReflect.Descriptor instead.
func (*CreateAgentReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{10}
}

func (x *CreateAgentReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *CreateAgentReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateAgentReq) GetSshHostId() string {
	if x != nil {
		return x.SshHostId
	}
	return ""
}

func (x *CreateAgentReq) GetSshPort() uint32 {
	if x != nil {
		return x.SshPort
	}
	return 0
}

func (x *CreateAgentReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *CreateAgentReq) GetSshUsername() string {
	if x != nil {
		return x.SshUsername
	}
	return ""
}

func (x *CreateAgentReq) GetSshPassword() string {
	if x != nil {
		return x.SshPassword
	}
	return ""
}

func (x *CreateAgentReq) GetSshPem() string {
	if x != nil {
		return x.SshPem
	}
	return ""
}

func (x *CreateAgentReq) GetNodeType() string {
	if x != nil {
		return x.NodeType
	}
	return ""
}

func (x *CreateAgentReq) GetNetworkType() string {
	if x != nil {
		return x.NetworkType
	}
	return ""
}

type ListAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Ordering    []string `protobuf:"bytes,3,rep,name=ordering,proto3" form:"ordering" json:"ordering" query:"ordering"`
	Keywords    string   `protobuf:"bytes,4,opt,name=keywords,proto3" form:"keywords" json:"keywords" query:"keywords"`
	HostName    string   `protobuf:"bytes,5,opt,name=host_name,json=hostName,proto3" form:"host_name" json:"host_name" query:"host_name"`
	NodeType    string   `protobuf:"bytes,6,opt,name=node_type,json=nodeType,proto3" form:"node_type" json:"node_type" query:"node_type"`
	Status      int32    `protobuf:"varint,7,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Env         string   `protobuf:"bytes,8,opt,name=env,proto3" form:"env" json:"env" query:"env"`
	Version     string   `protobuf:"bytes,9,opt,name=version,proto3" form:"version" json:"version" query:"version"`
	HostIp      string   `protobuf:"bytes,10,opt,name=host_ip,json=hostIp,proto3" form:"host_ip" json:"host_ip" query:"host_ip"`
	AgentId     string   `protobuf:"bytes,11,opt,name=agent_id,json=agentId,proto3" form:"agent_id" json:"agent_id" query:"agent_id"`
	SearchParam []string `protobuf:"bytes,12,rep,name=search_param,json=searchParam,proto3" form:"search_param" json:"search_param" query:"search_param"`
}

func (x *ListAgentReq) Reset() {
	*x = ListAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentReq) ProtoMessage() {}

func (x *ListAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentReq.ProtoReflect.Descriptor instead.
func (*ListAgentReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{11}
}

func (x *ListAgentReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAgentReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListAgentReq) GetOrdering() []string {
	if x != nil {
		return x.Ordering
	}
	return nil
}

func (x *ListAgentReq) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *ListAgentReq) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *ListAgentReq) GetNodeType() string {
	if x != nil {
		return x.NodeType
	}
	return ""
}

func (x *ListAgentReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListAgentReq) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *ListAgentReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ListAgentReq) GetHostIp() string {
	if x != nil {
		return x.HostIp
	}
	return ""
}

func (x *ListAgentReq) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *ListAgentReq) GetSearchParam() []string {
	if x != nil {
		return x.SearchParam
	}
	return nil
}

type ListAgentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List     []*AgentCallbackReq `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total    uint32              `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	Running  uint32              `protobuf:"varint,3,opt,name=running,proto3" form:"running" json:"running" query:"running"`
	Outdated uint32              `protobuf:"varint,4,opt,name=outdated,proto3" form:"outdated" json:"outdated" query:"outdated"`
	Current  uint32              `protobuf:"varint,5,opt,name=current,proto3" form:"current" json:"current" query:"current"`
}

func (x *ListAgentResp) Reset() {
	*x = ListAgentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentResp) ProtoMessage() {}

func (x *ListAgentResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentResp.ProtoReflect.Descriptor instead.
func (*ListAgentResp) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{12}
}

func (x *ListAgentResp) GetList() []*AgentCallbackReq {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListAgentResp) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListAgentResp) GetRunning() uint32 {
	if x != nil {
		return x.Running
	}
	return 0
}

func (x *ListAgentResp) GetOutdated() uint32 {
	if x != nil {
		return x.Outdated
	}
	return 0
}

func (x *ListAgentResp) GetCurrent() uint32 {
	if x != nil {
		return x.Current
	}
	return 0
}

type AgentCallbackReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentId       string   `protobuf:"bytes,1,opt,name=agentId,proto3" form:"agentId" json:"agentId" query:"agentId"`
	Status        int32    `protobuf:"varint,2,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	RealIps       []string `protobuf:"bytes,3,rep,name=real_ips,json=realIps,proto3" form:"real_ips" json:"real_ips" query:"real_ips"`
	Version       string   `protobuf:"bytes,4,opt,name=version,proto3" form:"version" json:"version" query:"version"`
	SystemVersion string   `protobuf:"bytes,5,opt,name=system_version,json=systemVersion,proto3" form:"system_version" json:"system_version" query:"system_version"`
	NodeType      string   `protobuf:"bytes,6,opt,name=node_type,json=nodeType,proto3" form:"node_type" json:"node_type" query:"node_type"`
	ProxyId       string   `protobuf:"bytes,7,opt,name=proxy_id,json=proxyId,proto3" form:"proxy_id" json:"proxy_id" query:"proxy_id"`
	Hostname      string   `protobuf:"bytes,8,opt,name=hostname,proto3" form:"hostname" json:"hostname" query:"hostname"`
	InstanceId    string   `protobuf:"bytes,9,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	Region        string   `protobuf:"bytes,10,opt,name=region,proto3" form:"region" json:"region" query:"region"`
	HostIp        string   `protobuf:"bytes,11,opt,name=host_ip,json=hostIp,proto3" form:"host_ip" json:"host_ip" query:"host_ip"`
	UpdatedTime   uint32   `protobuf:"varint,12,opt,name=updated_time,json=updatedTime,proto3" form:"updated_time" json:"updated_time" query:"updated_time"`
	CreatedTime   uint32   `protobuf:"varint,13,opt,name=created_time,json=createdTime,proto3" form:"created_time" json:"created_time" query:"created_time"`
	CpuModel      string   `protobuf:"bytes,14,opt,name=cpu_model,json=cpuModel,proto3" form:"cpu_model" json:"cpu_model" query:"cpu_model"`
	CpuCore       int32    `protobuf:"varint,15,opt,name=cpu_core,json=cpuCore,proto3" form:"cpu_core" json:"cpu_core" query:"cpu_core"`
	GpuList       []string `protobuf:"bytes,16,rep,name=gpu_list,json=gpuList,proto3" form:"gpu_list" json:"gpu_list" query:"gpu_list"`
	MemTotal      int64    `protobuf:"varint,17,opt,name=mem_total,json=memTotal,proto3" form:"mem_total" json:"mem_total" query:"mem_total"`
	Env           string   `protobuf:"bytes,18,opt,name=env,proto3" form:"env" json:"env" query:"env"`
	NetSpeed      []string `protobuf:"bytes,19,rep,name=net_speed,json=netSpeed,proto3" form:"net_speed" json:"net_speed" query:"net_speed"`
}

func (x *AgentCallbackReq) Reset() {
	*x = AgentCallbackReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentCallbackReq) ProtoMessage() {}

func (x *AgentCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentCallbackReq.ProtoReflect.Descriptor instead.
func (*AgentCallbackReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{13}
}

func (x *AgentCallbackReq) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *AgentCallbackReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AgentCallbackReq) GetRealIps() []string {
	if x != nil {
		return x.RealIps
	}
	return nil
}

func (x *AgentCallbackReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AgentCallbackReq) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *AgentCallbackReq) GetNodeType() string {
	if x != nil {
		return x.NodeType
	}
	return ""
}

func (x *AgentCallbackReq) GetProxyId() string {
	if x != nil {
		return x.ProxyId
	}
	return ""
}

func (x *AgentCallbackReq) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *AgentCallbackReq) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *AgentCallbackReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *AgentCallbackReq) GetHostIp() string {
	if x != nil {
		return x.HostIp
	}
	return ""
}

func (x *AgentCallbackReq) GetUpdatedTime() uint32 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *AgentCallbackReq) GetCreatedTime() uint32 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *AgentCallbackReq) GetCpuModel() string {
	if x != nil {
		return x.CpuModel
	}
	return ""
}

func (x *AgentCallbackReq) GetCpuCore() int32 {
	if x != nil {
		return x.CpuCore
	}
	return 0
}

func (x *AgentCallbackReq) GetGpuList() []string {
	if x != nil {
		return x.GpuList
	}
	return nil
}

func (x *AgentCallbackReq) GetMemTotal() int64 {
	if x != nil {
		return x.MemTotal
	}
	return 0
}

func (x *AgentCallbackReq) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

func (x *AgentCallbackReq) GetNetSpeed() []string {
	if x != nil {
		return x.NetSpeed
	}
	return nil
}

type GetAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hostname       string `protobuf:"bytes,1,opt,name=hostname,proto3" form:"hostname" json:"hostname" query:"hostname"`
	InnerIpaddress string `protobuf:"bytes,2,opt,name=inner_ipaddress,json=innerIpaddress,proto3" form:"inner_ipaddress" json:"inner_ipaddress" query:"inner_ipaddress"`
	Region         string `protobuf:"bytes,3,opt,name=region,proto3" form:"region" json:"region" query:"region"`
	AccountId      string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" form:"account_id" json:"account_id" query:"account_id"`
}

func (x *GetAgentReq) Reset() {
	*x = GetAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentReq) ProtoMessage() {}

func (x *GetAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentReq.ProtoReflect.Descriptor instead.
func (*GetAgentReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{14}
}

func (x *GetAgentReq) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *GetAgentReq) GetInnerIpaddress() string {
	if x != nil {
		return x.InnerIpaddress
	}
	return ""
}

func (x *GetAgentReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *GetAgentReq) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

type AgentInstallConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IspId     string `protobuf:"bytes,1,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	RegionId  string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	CmdbEnv   string `protobuf:"bytes,3,opt,name=cmdb_env,json=cmdbEnv,proto3" form:"cmdb_env" json:"cmdb_env" query:"cmdb_env"`
	BinaryUrl string `protobuf:"bytes,4,opt,name=binary_url,json=binaryUrl,proto3" form:"binary_url" json:"binary_url" query:"binary_url"`
	RegUrl    string `protobuf:"bytes,5,opt,name=reg_url,json=regUrl,proto3" form:"reg_url" json:"reg_url" query:"reg_url"`
}

func (x *AgentInstallConfig) Reset() {
	*x = AgentInstallConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentInstallConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentInstallConfig) ProtoMessage() {}

func (x *AgentInstallConfig) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentInstallConfig.ProtoReflect.Descriptor instead.
func (*AgentInstallConfig) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{15}
}

func (x *AgentInstallConfig) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *AgentInstallConfig) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *AgentInstallConfig) GetCmdbEnv() string {
	if x != nil {
		return x.CmdbEnv
	}
	return ""
}

func (x *AgentInstallConfig) GetBinaryUrl() string {
	if x != nil {
		return x.BinaryUrl
	}
	return ""
}

func (x *AgentInstallConfig) GetRegUrl() string {
	if x != nil {
		return x.RegUrl
	}
	return ""
}

type ListAgentInstallConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*AgentInstallConfig `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *ListAgentInstallConfigResp) Reset() {
	*x = ListAgentInstallConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgentInstallConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentInstallConfigResp) ProtoMessage() {}

func (x *ListAgentInstallConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentInstallConfigResp.ProtoReflect.Descriptor instead.
func (*ListAgentInstallConfigResp) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{16}
}

func (x *ListAgentInstallConfigResp) GetList() []*AgentInstallConfig {
	if x != nil {
		return x.List
	}
	return nil
}

type ReinstallAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids      []string `protobuf:"bytes,1,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
	Username string   `protobuf:"bytes,2,opt,name=username,proto3" form:"username" json:"username" query:"username"`
	Password string   `protobuf:"bytes,3,opt,name=password,proto3" form:"password" json:"password" query:"password"`
	Port     string   `protobuf:"bytes,4,opt,name=port,proto3" form:"port" json:"port" query:"port"`
}

func (x *ReinstallAgentReq) Reset() {
	*x = ReinstallAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReinstallAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReinstallAgentReq) ProtoMessage() {}

func (x *ReinstallAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReinstallAgentReq.ProtoReflect.Descriptor instead.
func (*ReinstallAgentReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{17}
}

func (x *ReinstallAgentReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReinstallAgentReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ReinstallAgentReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ReinstallAgentReq) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

type UpdateAgentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
}

func (x *UpdateAgentReq) Reset() {
	*x = UpdateAgentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAgentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentReq) ProtoMessage() {}

func (x *UpdateAgentReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentReq.ProtoReflect.Descriptor instead.
func (*UpdateAgentReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateAgentReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type ChangeAgentStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids    []string `protobuf:"bytes,1,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
	Status int32    `protobuf:"varint,2,opt,name=status,proto3" form:"status" json:"status" query:"status"`
}

func (x *ChangeAgentStatusReq) Reset() {
	*x = ChangeAgentStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeAgentStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeAgentStatusReq) ProtoMessage() {}

func (x *ChangeAgentStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeAgentStatusReq.ProtoReflect.Descriptor instead.
func (*ChangeAgentStatusReq) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{19}
}

func (x *ChangeAgentStatusReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ChangeAgentStatusReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type ListAgentVersionResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Versions []string `protobuf:"bytes,1,rep,name=versions,proto3" form:"versions" json:"versions" query:"versions"`
}

func (x *ListAgentVersionResp) Reset() {
	*x = ListAgentVersionResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cloudGateway_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAgentVersionResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentVersionResp) ProtoMessage() {}

func (x *ListAgentVersionResp) ProtoReflect() protoreflect.Message {
	mi := &file_cloudGateway_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentVersionResp.ProtoReflect.Descriptor instead.
func (*ListAgentVersionResp) Descriptor() ([]byte, []int) {
	return file_cloudGateway_proto_rawDescGZIP(), []int{20}
}

func (x *ListAgentVersionResp) GetVersions() []string {
	if x != nil {
		return x.Versions
	}
	return nil
}

var File_cloudGateway_proto protoreflect.FileDescriptor

var file_cloudGateway_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x22, 0x89,
	0x02, 0x0a, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x5f, 0x72, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x73, 0x73, 0x68, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x73, 0x68, 0x48, 0x6f, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x73, 0x68, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x73, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x73, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x73, 0x68, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x73, 0x68, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x73, 0x68, 0x5f, 0x70, 0x65, 0x6d, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x73, 0x68, 0x50, 0x65, 0x6d, 0x22, 0x3f, 0x0a, 0x11, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x12,
	0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x70, 0x0a, 0x0c, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x20, 0x0a,
	0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x22,
	0xb1, 0x01, 0x0a, 0x13, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x12, 0x25, 0x0a, 0x0e,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x22, 0xd6, 0x02, 0x0a, 0x0a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f,
	0x73, 0x74, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x6c, 0x6f, 0x73, 0x74, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x67, 0x61, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x47, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x49, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x48, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x49, 0x64, 0x22, 0x6b, 0x0a, 0x0b,
	0x74, 0x6f, 0x70, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x30, 0x0a, 0x07, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x2a, 0x0a,
	0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x52, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x22, 0xda, 0x01, 0x0a, 0x0e, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x54, 0x6f, 0x70, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x04, 0x74, 0x6f, 0x70, 0x6f, 0x12,
	0x3b, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65,
	0x71, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0b,
	0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x52,
	0x0a, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9d, 0x01, 0x0a, 0x11,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x54, 0x6f, 0x70, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x2b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x5f, 0x74, 0x6f, 0x70, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x74, 0x6f, 0x70, 0x6f, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x54, 0x6f, 0x70, 0x6f, 0x12, 0x2b,
	0x0a, 0x09, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x6f, 0x70, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x6f, 0x70,
	0x6f, 0x52, 0x08, 0x74, 0x65, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x6f, 0x12, 0x2e, 0x0a, 0x0b, 0x6e,
	0x6f, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x74, 0x6f, 0x70, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x74, 0x6f, 0x70, 0x6f,
	0x52, 0x09, 0x6e, 0x6f, 0x45, 0x6e, 0x76, 0x54, 0x6f, 0x70, 0x6f, 0x22, 0xb3, 0x02, 0x0a, 0x10,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69,
	0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0b, 0x73, 0x73,
	0x68, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x73, 0x68, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x73,
	0x68, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x73,
	0x68, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x73, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x73, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x73, 0x68, 0x5f, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x73, 0x68,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x73, 0x68, 0x5f,
	0x70, 0x65, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x73, 0x68, 0x50, 0x65,
	0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xc5, 0x02, 0x0a, 0x0e, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x22, 0xa9, 0x01, 0x0a, 0x0f, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12,
	0x1a, 0x0a, 0x08, 0x6f, 0x75, 0x74, 0x64, 0x61, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6f, 0x75, 0x74, 0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x22, 0xad, 0x04, 0x0a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x65, 0x61, 0x6c, 0x49, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x78, 0x79,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x70,
	0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x70, 0x75, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x70, 0x75, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x70, 0x75, 0x5f, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x70, 0x75, 0x43, 0x6f, 0x72, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x67, 0x70, 0x75, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x67, 0x70, 0x75, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x65, 0x6d,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x65,
	0x6d, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x74, 0x5f,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x65, 0x74,
	0x53, 0x70, 0x65, 0x65, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x0d, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e,
	0x6e, 0x65, 0x72, 0x49, 0x70, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x14, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x15, 0x0a, 0x06,
	0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73,
	0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6d, 0x64, 0x62, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6d, 0x64, 0x62, 0x45, 0x6e, 0x76, 0x12, 0x1d, 0x0a, 0x0a, 0x62,
	0x69, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x65,
	0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67,
	0x55, 0x72, 0x6c, 0x22, 0x54, 0x0a, 0x1e, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x5f, 0x72, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x71, 0x0a, 0x11, 0x52, 0x65, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x22, 0x0a, 0x0e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x22, 0x40, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x32, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x3b, 0x5a, 0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69,
	0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c,
	0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cloudGateway_proto_rawDescOnce sync.Once
	file_cloudGateway_proto_rawDescData = file_cloudGateway_proto_rawDesc
)

func file_cloudGateway_proto_rawDescGZIP() []byte {
	file_cloudGateway_proto_rawDescOnce.Do(func() {
		file_cloudGateway_proto_rawDescData = protoimpl.X.CompressGZIP(file_cloudGateway_proto_rawDescData)
	})
	return file_cloudGateway_proto_rawDescData
}

var file_cloudGateway_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_cloudGateway_proto_goTypes = []interface{}{
	(*CreateGatewayReq)(nil),           // 0: cloudman.create_gateway_req
	(*ListGatewayResp)(nil),            // 1: cloudman.list_gateway_resp
	(*GatewayResp)(nil),                // 2: cloudman.gateway_resp
	(*Script)(nil),                     // 3: cloudman.script
	(*GatewayAddressReq)(nil),          // 4: cloudman.gateway_address_req
	(*AgentResp)(nil),                  // 5: cloudman.agent_resp
	(*TopoResult)(nil),                 // 6: cloudman.topo_result
	(*GatewayTopoReq)(nil),             // 7: cloudman.gatewayTopoReq
	(*Topo)(nil),                       // 8: cloudman.topo
	(*GatewayTopoResult)(nil),          // 9: cloudman.gatewayTopoResult
	(*CreateAgentReq)(nil),             // 10: cloudman.create_agent_req
	(*ListAgentReq)(nil),               // 11: cloudman.list_agent_req
	(*ListAgentResp)(nil),              // 12: cloudman.list_agent_resp
	(*AgentCallbackReq)(nil),           // 13: cloudman.agent_callback_req
	(*GetAgentReq)(nil),                // 14: cloudman.get_agent_req
	(*AgentInstallConfig)(nil),         // 15: cloudman.agent_install_config
	(*ListAgentInstallConfigResp)(nil), // 16: cloudman.list_agent_install_config_resp
	(*ReinstallAgentReq)(nil),          // 17: cloudman.ReinstallAgentReq
	(*UpdateAgentReq)(nil),             // 18: cloudman.UpdateAgentReq
	(*ChangeAgentStatusReq)(nil),       // 19: cloudman.ChangeAgentStatusReq
	(*ListAgentVersionResp)(nil),       // 20: cloudman.ListAgentVersionResp
}
var file_cloudGateway_proto_depIdxs = []int32{
	2,  // 0: cloudman.list_gateway_resp.list:type_name -> cloudman.gateway_resp
	2,  // 1: cloudman.topo_result.gateway:type_name -> cloudman.gateway_resp
	5,  // 2: cloudman.topo_result.agent:type_name -> cloudman.agent_resp
	13, // 3: cloudman.topo.proxy_list:type_name -> cloudman.agent_callback_req
	13, // 4: cloudman.topo.normal_list:type_name -> cloudman.agent_callback_req
	8,  // 5: cloudman.gatewayTopoResult.prod_topo:type_name -> cloudman.topo
	8,  // 6: cloudman.gatewayTopoResult.test_topo:type_name -> cloudman.topo
	8,  // 7: cloudman.gatewayTopoResult.no_env_topo:type_name -> cloudman.topo
	13, // 8: cloudman.list_agent_resp.list:type_name -> cloudman.agent_callback_req
	15, // 9: cloudman.list_agent_install_config_resp.list:type_name -> cloudman.agent_install_config
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_cloudGateway_proto_init() }
func file_cloudGateway_proto_init() {
	if File_cloudGateway_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cloudGateway_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateGatewayReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGatewayResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Script); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayTopoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Topo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GatewayTopoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentCallbackReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentInstallConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgentInstallConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReinstallAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAgentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeAgentStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cloudGateway_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAgentVersionResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cloudGateway_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cloudGateway_proto_goTypes,
		DependencyIndexes: file_cloudGateway_proto_depIdxs,
		MessageInfos:      file_cloudGateway_proto_msgTypes,
	}.Build()
	File_cloudGateway_proto = out.File
	file_cloudGateway_proto_rawDesc = nil
	file_cloudGateway_proto_goTypes = nil
	file_cloudGateway_proto_depIdxs = nil
}
