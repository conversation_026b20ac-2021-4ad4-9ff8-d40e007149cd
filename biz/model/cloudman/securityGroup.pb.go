// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: securityGroup.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/api"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SecurityGroupEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreationTime            string                     `protobuf:"bytes,2,opt,name=CreationTime,proto3" form:"CreationTime" json:"CreationTime" query:"CreationTime"`
	VpcId                   string                     `protobuf:"bytes,3,opt,name=VpcId,proto3" form:"VpcId" json:"VpcId" query:"VpcId"`
	ServiceManaged          bool                       `protobuf:"varint,4,opt,name=ServiceManaged,proto3" form:"ServiceManaged" json:"ServiceManaged" query:"ServiceManaged"`
	Description             string                     `protobuf:"bytes,5,opt,name=Description,proto3" form:"Description" json:"Description" query:"Description"`
	SecurityGroupId         string                     `protobuf:"bytes,6,opt,name=SecurityGroupId,proto3" form:"SecurityGroupId" json:"SecurityGroupId" query:"SecurityGroupId"`
	ResourceGroupId         string                     `protobuf:"bytes,7,opt,name=ResourceGroupId,proto3" form:"ResourceGroupId" json:"ResourceGroupId" query:"ResourceGroupId"`
	SecurityGroupName       string                     `protobuf:"bytes,8,opt,name=SecurityGroupName,proto3" form:"SecurityGroupName" json:"SecurityGroupName" query:"SecurityGroupName"`
	EcsCount                int32                      `protobuf:"varint,9,opt,name=EcsCount,proto3" form:"EcsCount" json:"EcsCount" query:"EcsCount"`
	ServiceID               int64                      `protobuf:"varint,10,opt,name=ServiceID,proto3" form:"ServiceID" json:"ServiceID" query:"ServiceID"`
	SecurityGroupType       string                     `protobuf:"bytes,11,opt,name=SecurityGroupType,proto3" form:"SecurityGroupType" json:"SecurityGroupType" query:"SecurityGroupType"`
	AvailableInstanceAmount int32                      `protobuf:"varint,12,opt,name=AvailableInstanceAmount,proto3" form:"AvailableInstanceAmount" json:"AvailableInstanceAmount" query:"AvailableInstanceAmount"`
	RegionID                string                     `protobuf:"bytes,13,opt,name=RegionID,proto3" form:"RegionID" json:"RegionID" query:"RegionID"`
	Tags                    []*ResourceTag             `protobuf:"bytes,14,rep,name=Tags,proto3" form:"Tags" json:"Tags" query:"Tags"`
	Permissions             []*SecurityGroupPermission `protobuf:"bytes,15,rep,name=Permissions,proto3" form:"Permissions" json:"Permissions" query:"Permissions"`
	IspId                   string                     `protobuf:"bytes,16,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType                 string                     `protobuf:"bytes,17,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName                 string                     `protobuf:"bytes,18,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
	BindInstanceInfos       []*InstanceInfo            `protobuf:"bytes,19,rep,name=bind_instance_infos,json=bindInstanceInfos,proto3" form:"bind_instance_infos" json:"bind_instance_infos" query:"bind_instance_infos"`
	CustomTag               string                     `protobuf:"bytes,20,opt,name=custom_tag,json=customTag,proto3" form:"custom_tag" json:"custom_tag" query:"custom_tag"`
	Count                   *RelateInstanceCount       `protobuf:"bytes,21,opt,name=count,proto3" form:"count" json:"count" query:"count"`
	LevelTag                *RuleTag                   `protobuf:"bytes,22,opt,name=level_tag,json=levelTag,proto3" form:"level_tag" json:"level_tag" query:"level_tag"`
	NeedCleanup             bool                       `protobuf:"varint,23,opt,name=NeedCleanup,proto3" form:"NeedCleanup" json:"NeedCleanup" query:"NeedCleanup"`
}

func (x *SecurityGroupEntity) Reset() {
	*x = SecurityGroupEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityGroupEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityGroupEntity) ProtoMessage() {}

func (x *SecurityGroupEntity) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityGroupEntity.ProtoReflect.Descriptor instead.
func (*SecurityGroupEntity) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{0}
}

func (x *SecurityGroupEntity) GetCreationTime() string {
	if x != nil {
		return x.CreationTime
	}
	return ""
}

func (x *SecurityGroupEntity) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *SecurityGroupEntity) GetServiceManaged() bool {
	if x != nil {
		return x.ServiceManaged
	}
	return false
}

func (x *SecurityGroupEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SecurityGroupEntity) GetSecurityGroupId() string {
	if x != nil {
		return x.SecurityGroupId
	}
	return ""
}

func (x *SecurityGroupEntity) GetResourceGroupId() string {
	if x != nil {
		return x.ResourceGroupId
	}
	return ""
}

func (x *SecurityGroupEntity) GetSecurityGroupName() string {
	if x != nil {
		return x.SecurityGroupName
	}
	return ""
}

func (x *SecurityGroupEntity) GetEcsCount() int32 {
	if x != nil {
		return x.EcsCount
	}
	return 0
}

func (x *SecurityGroupEntity) GetServiceID() int64 {
	if x != nil {
		return x.ServiceID
	}
	return 0
}

func (x *SecurityGroupEntity) GetSecurityGroupType() string {
	if x != nil {
		return x.SecurityGroupType
	}
	return ""
}

func (x *SecurityGroupEntity) GetAvailableInstanceAmount() int32 {
	if x != nil {
		return x.AvailableInstanceAmount
	}
	return 0
}

func (x *SecurityGroupEntity) GetRegionID() string {
	if x != nil {
		return x.RegionID
	}
	return ""
}

func (x *SecurityGroupEntity) GetTags() []*ResourceTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SecurityGroupEntity) GetPermissions() []*SecurityGroupPermission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *SecurityGroupEntity) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *SecurityGroupEntity) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *SecurityGroupEntity) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

func (x *SecurityGroupEntity) GetBindInstanceInfos() []*InstanceInfo {
	if x != nil {
		return x.BindInstanceInfos
	}
	return nil
}

func (x *SecurityGroupEntity) GetCustomTag() string {
	if x != nil {
		return x.CustomTag
	}
	return ""
}

func (x *SecurityGroupEntity) GetCount() *RelateInstanceCount {
	if x != nil {
		return x.Count
	}
	return nil
}

func (x *SecurityGroupEntity) GetLevelTag() *RuleTag {
	if x != nil {
		return x.LevelTag
	}
	return nil
}

func (x *SecurityGroupEntity) GetNeedCleanup() bool {
	if x != nil {
		return x.NeedCleanup
	}
	return false
}

type RelateInstanceCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host  int32 `protobuf:"varint,1,opt,name=host,proto3" form:"host" json:"host" query:"host"`
	Mysql int32 `protobuf:"varint,2,opt,name=mysql,proto3" form:"mysql" json:"mysql" query:"mysql"`
	Redis int32 `protobuf:"varint,3,opt,name=redis,proto3" form:"redis" json:"redis" query:"redis"`
}

func (x *RelateInstanceCount) Reset() {
	*x = RelateInstanceCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelateInstanceCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelateInstanceCount) ProtoMessage() {}

func (x *RelateInstanceCount) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelateInstanceCount.ProtoReflect.Descriptor instead.
func (*RelateInstanceCount) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{1}
}

func (x *RelateInstanceCount) GetHost() int32 {
	if x != nil {
		return x.Host
	}
	return 0
}

func (x *RelateInstanceCount) GetMysql() int32 {
	if x != nil {
		return x.Mysql
	}
	return 0
}

func (x *RelateInstanceCount) GetRedis() int32 {
	if x != nil {
		return x.Redis
	}
	return 0
}

type InstanceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceID   string `protobuf:"bytes,1,opt,name=InstanceID,proto3" form:"InstanceID" json:"InstanceID" query:"InstanceID"`
	InstanceName string `protobuf:"bytes,2,opt,name=InstanceName,proto3" form:"InstanceName" json:"InstanceName" query:"InstanceName"`
}

func (x *InstanceInfo) Reset() {
	*x = InstanceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InstanceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstanceInfo) ProtoMessage() {}

func (x *InstanceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstanceInfo.ProtoReflect.Descriptor instead.
func (*InstanceInfo) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{2}
}

func (x *InstanceInfo) GetInstanceID() string {
	if x != nil {
		return x.InstanceID
	}
	return ""
}

func (x *InstanceInfo) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

type SecurityGroupPermission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateTime              string     `protobuf:"bytes,1,opt,name=CreateTime,proto3" form:"CreateTime" json:"CreateTime" query:"CreateTime"`
	Description             string     `protobuf:"bytes,2,opt,name=Description,proto3" form:"Description" json:"Description" query:"Description"`
	DestCidrIp              string     `protobuf:"bytes,3,opt,name=DestCidrIp,proto3" form:"DestCidrIp" json:"DestCidrIp" query:"DestCidrIp"`
	DestGroupId             string     `protobuf:"bytes,4,opt,name=DestGroupId,proto3" form:"DestGroupId" json:"DestGroupId" query:"DestGroupId"`
	DestGroupName           string     `protobuf:"bytes,5,opt,name=DestGroupName,proto3" form:"DestGroupName" json:"DestGroupName" query:"DestGroupName"`
	DestGroupOwnerAccount   string     `protobuf:"bytes,6,opt,name=DestGroupOwnerAccount,proto3" form:"DestGroupOwnerAccount" json:"DestGroupOwnerAccount" query:"DestGroupOwnerAccount"`
	DestPrefixListId        string     `protobuf:"bytes,7,opt,name=DestPrefixListId,proto3" form:"DestPrefixListId" json:"DestPrefixListId" query:"DestPrefixListId"`
	DestPrefixListName      string     `protobuf:"bytes,8,opt,name=DestPrefixListName,proto3" form:"DestPrefixListName" json:"DestPrefixListName" query:"DestPrefixListName"`
	Direction               string     `protobuf:"bytes,9,opt,name=Direction,proto3" form:"Direction" json:"Direction" query:"Direction"`
	IpProtocol              string     `protobuf:"bytes,10,opt,name=IpProtocol,proto3" form:"IpProtocol" json:"IpProtocol" query:"IpProtocol"`
	Ipv6DestCidrIp          string     `protobuf:"bytes,11,opt,name=Ipv6DestCidrIp,proto3" form:"Ipv6DestCidrIp" json:"Ipv6DestCidrIp" query:"Ipv6DestCidrIp"`
	Ipv6SourceCidrIp        string     `protobuf:"bytes,12,opt,name=Ipv6SourceCidrIp,proto3" form:"Ipv6SourceCidrIp" json:"Ipv6SourceCidrIp" query:"Ipv6SourceCidrIp"`
	NicType                 string     `protobuf:"bytes,13,opt,name=NicType,proto3" form:"NicType" json:"NicType" query:"NicType"`
	Policy                  string     `protobuf:"bytes,14,opt,name=Policy,proto3" form:"Policy" json:"Policy" query:"Policy"`
	PortRange               string     `protobuf:"bytes,15,opt,name=PortRange,proto3" form:"PortRange" json:"PortRange" query:"PortRange"`
	Priority                int32      `protobuf:"varint,16,opt,name=Priority,proto3" form:"Priority" json:"Priority" query:"Priority"`
	SourceCidrIp            string     `protobuf:"bytes,17,opt,name=SourceCidrIp,proto3" form:"SourceCidrIp" json:"SourceCidrIp" query:"SourceCidrIp"`
	SourceGroupId           string     `protobuf:"bytes,18,opt,name=SourceGroupId,proto3" form:"SourceGroupId" json:"SourceGroupId" query:"SourceGroupId"`
	SourceGroupName         string     `protobuf:"bytes,19,opt,name=SourceGroupName,proto3" form:"SourceGroupName" json:"SourceGroupName" query:"SourceGroupName"`
	SourceGroupOwnerAccount string     `protobuf:"bytes,20,opt,name=SourceGroupOwnerAccount,proto3" form:"SourceGroupOwnerAccount" json:"SourceGroupOwnerAccount" query:"SourceGroupOwnerAccount"`
	SourcePortRange         string     `protobuf:"bytes,21,opt,name=SourcePortRange,proto3" form:"SourcePortRange" json:"SourcePortRange" query:"SourcePortRange"`
	SourcePrefixListId      string     `protobuf:"bytes,22,opt,name=SourcePrefixListId,proto3" form:"SourcePrefixListId" json:"SourcePrefixListId" query:"SourcePrefixListId"`
	SourcePrefixListName    string     `protobuf:"bytes,23,opt,name=SourcePrefixListName,proto3" form:"SourcePrefixListName" json:"SourcePrefixListName" query:"SourcePrefixListName"`
	SecurityGroupRuleId     string     `protobuf:"bytes,24,opt,name=SecurityGroupRuleId,proto3" form:"SecurityGroupRuleId" json:"SecurityGroupRuleId" query:"SecurityGroupRuleId"`
	Tags                    []*RuleTag `protobuf:"bytes,25,rep,name=tags,proto3" form:"tags" json:"tags" query:"tags"`
}

func (x *SecurityGroupPermission) Reset() {
	*x = SecurityGroupPermission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityGroupPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityGroupPermission) ProtoMessage() {}

func (x *SecurityGroupPermission) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityGroupPermission.ProtoReflect.Descriptor instead.
func (*SecurityGroupPermission) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{3}
}

func (x *SecurityGroupPermission) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *SecurityGroupPermission) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SecurityGroupPermission) GetDestCidrIp() string {
	if x != nil {
		return x.DestCidrIp
	}
	return ""
}

func (x *SecurityGroupPermission) GetDestGroupId() string {
	if x != nil {
		return x.DestGroupId
	}
	return ""
}

func (x *SecurityGroupPermission) GetDestGroupName() string {
	if x != nil {
		return x.DestGroupName
	}
	return ""
}

func (x *SecurityGroupPermission) GetDestGroupOwnerAccount() string {
	if x != nil {
		return x.DestGroupOwnerAccount
	}
	return ""
}

func (x *SecurityGroupPermission) GetDestPrefixListId() string {
	if x != nil {
		return x.DestPrefixListId
	}
	return ""
}

func (x *SecurityGroupPermission) GetDestPrefixListName() string {
	if x != nil {
		return x.DestPrefixListName
	}
	return ""
}

func (x *SecurityGroupPermission) GetDirection() string {
	if x != nil {
		return x.Direction
	}
	return ""
}

func (x *SecurityGroupPermission) GetIpProtocol() string {
	if x != nil {
		return x.IpProtocol
	}
	return ""
}

func (x *SecurityGroupPermission) GetIpv6DestCidrIp() string {
	if x != nil {
		return x.Ipv6DestCidrIp
	}
	return ""
}

func (x *SecurityGroupPermission) GetIpv6SourceCidrIp() string {
	if x != nil {
		return x.Ipv6SourceCidrIp
	}
	return ""
}

func (x *SecurityGroupPermission) GetNicType() string {
	if x != nil {
		return x.NicType
	}
	return ""
}

func (x *SecurityGroupPermission) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *SecurityGroupPermission) GetPortRange() string {
	if x != nil {
		return x.PortRange
	}
	return ""
}

func (x *SecurityGroupPermission) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SecurityGroupPermission) GetSourceCidrIp() string {
	if x != nil {
		return x.SourceCidrIp
	}
	return ""
}

func (x *SecurityGroupPermission) GetSourceGroupId() string {
	if x != nil {
		return x.SourceGroupId
	}
	return ""
}

func (x *SecurityGroupPermission) GetSourceGroupName() string {
	if x != nil {
		return x.SourceGroupName
	}
	return ""
}

func (x *SecurityGroupPermission) GetSourceGroupOwnerAccount() string {
	if x != nil {
		return x.SourceGroupOwnerAccount
	}
	return ""
}

func (x *SecurityGroupPermission) GetSourcePortRange() string {
	if x != nil {
		return x.SourcePortRange
	}
	return ""
}

func (x *SecurityGroupPermission) GetSourcePrefixListId() string {
	if x != nil {
		return x.SourcePrefixListId
	}
	return ""
}

func (x *SecurityGroupPermission) GetSourcePrefixListName() string {
	if x != nil {
		return x.SourcePrefixListName
	}
	return ""
}

func (x *SecurityGroupPermission) GetSecurityGroupRuleId() string {
	if x != nil {
		return x.SecurityGroupRuleId
	}
	return ""
}

func (x *SecurityGroupPermission) GetTags() []*RuleTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

type DescribeSecurityGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page               uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size               uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp                string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId           string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	SearchKey          string `protobuf:"bytes,5,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue        string `protobuf:"bytes,6,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
	CustomTag          string `protobuf:"bytes,7,opt,name=custom_tag,json=customTag,proto3" form:"custom_tag" json:"custom_tag" query:"custom_tag"`
	Level              string `protobuf:"bytes,8,opt,name=level,proto3" form:"level" json:"level" query:"level"`
	DisplayNeedCleanUp bool   `protobuf:"varint,9,opt,name=display_need_clean_up,json=displayNeedCleanUp,proto3" form:"display_need_clean_up" json:"display_need_clean_up" query:"display_need_clean_up"`
	Type               string `protobuf:"bytes,10,opt,name=type,proto3" form:"type" json:"type" query:"type"`
}

func (x *DescribeSecurityGroupReq) Reset() {
	*x = DescribeSecurityGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSecurityGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSecurityGroupReq) ProtoMessage() {}

func (x *DescribeSecurityGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSecurityGroupReq.ProtoReflect.Descriptor instead.
func (*DescribeSecurityGroupReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{4}
}

func (x *DescribeSecurityGroupReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeSecurityGroupReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeSecurityGroupReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeSecurityGroupReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeSecurityGroupReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *DescribeSecurityGroupReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

func (x *DescribeSecurityGroupReq) GetCustomTag() string {
	if x != nil {
		return x.CustomTag
	}
	return ""
}

func (x *DescribeSecurityGroupReq) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *DescribeSecurityGroupReq) GetDisplayNeedCleanUp() bool {
	if x != nil {
		return x.DisplayNeedCleanUp
	}
	return false
}

func (x *DescribeSecurityGroupReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type DescribeSecurityGroupRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                  `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*SecurityGroupEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeSecurityGroupRes) Reset() {
	*x = DescribeSecurityGroupRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSecurityGroupRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSecurityGroupRes) ProtoMessage() {}

func (x *DescribeSecurityGroupRes) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSecurityGroupRes.ProtoReflect.Descriptor instead.
func (*DescribeSecurityGroupRes) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeSecurityGroupRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeSecurityGroupRes) GetList() []*SecurityGroupEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type DescribeSecurityGroupByInstancesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page         uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size         uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	InstanceType string   `protobuf:"bytes,3,opt,name=instanceType,proto3" form:"instanceType" json:"instanceType" query:"instanceType"`
	InstanceIds  []string `protobuf:"bytes,4,rep,name=instance_ids,json=instanceIds,proto3" form:"instance_ids" json:"instance_ids" query:"instance_ids"`
}

func (x *DescribeSecurityGroupByInstancesReq) Reset() {
	*x = DescribeSecurityGroupByInstancesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSecurityGroupByInstancesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSecurityGroupByInstancesReq) ProtoMessage() {}

func (x *DescribeSecurityGroupByInstancesReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSecurityGroupByInstancesReq.ProtoReflect.Descriptor instead.
func (*DescribeSecurityGroupByInstancesReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{6}
}

func (x *DescribeSecurityGroupByInstancesReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeSecurityGroupByInstancesReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeSecurityGroupByInstancesReq) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *DescribeSecurityGroupByInstancesReq) GetInstanceIds() []string {
	if x != nil {
		return x.InstanceIds
	}
	return nil
}

type DescribeSecurityGroupByInstancesRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                  `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*SecurityGroupEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeSecurityGroupByInstancesRes) Reset() {
	*x = DescribeSecurityGroupByInstancesRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSecurityGroupByInstancesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSecurityGroupByInstancesRes) ProtoMessage() {}

func (x *DescribeSecurityGroupByInstancesRes) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSecurityGroupByInstancesRes.ProtoReflect.Descriptor instead.
func (*DescribeSecurityGroupByInstancesRes) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{7}
}

func (x *DescribeSecurityGroupByInstancesRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeSecurityGroupByInstancesRes) GetList() []*SecurityGroupEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type DescribeRulesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size      uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp       string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId  string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IpType    string `protobuf:"bytes,5,opt,name=ip_type,json=ipType,proto3" form:"ip_type" json:"ip_type" query:"ip_type"`
	PortRange string `protobuf:"bytes,6,opt,name=portRange,proto3" form:"portRange" json:"portRange" query:"portRange"`
	Priority  int32  `protobuf:"varint,7,opt,name=priority,proto3" form:"priority" json:"priority" query:"priority"`
	IpStr     string `protobuf:"bytes,8,opt,name=ip_str,json=ipStr,proto3" form:"ip_str" json:"ip_str" query:"ip_str"`
}

func (x *DescribeRulesReq) Reset() {
	*x = DescribeRulesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRulesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRulesReq) ProtoMessage() {}

func (x *DescribeRulesReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRulesReq.ProtoReflect.Descriptor instead.
func (*DescribeRulesReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{8}
}

func (x *DescribeRulesReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeRulesReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeRulesReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeRulesReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeRulesReq) GetIpType() string {
	if x != nil {
		return x.IpType
	}
	return ""
}

func (x *DescribeRulesReq) GetPortRange() string {
	if x != nil {
		return x.PortRange
	}
	return ""
}

func (x *DescribeRulesReq) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *DescribeRulesReq) GetIpStr() string {
	if x != nil {
		return x.IpStr
	}
	return ""
}

type DescribeRulesRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*SecurityGroupPermission `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total int32                      `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *DescribeRulesRes) Reset() {
	*x = DescribeRulesRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRulesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRulesRes) ProtoMessage() {}

func (x *DescribeRulesRes) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRulesRes.ProtoReflect.Descriptor instead.
func (*DescribeRulesRes) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{9}
}

func (x *DescribeRulesRes) GetList() []*SecurityGroupPermission {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DescribeRulesRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type UpdateSecurityGroupRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SgId        string                     `protobuf:"bytes,1,opt,name=sg_id,json=sgId,proto3" form:"sg_id" json:"sg_id" query:"sg_id"`
	Permissions []*SecurityGroupPermission `protobuf:"bytes,2,rep,name=permissions,proto3" form:"permissions" json:"permissions" query:"permissions"`
	LevelTag    *RuleTag                   `protobuf:"bytes,3,opt,name=level_tag,json=levelTag,proto3" form:"level_tag" json:"level_tag" query:"level_tag"`
}

func (x *UpdateSecurityGroupRuleReq) Reset() {
	*x = UpdateSecurityGroupRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSecurityGroupRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSecurityGroupRuleReq) ProtoMessage() {}

func (x *UpdateSecurityGroupRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSecurityGroupRuleReq.ProtoReflect.Descriptor instead.
func (*UpdateSecurityGroupRuleReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateSecurityGroupRuleReq) GetSgId() string {
	if x != nil {
		return x.SgId
	}
	return ""
}

func (x *UpdateSecurityGroupRuleReq) GetPermissions() []*SecurityGroupPermission {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *UpdateSecurityGroupRuleReq) GetLevelTag() *RuleTag {
	if x != nil {
		return x.LevelTag
	}
	return nil
}

type RuleTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level string `protobuf:"bytes,1,opt,name=level,proto3" form:"level" json:"level" query:"level"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" form:"value" json:"value" query:"value"`
}

func (x *RuleTag) Reset() {
	*x = RuleTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleTag) ProtoMessage() {}

func (x *RuleTag) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleTag.ProtoReflect.Descriptor instead.
func (*RuleTag) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{11}
}

func (x *RuleTag) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *RuleTag) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type UpdateSecurityGroupsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sgs []*SecurityGroupEntity `protobuf:"bytes,1,rep,name=sgs,proto3" form:"sgs" json:"sgs" query:"sgs"`
}

func (x *UpdateSecurityGroupsReq) Reset() {
	*x = UpdateSecurityGroupsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSecurityGroupsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSecurityGroupsReq) ProtoMessage() {}

func (x *UpdateSecurityGroupsReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSecurityGroupsReq.ProtoReflect.Descriptor instead.
func (*UpdateSecurityGroupsReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateSecurityGroupsReq) GetSgs() []*SecurityGroupEntity {
	if x != nil {
		return x.Sgs
	}
	return nil
}

type GetAccountRegionTagsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IspId    string `protobuf:"bytes,1,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	RegionId string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *GetAccountRegionTagsReq) Reset() {
	*x = GetAccountRegionTagsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountRegionTagsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountRegionTagsReq) ProtoMessage() {}

func (x *GetAccountRegionTagsReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountRegionTagsReq.ProtoReflect.Descriptor instead.
func (*GetAccountRegionTagsReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{13}
}

func (x *GetAccountRegionTagsReq) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *GetAccountRegionTagsReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type GetAccountRegionTagsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomTags []string `protobuf:"bytes,1,rep,name=customTags,proto3" form:"customTags" json:"customTags" query:"customTags"`
}

func (x *GetAccountRegionTagsRes) Reset() {
	*x = GetAccountRegionTagsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountRegionTagsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountRegionTagsRes) ProtoMessage() {}

func (x *GetAccountRegionTagsRes) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountRegionTagsRes.ProtoReflect.Descriptor instead.
func (*GetAccountRegionTagsRes) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{14}
}

func (x *GetAccountRegionTagsRes) GetCustomTags() []string {
	if x != nil {
		return x.CustomTags
	}
	return nil
}

type GetCustomTagSecurityGroupsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IspId     string `protobuf:"bytes,1,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	RegionId  string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	CustomTag string `protobuf:"bytes,3,opt,name=custom_tag,json=customTag,proto3" form:"custom_tag" json:"custom_tag" query:"custom_tag"`
}

func (x *GetCustomTagSecurityGroupsReq) Reset() {
	*x = GetCustomTagSecurityGroupsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomTagSecurityGroupsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomTagSecurityGroupsReq) ProtoMessage() {}

func (x *GetCustomTagSecurityGroupsReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomTagSecurityGroupsReq.ProtoReflect.Descriptor instead.
func (*GetCustomTagSecurityGroupsReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{15}
}

func (x *GetCustomTagSecurityGroupsReq) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *GetCustomTagSecurityGroupsReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *GetCustomTagSecurityGroupsReq) GetCustomTag() string {
	if x != nil {
		return x.CustomTag
	}
	return ""
}

type GetCustomTagSecurityGroupsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32                  `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*SecurityGroupEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *GetCustomTagSecurityGroupsRes) Reset() {
	*x = GetCustomTagSecurityGroupsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomTagSecurityGroupsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomTagSecurityGroupsRes) ProtoMessage() {}

func (x *GetCustomTagSecurityGroupsRes) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomTagSecurityGroupsRes.ProtoReflect.Descriptor instead.
func (*GetCustomTagSecurityGroupsRes) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{16}
}

func (x *GetCustomTagSecurityGroupsRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetCustomTagSecurityGroupsRes) GetList() []*SecurityGroupEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type UpdateSecurityGroupCustomTagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SgIds     []string `protobuf:"bytes,1,rep,name=sg_ids,json=sgIds,proto3" form:"sg_ids" json:"sg_ids" query:"sg_ids"`
	CustomTag string   `protobuf:"bytes,2,opt,name=custom_tag,json=customTag,proto3" form:"custom_tag" json:"custom_tag" query:"custom_tag"`
}

func (x *UpdateSecurityGroupCustomTagReq) Reset() {
	*x = UpdateSecurityGroupCustomTagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSecurityGroupCustomTagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSecurityGroupCustomTagReq) ProtoMessage() {}

func (x *UpdateSecurityGroupCustomTagReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSecurityGroupCustomTagReq.ProtoReflect.Descriptor instead.
func (*UpdateSecurityGroupCustomTagReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateSecurityGroupCustomTagReq) GetSgIds() []string {
	if x != nil {
		return x.SgIds
	}
	return nil
}

func (x *UpdateSecurityGroupCustomTagReq) GetCustomTag() string {
	if x != nil {
		return x.CustomTag
	}
	return ""
}

type ExportSecurityGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keys []string `protobuf:"bytes,1,rep,name=keys,proto3" form:"keys" json:"keys" query:"keys"`
}

func (x *ExportSecurityGroupReq) Reset() {
	*x = ExportSecurityGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportSecurityGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportSecurityGroupReq) ProtoMessage() {}

func (x *ExportSecurityGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportSecurityGroupReq.ProtoReflect.Descriptor instead.
func (*ExportSecurityGroupReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{18}
}

func (x *ExportSecurityGroupReq) GetKeys() []string {
	if x != nil {
		return x.Keys
	}
	return nil
}

type ExportSecurityGroupRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SgId string `protobuf:"bytes,1,opt,name=sg_id,json=sgId,proto3" form:"sg_id" json:"sg_id" query:"sg_id"`
}

func (x *ExportSecurityGroupRuleReq) Reset() {
	*x = ExportSecurityGroupRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportSecurityGroupRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportSecurityGroupRuleReq) ProtoMessage() {}

func (x *ExportSecurityGroupRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportSecurityGroupRuleReq.ProtoReflect.Descriptor instead.
func (*ExportSecurityGroupRuleReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{19}
}

func (x *ExportSecurityGroupRuleReq) GetSgId() string {
	if x != nil {
		return x.SgId
	}
	return ""
}

type ExportRelateInstancesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SgIds                     []string `protobuf:"bytes,1,rep,name=sg_ids,json=sgIds,proto3" form:"sg_ids" json:"sg_ids" query:"sg_ids"`
	AndSearchSecurityGroupIds bool     `protobuf:"varint,2,opt,name=and_search_security_group_ids,json=andSearchSecurityGroupIds,proto3" form:"and_search_security_group_ids" json:"and_search_security_group_ids" query:"and_search_security_group_ids"`
	InstanceType              string   `protobuf:"bytes,3,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
}

func (x *ExportRelateInstancesReq) Reset() {
	*x = ExportRelateInstancesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportRelateInstancesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportRelateInstancesReq) ProtoMessage() {}

func (x *ExportRelateInstancesReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportRelateInstancesReq.ProtoReflect.Descriptor instead.
func (*ExportRelateInstancesReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{20}
}

func (x *ExportRelateInstancesReq) GetSgIds() []string {
	if x != nil {
		return x.SgIds
	}
	return nil
}

func (x *ExportRelateInstancesReq) GetAndSearchSecurityGroupIds() bool {
	if x != nil {
		return x.AndSearchSecurityGroupIds
	}
	return false
}

func (x *ExportRelateInstancesReq) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

type DescribeIPWhitelistsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	WhitelistId string `protobuf:"bytes,3,opt,name=whitelist_id,json=whitelistId,proto3" form:"whitelist_id" json:"whitelist_id" query:"whitelist_id"`
	Name        string `protobuf:"bytes,4,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *DescribeIPWhitelistsReq) Reset() {
	*x = DescribeIPWhitelistsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIPWhitelistsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIPWhitelistsReq) ProtoMessage() {}

func (x *DescribeIPWhitelistsReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIPWhitelistsReq.ProtoReflect.Descriptor instead.
func (*DescribeIPWhitelistsReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{21}
}

func (x *DescribeIPWhitelistsReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeIPWhitelistsReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeIPWhitelistsReq) GetWhitelistId() string {
	if x != nil {
		return x.WhitelistId
	}
	return ""
}

func (x *DescribeIPWhitelistsReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeIPWhitelistsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*IPWhitelist `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total int32          `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *DescribeIPWhitelistsResp) Reset() {
	*x = DescribeIPWhitelistsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIPWhitelistsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIPWhitelistsResp) ProtoMessage() {}

func (x *DescribeIPWhitelistsResp) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIPWhitelistsResp.ProtoReflect.Descriptor instead.
func (*DescribeIPWhitelistsResp) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{22}
}

func (x *DescribeIPWhitelistsResp) GetList() []*IPWhitelist {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DescribeIPWhitelistsResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type IPWhitelist struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string   `protobuf:"bytes,1,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	WhitelistId    string   `protobuf:"bytes,2,opt,name=whitelist_id,json=whitelistId,proto3" form:"whitelist_id" json:"whitelist_id" query:"whitelist_id"`
	SecurityIpType string   `protobuf:"bytes,3,opt,name=security_ip_type,json=securityIpType,proto3" form:"security_ip_type" json:"security_ip_type" query:"security_ip_type"`
	Ips            []string `protobuf:"bytes,4,rep,name=ips,proto3" form:"ips" json:"ips" query:"ips"`
	DbInstanceIds  []string `protobuf:"bytes,5,rep,name=db_instance_ids,json=dbInstanceIds,proto3" form:"db_instance_ids" json:"db_instance_ids" query:"db_instance_ids"`
	RegionId       string   `protobuf:"bytes,6,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IspId          string   `protobuf:"bytes,7,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType        string   `protobuf:"bytes,8,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName        string   `protobuf:"bytes,9,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
}

func (x *IPWhitelist) Reset() {
	*x = IPWhitelist{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPWhitelist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPWhitelist) ProtoMessage() {}

func (x *IPWhitelist) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPWhitelist.ProtoReflect.Descriptor instead.
func (*IPWhitelist) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{23}
}

func (x *IPWhitelist) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IPWhitelist) GetWhitelistId() string {
	if x != nil {
		return x.WhitelistId
	}
	return ""
}

func (x *IPWhitelist) GetSecurityIpType() string {
	if x != nil {
		return x.SecurityIpType
	}
	return ""
}

func (x *IPWhitelist) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *IPWhitelist) GetDbInstanceIds() []string {
	if x != nil {
		return x.DbInstanceIds
	}
	return nil
}

func (x *IPWhitelist) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *IPWhitelist) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *IPWhitelist) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *IPWhitelist) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

type ModifyIPWhitelistsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SecurityGroupId string   `protobuf:"bytes,1,opt,name=security_group_id,json=securityGroupId,proto3" json:"security_group_id" path:"security_group_id"`
	Ips             []string `protobuf:"bytes,2,rep,name=ips,proto3" form:"ips" json:"ips" query:"ips"`
}

func (x *ModifyIPWhitelistsReq) Reset() {
	*x = ModifyIPWhitelistsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyIPWhitelistsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyIPWhitelistsReq) ProtoMessage() {}

func (x *ModifyIPWhitelistsReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyIPWhitelistsReq.ProtoReflect.Descriptor instead.
func (*ModifyIPWhitelistsReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{24}
}

func (x *ModifyIPWhitelistsReq) GetSecurityGroupId() string {
	if x != nil {
		return x.SecurityGroupId
	}
	return ""
}

func (x *ModifyIPWhitelistsReq) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

type DescribeALBAclsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size  uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	AclId string `protobuf:"bytes,3,opt,name=acl_id,json=aclId,proto3" form:"acl_id" json:"acl_id" query:"acl_id"`
	Name  string `protobuf:"bytes,4,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *DescribeALBAclsReq) Reset() {
	*x = DescribeALBAclsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeALBAclsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeALBAclsReq) ProtoMessage() {}

func (x *DescribeALBAclsReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeALBAclsReq.ProtoReflect.Descriptor instead.
func (*DescribeALBAclsReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{25}
}

func (x *DescribeALBAclsReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeALBAclsReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeALBAclsReq) GetAclId() string {
	if x != nil {
		return x.AclId
	}
	return ""
}

func (x *DescribeALBAclsReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeALBAclsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*ALBAclEntity `protobuf:"bytes,1,rep,name=list,proto3" form:"list" json:"list" query:"list"`
	Total int32           `protobuf:"varint,2,opt,name=total,proto3" form:"total" json:"total" query:"total"`
}

func (x *DescribeALBAclsResp) Reset() {
	*x = DescribeALBAclsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeALBAclsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeALBAclsResp) ProtoMessage() {}

func (x *DescribeALBAclsResp) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeALBAclsResp.ProtoReflect.Descriptor instead.
func (*DescribeALBAclsResp) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{26}
}

func (x *DescribeALBAclsResp) GetList() []*ALBAclEntity {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DescribeALBAclsResp) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ALBAclEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AclId            string             `protobuf:"bytes,1,opt,name=acl_id,json=aclId,proto3" form:"acl_id" json:"acl_id" query:"acl_id"`
	Name             string             `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Status           string             `protobuf:"bytes,3,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	AddressIpVersion string             `protobuf:"bytes,4,opt,name=address_ip_version,json=addressIpVersion,proto3" form:"address_ip_version" json:"address_ip_version" query:"address_ip_version"`
	CreateTime       string             `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" form:"create_time" json:"create_time" query:"create_time"`
	AclEntries       []*AclEntry        `protobuf:"bytes,6,rep,name=acl_entries,json=aclEntries,proto3" form:"acl_entries" json:"acl_entries" query:"acl_entries"`
	RelatedListeners []*RelatedListener `protobuf:"bytes,7,rep,name=related_listeners,json=relatedListeners,proto3" form:"related_listeners" json:"related_listeners" query:"related_listeners"`
	RegionId         string             `protobuf:"bytes,8,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	IspId            string             `protobuf:"bytes,9,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType          string             `protobuf:"bytes,10,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName          string             `protobuf:"bytes,11,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
}

func (x *ALBAclEntity) Reset() {
	*x = ALBAclEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ALBAclEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ALBAclEntity) ProtoMessage() {}

func (x *ALBAclEntity) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ALBAclEntity.ProtoReflect.Descriptor instead.
func (*ALBAclEntity) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{27}
}

func (x *ALBAclEntity) GetAclId() string {
	if x != nil {
		return x.AclId
	}
	return ""
}

func (x *ALBAclEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ALBAclEntity) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ALBAclEntity) GetAddressIpVersion() string {
	if x != nil {
		return x.AddressIpVersion
	}
	return ""
}

func (x *ALBAclEntity) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ALBAclEntity) GetAclEntries() []*AclEntry {
	if x != nil {
		return x.AclEntries
	}
	return nil
}

func (x *ALBAclEntity) GetRelatedListeners() []*RelatedListener {
	if x != nil {
		return x.RelatedListeners
	}
	return nil
}

func (x *ALBAclEntity) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *ALBAclEntity) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *ALBAclEntity) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *ALBAclEntity) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

type AclEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      string `protobuf:"bytes,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Ip          string `protobuf:"bytes,2,opt,name=ip,proto3" form:"ip" json:"ip" query:"ip"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" form:"description" json:"description" query:"description"`
}

func (x *AclEntry) Reset() {
	*x = AclEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AclEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AclEntry) ProtoMessage() {}

func (x *AclEntry) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AclEntry.ProtoReflect.Descriptor instead.
func (*AclEntry) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{28}
}

func (x *AclEntry) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AclEntry) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *AclEntry) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type RelatedListener struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           string `protobuf:"bytes,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	ListenerPort     string `protobuf:"bytes,2,opt,name=listener_port,json=listenerPort,proto3" form:"listener_port" json:"listener_port" query:"listener_port"`
	ListenerProtocol string `protobuf:"bytes,3,opt,name=listener_protocol,json=listenerProtocol,proto3" form:"listener_protocol" json:"listener_protocol" query:"listener_protocol"`
	LbId             string `protobuf:"bytes,4,opt,name=lb_id,json=lbId,proto3" form:"lb_id" json:"lb_id" query:"lb_id"`
}

func (x *RelatedListener) Reset() {
	*x = RelatedListener{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelatedListener) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelatedListener) ProtoMessage() {}

func (x *RelatedListener) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelatedListener.ProtoReflect.Descriptor instead.
func (*RelatedListener) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{29}
}

func (x *RelatedListener) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *RelatedListener) GetListenerPort() string {
	if x != nil {
		return x.ListenerPort
	}
	return ""
}

func (x *RelatedListener) GetListenerProtocol() string {
	if x != nil {
		return x.ListenerProtocol
	}
	return ""
}

func (x *RelatedListener) GetLbId() string {
	if x != nil {
		return x.LbId
	}
	return ""
}

type BatchDeleteSecurityGroupRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SecurityGroupId string   `protobuf:"bytes,1,opt,name=security_group_id,json=securityGroupId,proto3" form:"security_group_id" json:"security_group_id" query:"security_group_id"`
	Ids             []string `protobuf:"bytes,2,rep,name=ids,proto3" form:"ids" json:"ids" query:"ids"`
}

func (x *BatchDeleteSecurityGroupRuleReq) Reset() {
	*x = BatchDeleteSecurityGroupRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteSecurityGroupRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteSecurityGroupRuleReq) ProtoMessage() {}

func (x *BatchDeleteSecurityGroupRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteSecurityGroupRuleReq.ProtoReflect.Descriptor instead.
func (*BatchDeleteSecurityGroupRuleReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{30}
}

func (x *BatchDeleteSecurityGroupRuleReq) GetSecurityGroupId() string {
	if x != nil {
		return x.SecurityGroupId
	}
	return ""
}

func (x *BatchDeleteSecurityGroupRuleReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type CreateSecurityGroupRuleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SecurityGroupId string                           `protobuf:"bytes,1,opt,name=security_group_id,json=securityGroupId,proto3" form:"security_group_id" json:"security_group_id" query:"security_group_id"`
	Permissions     []*CreateSecurityGroupRuleEntity `protobuf:"bytes,2,rep,name=permissions,proto3" form:"permissions" json:"permissions" query:"permissions"`
}

func (x *CreateSecurityGroupRuleReq) Reset() {
	*x = CreateSecurityGroupRuleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSecurityGroupRuleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecurityGroupRuleReq) ProtoMessage() {}

func (x *CreateSecurityGroupRuleReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecurityGroupRuleReq.ProtoReflect.Descriptor instead.
func (*CreateSecurityGroupRuleReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{31}
}

func (x *CreateSecurityGroupRuleReq) GetSecurityGroupId() string {
	if x != nil {
		return x.SecurityGroupId
	}
	return ""
}

func (x *CreateSecurityGroupRuleReq) GetPermissions() []*CreateSecurityGroupRuleEntity {
	if x != nil {
		return x.Permissions
	}
	return nil
}

type CreateSecurityGroupRuleEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IpProtocol   string `protobuf:"bytes,1,opt,name=ip_protocol,json=ipProtocol,proto3" form:"ip_protocol" json:"ip_protocol" query:"ip_protocol"`
	SourceCidrIp string `protobuf:"bytes,2,opt,name=source_cidr_ip,json=sourceCidrIp,proto3" form:"source_cidr_ip" json:"source_cidr_ip" query:"source_cidr_ip"`
	PortRange    string `protobuf:"bytes,3,opt,name=port_range,json=portRange,proto3" form:"port_range" json:"port_range" query:"port_range"`
	Policy       string `protobuf:"bytes,4,opt,name=policy,proto3" form:"policy" json:"policy" query:"policy"`
	Description  string `protobuf:"bytes,5,opt,name=description,proto3" form:"description" json:"description" query:"description"`
	Priority     int32  `protobuf:"varint,6,opt,name=priority,proto3" form:"priority" json:"priority" query:"priority"`
}

func (x *CreateSecurityGroupRuleEntity) Reset() {
	*x = CreateSecurityGroupRuleEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSecurityGroupRuleEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecurityGroupRuleEntity) ProtoMessage() {}

func (x *CreateSecurityGroupRuleEntity) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecurityGroupRuleEntity.ProtoReflect.Descriptor instead.
func (*CreateSecurityGroupRuleEntity) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{32}
}

func (x *CreateSecurityGroupRuleEntity) GetIpProtocol() string {
	if x != nil {
		return x.IpProtocol
	}
	return ""
}

func (x *CreateSecurityGroupRuleEntity) GetSourceCidrIp() string {
	if x != nil {
		return x.SourceCidrIp
	}
	return ""
}

func (x *CreateSecurityGroupRuleEntity) GetPortRange() string {
	if x != nil {
		return x.PortRange
	}
	return ""
}

func (x *CreateSecurityGroupRuleEntity) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *CreateSecurityGroupRuleEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateSecurityGroupRuleEntity) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type AddALBAclsEntriesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AclId   string             `protobuf:"bytes,1,opt,name=acl_id,json=aclId,proto3" json:"acl_id" path:"acl_id"`
	Entries []*AddALBAclsEntry `protobuf:"bytes,2,rep,name=entries,proto3" form:"entries" json:"entries" query:"entries"`
}

func (x *AddALBAclsEntriesReq) Reset() {
	*x = AddALBAclsEntriesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddALBAclsEntriesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddALBAclsEntriesReq) ProtoMessage() {}

func (x *AddALBAclsEntriesReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddALBAclsEntriesReq.ProtoReflect.Descriptor instead.
func (*AddALBAclsEntriesReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{33}
}

func (x *AddALBAclsEntriesReq) GetAclId() string {
	if x != nil {
		return x.AclId
	}
	return ""
}

func (x *AddALBAclsEntriesReq) GetEntries() []*AddALBAclsEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

type AddALBAclsEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entry       string `protobuf:"bytes,1,opt,name=entry,proto3" form:"entry" json:"entry" query:"entry"`
	Description string `protobuf:"bytes,2,opt,name=description,proto3" form:"description" json:"description" query:"description"`
}

func (x *AddALBAclsEntry) Reset() {
	*x = AddALBAclsEntry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddALBAclsEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddALBAclsEntry) ProtoMessage() {}

func (x *AddALBAclsEntry) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddALBAclsEntry.ProtoReflect.Descriptor instead.
func (*AddALBAclsEntry) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{34}
}

func (x *AddALBAclsEntry) GetEntry() string {
	if x != nil {
		return x.Entry
	}
	return ""
}

func (x *AddALBAclsEntry) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type RemoveALBAclsEntriesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AclId   string   `protobuf:"bytes,1,opt,name=acl_id,json=aclId,proto3" json:"acl_id" path:"acl_id"`
	Entries []string `protobuf:"bytes,2,rep,name=entries,proto3" form:"entries" json:"entries" query:"entries"`
}

func (x *RemoveALBAclsEntriesReq) Reset() {
	*x = RemoveALBAclsEntriesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveALBAclsEntriesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveALBAclsEntriesReq) ProtoMessage() {}

func (x *RemoveALBAclsEntriesReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveALBAclsEntriesReq.ProtoReflect.Descriptor instead.
func (*RemoveALBAclsEntriesReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{35}
}

func (x *RemoveALBAclsEntriesReq) GetAclId() string {
	if x != nil {
		return x.AclId
	}
	return ""
}

func (x *RemoveALBAclsEntriesReq) GetEntries() []string {
	if x != nil {
		return x.Entries
	}
	return nil
}

type JoinOrLeaveSecurityGroupReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId          string                `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	InstanceType      string                `protobuf:"bytes,2,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
	InstanceInfo      []*InstanceIdNamePair `protobuf:"bytes,3,rep,name=instance_info,json=instanceInfo,proto3" form:"instance_info" json:"instance_info" query:"instance_info"`
	SecurityGroupName string                `protobuf:"bytes,4,opt,name=security_group_name,json=securityGroupName,proto3" form:"security_group_name" json:"security_group_name" query:"security_group_name"`
}

func (x *JoinOrLeaveSecurityGroupReq) Reset() {
	*x = JoinOrLeaveSecurityGroupReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JoinOrLeaveSecurityGroupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinOrLeaveSecurityGroupReq) ProtoMessage() {}

func (x *JoinOrLeaveSecurityGroupReq) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinOrLeaveSecurityGroupReq.ProtoReflect.Descriptor instead.
func (*JoinOrLeaveSecurityGroupReq) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{36}
}

func (x *JoinOrLeaveSecurityGroupReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *JoinOrLeaveSecurityGroupReq) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *JoinOrLeaveSecurityGroupReq) GetInstanceInfo() []*InstanceIdNamePair {
	if x != nil {
		return x.InstanceInfo
	}
	return nil
}

func (x *JoinOrLeaveSecurityGroupReq) GetSecurityGroupName() string {
	if x != nil {
		return x.SecurityGroupName
	}
	return ""
}

type FailedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId string `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	Message    string `protobuf:"bytes,2,opt,name=message,proto3" form:"message" json:"message" query:"message"`
}

func (x *FailedInfo) Reset() {
	*x = FailedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FailedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FailedInfo) ProtoMessage() {}

func (x *FailedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FailedInfo.ProtoReflect.Descriptor instead.
func (*FailedInfo) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{37}
}

func (x *FailedInfo) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *FailedInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ModifySecurityGroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessInstanceIds []string      `protobuf:"bytes,1,rep,name=success_instance_ids,json=successInstanceIds,proto3" form:"success_instance_ids" json:"success_instance_ids" query:"success_instance_ids"`
	FailedInfos        []*FailedInfo `protobuf:"bytes,2,rep,name=failed_infos,json=failedInfos,proto3" form:"failed_infos" json:"failed_infos" query:"failed_infos"`
}

func (x *ModifySecurityGroupResult) Reset() {
	*x = ModifySecurityGroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_securityGroup_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifySecurityGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifySecurityGroupResult) ProtoMessage() {}

func (x *ModifySecurityGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_securityGroup_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifySecurityGroupResult.ProtoReflect.Descriptor instead.
func (*ModifySecurityGroupResult) Descriptor() ([]byte, []int) {
	return file_securityGroup_proto_rawDescGZIP(), []int{38}
}

func (x *ModifySecurityGroupResult) GetSuccessInstanceIds() []string {
	if x != nil {
		return x.SuccessInstanceIds
	}
	return nil
}

func (x *ModifySecurityGroupResult) GetFailedInfos() []*FailedInfo {
	if x != nil {
		return x.FailedInfos
	}
	return nil
}

var File_securityGroup_proto protoreflect.FileDescriptor

var file_securityGroup_proto_rawDesc = []byte{
	0x0a, 0x13, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a,
	0x09, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x72, 0x65, 0x73, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x07,
	0x0a, 0x13, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x70, 0x63,
	0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x56, 0x70, 0x63, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x45,
	0x63, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x45,
	0x63, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x17, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x04, 0x54, 0x61, 0x67,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x52,
	0x04, 0x54, 0x61, 0x67, 0x73, 0x12, 0x43, 0x0a, 0x0b, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x69, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x69, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x13, 0x62, 0x69, 0x6e, 0x64, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x13,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x62,
	0x69, 0x6e, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x12,
	0x35, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f,
	0x74, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x52, 0x08, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x54, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x4e, 0x65, 0x65, 0x64, 0x43, 0x6c,
	0x65, 0x61, 0x6e, 0x75, 0x70, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x4e, 0x65, 0x65,
	0x64, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70, 0x22, 0x57, 0x0a, 0x15, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x79, 0x73, 0x71, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x72,
	0x65, 0x64, 0x69, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x65, 0x64, 0x69,
	0x73, 0x22, 0x53, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe8, 0x07, 0x0a, 0x17, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x73, 0x74, 0x43, 0x69, 0x64, 0x72,
	0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x65, 0x73, 0x74, 0x43, 0x69,
	0x64, 0x72, 0x49, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x44,
	0x65, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x15,
	0x44, 0x65, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x44, 0x65, 0x73,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x44, 0x65, 0x73, 0x74, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x44, 0x65,
	0x73, 0x74, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2e,
	0x0a, 0x12, 0x44, 0x65, 0x73, 0x74, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4c, 0x69, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x44, 0x65, 0x73, 0x74,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x49, 0x70, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x49, 0x70, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x26, 0x0a, 0x0e,
	0x49, 0x70, 0x76, 0x36, 0x44, 0x65, 0x73, 0x74, 0x43, 0x69, 0x64, 0x72, 0x49, 0x70, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x49, 0x70, 0x76, 0x36, 0x44, 0x65, 0x73, 0x74, 0x43, 0x69,
	0x64, 0x72, 0x49, 0x70, 0x12, 0x2a, 0x0a, 0x10, 0x49, 0x70, 0x76, 0x36, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x43, 0x69, 0x64, 0x72, 0x49, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x49, 0x70, 0x76, 0x36, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x69, 0x64, 0x72, 0x49, 0x70,
	0x12, 0x18, 0x0a, 0x07, 0x4e, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x4e, 0x69, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x22, 0x0a, 0x0c,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x69, 0x64, 0x72, 0x49, 0x70, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x69, 0x64, 0x72, 0x49, 0x70,
	0x12, 0x24, 0x0a, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x38, 0x0a, 0x17, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f,
	0x77, 0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x17, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x14, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x14, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x22, 0xaf, 0x02, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x31, 0x0a, 0x15,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6c, 0x65,
	0x61, 0x6e, 0x5f, 0x75, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x65, 0x65, 0x64, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x63, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x94, 0x01, 0x0a, 0x23, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x42, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22,
	0x6e, 0x0a, 0x23, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0xd3, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x69, 0x73, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69,
	0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x70, 0x53, 0x74, 0x72, 0x22, 0x5f, 0x0a, 0x10, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xa6, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x13, 0x0a, 0x05, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x67, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0b, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2e, 0x0a, 0x09, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x54, 0x61, 0x67, 0x52, 0x08, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x54, 0x61, 0x67, 0x22,
	0x35, 0x0a, 0x07, 0x52, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4a, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x2f, 0x0a, 0x03, 0x73, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x03, 0x73,
	0x67, 0x73, 0x22, 0x4d, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x73, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x39, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x73, 0x22, 0x72, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69,
	0x73, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x74, 0x61, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67,
	0x22, 0x68, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x57, 0x0a, 0x1f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a,
	0x06, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x67, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x54, 0x61, 0x67, 0x22, 0x2c, 0x0a, 0x16, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79,
	0x73, 0x22, 0x31, 0x0a, 0x1a, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x13, 0x0a, 0x05, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x73, 0x67, 0x49, 0x64, 0x22, 0x98, 0x01, 0x0a, 0x18, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x67, 0x49, 0x64, 0x73, 0x12, 0x40, 0x0a, 0x1d, 0x61, 0x6e, 0x64, 0x5f,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x19, 0x61, 0x6e, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x78, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5b, 0x0a, 0x18, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49,
	0x50, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x92, 0x02, 0x0a, 0x0b, 0x49, 0x50, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x49, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x62, 0x5f,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0d, 0x64, 0x62, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x15,
	0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x6c, 0x0a, 0x15, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x49, 0x50, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x41, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x15, 0xd2, 0xbb, 0x18, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x70, 0x73, 0x22, 0x67, 0x0a, 0x12, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x63, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x63, 0x6c, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x57, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x41, 0x4c,
	0x42, 0x41, 0x63, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d,
	0x61, 0x6e, 0x2e, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x87, 0x03, 0x0a, 0x0c,
	0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x15, 0x0a, 0x06,
	0x61, 0x63, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x63,
	0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x2c, 0x0a, 0x12, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x70, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x49, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33,
	0x0a, 0x0b, 0x61, 0x63, 0x6c, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41,
	0x63, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x63, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x11, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65,
	0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x08, 0x41, 0x63, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x90, 0x01, 0x0a, 0x0f,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x2b, 0x0a, 0x11,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x13, 0x0a, 0x05, 0x6c, 0x62, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x62, 0x49, 0x64, 0x22, 0x5f,
	0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22,
	0x93, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2a,
	0x0a, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0b, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75,
	0x6c, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xdb, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x70, 0x5f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x70,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x69, 0x64, 0x72, 0x49, 0x70, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x22, 0x6e, 0x0a, 0x14, 0x41, 0x64, 0x64, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x06, 0x61,
	0x63, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xd2, 0xbb, 0x18,
	0x06, 0x61, 0x63, 0x6c, 0x5f, 0x69, 0x64, 0x52, 0x05, 0x61, 0x63, 0x6c, 0x49, 0x64, 0x12, 0x33,
	0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x4c,
	0x42, 0x41, 0x63, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x22, 0x49, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x56,
	0x0a, 0x17, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x41, 0x4c, 0x42, 0x41, 0x63, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x06, 0x61, 0x63, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xd2, 0xbb, 0x18, 0x06, 0x61,
	0x63, 0x6c, 0x5f, 0x69, 0x64, 0x52, 0x05, 0x61, 0x63, 0x6c, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x22, 0xd2, 0x01, 0x0a, 0x1b, 0x4a, 0x6f, 0x69, 0x6e, 0x4f,
	0x72, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0c, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x47, 0x0a, 0x0a, 0x46,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x19, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x12, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x42, 0x3b, 0x5a,
	0x39, 0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_securityGroup_proto_rawDescOnce sync.Once
	file_securityGroup_proto_rawDescData = file_securityGroup_proto_rawDesc
)

func file_securityGroup_proto_rawDescGZIP() []byte {
	file_securityGroup_proto_rawDescOnce.Do(func() {
		file_securityGroup_proto_rawDescData = protoimpl.X.CompressGZIP(file_securityGroup_proto_rawDescData)
	})
	return file_securityGroup_proto_rawDescData
}

var file_securityGroup_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_securityGroup_proto_goTypes = []interface{}{
	(*SecurityGroupEntity)(nil),                 // 0: cloudman.SecurityGroupEntity
	(*RelateInstanceCount)(nil),                 // 1: cloudman.relate_instance_count
	(*InstanceInfo)(nil),                        // 2: cloudman.instance_info
	(*SecurityGroupPermission)(nil),             // 3: cloudman.SecurityGroupPermission
	(*DescribeSecurityGroupReq)(nil),            // 4: cloudman.DescribeSecurityGroupReq
	(*DescribeSecurityGroupRes)(nil),            // 5: cloudman.DescribeSecurityGroupRes
	(*DescribeSecurityGroupByInstancesReq)(nil), // 6: cloudman.DescribeSecurityGroupByInstancesReq
	(*DescribeSecurityGroupByInstancesRes)(nil), // 7: cloudman.DescribeSecurityGroupByInstancesRes
	(*DescribeRulesReq)(nil),                    // 8: cloudman.DescribeRulesReq
	(*DescribeRulesRes)(nil),                    // 9: cloudman.DescribeRulesRes
	(*UpdateSecurityGroupRuleReq)(nil),          // 10: cloudman.UpdateSecurityGroupRuleReq
	(*RuleTag)(nil),                             // 11: cloudman.RuleTag
	(*UpdateSecurityGroupsReq)(nil),             // 12: cloudman.UpdateSecurityGroupsReq
	(*GetAccountRegionTagsReq)(nil),             // 13: cloudman.GetAccountRegionTagsReq
	(*GetAccountRegionTagsRes)(nil),             // 14: cloudman.GetAccountRegionTagsRes
	(*GetCustomTagSecurityGroupsReq)(nil),       // 15: cloudman.GetCustomTagSecurityGroupsReq
	(*GetCustomTagSecurityGroupsRes)(nil),       // 16: cloudman.GetCustomTagSecurityGroupsRes
	(*UpdateSecurityGroupCustomTagReq)(nil),     // 17: cloudman.UpdateSecurityGroupCustomTagReq
	(*ExportSecurityGroupReq)(nil),              // 18: cloudman.ExportSecurityGroupReq
	(*ExportSecurityGroupRuleReq)(nil),          // 19: cloudman.ExportSecurityGroupRuleReq
	(*ExportRelateInstancesReq)(nil),            // 20: cloudman.ExportRelateInstancesReq
	(*DescribeIPWhitelistsReq)(nil),             // 21: cloudman.DescribeIPWhitelistsReq
	(*DescribeIPWhitelistsResp)(nil),            // 22: cloudman.DescribeIPWhitelistsResp
	(*IPWhitelist)(nil),                         // 23: cloudman.IPWhitelist
	(*ModifyIPWhitelistsReq)(nil),               // 24: cloudman.ModifyIPWhitelistsReq
	(*DescribeALBAclsReq)(nil),                  // 25: cloudman.DescribeALBAclsReq
	(*DescribeALBAclsResp)(nil),                 // 26: cloudman.DescribeALBAclsResp
	(*ALBAclEntity)(nil),                        // 27: cloudman.ALBAclEntity
	(*AclEntry)(nil),                            // 28: cloudman.AclEntry
	(*RelatedListener)(nil),                     // 29: cloudman.RelatedListener
	(*BatchDeleteSecurityGroupRuleReq)(nil),     // 30: cloudman.BatchDeleteSecurityGroupRuleReq
	(*CreateSecurityGroupRuleReq)(nil),          // 31: cloudman.CreateSecurityGroupRuleReq
	(*CreateSecurityGroupRuleEntity)(nil),       // 32: cloudman.CreateSecurityGroupRuleEntity
	(*AddALBAclsEntriesReq)(nil),                // 33: cloudman.AddALBAclsEntriesReq
	(*AddALBAclsEntry)(nil),                     // 34: cloudman.AddALBAclsEntry
	(*RemoveALBAclsEntriesReq)(nil),             // 35: cloudman.RemoveALBAclsEntriesReq
	(*JoinOrLeaveSecurityGroupReq)(nil),         // 36: cloudman.JoinOrLeaveSecurityGroupReq
	(*FailedInfo)(nil),                          // 37: cloudman.FailedInfo
	(*ModifySecurityGroupResult)(nil),           // 38: cloudman.ModifySecurityGroupResult
	(*ResourceTag)(nil),                         // 39: cloudman.resource_tag
	(*InstanceIdNamePair)(nil),                  // 40: cloudman.InstanceIdNamePair
}
var file_securityGroup_proto_depIdxs = []int32{
	39, // 0: cloudman.SecurityGroupEntity.Tags:type_name -> cloudman.resource_tag
	3,  // 1: cloudman.SecurityGroupEntity.Permissions:type_name -> cloudman.SecurityGroupPermission
	2,  // 2: cloudman.SecurityGroupEntity.bind_instance_infos:type_name -> cloudman.instance_info
	1,  // 3: cloudman.SecurityGroupEntity.count:type_name -> cloudman.relate_instance_count
	11, // 4: cloudman.SecurityGroupEntity.level_tag:type_name -> cloudman.RuleTag
	11, // 5: cloudman.SecurityGroupPermission.tags:type_name -> cloudman.RuleTag
	0,  // 6: cloudman.DescribeSecurityGroupRes.list:type_name -> cloudman.SecurityGroupEntity
	0,  // 7: cloudman.DescribeSecurityGroupByInstancesRes.list:type_name -> cloudman.SecurityGroupEntity
	3,  // 8: cloudman.DescribeRulesRes.list:type_name -> cloudman.SecurityGroupPermission
	3,  // 9: cloudman.UpdateSecurityGroupRuleReq.permissions:type_name -> cloudman.SecurityGroupPermission
	11, // 10: cloudman.UpdateSecurityGroupRuleReq.level_tag:type_name -> cloudman.RuleTag
	0,  // 11: cloudman.UpdateSecurityGroupsReq.sgs:type_name -> cloudman.SecurityGroupEntity
	0,  // 12: cloudman.GetCustomTagSecurityGroupsRes.list:type_name -> cloudman.SecurityGroupEntity
	23, // 13: cloudman.DescribeIPWhitelistsResp.list:type_name -> cloudman.IPWhitelist
	27, // 14: cloudman.DescribeALBAclsResp.list:type_name -> cloudman.ALBAclEntity
	28, // 15: cloudman.ALBAclEntity.acl_entries:type_name -> cloudman.AclEntry
	29, // 16: cloudman.ALBAclEntity.related_listeners:type_name -> cloudman.RelatedListener
	32, // 17: cloudman.CreateSecurityGroupRuleReq.permissions:type_name -> cloudman.CreateSecurityGroupRuleEntity
	34, // 18: cloudman.AddALBAclsEntriesReq.entries:type_name -> cloudman.AddALBAclsEntry
	40, // 19: cloudman.JoinOrLeaveSecurityGroupReq.instance_info:type_name -> cloudman.InstanceIdNamePair
	37, // 20: cloudman.ModifySecurityGroupResult.failed_infos:type_name -> cloudman.FailedInfo
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_securityGroup_proto_init() }
func file_securityGroup_proto_init() {
	if File_securityGroup_proto != nil {
		return
	}
	file_resTemplate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_securityGroup_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityGroupEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelateInstanceCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InstanceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityGroupPermission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSecurityGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSecurityGroupRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSecurityGroupByInstancesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSecurityGroupByInstancesRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRulesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRulesRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSecurityGroupRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSecurityGroupsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountRegionTagsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountRegionTagsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomTagSecurityGroupsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomTagSecurityGroupsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSecurityGroupCustomTagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportSecurityGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportSecurityGroupRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportRelateInstancesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIPWhitelistsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIPWhitelistsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPWhitelist); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyIPWhitelistsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeALBAclsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeALBAclsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ALBAclEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AclEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelatedListener); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteSecurityGroupRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSecurityGroupRuleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSecurityGroupRuleEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddALBAclsEntriesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddALBAclsEntry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveALBAclsEntriesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JoinOrLeaveSecurityGroupReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FailedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_securityGroup_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifySecurityGroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_securityGroup_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_securityGroup_proto_goTypes,
		DependencyIndexes: file_securityGroup_proto_depIdxs,
		MessageInfos:      file_securityGroup_proto_msgTypes,
	}.Build()
	File_securityGroup_proto = out.File
	file_securityGroup_proto_rawDesc = nil
	file_securityGroup_proto_goTypes = nil
	file_securityGroup_proto_depIdxs = nil
}
