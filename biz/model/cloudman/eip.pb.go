// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: eip.proto

//import "google/protobuf/wrappers.proto";

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EipEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllocationId                  string                      `protobuf:"bytes,1,opt,name=allocation_id,json=allocationId,proto3" form:"allocation_id" json:"allocation_id" query:"allocation_id"`
	AllocationTime                string                      `protobuf:"bytes,2,opt,name=allocation_time,json=allocationTime,proto3" form:"allocation_time" json:"allocation_time" query:"allocation_time"`
	Bandwidth                     string                      `protobuf:"bytes,3,opt,name=bandwidth,proto3" form:"bandwidth" json:"bandwidth" query:"bandwidth"`
	BandwidthPackageBandwidth     string                      `protobuf:"bytes,4,opt,name=bandwidth_package_bandwidth,json=bandwidthPackageBandwidth,proto3" form:"bandwidth_package_bandwidth" json:"bandwidth_package_bandwidth" query:"bandwidth_package_bandwidth"`
	BandwidthPackageId            string                      `protobuf:"bytes,5,opt,name=bandwidth_package_id,json=bandwidthPackageId,proto3" form:"bandwidth_package_id" json:"bandwidth_package_id" query:"bandwidth_package_id"`
	BandwidthPackageType          string                      `protobuf:"bytes,6,opt,name=bandwidth_package_type,json=bandwidthPackageType,proto3" form:"bandwidth_package_type" json:"bandwidth_package_type" query:"bandwidth_package_type"`
	BizType                       string                      `protobuf:"bytes,7,opt,name=biz_type,json=bizType,proto3" form:"biz_type" json:"biz_type" query:"biz_type"`
	BusinessStatus                string                      `protobuf:"bytes,8,opt,name=business_status,json=businessStatus,proto3" form:"business_status" json:"business_status" query:"business_status"`
	ChargeType                    string                      `protobuf:"bytes,9,opt,name=charge_type,json=chargeType,proto3" form:"charge_type" json:"charge_type" query:"charge_type"`
	DeletionProtection            bool                        `protobuf:"varint,10,opt,name=deletion_protection,json=deletionProtection,proto3" form:"deletion_protection" json:"deletion_protection" query:"deletion_protection"`
	Description                   string                      `protobuf:"bytes,11,opt,name=description,proto3" form:"description" json:"description" query:"description"`
	EipBandwidth                  string                      `protobuf:"bytes,12,opt,name=eip_bandwidth,json=eipBandwidth,proto3" form:"eip_bandwidth" json:"eip_bandwidth" query:"eip_bandwidth"`
	ExpiredTime                   string                      `protobuf:"bytes,13,opt,name=expired_time,json=expiredTime,proto3" form:"expired_time" json:"expired_time" query:"expired_time"`
	HdMonitorStatus               string                      `protobuf:"bytes,14,opt,name=hd_monitor_status,json=hdMonitorStatus,proto3" form:"hd_monitor_status" json:"hd_monitor_status" query:"hd_monitor_status"`
	HasReservationData            string                      `protobuf:"bytes,15,opt,name=has_reservation_data,json=hasReservationData,proto3" form:"has_reservation_data" json:"has_reservation_data" query:"has_reservation_data"`
	Isp                           string                      `protobuf:"bytes,16,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	InstanceId                    string                      `protobuf:"bytes,17,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
	InstanceRegionId              string                      `protobuf:"bytes,18,opt,name=instance_region_id,json=instanceRegionId,proto3" form:"instance_region_id" json:"instance_region_id" query:"instance_region_id"`
	InstanceType                  string                      `protobuf:"bytes,19,opt,name=instance_type,json=instanceType,proto3" form:"instance_type" json:"instance_type" query:"instance_type"`
	InternetChargeType            string                      `protobuf:"bytes,20,opt,name=internet_charge_type,json=internetChargeType,proto3" form:"internet_charge_type" json:"internet_charge_type" query:"internet_charge_type"`
	IpAddress                     string                      `protobuf:"bytes,21,opt,name=ip_address,json=ipAddress,proto3" form:"ip_address" json:"ip_address" query:"ip_address"`
	Name                          string                      `protobuf:"bytes,22,opt,name=name,proto3" form:"name" json:"name" query:"name"`
	Netmode                       string                      `protobuf:"bytes,23,opt,name=netmode,proto3" form:"netmode" json:"netmode" query:"netmode"`
	OperationLocks                []*EipAddressOperationLocks `protobuf:"bytes,24,rep,name=operation_locks,json=operationLocks,proto3" form:"operation_locks" json:"operation_locks" query:"operation_locks"`
	PublicIpAddressPoolId         string                      `protobuf:"bytes,25,opt,name=public_ip_address_pool_id,json=publicIpAddressPoolId,proto3" form:"public_ip_address_pool_id" json:"public_ip_address_pool_id" query:"public_ip_address_pool_id"`
	RegionId                      string                      `protobuf:"bytes,26,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	ReservationActiveTime         string                      `protobuf:"bytes,27,opt,name=reservation_active_time,json=reservationActiveTime,proto3" form:"reservation_active_time" json:"reservation_active_time" query:"reservation_active_time"`
	ReservationBandwidth          string                      `protobuf:"bytes,28,opt,name=reservation_bandwidth,json=reservationBandwidth,proto3" form:"reservation_bandwidth" json:"reservation_bandwidth" query:"reservation_bandwidth"`
	ReservationInternetChargeType string                      `protobuf:"bytes,29,opt,name=reservation_internet_charge_type,json=reservationInternetChargeType,proto3" form:"reservation_internet_charge_type" json:"reservation_internet_charge_type" query:"reservation_internet_charge_type"`
	ReservationOrderType          string                      `protobuf:"bytes,30,opt,name=reservation_order_type,json=reservationOrderType,proto3" form:"reservation_order_type" json:"reservation_order_type" query:"reservation_order_type"`
	ResourceGroupId               string                      `protobuf:"bytes,31,opt,name=resource_group_id,json=resourceGroupId,proto3" form:"resource_group_id" json:"resource_group_id" query:"resource_group_id"`
	SecondLimited                 bool                        `protobuf:"varint,32,opt,name=second_limited,json=secondLimited,proto3" form:"second_limited" json:"second_limited" query:"second_limited"`
	SecurityProtectionTypes       []string                    `protobuf:"bytes,33,rep,name=security_protection_types,json=securityProtectionTypes,proto3" form:"security_protection_types" json:"security_protection_types" query:"security_protection_types"`
	SegmentInstanceId             string                      `protobuf:"bytes,34,opt,name=segment_instance_id,json=segmentInstanceId,proto3" form:"segment_instance_id" json:"segment_instance_id" query:"segment_instance_id"`
	ServiceManaged                int32                       `protobuf:"varint,35,opt,name=service_managed,json=serviceManaged,proto3" form:"service_managed" json:"service_managed" query:"service_managed"`
	Status                        string                      `protobuf:"bytes,36,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Tags                          []*ResourceTag              `protobuf:"bytes,37,rep,name=tags,proto3" form:"tags" json:"tags" query:"tags"`
	VpcId                         string                      `protobuf:"bytes,38,opt,name=vpc_id,json=vpcId,proto3" form:"vpc_id" json:"vpc_id" query:"vpc_id"`
	Zone                          string                      `protobuf:"bytes,39,opt,name=zone,proto3" form:"zone" json:"zone" query:"zone"`
	IspId                         string                      `protobuf:"bytes,40,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	IspType                       string                      `protobuf:"bytes,41,opt,name=isp_type,json=ispType,proto3" form:"isp_type" json:"isp_type" query:"isp_type"`
	IspName                       string                      `protobuf:"bytes,42,opt,name=isp_name,json=ispName,proto3" form:"isp_name" json:"isp_name" query:"isp_name"`
}

func (x *EipEntity) Reset() {
	*x = EipEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EipEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EipEntity) ProtoMessage() {}

func (x *EipEntity) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EipEntity.ProtoReflect.Descriptor instead.
func (*EipEntity) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{0}
}

func (x *EipEntity) GetAllocationId() string {
	if x != nil {
		return x.AllocationId
	}
	return ""
}

func (x *EipEntity) GetAllocationTime() string {
	if x != nil {
		return x.AllocationTime
	}
	return ""
}

func (x *EipEntity) GetBandwidth() string {
	if x != nil {
		return x.Bandwidth
	}
	return ""
}

func (x *EipEntity) GetBandwidthPackageBandwidth() string {
	if x != nil {
		return x.BandwidthPackageBandwidth
	}
	return ""
}

func (x *EipEntity) GetBandwidthPackageId() string {
	if x != nil {
		return x.BandwidthPackageId
	}
	return ""
}

func (x *EipEntity) GetBandwidthPackageType() string {
	if x != nil {
		return x.BandwidthPackageType
	}
	return ""
}

func (x *EipEntity) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *EipEntity) GetBusinessStatus() string {
	if x != nil {
		return x.BusinessStatus
	}
	return ""
}

func (x *EipEntity) GetChargeType() string {
	if x != nil {
		return x.ChargeType
	}
	return ""
}

func (x *EipEntity) GetDeletionProtection() bool {
	if x != nil {
		return x.DeletionProtection
	}
	return false
}

func (x *EipEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EipEntity) GetEipBandwidth() string {
	if x != nil {
		return x.EipBandwidth
	}
	return ""
}

func (x *EipEntity) GetExpiredTime() string {
	if x != nil {
		return x.ExpiredTime
	}
	return ""
}

func (x *EipEntity) GetHdMonitorStatus() string {
	if x != nil {
		return x.HdMonitorStatus
	}
	return ""
}

func (x *EipEntity) GetHasReservationData() string {
	if x != nil {
		return x.HasReservationData
	}
	return ""
}

func (x *EipEntity) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *EipEntity) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *EipEntity) GetInstanceRegionId() string {
	if x != nil {
		return x.InstanceRegionId
	}
	return ""
}

func (x *EipEntity) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *EipEntity) GetInternetChargeType() string {
	if x != nil {
		return x.InternetChargeType
	}
	return ""
}

func (x *EipEntity) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *EipEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EipEntity) GetNetmode() string {
	if x != nil {
		return x.Netmode
	}
	return ""
}

func (x *EipEntity) GetOperationLocks() []*EipAddressOperationLocks {
	if x != nil {
		return x.OperationLocks
	}
	return nil
}

func (x *EipEntity) GetPublicIpAddressPoolId() string {
	if x != nil {
		return x.PublicIpAddressPoolId
	}
	return ""
}

func (x *EipEntity) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *EipEntity) GetReservationActiveTime() string {
	if x != nil {
		return x.ReservationActiveTime
	}
	return ""
}

func (x *EipEntity) GetReservationBandwidth() string {
	if x != nil {
		return x.ReservationBandwidth
	}
	return ""
}

func (x *EipEntity) GetReservationInternetChargeType() string {
	if x != nil {
		return x.ReservationInternetChargeType
	}
	return ""
}

func (x *EipEntity) GetReservationOrderType() string {
	if x != nil {
		return x.ReservationOrderType
	}
	return ""
}

func (x *EipEntity) GetResourceGroupId() string {
	if x != nil {
		return x.ResourceGroupId
	}
	return ""
}

func (x *EipEntity) GetSecondLimited() bool {
	if x != nil {
		return x.SecondLimited
	}
	return false
}

func (x *EipEntity) GetSecurityProtectionTypes() []string {
	if x != nil {
		return x.SecurityProtectionTypes
	}
	return nil
}

func (x *EipEntity) GetSegmentInstanceId() string {
	if x != nil {
		return x.SegmentInstanceId
	}
	return ""
}

func (x *EipEntity) GetServiceManaged() int32 {
	if x != nil {
		return x.ServiceManaged
	}
	return 0
}

func (x *EipEntity) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *EipEntity) GetTags() []*ResourceTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *EipEntity) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *EipEntity) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *EipEntity) GetIspId() string {
	if x != nil {
		return x.IspId
	}
	return ""
}

func (x *EipEntity) GetIspType() string {
	if x != nil {
		return x.IspType
	}
	return ""
}

func (x *EipEntity) GetIspName() string {
	if x != nil {
		return x.IspName
	}
	return ""
}

type EipAddressOperationLocks struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LockReason string `protobuf:"bytes,1,opt,name=lock_reason,json=lockReason,proto3" form:"lock_reason" json:"lock_reason" query:"lock_reason"`
}

func (x *EipAddressOperationLocks) Reset() {
	*x = EipAddressOperationLocks{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EipAddressOperationLocks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EipAddressOperationLocks) ProtoMessage() {}

func (x *EipAddressOperationLocks) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EipAddressOperationLocks.ProtoReflect.Descriptor instead.
func (*EipAddressOperationLocks) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{1}
}

func (x *EipAddressOperationLocks) GetLockReason() string {
	if x != nil {
		return x.LockReason
	}
	return ""
}

type DescribeEipReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size        uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp         string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId    string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
	SearchKey   string `protobuf:"bytes,5,opt,name=search_key,json=searchKey,proto3" form:"search_key" json:"search_key" query:"search_key"`
	SearchValue string `protobuf:"bytes,6,opt,name=search_value,json=searchValue,proto3" form:"search_value" json:"search_value" query:"search_value"`
	IpAddress   string `protobuf:"bytes,7,opt,name=ip_address,json=ipAddress,proto3" form:"ip_address" json:"ip_address" query:"ip_address"`
}

func (x *DescribeEipReq) Reset() {
	*x = DescribeEipReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEipReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEipReq) ProtoMessage() {}

func (x *DescribeEipReq) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEipReq.ProtoReflect.Descriptor instead.
func (*DescribeEipReq) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeEipReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeEipReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeEipReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeEipReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *DescribeEipReq) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *DescribeEipReq) GetSearchValue() string {
	if x != nil {
		return x.SearchValue
	}
	return ""
}

func (x *DescribeEipReq) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type DescribeEipRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32        `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*EipEntity `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeEipRes) Reset() {
	*x = DescribeEipRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEipRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEipRes) ProtoMessage() {}

func (x *DescribeEipRes) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEipRes.ProtoReflect.Descriptor instead.
func (*DescribeEipRes) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeEipRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeEipRes) GetList() []*EipEntity {
	if x != nil {
		return x.List
	}
	return nil
}

type DescribeEipByInstancesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page         uint64   `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size         uint64   `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	InstanceType string   `protobuf:"bytes,3,opt,name=instanceType,proto3" form:"instanceType" json:"instanceType" query:"instanceType"`
	InstanceIds  []string `protobuf:"bytes,4,rep,name=instance_ids,json=instanceIds,proto3" form:"instance_ids" json:"instance_ids" query:"instance_ids"`
}

func (x *DescribeEipByInstancesReq) Reset() {
	*x = DescribeEipByInstancesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEipByInstancesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEipByInstancesReq) ProtoMessage() {}

func (x *DescribeEipByInstancesReq) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEipByInstancesReq.ProtoReflect.Descriptor instead.
func (*DescribeEipByInstancesReq) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{4}
}

func (x *DescribeEipByInstancesReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeEipByInstancesReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeEipByInstancesReq) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *DescribeEipByInstancesReq) GetInstanceIds() []string {
	if x != nil {
		return x.InstanceIds
	}
	return nil
}

type DescribeEipSegmentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     uint64 `protobuf:"varint,1,opt,name=page,proto3" form:"page" json:"page" query:"page"`
	Size     uint64 `protobuf:"varint,2,opt,name=size,proto3" form:"size" json:"size" query:"size"`
	Isp      string `protobuf:"bytes,3,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId string `protobuf:"bytes,4,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *DescribeEipSegmentReq) Reset() {
	*x = DescribeEipSegmentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEipSegmentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEipSegmentReq) ProtoMessage() {}

func (x *DescribeEipSegmentReq) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEipSegmentReq.ProtoReflect.Descriptor instead.
func (*DescribeEipSegmentReq) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeEipSegmentReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DescribeEipSegmentReq) GetSize() uint64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DescribeEipSegmentReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeEipSegmentReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type DescribeEipSegmentRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32         `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*EipSegment `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeEipSegmentRes) Reset() {
	*x = DescribeEipSegmentRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeEipSegmentRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeEipSegmentRes) ProtoMessage() {}

func (x *DescribeEipSegmentRes) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeEipSegmentRes.ProtoReflect.Descriptor instead.
func (*DescribeEipSegmentRes) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{6}
}

func (x *DescribeEipSegmentRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeEipSegmentRes) GetList() []*EipSegment {
	if x != nil {
		return x.List
	}
	return nil
}

type EipSegment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     string `protobuf:"bytes,1,opt,name=status,proto3" form:"status" json:"status" query:"status"`
	Segment    string `protobuf:"bytes,2,opt,name=segment,proto3" form:"segment" json:"segment" query:"segment"`
	IpCount    string `protobuf:"bytes,3,opt,name=ip_count,json=ipCount,proto3" form:"ip_count" json:"ip_count" query:"ip_count"`
	InstanceId string `protobuf:"bytes,4,opt,name=instance_id,json=instanceId,proto3" form:"instance_id" json:"instance_id" query:"instance_id"`
}

func (x *EipSegment) Reset() {
	*x = EipSegment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EipSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EipSegment) ProtoMessage() {}

func (x *EipSegment) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EipSegment.ProtoReflect.Descriptor instead.
func (*EipSegment) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{7}
}

func (x *EipSegment) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *EipSegment) GetSegment() string {
	if x != nil {
		return x.Segment
	}
	return ""
}

func (x *EipSegment) GetIpCount() string {
	if x != nil {
		return x.IpCount
	}
	return ""
}

func (x *EipSegment) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

type DescribeIpamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Isp      string `protobuf:"bytes,1,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`
	RegionId string `protobuf:"bytes,2,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"`
}

func (x *DescribeIpamReq) Reset() {
	*x = DescribeIpamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIpamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIpamReq) ProtoMessage() {}

func (x *DescribeIpamReq) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIpamReq.ProtoReflect.Descriptor instead.
func (*DescribeIpamReq) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{8}
}

func (x *DescribeIpamReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *DescribeIpamReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

type DescribeIpamRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32   `protobuf:"varint,1,opt,name=total,proto3" form:"total" json:"total" query:"total"`
	List  []*Ipam `protobuf:"bytes,2,rep,name=list,proto3" form:"list" json:"list" query:"list"`
}

func (x *DescribeIpamRes) Reset() {
	*x = DescribeIpamRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeIpamRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeIpamRes) ProtoMessage() {}

func (x *DescribeIpamRes) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeIpamRes.ProtoReflect.Descriptor instead.
func (*DescribeIpamRes) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{9}
}

func (x *DescribeIpamRes) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DescribeIpamRes) GetList() []*Ipam {
	if x != nil {
		return x.List
	}
	return nil
}

type Ipam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string   `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	State           string   `protobuf:"bytes,2,opt,name=state,proto3" form:"state" json:"state" query:"state"`
	Cidrs           []string `protobuf:"bytes,3,rep,name=cidrs,proto3" form:"cidrs" json:"cidrs" query:"cidrs"`
	TotalCount      int32    `protobuf:"varint,4,opt,name=total_count,json=totalCount,proto3" form:"total_count" json:"total_count" query:"total_count"`
	AssignedPercent float32  `protobuf:"fixed32,5,opt,name=assigned_percent,json=assignedPercent,proto3" form:"assigned_percent" json:"assigned_percent" query:"assigned_percent"`
}

func (x *Ipam) Reset() {
	*x = Ipam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_eip_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ipam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ipam) ProtoMessage() {}

func (x *Ipam) ProtoReflect() protoreflect.Message {
	mi := &file_eip_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ipam.ProtoReflect.Descriptor instead.
func (*Ipam) Descriptor() ([]byte, []int) {
	return file_eip_proto_rawDescGZIP(), []int{10}
}

func (x *Ipam) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Ipam) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *Ipam) GetCidrs() []string {
	if x != nil {
		return x.Cidrs
	}
	return nil
}

func (x *Ipam) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *Ipam) GetAssignedPercent() float32 {
	if x != nil {
		return x.AssignedPercent
	}
	return 0
}

var File_eip_proto protoreflect.FileDescriptor

var file_eip_proto_rawDesc = []byte{
	0x0a, 0x09, 0x65, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x1a, 0x11, 0x72, 0x65, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x0d, 0x0a, 0x09, 0x45, 0x69, 0x70,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61,
	0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x3e, 0x0a, 0x1b, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x12, 0x30, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74,
	0x68, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69,
	0x7a, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x69,
	0x7a, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2f, 0x0a, 0x13, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x69, 0x70, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x69, 0x70, 0x42, 0x61,
	0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x68, 0x64,
	0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x68, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x65,
	0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x68, 0x61, 0x73, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30,
	0x0a, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x4b, 0x0a,
	0x0f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x73,
	0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61,
	0x6e, 0x2e, 0x45, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x73, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x38, 0x0a, 0x19, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x6f,
	0x6f, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x15, 0x72, 0x65, 0x73,
	0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64,
	0x74, 0x68, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x61, 0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x47,
	0x0a, 0x20, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x72, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64,
	0x12, 0x3a, 0x0a, 0x19, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x21, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x17, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x25, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x76, 0x70, 0x63,
	0x5f, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x70, 0x63, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x28,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x73, 0x70, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x73, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69,
	0x73, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x73, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x3b, 0x0a, 0x18, 0x45, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xc8,
	0x01, 0x0a, 0x0e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x70,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x4f, 0x0a, 0x0e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x45, 0x69, 0x70, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8a, 0x01, 0x0a, 0x19, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x42, 0x79, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x6e, 0x0a, 0x15, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x57, 0x0a, 0x15, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x45, 0x69, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e,
	0x45, 0x69, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x7a, 0x0a, 0x0a, 0x45, 0x69, 0x70, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x69, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x0f,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x70, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73,
	0x70, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x4b,
	0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49, 0x70, 0x61, 0x6d, 0x52, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x22, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x49, 0x70, 0x61, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x04,
	0x49, 0x70, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x69,
	0x64, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x63, 0x69, 0x64, 0x72, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x29, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x61, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x42, 0x3b, 0x5a, 0x39,
	0x70, 0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_eip_proto_rawDescOnce sync.Once
	file_eip_proto_rawDescData = file_eip_proto_rawDesc
)

func file_eip_proto_rawDescGZIP() []byte {
	file_eip_proto_rawDescOnce.Do(func() {
		file_eip_proto_rawDescData = protoimpl.X.CompressGZIP(file_eip_proto_rawDescData)
	})
	return file_eip_proto_rawDescData
}

var file_eip_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_eip_proto_goTypes = []interface{}{
	(*EipEntity)(nil),                 // 0: cloudman.EipEntity
	(*EipAddressOperationLocks)(nil),  // 1: cloudman.EipAddressOperationLocks
	(*DescribeEipReq)(nil),            // 2: cloudman.DescribeEipReq
	(*DescribeEipRes)(nil),            // 3: cloudman.DescribeEipRes
	(*DescribeEipByInstancesReq)(nil), // 4: cloudman.DescribeEipByInstancesReq
	(*DescribeEipSegmentReq)(nil),     // 5: cloudman.DescribeEipSegmentReq
	(*DescribeEipSegmentRes)(nil),     // 6: cloudman.DescribeEipSegmentRes
	(*EipSegment)(nil),                // 7: cloudman.EipSegment
	(*DescribeIpamReq)(nil),           // 8: cloudman.DescribeIpamReq
	(*DescribeIpamRes)(nil),           // 9: cloudman.DescribeIpamRes
	(*Ipam)(nil),                      // 10: cloudman.Ipam
	(*ResourceTag)(nil),               // 11: cloudman.resource_tag
}
var file_eip_proto_depIdxs = []int32{
	1,  // 0: cloudman.EipEntity.operation_locks:type_name -> cloudman.EipAddressOperationLocks
	11, // 1: cloudman.EipEntity.tags:type_name -> cloudman.resource_tag
	0,  // 2: cloudman.DescribeEipRes.list:type_name -> cloudman.EipEntity
	7,  // 3: cloudman.DescribeEipSegmentRes.list:type_name -> cloudman.EipSegment
	10, // 4: cloudman.DescribeIpamRes.list:type_name -> cloudman.Ipam
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_eip_proto_init() }
func file_eip_proto_init() {
	if File_eip_proto != nil {
		return
	}
	file_resTemplate_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_eip_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EipEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EipAddressOperationLocks); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEipReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEipRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEipByInstancesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEipSegmentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeEipSegmentRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EipSegment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIpamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeIpamRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_eip_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Ipam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_eip_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_eip_proto_goTypes,
		DependencyIndexes: file_eip_proto_depIdxs,
		MessageInfos:      file_eip_proto_msgTypes,
	}.Build()
	File_eip_proto = out.File
	file_eip_proto_rawDesc = nil
	file_eip_proto_goTypes = nil
	file_eip_proto_depIdxs = nil
}
