// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.21.12
// source: initResource.proto

package cloudman

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetJumpserverReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId string `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"` // 区域数据库id
	Isp      string `protobuf:"bytes,2,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`                                       // 云商数据库id
}

func (x *GetJumpserverReq) Reset() {
	*x = GetJumpserverReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJumpserverReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJumpserverReq) ProtoMessage() {}

func (x *GetJumpserverReq) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJumpserverReq.ProtoReflect.Descriptor instead.
func (*GetJumpserverReq) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{0}
}

func (x *GetJumpserverReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *GetJumpserverReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

type GetJumpserverResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hosts []string `protobuf:"bytes,1,rep,name=hosts,proto3" form:"hosts" json:"hosts" query:"hosts"` // 堡垒机主机名列表
}

func (x *GetJumpserverResp) Reset() {
	*x = GetJumpserverResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJumpserverResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJumpserverResp) ProtoMessage() {}

func (x *GetJumpserverResp) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJumpserverResp.ProtoReflect.Descriptor instead.
func (*GetJumpserverResp) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{1}
}

func (x *GetJumpserverResp) GetHosts() []string {
	if x != nil {
		return x.Hosts
	}
	return nil
}

type GetPipelineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegionId string `protobuf:"bytes,1,opt,name=region_id,json=regionId,proto3" form:"region_id" json:"region_id" query:"region_id"` // 区域数据库id
	Isp      string `protobuf:"bytes,2,opt,name=isp,proto3" form:"isp" json:"isp" query:"isp"`                                       // 云商数据库id
}

func (x *GetPipelineReq) Reset() {
	*x = GetPipelineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineReq) ProtoMessage() {}

func (x *GetPipelineReq) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineReq.ProtoReflect.Descriptor instead.
func (*GetPipelineReq) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{2}
}

func (x *GetPipelineReq) GetRegionId() string {
	if x != nil {
		return x.RegionId
	}
	return ""
}

func (x *GetPipelineReq) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

type GetPipelineResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pipelines []*PipelineData `protobuf:"bytes,1,rep,name=pipelines,proto3" form:"pipelines" json:"pipelines" query:"pipelines"` // 流水线列表
}

func (x *GetPipelineResp) Reset() {
	*x = GetPipelineResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPipelineResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPipelineResp) ProtoMessage() {}

func (x *GetPipelineResp) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPipelineResp.ProtoReflect.Descriptor instead.
func (*GetPipelineResp) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{3}
}

func (x *GetPipelineResp) GetPipelines() []*PipelineData {
	if x != nil {
		return x.Pipelines
	}
	return nil
}

type PipelineData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" form:"id" json:"id" query:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" form:"name" json:"name" query:"name"`
}

func (x *PipelineData) Reset() {
	*x = PipelineData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineData) ProtoMessage() {}

func (x *PipelineData) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineData.ProtoReflect.Descriptor instead.
func (*PipelineData) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{4}
}

func (x *PipelineData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PipelineData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type PipelineParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key              string `protobuf:"bytes,1,opt,name=key,proto3" form:"key" json:"key" query:"key"`
	Value            string `protobuf:"bytes,2,opt,name=value,proto3" form:"value" json:"value" query:"value"`
	EnableGzipBase64 bool   `protobuf:"varint,3,opt,name=enable_gzip_base64,json=enableGzipBase64,proto3" form:"enable_gzip_base64" json:"enable_gzip_base64" query:"enable_gzip_base64"`
}

func (x *PipelineParam) Reset() {
	*x = PipelineParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineParam) ProtoMessage() {}

func (x *PipelineParam) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineParam.ProtoReflect.Descriptor instead.
func (*PipelineParam) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{5}
}

func (x *PipelineParam) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *PipelineParam) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *PipelineParam) GetEnableGzipBase64() bool {
	if x != nil {
		return x.EnableGzipBase64
	}
	return false
}

type PreviewParamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Params        []*PipelineParam `protobuf:"bytes,1,rep,name=params,proto3" form:"params" json:"params" query:"params"`
	BuiltinParams []string         `protobuf:"bytes,2,rep,name=builtin_params,json=builtinParams,proto3" form:"builtin_params" json:"builtin_params" query:"builtin_params"`
	FormJson      string           `protobuf:"bytes,3,opt,name=form_json,json=formJson,proto3" form:"form_json" json:"form_json" query:"form_json"`
}

func (x *PreviewParamReq) Reset() {
	*x = PreviewParamReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewParamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewParamReq) ProtoMessage() {}

func (x *PreviewParamReq) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewParamReq.ProtoReflect.Descriptor instead.
func (*PreviewParamReq) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{6}
}

func (x *PreviewParamReq) GetParams() []*PipelineParam {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *PreviewParamReq) GetBuiltinParams() []string {
	if x != nil {
		return x.BuiltinParams
	}
	return nil
}

func (x *PreviewParamReq) GetFormJson() string {
	if x != nil {
		return x.FormJson
	}
	return ""
}

type PipelineParamPreviewData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key           string `protobuf:"bytes,1,opt,name=key,proto3" form:"key" json:"key" query:"key"`
	Value         string `protobuf:"bytes,2,opt,name=value,proto3" form:"value" json:"value" query:"value"`
	RenderedValue string `protobuf:"bytes,3,opt,name=rendered_value,json=renderedValue,proto3" form:"rendered_value" json:"rendered_value" query:"rendered_value"`
}

func (x *PipelineParamPreviewData) Reset() {
	*x = PipelineParamPreviewData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PipelineParamPreviewData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineParamPreviewData) ProtoMessage() {}

func (x *PipelineParamPreviewData) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineParamPreviewData.ProtoReflect.Descriptor instead.
func (*PipelineParamPreviewData) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{7}
}

func (x *PipelineParamPreviewData) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *PipelineParamPreviewData) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *PipelineParamPreviewData) GetRenderedValue() string {
	if x != nil {
		return x.RenderedValue
	}
	return ""
}

type PreviewParamResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreviewParams []*PipelineParamPreviewData `protobuf:"bytes,1,rep,name=preview_params,json=previewParams,proto3" form:"preview_params" json:"preview_params" query:"preview_params"`
}

func (x *PreviewParamResp) Reset() {
	*x = PreviewParamResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewParamResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewParamResp) ProtoMessage() {}

func (x *PreviewParamResp) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewParamResp.ProtoReflect.Descriptor instead.
func (*PreviewParamResp) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{8}
}

func (x *PreviewParamResp) GetPreviewParams() []*PipelineParamPreviewData {
	if x != nil {
		return x.PreviewParams
	}
	return nil
}

type InitOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lock           bool             `protobuf:"varint,1,opt,name=lock,proto3" form:"lock" json:"lock" query:"lock"`
	EnableInit     bool             `protobuf:"varint,2,opt,name=enable_init,json=enableInit,proto3" form:"enable_init" json:"enable_init" query:"enable_init"`
	JumpserverHost string           `protobuf:"bytes,3,opt,name=jumpserver_host,json=jumpserverHost,proto3" form:"jumpserver_host" json:"jumpserver_host" query:"jumpserver_host"`
	PipelineId     string           `protobuf:"bytes,4,opt,name=pipeline_id,json=pipelineId,proto3" form:"pipeline_id" json:"pipeline_id" query:"pipeline_id"`
	PipelineParams []*PipelineParam `protobuf:"bytes,5,rep,name=pipeline_params,json=pipelineParams,proto3" form:"pipeline_params" json:"pipeline_params" query:"pipeline_params"`
	BuiltinParams  []string         `protobuf:"bytes,6,rep,name=builtin_params,json=builtinParams,proto3" form:"builtin_params" json:"builtin_params" query:"builtin_params"`
}

func (x *InitOption) Reset() {
	*x = InitOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_initResource_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitOption) ProtoMessage() {}

func (x *InitOption) ProtoReflect() protoreflect.Message {
	mi := &file_initResource_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitOption.ProtoReflect.Descriptor instead.
func (*InitOption) Descriptor() ([]byte, []int) {
	return file_initResource_proto_rawDescGZIP(), []int{9}
}

func (x *InitOption) GetLock() bool {
	if x != nil {
		return x.Lock
	}
	return false
}

func (x *InitOption) GetEnableInit() bool {
	if x != nil {
		return x.EnableInit
	}
	return false
}

func (x *InitOption) GetJumpserverHost() string {
	if x != nil {
		return x.JumpserverHost
	}
	return ""
}

func (x *InitOption) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *InitOption) GetPipelineParams() []*PipelineParam {
	if x != nil {
		return x.PipelineParams
	}
	return nil
}

func (x *InitOption) GetBuiltinParams() []string {
	if x != nil {
		return x.BuiltinParams
	}
	return nil
}

var File_initResource_proto protoreflect.FileDescriptor

var file_initResource_proto_rawDesc = []byte{
	0x0a, 0x12, 0x69, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x22, 0x41,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x4a, 0x75, 0x6d, 0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x73, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73,
	0x70, 0x22, 0x29, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4a, 0x75, 0x6d, 0x70, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x22, 0x3f, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69,
	0x73, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x73, 0x70, 0x22, 0x47, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x34, 0x0a, 0x09, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x70, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x32, 0x0a, 0x0c, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69,
	0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x65, 0x0a, 0x0d, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x67, 0x7a,
	0x69, 0x70, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x47, 0x7a, 0x69, 0x70, 0x42, 0x61, 0x73, 0x65, 0x36,
	0x34, 0x22, 0x86, 0x01, 0x0a, 0x0f, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e,
	0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x06,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x69, 0x6c, 0x74, 0x69,
	0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x62, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x69, 0x0a, 0x18, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x65, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x5d, 0x0a, 0x10, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x0e, 0x70, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x50, 0x69, 0x70,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x22, 0xf4, 0x01, 0x0a, 0x0a, 0x49, 0x6e, 0x69, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x04, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x69, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6a, 0x75, 0x6d, 0x70,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6a, 0x75, 0x6d, 0x70, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x48, 0x6f, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x40, 0x0a, 0x0f, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2e, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x52, 0x0e, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x69, 0x6c, 0x74, 0x69, 0x6e, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x75,
	0x69, 0x6c, 0x74, 0x69, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x3b, 0x5a, 0x39, 0x70,
	0x6c, 0x61, 0x74, 0x67, 0x69, 0x74, 0x2e, 0x6d, 0x69, 0x68, 0x6f, 0x79, 0x6f, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x6a, 0x71, 0x6c, 0x2d, 0x6f, 0x70, 0x73, 0x2f, 0x6f, 0x70, 0x2d, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x6d, 0x61, 0x6e, 0x2f, 0x62, 0x69, 0x7a, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x6d, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_initResource_proto_rawDescOnce sync.Once
	file_initResource_proto_rawDescData = file_initResource_proto_rawDesc
)

func file_initResource_proto_rawDescGZIP() []byte {
	file_initResource_proto_rawDescOnce.Do(func() {
		file_initResource_proto_rawDescData = protoimpl.X.CompressGZIP(file_initResource_proto_rawDescData)
	})
	return file_initResource_proto_rawDescData
}

var file_initResource_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_initResource_proto_goTypes = []interface{}{
	(*GetJumpserverReq)(nil),         // 0: cloudman.GetJumpserverReq
	(*GetJumpserverResp)(nil),        // 1: cloudman.GetJumpserverResp
	(*GetPipelineReq)(nil),           // 2: cloudman.GetPipelineReq
	(*GetPipelineResp)(nil),          // 3: cloudman.GetPipelineResp
	(*PipelineData)(nil),             // 4: cloudman.PipelineData
	(*PipelineParam)(nil),            // 5: cloudman.PipelineParam
	(*PreviewParamReq)(nil),          // 6: cloudman.PreviewParamReq
	(*PipelineParamPreviewData)(nil), // 7: cloudman.PipelineParamPreviewData
	(*PreviewParamResp)(nil),         // 8: cloudman.PreviewParamResp
	(*InitOption)(nil),               // 9: cloudman.InitOption
}
var file_initResource_proto_depIdxs = []int32{
	4, // 0: cloudman.GetPipelineResp.pipelines:type_name -> cloudman.PipelineData
	5, // 1: cloudman.PreviewParamReq.params:type_name -> cloudman.PipelineParam
	7, // 2: cloudman.PreviewParamResp.preview_params:type_name -> cloudman.PipelineParamPreviewData
	5, // 3: cloudman.InitOption.pipeline_params:type_name -> cloudman.PipelineParam
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_initResource_proto_init() }
func file_initResource_proto_init() {
	if File_initResource_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_initResource_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJumpserverReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJumpserverResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPipelineResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewParamReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PipelineParamPreviewData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewParamResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_initResource_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_initResource_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_initResource_proto_goTypes,
		DependencyIndexes: file_initResource_proto_depIdxs,
		MessageInfos:      file_initResource_proto_msgTypes,
	}.Build()
	File_initResource_proto = out.File
	file_initResource_proto_rawDesc = nil
	file_initResource_proto_goTypes = nil
	file_initResource_proto_depIdxs = nil
}
