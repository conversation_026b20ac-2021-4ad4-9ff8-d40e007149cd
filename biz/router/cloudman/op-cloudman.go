// Code generated by hertz generator. DO NOT EDIT.

package cloudman

import (
	"github.com/cloudwego/hertz/pkg/app/server"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/handler/cloudman"
)

/*
 This file will register all the routes of the services in the master idl.
 And it will update automatically when you use the "update" command for the idl.
 So don't modify the contents of the file, or your code will be deleted when it is updated.
*/

// Register register routes based on the IDL 'api.${HTTP Method}' annotation.
func Register(r *server.Hertz) {

	root := r.Group("/", rootMw()...)
	{
		_api := root.Group("/api", _apiMw()...)
		{
			_v1 := _api.Group("/v1", _v1Mw()...)
			_v1.GET("/account", append(_accountlistMw(), cloudman.AccountList)...)
			_account := _v1.Group("/account", _accountMw()...)
			_account.DELETE("/:id", append(_accountdestroyMw(), cloudman.AccountDestroy)...)
			_id := _account.Group("/:id", _idMw()...)
			_id.GET("/bind-region", append(_accountgetbindregionMw(), cloudman.AccountGetBindRegion)...)
			_id.POST("/ping", append(_accountpingMw(), cloudman.AccountPing)...)
			_id.POST("/sync", append(_accountsyncMw(), cloudman.AccountSync)...)
			_account.GET("/:id", append(_accountretrieveMw(), cloudman.AccountRetrieve)...)
			_account.PUT("/:id", append(_accountupdateMw(), cloudman.AccountUpdate)...)
			{
				_id0 := _account.Group("/:id", _id0Mw()...)
				_id0.PUT("/status", append(_accountupdatestatusMw(), cloudman.AccountUpdateStatus)...)
			}
			_v1.POST("/account", append(_accountcreateMw(), cloudman.AccountCreate)...)
			_v1.POST("/account-ping", append(_accounttestrawpingMw(), cloudman.AccountTestRawPing)...)
			_v1.POST("/inner", append(_innerruntaskapiMw(), cloudman.InnerRunTaskAPI)...)
			_v1.GET("/log", append(_taskloglisttasklogMw(), cloudman.TaskLogListTaskLog)...)
			_log := _v1.Group("/log", _logMw()...)
			_log.GET("/:id", append(_tasklogretrievetasklogMw(), cloudman.TaskLogRetrieveTaskLog)...)
			_v1.POST("/refresh-region", append(_accountgetaccountregionMw(), cloudman.AccountGetAccountRegion)...)
			_v1.GET("/region", append(_regionlistMw(), cloudman.RegionList)...)
			_region := _v1.Group("/region", _regionMw()...)
			_region.GET("/:id", append(_regioninfoMw(), cloudman.RegionInfo)...)
			_v1.POST("/region", append(_regioncreateMw(), cloudman.RegionCreate)...)
			_v1.GET("/task", append(_tasklistMw(), cloudman.TaskList)...)
			_task := _v1.Group("/task", _taskMw()...)
			_task.DELETE("/:id", append(_taskdeleteMw(), cloudman.TaskDelete)...)
			_task.GET("/:id", append(_taskretrieveMw(), cloudman.TaskRetrieve)...)
			_id1 := _task.Group("/:id", _id1Mw()...)
			_id1.POST("/run", append(_taskruntaskapiMw(), cloudman.TaskRunTaskAPI)...)
			_task.PUT("/:id", append(_taskupdateMw(), cloudman.TaskUpdate)...)
			_v1.POST("/task", append(_taskcreateMw(), cloudman.TaskCreate)...)
			{
				_all := _v1.Group("/all", _allMw()...)
				_all.GET("/account", append(_accountlistallMw(), cloudman.AccountListAll)...)
			}
			{
				_cloud_agent := _v1.Group("/cloud_agent", _cloud_agentMw()...)
				{
					_task0 := _cloud_agent.Group("/task", _task0Mw()...)
					_task0.POST("/list", append(_cloudagenttasklistMw(), cloudman.CloudAgentTaskList)...)
					{
						_id2 := _task0.Group("/:id", _id2Mw()...)
						_id2.GET("/detail", append(_cloudagenttaskdetailMw(), cloudman.CloudAgentTaskDetail)...)
					}
				}
			}
			{
				_dashboard := _v1.Group("/dashboard", _dashboardMw()...)
				_dashboard.GET("/account", append(_dashboardgetaccountMw(), cloudman.DashBoardGetAccount)...)
				_dashboard.POST("/auto_fill_snapshot", append(_dashboardautofillsnapshotMw(), cloudman.DashBoardAutoFillSnapshot)...)
				_dashboard.GET("/backendVersion", append(_dashboardgetbackendversionMw(), cloudman.DashBoardGetBackendVersion)...)
				_dashboard.POST("/fill_snapshot", append(_dashboardfillsnapshotMw(), cloudman.DashBoardFillSnapshot)...)
				_dashboard.POST("/host_brief", append(_dashboardgethostbriefMw(), cloudman.DashBoardGetHostBrief)...)
				_dashboard.POST("/host_daily_chart", append(_dashboardgethostdailychartMw(), cloudman.DashBoardGetHostDailyChart)...)
				_dashboard.GET("/ifworkorderused", append(_dashboardgetifworkorderusedMw(), cloudman.DashBoardGetIfWorkOrderUsed)...)
				_dashboard.POST("/res_brief", append(_dashboardgetresbriefMw(), cloudman.DashBoardGetResBrief)...)
				_dashboard.POST("/res_daily_chart", append(_dashboardgetresdailychartMw(), cloudman.DashBoardGetResDailyChart)...)
				_dashboard.GET("/userauth", append(_dashboardgetuserauthMw(), cloudman.DashBoardGetUserAuth)...)
			}
			{
				_debug := _v1.Group("/debug", _debugMw()...)
				_debug.GET("/fix-tag", append(_debugfixrestagbindMw(), cloudman.DebugFixResTagBind)...)
				_debug.GET("/test", append(_debugtestMw(), cloudman.DebugTest)...)
			}
			{
				_perm := _v1.Group("/perm", _permMw()...)
				_perm.POST("/instance", append(_permissioncheckinstancepermissionMw(), cloudman.PermissionCheckInstancePermission)...)
			}
			{
				_res_changelist := _v1.Group("/res_changelist", _res_changelistMw()...)
				_res_changelist.POST("/query", append(_cloudagentreschangelistMw(), cloudman.CloudAgentResChangelist)...)
			}
			{
				_servicetree := _v1.Group("/servicetree", _servicetreeMw()...)
				_servicetree.POST("/add_node", append(_servicetreeaddnodeMw(), cloudman.ServiceTreeAddNode)...)
				_servicetree.POST("/get_entity", append(_servicetreegetentityMw(), cloudman.ServiceTreeGetEntity)...)
				_servicetree.POST("/get_tree", append(_servicetreegettreeMw(), cloudman.ServiceTreeGetTree)...)
				_servicetree.POST("/verify", append(_servicetreeverifyMw(), cloudman.ServiceTreeVerify)...)
				{
					_node := _servicetree.Group("/node", _nodeMw()...)
					_node.POST("/entity_count", append(_servicetreenodeentitycountMw(), cloudman.ServiceTreeNodeEntityCount)...)
				}
			}
		}
		{
			_v2 := _api.Group("/v2", _v2Mw()...)
			_v2.GET("/config", append(_systemgetconfigMw(), cloudman.SystemGetConfig)...)
			_v2.GET("/gateway", append(_cloudgatewaylistgatewayMw(), cloudman.CloudGatewayListGateway)...)
			_gateway := _v2.Group("/gateway", _gatewayMw()...)
			_gateway.GET("/agent", append(_cloudgatewaylistagentMw(), cloudman.CloudGatewayListAgent)...)
			_agent := _gateway.Group("/agent", _agentMw()...)
			_agent.POST("/export", append(_cloudgatewayexportxlsxMw(), cloudman.CloudGatewayExportXlsx)...)
			_agent.GET("/info", append(_cloudgatewayrawagentinfoMw(), cloudman.CloudGatewayRawAgentInfo)...)
			_gateway.POST("/agent", append(_cloudgatewaychangeagentMw(), cloudman.CloudGatewayChangeAgent)...)
			_agent0 := _gateway.Group("/agent", _agent0Mw()...)
			_agent0.POST("/delete", append(_cloudgatewaydeleteagentMw(), cloudman.CloudGatewayDeleteAgent)...)
			_agent0.POST("/status", append(_cloudgatewaychangeagentstatusMw(), cloudman.CloudGatewayChangeAgentStatus)...)
			_gateway.POST("/agents", append(_cloudgatewaychangeagentsMw(), cloudman.CloudGatewayChangeAgents)...)
			_gateway.GET("/config", append(_cloudgatewaygetgateaddressMw(), cloudman.CloudGatewayGetGateAddress)...)
			_gateway.POST("/config", append(_cloudgatewayinsertgateaddressMw(), cloudman.CloudGatewayInsertGateAddress)...)
			_gateway.GET("/install_config", append(_cloudgatewaylistagentinstallconfigMw(), cloudman.CloudGatewayListAgentInstallConfig)...)
			_gateway.POST("/install_config", append(_cloudgatewayupdateagentinstallconfigMw(), cloudman.CloudGatewayUpdateAgentInstallConfig)...)
			_gateway.GET("/list_version", append(_cloudgatewaylistagentversionMw(), cloudman.CloudGatewayListAgentVersion)...)
			_gateway.POST("/reinstall_agent", append(_cloudgatewayreinstallagentMw(), cloudman.CloudGatewayReinstallAgent)...)
			_gateway.POST("/update_agent", append(_cloudgatewayupdateagentMw(), cloudman.CloudGatewayUpdateAgent)...)
			{
				_agent1 := _gateway.Group("/agent", _agent1Mw()...)
				_agent1.POST("/script", append(_cloudgatewaygetagentscriptMw(), cloudman.CloudGatewayGetAgentScript)...)
			}
			_v2.POST("/gateway", append(_cloudgatewaycreategatewayMw(), cloudman.CloudGatewayCreateGateway)...)
			_gateway0 := _v2.Group("/gateway", _gateway0Mw()...)
			_gateway0.POST("/script", append(_cloudgatewaygetgatewayscriptMw(), cloudman.CloudGatewayGetGatewayScript)...)
			_gateway0.POST("/topo", append(_cloudgatewaygetgatewaytopoMw(), cloudman.CloudGatewayGetGatewayTopo)...)
			_v2.GET("/gateway-topo", append(_cloudgatewaygettopoMw(), cloudman.CloudGatewayGettopo)...)
			_v2.GET("/securitygroup_rule_daily_notify", append(_systemsecuritygroupruledailynotifyMw(), cloudman.SystemSecurityGroupRuleDailyNotify)...)
			_v2.GET("/wecom_daily_notify", append(_systemwecomdailynotifyMw(), cloudman.SystemWecomDailyNotify)...)
			{
				_account0 := _v2.Group("/account", _account0Mw()...)
				{
					_accountid := _account0.Group("/:accountId", _accountidMw()...)
					_accountid.GET("/params", append(_accountlistparamMw(), cloudman.AccountListParam)...)
					_params := _accountid.Group("/params", _paramsMw()...)
					_params.DELETE("/:id", append(_accountdelparamMw(), cloudman.AccountDelParam)...)
					_params.PUT("/:id", append(_accountupdateparamMw(), cloudman.AccountUpdateParam)...)
					_accountid.POST("/params", append(_accountcreateparamMw(), cloudman.AccountCreateParam)...)
					_params0 := _accountid.Group("/params", _params0Mw()...)
					_params0.GET("/:id", append(_accountparaminfoMw(), cloudman.AccountParamInfo)...)
				}
			}
			{
				_audit := _v2.Group("/audit", _auditMw()...)
				_audit.POST("/order", append(_resauditgetorderlistMw(), cloudman.ResAuditGetOrderList)...)
				_order := _audit.Group("/order", _orderMw()...)
				_order.GET("/:id", append(_resauditgetorderMw(), cloudman.ResAuditGetOrder)...)
				{
					_order_log := _audit.Group("/order-log", _order_logMw()...)
					_order_log.GET("/:id", append(_resauditgetorderlogMw(), cloudman.ResAuditGetOrderLog)...)
				}
			}
			{
				_cmdb := _v2.Group("/cmdb", _cmdbMw()...)
				_cmdb.POST("/delete", append(_cmdbdeleteMw(), cloudman.CmdbDelete)...)
				_cmdb.POST("/diff", append(_cmdbdiffMw(), cloudman.CmdbDiff)...)
				_diff := _cmdb.Group("/diff", _diffMw()...)
				_diff.POST("/delete", append(_cmdbdiffdeleteMw(), cloudman.CmdbDiffDelete)...)
				_diff.POST("/update", append(_cmdbdiffupdateMw(), cloudman.CmdbDiffUpdate)...)
				_cmdb.POST("/manual_update", append(_cmdbmanualupdateMw(), cloudman.CmdbManualUpdate)...)
				_cmdb.POST("/process", append(_cmdbdiffprocessMw(), cloudman.CmdbDiffProcess)...)
				_cmdb.POST("/pull_info", append(_cmdbpullinfoMw(), cloudman.CmdbPullInfo)...)
				_cmdb.POST("/query", append(_cmdbqueryMw(), cloudman.CmdbQuery)...)
				_cmdb.POST("/query_associate", append(_cmdbqueryassociateMw(), cloudman.CmdbQueryAssociate)...)
			}
			{
				_common := _v2.Group("/common", _commonMw()...)
				_common.POST("/get_agent_id", append(_cloudgatewaygethostagentidMw(), cloudman.CloudGatewayGetHostAgentID)...)
			}
			{
				_init := _v2.Group("/init", _initMw()...)
				_init.GET("/status", append(_opcloudmantakumiinitstatusMw(), cloudman.OpCloudmanTakumiInitStatus)...)
			}
			{
				_resource := _v2.Group("/resource", _resourceMw()...)
				_resource.POST("/bulk-update", append(_restemplateupdateresourcesMw(), cloudman.ResTemplateUpdateResources)...)
				_resource.POST("/check_instancename_seq", append(_restemplatecheckinstancenameseqMw(), cloudman.ResTemplateCheckInstanceNameSeq)...)
				_resource.GET("/cloud_group", append(_resourcegrouplistcloudMw(), cloudman.ResourceGroupListCloud)...)
				_resource.POST("/create", append(_restemplatecreateresourceMw(), cloudman.ResTemplateCreateResource)...)
				_resource.POST("/domain", append(_domaindescribeMw(), cloudman.DomainDescribe)...)
				_domain := _resource.Group("/domain", _domainMw()...)
				_domain.POST("/create", append(_domaincreatedomainMw(), cloudman.DomainCreateDomain)...)
				_domain.POST("/records", append(_domaindescriberecordsMw(), cloudman.DomainDescribeRecords)...)
				_domain.POST("/sync-dnspod", append(_domainsyncdnspodMw(), cloudman.DomainSyncDnspod)...)
				_resource.POST("/eip", append(_eipdescribeMw(), cloudman.EipDescribe)...)
				_eip := _resource.Group("/eip", _eipMw()...)
				_eip.POST("/ipam", append(_eipdescribeipamMw(), cloudman.EipDescribeIpam)...)
				_eip.POST("/segment", append(_eipdescribesegmentMw(), cloudman.EipDescribeSegment)...)
				_resource.POST("/gen_instancename_with_rule", append(_restemplategeninstancenamewithruleMw(), cloudman.ResTemplateGenInstanceNameWithRule)...)
				_resource.GET("/group", append(_resourcegrouplistMw(), cloudman.ResourceGroupList)...)
				_group := _resource.Group("/group", _groupMw()...)
				_group.DELETE("/:id", append(_resourcegroupdelMw(), cloudman.ResourceGroupDel)...)
				_group.GET("/:id", append(_resourcegroupinfoMw(), cloudman.ResourceGroupInfo)...)
				_group.PUT("/:id", append(_resourcegroupupdateMw(), cloudman.ResourceGroupUpdate)...)
				_resource.POST("/group", append(_resourcegroupcreateMw(), cloudman.ResourceGroupCreate)...)
				_resource.POST("/host", append(_hostrestemplatedescribeMw(), cloudman.HostResTemplateDescribe)...)
				_host := _resource.Group("/host", _hostMw()...)
				_host.GET("/dashboard", append(_hostrestemplatedashboardMw(), cloudman.HostResTemplateDashboard)...)
				_host.GET("/exist_os_names", append(_hostrestemplategetexistosnamesMw(), cloudman.HostResTemplateGetExistOSNames)...)
				_host.GET("/remote_token", append(_hostrestemplategetremotetokenMw(), cloudman.HostResTemplateGetRemoteToken)...)
				_host.POST("/sync", append(_hostrestemplatesynchostinstancesMw(), cloudman.HostResTemplateSyncHostInstances)...)
				{
					_ipv6 := _host.Group("/ipv6", _ipv6Mw()...)
					_ipv6.POST("/assign", append(_hostrestemplateassignipv6addressMw(), cloudman.HostResTemplateAssignIpv6Address)...)
					_ipv6.POST("/unassign", append(_hostrestemplateunassignipv6addressMw(), cloudman.HostResTemplateUnassignIpv6Address)...)
				}
				_resource.POST("/host-create", append(_hostrestemplateinputresourceMw(), cloudman.HostResTemplateInputResource)...)
				_resource.POST("/host-export", append(_hostrestemplateexportxlsxMw(), cloudman.HostResTemplateExportXlsx)...)
				_resource.POST("/host-import", append(_hostrestemplateimportxlsxMw(), cloudman.HostResTemplateImportXlsx)...)
				_resource.DELETE("/host-lock", append(_hostrestemplateunlockresourceMw(), cloudman.HostResTemplateUnlockResource)...)
				_resource.POST("/host-lock", append(_hostrestemplatelockresourceMw(), cloudman.HostResTemplateLockResource)...)
				_resource.POST("/host-ops-status", append(_hostrestemplatedescribeopsstatusMw(), cloudman.HostResTemplateDescribeOpsStatus)...)
				_resource.POST("/host-query", append(_hostrestemplatequeryMw(), cloudman.HostResTemplateQuery)...)
				_resource.POST("/ip", append(_ipdescribeMw(), cloudman.IPDescribe)...)
				_ip := _resource.Group("/ip", _ipMw()...)
				_ip.POST("/create", append(_ipcreatecustomipMw(), cloudman.IPCreateCustomIP)...)
				_ip.DELETE("/:id", append(_ipdeleteipMw(), cloudman.IPDeleteIP)...)
				_ip.POST("/ips", append(_ipdescribebyipsMw(), cloudman.IPDescribeByIPs)...)
				_resource.POST("/ip_group", append(_ipcreateipgroupMw(), cloudman.IPCreateIPGroup)...)
				_ip_group := _resource.Group("/ip_group", _ip_groupMw()...)
				_ip_group.DELETE("/:id", append(_ipdeleteipgroupMw(), cloudman.IPDeleteIPGroup)...)
				_ip_group.PUT("/:id", append(_ipmodifyipgroupMw(), cloudman.IPModifyIPGroup)...)
				_resource.GET("/ip_groups", append(_ipdescribeipgroupMw(), cloudman.IPDescribeIPGroup)...)
				_resource.POST("/loadbalancer", append(_loadbalancerdescribeMw(), cloudman.LoadBalancerDescribe)...)
				_loadbalancer := _resource.Group("/loadbalancer", _loadbalancerMw()...)
				_loadbalancer.POST("/cleanup", append(_loadbalancercleanuploadbalancerMw(), cloudman.LoadBalancerCleanupLoadBalancer)...)
				_loadbalancer.GET("/:id", append(_loadbalancerdescribedetailMw(), cloudman.LoadBalancerDescribeDetail)...)
				_resource.POST("/mysql", append(_mysqldescribeclusterMw(), cloudman.MysqlDescribeCluster)...)
				_mysql := _resource.Group("/mysql", _mysqlMw()...)
				_mysql.POST("/sync", append(_mysqlsyncmysqlinstancesMw(), cloudman.MysqlSyncMysqlInstances)...)
				{
					_backup := _mysql.Group("/backup", _backupMw()...)
					_backup.POST("/upload", append(_mysqluploadbackupsettoossMw(), cloudman.MysqlUploadBackupSetToOSS)...)
				}
				{
					_parameter_groups := _mysql.Group("/parameter-groups", _parameter_groupsMw()...)
					{
						_isp_id := _parameter_groups.Group("/:isp_id", _isp_idMw()...)
						_isp_id.POST("/:rid", append(_mysqldescribeparamsgroupsMw(), cloudman.MysqlDescribeParamsGroups)...)
					}
				}
				_resource.POST("/mysql-export", append(_mysqlexportxlsxMw(), cloudman.MysqlExportXlsx)...)
				_resource.POST("/mysql-import", append(_mysqlimportxlsxMw(), cloudman.MysqlImportXlsx)...)
				_resource.DELETE("/mysql-lock", append(_mysqlunlockresourceMw(), cloudman.MysqlUnlockResource)...)
				_resource.POST("/mysql-lock", append(_mysqllockresourceMw(), cloudman.MysqlLockResource)...)
				_resource.POST("/order-price", append(_restemplategetsalepriceMw(), cloudman.ResTemplateGetSalePrice)...)
				_resource.GET("/rams", append(_ramdescriberampolicyMw(), cloudman.RAMDescribeRAMPolicy)...)
				_resource.GET("/recycle", append(_recyclelistMw(), cloudman.RecycleList)...)
				_recycle := _resource.Group("/recycle", _recycleMw()...)
				_recycle.GET("/:id", append(_recycleinfoMw(), cloudman.RecycleInfo)...)
				_id3 := _recycle.Group("/:id", _id3Mw()...)
				_id3.PUT("/destroy", append(_recycledestroyMw(), cloudman.RecycleDestroy)...)
				_id3.POST("/recover", append(_recyclerecoverMw(), cloudman.RecycleRecover)...)
				_resource.GET("/recycle-policy", append(_recyclegetpolicylistMw(), cloudman.RecycleGetPolicyList)...)
				_recycle_policy := _resource.Group("/recycle-policy", _recycle_policyMw()...)
				_recycle_policy.DELETE("/:id", append(_recycledelpolicyMw(), cloudman.RecycleDelPolicy)...)
				_recycle_policy.PUT("/:id", append(_recyclechangepolicyMw(), cloudman.RecycleChangePolicy)...)
				_resource.POST("/recycle-policy", append(_recyclecreatepolicyMw(), cloudman.RecycleCreatePolicy)...)
				_recycle_policy0 := _resource.Group("/recycle-policy", _recycle_policy0Mw()...)
				_recycle_policy0.GET("/:id", append(_recyclegetpolicyMw(), cloudman.RecycleGetPolicy)...)
				_resource.POST("/redis", append(_redisdescribeMw(), cloudman.RedisDescribe)...)
				_redis := _resource.Group("/redis", _redisMw()...)
				_redis.POST("/sync", append(_redissyncredisinstancesMw(), cloudman.RedisSyncRedisInstances)...)
				{
					_backup0 := _redis.Group("/backup", _backup0Mw()...)
					_backup0.GET("/:order_id", append(_redisgetbackupdetailMw(), cloudman.RedisGetBackupDetail)...)
				}
				_resource.POST("/redis-export", append(_redisexportxlsxMw(), cloudman.RedisExportXlsx)...)
				_resource.POST("/redis-import", append(_redisimportxlsxMw(), cloudman.RedisImportXlsx)...)
				_resource.DELETE("/redis-lock", append(_redisunlockresourceMw(), cloudman.RedisUnlockResource)...)
				_resource.POST("/redis-lock", append(_redislockresourceMw(), cloudman.RedisLockResource)...)
				_resource.POST("/run-policy", append(_recyclerunpolicyMw(), cloudman.RecycleRunPolicy)...)
				_resource.POST("/securitygroup", append(_securitygroupdescribeMw(), cloudman.SecurityGroupDescribe)...)
				_securitygroup := _resource.Group("/securitygroup", _securitygroupMw()...)
				_securitygroup.GET("/alb_acls", append(_securitygroupdescribealbaclsMw(), cloudman.SecurityGroupDescribeALBAcls)...)
				_securitygroup.POST("/cleanup", append(_securitygroupcleanupsecuritygroupsMw(), cloudman.SecurityGroupCleanupSecurityGroups)...)
				_securitygroup.GET("/custom-tags", append(_securitygroupgetaccountregiontagsMw(), cloudman.SecurityGroupGetAccountRegionTags)...)
				_custom_tags := _securitygroup.Group("/custom-tags", _custom_tagsMw()...)
				_custom_tags.POST("/update", append(_securitygroupupdatesecuritygroupcustomtagMw(), cloudman.SecurityGroupUpdateSecurityGroupCustomTag)...)
				_securitygroup.GET("/custom-tags-sg", append(_securitygroupgetcustomtagsecuritygroupsMw(), cloudman.SecurityGroupGetCustomTagSecurityGroups)...)
				_securitygroup.POST("/export", append(_securitygroupexportsecuritygroupMw(), cloudman.SecurityGroupExportSecurityGroup)...)
				_securitygroup.POST("/instances", append(_securitygroupdescribebyinstancesMw(), cloudman.SecurityGroupDescribeByInstances)...)
				_securitygroup.POST("/join", append(_securitygroupjoinsecuritygroupMw(), cloudman.SecurityGroupJoinSecurityGroup)...)
				_securitygroup.POST("/leave", append(_securitygroupleavesecuritygroupMw(), cloudman.SecurityGroupLeaveSecurityGroup)...)
				_securitygroup.POST("/rule", append(_securitygroupcreatesecuritygroupruleMw(), cloudman.SecurityGroupCreateSecurityGroupRule)...)
				_securitygroup.PUT("/rule", append(_securitygroupupdatesecuritygroupruleMw(), cloudman.SecurityGroupUpdateSecurityGroupRule)...)
				_rule := _securitygroup.Group("/rule", _ruleMw()...)
				_rule.POST("/batch-delete", append(_securitygroupbatchdeletesecuritygroupruleMw(), cloudman.SecurityGroupBatchDeleteSecurityGroupRule)...)
				_rule.POST("/export", append(_securitygroupexportsecuritygroupruleMw(), cloudman.SecurityGroupExportSecurityGroupRule)...)
				_securitygroup.POST("/rules", append(_securitygroupdescriberulesMw(), cloudman.SecurityGroupDescribeRules)...)
				_securitygroup.POST("/update", append(_securitygroupupdatesecuritygroupsMw(), cloudman.SecurityGroupUpdateSecurityGroups)...)
				_securitygroup.GET("/whitelists", append(_securitygroupdescribeipwhitelistsMw(), cloudman.SecurityGroupDescribeIPWhitelists)...)
				{
					_alb_acl := _securitygroup.Group("/alb_acl", _alb_aclMw()...)
					{
						_acl_id := _alb_acl.Group("/:acl_id", _acl_idMw()...)
						_acl_id.POST("/entry", append(_securitygroupaddalbaclsentriesMw(), cloudman.SecurityGroupAddALBAclsEntries)...)
						_entry := _acl_id.Group("/entry", _entryMw()...)
						_entry.POST("/batch-delete", append(_securitygroupremovealbaclsentriesMw(), cloudman.SecurityGroupRemoveALBAclsEntries)...)
					}
				}
				{
					_relate_instances := _securitygroup.Group("/relate-instances", _relate_instancesMw()...)
					_relate_instances.POST("/export", append(_securitygroupexportrelateinstancesMw(), cloudman.SecurityGroupExportRelateInstances)...)
				}
				{
					_whitelist := _securitygroup.Group("/whitelist", _whitelistMw()...)
					_whitelist.PUT("/:security_group_id", append(_securitygroupmodifyipwhitelistsMw(), cloudman.SecurityGroupModifyIPWhitelists)...)
				}
				_resource.GET("/tags", append(_tagslistMw(), cloudman.TagsList)...)
				_tags := _resource.Group("/tags", _tagsMw()...)
				_tags.DELETE("/:id", append(_tagsdelMw(), cloudman.TagsDel)...)
				_tags.GET("/:id", append(_tagsinfoMw(), cloudman.TagsInfo)...)
				_tags.PUT("/:id", append(_tagsupdateMw(), cloudman.TagsUpdate)...)
				_resource.POST("/tags", append(_tagscreateMw(), cloudman.TagsCreate)...)
				_resource.GET("/template", append(_restemplatelistMw(), cloudman.ResTemplateList)...)
				_template := _resource.Group("/template", _templateMw()...)
				_template.DELETE("/:id", append(_restemplatedelMw(), cloudman.ResTemplateDel)...)
				_template.GET("/:id", append(_restemplateinfoMw(), cloudman.ResTemplateInfo)...)
				_template.PUT("/:id", append(_restemplateupdateMw(), cloudman.ResTemplateUpdate)...)
				_resource.POST("/template", append(_restemplatecreateMw(), cloudman.ResTemplateCreate)...)
				_resource.POST("/update", append(_restemplateupdateresourceMw(), cloudman.ResTemplateUpdateResource)...)
				{
					_bandwidth_package := _resource.Group("/bandwidth-package", _bandwidth_packageMw()...)
					_bandwidth_package.GET("/cbwp", append(_bandwidthpackagedescribecbwpMw(), cloudman.BandwidthPackageDescribeCbwp)...)
				}
				{
					_ddos := _resource.Group("/ddos", _ddosMw()...)
					_ddos.GET("/bgp", append(_ddosdescribeddosbgpMw(), cloudman.DdosDescribeDdosBgp)...)
				}
				{
					_group_policy := _resource.Group("/group-policy", _group_policyMw()...)
					_group_policy.GET("/:id", append(_resgrouppolicyinfoMw(), cloudman.ResGroupPolicyInfo)...)
					_group_policy.PUT("/:id", append(_resgrouppolicyupdateMw(), cloudman.ResGroupPolicyUpdate)...)
				}
				{
					_group_policy_preview := _resource.Group("/group-policy-preview", _group_policy_previewMw()...)
					_group_policy_preview.GET("/count", append(_resgrouppolicycountresourceMw(), cloudman.ResGroupPolicyCountResource)...)
					_group_policy_preview.POST("/query", append(_resgrouppolicyfindresourceMw(), cloudman.ResGroupPolicyFindResource)...)
				}
				{
					_host0 := _resource.Group("/host", _host0Mw()...)
					_host0.GET("/option", append(_hostrestemplateoptionMw(), cloudman.HostResTemplateOption)...)
				}
				{
					_host_info := _resource.Group("/host-info", _host_infoMw()...)
					_host_info.GET("/:id", append(_hostrestemplategethostinfoMw(), cloudman.HostResTemplateGetHostInfo)...)
					_id4 := _host_info.Group("/:id", _id4Mw()...)
					_id4.GET("/disk", append(_hostrestemplategethostdiskinfoMw(), cloudman.HostResTemplateGetHostDiskInfo)...)
				}
				{
					_host_monitor := _resource.Group("/host-monitor", _host_monitorMw()...)
					_host_monitor.POST("/cpu", append(_hostrestemplategetcpumonitorMw(), cloudman.HostResTemplateGetCPUMonitor)...)
					_host_monitor.POST("/disk", append(_hostrestemplategetdiskmonitorMw(), cloudman.HostResTemplateGetDiskMonitor)...)
					_host_monitor.POST("/gaia", append(_hostrestemplategetgaiamonitorMw(), cloudman.HostResTemplateGetGaiaMonitor)...)
					_host_monitor.POST("/gaia-view", append(_hostrestemplategetgaiaviewMw(), cloudman.HostResTemplateGetGaiaView)...)
					_host_monitor.POST("/io", append(_hostrestemplategetiomonitorMw(), cloudman.HostResTemplateGetIOMonitor)...)
					_host_monitor.POST("/mem", append(_hostrestemplategetmemmonitorMw(), cloudman.HostResTemplateGetMemMonitor)...)
					_host_monitor.POST("/net", append(_hostrestemplategetnetmonitorMw(), cloudman.HostResTemplateGetNetMonitor)...)
					_host_monitor.POST("/uptime", append(_hostrestemplategetuptimemonitorMw(), cloudman.HostResTemplateGetUPTimeMonitor)...)
					_host_monitor.POST("/view", append(_hostrestemplategetgraphviewMw(), cloudman.HostResTemplateGetGraphView)...)
				}
				{
					_host_update := _resource.Group("/host-update", _host_updateMw()...)
					_host_update.POST("/monitor-status", append(_hostrestemplateupdatemonitorstatusMw(), cloudman.HostResTemplateUpdateMonitorStatus)...)
				}
				{
					_loadbalancer_network := _resource.Group("/loadbalancer-network", _loadbalancer_networkMw()...)
					_loadbalancer_network.POST("/acls", append(_loadbalancerlistaclsMw(), cloudman.LoadBalancerListACLs)...)
					_loadbalancer_network.POST("/certs", append(_loadbalancerlistcertificatesMw(), cloudman.LoadBalancerListCertificates)...)
					_loadbalancer_network.POST("/zones", append(_loadbalancerdescribezonesMw(), cloudman.LoadBalancerDescribeZones)...)
				}
				{
					_loadbalancer_server_groups := _resource.Group("/loadbalancer-server-groups", _loadbalancer_server_groupsMw()...)
					_loadbalancer_server_groups.GET("/:id", append(_loadbalancerdescribeservergroupMw(), cloudman.LoadBalancerDescribeServerGroup)...)
				}
				{
					_mysql0 := _resource.Group("/mysql", _mysql0Mw()...)
					_mysql0.GET("/option", append(_mysqloptionMw(), cloudman.MysqlOption)...)
				}
				{
					_mysql_info := _resource.Group("/mysql-info", _mysql_infoMw()...)
					_mysql_info.GET("/:id", append(_mysqlclusterinfoMw(), cloudman.MysqlClusterInfo)...)
				}
				{
					_mysql_info_account := _resource.Group("/mysql-info-account", _mysql_info_accountMw()...)
					_mysql_info_account.GET("/:id", append(_mysqlgetaccountsMw(), cloudman.MysqlGetAccounts)...)
				}
				{
					_mysql_info_database := _resource.Group("/mysql-info-database", _mysql_info_databaseMw()...)
					_mysql_info_database.GET("/:id", append(_mysqlgetdatabasesMw(), cloudman.MysqlGetDatabases)...)
				}
				{
					_mysql_info_endpoint := _resource.Group("/mysql-info-endpoint", _mysql_info_endpointMw()...)
					_mysql_info_endpoint.GET("/:id", append(_mysqlgetendpointsMw(), cloudman.MysqlGetEndpoints)...)
				}
				{
					_mysql_info_whitelist := _resource.Group("/mysql-info-whitelist", _mysql_info_whitelistMw()...)
					_mysql_info_whitelist.GET("/:id", append(_mysqlgetwhitelistsMw(), cloudman.MysqlGetWhitelists)...)
				}
				{
					_mysql_network := _resource.Group("/mysql-network", _mysql_networkMw()...)
					{
						_ip_whitelist := _mysql_network.Group("/ip-whitelist", _ip_whitelistMw()...)
						_ip_whitelist.POST("/:rid", append(_mysqldescribeipwhitelistMw(), cloudman.MysqlDescribeIPWhiteList)...)
					}
					{
						_network := _mysql_network.Group("/network", _networkMw()...)
						_network.GET("/:rid", append(_mysqlgetnetworkMw(), cloudman.MysqlGetNetwork)...)
					}
					{
						_security_group := _mysql_network.Group("/security-group", _security_groupMw()...)
						_security_group.GET("/:rid", append(_mysqlgetsecuritygroupMw(), cloudman.MysqlGetSecurityGroup)...)
					}
					{
						_vswitch := _mysql_network.Group("/vswitch", _vswitchMw()...)
						_vswitch.GET("/:rid", append(_mysqlgetvswitchMw(), cloudman.MysqlGetVSwitch)...)
					}
				}
				{
					_mysql_ref := _resource.Group("/mysql-ref", _mysql_refMw()...)
					{
						_available_zone := _mysql_ref.Group("/available_zone", _available_zoneMw()...)
						_available_zone.GET("/:rid", append(_mysqldescribeavailablezoneMw(), cloudman.MysqlDescribeAvailableZone)...)
					}
					{
						_classes := _mysql_ref.Group("/classes", _classesMw()...)
						_classes.GET("/:rid", append(_mysqldescribedbclassesMw(), cloudman.MysqlDescribeDBClasses)...)
					}
					{
						_db_type := _mysql_ref.Group("/db_type", _db_typeMw()...)
						_db_type.GET("/:rid", append(_mysqlgetdbtypesMw(), cloudman.MysqlGetDBTypes)...)
					}
				}
				{
					_ram := _resource.Group("/ram", _ramMw()...)
					_ram.GET("/cloud", append(_ramgetcloudrampolicyMw(), cloudman.RAMGetCloudRamPolicy)...)
					_ram.POST("/deploy", append(_ramrampolicydeployMw(), cloudman.RAMRAMPolicyDeploy)...)
					_ram.POST("/sync", append(_ramsyncrampolicyMw(), cloudman.RAMSyncRAMPolicy)...)
					{
						_deploy := _ram.Group("/deploy", _deployMw()...)
						_deploy.POST("/preview", append(_rampreviewrampolicydeployMw(), cloudman.RAMPreviewRAMPolicyDeploy)...)
					}
					{
						_id5 := _ram.Group("/:id", _id5Mw()...)
						_id5.PUT("/ip_group", append(_rammodifyrampolicysourceipgroupMw(), cloudman.RAMModifyRAMPolicySourceIPGroup)...)
					}
					{
						_new := _ram.Group("/new", _newMw()...)
						_new.POST("/sync", append(_ramsyncnewrampolicyMw(), cloudman.RAMSyncNewRAMPolicy)...)
					}
				}
				{
					_redis0 := _resource.Group("/redis", _redis0Mw()...)
					_redis0.GET("/option", append(_redisoptionMw(), cloudman.RedisOption)...)
				}
				{
					_redis_info := _resource.Group("/redis-info", _redis_infoMw()...)
					_redis_info.GET("/:id", append(_redisinfoMw(), cloudman.RedisInfo)...)
				}
				{
					_redis_info_account := _resource.Group("/redis-info-account", _redis_info_accountMw()...)
					_redis_info_account.GET("/:id", append(_redisgetaccountsMw(), cloudman.RedisGetAccounts)...)
				}
				{
					_redis_info_whitelist := _resource.Group("/redis-info-whitelist", _redis_info_whitelistMw()...)
					_redis_info_whitelist.GET("/:id", append(_redisgetwhitelistsMw(), cloudman.RedisGetWhitelists)...)
				}
				{
					_redis_network := _resource.Group("/redis-network", _redis_networkMw()...)
					{
						_network0 := _redis_network.Group("/network", _network0Mw()...)
						_network0.GET("/:rid", append(_redisgetnetworkMw(), cloudman.RedisGetNetwork)...)
					}
					{
						_security_group0 := _redis_network.Group("/security-group", _security_group0Mw()...)
						_security_group0.GET("/:rid", append(_redisgetsecuritygroupMw(), cloudman.RedisGetSecurityGroup)...)
					}
					{
						_vswitch0 := _redis_network.Group("/vswitch", _vswitch0Mw()...)
						_vswitch0.GET("/:rid", append(_redisgetvswitchMw(), cloudman.RedisGetVSwitch)...)
					}
				}
				{
					_redis_ref := _resource.Group("/redis-ref", _redis_refMw()...)
					{
						_available_zone0 := _redis_ref.Group("/available_zone", _available_zone0Mw()...)
						_available_zone0.GET("/:rid", append(_redisdescribeavailablezoneMw(), cloudman.RedisDescribeAvailableZone)...)
					}
					{
						_classes0 := _redis_ref.Group("/classes", _classes0Mw()...)
						_classes0.GET("/:rid", append(_redisdescribecacheclassesMw(), cloudman.RedisDescribeCacheClasses)...)
					}
					{
						_parameter_groups0 := _redis_ref.Group("/parameter-groups", _parameter_groups0Mw()...)
						{
							_isp_id0 := _parameter_groups0.Group("/:isp_id", _isp_id0Mw()...)
							_isp_id0.GET("/:rid", append(_redisdescribeparamsgroupsMw(), cloudman.RedisDescribeParamsGroups)...)
						}
					}
					{
						_type := _redis_ref.Group("/type", _typeMw()...)
						_type.GET("/:rid", append(_redisgetcachetypesMw(), cloudman.RedisGetCacheTypes)...)
					}
				}
				{
					_run := _resource.Group("/run", _runMw()...)
					_run.POST("/:id", append(_restemplaterunorderMw(), cloudman.ResTemplateRunOrder)...)
					_id6 := _run.Group("/:id", _id6Mw()...)
					_id6.POST("/retry", append(_restemplaterunorderretryMw(), cloudman.ResTemplateRunOrderRetry)...)
				}
				{
					_system := _resource.Group("/system", _systemMw()...)
					_system.GET("/tags", append(_tagslistsystemMw(), cloudman.TagsListSystem)...)
				}
				{
					_template_hostinfo := _resource.Group("/template-hostinfo", _template_hostinfoMw()...)
					_template_hostinfo.GET("/images", append(_hostrestemplategetimagesMw(), cloudman.HostResTemplateGetImages)...)
					_template_hostinfo.GET("/instance-types", append(_hostrestemplategetinstancetypesMw(), cloudman.HostResTemplateGetInstanceTypes)...)
					{
						_auto_snapshot_policyex := _template_hostinfo.Group("/auto-snapshot-policyex", _auto_snapshot_policyexMw()...)
						_auto_snapshot_policyex.GET("/:isp_id", append(_hostrestemplatedescribeautosnapshotpolicyexMw(), cloudman.HostResTemplateDescribeAutoSnapshotPolicyEX)...)
					}
					{
						_charge_types := _template_hostinfo.Group("/charge-types", _charge_typesMw()...)
						_charge_types.GET("/:isp", append(_hostrestemplatelistchargetypeMw(), cloudman.HostResTemplateListChargeType)...)
					}
					{
						_keypairs := _template_hostinfo.Group("/keypairs", _keypairsMw()...)
						_keypairs.GET("/:rid", append(_hostrestemplategetkeypairsMw(), cloudman.HostResTemplateGetKeyPairs)...)
					}
					{
						_network1 := _template_hostinfo.Group("/network", _network1Mw()...)
						_network1.GET("/:rid", append(_hostrestemplategetnetworkMw(), cloudman.HostResTemplateGetNetwork)...)
					}
					{
						_security_group1 := _template_hostinfo.Group("/security-group", _security_group1Mw()...)
						_security_group1.GET("/:rid", append(_hostrestemplategetsecuritygroupMw(), cloudman.HostResTemplateGetSecurityGroup)...)
					}
					{
						_volume_types := _template_hostinfo.Group("/volume-types", _volume_typesMw()...)
						_volume_types.GET("/:rid", append(_hostrestemplategetvolumetypesMw(), cloudman.HostResTemplateGetVolumeTypes)...)
					}
					{
						_vswitch1 := _template_hostinfo.Group("/vswitch", _vswitch1Mw()...)
						_vswitch1.GET("/:rid", append(_hostrestemplategetvswitchMw(), cloudman.HostResTemplateGetVSwitch)...)
					}
					{
						_zone := _template_hostinfo.Group("/zone", _zoneMw()...)
						_zone.GET("/:rid", append(_hostrestemplategetzoneMw(), cloudman.HostResTemplateGetZone)...)
					}
				}
			}
			{
				_resource_init := _v2.Group("/resource_init", _resource_initMw()...)
				_resource_init.GET("/jumpserver", append(_initresourcegetjumpserverMw(), cloudman.InitResourceGetJumpserver)...)
				_resource_init.GET("/pipeline", append(_initresourcegetpipelineMw(), cloudman.InitResourceGetPipeline)...)
				{
					_param := _resource_init.Group("/param", _paramMw()...)
					_param.POST("/preview", append(_initresourcepreviewparamMw(), cloudman.InitResourcePreviewParam)...)
				}
			}
		}
	}
}
