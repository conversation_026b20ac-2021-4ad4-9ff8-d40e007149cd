// Code generated by hertz generator.

package cloudman

import (
	"github.com/cloudwego/hertz/pkg/app"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/middlewares/auth"
)

func rootMw() []app.HandlerFunc {
	// your code...
	return []app.HandlerFunc{
		auth.Auth(),
	}
}

func _apiMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _v1Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _idMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountdestroyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountgetbindregionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountpingMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountsyncMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountretrieveMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _id0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountupdatestatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountcreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accounttestrawpingMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _innerruntaskapiMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _logMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taskloglisttasklogMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tasklogretrievetasklogMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountgetaccountregionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _regionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _regionlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _regioninfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _regioncreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taskMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tasklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taskdeleteMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _id1Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taskretrieveMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taskruntaskapiMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taskupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _taskcreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _allMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountlistallMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloud_agentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _task0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudagenttasklistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _id2Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudagenttaskdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgetaccountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardautofillsnapshotMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgetbackendversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardfillsnapshotMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgethostbriefMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgethostdailychartMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgetifworkorderusedMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgetresbriefMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgetresdailychartMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _dashboardgetuserauthMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _debugMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _debugfixrestagbindMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _debugtestMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _permMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _permissioncheckinstancepermissionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _res_changelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudagentreschangelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _servicetreeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _servicetreeaddnodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _servicetreegetentityMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _servicetreegettreeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _servicetreeverifyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _nodeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _servicetreenodeentitycountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _v2Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _systemgetconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _gatewayMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaylistgatewayMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _agentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaylistagentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewayexportxlsxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewayrawagentinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _agent0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaychangeagentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaydeleteagentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaychangeagentstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaychangeagentsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaygetgateaddressMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewayinsertgateaddressMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaylistagentinstallconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewayupdateagentinstallconfigMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaylistagentversionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewayreinstallagentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewayupdateagentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _agent1Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaygetagentscriptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _gateway0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaycreategatewayMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaygetgatewayscriptMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaygetgatewaytopoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaygettopoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _systemsecuritygroupruledailynotifyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _systemwecomdailynotifyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _account0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _paramsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountlistparamMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountdelparamMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountupdateparamMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _params0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountcreateparamMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _accountparaminfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _auditMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _orderMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resauditgetorderlistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resauditgetorderMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _order_logMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resauditgetorderlogMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbdeleteMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _diffMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbdiffMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbdiffdeleteMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbdiffupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbmanualupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbdiffprocessMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbpullinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cmdbqueryassociateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _commonMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _cloudgatewaygethostagentidMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _initMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _opcloudmantakumiinitstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplateupdateresourcesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplatecheckinstancenameseqMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcegrouplistcloudMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplatecreateresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _domainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _domaindescribeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _domaincreatedomainMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _domaindescriberecordsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _domainsyncdnspodMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _eipMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _eipdescribeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _eipdescribesegmentMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplategeninstancenamewithruleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _groupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcegrouplistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcegroupdelMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcegroupinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcegroupupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcegroupcreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatedescribeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatedashboardMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetexistosnamesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetremotetokenMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatesynchostinstancesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipv6Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateassignipv6addressMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateunassignipv6addressMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateinputresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateexportxlsxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateimportxlsxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateunlockresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatelockresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatedescribeopsstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatequeryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipdescribeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipcreatecustomipMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipdeleteipMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipdescribebyipsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ip_groupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipcreateipgroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipdeleteipgroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipmodifyipgroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ipdescribeipgroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancerMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancerdescribeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancercleanuploadbalancerMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancerdescribedetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqldescribeclusterMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlsyncmysqlinstancesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _backupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqluploadbackupsettoossMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlexportxlsxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlimportxlsxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlunlockresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqllockresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplategetsalepriceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ramdescriberampolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recycleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recyclelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _id3Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recycleinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recycledestroyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recyclerecoverMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recycle_policyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recyclegetpolicylistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recycledelpolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recyclechangepolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recycle_policy0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recyclecreatepolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recyclegetpolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisdescribeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redissyncredisinstancesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _backup0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisgetbackupdetailMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisexportxlsxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisimportxlsxMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisunlockresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redislockresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _recyclerunpolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupdescribeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupdescribealbaclsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupcleanupsecuritygroupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _custom_tagsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupgetaccountregiontagsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupupdatesecuritygroupcustomtagMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupgetcustomtagsecuritygroupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupexportsecuritygroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupdescribebyinstancesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupjoinsecuritygroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupleavesecuritygroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupcreatesecuritygroupruleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ruleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupupdatesecuritygroupruleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupbatchdeletesecuritygroupruleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupexportsecuritygroupruleMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupdescriberulesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupupdatesecuritygroupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupdescribeipwhitelistsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _alb_aclMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _acl_idMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _entryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupaddalbaclsentriesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupremovealbaclsentriesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _relate_instancesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupexportrelateinstancesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _whitelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _securitygroupmodifyipwhitelistsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagslistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagsdelMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagsinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagsupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagscreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _templateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplatelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplatedelMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplateinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplateupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplatecreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplateupdateresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ddosMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ddosdescribeddosbgpMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _group_policyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resgrouppolicyinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resgrouppolicyupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _group_policy_previewMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resgrouppolicycountresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resgrouppolicyfindresourceMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _host0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateoptionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _host_infoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _id4Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategethostinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategethostdiskinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _host_monitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetcpumonitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetdiskmonitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetgaiamonitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetgaiaviewMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetiomonitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetmemmonitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetnetmonitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetuptimemonitorMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetgraphviewMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _host_updateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplateupdatemonitorstatusMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancer_networkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancerlistaclsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancerlistcertificatesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancerdescribezonesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancer_server_groupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _loadbalancerdescribeservergroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqloptionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql_infoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlclusterinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql_info_accountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetaccountsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql_info_databaseMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetdatabasesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql_info_endpointMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetendpointsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql_info_whitelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetwhitelistsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql_networkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ip_whitelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqldescribeipwhitelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _networkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetnetworkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _security_groupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetsecuritygroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _vswitchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetvswitchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysql_refMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _available_zoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqldescribeavailablezoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _classesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqldescribedbclassesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _db_typeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqlgetdbtypesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ramMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ramgetcloudrampolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ramrampolicydeployMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ramsyncrampolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deployMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _rampreviewrampolicydeployMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _id5Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _rammodifyrampolicysourceipgroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _newMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _ramsyncnewrampolicyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redis0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisoptionMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redis_infoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redis_info_accountMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisgetaccountsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redis_info_whitelistMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisgetwhitelistsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redis_networkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _network0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisgetnetworkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _security_group0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisgetsecuritygroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _vswitch0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisgetvswitchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redis_refMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _available_zone0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisdescribeavailablezoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _classes0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisdescribecacheclassesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _typeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisgetcachetypesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _runMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _id6Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplaterunorderMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _restemplaterunorderretryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _systemMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tagslistsystemMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _template_hostinfoMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetimagesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetinstancetypesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _auto_snapshot_policyexMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatedescribeautosnapshotpolicyexMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _charge_typesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplatelistchargetypeMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _keypairsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetkeypairsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _network1Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetnetworkMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _security_group1Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetsecuritygroupMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _volume_typesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetvolumetypesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _vswitch1Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetvswitchMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _zoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _hostrestemplategetzoneMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resource_initMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _initresourcegetjumpserverMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _initresourcegetpipelineMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _paramMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _initresourcepreviewparamMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _parameter_groupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _isp_idMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _mysqldescribeparamsgroupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _bandwidth_packageMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _bandwidthpackagedescribecbwpMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _parameter_groups0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _redisdescribeparamsgroupsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _isp_id0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _eipdescribeipamMw() []app.HandlerFunc {
	// your code...
	return nil
}
