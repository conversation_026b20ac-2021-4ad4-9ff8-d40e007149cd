package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	provider "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// ResourceAudit 资源操作审计
type ResourceAudit struct {
}

// ResourceForm 资源表单
type ResourceForm struct {
	Action       schema.ResourceAction
	IspID        string
	RegionPK     string
	InstanceType string
	RegionID     string
	RawData      string
	Reason       string
	TemplateId   *string
	TemplateName *string
	InitOption   *cloudman.InitOption // 初始化选项, 只有创建资源时有
}

// operateResourceBefore 操作资源前，instanceType:资源类型, action:操作类型, ispID运营商ID, rid region-pk, regionID region-id/cn-shanghai
func operateResourceBefore(ctx context.Context, form ResourceForm) (*common.InitProvider, error) {
	pr, err := getIspInfo(ctx, form.IspID, form.RegionPK, form.RegionID)
	if err != nil {
		return nil, err
	}

	provideName := pr.IspType + "_" + form.InstanceType
	_, ok := provider.ProvideMap[provideName]
	if !ok {
		return nil, errors.New("not found provider")
	}

	pr.Logger = logrus.New()
	return pr, nil
}

func createOrder(ctx context.Context, form ResourceForm, pr *common.InitProvider, createUser string) (*entity.ResOrder, error) {
	isp, err := models.AccountModel.Get(context.Background(), form.IspID)
	if err != nil {
		if form.IspID == "global" {
			isp = &entity.Account{
				Name:  "global",
				AType: "global",
			}
		} else {
			return nil, err
		}
	}
	status := constant.TaskWaitRun
	if cfg.GetSystemConfig().EnableWorkOrder {
		status = constant.TaskFormRunning
	}
	data := &entity.ResOrder{
		Status:       status,
		IspID:        form.IspID,
		IspName:      isp.Name,
		IspType:      isp.AType,
		RegionID:     pr.RegionID,
		RegionName:   pr.RegionName,
		Type:         form.InstanceType,
		Action:       string(form.Action),
		RawData:      form.RawData,
		Reason:       form.Reason,
		TemplateId:   form.TemplateId,
		TemplateName: form.TemplateName,
	}
	if form.InitOption != nil {
		initOptionBytes, err := json.Marshal(form.InitOption)
		if err != nil {
			return nil, err
		}
		initOption := string(initOptionBytes)
		data.InitOption = &initOption
	}

	data.CreateUser = createUser
	if createUser == "" {
		data.CreateUser = permission.GetUsername(ctx)
	}

	data.CreatedTime = time.Now().Unix()

	_, err = models.ResourceOrder.Create(ctx, data)
	if err != nil {
		return nil, err
	}

	taskID := data.ID.Hex()
	logging := BuildLogger(taskID)
	logging.Infoln("初始化任务中......")

	pr.SetLogger(logging)
	return data, nil
}

// BuildLogger 生成logger
func BuildLogger(taskID string) *logrus.Logger {
	hook := LogResultHook{
		TaskLogResultModel: func(meta *entity.TaskLogResult) error {
			return models.TaskLogResultModel.Create(context.Background(), meta)
		},
		TaskLogID: taskID,
	}

	logging := newResultLogger(hook)
	return logging
}

func operateResourceAfter(order *entity.ResOrder, err error) {
	order.OverTime = time.Now().Unix()
	order.Status = constant.TaskSuccess
	if err != nil {
		order.Status = constant.TaskErr
		order.ErrorMsg = err.Error()
	}

	m := map[string]interface{}{
		"over_time": time.Now().Unix(),
		"status":    constant.TaskSuccess,
		"error_msg": "",
	}
	overResult := "任务执行成功"
	// detail := "success"
	if err != nil {
		overResult = "任务执行失败"
		m["status"] = constant.TaskErr
		m["error_msg"] = err.Error()
		// detail = err.Error()
	}
	go hooks.PubOrderEndHandler(*order)
	models.ResourceOrder.Update(context.Background(), order.ID.Hex(), m)
	flushLog(order.ID.Hex(), overResult)
	// 发送企业微信通知
	// go func() {
	// 	if order.GetAction() == "modifyTags" {
	// 		return
	// 	}
	// 	ferryURL := fmt.Sprintf("%s/ferry/process/handle-ticket?workOrderId=%d&processId=%s", cfg.GetSystemConfig().WorkOrderWebHost, order.TicketID, cfg.GetSystemConfig().FlowID)
	// 	msg := OrderMsg{IspType: order.IspType, RegionID: order.RegionID, InstanceType: order.GetOrderType(), Action: order.GetAction(), Status: overResult, Detail: detail, FerryURL: ferryURL, OrderURL: fmt.Sprintf("%s/?order_id=%s", cfg.GetSystemConfig().OrderURL, order.ID.Hex())}
	// 	msgRaw, _ := json.Marshal(msg)
	// 	if err := cloudmanCore.SendNotice(&notifySdk.QNotify{
	// 		Name:        "工单任务通知",
	// 		TemplateMsg: string(msgRaw),
	// 		MikuUser:    []string{order.CreateUser},
	// 	}); err != nil {
	// 		logger.Error(context.Background(), err.Error())
	// 	}
	// }()
	// 如果配置了回调url，执行回调
	// go func() {
	// 	callbackURL := gjson.Get(order.RawData, "callback_url").String()
	// 	header := http.Header{}
	// 	token, err := cfg.GetIAMCliToken("cur")
	// 	if err != nil {
	// 		logger.Errorf(context.Background(), "order callback failed: get iam self token failed")
	// 		return
	// 	}
	// 	header.Add("Authorization", "Bearer "+token)
	// 	bodyObj := map[string]interface{}{
	// 		"status":   order.Status,
	// 		"order_id": order.ID.Hex(),
	// 	}
	// 	body, _ := json.Marshal(bodyObj)
	// 	if callbackURL != "" {
	// 		respBody, respCode, err := utils.HTTPPost(context.Background(), callbackURL, header, body, 5*time.Second)
	// 		if err != nil {
	// 			logger.Errorf(context.Background(), "order callback failed: order:%s, error:%s, respBody:%s, respCode:%d", order.ID.Hex(), err.Error(), string(respBody), respCode)
	// 		}
	// 		logger.Infof(context.Background(), "order callback done: order:%s, respBody:%s, respCode:%d", order.ID.Hex(), string(respBody), respCode)
	// 	}
	// }()
}

// closeTask 关闭任务
func closeTask(order *entity.ResOrder, reason string) error {
	m := map[string]interface{}{
		"over_time": time.Now().Unix(),
		"status":    constant.TaskErr,
		"error_msg": reason,
	}
	return models.ResourceOrder.Update(context.Background(), order.ID.Hex(), m)
}

// GetOrderList 获取订单列表
func (r ResourceAudit) GetOrderList(ctx context.Context, req *cloudman.OrderTaskReq) (*cloudman.OrderTaskListResp, error) {
	li, total, err := models.ResourceOrder.Query(ctx, &schema.ResourceOrderQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		ResourceOrderColumnParam: schema.ResourceOrderColumnParam{
			ISPID:  req.Isp,
			Status: req.Status,
			CreateTime: schema.TimeFilter{
				Ref:   req.CreatedTime.Ref,
				Value: req.CreatedTime.Value,
			},
			Action:   req.Action,
			Type:     req.Type,
			RegionID: req.RegionId,
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Keywords,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	ferryURLTemplate := fmt.Sprintf("%s/ferry/process/handle-ticket?workOrderId=%s&processId=%s", cfg.GetSystemConfig().WorkOrderWebHost, "%d", cfg.GetSystemConfig().FlowID)
	var list []*cloudman.OrderTaskListResult
	for _, l := range li {
		orderObj := &cloudman.OrderTaskListResult{
			Id:           l.ID.Hex(),
			CreatedTime:  l.CreatedTime,
			CreateUser:   l.CreateUser,
			Status:       l.Status,
			IspName:      l.IspName,
			Type:         l.Type,
			Action:       l.Action,
			BeforeInfo:   l.BeforeInfo,
			AfterInfo:    l.AfterInfo,
			ErrorMsg:     l.ErrorMsg,
			IspType:      l.IspType,
			RegionName:   l.RegionName,
			RegionId:     l.RegionID,
			StartTime:    l.StartTime,
			OverTime:     l.OverTime,
			RawData:      l.RawData,
			Reason:       l.Reason,
			StatusDetail: l.StatusDetail,
			IspId:        l.IspID,
		}
		if l.TicketID != 0 {
			orderObj.TicketUrl = fmt.Sprintf(ferryURLTemplate, l.TicketID)
		}
		if l.TemplateId != nil {
			orderObj.TemplateId = *l.TemplateId
		}
		if l.TemplateName != nil {
			orderObj.TemplateName = *l.TemplateName
		}
		list = append(list, orderObj)
	}

	return &cloudman.OrderTaskListResp{
		Total: int32(total),
		List:  list,
	}, nil
}

// GetOrderLog 获取日志
func (r ResourceAudit) GetOrderLog(ctx context.Context, req *cloudman.OrderLogReq) (*cloudman.TaskLogResultList, error) {
	li, _, err := models.ResourceOrder.GetLogList(ctx, &schema.ResourceOrderLogQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Start,
			Size: 5000,
		},
		ResourceOrderColumnParam: schema.ResourceOrderColumnParam{
			TaskLogID: req.Id,
		},
	})
	if err != nil {
		return nil, err
	}

	var list []*cloudman.TaskLogResult
	for _, v := range li {
		list = append(list, &cloudman.TaskLogResult{
			CreatedTime: v.CreatedTime,
			TaskLogId:   v.TaskLogID,
			Stopped:     false,
			TaskResult:  v.TaskResult,
		})
	}
	return &cloudman.TaskLogResultList{List: list}, nil
}

// GetOrder 查询符合条件的订单
func (r ResourceAudit) GetOrder(ctx context.Context, id *cloudman.IDReq) (*cloudman.OrderTaskListResult, error) {
	orderObj, err := models.ResourceOrder.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	result := &cloudman.OrderTaskListResult{
		Id:           orderObj.ID.Hex(),
		CreatedTime:  orderObj.CreatedTime,
		CreateUser:   orderObj.CreateUser,
		Status:       orderObj.Status,
		IspName:      orderObj.IspName,
		Type:         orderObj.Type,
		Action:       orderObj.Action,
		BeforeInfo:   orderObj.BeforeInfo,
		AfterInfo:    orderObj.AfterInfo,
		ErrorMsg:     orderObj.ErrorMsg,
		IspType:      orderObj.IspType,
		RegionName:   orderObj.RegionName,
		RegionId:     orderObj.RegionID,
		StartTime:    orderObj.StartTime,
		OverTime:     orderObj.OverTime,
		RawData:      orderObj.RawData,
		Reason:       orderObj.Reason,
		StatusDetail: orderObj.StatusDetail,
		IspId:        orderObj.IspID,
	}
	if orderObj.TemplateId != nil {
		result.TemplateId = *orderObj.TemplateId
	}
	if orderObj.TemplateName != nil {
		result.TemplateName = *orderObj.TemplateName
	}
	if orderObj.InitOption != nil {
		initOption := make(map[string]interface{})
		if err := json.Unmarshal([]byte(*orderObj.InitOption), &initOption); err != nil {
			return nil, err
		}
		res, err := sjson.Set(result.RawData, "init_option", initOption)
		if err != nil {
			return nil, err
		}
		result.RawData = res
	}
	return result, nil
}
