package service

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"

	"google.golang.org/grpc/metadata"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// Task 任务
type Task struct {
}

// List 获取任务列表
func (t Task) List(ctx context.Context, req *cloudman.TaskListReq) (*cloudman.TaskListResp, error) {
	res, total, err := models.SyncTaskModel.Query(ctx, &schema.SyncTaskQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
		SyncTaskColumnParam: schema.SyncTaskColumnParam{
			Name: req.Name,
		},
		SyncTaskSearchParam: schema.SyncTaskSearchParam{
			Keywords: req.Keywords,
		},
	})

	if err != nil {
		return nil, err
	}

	ids := make(map[string]struct{})
	for _, v := range res {
		if v.AccountID != "" && v.AccountID != constant.AllAccountID {
			ids[v.AccountID] = struct{}{}
		}
	}
	var dds []string
	for k := range ids {
		dds = append(dds, k)
	}

	m, err := models.AccountModel.FindManyWithPkToMap(ctx, dds)
	if err != nil {
		return nil, err
	}

	var l []*cloudman.TaskDetail
	for _, v := range res {
		l = append(l, &cloudman.TaskDetail{
			AccountName:        m[v.AccountID].Name,
			AccountType:        m[v.AccountID].AType,
			Status:             int32(v.Status),
			Name:               v.Name,
			AccountId:          v.AccountID,
			BindZoneId:         v.BindRegionID,
			BindZoneSourceId:   v.BindRegionSourceID,
			BindZoneName:       v.BindRegionName,
			TaskType:           v.TaskType,
			Policy:             uint32(v.Policy),
			Rate:               uint32(v.Rate),
			Timeout:            uint32(v.Timeout),
			LatestRunId:        v.LatestRunID,
			LatestRunStatus:    int32(v.LatestRunStatus),
			LatestRunStartTime: v.LatestRunStartTime,
			LatestRunEndTime:   v.LatestRunEndTime,
			Id:                 v.ID.Hex(),
		})
	}

	return &cloudman.TaskListResp{
		List:  l,
		Total: uint32(total),
	}, nil
}

// Retrieve 获取任务详情
func (t Task) Retrieve(ctx context.Context, req *cloudman.RunTaskReq) (*cloudman.TaskDetail, error) {
	res, err := models.SyncTaskModel.Get(ctx, req.Oid)
	if err != nil {
		return nil, err
	}

	return &cloudman.TaskDetail{
		Status:             int32(res.Status),
		Name:               res.Name,
		AccountId:          res.AccountID,
		BindZoneId:         res.BindRegionID,
		BindZoneSourceId:   res.BindRegionSourceID,
		BindZoneName:       res.BindRegionName,
		TaskType:           res.TaskType,
		Policy:             uint32(res.Policy),
		Rate:               uint32(res.Rate),
		Timeout:            uint32(res.Timeout),
		LatestRunId:        res.LatestRunID,
		LatestRunStatus:    int32(res.LatestRunStatus),
		LatestRunStartTime: res.LatestRunStartTime,
		LatestRunEndTime:   res.LatestRunEndTime,
		Id:                 res.ID.Hex(),
		Retry:              uint32(res.Retry),
	}, nil
}

// Create 创建任务
func (t Task) Create(ctx context.Context, req *cloudman.CreateTaskReq) (*cloudman.Result, error) {
	data := &entity.SyncTask{
		Name:      req.Name,
		AccountID: req.AccountId,
		TaskType:  req.TaskType,
		Policy:    uint(req.Policy),
		Rate:      uint(req.Rate),
		Retry:     uint(req.Retry),
		Timeout:   uint(req.Timeout),
		Status:    uint(req.Status),
	}
	if req.BindZoneId != "" {
		zoneResp, err := models.RegionModel.Get(ctx, req.BindZoneId)
		if err != nil {
			return nil, err
		}
		data.BindRegionID = req.BindZoneId
		data.BindRegionName = zoneResp.Name
		data.BindRegionSourceID = zoneResp.RegionID
	}
	data.CreateUser = permission.GetUsername(ctx)

	taskID, err := models.SyncTaskModel.Create(ctx, data)
	if err != nil {
		return nil, err
	}
	logger.Infof("创建定时任务成功, taskID: %s", taskID)

	return &cloudman.Result{Message: "success"}, nil
}

// RunTaskAPI 运行任务
func (t Task) RunTaskAPI(ctx context.Context, req *cloudman.RunTaskReq) (*cloudman.Result, error) {
	// 检查权限;检查任务状态;
	hasPerm, err := permission.CheckPermissionFromCtx(ctx, "order_admin", "")
	if err != nil || !hasPerm {
		return nil, fmt.Errorf("没有执行权限")
	}
	err = runSyncTask(ctx, req.Oid, true)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

func runInnerRefreshTask(ctx context.Context, task *entity.SyncTask) error {
	username := permission.GetUsername(ctx)
	bgCtx := context.Background()
	var err error
	defer func() {
		status := constant.TaskSuccess
		if err != nil {
			status = constant.TaskErr
		}
		_ = models.SyncTaskModel.Update(bgCtx, task.ID.Hex(), map[string]interface{}{
			"latest_run_status":   status,
			"latest_run_end_time": time.Now().Unix(),
			"update_user":         username,
		})
	}()
	err = models.SyncTaskModel.Update(ctx, task.ID.Hex(), map[string]interface{}{
		"latest_run_status":     constant.TaskRunning,
		"latest_run_start_time": time.Now().Unix(),
		"update_user":           permission.GetUsername(ctx),
	})
	switch task.TaskType {
	case constant.SyncMonitorAgetnStatus:
		err = RefreshMonitorStatus(ctx)
	case constant.SyncAgentStatus:
		err = RefreshAgentStatus(ctx)
	}
	return err
}

func runSyncTask(ctx context.Context, taskID string, isAsync bool) error {
	resp, err := models.SyncTaskModel.Get(ctx, taskID)
	if err != nil {
		return err
	}
	if resp.LatestRunStatus == int(constant.TaskRunning) && time.Now().Unix()-resp.CreatedTime < 7200 {
		return fmt.Errorf("任务正在运行中")
	}

	if resp.AccountID == constant.AllAccountID {
		go runInnerRefreshTask(ctx, resp)
		return nil
	}

	accountResp, err := models.AccountModel.Get(ctx, resp.AccountID, true)
	if err != nil {
		return fmt.Errorf("获取云账户信息失败: %v", err.Error())
	}
	if accountResp.Status == 0 {
		return fmt.Errorf("云账户:%s,已禁用", accountResp.Name)
	}

	taskLog := &entity.TaskLog{
		Name:           resp.Name,
		SyncTaskID:     taskID,
		AccountID:      resp.AccountID,
		AccountName:    accountResp.Name,
		BindRegionID:   resp.BindRegionID,
		BindRegionName: resp.BindRegionName,
		TaskType:       resp.TaskType,
		Status:         uint(constant.TaskRunning),
	}
	taskLog.CreateUser = permission.GetUsername(ctx)

	oid, err := models.TaskLogModel.Create(ctx, taskLog)
	if err != nil {
		logger.Errorf("创建任务记录失败: error:", err.Error())
		return err
	}

	err = models.SyncTaskModel.Update(ctx, taskID, map[string]interface{}{
		"latest_run_status":     constant.TaskRunning,
		"latest_run_start_time": time.Now().Unix(),
		"latest_run_id":         oid,
		"update_user":           permission.GetUsername(ctx),
	})
	if err != nil {
		logger.Errorf("修改任务状态失败: error:", err.Error())
		return err
	}

	err = models.TaskLogResultModel.Create(context.Background(), &entity.TaskLogResult{
		TaskLogID: oid,
	})
	if err != nil {
		return err
	}

	hook := LogResultHook{
		TaskLogResultModel: func(meta *entity.TaskLogResult) error {
			return models.TaskLogResultModel.Create(context.Background(), meta)
		},
		TaskLogID: oid,
	}

	logging := newResultLogger(hook)

	logging.Infoln("初始化任务中........")
	if isAsync {
		// 使用异步执行
		go doSyncTask(logging, oid, taskID, accountResp.AType, permission.GetUsername(ctx), resp)
	} else {
		// 使用同步执行
		doSyncTask(logging, oid, taskID, accountResp.AType, permission.GetUsername(ctx), resp)
	}
	return nil
}

func doSyncTask(logging *logrus.Logger, orderID, taskID, accountType, username string, resp *entity.SyncTask) {
	status := constant.TaskSuccess
	gCtx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
		"x-rpc-" + "username": username,
	}))

	var (
		err      error
		regionID string
	)

	defer func() {
		overResult := "任务执行成功"

		if err != nil {
			logging.Error(err.Error())
			status = constant.TaskErr
			overResult = "任务执行失败"
		}
		resp.Status = uint(status)
		resp.BindRegionSourceID = regionID
		go hooks.PubSyncTaskHandler(*resp)

		_ = flushSyncTaskLog(orderID, overResult)

		_ = models.SyncTaskModel.Update(gCtx, taskID, map[string]interface{}{
			"latest_run_status":   status,
			"latest_run_end_time": time.Now().Unix(),
			"update_user":         username,
		})

		_ = models.TaskLogModel.Update(gCtx, orderID, map[string]interface{}{
			"status":   status,
			"end_time": time.Now().Unix(),
		})
	}()

	regionResp, err := models.RegionModel.Get(gCtx, resp.BindRegionID)
	if err != nil {
		logging.Error("获取region信息失败: error", err.Error())
		return
	}
	regionID = regionResp.RegionID

	logging.Infoln("准备执行任务:", resp.Name)
	err = synctask.RunSyncTask(gCtx, logging, synctask.TaskInput{
		TaskName:   resp.Name,
		MethodName: resp.TaskType,
	}, synctask.SyncOption{
		IspID:      resp.AccountID,
		IspType:    accountType,
		RegionID:   regionResp.RegionID,
		RateLimit:  resp.Rate,
		RetryLimit: resp.Retry,
		Timeout:    resp.Timeout,
	})
	if err != nil {
		logging.Error("执行任务失败: error", err.Error())
		return
	}
}

// Update 更新任务
func (t Task) Update(ctx context.Context, req *cloudman.UpdateTaskReq) (*cloudman.Result, error) {
	_, err := models.SyncTaskModel.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	syncTaskMeta := &entity.SyncTask{
		Name:      req.Name,
		AccountID: req.AccountId,
		TaskType:  req.TaskType,
		Policy:    uint(req.Policy),
		Rate:      uint(req.Rate),
		Retry:     uint(req.Retry),
		Timeout:   uint(req.Timeout),
		Status:    uint(req.Status),
	}
	if req.BindZoneId != "" {
		zresp, err := models.RegionModel.Get(ctx, req.BindZoneId)
		if err != nil {
			return nil, err
		}
		syncTaskMeta.BindRegionID = req.BindZoneId
		syncTaskMeta.BindRegionName = zresp.Name
		syncTaskMeta.BindRegionSourceID = zresp.RegionID
	}

	syncTaskMeta.UpdateUser = permission.GetUsername(ctx)

	d, err := mapstruct.Struct2Map(syncTaskMeta)
	if err != nil {
		return nil, err
	}

	// int零值需要单独处理
	d["status"] = req.Status

	err = models.SyncTaskModel.Update(ctx, req.Id, d)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Delete 删除任务
func (t Task) Delete(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	_, err := models.SyncTaskModel.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	data := map[string]interface{}{
		"update_user":  permission.GetUsername(ctx),
		"is_delete":    1,
		"deleted_time": time.Now().Unix(),
	}

	err = models.SyncTaskModel.Update(ctx, id.Id, data)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, err
}

// flushSyncTaskLog 持久化执行结果,任务执行结束时调用
func flushSyncTaskLog(taskLogID string, result string) error {
	return models.TaskLogResultModel.Create(context.Background(), &entity.TaskLogResult{
		TaskLogID:  taskLogID,
		Stopped:    true,
		TaskResult: result,
	})
}

const taskURL = "/api/v1/task/%s/run"

// GetLocalTaskURL 获取callback url
func GetLocalTaskURL(taskID string) string {
	// return fmt.Sprintf("http://%s:%d%s", cfg.Conf.BindAddress, cfg.Conf.BindPort, fmt.Sprintf(taskURL, taskId))
	return ""
}
