package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/core"
	cloudmanCore "platgit.mihoyo.com/jql-ops/op-cloudman/core"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/ferry"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hotwheel"
	notifySdk "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/op-notifyman-sdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/querybuilder"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// Recycle 资源回收站
type Recycle struct {
}

// BatchRecover 批量恢复资源, 暂停实现，权限难控制，由前端串行操作
func (r Recycle) BatchRecover(ctx context.Context, ds *cloudman.ObjectIDs) (*cloudman.Result, error) {
	err := models.RecycleModel.AllTaskWaitRun(ctx, ds.List) // 批量判断状态
	if err != nil {
		return nil, err
	}

	return nil, err
}

// operateResourceBefore 资源释放后
func (r Recycle) operateResourceBefore() {

}

// releaseResourceAfter 资源释放后
func (r Recycle) releaseResourceAfter(order *entity.ResOrder, err error) {
	m := map[string]interface{}{
		"over_time": time.Now().Unix(),
		"status":    constant.TaskSuccess,
		"error_msg": "",
	}
	overResult := "任务执行成功"
	if err != nil {
		overResult = "任务执行失败"
		m["status"] = constant.TaskErr
		m["error_msg"] = err.Error()
	}
	err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), m)
	err = flushLog(order.ID.Hex(), overResult)
}

// RunPolicy 外部触发执行释放策略,离正式释放前12小时，删除成功后发消息提醒
func (r Recycle) RunPolicy(ctx context.Context, empty *cloudman.Empty) (*cloudman.Result, error) {
	resources := []string{"host"} // 当前仅支持主机
	for _, instanceType := range resources {
		result, err := models.RecycleModel.FindMany(ctx, map[string]interface{}{"instance_type": instanceType})
		if err != nil {
			return nil, err
		}
		instanceResult, err := core.RecycleCore.GetExpireResource(ctx, instanceType, result)
		if err != nil {
			return nil, err
		}

		if instanceType == "host" {
			// 根据云厂商_地域分组操作
			var groupName string
			ispGroup := make(map[string][]*entity.HostResource)
			for _, v := range instanceResult {
				r, ok := v.(*entity.HostResource)
				if !ok {
					return nil, nil
				}
				groupName = r.IspID + "_" + r.RegionID
				if _, ok := ispGroup[groupName]; ok {
					ispGroup[groupName] = append(ispGroup[groupName], r)
				} else {
					ispGroup[groupName] = []*entity.HostResource{r}
				}
			}

			for ispIDRegion, host := range ispGroup {
				flag := strings.Split(ispIDRegion, "_")
				ispID := flag[0]
				regionID := flag[1]

				var instanceID []string
				for _, v := range host {
					instanceID = append(instanceID, v.InstanceID)
				}
				err = destroyResource(ctx, ispID, instanceType, regionID, instanceID, "", false, nil)
				if err != nil {
					logger.Errorf("destroy resource error:", err.Error())
					return nil, err
				}
			}
		}
	}

	return &cloudman.Result{Message: "success"}, nil
}

func pbToRecyclePolicy(ctx context.Context, policy *cloudman.RecyclePolicy) (*entity.RecyclePolicy, error) {
	// 检查是否合法
	data := &entity.RecyclePolicy{
		InstanceType: policy.InstanceType,
		Name:         policy.Name,
		Enabled:      policy.Enable,
		PolicyInfo:   policy.PolicyInfo,
		ReleaseTime:  policy.ReleaseTime,
	}

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}

	filter, errKey, err := querybuilder.ParseRuleFromBytes([]byte(policy.PolicyInfo))
	if err != nil {
		return nil, err
	}

	if errKey != "" {
		return nil, fmt.Errorf(errKey)
	}

	_, errKey, err = filter.ToMgo()
	if errKey != "" {
		return nil, fmt.Errorf(errKey)
	}

	return data, err
}

// GetPolicyList 获取策略列表
func (r Recycle) GetPolicyList(ctx context.Context, req *cloudman.RecyclePolicyReq) (*cloudman.RecyclePolicyResp, error) {
	policy, total, err := models.RecyclePolicyModel.Query(ctx, &schema.RecyclePolicyQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		RecyclePolicyColumnParam: schema.RecyclePolicyColumnParam{
			InstanceType: req.InstanceType,
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Name,
		},
		OrderParams: schema.OrderParams{},
	})

	if err != nil {
		return nil, err
	}

	var list []*cloudman.RecyclePolicy
	for _, v := range policy {
		list = append(list, &cloudman.RecyclePolicy{
			Id:           v.ID.Hex(),
			Name:         v.Name,
			InstanceType: v.InstanceType,
			Enable:       v.Enabled,
			PolicyInfo:   v.PolicyInfo,
			ReleaseTime:  v.ReleaseTime,
		})
	}

	return &cloudman.RecyclePolicyResp{List: list, Total: int32(total)}, nil
}

// ChangePolicy 修改策略
func (r Recycle) ChangePolicy(ctx context.Context, policy *cloudman.RecyclePolicy) (*cloudman.Result, error) {
	data, err := pbToRecyclePolicy(ctx, policy)
	if err != nil {
		return nil, err
	}
	err = models.RecyclePolicyModel.UpdateOne(ctx, policy.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// DelPolicy 删除策略
func (r Recycle) DelPolicy(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	err := models.RecyclePolicyModel.Delete(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// List 获取资源回收资源列表
func (r Recycle) List(ctx context.Context, req *cloudman.RecycleQueryReq) (*cloudman.RecycleListResp, error) {
	list, total, err := models.RecycleModel.Query(ctx, &schema.RecycleQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		RecycleColumnParam: schema.RecycleColumnParam{
			InstanceName: req.InstanceName,
			InstanceID:   req.InstanceId,
			InstanceType: req.InstanceType,
			IspID:        req.IspId,
			RegionID:     req.RegionId,
			CreateUser:   req.CreateUser,
			Status:       req.Status,
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var li []*cloudman.RecycleShort
	for _, v := range list {
		li = append(li, &cloudman.RecycleShort{
			Id:             v.ID.Hex(),
			Name:           v.InstanceName,
			Type:           v.InstanceType,
			Isp:            v.IspID,
			Status:         v.Status,
			CreatedTime:    v.CreatedTime,
			DestroyTime:    v.DestroyTime,
			CreateUser:     v.CreateUser,
			UpdateUser:     v.UpdateUser,
			InstanceId:     v.InstanceID,
			InstanceType:   v.InstanceType,
			InnerIpAddress: v.InnerIPAddress,
		})
	}

	return &cloudman.RecycleListResp{Total: uint32(total), List: li}, err
}

// Info 获取资源回收站详情
func (r Recycle) Info(ctx context.Context, id *cloudman.ObjectID) (*cloudman.RecycleInfo, error) {
	return nil, nil
}

// Recover 恢复资源
func (r Recycle) Recover(ctx context.Context, req *cloudman.RecoverReq) (*cloudman.CommonResourceResp, error) {
	rec, err := models.RecycleModel.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if rec.Status != constant.TaskWaitRun {
		return nil, fmt.Errorf("当前状态不可恢复")
	}

	adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	checkTag := []models.CheckTag{}
	if rec.InstanceType == "host" {
		resourceObj, err := models.HostResourceModel.FindOneIncludeDeleted(adminCtx, map[string]interface{}{"InstanceID": rec.InstanceID})
		if err != nil {
			return nil, err
		}
		checkTag = models.EntityToCheckTags(resourceObj.Tags.Tag)
	} else if rec.InstanceType == "mysql" {
		resourceObj, err := models.MysqlClusterResourceModel.FindOneIncludeDeleted(adminCtx, map[string]interface{}{"DBClusterId": rec.InstanceID})
		if err != nil {
			return nil, err
		}
		checkTag = models.EntityToCheckTags(resourceObj.Tags.Tag)
	} else if rec.InstanceType == "redis" {
		resourceObj, err := models.RedisResourceModel.FindOneIncludeDeleted(adminCtx, map[string]interface{}{"InstanceID": rec.InstanceID})
		if err != nil {
			return nil, err
		}
		checkTag = models.RedisEntityToCheckTags(resourceObj.Tags.Tag)
	}
	if !models.CheckIspAndTagsPermFromCtx(ctx, rec.InstanceType, "admin", rec.IspID, checkTag) {
		return nil, errors.New("无移出回收站权限")
	}

	updateData := common.UpdateInstanceForm{Action: string(schema.ResourceRecover), Instances: []string{rec.InstanceID}}
	rawData, _ := json.Marshal(updateData)

	form := ResourceForm{
		Action:       schema.ResourceRecover,
		IspID:        rec.IspID,
		RegionPK:     "",
		InstanceType: rec.InstanceType,
		RegionID:     rec.RegionID,
		RawData:      string(rawData),
		Reason:       req.Reason,
	}
	pr, err := operateResourceBefore(ctx, form)
	if err != nil {
		return nil, err
	}

	order, err := createOrder(ctx, form, pr, "")
	if err != nil {
		return nil, err
	}

	// 根据规则创建修改审批单
	if cfg.GetSystemConfig().EnableWorkOrder && order.IspType != "custom" {
		data, err := orderDescribeToMD(order.RawData)
		if err != nil {
			logger.Errorf(err.Error())
		}
		token, err := cfg.GetIAMCliToken(cfg.GetSystemConfig().WorkOrderIAMEnv)
		if err != nil {
			logger.Errorf(err.Error())
			return nil, err
		}
		ticketID, err := ferry.NewFerryClient(cfg.GetSystemConfig().WorkOrderHost, token).
			CreateTicket(ctx, ferry.FormValues{
				FormTitle:   fmt.Sprintf("申请移出回收站-%s-%s", pr.IspName, order.GetOrderType()),
				FormFlowID:  cfg.GetSystemConfig().FlowID,
				FormCreator: permission.GetUsername(ctx),
				ResType:     order.GetOrderType(),
				Action:      order.GetAction(),
				IspName:     pr.IspName,
				FormReason:  req.Reason,
				OrderID:     order.ID.Hex(),
				Describe:    data, // 表单摘要
				Info:        fmt.Sprintf("%s/?order_id=%s", cfg.GetSystemConfig().OrderURL, order.ID.Hex()),
			})
		if err != nil {
			return nil, err
		}
		pr.Logger.Infof("该任务执行需审批等待审批结束后开始运行")
		_ = models.ResourceOrder.Update(ctx, order.ID.Hex(), map[string]interface{}{"ticket_id": ticketID})
		ferryDetailURL := fmt.Sprintf("%s/ferry/process/handle-ticket?workOrderId=%d&processId=%s",
			cfg.GetSystemConfig().WorkOrderWebHost, ticketID, cfg.GetSystemConfig().FlowID,
		)
		return &cloudman.CommonResourceResp{Message: "订单已提交,等待审批", OrderId: order.ID.Hex(), FerryUrl: ferryDetailURL}, err
	}

	defer func() {
		operateResourceAfter(order, err)
	}()

	// 避免循环引用，此处调用ResTemplate中的recoverResource方法
	_, err = ResTemplate{}.ProxyRecoverResource(ctx, order)
	if err != nil {
		return nil, err
	}

	return &cloudman.CommonResourceResp{Message: "success", OrderId: order.ID.Hex()}, nil
}

// destroyResource 统一销毁逻辑
func destroyResource(ctx context.Context, ispID, instanceType, regionID string, instanceID []string, createUser string, isForce bool, destroyOrder *entity.ResOrder) error {
	// 先查询一次资源状态
	recycleModels, err := models.RecycleModel.FindMany(ctx, map[string]interface{}{
		"instance_type": instanceType,
		"instance_id": map[string]interface{}{
			"$in": instanceID,
		},
	})
	if err != nil {
		return err
	}
	unfinishedInstanceIds := make([]string, 0)
	for _, m := range recycleModels {
		if m.Status != constant.TaskWaitRun {
			continue
		}
		unfinishedInstanceIds = append(unfinishedInstanceIds, m.InstanceID)
	}

	if len(unfinishedInstanceIds) == 0 {
		logger.Info("所有资源都已销毁,无需再次销毁")
		return nil
	}

	updateData := common.UpdateInstanceForm{Action: string(schema.ResourceDelete), Instances: unfinishedInstanceIds, IsForce: isForce}
	rawData, _ := json.Marshal(updateData)

	form := ResourceForm{
		Action:       schema.ResourceDelete,
		IspID:        ispID,
		RegionPK:     "",
		InstanceType: instanceType,
		RegionID:     regionID,
		RawData:      string(rawData),
		Reason:       "",
	}
	provide, err := operateResourceBefore(ctx, form)
	if err != nil {
		return err
	}

	if destroyOrder == nil {
		destroyOrder, err = createOrder(ctx, form, provide, createUser)
		if err != nil {
			return err
		}
	}

	_ = models.ResourceOrder.Update(context.Background(), destroyOrder.ID.Hex(), map[string]interface{}{
		"start_time": time.Now().Unix(),
		"status":     constant.TaskRunning,
	})

	// 标记资源为正在释放
	_ = models.RecycleModel.UpdateMany(ctx, map[string]interface{}{
		"instance_type": instanceType,
		"instance_id": map[string]interface{}{
			"$in": unfinishedInstanceIds,
		},
	}, map[string]interface{}{
		"status": constant.TaskRunning,
	})

	err = core.RecycleCore.RunRelease(ctx, instanceType, destroyOrder.IspType, destroyOrder.ID.Hex(), *provide, unfinishedInstanceIds, isForce)
	operateResourceAfter(destroyOrder, err)
	if err != nil {
		logger.Error(err.Error())
		// 标记资源为等待释放
		_ = models.RecycleModel.UpdateMany(ctx, map[string]interface{}{
			"instance_type": instanceType,
			"instance_id": map[string]interface{}{
				"$in": unfinishedInstanceIds,
			},
		}, map[string]interface{}{
			"status": constant.TaskWaitRun,
		})
		return err
	}

	// 将相关资源状态修改为已删除
	_ = models.RecycleModel.UpdateMany(ctx, map[string]interface{}{
		"instance_type": instanceType,
		"instance_id": map[string]interface{}{
			"$in": unfinishedInstanceIds,
		},
	}, map[string]interface{}{
		"destroy_time": time.Now().Unix(),
		"status":       constant.TaskSuccess,
		"update_user":  createUser,
	})
	mf, err := core.RecycleCore.GetModel(instanceType)
	if err != nil {
		return err
	}

	// 主机资源
	if instanceType == "host" {
		hostRecyclePipeline(ctx, regionID, unfinishedInstanceIds, provide)
	}

	go func() {
		baseCtx := context.WithValue(context.Background(), constant.HookCtxMeta, map[string]string{
			"isp_id":   ispID,
			"username": createUser,
		})
		hooks.PubDeleteHostResourceHandler(baseCtx, "destroy", unfinishedInstanceIds)
	}()
	return mf.SetRelease(ctx, unfinishedInstanceIds)
}

func hostRecyclePipeline(ctx context.Context, regionID string, instanceID []string, provider *common.InitProvider) {
	recycleCfg, err := models.RecycleCfgModel.GetByRegionID(ctx, regionID)
	if err != nil {
		provider.Logger.Warnf("未配置标准运维流水线，跳过执行回收流程")
		return
	}

	// instanceID实际只有一个值, 直接取第一个结果就行
	res, err := models.HostResourceModel.FindManyIncludeDeleted(ctx, map[string]interface{}{"InstanceID": map[string][]string{"$in": instanceID}})
	if err != nil || len(res) == 0 {
		provider.Logger.Errorf("未找到对应的主机")
		return
	}
	instance := res[0]
	// 取第一个jumpserver
	jumpserver := recycleCfg.Jumpservers[0]
	params := []*hotwheel.PipelineParam{
		{
			Key:   "instance_name",
			Value: instance.InstanceName,
		},
		{
			Key:   "instance_id",
			Value: instance.InstanceID,
		},
	}

	hotwheelClient := hotwheel.NewClient(recycleCfg.HotwheelOption.URI, recycleCfg.HotwheelOption.Token)
	taskID, err := hotwheelClient.StartPipeline(ctx, recycleCfg.HotwheelOption.PipelineID, fmt.Sprintf("回收实例-%s", instance.InstanceName), &jumpserver, params)
	if err != nil {
		provider.Logger.Errorf("执行回收流程失败, err: %v", err)
		return
	}
	provider.Logger.Infof("执行回收流程成功, 流程ID: %s", taskID)
}

// Destroy 立即销毁资源
func (r Recycle) Destroy(ctx context.Context, req *cloudman.RecycleReq) (*cloudman.Result, error) {
	rec, err := models.RecycleModel.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	systemCfg := cfg.GetSystemConfig()
	if systemCfg.EnableWorkOrder && rec.IspType != "custom" {
		return nil, errors.New("请通过工单系统触发销毁")
	}

	adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)

	checkTag := []models.CheckTag{}
	if rec.InstanceType == "host" {
		resourceObj, err := models.HostResourceModel.FindOneIncludeDeleted(adminCtx, map[string]interface{}{"InstanceID": rec.InstanceID})
		if err != nil {
			return nil, err
		}
		checkTag = models.EntityToCheckTags(resourceObj.Tags.Tag)
	} else if rec.InstanceType == "mysql" {
		resourceObj, err := models.MysqlClusterResourceModel.FindOneIncludeDeleted(adminCtx, map[string]interface{}{"DBClusterId": rec.InstanceID})
		if err != nil {
			return nil, err
		}
		checkTag = models.EntityToCheckTags(resourceObj.Tags.Tag)
	} else if rec.InstanceType == "redis" {
		resourceObj, err := models.RedisResourceModel.FindOneIncludeDeleted(adminCtx, map[string]interface{}{"InstanceID": rec.InstanceID})
		if err != nil {
			return nil, err
		}
		checkTag = models.RedisEntityToCheckTags(resourceObj.Tags.Tag)
	}
	if err != nil {
		return nil, err
	}
	if !models.CheckIspAndTagsPermFromCtx(ctx, rec.InstanceType, "admin", rec.IspID, checkTag) {
		return nil, errors.New("无销毁权限")
	}

	// 异步执行
	go func() {
		// taskBaseCtx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
		// 	"x-rpc-" + "username": permission.GetUsername(ctx),
		// }))
		taskBaseCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
		err = destroyResource(taskBaseCtx, rec.IspID, rec.InstanceType, rec.RegionID, []string{rec.InstanceID}, permission.GetUsername(ctx), req.IsForce, nil)
	}()

	// 释放通知
	go func() {
		sendErr := cloudmanCore.SendNotice(&notifySdk.QNotify{
			Name:    fmt.Sprintf("云管平台通知"),
			Message: fmt.Sprintf("以下实例已开始销毁，请至订单页检查进度：%v", []string{rec.InstanceID}),
		})
		if sendErr != nil {
			logger.Error(sendErr.Error())
		}
	}()

	return &cloudman.Result{Message: "success"}, nil
}

// CreatePolicy 创建回收策略
func (r Recycle) CreatePolicy(ctx context.Context, policy *cloudman.RecyclePolicy) (*cloudman.Result, error) {
	data, err := pbToRecyclePolicy(ctx, policy)
	if err != nil {
		return nil, err
	}
	data.UpdateUser = permission.GetUsername(ctx)

	err = models.RecyclePolicyModel.CreateOrUpdatePolicy(ctx, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// GetPolicy 获取回收策略
func (r Recycle) GetPolicy(ctx context.Context, oid *cloudman.ObjectID) (*cloudman.RecyclePolicy, error) {
	p, err := models.RecyclePolicyModel.Get(ctx, oid.Id)

	if err != nil {
		return nil, err
	}

	res := &cloudman.RecyclePolicy{
		Name:         p.Name,
		InstanceType: p.InstanceType,
		Enable:       p.Enabled,
		PolicyInfo:   p.PolicyInfo,
	}

	return res, nil
}
