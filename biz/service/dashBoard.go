package service

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/constants"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
)

// DashBoard 首页
type DashBoard struct{}

// GetAccount 获取账户
func (d DashBoard) GetAccount(ctx context.Context, request *cloudman.DashBoardAccountRequest) (*cloudman.DashBoardAccountResponse, error) {
	res, err := models.AccountModel.ToDashBoard(ctx)
	if err != nil {
		return nil, err
	}
	return &cloudman.DashBoardAccountResponse{List: res}, nil
}

// GetUserAuth 获取用户权限
func (d DashBoard) GetUserAuth(ctx context.Context, request *cloudman.DashBoardUserAuthRequest) (*cloudman.DashBoardUserAuthResponse, error) {
	if permission.IsAdminFromCtx(ctx) {
		return &cloudman.DashBoardUserAuthResponse{Auth: "admin"}, nil
	}
	return &cloudman.DashBoardUserAuthResponse{Auth: "user"}, nil
}

// GetIfWorkOrderUsed 获取工单是否开启
func (d DashBoard) GetIfWorkOrderUsed(ctx context.Context, request *cloudman.Empty) (*cloudman.DashboardIfWorkOrderUsedResponse, error) {
	sysCfg := cfg.GetSystemConfig()
	return &cloudman.DashboardIfWorkOrderUsedResponse{IsUsed: sysCfg.EnableWorkOrder}, nil
}

// GetBackendVersion ...
func (d DashBoard) GetBackendVersion(ctx context.Context, req *cloudman.Empty) (*cloudman.DashboardBackendVersionResponse, error) {
	return &cloudman.DashboardBackendVersionResponse{BuildVersion: constants.GetVersion(), BuildCommit: constants.GetGitCommit(), BuildDate: constants.GetBuildDate()}, nil
}

// GetHostBrief ...
func (d DashBoard) GetHostBrief(ctx context.Context, req *cloudman.DashboardHostBriefReq) (*cloudman.DashboardHostBriefResp, error) {
	resultSlice := []*cloudman.DashboardHostBrief{}
	for _, regionID := range req.RegionIds {
		m, err := models.HostResourceModel.GetBrief(ctx, req.AccountId, regionID)
		if err != nil {
			return nil, err
		}
		resultSlice = append(resultSlice, &cloudman.DashboardHostBrief{
			AccountId:        req.AccountId,
			RegionId:         regionID,
			HostTotal:        int32(m["total"]),
			HostRunning:      int32(m["running"]),
			HostAgentRunning: int32(m["agent_running"]),
			Host_7DayCreated: int32(m["7day_created"]),
			Host_7DayDeleted: int32(m["7day_deleted"]),
			Host_7DayExpire:  int32(m["7day_expire"]),
			HostNeedCleanup:  int32(m["need_cleanup"]),
			HostInRecycle:    int32(m["recycled"]),
		})
	}
	return &cloudman.DashboardHostBriefResp{List: resultSlice}, nil
}

// GetResBrief ...
func (d DashBoard) GetResBrief(ctx context.Context, req *cloudman.DashboardResBriefReq) (*cloudman.DashboardResBriefResp, error) {
	var resultSlice []*cloudman.DashboardResBrief
	var err error
	for _, regionID := range req.RegionIds {
		var m map[string]int64
		if req.ResType == "host" {
			m, err = models.HostResourceModel.GetBrief(ctx, req.AccountId, regionID)
		} else if req.ResType == "mysql" {
			m, err = models.MysqlClusterResourceModel.GetBrief(ctx, req.AccountId, regionID)
		} else if req.ResType == "redis" {
			m, err = models.RedisResourceModel.GetBrief(ctx, req.AccountId, regionID)
		}
		if err != nil {
			return nil, err
		}
		resultSlice = append(resultSlice, &cloudman.DashboardResBrief{
			AccountId:       req.AccountId,
			RegionId:        regionID,
			ResTotal:        int32(m["total"]),
			ResRunning:      int32(m["running"]),
			ResAgentRunning: int32(m["agent_running"]),
			Res_7DayCreated: int32(m["7day_created"]),
			Res_7DayDeleted: int32(m["7day_deleted"]),
			Res_7DayExpire:  int32(m["7day_expire"]),
			ResNeedCleanup:  int32(m["need_cleanup"]),
			ResInRecycle:    int32(m["recycled"]),
		})
	}
	return &cloudman.DashboardResBriefResp{List: resultSlice}, nil
}

// GetHostDailyChart -
func (d DashBoard) GetHostDailyChart(ctx context.Context, req *cloudman.DashboardHostDailyCountReq) (*cloudman.DashboardHostDailyCountResp, error) {
	m, err := models.HostResourceModel.GetDailyCount(ctx, req.AccountId, req.RegionId, req.DayBefore)
	if err != nil {
		return nil, err
	}
	resultSlice := []*cloudman.DashboardHostDailyCount{}
	for _, d := range m {
		resultSlice = append(resultSlice, &cloudman.DashboardHostDailyCount{
			DayName:      d.DayName,
			StateCount:   int32(d.State),
			CreatedCount: int32(d.Created),
			DeletedCount: int32(d.Deleted),
		})
	}
	return &cloudman.DashboardHostDailyCountResp{List: resultSlice}, nil
}

// GetResDailyChart -
func (d DashBoard) GetResDailyChart(ctx context.Context, req *cloudman.DashboardResDailyCountReq) (*cloudman.DashboardResDailyCountResp, error) {
	if req.DayBefore == 0 {
		m, err := models.ResSnapshotModel.GetDailyCount(ctx, req.AccountId, req.RegionId, req.ResType, req.Start, req.End)
		if err != nil {
			return nil, err
		}

		var resultSlice []*cloudman.DashboardResDailyCount
		for i := len(m) - 1; i >= 0; i-- {
			resultSlice = append(resultSlice, &cloudman.DashboardResDailyCount{
				DayName:      m[i].DayName,
				StateCount:   int32(m[i].State),
				CreatedCount: int32(m[i].Created),
				DeletedCount: int32(m[i].Deleted),
			})
		}
		return &cloudman.DashboardResDailyCountResp{List: resultSlice}, nil
	}

	var m []models.DailyCount
	var err error

	if req.ResType == "host" {
		m, err = models.HostResourceModel.GetDailyCount(ctx, req.AccountId, req.RegionId, req.DayBefore)
	} else if req.ResType == "mysql" {
		m, err = models.MysqlClusterResourceModel.GetDailyCount(ctx, req.AccountId, req.RegionId, req.DayBefore)
	} else if req.ResType == "redis" {
		m, err = models.RedisResourceModel.GetDailyCount(ctx, req.AccountId, req.RegionId, req.DayBefore)
	} else if req.ResType == "all" {
		hc, mc, rc, err := getAllResDailyCount(ctx, req.AccountId, req.RegionId, req.DayBefore)
		if err != nil {
			return nil, err
		}
		m = models.AddDailyCount(hc, mc, rc)
	} else {
		return nil, errors.New("unsupported resType")
	}
	if err != nil {
		return nil, err
	}

	var resultSlice []*cloudman.DashboardResDailyCount
	for _, d := range m {
		resultSlice = append(resultSlice, &cloudman.DashboardResDailyCount{
			DayName:      d.DayName,
			StateCount:   int32(d.State),
			CreatedCount: int32(d.Created),
			DeletedCount: int32(d.Deleted),
		})
	}
	return &cloudman.DashboardResDailyCountResp{List: resultSlice}, nil
}

func getAllResDailyCount(ctx context.Context, accountID, regionID string, dayBefore int32) (hc, mc, rc []models.DailyCount, err error) {
	hc, err = models.HostResourceModel.GetDailyCount(ctx, accountID, regionID, dayBefore)
	if err != nil {
		return nil, nil, nil, err
	}
	mc, err = models.MysqlClusterResourceModel.GetDailyCount(ctx, accountID, regionID, dayBefore)
	if err != nil {
		return nil, nil, nil, err
	}
	rc, err = models.RedisResourceModel.GetDailyCount(ctx, accountID, regionID, dayBefore)
	if err != nil {
		return nil, nil, nil, err
	}
	return
}

// AutoFillSnapshot ...
func (d DashBoard) AutoFillSnapshot(ctx context.Context, req *cloudman.AutoFillSnapshotReq) (*cloudman.Result, error) {
	res, err := models.AccountModel.ToDashBoard(ctx)
	if err != nil {
		return nil, err
	}
	var regions []*cloudman.DashBoardAccountRegion
	var accountDetail *cloudman.DashBoardAccountDetail
	for _, account := range res {
		if account.AccountId == req.AccountId {
			regions = account.Regions
			accountDetail = account
			break
		}
	}

	logger.Infof("account: %s, day before: %d", accountDetail.AccountName, req.DayBefore)
	for _, region := range regions {
		hc, mc, rc, err := getAllResDailyCount(ctx, req.AccountId, region.RegionId, req.DayBefore)
		if err != nil {
			return nil, err
		}
		dailyCounts := [][]models.DailyCount{hc, mc, rc}
		resTypes := []string{"host", "mysql", "redis"}
		for k, dailyCount := range dailyCounts {
			for _, d := range dailyCount {
				t, err := time.Parse("2006-01-02", d.DayName)
				if err != nil {
					return nil, err
				}
				if err := models.ResSnapshotModel.FindOrCreate(ctx, &entity.ResSnapshot{
					AccountID:  req.AccountId,
					ResType:    resTypes[k],
					Region:     region.RegionId,
					DayTime:    t.Unix(),
					TotalCount: d.State,
				}); err != nil {
					return nil, err
				}
			}
		}
	}

	return &cloudman.Result{Message: "success"}, nil
}

// FillSnapshot ...
func (d DashBoard) FillSnapshot(ctx context.Context, req *cloudman.FillSnapshotReq) (*cloudman.FillSnapshotRes, error) {
	var res []*cloudman.Snapshot
	for _, data := range req.Data {
		err := insertSnapshot(ctx, data)
		if err != nil {
			logger.Errorf("fail to insert snapshot: %s, error: %s", data, err.Error())
			res = append(res, data)
		}
	}
	return &cloudman.FillSnapshotRes{Failures: res}, nil
}

func insertSnapshot(ctx context.Context, req *cloudman.Snapshot) error {
	var t time.Time
	var err error
	t, err = time.Parse("2006-01-02", req.DayName)
	if err != nil {
		return err
	}
	if req.ResType != "host" && req.ResType != "mysql" && req.ResType != "redis" {
		return errors.New("unsupported resType")
	}
	searchFilter := models.DefaultFilter(ctx,
		bson.E{Key: "account_id", Value: req.AccountId},
		bson.E{Key: "res_type", Value: req.ResType},
		bson.E{Key: "region", Value: req.RegionId},
		bson.E{Key: "day_time", Value: t.Unix()})
	if req.Action == "create" {
		err = models.ResSnapshotModel.FindOrCreate(ctx, &entity.ResSnapshot{
			AccountID:  req.AccountId,
			ResType:    req.ResType,
			Region:     req.RegionId,
			DayTime:    t.Unix(),
			TotalCount: int(req.TotalCount),
		})
	} else if req.Action == "update" {
		err = models.ResSnapshotModel.CreateOrUpdateDailyStatus(ctx, searchFilter,
			&entity.ResSnapshot{
				AccountID:  req.AccountId,
				ResType:    req.ResType,
				Region:     req.RegionId,
				DayTime:    t.Unix(),
				TotalCount: int(req.TotalCount),
			})
	} else if req.Action == "delete" {
		err = models.ResSnapshotModel.Delete(ctx, searchFilter)
	} else {
		return errors.New("unsupported action")
	}
	if err != nil {
		return err
	}
	return nil
}
