package service

import (
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// LoadBalancer 安全组对象
type LoadBalancer struct {
}

// Describe ...
func (h LoadBalancer) Describe(ctx context.Context, req *cloudman.DescribeLoadBalancerReq) (*cloudman.DescribeLoadBalancerRes, error) {
	resp, total, err := models.LoadBalancerModel.Query(ctx, &schema.LoadBalancerQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		LoadBalancerColumnParam: schema.LoadBalancerColumnParam{
			RegionID:    req.RegionId,
			IspID:       req.Isp,
			Address:     req.Address,
			LbType:      req.LbType,
			NeedCleanup: req.NeedCleanup,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.LoadBalancerModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeLoadBalancerRes{List: list, Total: int32(total)}, err
}

// Describe 获取负载均衡资源列表
func (h LoadBalancer) DescribeDetail(ctx context.Context, req *cloudman.ObjIDReq) (*cloudman.DescribeLoadBalancerDetailRes, error) {
	lb, err := models.LoadBalancerModel.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	detail, err := models.LoadBalancerModelToPbDetail(ctx, lb)
	if err != nil {
		return nil, err
	}

	return detail, nil
}

func (h LoadBalancer) DescribeServerGroup(ctx context.Context, req *cloudman.IDReq) (*cloudman.DescribeLoadBalancerServerGroupRes, error) {
	serverGroup, err := models.LBServerGroupModel.GetByServerGroupID(ctx, req.Id)

	if err != nil {
		return nil, err
	}

	detail := models.LBServerGroupModelToPB(serverGroup)
	return detail, nil
}

func (h LoadBalancer) DescribeZones(ctx context.Context, req *cloudman.DescribeZonesReq) (*cloudman.DescribeZonesRes, error) {
	client, err := getCloudLB(ctx, req.Isp, req.RegionId)
	if err != nil {
		return nil, err
	}

	return client.DescribeZones(ctx)
}

// func (h LoadBalancer) ListACLs(ctx context.Context, req *cloudman.ListACLsReq) (*cloudman.ListAClsRes, error) {
// 	client, err := getCloudLB(ctx, req.Isp, req.RegionId)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return nil, nil
// }

func (h LoadBalancer) ListCertificates(ctx context.Context, req *cloudman.ListCertificatesReq) (*cloudman.ListCertificatesRes, error) {
	client, err := getCloudLB(ctx, req.Isp, req.RegionId)
	if err != nil {
		return nil, err
	}
	return client.ListCertificates(ctx)
}

func (h LoadBalancer) CleanupLoadBalancer(ctx context.Context, req *cloudman.LoadBalancerIDsReq) (*cloudman.Result, error) {
	err := models.LoadBalancerModel.Cleanup(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{
		Message: "success",
	}, nil
}
