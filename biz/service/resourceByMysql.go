package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"runtime/debug"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/sirupsen/logrus"
	"github.com/tealeg/xlsx"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/rds"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	provider_common "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// MysqlRes 主机对象
type MysqlRes struct {
}

// DatabaseEntityToPB database-entity-to-pb
func DatabaseEntityToPB(v entity.MysqlDatabaseResource) *cloudman.MysqlDatabase {
	var tag []*cloudman.ResourceTag
	var account []*cloudman.MysqlAccount
	for _, t := range v.DatabaseTags.Tag {
		tag = append(tag, &cloudman.ResourceTag{Key: t.TagKey, Value: t.TagValue})
	}
	for _, ac := range v.Accounts.Account {
		account = append(account, &cloudman.MysqlAccount{
			PrivilegeStatus:  ac.PrivilegeStatus,
			AccountStatus:    ac.AccountStatus,
			AccountPrivilege: ac.AccountPrivilege,
			AccountName:      ac.AccountName,
		})
	}

	return &cloudman.MysqlDatabase{
		Id:               v.ID.Hex(),
		Tags:             tag,
		Charactersetname: v.CharacterSetName,
		ClusterId:        v.ClusterID,
		Dbname:           v.DBName,
		Dbstatus:         v.DBStatus,
		Dbdescription:    v.DBDescription,
		IsLock:           v.IsLock,
		Accounts:         account,
	}
}

// GetSecurityGroup 获取安全组列表
func (h MysqlRes) GetSecurityGroup(ctx context.Context, req *cloudman.SecurityGroupReq) (*cloudman.SecurityGroupResp, error) {
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeSecurityGroups(ctx, &common.DescribeSecurityGroupInput{
		VpcID: req.VpcId,
		Size:  req.Size,
	})
}

// DescribeIPWhiteList 获取ip白名单模版列表
func (h MysqlRes) DescribeIPWhiteList(ctx context.Context, req *cloudman.IPWhiteListReq) (*cloudman.IPWhiteListResp, error) {
	client, err := getCloudRDS(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}
	return client.DescribeIPWhiteList(ctx, &common.DescribeNetworksInput{})
}

// GetNetwork 获取VPC网络
func (h MysqlRes) GetNetwork(ctx context.Context, req *cloudman.NetworkReq) (*cloudman.NetworkResp, error) {
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeNetworks(ctx, &common.DescribeNetworksInput{})
}

// GetVSwitch 获取交换机子网
func (h MysqlRes) GetVSwitch(ctx context.Context, req *cloudman.VSwitchReq) (*cloudman.VSwitchResp, error) {
	client, err := getCloudRDS(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeSubnets(ctx, &common.DescribeSubnetsInput{VpcID: req.VpcId, ZoneID: req.ZoneId})
}

// GetDBTypes 获取DB类型与版本信息
func (h MysqlRes) GetDBTypes(ctx context.Context, req *cloudman.DBTypeReq) (*cloudman.DBTypeResp, error) {
	client, err := getCloudRDS(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}
	return client.GetDBTypes(ctx, &rds.AvailableDBTypesInput{
		ZoneID: req.ZoneId,
		Engine: req.Engine,
	})
}

// DescribeAvailableZone 获取指定地域下可用区列表
func (h MysqlRes) DescribeAvailableZone(ctx context.Context, req *cloudman.DBZoneReq) (*cloudman.DBZoneResp, error) {
	client, err := getCloudRDS(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.ListAvailableZone(ctx, &rds.AvailableZoneInput{})
}

// DescribeDBClasses 根据类型rds及polarDB与对应的版本获取对应支持的规格列表
func (h MysqlRes) DescribeDBClasses(ctx context.Context, req *cloudman.DBClassesReq) (*cloudman.DBClassesResp, error) {
	client, err := getCloudRDS(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}
	return client.GetAvailableClasses(ctx, &rds.AvailableClassesInput{
		Engine:        req.DBType,
		EngineVersion: req.DBVersion,
	})
}

func manyEntityToPbMysql(ctx context.Context, data []*entity.MysqlClusterResource) (resp []*cloudman.MysqlClusterDetail, err error) {
	var ids []string
	for _, v := range data {
		isp := v.IspID
		if isp != "" {
			ids = append(ids, isp)
		}
	}

	var ac map[string]entity.Account
	if len(ids) != 0 {
		ac, err = models.AccountModel.FindManyWithPkToMap(ctx, ids)
		if err != nil {
			return nil, err
		}
	}

	for _, v := range data {
		var tags []*cloudman.ResourceTag
		for _, tag := range v.Tags.Tag {
			tags = append(tags, &cloudman.ResourceTag{Key: tag.TagKey, Value: tag.TagValue})
		}
		detail := &cloudman.MysqlClusterDetail{
			Id:                   v.ID.Hex(),
			IsLock:               v.IsLock,
			IspId:                v.IspID,
			IspName:              ac[v.IspID].Name,
			IspType:              ac[v.IspID].AType,
			VpcId:                v.VpcID,
			ExpireTime:           v.ExpireTime,
			Expired:              v.Expired,
			DBNodeNumber:         v.DBNodeNumber,
			CreateTime:           v.CreateTime,
			Status:               v.TransStatus(),
			DBNodeClass:          v.DBNodeClass,
			Tags:                 tags,
			DBType:               v.DBType,
			LockMode:             v.LockMode,
			RegionId:             v.RegionID,
			DBVersion:            v.DBVersion,
			DBClusterId:          v.DBClusterID,
			DBClusterStatus:      v.TransStatus(),
			ResourceGroupId:      v.ResourceGroupID,
			StorageUsed:          v.StorageUsed,
			DBClusterNetworkType: v.DBClusterNetworkType,
			DBClusterDescription: v.DBClusterDescription,
			ZoneId:               v.ZoneID,
			Engine:               v.Engine,
			PayType:              v.PayType,
		}

		resp = append(resp, detail)
	}

	return
}

// GetEndpoints 查询PolarDB集群的地址信息
func (h MysqlRes) GetEndpoints(ctx context.Context, id *cloudman.ObjectID) (*cloudman.MysqlEndpoints, error) {
	resp, err := models.MysqlClusterEndpointResourceModel.FindMany(ctx, map[string]interface{}{
		"DBClusterID": id.Id,
	})

	logger.Debugf("DBClusterID:", id.Id, ", Resp:", len(resp))

	if err != nil {
		return nil, err
	}

	var list []*cloudman.MysqlEndpoint
	str, _ := json.Marshal(resp)
	err = json.Unmarshal(str, &list)
	if err != nil {
		return nil, err
	}

	return &cloudman.MysqlEndpoints{List: list}, nil
}

// ClusterInfo 获取集群详情
func (h MysqlRes) ClusterInfo(ctx context.Context, cid *cloudman.ObjectID) (*cloudman.MysqlClusterDetail, error) {
	resp, err := models.MysqlClusterResourceModel.FindOne(ctx, map[string]interface{}{
		"DBClusterId": cid.Id,
	})
	if err == mongo.ErrNoDocuments {
		return nil, errors.New("没有查看此资源的权限")
	} else if err != nil {
		return nil, err
	}

	var tags []*cloudman.ResourceTag
	for _, v := range resp.Tags.Tag {
		tags = append(tags, &cloudman.ResourceTag{
			Key:   v.TagKey,
			Value: v.TagValue,
		})
	}

	ac, err := models.AccountModel.FindPK(ctx, resp.IspID)
	if err != nil {
		return nil, err
	}

	return &cloudman.MysqlClusterDetail{
		Id:                   resp.ID.Hex(),
		IsLock:               resp.IsLock,
		IspId:                resp.IspID,
		IspName:              ac.Name,
		IspType:              resp.IspType,
		VpcId:                resp.VpcID,
		ExpireTime:           resp.ExpireTime,
		Expired:              resp.Expired,
		DBNodeNumber:         resp.DBNodeNumber,
		CreateTime:           resp.CreateTime,
		Status:               resp.TransStatus(),
		DBNodeClass:          resp.DBNodeClass,
		Tags:                 tags,
		DBType:               resp.DBType,
		LockMode:             resp.LockMode,
		RegionId:             resp.RegionID,
		DBVersion:            resp.DBVersion,
		DBClusterId:          resp.DBClusterID,
		DBClusterStatus:      resp.TransStatus(),
		ResourceGroupId:      resp.ResourceGroupID,
		StorageUsed:          resp.StorageUsed,
		DBClusterNetworkType: resp.DBClusterNetworkType,
		DBClusterDescription: resp.DBClusterDescription,
		ZoneId:               resp.ZoneID,
		Engine:               resp.Engine,
		PayType:              resp.PayType,
	}, nil
}

// GetDatabases 获取database列表
func (h MysqlRes) GetDatabases(ctx context.Context, cid *cloudman.ObjectID) (*cloudman.MysqlDatabases, error) {
	cids := strings.Split(cid.Id, ",")
	list, err := models.MysqlDatabaseResourceModel.FindMany(ctx, map[string]interface{}{"cluster_id": map[string]interface{}{"$in": cids}})
	if err != nil {
		return nil, err
	}

	var li []*cloudman.MysqlDatabase
	for _, v := range list {
		var tag []*cloudman.ResourceTag
		var account []*cloudman.MysqlAccount
		for _, t := range v.DatabaseTags.Tag {
			tag = append(tag, &cloudman.ResourceTag{Key: t.TagKey, Value: t.TagValue})
		}
		for _, ac := range v.Accounts.Account {
			account = append(account, &cloudman.MysqlAccount{
				PrivilegeStatus:  ac.PrivilegeStatus,
				AccountStatus:    ac.AccountStatus,
				AccountPrivilege: ac.AccountPrivilege,
				AccountName:      ac.AccountName,
			})
		}
		mdbPb := &cloudman.MysqlDatabase{
			Id:               v.ID.Hex(),
			Tags:             tag,
			Charactersetname: v.CharacterSetName,
			ClusterId:        v.ClusterID,
			Dbname:           v.DBName,
			Dbstatus:         v.DBStatus,
			Dbdescription:    v.DBDescription,
			IsLock:           v.IsLock,
			Accounts:         account,
		}
		if v.BkCmdb != nil {
			mdbPb.CmdbInfo = &cloudman.CmdbInfo{
				InstId:     v.BkCmdb.InstID,
				GameRegion: v.BkCmdb.GameRegionName,
				Process:    v.BkCmdb.ProcessName,
			}
		}
		li = append(li, mdbPb)
	}

	return &cloudman.MysqlDatabases{List: li}, nil
}

// GetAccounts 获取account列表
func (h MysqlRes) GetAccounts(ctx context.Context, cid *cloudman.ObjectID) (*cloudman.MysqlAccountsInfo, error) {
	list, err := models.MysqlDatabaseAccountResourceModel.FindMany(ctx, map[string]interface{}{"cluster_id": cid.Id})
	if err != nil {
		return nil, err
	}

	var account []*cloudman.MysqlAccountInfo
	for _, v := range list {
		var privilege []*cloudman.MysqlAccountsPrivileges
		for _, p := range v.DatabasePrivileges {
			privilege = append(privilege, &cloudman.MysqlAccountsPrivileges{
				DBName:           p.DBName,
				AccountPrivilege: p.AccountPrivilege,
			})
		}

		account = append(account, &cloudman.MysqlAccountInfo{
			Id:                       v.ID.Hex(),
			IsLock:                   v.IsLock,
			ClusterId:                v.ClusterID,
			AccountStatus:            v.AccountStatus,
			DatabasePrivileges:       privilege,
			AccountDescription:       v.AccountDescription,
			AccountPasswordValidTime: v.AccountPasswordValidTime,
			AccountType:              v.AccountType,
			AccountLockState:         v.AccountLockState,
			AccountName:              v.AccountName,
		})
	}

	return &cloudman.MysqlAccountsInfo{List: account}, nil
}

// GetWhitelists 获取白名单列表
func (h MysqlRes) GetWhitelists(ctx context.Context, cid *cloudman.ObjectID) (*cloudman.WhitelistInfo, error) {
	cluster, err := models.MysqlClusterResourceModel.FindOne(ctx, map[string]interface{}{"DBClusterId": cid.Id})
	if err != nil {
		return nil, err
	}

	var ipGroups []*cloudman.InstanceIPGroup
	for _, ipg := range cluster.IPGroups {
		ipGroups = append(ipGroups, &cloudman.InstanceIPGroup{
			SecurityIpGroupAttribute: ipg.SecurityIPGroupAttribute,
			SecurityIpGroupName:      ipg.SecurityIPGroupName,
			SecurityIpList:           ipg.SecurityIPList,
		})
	}

	return &cloudman.WhitelistInfo{
		ClusterId:        cluster.DBClusterID,
		SecurityGroupIds: &cloudman.InstanceSecurityGroupIds{SecurityGroupID: cluster.SecurityGroupIds.SecurityGroupID},
		IpGroups:         ipGroups,
	}, nil
}

// UnlockResource 解锁资源对象
func (h MysqlRes) UnlockResource(ctx context.Context, ids *cloudman.Ids) (*cloudman.Result, error) {
	err := models.MysqlClusterResourceModel.UpdateLockStatus(ctx, ids.Ids, false)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// ImportXlsx 只允许导入自定义厂商对象
func (h MysqlRes) ImportXlsx(ctx context.Context, req *cloudman.ImportXlsxReq) (*cloudman.BatchResult, error) {
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	isp, err := models.AccountModel.Get(ctx, req.IspId)
	if err != nil {
		return nil, err
	}

	file, err = xlsx.OpenBinary(req.GetFile())
	if err != nil {
		return nil, err
	}

	st := models.MysqlClusterResourceModel.FiledOptions(ctx)
	mysqlMetaMap := make(map[string]*entity.FiledStruct)
	for _, v := range st {
		if v.Extra {
			continue
		}
		mysqlMetaMap[v.Name] = &entity.FiledStruct{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
			Extra:    v.Extra,
		}
	}

	sheet = file.Sheets[0]
	// step1 读取标题
	// step2 标题与Value对应
	row = sheet.Row(0)
	var rowList []*entity.FiledStruct
	for _, cell = range row.Cells {
		if k, ok := mysqlMetaMap[cell.Value]; ok {
			rowList = append(rowList, k)
		} else {
			rowList = append(rowList, nil)
		}
	}

	var mysqlList []*entity.MysqlClusterResource
	for i := 1; i < len(sheet.Rows); i++ {
		row = sheet.Rows[i]
		m := make(map[string]interface{})
		for index, cellStr := range rowList {
			if cellStr == nil {
				continue
			}
			cell = row.Cells[index]
			value := row.Cells[index].Value
			switch cellStr.Type {
			case "float32":
				if value != "" {
					val, err := cell.Float()
					if err != nil {
						return nil, err
					}
					m[cellStr.Value] = val
				}
				m[cellStr.Value] = 0
				break
			case "int64":
				if value != "" {
					val, err := cell.Int64()
					if err != nil {
						return nil, err
					}
					m[cellStr.Value] = val
				} else {
					m[cellStr.Value] = 0
				}
				break
			case "int32":
				if value != "" {
					val, err := cell.Int()
					if err != nil {
						return nil, err
					}
					m[cellStr.Value] = val
				} else {
					m[cellStr.Value] = 0
				}
				break
			case "bool":
				m[cellStr.Value] = cell.Bool()
				break
			case "struct":
				if value == "" {
					m[cellStr.Value] = nil
					break
				}
				var rawMap map[string]interface{}
				err = json.Unmarshal([]byte(value), &rawMap)
				if err != nil {
					return nil, err
				}

				m[cellStr.Value] = rawMap
				break
			case "slice":
				if value == "" {
					m[cellStr.Value] = nil
					break
				}
				var list []interface{}
				err = json.Unmarshal([]byte(value), &list)
				if err != nil {
					return nil, err
				}

				m[cellStr.Value] = list
				break
			default:
				m[cellStr.Value] = value
			}
		}
		var mysql *entity.MysqlClusterResource
		err = mapstructure.Decode(m, &mysql)
		if err != nil {
			return nil, err
		}

		mysql.IspID = isp.ID.Hex() // 前端传入
		mysql.IspType = "custom"   // 只允许自定义厂商
		mysql.RegionID = "cn-shanghai"
		mysql.DBClusterStatus = "Running"
		mysql.DBClusterID = fmt.Sprintf("%s_%s", mysql.DBClusterID, mysql.IspID)

		mysqlList = append(mysqlList, mysql)
	}

	result, err := models.MysqlClusterResourceModel.BatchCreateOrUpdate(ctx, mysqlList)
	if err != nil {
		return nil, err
	}

	return &cloudman.BatchResult{
		Errors:         result.Errors,
		Success:        result.Success,
		SuccessCreated: result.SuccessCreated,
		SuccessUpdated: result.SuccessUpdated,
		UpdateErrors:   result.UpdateErrors,
	}, nil
}

// Option 获取描述信息
func (h MysqlRes) Option(ctx context.Context, empty *cloudman.Empty) (*cloudman.OptionResp, error) {
	st := models.MysqlClusterResourceModel.FiledOptions(ctx)
	var li []*cloudman.FieldOption

	for _, v := range st {
		li = append(li, &cloudman.FieldOption{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
			Indexed:  v.Indexed,
			Extra:    v.Extra,
		})
	}

	return &cloudman.OptionResp{List: li}, nil
}

// LockResource 锁定资源
func (h MysqlRes) LockResource(ctx context.Context, ids *cloudman.Ids) (*cloudman.Result, error) {
	err := models.MysqlClusterResourceModel.UpdateLockStatus(ctx, ids.Ids, true)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// DescribeCluster 获取Mysql类资源列表
func (h MysqlRes) DescribeCluster(ctx context.Context, req *cloudman.MysqlClusterReq) (*cloudman.MysqlClusterResp, error) {
	resp, total, err := models.MysqlClusterResourceModel.Query(ctx, &schema.MysqlClusterResourceQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		MysqlClusterResourceColumnParam: schema.MysqlClusterResourceColumnParam{
			IspID:              req.Isp,
			IspType:            req.Type,
			RegionID:           req.RegionId,
			TagKey:             req.TagKey,
			TagValues:          req.TagValues,
			DBClusterStatus:    req.DBClusterStatus,
			DisplayRecyclable:  req.DisplayRecyclable,
			DisplayNeedCleanup: req.DisplayNeedCleanup,
			UpdateTime: schema.UpdateTimeColumnParam{
				Ref: req.UpdateTimeRef,
				Val: req.UpdateTimeVal,
			},
			SecurityGroupIds:          req.SecurityGroupIds,
			AndSearchSecurityGroupIds: req.AndSearchSecurityGroupIds,
		},
		SearchParams: schema.SearchParams{},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{},
	})

	if err != nil {
		return nil, err
	}

	var list []*cloudman.MysqlClusterDetail

	var ids []string
	for _, v := range resp {
		isp := v.IspID
		if isp != "" {
			ids = append(ids, isp)
		}
	}

	var ac map[string]entity.Account
	if len(ids) != 0 {
		ac, err = models.AccountModel.FindManyWithPkToMap(ctx, ids)
		if err != nil {
			return nil, err
		}
	}

	// 查询securityGroup信息
	sgIDMap := map[string]bool{}
	for _, v := range resp {
		for _, sgID := range v.SecurityGroupIds.SecurityGroupID {
			sgIDMap[sgID] = true
		}
	}
	var securityGroupIDs []string
	for sgID := range sgIDMap {
		securityGroupIDs = append(securityGroupIDs, sgID)
	}
	var sgMap map[string]entity.SecurityGroup
	if len(securityGroupIDs) != 0 {
		sgMap, err = models.SecurityGroupModel.FindManyWithIDToMap(ctx, securityGroupIDs)
		if err != nil {
			return nil, err
		}
	}

	for _, v := range resp {
		var tags []*cloudman.ResourceTag
		for _, tag := range v.Tags.Tag {
			tags = append(tags, &cloudman.ResourceTag{Key: tag.TagKey, Value: tag.TagValue})
		}
		var sgInfos []*cloudman.SecurityGroupInfo
		for _, sgID := range v.SecurityGroupIds.SecurityGroupID {
			sg := sgMap[sgID]
			sgInfos = append(sgInfos, &cloudman.SecurityGroupInfo{
				SecurityGroupId:   sgID,
				SecurityGroupName: sg.SecurityGroupName,
				Description:       sg.Description,
				VpcId:             sg.VpcID,
			})
		}

		detail := &cloudman.MysqlClusterDetail{
			Id:                   v.ID.Hex(),
			IsLock:               v.IsLock,
			IspId:                v.IspID,
			IspName:              ac[v.IspID].Name,
			IspType:              ac[v.IspID].AType,
			VpcId:                v.VpcID,
			ExpireTime:           v.ExpireTime,
			Expired:              v.Expired,
			DBNodeNumber:         v.DBNodeNumber,
			CreateTime:           v.CreateTime,
			Status:               v.TransStatus(),
			DBNodeClass:          v.DBNodeClass,
			Tags:                 tags,
			DBType:               v.DBType,
			LockMode:             v.LockMode,
			RegionId:             v.RegionID,
			DBVersion:            v.DBVersion,
			DBClusterId:          v.DBClusterID,
			DBClusterStatus:      v.TransStatus(),
			ResourceGroupId:      v.ResourceGroupID,
			StorageUsed:          v.StorageUsed,
			DBClusterNetworkType: v.DBClusterNetworkType,
			DBClusterDescription: v.DBClusterDescription,
			ZoneId:               v.ZoneID,
			Engine:               v.Engine,
			PayType:              v.PayType,
			UpdatedTime:          v.UpdatedTime,
			Recyclable:           v.Recyclable,
			NeedCleanup:          v.NeedCleanup,
			SecurityGroupInfos:   sgInfos,
		}

		list = append(list, detail)
	}

	return &cloudman.MysqlClusterResp{List: list, Total: int32(total)}, err
}

// ExportXlsx 导出xlsx
func (h MysqlRes) ExportXlsx(ctx context.Context, req *cloudman.ExportXlsxReq) (*cloudman.ExportXlsxResp, string, error) {
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	st := models.MysqlClusterResourceModel.FiledOptions(ctx)
	file = xlsx.NewFile()
	sheet, err = file.AddSheet("Mysql实例列表")
	if err != nil {
		return nil, "", err
	}
	// 加入标题(根据自定义导出列返回)

	selectFieldMap := map[string]struct{}{}
	for _, v := range req.ExportFields {
		selectFieldMap[v] = struct{}{}
	}

	var rowList []*entity.FiledStruct
	row = sheet.AddRow()
	for _, v := range st {
		if v.Extra {
			continue
		}
		if len(req.ExportFields) != 0 {
			if _, ok := selectFieldMap[v.Value]; !ok {
				continue
			}
		}

		cell = row.AddCell()
		cell.Value = v.Name
		if v.Required {
			cell.SetStyle(&xlsx.Style{
				Font: xlsx.Font{
					Bold: true,
				},
			})
		}

		rowList = append(rowList, &entity.FiledStruct{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
		})
	}

	var res []*entity.MysqlClusterResource
	if req.ExportType == "selectWithCond" {
		res, _, err = models.MysqlClusterResourceModel.Query(ctx, &schema.MysqlClusterResourceQueryParams{
			PaginationParam: schema.PaginationParam{
				Size: 9999,
			},
			MysqlClusterResourceColumnParam: schema.MysqlClusterResourceColumnParam{
				IspID:    req.Isp,
				RegionID: req.RegionId,
			},
		})
	}

	if req.ExportType == "" {
		// 导出全部
		res, err = models.MysqlClusterResourceModel.FindMany(ctx, map[string]interface{}{})
	}

	if req.ExportType == "select" && len(req.Selected) != 0 {
		// 导出范围: 导出选中并且选中的内容为空则直接返回
		var selected []string
		for _, v := range req.Selected {
			selected = append(selected, v.Id)
		}
		res, err = models.MysqlClusterResourceModel.FindWithManyPK(ctx, selected)
	}

	if err != nil {
		return nil, "", err
	}

	for _, v := range res {
		row = sheet.AddRow()
		strMap, err := mapstruct.Struct2Map(v)
		if err != nil {
			break
		}

		for _, field := range rowList {

			cell = row.AddCell()
			if strMap[field.Value] == nil {
				cell.Value = ""
				continue
			}

			if field.Value == "_id" {
				cell.Value = v.ID.Hex()
				continue
			}

			if field.Type == "struct" {
				str, err1 := json.Marshal(strMap[field.Value])
				if err1 != nil {
					logger.Error(err1.Error())
				}

				cell.Value = string(str)
			} else {
				cell.SetValue(strMap[field.Value])
			}
			// if field.Type == "string" {
			// 	cell.Value = (strMap[field.Value]).(string)
			// } else if field.Type == "int64" {
			// 	cell.SetInt64((strMap[field.Value]).(int64))
			// } else if field.Type == "int" {
			// 	cell.SetInt((strMap[field.Value]).(int))
			// } else {
			// 	str, err1 := json.Marshal(strMap[field.Value])
			// 	if err1 != nil {
			// 		logger.Println(err1.Error())
			// 	}
			//
			// 	cell.Value = string(str)
			// }
		}
	}

	var buf bytes.Buffer
	err = file.Write(&buf)
	if err != nil {
		return nil, "", err
	}

	filename := fmt.Sprintf("mysql_instance-%s.xlsx", time.Now().Format("**************"))
	return &cloudman.ExportXlsxResp{File: buf.Bytes()}, filename, err
}

// SyncMysqlInstances ...
func (h MysqlRes) SyncMysqlInstances(ctx context.Context, req *cloudman.SyncInstancesReq) (*cloudman.Result, error) {
	if len(req.Ids) == 0 {
		return nil, errors.New("empty host resource ids")
	}
	allMysqls, err := models.MysqlClusterResourceModel.FindWithManyPK(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	// key: account values: mysqls
	accountMysqlMap := map[string][]*entity.MysqlClusterResource{}
	for _, mysql := range allMysqls {
		mysqls, ok := accountMysqlMap[mysql.IspID]
		if !ok {
			accountMysqlMap[mysql.IspID] = []*entity.MysqlClusterResource{mysql}
		} else {
			mysqls = append(mysqls, mysql)
			accountMysqlMap[mysql.IspID] = mysqls
		}
	}

	for ispID, accountMysqls := range accountMysqlMap {
		account, err := models.AccountModel.Get(ctx, ispID)
		if err != nil {
			logger.Errorf("handler:SyncResource:FindAccountFailed: accountID=%s err=%s", ispID, err.Error())
			continue
		}

		// key: region values: instanceIDs
		mysqlRegionMap := map[string][]string{}
		for _, mysql := range accountMysqls {
			mysqlRegions, ok := mysqlRegionMap[mysql.RegionID]
			if !ok {
				mysqlRegionMap[mysql.RegionID] = []string{mysql.DBClusterID}
			} else {
				mysqlRegions = append(mysqlRegions, mysql.DBClusterID)
				mysqlRegionMap[mysql.RegionID] = mysqlRegions
			}
		}

		for regionID, instances := range mysqlRegionMap {
			for i := 0; i <= len(instances)/30; i++ {
				var searchInstances []string
				if i == len(instances)/30 {
					searchInstances = instances[i*30:]
				} else {
					searchInstances = instances[i*30 : (i+1)*30]
				}
				// send requests to Aliyun
				if account.AType == "aliyun" {
					aliSyncer := &synctask.AliyunSyncer{}
					if err := aliSyncer.SyncMysql(ctx, nil, synctask.SyncOption{RegionID: regionID, IspID: ispID}, searchInstances...); err != nil {
						return nil, err
					}
				} else if account.AType == "aws" {
					awsSyncer := &synctask.AwsSyncer{}
					if err := awsSyncer.SyncMysql(ctx, nil, synctask.SyncOption{RegionID: regionID, IspID: ispID}, searchInstances...); err != nil {
						return nil, err
					}
				}
			}
		}
	}

	return &cloudman.Result{Message: "success"}, nil
}

// DescribeParamsGroups 展示所有的参数组
func (h MysqlRes) DescribeParamsGroups(ctx context.Context, req *cloudman.DBParamsGroupsReq) (*cloudman.DBParamsGroupsResp, error) {
	client, err := getCloudRDS(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}
	return client.DescribeParameterGroups(ctx, &common.DescribeParameterGroupsInput{
		EngineName:    req.Engine,
		EngineVersion: req.EngineVersion,
	})
}

func (h MysqlRes) UploadBackupSetToOSS(ctx context.Context, req *cloudman.UploadBackupSetToOSSReq) (*cloudman.UploadBackupSetToOSSResp, error) {
	order, err := models.ResourceOrder.Get(ctx, req.BackupOrderId)
	if err != nil {
		return nil, err
	}
	if order.Action != "backup" {
		return nil, fmt.Errorf("wrong order type %s", order.Action)
	}
	if order.Status != 2 {
		return nil, fmt.Errorf("wrong status %d, order is not in success status", order.Status)
	}

	log := logrus.StandardLogger()
	// create download backup order first
	downloadOrder, err := createOrder(ctx, ResourceForm{
		Action:       "backup_download",
		IspID:        order.IspID,
		InstanceType: "mysql",
		RegionID:     order.RegionID,
	}, &provider_common.InitProvider{
		RegionID:   order.RegionID,
		RegionName: order.RegionName,
		IspType:    order.IspType,
		IspID:      order.IspID,
		IspName:    order.IspName,
		Logger:     log,
	}, "")
	if err != nil {
		return nil, err
	}
	polarProvider, err := alicloud.ResourceAliyunPoldbInstance(order.RegionID, order.IspID, BuildLogger(downloadOrder.ID.Hex()), false)
	if err != nil {
		return nil, err
	}

	// run order
	_ = models.ResourceOrder.Update(ctx, downloadOrder.ID.Hex(), map[string]interface{}{"status": constant.TaskRunning, "start_time": time.Now().Unix()})
	go func() {
		ctx := context.Background()
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("recover stack: %s", string(debug.Stack()))
			}
		}()
		err := polarProvider.UploadBackupToOSS(ctx, order, downloadOrder)
		if err != nil {
			logger.Errorf("upload backup to oss failed, err: %v", err)
			_ = models.ResourceOrder.Update(ctx, downloadOrder.ID.Hex(), map[string]interface{}{"status": constant.TaskErr, "end_time": time.Now().Unix(), "over_time": time.Now().Unix()})
			return
		}
		_ = models.ResourceOrder.Update(ctx, downloadOrder.ID.Hex(), map[string]interface{}{"status": constant.TaskSuccess, "end_time": time.Now().Unix(), "over_time": time.Now().Unix()})
	}()

	return &cloudman.UploadBackupSetToOSSResp{
		UploadOrderId: downloadOrder.ID.Hex(),
	}, nil
}
