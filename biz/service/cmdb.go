package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cmdb"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// Cmdb Cmdb-handler
type Cmdb struct {
}

// Query -
func (s Cmdb) Query(ctx context.Context, req *cloudman.CmdbQueryRequest) (*cloudman.CmdbQueryResponse, error) {
	list, queryRespObj, err := cmdb.ListBkCmdbObj(ctx, req.Env, req.InstType, req.Conds, req.Limit, req.Start, req.Sort)
	if err != nil {
		return nil, err
	}
	return &cloudman.CmdbQueryResponse{
		BkErrorCode: int32(queryRespObj.BkErrorCode),
		BkErrorMsg:  queryRespObj.BKErrorMsg,
		Count:       int32(queryRespObj.Data.Count),
		Info:        list,
	}, nil
}

// QueryAssociate -
func (s Cmdb) QueryAssociate(ctx context.Context, req *cloudman.CmdbQueryAssociateRequest) (*cloudman.CmdbQueryResponse, error) {
	var err error
	hostBase := ""
	if req.Env == "prod" {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	} else if req.Env == "test" {
		hostBase = cfg.GetCmdbConfig().BkCmdbTestURL
	} else {
		return nil, fmt.Errorf("cmdbQuery.unknown_env")
	}
	queryURL := fmt.Sprintf("%s/api/v3/find/instassociation", hostBase)
	queryRespBin, queryRespCode, err := utils.HTTPPost(context.Background(), queryURL, cmdb.JwtHeader(req.Env == "prod"), []byte(req.Body), 5*time.Second)
	if queryRespCode != 200 {
		return nil, fmt.Errorf("cmdbQuery.http.error, respCode: %d", queryRespCode)
	}
	logger.Infof("cmdbQuery.http.dump: %s", string(queryRespBin))
	queryRespObj := bkCmdbQueryDataResult{}
	err = json.Unmarshal(queryRespBin, &queryRespObj)
	if err != nil {
		return nil, fmt.Errorf("cmdbQuery.http.error: %s", err.Error())
	}
	respInfo := []string{}
	for _, i := range queryRespObj.Data {
		respInfo = append(respInfo, string(i))
	}
	return &cloudman.CmdbQueryResponse{
		BkErrorCode: int32(queryRespObj.BkErrorCode),
		BkErrorMsg:  queryRespObj.BKErrorMsg,
		Count:       int32(len(queryRespObj.Data)),
		Info:        respInfo,
	}, nil
}

// ManualUpdate -
func (s Cmdb) ManualUpdate(ctx context.Context, req *cloudman.CmdbUpdateRequest) (*cloudman.CmdbUpdateResponse, error) {
	ctx = context.WithValue(ctx, permission.Inner, permission.InnerTrue)

	// 处理筛选条件
	filter := map[string]interface{}{}
	if req.CustCond != "" {
		err := json.Unmarshal([]byte(req.CustCond), &filter)
		if err != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.custom_cond.error: %s", err.Error())
		}
	}
	if req.IspId != "any" {
		filter["isp_id"] = req.IspId
	}
	if req.RegionId != "any" {
		filter["RegionID"] = req.RegionId
	}
	// 执行搜索和push
	logger := logrus.StandardLogger()
	var resultObj interface{}
	resultStr := ""
	switch req.ResourceType {
	case "host":
		if len(req.ResourceId) > 0 {
			filter["InstanceID"] = map[string]interface{}{
				"$in": req.ResourceId,
			}
		}
		objP, findErr := models.HostResource{}.FindMany(ctx, filter)
		if findErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.find.error: %s", findErr.Error())
		}
		if int(req.UpdateCount) != len(objP) {
			return nil, fmt.Errorf("cmdbManualUpdate.update_count.expect: %d", len(objP))
		}
		obj := []entity.HostResource{}
		for _, r := range objP {
			if r != nil {
				obj = append(obj, *r)
			}
		}
		result, pushErr := cmdb.PushBkCmdbHost(logger, obj)
		if pushErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.push.error: %s", pushErr.Error())
		}
		resultObj = result
	case "mysql":
		if len(req.ResourceId) > 0 {
			filter["DBClusterId"] = map[string]interface{}{
				"$in": req.ResourceId,
			}
		}
		objP, findErr := models.MysqlClusterResource{}.FindMany(ctx, filter)
		if findErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.find.error: %s", findErr.Error())
		}
		if int(req.UpdateCount) != len(objP) {
			return nil, fmt.Errorf("cmdbManualUpdate.update_count.expect: %d", len(objP))
		}
		obj := []entity.MysqlClusterResource{}
		for _, r := range objP {
			if r != nil {
				obj = append(obj, *r)
			}
		}
		result, pushErr := cmdb.PushBkCmdbMysql(logger, obj)
		if pushErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.push.error: %s", pushErr.Error())
		}
		resultObj = result
	case "mysql-db":
		if len(req.ResourceId) > 0 {
			clusterName := ""
			dbNames := []string{}
			for _, r := range req.ResourceId {
				clusterAndDB := strings.Split(r, "|")
				if len(clusterAndDB) != 2 {
					continue
				}
				if clusterName != "" && clusterName != clusterAndDB[0] {
					continue
				}
				clusterName = clusterAndDB[0]
				dbNames = append(dbNames, clusterAndDB[1])
			}
			filter["cluster_id"] = clusterName
			filter["DBName"] = map[string]interface{}{
				"$in": dbNames,
			}
		}
		objP, findErr := models.MysqlDatabaseResource{}.FindMany(ctx, filter)
		if findErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.find.error: %s", findErr.Error())
		}
		if int(req.UpdateCount) != len(objP) {
			return nil, fmt.Errorf("cmdbManualUpdate.update_count.expect: %d", len(objP))
		}
		obj := []entity.MysqlDatabaseResource{}
		for _, r := range objP {
			if r != nil {
				obj = append(obj, *r)
			}
		}
		result, pushErr := cmdb.PushBkCmdbMysqlDatabase(logger, obj)
		if pushErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.push.error: %s", pushErr.Error())
		}
		resultObj = result
	case "redis":
		if len(req.ResourceId) > 0 {
			filter["InstanceID"] = map[string]interface{}{
				"$in": req.ResourceId,
			}
		}
		objP, findErr := models.RedisResource{}.FindMany(ctx, filter)
		if findErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.find.error: %s", findErr.Error())
		}
		if int(req.UpdateCount) != len(objP) {
			return nil, fmt.Errorf("cmdbManualUpdate.update_count.expect: %d", len(objP))
		}
		obj := []entity.RedisResource{}
		for _, r := range objP {
			if r != nil {
				obj = append(obj, *r)
			}
		}
		result, pushErr := cmdb.PushBkCmdbRedis(logger, obj)
		if pushErr != nil {
			return nil, fmt.Errorf("cmdbManualUpdate.push.error: %s", pushErr.Error())
		}
		resultObj = result
	default:
		return nil, fmt.Errorf("cmdbManualUpdate.unknown_ResourceType")
	}
	resultBin, _ := json.Marshal(resultObj)
	resultStr = string(resultBin)
	return &cloudman.CmdbUpdateResponse{
		Result: resultStr,
	}, nil
}

// Delete -
func (s Cmdb) Delete(ctx context.Context, req *cloudman.CmdbDeleteRequest) (*cloudman.CmdbDeleteResponse, error) {
	var err error
	hostBase := ""
	if req.Env == "prod" {
		hostBase = cfg.GetCmdbConfig().BkCmdbURL
	} else if req.Env == "test" {
		hostBase = cfg.GetCmdbConfig().BkCmdbTestURL
	} else {
		return nil, fmt.Errorf("cmdbDelete.unknown_env")
	}

	deleteURL := fmt.Sprintf("%s/api/v3/%s/instance/object/%s", hostBase, "deletemany", req.InstType)
	deleteBody := ""
	for _, id := range req.Ids {
		deleteBody, _ = sjson.Set(deleteBody, "delete.inst_ids.-1", id)
	}
	logger.Infof("cmdbDelete.req.dump: %s", string(deleteBody))
	deleteCtx := context.WithValue(context.Background(), utils.CtxKeyMethod, "DELETE")
	deleteRespBin, deleteRespCode, err := utils.HTTPPost(deleteCtx, deleteURL, cmdb.JwtHeader(req.Env == "prod"), []byte(deleteBody), 5*time.Second)
	logger.Infof("cmdbDelete.resp.dump: %s", string(deleteRespBin))

	if deleteRespCode != 200 {
		return nil, fmt.Errorf("cmdbDelete.http.error, respCode: %d", deleteRespCode)
	}
	respObj := bkCmdbQueryDataResult{}
	err = json.Unmarshal(deleteRespBin, &respObj)
	if err != nil {
		return nil, fmt.Errorf("cmdbDelete.http.Unmarshal.error, err: %s", err.Error())
	}
	if respObj.BkErrorCode != 0 {
		return nil, fmt.Errorf("cmdbDelete.business.error, BKErrorCode: %d, BKErrorMsg: %s", respObj.BkErrorCode, respObj.BKErrorMsg)
	}
	return &cloudman.CmdbDeleteResponse{
		BkErrorCode: int32(respObj.BkErrorCode),
		BkErrorMsg:  respObj.BKErrorMsg,
	}, nil
}

type bkCmdbQueryDataResult struct {
	Result      bool              `json:"result"`
	BkErrorCode int               `json:"bk_error_code"`
	BKErrorMsg  string            `json:"bk_error_msg"`
	Data        []json.RawMessage `json:"data"`
}

// PullInfo -
func (s Cmdb) PullInfo(ctx context.Context, req *cloudman.CmdbPullInfoRequest) (*cloudman.Empty, error) {
	switch req.ResourceType {
	case "host":
		err := synctask.GetBkCmdbSyncer().SyncHost(ctx, logrus.StandardLogger(), synctask.SyncOption{
			RegionID: req.RegionId,
		}, req.Instances...)
		if err != nil {
			return nil, err
		}
	case "mysql":
		err := synctask.GetBkCmdbSyncer().SyncMysql(ctx, logrus.StandardLogger(), synctask.SyncOption{
			RegionID: req.RegionId,
		}, req.Instances...)
		if err != nil {
			return nil, err
		}
	case "redis":
		err := synctask.GetBkCmdbSyncer().SyncRedis(ctx, logrus.StandardLogger(), synctask.SyncOption{
			RegionID: req.RegionId,
		}, req.Instances...)
		if err != nil {
			return nil, err
		}
	default:
		return nil, fmt.Errorf("unknown resource type")
	}
	return &cloudman.Empty{}, nil
}

// Diff -
func (s Cmdb) Diff(ctx context.Context, req *cloudman.CmdbDiffRequest) (*cloudman.CommonResourceResp, error) {
	reqDump, _ := json.Marshal(req)
	resForm := ResourceForm{
		Action:       "cmdb_diff",
		IspID:        "global",
		InstanceType: req.ResourceType,
		RegionID:     req.RegionId,
		RawData:      string(reqDump),
	}

	pr := &common.InitProvider{
		RegionID: req.RegionId,
	}

	order, err := createOrder(ctx, resForm, pr, "")
	if err != nil {
		return nil, err
	}

	taskCtx, taskCancel := context.WithTimeout(context.Background(), 60*time.Minute)
	go func() {
		var taskErr error
		defer func() {
			operateResourceAfter(order, taskErr)
		}()
		defer taskCancel()
		err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{
			"start_time": time.Now().Unix(),
			"status":     constant.TaskRunning,
		})
		t := cmdb.CompareTask{
			OrderID:      order.ID.Hex(),
			Env:          req.Env,
			RegionID:     req.RegionId,
			ResourceType: strings.Split(req.ResourceType, ","),
			InstanceID:   req.Instances,
		}
		taskErr = t.DoCompare(taskCtx, pr)
		if taskErr != nil {
			logger.Errorf("cmdb_diff(%s)执行错误：%s", order.ID.Hex(), err.Error())
		} else {
			logger.Infof("cmdb_diff(%s)执行成功", order.ID.Hex())
		}
	}()

	return &cloudman.CommonResourceResp{OrderId: order.ID.Hex()}, nil
}

// DiffProcess -
func (s Cmdb) DiffProcess(ctx context.Context, req *cloudman.CmdbDiffProcessRequest) (*cloudman.Empty, error) {
	order, err := models.ResourceOrder.Get(ctx, req.OrderId)
	if err != nil {
		return &cloudman.Empty{}, err
	}
	if order.Status == constant.TaskRunning {
		return &cloudman.Empty{}, fmt.Errorf("当前任务状态执行中，请勿重复提交")
	}
	if order.Status != constant.TaskSuccess {
		return &cloudman.Empty{}, fmt.Errorf("当前任务状态异常，建议重新diff并执行修复")
	}
	// 锁/解锁订单
	err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{
		"status": constant.TaskRunning,
	})
	if err != nil {
		return &cloudman.Empty{}, err
	}
	taskCtx, taskCancel := context.WithTimeout(context.Background(), 60*time.Minute)
	go func() {
		defer func() {
			err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{
				"status": constant.TaskSuccess,
			})
		}()
		defer taskCancel()
		logging := BuildLogger(order.ID.Hex())
		env := gjson.Get(order.RawData, "env").String()
		statusDetail := order.StatusDetail
		for _, item := range req.List {
			if item.ActionType == "add" || item.ActionType == "update" {
				_, err := s.ManualUpdate(taskCtx, &cloudman.CmdbUpdateRequest{
					UpdateCount:  1,
					ResourceType: item.ResourceType,
					IspId:        "any",
					RegionId:     "any",
					ResourceId:   []string{item.InstanceId},
					CustCond:     "",
				})
				if err != nil {
					statusDetail = common.ReportOrderStatusDetailKv(taskCtx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.%s.%s.solve_err", item.ResourceType, item.ActionType, item.InstanceId): true,
						common.ReportSprintf("%s.%s.%s.reason.-1", item.ResourceType, item.ActionType, item.InstanceId): "process error: " + err.Error(),
					})
				} else {
					statusDetail = common.ReportOrderStatusDetailKv(taskCtx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.%s.%s.solved", item.ResourceType, item.ActionType, item.InstanceId): true,
					})
				}
				continue
			}
			if item.ActionType == "remove" {
				ids, err := cmdb.FindBkIDByBkName(logging, env == "prod", cmdb.CmdbTypeMap[item.ResourceType], []string{item.InstanceId})
				if err != nil {
					statusDetail = common.ReportOrderStatusDetailKv(taskCtx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.%s.%s.solve_err", item.ResourceType, item.ActionType, item.InstanceId): true,
						common.ReportSprintf("%s.%s.%s.reason.-1", item.ResourceType, item.ActionType, item.InstanceId): "process error: " + err.Error(),
					})
					continue
				}
				id := ids[item.InstanceId]
				if id == 0 {
					statusDetail = common.ReportOrderStatusDetailKv(taskCtx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.%s.%s.solve_err", item.ResourceType, item.ActionType, item.InstanceId): true,
						common.ReportSprintf("%s.%s.%s.reason.-1", item.ResourceType, item.ActionType, item.InstanceId): "process error: cannot get bk_id",
					})
					continue
				}
				_, err = s.Delete(taskCtx, &cloudman.CmdbDeleteRequest{
					Env:      env,
					InstType: cmdb.CmdbTypeMap[item.ResourceType],
					Ids:      []int32{int32(id)},
				})
				if err != nil {
					statusDetail = common.ReportOrderStatusDetailKv(taskCtx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.%s.%s.solve_err", item.ResourceType, item.ActionType, item.InstanceId): true,
						common.ReportSprintf("%s.%s.%s.reason.-1", item.ResourceType, item.ActionType, item.InstanceId): "process error: " + err.Error(),
					})
				} else {
					statusDetail = common.ReportOrderStatusDetailKv(taskCtx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.%s.%s.solved", item.ResourceType, item.ActionType, item.InstanceId): true,
					})
				}
			}
		}
	}()
	return &cloudman.Empty{}, nil
}

// DiffDelete -
func (c Cmdb) DiffDelete(ctx context.Context, req *cloudman.CmdbDiffDeleteReq) (*cloudman.CommonResourceResp, error) {
	aliSyncer := &synctask.AliyunSyncer{}
	res, err := aliSyncer.DeleteNonExistHost(ctx, logrus.StandardLogger(), req.DryRun)
	if err != nil {
		return nil, err
	}
	return &cloudman.CommonResourceResp{
		Message: res,
	}, nil
}

// DiffUpdate -
func (c Cmdb) DiffUpdate(ctx context.Context, req *cloudman.CmdbDiffDeleteReq) (*cloudman.CommonResourceResp, error) {
	updateInstanceNames, err := synctask.GetBkCmdbNapSyncer().DiffUpdate(ctx, logrus.StandardLogger(), req.DryRun)
	if err != nil {
		return nil, err
	}
	return &cloudman.CommonResourceResp{
		Message: fmt.Sprintf("update instance names: %s", strings.Join(updateInstanceNames, ",")),
	}, nil
}
