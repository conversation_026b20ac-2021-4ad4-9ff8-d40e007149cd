package service

import (
	"context"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/cache"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/host"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/lb"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/rds"
)

func getCloudRDS(ctx context.Context, isp string, rid string) (rds.RDS, error) {
	account, err := models.AccountModel.Get(ctx, isp)
	if err != nil {
		return nil, err
	}

	region, err := models.RegionModel.FindManyWithPK(ctx, []string{rid})
	if err != nil || len(region) == 0 {
		return nil, fmt.Errorf("find region error: %s", err.Error())
	}

	return rds.GetRDS(ctx, account.AType, region[0].RegionID, account.ID.Hex())
}

func getCloudHost(ctx context.Context, isp string, rid string) (host.Host, error) {
	account, err := models.AccountModel.Get(ctx, isp)
	if err != nil {
		return nil, err
	}

	region, err := models.RegionModel.FindManyWithPK(ctx, []string{rid})
	if err != nil || len(region) == 0 {
		return nil, fmt.Errorf("find region error: %s", err.Error())
	}

	return host.GetHost(ctx, account.AType, region[0].RegionID, account.ID.Hex())
}

func getCloudCache(ctx context.Context, ispID string, rid string) (cache.Cache, error) {
	account, err := models.AccountModel.Get(ctx, ispID)
	if err != nil {
		return nil, err
	}

	region, err := models.RegionModel.FindManyWithPK(ctx, []string{rid})
	if err != nil || len(region) == 0 {
		return nil, fmt.Errorf("find region error: %s", err.Error())
	}

	return cache.GetCache(ctx, account.AType, region[0].RegionID, ispID)
}

func getLocalHostParams(ctx context.Context, isp string, rid string) (host.Host, error) {
	// 判定是否在云厂商中配置相应参数
	hostAccount, err := models.AccountParamModel.GetHostParams(ctx, isp, rid)
	if err != nil {
		return nil, err
	}
	return hostAccount, nil
}

func getCloudLB(ctx context.Context, isp string, rid string) (lb.LB, error) {
	account, err := models.AccountModel.Get(ctx, isp)
	if err != nil {
		return nil, err
	}

	region, err := models.RegionModel.FindManyWithPK(ctx, []string{rid})
	if err != nil || len(region) == 0 {
		return nil, fmt.Errorf("find region error: %s", err.Error())
	}

	return lb.GetLB(ctx, account.AType, region[0].RegionID, account.ID.Hex())
}
