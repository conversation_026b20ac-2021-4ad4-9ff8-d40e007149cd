package service

import (
	"context"
	"testing"

	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

func init() {
	logger.Init()
}

func TestSendInitFailedNotice(t *testing.T) {
	order := &entity.ResOrder{
		UserModel: entity.UserModel{
			Model: entity.Model{},
			AddUser: entity.AddUser{
				CreateUser: "yi.xie",
			},
		},
		StartTime:    0,
		OverTime:     0,
		Status:       0,
		IspID:        "",
		IspName:      "",
		IspType:      "",
		RegionID:     "",
		RegionName:   "",
		Type:         "",
		Action:       "",
		BeforeInfo:   "",
		AfterInfo:    "",
		ErrorMsg:     "",
		RawData:      "",
		Reason:       "",
		StatusDetail: "",
		TicketID:     0,
		TemplateId:   new(string),
		TemplateName: new(string),
		InitOption:   new(string),
	}
	err := sendInitFailedNotice(context.Background(), []string{"i-xxxxxx"}, order)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("sendInitFailedNotice error: %v", err)
	}
}
