package service

import (
	"bytes"
	"errors"
	"fmt"
	"math"
	"net"
	"slices"
	"strings"
	"time"

	ecs20140526V3 "github.com/alibabacloud-go/ecs-20140526/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
	"github.com/tealeg/xlsx"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// SecurityGroup 安全组对象
type SecurityGroup struct {
}

// Describe 获取安全组资源列表
func (h SecurityGroup) Describe(ctx context.Context, req *cloudman.DescribeSecurityGroupReq) (*cloudman.DescribeSecurityGroupRes, error) {
	resp, total, err := models.SecurityGroupModel.Query(ctx, &schema.SecurityGroupQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		SecurityGroupColumnParam: schema.SecurityGroupColumnParam{
			RegionID:           req.RegionId,
			IspID:              req.Isp,
			CustomTag:          req.CustomTag,
			Level:              req.Level,
			DisplayNeedCleanup: req.DisplayNeedCleanUp,
			Type:               req.Type,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{
			// 默认创建时间降序
			Ordering: []string{
				"-CreationTime",
			},
		},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.SecurityGroupModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	sgIDs := make([]string, 0)
	sgMap := make(map[string]*cloudman.SecurityGroupEntity)
	for _, sg := range list {
		sgIDs = append(sgIDs, sg.SecurityGroupId)
		sgMap[sg.SecurityGroupId] = sg
		sg.Count = &cloudman.RelateInstanceCount{
			Host:  0,
			Mysql: 0,
			Redis: 0,
		}
	}

	hosts, err := HostRes{}.Describe(ctx, &cloudman.HostResReq{Page: 1, Size: math.MaxInt32, SecurityGroupIds: sgIDs})
	if err != nil {
		return nil, err
	}
	for _, h := range hosts.List {
		for _, s := range h.SecurityGroupInfos {
			if s, ok := sgMap[s.SecurityGroupId]; ok {
				s.Count.Host++
			}
		}
	}

	mysqls, err := MysqlRes{}.DescribeCluster(ctx, &cloudman.MysqlClusterReq{Page: 1, Size: math.MaxInt32, SecurityGroupIds: sgIDs})
	if err != nil {
		return nil, err
	}
	for _, h := range mysqls.List {
		for _, s := range h.SecurityGroupInfos {
			if s, ok := sgMap[s.SecurityGroupId]; ok {
				s.Count.Mysql++
			}
		}
	}

	redises, err := RedisRes{}.Describe(ctx, &cloudman.RedisReq{Page: 1, Size: math.MaxInt32, SecurityGroupIds: sgIDs})
	if err != nil {
		return nil, err
	}
	for _, h := range redises.List {
		for _, s := range h.SecurityGroupInfos {
			if s, ok := sgMap[s.SecurityGroupId]; ok {
				s.Count.Redis++
			}
		}
	}

	return &cloudman.DescribeSecurityGroupRes{List: list, Total: int32(total)}, err
}

// DescribeByInstances 获取主机资源列表
func (h SecurityGroup) DescribeByInstances(ctx context.Context, req *cloudman.DescribeSecurityGroupByInstancesReq) (*cloudman.DescribeSecurityGroupRes, error) {
	if len(req.InstanceIds) == 0 {
		return &cloudman.DescribeSecurityGroupRes{List: nil, Total: 0}, nil
	}

	type InstanceInfo struct {
		InstanceID   string
		InstanceName string
	}
	sgMap := map[string][]InstanceInfo{}
	if req.InstanceType == "host" {
		hosts, err := describeHostByInstanceIDs(ctx, req.InstanceIds)
		if err != nil {
			return nil, err
		}
		for _, host := range hosts {
			for _, sg := range host.SecurityGroupIds.SecurityGroupID {
				_, ok := sgMap[sg]
				if !ok {
					sgMap[sg] = []InstanceInfo{{InstanceID: host.InstanceID, InstanceName: host.InstanceName}}
				} else {
					sgMap[sg] = append(sgMap[sg], InstanceInfo{InstanceID: host.InstanceID, InstanceName: host.InstanceName})
				}
			}
		}
	}

	if len(sgMap) == 0 {
		return &cloudman.DescribeSecurityGroupRes{List: nil, Total: 0}, nil
	}

	var sgList []string
	for sg := range sgMap {
		sgList = append(sgList, sg)
	}

	resp, err := models.SecurityGroupModel.FindMany(ctx, map[string]interface{}{
		"SecurityGroupId": map[string][]string{
			"$in": sgList,
		},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.SecurityGroupModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	for _, sg := range list {
		v, ok := sgMap[sg.SecurityGroupId]
		if ok {
			sg.BindInstanceInfos = []*cloudman.InstanceInfo{}
			for _, info := range v {
				sg.BindInstanceInfos = append(sg.BindInstanceInfos, &cloudman.InstanceInfo{
					InstanceID:   info.InstanceID,
					InstanceName: info.InstanceName,
				})
			}
		}
	}

	if (req.Page-1)*req.Size >= uint64(len(list)) {
		return &cloudman.DescribeSecurityGroupRes{List: list, Total: int32(len(list))}, nil
	}
	if req.Page*req.Size > uint64(len(list)) {
		return &cloudman.DescribeSecurityGroupRes{List: list[(req.Page-1)*req.Size:], Total: int32(len(list))}, nil
	}
	return &cloudman.DescribeSecurityGroupRes{List: list[(req.Page-1)*req.Size : req.Page*req.Size], Total: int32(len(list))}, nil
}

// DescribeRules ...
func (h SecurityGroup) DescribeRules(ctx context.Context, req *cloudman.DescribeRulesReq) (*cloudman.DescribeSecurityGroupRes, error) {
	resp, _, err := models.SecurityGroupModel.Query(ctx, &schema.SecurityGroupQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: math.MaxInt32,
		},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.SecurityGroupModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	var resSG []*cloudman.SecurityGroupEntity
	total := 0
	for _, sg := range list {
		var resRules []*cloudman.SecurityGroupPermission
		if req.Isp != "" && sg.IspId != req.Isp {
			continue
		}
		if req.RegionId != "" && sg.RegionID != req.RegionId {
			continue
		}
		for _, rule := range sg.Permissions {
			if req.Priority != 0 && rule.Priority != req.Priority {
				continue
			}
			if (req.IpType != "" || req.IpStr != "" || req.PortRange != "") && rule.SourceCidrIp == "" {
				continue
			}
			if req.IpType != "" {
				isPublic := false
				IP := net.ParseIP(rule.SourceCidrIp)
				if IP != nil {
					isPublic = isPublicIP(IP)
				} else {
					_, IPNet, _ := net.ParseCIDR(rule.SourceCidrIp)
					isPublic = isPublicIP(IPNet.IP)
				}
				if req.IpType == "public" && !isPublic || req.IpType == "private" && isPublic {
					continue
				}
			}
			if req.PortRange != "" && rule.PortRange != req.PortRange {
				continue
			}
			if req.IpStr != "" && rule.SourceCidrIp != req.IpStr && !strings.HasPrefix(rule.SourceCidrIp, req.IpStr+"/") {
				continue
			}
			resRules = append(resRules, rule)
		}
		if len(resRules) != 0 {
			total += len(resRules)
			sg.Permissions = resRules
			resSG = append(resSG, sg)
		}
	}
	return &cloudman.DescribeSecurityGroupRes{
		Total: int32(total),
		List:  resSG,
	}, nil
}

// UpdateSecurityGroupRule 更新安全组规则（仅限标签）
func (h SecurityGroup) UpdateSecurityGroupRule(ctx context.Context, req *cloudman.UpdateSecurityGroupRuleReq) (*cloudman.Result, error) {
	sg, err := models.SecurityGroupModel.GetBySecurityGroupID(ctx, req.SgId)
	if err != nil {
		return nil, err
	}

	if req.LevelTag != nil {
		sg.LevelTag = entity.RuleTag{
			Level: req.LevelTag.Level,
			Value: req.LevelTag.Value,
		}
	}

	for i := 0; i < len(sg.Permissions); i++ {
		for _, reqPerm := range req.Permissions {
			if sg.Permissions[i].SecurityGroupRuleID == reqPerm.SecurityGroupRuleId {
				var tags []entity.RuleTag
				for _, tag := range reqPerm.Tags {
					tags = append(tags, entity.RuleTag{
						Level: tag.Level,
						Value: tag.Value,
					})
				}
				sg.Permissions[i].Tags = tags
				break
			}
		}
	}

	err = models.SecurityGroupModel.Update(ctx, sg.ID.Hex(), sg)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, err
}

// UpdateSecurityGroups 更新安全组（仅限标签）
func (h SecurityGroup) UpdateSecurityGroups(ctx context.Context, req *cloudman.UpdateSecurityGroupsReq) (*cloudman.Result, error) {
	for _, sg := range req.Sgs {
		if sg.LevelTag != nil {
			err := models.SecurityGroupModel.UpdateLevelTag(ctx, sg.SecurityGroupId, entity.RuleTag{
				Level: sg.LevelTag.Level,
				Value: sg.LevelTag.Value,
			})
			if err != nil {
				return nil, fmt.Errorf("update sg %s failed: %v", sg.SecurityGroupId, err)
			}
		}
	}
	return &cloudman.Result{Message: "success"}, nil
}

// GetAccountRegionTags 获取云厂商地域自定义安全组页签
func (h SecurityGroup) GetAccountRegionTags(ctx context.Context, req *cloudman.GetAccountRegionTagsReq) (*cloudman.GetAccountRegionTagsRes, error) {
	res, err := models.SecurityGroupModel.GetCustomTags(ctx, req.IspId, req.RegionId)
	if err != nil {
		return nil, err
	}
	res = append(res, "默认节点")
	return &cloudman.GetAccountRegionTagsRes{CustomTags: res}, nil
}

// GetCustomTagSecurityGroups 获取自定义页签的安全组
func (h SecurityGroup) GetCustomTagSecurityGroups(ctx context.Context, req *cloudman.GetCustomTagSecurityGroupsReq) (*cloudman.GetCustomTagSecurityGroupsRes, error) {
	res, err := models.SecurityGroupModel.GetCustomTagSecurityGroups(ctx, req.IspId, req.RegionId, req.CustomTag)
	if err != nil {
		return nil, err
	}
	sgEntities, err := models.SecurityGroupModelToPb(ctx, res)
	if err != nil {
		return nil, err
	}
	return &cloudman.GetCustomTagSecurityGroupsRes{
		List:  sgEntities,
		Total: int32(len(sgEntities)),
	}, nil
}

// UpdateSecurityGroupCustomTag xxx
func (h SecurityGroup) UpdateSecurityGroupCustomTag(ctx context.Context, req *cloudman.UpdateSecurityGroupCustomTagReq) (*cloudman.Result, error) {
	if req.CustomTag == "默认节点" {
		req.CustomTag = ""
	}
	err := models.SecurityGroupModel.UpdateCustomTagBySgID(ctx, req.SgIds, req.CustomTag)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// CleanupSecurityGroups xxx
func (h SecurityGroup) CleanupSecurityGroups(ctx context.Context, req *cloudman.UpdateSecurityGroupCustomTagReq) (*cloudman.Result, error) {
	if len(req.SgIds) == 0 {
		return nil, errors.New("请求id列表为空")
	}
	err := models.SecurityGroupModel.Delete(ctx, req.SgIds)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// ExportSecurityGroup xxx
func (h SecurityGroup) ExportSecurityGroup(ctx context.Context, req *cloudman.ExportSecurityGroupReq) (*cloudman.ExportXlsxResp, string, error) {
	exportMap := map[string]bool{}

	if len(req.Keys) == 0 {
		exportMap[""] = true
	}

	for _, key := range req.Keys {
		keyParts := strings.Split(key, "_!_")
		if len(keyParts) == 0 {
			continue
		}
		dynamicKey := keyParts[0]
		insert := true
		for k := 0; k < len(keyParts)-1; k++ {
			if k != 0 {
				dynamicKey = strings.Join([]string{dynamicKey, keyParts[k]}, "_!_")
			}
			_, ok := exportMap[dynamicKey]
			if ok {
				insert = false
				break
			}
		}
		if insert {
			exportMap[key] = true
		}
	}

	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	colHeader := []string{"安全组id", "安全组名称", "所属云厂商", "地域", "页签", "VPC", "备注"}
	var err error

	file = xlsx.NewFile()
	sheet, err = file.AddSheet("安全组列表")
	if err != nil {
		return nil, "", err
	}

	row = sheet.AddRow()
	for _, header := range colHeader {
		cell = row.AddCell()
		cell.Value = header
	}

	for exportKey := range exportMap {
		isp, regionID, customTag := "", "", ""
		keyParts := strings.Split(exportKey, "_!_")
		if len(keyParts) > 0 {
			isp = keyParts[0]
		}
		if len(keyParts) > 1 {
			regionID = keyParts[1]
		}
		if len(keyParts) > 2 {
			customTag = keyParts[2]
		}
		res, err := h.Describe(ctx, &cloudman.DescribeSecurityGroupReq{
			Isp:       isp,
			RegionId:  regionID,
			CustomTag: customTag,
			Page:      1,
			Size:      math.MaxUint32,
		})
		if err != nil {
			return nil, "", fmt.Errorf("get security group of %s error: %v", exportKey, err)
		}

		for _, sg := range res.List {
			row = sheet.AddRow()
			cell = row.AddCell()
			cell.Value = sg.SecurityGroupId
			cell = row.AddCell()
			cell.Value = sg.SecurityGroupName
			cell = row.AddCell()
			cell.Value = sg.IspName
			cell = row.AddCell()
			cell.Value = sg.RegionID
			cell = row.AddCell()
			cell.Value = sg.CustomTag
			cell = row.AddCell()
			cell.Value = sg.VpcId
			cell = row.AddCell()
			cell.Value = sg.Description
		}
	}

	var buf bytes.Buffer
	err = file.Write(&buf)
	if err != nil {
		return nil, "", err
	}
	filename := fmt.Sprintf("SecurityGroup-%s.xlsx", time.Now().Format("20060102150405"))
	return &cloudman.ExportXlsxResp{File: buf.Bytes()}, filename, err
}

// ExportSecurityGroupRule xxx
func (h SecurityGroup) ExportSecurityGroupRule(ctx context.Context, req *cloudman.ExportSecurityGroupRuleReq) (*cloudman.ExportXlsxResp, string, error) {
	sg, err := models.SecurityGroupModel.GetBySecurityGroupID(ctx, req.SgId)
	if err != nil {
		return nil, "", err
	}

	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell

	file = xlsx.NewFile()
	sheet, err = file.AddSheet("安全组规则")
	if err != nil {
		return nil, "", err
	}

	row = sheet.AddRow()
	colHeader := []string{"方向", "动作", "优先级", "协议", "端口范围", "来源", "标签", "备注", "描述"}
	for _, header := range colHeader {
		cell = row.AddCell()
		cell.Value = header
	}

	for _, perm := range sg.Permissions {
		row = sheet.AddRow()
		cell = row.AddCell()
		direction := "未知"
		if perm.Direction == "ingress" {
			direction = "入"
		} else if perm.Direction == "egress" {
			direction = "出"
		}
		cell.Value = direction
		cell = row.AddCell()
		cell.Value = perm.Policy
		cell = row.AddCell()
		cell.Value = perm.Priority
		cell = row.AddCell()
		cell.Value = perm.IPProtocol
		cell = row.AddCell()
		cell.Value = perm.PortRange
		cell = row.AddCell()
		cell.Value = perm.SourceCidrIP
		if perm.SourceCidrIP == "" {
			cell.Value = perm.SourceGroupID
		}
		cell1 := row.AddCell()
		cell2 := row.AddCell()
		if len(perm.Tags) > 0 {
			cell1.Value = levelToText(perm.Tags[0].Level)
			cell2.Value = perm.Tags[0].Value
		}
		cell = row.AddCell()
		cell.Value = perm.Description
	}

	var buf bytes.Buffer
	err = file.Write(&buf)
	if err != nil {
		return nil, "", err
	}
	filename := fmt.Sprintf("SecurityGroupRule-%s-%s.xlsx", sg.SecurityGroupName, time.Now().Format("20060102150405"))
	return &cloudman.ExportXlsxResp{File: buf.Bytes()}, filename, err
}

func levelToText(level string) string {
	switch level {
	case "success":
		return "安全"
	case "warning":
		return "警告"
	case "info":
		return "信息"
	case "danger":
		return "危险"
	default:
		return ""
	}
}

// ExportRelateInstances xxx
func (h SecurityGroup) ExportRelateInstances(ctx context.Context, req *cloudman.ExportRelateInstancesReq) (*cloudman.ExportXlsxResp, string, error) {
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	file = xlsx.NewFile()

	hosts, err := HostRes{}.Describe(ctx, &cloudman.HostResReq{
		SecurityGroupIds:          req.SgIds,
		AndSearchSecurityGroupIds: req.AndSearchSecurityGroupIds,
		Page:                      1,
		Size:                      math.MaxUint32,
	})
	if err != nil {
		return nil, "", err
	}
	sheet, err = file.AddSheet("主机")
	if err != nil {
		return nil, "", err
	}
	row = sheet.AddRow()
	colHeader := []string{"实例ID", "实例名称", "关联安全组"}
	for _, header := range colHeader {
		cell = row.AddCell()
		cell.Value = header
	}
	for _, host := range hosts.List {
		row = sheet.AddRow()
		cell = row.AddCell()
		cell.Value = host.InstanceId
		cell = row.AddCell()
		cell.Value = host.InstanceName
		cell = row.AddCell()
		cell.Value = strings.Join(sgInfosToIDName(host.SecurityGroupInfos), ";")
	}

	mysqls, err := MysqlRes{}.DescribeCluster(ctx, &cloudman.MysqlClusterReq{
		SecurityGroupIds:          req.SgIds,
		AndSearchSecurityGroupIds: req.AndSearchSecurityGroupIds,
		Page:                      1,
		Size:                      math.MaxUint32,
	})
	if err != nil {
		return nil, "", err
	}
	sheet, err = file.AddSheet("数据库")
	if err != nil {
		return nil, "", err
	}
	row = sheet.AddRow()
	colHeader = []string{"实例ID", "实例名称", "关联安全组"}
	for _, header := range colHeader {
		cell = row.AddCell()
		cell.Value = header
	}
	for _, mysql := range mysqls.List {
		row = sheet.AddRow()
		cell = row.AddCell()
		cell.Value = mysql.DBClusterId
		cell = row.AddCell()
		cell.Value = mysql.DBClusterDescription
		cell = row.AddCell()
		cell.Value = strings.Join(sgInfosToIDName(mysql.SecurityGroupInfos), ";")
	}

	redises, err := RedisRes{}.Describe(ctx, &cloudman.RedisReq{
		SecurityGroupIds:          req.SgIds,
		AndSearchSecurityGroupIds: req.AndSearchSecurityGroupIds,
		Page:                      1,
		Size:                      math.MaxUint32,
	})
	if err != nil {
		return nil, "", err
	}
	sheet, err = file.AddSheet("缓存")
	if err != nil {
		return nil, "", err
	}
	row = sheet.AddRow()
	colHeader = []string{"实例ID", "实例名称", "关联安全组"}
	for _, header := range colHeader {
		cell = row.AddCell()
		cell.Value = header
	}
	for _, redis := range redises.List {
		row = sheet.AddRow()
		cell = row.AddCell()
		cell.Value = redis.InstanceId
		cell = row.AddCell()
		cell.Value = redis.InstanceName
		cell = row.AddCell()
		cell.Value = strings.Join(sgInfosToIDName(redis.SecurityGroupInfos), ";")
	}

	var buf bytes.Buffer
	err = file.Write(&buf)
	if err != nil {
		return nil, "", err
	}
	filename := fmt.Sprintf("RelateInstance-%s.xlsx", time.Now().Format("20060102150405"))
	return &cloudman.ExportXlsxResp{File: buf.Bytes()}, filename, err
}

func sgInfosToIDName(sgInfo []*cloudman.SecurityGroupInfo) []string {
	var idNames []string
	for _, sg := range sgInfo {
		idNames = append(idNames, fmt.Sprintf("%s(%s)", sg.SecurityGroupId, sg.SecurityGroupName))
	}
	return idNames
}

// DescribeIPWhitelists xxx
func (h SecurityGroup) DescribeIPWhitelists(ctx context.Context, req *cloudman.DescribeIPWhitelistsReq) (*cloudman.DescribeIPWhitelistsResp, error) {
	resp, total, err := models.IPWhitelistModel.Query(ctx, &schema.IPWhitelistQueryParam{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		IPWhitelistColumnParam: schema.IPWhitelistColumnParam{
			WhitelistId: req.WhitelistId,
			Name:        req.Name,
		},
		OrderParams: schema.OrderParams{},
	})

	if err != nil {
		return nil, err
	}

	whitelists, err := models.IPWhitelistsToPb(resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeIPWhitelistsResp{
		List:  whitelists,
		Total: int32(total),
	}, nil
}

func (h SecurityGroup) ModifyIPWhitelists(ctx context.Context, req *cloudman.ModifyIPWhitelistsReq) (*cloudman.Result, error) {
	// 先调用阿里云创建接口
	sg, err := models.IPWhitelistModel.GetBySecurityGroupID(ctx, req.SecurityGroupId)
	if err != nil {
		return nil, err
	}

	client := alicloud.CreateAliPolarDBClient(sg.RegionID, sg.IspID)
	err = client.ModifyGlobalSecurityIPGroup(ctx, sg.GlobalIgName, sg.GlobalSecurityGroupID, req.Ips)
	if err != nil {
		return nil, err
	}

	// 再同步
	aliSyncer := &synctask.AliyunSyncer{}
	if err := aliSyncer.SyncPolarDBWhitelist(ctx, logrus.StandardLogger(), synctask.SyncOption{RegionID: sg.RegionID, IspID: sg.IspID}, req.SecurityGroupId); err != nil {
		return nil, err
	}

	return &cloudman.Result{
		Message: "修改成功",
	}, err
}

// DescribeALBAcls xxx
func (h SecurityGroup) DescribeALBAcls(ctx context.Context, req *cloudman.DescribeALBAclsReq) (*cloudman.DescribeALBAclsResp, error) {
	resp, total, err := models.AlbACLModel.Query(ctx, &schema.ALBAclQueryParam{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		ALBAclColumnParam: schema.ALBAclColumnParam{
			AclId: req.AclId,
			Name:  req.Name,
		},
		OrderParams: schema.OrderParams{
			// 默认创建时间降序
			Ordering: []string{
				"-CreateTime",
			},
		},
	})

	if err != nil {
		return nil, err
	}

	acls, err := models.ALBAclsToPb(resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeALBAclsResp{
		List:  acls,
		Total: int32(total),
	}, nil
}

func (h SecurityGroup) CreateSecurityGroupRule(ctx context.Context, req *cloudman.CreateSecurityGroupRuleReq) (*cloudman.Result, error) {
	sg, err := models.SecurityGroupModel.GetBySecurityGroupID(ctx, req.SecurityGroupId)
	if err != nil {
		return nil, err
	}

	// 调用阿里云接口创建
	client, err := alicloud.CreateAliEcsClient(sg.RegionID, sg.IspID)
	if err != nil {
		return nil, err
	}

	clientReq := &ecs20140526V3.AuthorizeSecurityGroupRequest{}
	clientReq.SetRegionId(sg.RegionID)
	clientReq.SetSecurityGroupId(req.SecurityGroupId)
	permissions := make([]*ecs20140526V3.AuthorizeSecurityGroupRequestPermissions, 0)
	for _, p := range req.Permissions {
		permissions = append(permissions, &ecs20140526V3.AuthorizeSecurityGroupRequestPermissions{
			Description:  tea.String(p.Description),
			IpProtocol:   tea.String(p.IpProtocol),
			Policy:       tea.String(p.Policy),
			PortRange:    tea.String(p.PortRange),
			Priority:     tea.String(fmt.Sprintf("%d", p.Priority)),
			SourceCidrIp: tea.String(p.SourceCidrIp),
		})
	}
	clientReq.SetPermissions(permissions)
	err = client.AuthorizeSecurityGroup(ctx, clientReq)
	if err != nil {
		return nil, err
	}

	// 创建成功后从阿里云同步回来
	aliSyncer := &synctask.AliyunSyncer{}
	if err := aliSyncer.SyncECSSecurityGroup(ctx, logrus.StandardLogger(), synctask.SyncOption{RegionID: sg.RegionID, IspID: sg.IspID}, req.SecurityGroupId); err != nil {
		return nil, err
	}
	return &cloudman.Result{
		Message: "创建规则成功",
	}, err
}

func (h SecurityGroup) BatchDeleteSecurityGroupRule(ctx context.Context, req *cloudman.BatchDeleteSecurityGroupRuleReq) (*cloudman.Result, error) {
	// 调用阿里云接口删除规则
	sg, err := models.SecurityGroupModel.GetBySecurityGroupID(ctx, req.SecurityGroupId)
	if err != nil {
		return nil, err
	}

	// 调用阿里云接口创建
	client, err := alicloud.CreateAliEcsClient(sg.RegionID, sg.IspID)
	if err != nil {
		return nil, err
	}
	err = client.RevokeSecurityGroup(ctx, sg.RegionID, req.SecurityGroupId, req.Ids)
	if err != nil {
		return nil, err
	}

	// 删除成功后从阿里云同步回来
	aliSyncer := &synctask.AliyunSyncer{}
	if err := aliSyncer.SyncECSSecurityGroup(ctx, logrus.StandardLogger(), synctask.SyncOption{RegionID: sg.RegionID, IspID: sg.IspID}, req.SecurityGroupId); err != nil {
		return nil, err
	}
	return &cloudman.Result{
		Message: "创建规则成功",
	}, err
}

func waitALBReadyAndSync(acl *entity.AlbACL, client *alicloud.AliLoadBalancerClient) {
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()
	waitTimeout := time.Minute * 5
	ticker := time.NewTicker(time.Second * 1)
	timeout := time.NewTimer(waitTimeout)
	aclID := acl.ACLID
	for {
		select {
		case <-timeoutCtx.Done():
			fmt.Printf("AclID: %s, 轮询因为context取消\n", aclID)
			return
		case <-timeout.C:
			fmt.Printf("AclID: %s, 轮询因为timeout取消\n", aclID)
			return
		case <-ticker.C:
			acls, err := client.ListALBAclByIDs(timeoutCtx, aclID)
			if err != nil {
				fmt.Printf("AclID: %s, 轮询因为err%s取消\n", aclID, err)
				return
			}
			if tea.StringValue(acls[0].AclStatus) == "Available" {
				aliSyncer := &synctask.AliyunSyncer{}
				err = aliSyncer.SyncAlbACL(timeoutCtx, logrus.StandardLogger(), synctask.SyncOption{RegionID: acl.RegionID, IspID: acl.IspID}, aclID)
				if err != nil {
					fmt.Printf("AclID: %s, 轮询完成但同步失败:%s\n", aclID, err)
				}
				fmt.Printf("AclID: %s, 轮询完成退出\n", aclID)
				return
			}
			fmt.Printf("AclID: %s, 状态后台轮询中...\n", aclID)
		}
	}
}

func (h SecurityGroup) AddALBAclsEntries(ctx context.Context, req *cloudman.AddALBAclsEntriesReq) (*cloudman.Result, error) {
	acl, err := models.AlbACLModel.GetByAclID(ctx, req.AclId)
	if err != nil {
		return nil, err
	}

	client, err := alicloud.NewAliLoadBalancerClient(acl.RegionID, acl.IspID)
	if err != nil {
		return nil, err
	}

	err = client.AddEntriesToAcl(ctx, req.AclId, req.Entries)
	if err != nil {
		return nil, err
	}

	// 更新acl状态为配置中
	err = models.AlbACLModel.UpdateAclStatusByID(ctx, acl.ACLID, "Configuring")
	if err != nil {
		return nil, err
	}

	// 后台轮询状态，超时为5分钟
	go waitALBReadyAndSync(acl, client)

	return &cloudman.Result{
		Message: "创建IP条目成功",
	}, nil
}

func (h SecurityGroup) RemoveALBAclsEntries(ctx context.Context, req *cloudman.RemoveALBAclsEntriesReq) (*cloudman.Result, error) {
	acl, err := models.AlbACLModel.GetByAclID(ctx, req.AclId)
	if err != nil {
		return nil, err
	}

	client, err := alicloud.NewAliLoadBalancerClient(acl.RegionID, acl.IspID)
	if err != nil {
		return nil, err
	}

	err = client.RemoveEntriesFromAcl(ctx, req.AclId, req.Entries)
	if err != nil {
		return nil, err
	}

	// 更新acl状态为配置中
	err = models.AlbACLModel.UpdateAclStatusByID(ctx, acl.ACLID, "Configuring")
	if err != nil {
		return nil, err
	}

	// 后台轮询状态，超时为5分钟
	go waitALBReadyAndSync(acl, client)

	return &cloudman.Result{
		Message: "删除IP条目成功",
	}, nil
}

func getSecCommonInfo(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq) ([]string, map[string]string, *entity.SecurityGroup, error) {
	instanceIds := []string{}
	instanceIdMap := map[string]string{}
	for _, info := range req.InstanceInfo {
		instanceIds = append(instanceIds, info.InstanceId)
		instanceIdMap[info.InstanceId] = info.InstanceName
	}
	// get security group id by name
	securityGroups, err := models.SecurityGroupModel.GetBySecurityName(ctx, req.RegionId, req.SecurityGroupName)
	if err != nil {
		return nil, nil, nil, err
	}
	if len(securityGroups) != 1 {
		return nil, nil, nil, fmt.Errorf("find more than one or none security group by name %s", req.SecurityGroupName)
	}

	return instanceIds, instanceIdMap, securityGroups[0], nil
}

func getWhitelistCommonInfo(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq) ([]string, map[string]string, *entity.IPWhitelist, error) {
	instanceIds := []string{}
	instanceIdMap := map[string]string{}
	for _, info := range req.InstanceInfo {
		instanceIds = append(instanceIds, info.InstanceId)
		instanceIdMap[info.InstanceId] = info.InstanceName
	}
	// get security group id by name
	securityGroups, err := models.IPWhitelistModel.GetByName(ctx, req.RegionId, req.SecurityGroupName)
	if err != nil {
		return nil, nil, nil, err
	}
	if len(securityGroups) != 1 {
		return nil, nil, nil, fmt.Errorf("find more than one or none security group by name %s", req.SecurityGroupName)
	}

	return instanceIds, instanceIdMap, securityGroups[0], nil
}

func doEcsSecurityGroups(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq, action string) (*cloudman.ModifySecurityGroupResult, error) {
	instanceIds, instanceIdMap, securityGroup, err := getSecCommonInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	hostModels, err := models.HostResourceModel.GerByRegionInstanceIDs(ctx, req.RegionId, instanceIds)
	if err != nil {
		return nil, err
	}
	if len(instanceIds) != len(hostModels) {
		return nil, fmt.Errorf("there are non-exists instance id")
	}
	// check instance_name and instance_id first
	for _, model := range hostModels {
		instanceName := instanceIdMap[model.InstanceID]
		if instanceName != model.InstanceName {
			return nil, fmt.Errorf("instance_id %s and instance_name %s is not match", model.InstanceID, instanceName)
		}
	}
	successIds := []string{}
	failedInfo := []*cloudman.FailedInfo{}
	for _, model := range hostModels {
		var err error
		if action == "join" {
			err = ecsJoinSecurityGroup(ctx, model, securityGroup.SecurityGroupID)
		} else if action == "leave" {
			err = ecsLeaveSecurityGroup(ctx, model, securityGroup.SecurityGroupID)
		} else {
			err = fmt.Errorf("illegal action: %s", action)
		}
		if err != nil {
			failedInfo = append(failedInfo, &cloudman.FailedInfo{
				InstanceId: model.InstanceID,
				Message:    err.Error(),
			})
			continue
		}
		successIds = append(successIds, model.InstanceID)
	}

	go func() {
		aliSyncer := &synctask.AliyunSyncer{}
		if len(successIds) == 0 {
			return
		}
		ispID := hostModels[0].IspID
		if err := aliSyncer.SyncHost(context.Background(), logrus.StandardLogger(), synctask.SyncOption{
			RegionID: req.RegionId, IspID: ispID}, successIds...); err != nil {
			logrus.Errorf("sync ecs after modify security groups failed")
			return
		}
		logrus.Info("sync ecs after modify security groups success")
	}()

	return &cloudman.ModifySecurityGroupResult{
		SuccessInstanceIds: successIds,
		FailedInfos:        failedInfo,
	}, nil
}

func doPolarDBSecurityGroups(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq, action string) (*cloudman.ModifySecurityGroupResult, error) {
	instanceIds, instanceIdMap, securityGroup, err := getSecCommonInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	models, err := models.MysqlClusterResourceModel.GetByRegionInstanceIDs(ctx, req.RegionId, instanceIds)
	if err != nil {
		return nil, err
	}
	if len(instanceIds) != len(models) {
		return nil, fmt.Errorf("there are non-exists instance id")
	}
	// check instance_name and instance_id first
	for _, model := range models {
		instanceName := instanceIdMap[model.DBClusterID]
		if instanceName != model.DBClusterDescription {
			return nil, fmt.Errorf("instance_id %s and instance_name %s is not match", model.DBClusterID, instanceName)
		}
	}
	successIds := []string{}
	failedInfo := []*cloudman.FailedInfo{}
	for _, model := range models {
		var err error
		if action == "join" {
			err = polardbJoinSecurityGroup(ctx, model, securityGroup.SecurityGroupID)
		} else if action == "leave" {
			err = polardbLeaveSecurityGroup(ctx, model, securityGroup.SecurityGroupID)
		} else {
			err = fmt.Errorf("illegal action: %s", action)
		}
		if err != nil {
			failedInfo = append(failedInfo, &cloudman.FailedInfo{
				InstanceId: model.DBClusterID,
				Message:    err.Error(),
			})
			continue
		}
		successIds = append(successIds, model.DBClusterID)
	}

	go func() {
		aliSyncer := &synctask.AliyunSyncer{}
		if len(successIds) == 0 {
			return
		}
		ispID := models[0].IspID
		if err := aliSyncer.SyncMysql(context.Background(), logrus.StandardLogger(), synctask.SyncOption{
			RegionID: req.RegionId, IspID: ispID}, successIds...); err != nil {
			logrus.Errorf("sync polardb after modify security groups failed")
			return
		}
		logrus.Info("sync polardb after modify security groups success")
	}()

	return &cloudman.ModifySecurityGroupResult{
		SuccessInstanceIds: successIds,
		FailedInfos:        failedInfo,
	}, nil
}

func doAlbSecurityGroups(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq, action string) (*cloudman.ModifySecurityGroupResult, error) {
	instanceIds, instanceIdMap, securityGroup, err := getSecCommonInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	if securityGroup.SecurityGroupType != "normal" {
		return nil, fmt.Errorf("only normal security group is permitted")
	}

	models, err := models.LoadBalancerModel.GetByRegionInstanceIDs(ctx, req.RegionId, instanceIds)
	if err != nil {
		return nil, err
	}
	if len(instanceIds) != len(models) {
		return nil, fmt.Errorf("there are non-exists instance id")
	}
	// check instance_name and instance_id first
	for _, model := range models {
		instanceName := instanceIdMap[model.LoadBalancerID]
		if instanceName != model.LoadBalancerName {
			return nil, fmt.Errorf("instance_id %s and instance_name %s is not match", model.LoadBalancerID, instanceName)
		}
	}
	successIds := []string{}
	failedInfo := []*cloudman.FailedInfo{}
	for _, model := range models {
		var err error
		if action == "join" {
			err = albJoinSecurityGroup(ctx, model, securityGroup.SecurityGroupID)
		} else if action == "leave" {
			err = albLeaveSecurityGroup(ctx, model, securityGroup.SecurityGroupID)
		} else {
			err = fmt.Errorf("illegal action: %s", action)
		}
		if err != nil {
			failedInfo = append(failedInfo, &cloudman.FailedInfo{
				InstanceId: model.LoadBalancerID,
				Message:    err.Error(),
			})
			continue
		}
		successIds = append(successIds, model.LoadBalancerID)
	}

	go func() {
		aliSyncer := &synctask.AliyunSyncer{}
		if len(successIds) == 0 {
			return
		}
		albClient, err := alicloud.NewAliLoadBalancerClient(req.RegionId, models[0].IspID)
		if err != nil {
			logrus.Errorf("sync alb after modify security groups failed")
			return
		}
		bgCtx := context.Background()
		// 串行等待lb就绪
		for _, instanceId := range instanceIds {
			albClient.WaitForReady(bgCtx, instanceId, 2*time.Minute)
			logrus.Infof("sync alb %s, alb is ready", instanceId)
		}
		ispID := models[0].IspID
		if err := aliSyncer.SyncApplicationLoadBalancer(bgCtx, logrus.StandardLogger(), synctask.SyncOption{
			RegionID: req.RegionId, IspID: ispID}, time.Now().Format("20060102150405"),
			successIds...); err != nil {
			logrus.Errorf("sync alb after modify security groups failed")
			return
		}
		logrus.Info("sync alb after modify security groups success")
	}()

	return &cloudman.ModifySecurityGroupResult{
		SuccessInstanceIds: successIds,
		FailedInfos:        failedInfo,
	}, nil
}

func doPolarDBWhitelists(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq, action string) (*cloudman.ModifySecurityGroupResult, error) {
	instanceIds, instanceIdMap, securityGroup, err := getWhitelistCommonInfo(ctx, req)
	if err != nil {
		return nil, err
	}

	models, err := models.MysqlClusterResourceModel.GetByRegionInstanceIDs(ctx, req.RegionId, instanceIds)
	if err != nil {
		return nil, err
	}
	if len(instanceIds) != len(models) {
		return nil, fmt.Errorf("there are non-exists instance id")
	}
	// check instance_name and instance_id first
	for _, model := range models {
		instanceName := instanceIdMap[model.DBClusterID]
		if instanceName != model.DBClusterDescription {
			return nil, fmt.Errorf("instance_id %s and instance_name %s is not match", model.DBClusterID, instanceName)
		}
	}
	successIds := []string{}
	failedInfo := []*cloudman.FailedInfo{}
	for _, model := range models {
		var err error
		if action == "join" {
			err = polardbJoinWhitelist(ctx, model, securityGroup.GlobalSecurityGroupID)
		} else if action == "leave" {
			err = polardbLeaveWhitelist(ctx, model, securityGroup.GlobalSecurityGroupID)
		} else {
			err = fmt.Errorf("illegal action: %s", action)
		}
		if err != nil {
			failedInfo = append(failedInfo, &cloudman.FailedInfo{
				InstanceId: model.DBClusterID,
				Message:    err.Error(),
			})
			continue
		}
		successIds = append(successIds, model.DBClusterID)
	}

	go func() {
		aliSyncer := &synctask.AliyunSyncer{}
		if len(successIds) == 0 {
			return
		}
		ispID := models[0].IspID
		if err := aliSyncer.SyncMysql(context.Background(), logrus.StandardLogger(), synctask.SyncOption{
			RegionID: req.RegionId, IspID: ispID}, successIds...); err != nil {
			logrus.Errorf("sync polardb after modify security groups failed")
			return
		}
		logrus.Info("sync polardb after modify security groups success")
	}()

	return &cloudman.ModifySecurityGroupResult{
		SuccessInstanceIds: successIds,
		FailedInfos:        failedInfo,
	}, nil
}

func (h SecurityGroup) JoinSecurityGroup(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq) (*cloudman.ModifySecurityGroupResult, error) {
	var res *cloudman.ModifySecurityGroupResult
	var err error
	switch req.InstanceType {
	case "ecs":
		res, err = doEcsSecurityGroups(ctx, req, "join")
	case "polardb":
		res, err = doPolarDBSecurityGroups(ctx, req, "join")
	case "alb":
		res, err = doAlbSecurityGroups(ctx, req, "join")
	case "polardb_whitelist":
		res, err = doPolarDBWhitelists(ctx, req, "join")
	default:
		return nil, fmt.Errorf("wrong instance_type: %s", req.InstanceType)
	}
	return res, err
}

func (h SecurityGroup) LeaveSecurityGroup(ctx context.Context, req *cloudman.JoinOrLeaveSecurityGroupReq) (*cloudman.ModifySecurityGroupResult, error) {
	var res *cloudman.ModifySecurityGroupResult
	var err error
	switch req.InstanceType {
	case "ecs":
		res, err = doEcsSecurityGroups(ctx, req, "leave")
	case "polardb":
		res, err = doPolarDBSecurityGroups(ctx, req, "leave")
	case "alb":
		res, err = doAlbSecurityGroups(ctx, req, "leave")
	case "polardb_whitelist":
		res, err = doPolarDBWhitelists(ctx, req, "leave")
	default:
		return nil, fmt.Errorf("wrong instance_type: %s", req.InstanceType)
	}
	return res, err
}

func ecsJoinSecurityGroup(ctx context.Context, ecsModel *entity.HostResource, newSecurityGroupId string) error {
	client, err := alicloud.CreateAliEcsClient(ecsModel.RegionID, ecsModel.IspID)
	if err != nil {
		return err
	}
	if slices.Contains(ecsModel.SecurityGroupIds.SecurityGroupID, newSecurityGroupId) {
		return fmt.Errorf("ecs already has security group id %s", newSecurityGroupId)
	}
	resp, err := client.JoinSecurityGroup(ctx, ecsModel.InstanceID, newSecurityGroupId)
	if err != nil {
		return err
	}
	logrus.Infof("bind ecs %s security group id success, resp: %v", ecsModel.InstanceName, resp)
	return nil
}

func ecsLeaveSecurityGroup(ctx context.Context, ecsModel *entity.HostResource, removeSecurityGroupId string) error {
	client, err := alicloud.CreateAliEcsClient(ecsModel.RegionID, ecsModel.IspID)
	if err != nil {
		return err
	}
	if !slices.Contains(ecsModel.SecurityGroupIds.SecurityGroupID, removeSecurityGroupId) {
		return fmt.Errorf("ecs does not contain this security group id %s", removeSecurityGroupId)
	}
	resp, err := client.LeaveSecurityGroup(ctx, ecsModel.InstanceID, removeSecurityGroupId)
	if err != nil {
		return err
	}

	logrus.Infof("remove ecs %s security group id success, resp: %v", ecsModel.InstanceName, resp)
	return nil
}

func polardbJoinSecurityGroup(ctx context.Context, polarDbModel *entity.MysqlClusterResource, newSecurityGroupId string) error {
	client := alicloud.CreateAliPolarDBClient(polarDbModel.RegionID, polarDbModel.IspID)
	if slices.Contains(polarDbModel.SecurityGroupIds.SecurityGroupID, newSecurityGroupId) {
		return fmt.Errorf("polardb already has security group id %s", newSecurityGroupId)
	}
	input := alicloud.ModifyDBClusterAccessWhitelistInput{}
	input.SetDBClusterId(polarDbModel.DBClusterID)
	newSecurityGroupIds := polarDbModel.SecurityGroupIds.SecurityGroupID
	newSecurityGroupIds = append(newSecurityGroupIds, newSecurityGroupId)
	input.SetSecurityGroupIds(strings.Join(newSecurityGroupIds, ","))
	input.SetWhiteListType("SecurityGroup")
	err := client.ModifyDBClusterAccessWhitelist(ctx, input)
	if err != nil {
		return err
	}
	logrus.Infof("bind polardb %s security group id success", polarDbModel.DBClusterDescription)
	return nil
}

func polardbLeaveSecurityGroup(ctx context.Context, polarDbModel *entity.MysqlClusterResource, removeSecurityGroupId string) error {
	client := alicloud.CreateAliPolarDBClient(polarDbModel.RegionID, polarDbModel.IspID)
	if !slices.Contains(polarDbModel.SecurityGroupIds.SecurityGroupID, removeSecurityGroupId) {
		return fmt.Errorf("polardb does not contains security group id %s", removeSecurityGroupId)
	}
	input := alicloud.ModifyDBClusterAccessWhitelistInput{}
	input.SetDBClusterId(polarDbModel.DBClusterID)
	newSecurityGroupIds := []string{}
	for _, id := range polarDbModel.SecurityGroupIds.SecurityGroupID {
		if id != removeSecurityGroupId {
			newSecurityGroupIds = append(newSecurityGroupIds, id)
		}
	}

	input.SetSecurityGroupIds(strings.Join(newSecurityGroupIds, ","))
	input.SetWhiteListType("SecurityGroup")
	err := client.ModifyDBClusterAccessWhitelist(ctx, input)
	if err != nil {
		return err
	}
	logrus.Infof("remove polardb %s security group id success", polarDbModel.DBClusterDescription)
	return nil
}

func polardbJoinWhitelist(ctx context.Context, polarDbModel *entity.MysqlClusterResource, newSecurityGroupId string) error {
	client := alicloud.CreateAliPolarDBClient(polarDbModel.RegionID, polarDbModel.IspID)
	ids := make([]string, 0)
	for _, w := range polarDbModel.IPWhitelists {
		ids = append(ids, w.GlobalSecurityGroupId)
	}

	if slices.Contains(ids, newSecurityGroupId) {
		return fmt.Errorf("polardb already has security group id %s", newSecurityGroupId)
	}
	input := alicloud.ModifyGlobalSecurityIPGroupRelationInput{}
	input.SetDBClusterId(polarDbModel.DBClusterID)
	newSecurityGroupIds := ids
	newSecurityGroupIds = append(newSecurityGroupIds, newSecurityGroupId)
	input.SetGlobalSecurityGroupId(strings.Join(newSecurityGroupIds, ","))
	input.SetRegionId(polarDbModel.RegionID)
	err := client.ModifyGlobalSecurityIPGroupRelation(ctx, input)
	if err != nil {
		return err
	}
	logrus.Infof("bind polardb %s security group id success", polarDbModel.DBClusterDescription)
	return nil
}

func polardbLeaveWhitelist(ctx context.Context, polarDbModel *entity.MysqlClusterResource, removeSecurityGroupId string) error {
	client := alicloud.CreateAliPolarDBClient(polarDbModel.RegionID, polarDbModel.IspID)
	ids := make([]string, 0)
	for _, w := range polarDbModel.IPWhitelists {
		ids = append(ids, w.GlobalSecurityGroupId)
	}
	if !slices.Contains(ids, removeSecurityGroupId) {
		return fmt.Errorf("polardb does not contains security group id %s", removeSecurityGroupId)
	}
	input := alicloud.ModifyGlobalSecurityIPGroupRelationInput{}
	input.SetDBClusterId(polarDbModel.DBClusterID)
	newSecurityGroupIds := []string{}
	for _, w := range polarDbModel.IPWhitelists {
		if w.GlobalSecurityGroupId != removeSecurityGroupId {
			newSecurityGroupIds = append(newSecurityGroupIds, w.GlobalSecurityGroupId)
		}
	}
	input.SetGlobalSecurityGroupId(strings.Join(newSecurityGroupIds, ","))
	input.SetRegionId(polarDbModel.RegionID)
	err := client.ModifyGlobalSecurityIPGroupRelation(ctx, input)
	if err != nil {
		return err
	}
	logrus.Infof("remove polardb %s security group id success", polarDbModel.DBClusterDescription)
	return nil
}

func albJoinSecurityGroup(ctx context.Context, albModel *entity.LoadBalancer, newSecurityGroupId string) error {
	client, err := alicloud.NewAliLoadBalancerClient(albModel.RegionID, albModel.IspID)
	if err != nil {
		return err
	}
	if albModel.SecurityGroupIds != nil && slices.Contains(*albModel.SecurityGroupIds, newSecurityGroupId) {
		return fmt.Errorf("ecs already has security group id %s", newSecurityGroupId)
	}
	err = client.JoinSecurityGroup(ctx, albModel.LoadBalancerID, []string{newSecurityGroupId})
	if err != nil {
		return err
	}
	logrus.Infof("bind alb %s security group id success", albModel.LoadBalancerName)
	return nil
}

func albLeaveSecurityGroup(ctx context.Context, albModel *entity.LoadBalancer, removeSecurityGroupId string) error {
	client, err := alicloud.NewAliLoadBalancerClient(albModel.RegionID, albModel.IspID)
	if err != nil {
		return err
	}
	if albModel.SecurityGroupIds == nil || !slices.Contains(*albModel.SecurityGroupIds, removeSecurityGroupId) {
		return fmt.Errorf("alb does not contain this security group id %s", removeSecurityGroupId)
	}
	err = client.LeaveSecurityGroup(ctx, albModel.LoadBalancerID, []string{removeSecurityGroupId})
	if err != nil {
		return err
	}
	logrus.Infof("remove alb %s security group id success", albModel.LoadBalancerName)
	return nil
}
