package service

import (
	"context"
	"fmt"
	"math"
	"net"
	"strings"
	"time"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	notifySdk "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/op-notifyman-sdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// System System-hanlder
type System struct {
}

// GetConfig 获取系统配置
func (s System) GetConfig(ctx context.Context, empty *cloudman.Empty) (*cloudman.SysConfig, error) {
	conf := cfg.GetSystemConfig()
	return &cloudman.SysConfig{
		EnableWorkOrder:      conf.EnableWorkOrder,
		ShowTagValue:         conf.ShowTagValue,
		AutoRefreshOpsStatus: conf.AutoRefreshOpsStatus,
	}, nil
}

// WecomDailyNotify -
func (s System) WecomDailyNotify(ctx context.Context, empty *cloudman.Empty) (*cloudman.Empty, error) {
	// 查询
	list, _, err := models.ResChangelistModel.Query(ctx, &schema.ResChangelistQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: 9999,
		},
		ResChangelistColumnParam: schema.ResChangelistColumnParam{
			Field: "_add,_del",
			Start: int32(time.Now().Unix()) - 86400,
			End:   int32(time.Now().Unix()),
		},
	})
	if err != nil {
		return nil, err
	}
	// 计算
	addCount := map[string]int32{}
	delCount := map[string]int32{}
	addMap := map[string]map[string]int32{}
	delMap := map[string]map[string]int32{}
	specMap := map[string]string{}
	for _, item := range list {
		dbDump := ""
		if item.Field == "_add" {
			dbDump = item.After
		} else if item.Field == "_del" {
			dbDump = item.Before
		}
		dbObj := gjson.Parse(dbDump)
		spec := ""
		if item.ResourceType == "host" {
			spec = dbObj.Get("InstanceType").String()
			cpu := dbObj.Get("CPU").Int()
			mem := math.Floor(dbObj.Get("Memory").Float()/1024 + 0.5)
			if cpu == 0 && mem == 0 {
				specMap[spec] = "无规格实例"
			} else {
				specMap[spec] = fmt.Sprintf("%vC%vG", cpu, mem)
			}
		} else if item.ResourceType == "mysql" {
			spec = dbObj.Get("DBNodeClass").String()
		} else if item.ResourceType == "redis" {
			spec = dbObj.Get("InstanceClass").String()
		}
		if item.Field == "_add" {
			if len(addMap[item.ResourceType]) == 0 {
				addMap[item.ResourceType] = map[string]int32{}
			}
			addMap[item.ResourceType][spec]++
			addCount[item.ResourceType]++
		} else if item.Field == "_del" {
			if len(delMap[item.ResourceType]) == 0 {
				delMap[item.ResourceType] = map[string]int32{}
			}
			delMap[item.ResourceType][spec]++
			delCount[item.ResourceType]++
		}
	}

	// 格式化
	sendMsg := ""
	for i, rt := range []string{"host", "mysql", "redis"} {
		rtName := []string{"主机", "数据库集群", "缓存集群"}
		if addCount[rt] > 0 {
			sendMsg += fmt.Sprintf("%s新增：%d\n", rtName[i], addCount[rt])
			sendMsg += ">"
			for spec, count := range addMap[rt] {
				if rt == "host" {
					sendMsg += fmt.Sprintf("%s (%s): %d\n", spec, specMap[spec], count)
				} else {
					sendMsg += fmt.Sprintf("%s: %d\n", spec, count)
				}
			}
			sendMsg += "\n\n"
		}
		if delCount[rt] > 0 {
			sendMsg += fmt.Sprintf("%s减少：%d\n", rtName[i], delCount[rt])
			sendMsg += ">"
			for spec, count := range delMap[rt] {
				if rt == "host" {
					sendMsg += fmt.Sprintf("%s (%s): %d\n", spec, specMap[spec], count)
				} else {
					sendMsg += fmt.Sprintf("%s: %d\n", spec, count)
				}
			}
			sendMsg += "\n\n"
		}
	}
	sendMsg = strings.TrimSpace(sendMsg)
	if sendMsg == "" {
		return &cloudman.Empty{}, nil
	}
	if cfg.GetSystemConfig().Cluster != "" {
		sendMsg = fmt.Sprintf("统计范围：过去24小时；集群: %s\n\n\n", cfg.GetSystemConfig().Cluster) + sendMsg
	}
	sendMsg, _ = sjson.Set("", "msg", sendMsg)
	// 开始发送
	config, ok := cfg.GetNotifyCfg().Items["changeList"]
	if ok {
		err := notifySdk.SendMessage(context.Background(), &notifySdk.QNotify{
			Name:             "云管平台资源变更提醒",
			TemplateMsg:      sendMsg,
			MikuUser:         config.MikuUser,
			PlateFromID:      config.PlateFromID,
			ToolID:           config.ToolID,
			TemplateID:       config.TemplateID,
			NotifyManAddress: cfg.GetNotifyCfg().NotifyAddress,
		})
		if err != nil {
			logger.Errorf("sendChangeListMessage.SendNotice.error: %s", err.Error())
		}
	}
	return &cloudman.Empty{}, nil
}

// SecurityGroupRuleDailyNotify -
func (s System) SecurityGroupRuleDailyNotify(ctx context.Context, empty *cloudman.Empty) (*cloudman.Empty, error) {
	// 查询
	res, err := SecurityGroup{}.Describe(ctx, &cloudman.DescribeSecurityGroupReq{
		Page: 1,
		Size: math.MaxInt32,
	})
	if err != nil {
		return nil, err
	}
	var dangerSG []*cloudman.SecurityGroupEntity
	var dangerPermNum int = 0
	for _, sg := range res.List {
		var dangerPerms []*cloudman.SecurityGroupPermission
		for _, perm := range sg.Permissions {
			if perm.SourceCidrIp != "" {
				isPublic := false
				IP := net.ParseIP(perm.SourceCidrIp)
				if IP != nil {
					isPublic = isPublicIP(IP)
				} else {
					_, IPNet, _ := net.ParseCIDR(perm.SourceCidrIp)
					isPublic = isPublicIP(IPNet.IP)
				}
				// 告警具体规则
				if isPublic && perm.PortRange == "0/65535" && perm.Priority == 1 {
					dangerPerms = append(dangerPerms, perm)
				}
			}
		}
		if len(dangerPerms) != 0 {
			dangerPermNum += len(dangerPerms)
			sg.Permissions = dangerPerms
			dangerSG = append(dangerSG, sg)
		}
	}

	if len(dangerSG) == 0 {
		return &cloudman.Empty{}, nil
	}
	// 格式化
	for i := 0; i <= len(dangerSG)/10; i++ {
		sendMsg := fmt.Sprintf("--- %d / %d --- \n", i+1, len(dangerSG)/10+1)
		if i == 0 {
			sendMsg := fmt.Sprintf("判定规则:公网IP或IP段、开放端口为0/65535，且优先级为1的规则\n")
			sendMsg += fmt.Sprintf("异常安全组数量:%d 异常规则数量:%d\n", len(dangerSG), dangerPermNum)
		}
		groupStart := i * 10
		groupEnd := groupStart + 10
		if groupEnd > len(dangerSG) { // 如果下标越界则用数组的长度作为结束下标
			groupEnd = len(dangerSG)
		}
		groupSG := dangerSG[groupStart:groupEnd]
		for _, sg := range groupSG {
			sendMsg += fmt.Sprintf("**%s(%s): %d条**\n", sg.SecurityGroupName, sg.SecurityGroupId, len(sg.Permissions))
			showCount := 0
			for _, perm := range sg.Permissions {
				sendMsg += fmt.Sprintf("来源:%s 端口:%s\n", perm.SourceCidrIp, perm.PortRange)
				showCount++
				if showCount == 5 {
					sendMsg += "...\n"
					break
				}
			}
			sendMsg += "\n\n"
		}
		sendMsg, _ = sjson.Set("", "msg", sendMsg)
		config, ok := cfg.GetNotifyCfg().Items["sgRule"]
		if ok {
			err := notifySdk.SendMessage(context.Background(), &notifySdk.QNotify{
				Name:             "安全组规则异常告警",
				TemplateMsg:      sendMsg,
				MikuUser:         config.MikuUser,
				PlateFromID:      config.PlateFromID,
				ToolID:           config.ToolID,
				TemplateID:       config.TemplateID,
				NotifyManAddress: cfg.GetNotifyCfg().NotifyAddress,
			})
			if err != nil {
				logger.Errorf("sendSecurityGroupRuleMessage.SendNotice.error: %s", err.Error())
			}
		}
	}

	return &cloudman.Empty{}, nil
}

func isPublicIP(IP net.IP) bool {
	if IP.IsLoopback() || IP.IsLinkLocalMulticast() || IP.IsLinkLocalUnicast() {
		return false
	}
	if ip4 := IP.To4(); ip4 != nil {
		switch true {
		case ip4[0] == 10:
			return false
		case ip4[0] == 172 && ip4[1] >= 16 && ip4[1] <= 31:
			return false
		case ip4[0] == 192 && ip4[1] == 168:
			return false
		default:
			return true
		}
	}
	return false
}
