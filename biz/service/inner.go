package service

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/colorlog"
)

// Inner 内部通信使用
type Inner struct {
}

func (i Inner) flushLog(taskLogID string, result string) error {
	return models.TaskLogResultModel.Create(context.Background(), &entity.TaskLogResult{
		TaskLogID:  taskLogID,
		Stopped:    true,
		TaskResult: result,
	})
}

// RunTaskAPI rpc 运行任务
func (i Inner) RunTaskAPI(ctx context.Context, req *cloudman.RunTaskReq) (*cloudman.RunTaskResponse, error) {
	err := runSyncTask(ctx, req.Oid, true)
	if err != nil {
		return nil, err
	}
	return &cloudman.RunTaskResponse{Status: "success"}, nil
}

func newResultLogger(hook LogResultHook) *logrus.Logger {
	logg := logrus.New()
	logg.SetFormatter(LogResultFormat{})
	logg.SetLevel(logrus.DebugLevel)

	logg.AddHook(hook)
	return logg
}

// LogResultFormat 日志执行结果格式化
type LogResultFormat struct{}

// Format 格式化任务执行结果
func (l LogResultFormat) Format(entry *logrus.Entry) ([]byte, error) {
	res := entry.Message
	if entry.Level == logrus.InfoLevel {
		res = colorlog.INFO(res)
	}

	if entry.Level == logrus.ErrorLevel {
		res = colorlog.Error(res)
	}
	msg := fmt.Sprintln(res)
	return []byte(msg[:len(msg)-1]), nil
}

// LogResultHook 日志执行hook
type LogResultHook struct {
	TaskLogResultModel func(meta *entity.TaskLogResult) error
	TaskLogID          string
}

// Levels 输出等级
func (l LogResultHook) Levels() []logrus.Level {
	return []logrus.Level{
		logrus.ErrorLevel,
		logrus.InfoLevel,
		logrus.DebugLevel,
		logrus.WarnLevel,
	}
}

// Fire entry
func (l LogResultHook) Fire(entry *logrus.Entry) error {
	res := entry.Message
	if entry.Level == logrus.InfoLevel {
		res = colorlog.INFO(res)
	}

	if entry.Level == logrus.ErrorLevel {
		res = colorlog.Error(res)
	}

	return l.TaskLogResultModel(&entity.TaskLogResult{
		TaskLogID:  l.TaskLogID,
		TaskResult: res,
	})
}
