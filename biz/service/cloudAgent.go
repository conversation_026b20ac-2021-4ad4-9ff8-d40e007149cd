package service

import (
	"context"
	"strings"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// CloudAgent 账户rpc
type CloudAgent struct {
}

// TaskList ...
func (a CloudAgent) TaskList(ctx context.Context, req *cloudman.CloudAgentTaskListReq) (*cloudman.CloudAgentTaskListResp, error) {
	list, total, err := models.AgentTaskModel.Query(ctx, &schema.AgentTaskQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.<PERSON>,
		},
		AgentTaskColumnParam: schema.AgentTaskColumnParam{
			AccountID:    req.AccountId,
			Risky:        req.Risky,
			Result:       req.Result,
			ResourceType: req.ResourceType,
			Start:        req.Start,
			End:          req.End,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var res []*cloudman.CloudAgentTaskBrief
	for _, v := range list {
		item := &cloudman.CloudAgentTaskBrief{
			Id:             v.ID.Hex(),
			AccountId:      v.AccountID,
			RegionName:     v.Key.STSApply.Region,
			ResourceType:   v.ResourceType,
			MethodName:     v.MethodName,
			RunType:        v.RunType,
			Risky:          v.Extra.Risky,
			Operator:       v.Operator,
			SubmitTime:     v.Extra.SubmitTime / 1e9,
			AgentIp:        v.Extra.AgentIP,
			Result:         v.Status,
			BusinessResult: v.Extra.BusinessErrMsg == "",
		}
		res = append(res, item)
	}
	return &cloudman.CloudAgentTaskListResp{
		Data:  res,
		Count: int32(total),
	}, nil
}

// TaskDetail ...
func (a CloudAgent) TaskDetail(ctx context.Context, req *cloudman.IDReq) (*cloudman.CloudAgentTaskDetailResp, error) {
	data, err := models.AgentTaskModel.FindPK(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	dAk, _ := utils.DecryptStrByAes(data.Key.STSResult.Ak, constant.AccountSecret)
	dSk, _ := utils.DecryptStrByAes(data.Key.STSResult.Sk, constant.AccountSecret)
	dToken, _ := utils.DecryptStrByAes(data.Key.STSResult.Token, constant.AccountSecret)
	resp := &cloudman.CloudAgentTaskDetailResp{
		Id:               data.ID.Hex(),
		AccountId:        data.AccountID,
		RegionName:       data.Key.STSApply.Region,
		ResourceType:     data.ResourceType,
		MethodName:       data.MethodName,
		RunType:          data.RunType,
		Risky:            data.Extra.Risky,
		Operator:         data.Operator,
		SubmitTime:       data.Extra.SubmitTime / 1e6,
		StartTime:        data.Extra.StartTime / 1e6,
		EndTime:          data.Extra.EndTime / 1e6,
		CallbackTime:     data.Extra.CallbackTime / 1e6,
		Result:           data.Status,
		ErrorMsg:         data.ErrMsg,
		BusinessResult:   data.Extra.BusinessErrMsg == "",
		BusinessErrorMsg: data.Extra.BusinessErrMsg,
		RequestDump:      data.RequestDump,
		ResponseDump:     data.ResponseDump,
		AgentIp:          data.Extra.AgentIP,
		AgentId:          data.Extra.AgentID,
		AccessKey:        dAk,
		AccessSecret:     hideSecretKey(dSk),
		AccessToken:      hideSecretKey(dToken),
		KeyExpireTime:    data.Key.STSResult.Expiration,
		KeyPolicy:        data.Key.STSApply.Policy,
	}
	return resp, nil
}

func hideSecretKey(s string) string {
	if len(s) <= 6 {
		return "*****"
	}
	if len(s) <= 10 {
		return s[0:2] + "*****" + s[len(s)-2:]
	}
	if strings.HasPrefix(s, "STS") {
		return s[0:8] + "*****" + s[len(s)-4:]
	}
	return s[0:4] + "*****" + s[len(s)-4:]
}

// ResChangelist -
func (a CloudAgent) ResChangelist(ctx context.Context, req *cloudman.ResChangelistReq) (*cloudman.ResChangelistResp, error) {
	list, total, err := models.ResChangelistModel.Query(ctx, &schema.ResChangelistQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		ResChangelistColumnParam: schema.ResChangelistColumnParam{
			AccountID:    req.AccountId,
			InstanceID:   req.InstanceId,
			InstanceName: req.InstanceName,
			ResourceType: req.ResourceType,
			Field:        req.Field,
			Start:        req.Start,
			End:          req.End,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var res []*cloudman.ResChangelist
	for _, v := range list {
		item := &cloudman.ResChangelist{
			Id:           v.ID.Hex(),
			AccountId:    v.AccountID,
			ResourceType: v.ResourceType,
			InstanceId:   v.InstanceID,
			InstanceName: v.InstanceName,
			Field:        v.Field,
			Before:       v.Before,
			After:        v.After,
			CreatedTime:  int32(v.CreatedTime),
		}
		res = append(res, item)
	}
	return &cloudman.ResChangelistResp{
		Data:  res,
		Count: int32(total),
	}, nil
}
