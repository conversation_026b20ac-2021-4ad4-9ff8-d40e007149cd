package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"runtime/debug"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/kr/pretty"
	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hotwheel"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jumpserversdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/notification"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/render"

	ecs20140526 "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	ec2Types "github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/ssm"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudutils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cmdb"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jobman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"

	"go.mongodb.org/mongo-driver/bson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	cloudmanCore "platgit.mihoyo.com/jql-ops/op-cloudman/core"
	"platgit.mihoyo.com/jql-ops/op-cloudman/core/createinstance"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/ferry"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mdhelper"
	notifySdk "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/op-notifyman-sdk"
	provider "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/types"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// ResTemplate 资源组模板
type ResTemplate struct {
}

// GetSalePrice 获取订单售价
func (r ResTemplate) GetSalePrice(ctx context.Context, req *cloudman.ResTemCreateReq) (*cloudman.SalePrice, error) {
	// 当前仅支持host询价
	if req.Type != "host" {
		return nil, fmt.Errorf("当前仅支持host询价")
	}
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribePrice(ctx, "instance", []byte(req.Info))
}

type UpdateOrderInfo struct {
	IspId       string
	RegionId    string
	InstanceIds []string
}

func getHostOrderInfos(ctx context.Context, instanceInfos []*cloudman.InstanceIdNamePair) ([]*UpdateOrderInfo, error) {
	instanceMap := make(map[string]string)
	instanceIds := make([]string, 0)
	for _, info := range instanceInfos {
		instanceMap[info.InstanceId] = info.InstanceName
		instanceIds = append(instanceIds, info.InstanceId)
	}
	hostModels, err := models.HostResourceModel.GetByInstanceIDs(ctx, instanceIds)
	if err != nil {
		return nil, err
	}
	if len(hostModels) != len(instanceInfos) {
		return nil, fmt.Errorf("the number of instanceIDs/instanceNames and models are not matched")
	}

	orderInfoMap := map[string]*UpdateOrderInfo{}
	for _, host := range hostModels {
		if instanceName := instanceMap[host.InstanceID]; instanceName != host.InstanceName {
			return nil, fmt.Errorf("the instance name %s, input instance name %s is not matched", host.InstanceName, instanceName)
		}

		ispID := host.IspID
		regionID := host.RegionID
		orderInfo, ok := orderInfoMap[fmt.Sprintf("%s_%s", ispID, regionID)]
		if !ok {
			orderInfo = &UpdateOrderInfo{
				IspId:       ispID,
				RegionId:    regionID,
				InstanceIds: []string{},
			}
		}
		orderInfo.InstanceIds = append(orderInfo.InstanceIds, host.InstanceID)
		orderInfoMap[fmt.Sprintf("%s_%s", ispID, regionID)] = orderInfo
	}

	res := make([]*UpdateOrderInfo, 0)
	for _, orderInfo := range orderInfoMap {
		res = append(res, orderInfo)
	}
	return res, nil
}

func getPolardbOrderInfos(ctx context.Context, instanceInfos []*cloudman.InstanceIdNamePair) ([]*UpdateOrderInfo, error) {
	instanceMap := make(map[string]string)
	instanceIds := make([]string, 0)
	for _, info := range instanceInfos {
		instanceMap[info.InstanceId] = info.InstanceName
		instanceIds = append(instanceIds, info.InstanceId)
	}
	models, err := models.MysqlClusterResourceModel.GetByInstanceIDs(ctx, instanceIds)
	if err != nil {
		return nil, err
	}
	if len(models) != len(instanceInfos) {
		return nil, fmt.Errorf("the number of instanceIDs/instanceNames and models are not matched")
	}

	orderInfoMap := map[string]*UpdateOrderInfo{}
	for _, host := range models {
		if instanceName := instanceMap[host.DBClusterID]; instanceName != host.DBClusterDescription {
			return nil, fmt.Errorf("the instance name %s, input instance name %s is not matched", host.DBClusterDescription, instanceName)
		}

		ispID := host.IspID
		regionID := host.RegionID
		orderInfo, ok := orderInfoMap[fmt.Sprintf("%s_%s", ispID, regionID)]
		if !ok {
			orderInfo = &UpdateOrderInfo{
				IspId:       ispID,
				RegionId:    regionID,
				InstanceIds: []string{},
			}
		}
		orderInfo.InstanceIds = append(orderInfo.InstanceIds, host.DBClusterID)
		orderInfoMap[fmt.Sprintf("%s_%s", ispID, regionID)] = orderInfo
	}

	res := make([]*UpdateOrderInfo, 0)
	for _, orderInfo := range orderInfoMap {
		res = append(res, orderInfo)
	}
	return res, nil
}

func getRedisOrderInfos(ctx context.Context, instanceInfos []*cloudman.InstanceIdNamePair) ([]*UpdateOrderInfo, error) {
	instanceMap := make(map[string]string)
	instanceIds := make([]string, 0)
	for _, info := range instanceInfos {
		instanceMap[info.InstanceId] = info.InstanceName
		instanceIds = append(instanceIds, info.InstanceId)
	}
	models, err := models.RedisResourceModel.GetByInstanceIDs(ctx, instanceIds)
	if err != nil {
		return nil, err
	}
	if len(models) != len(instanceInfos) {
		return nil, fmt.Errorf("the number of instanceIDs/instanceNames and models are not matched")
	}

	orderInfoMap := map[string]*UpdateOrderInfo{}
	for _, host := range models {
		if instanceName := instanceMap[host.InstanceID]; instanceName != host.InstanceName {
			return nil, fmt.Errorf("the instance name %s, input instance name %s is not matched", host.InstanceName, instanceName)
		}

		ispID := host.IspID
		regionID := host.RegionID
		orderInfo, ok := orderInfoMap[fmt.Sprintf("%s_%s", ispID, regionID)]
		if !ok {
			orderInfo = &UpdateOrderInfo{
				IspId:       ispID,
				RegionId:    regionID,
				InstanceIds: []string{},
			}
		}
		orderInfo.InstanceIds = append(orderInfo.InstanceIds, host.InstanceID)
		orderInfoMap[fmt.Sprintf("%s_%s", ispID, regionID)] = orderInfo
	}

	res := make([]*UpdateOrderInfo, 0)
	for _, orderInfo := range orderInfoMap {
		res = append(res, orderInfo)
	}
	return res, nil
}

// UpdateResources 批量更新资源
func (r ResTemplate) UpdateResources(ctx context.Context, req *cloudman.UpdateResReq) (*cloudman.UpdateResourcesResp, error) {
	var orderInfo []*UpdateOrderInfo
	var err error
	// 接口这里根据RegionId, IspId做分组来创建更新工单
	switch req.InstanceType {
	case "host":
		orderInfo, err = getHostOrderInfos(ctx, req.InstanceInfos)
	case "mysql":
		orderInfo, err = getPolardbOrderInfos(ctx, req.InstanceInfos)
	case "redis":
		orderInfo, err = getRedisOrderInfos(ctx, req.InstanceInfos)
	default:
		err = fmt.Errorf("wrong instance type: %s", req.InstanceType)
	}
	if err != nil {
		return nil, err
	}

	failedMessage := []string{}
	orderIDs := []string{}
	for _, info := range orderInfo {
		orderID, err := createUpdateOrder(ctx, req, info)
		if err != nil {
			failedMessage = append(failedMessage, err.Error())
			continue
		}
		orderIDs = append(orderIDs, orderID)
	}

	return &cloudman.UpdateResourcesResp{
		Message: strings.Join(failedMessage, ","),
		OrderId: orderIDs,
	}, nil
}

func createUpdateOrder(ctx context.Context, req *cloudman.UpdateResReq, info *UpdateOrderInfo) (string, error) {
	account, err := models.AccountModel.Get(ctx, info.IspId)
	if err != nil {
		return "", err
	}
	pr := &common.InitProvider{
		RegionID: info.RegionId,
		IspType:  account.AType,
		IspID:    account.ID.Hex(),
		IspName:  account.Name,
	}
	provideName := pr.IspType + "_" + req.InstanceType
	f, ok := provider.ProvideMap[provideName]
	if !ok {
		return "", errors.New("not found provider")
	}
	form := ResourceForm{
		Action:       schema.ResourceAction(req.Action),
		IspID:        info.IspId,
		InstanceType: req.InstanceType,
		RegionID:     info.RegionId,
		RawData:      req.ActionData,
		Reason:       req.Reason,
	}

	instanceIds := make([]string, 0)
	for _, info := range req.InstanceInfos {
		instanceIds = append(instanceIds, info.InstanceId)
	}

	actionData := common.ActionData{
		IsForce: false,
	}
	err = json.Unmarshal([]byte(req.ActionData), &actionData)
	if err != nil {
		logger.Errorf("cannot unmarshal action data, err: %v", err)
	}

	operateForm := common.OperateForm{
		Action:         req.Action,
		Instances:      instanceIds,
		RetryInstances: []string{},
		IsForce:        actionData.IsForce,
	}
	if actionData.IsLevel2Backup != nil {
		operateForm.IsLevel2Backup = actionData.IsLevel2Backup
	}
	formData, err := json.Marshal(operateForm)
	if err != nil {
		return "", err
	}

	order, err := createOrder(ctx, form, pr, "")
	if err != nil {
		return "", err
	}
	pFunc, err := f(*pr)
	if err != nil {
		return "", err
	}

	_ = models.ResourceOrder.Update(ctx, order.ID.Hex(), map[string]interface{}{"status": constant.TaskRunning, "start_time": time.Now().Unix()})
	go func() {
		bgCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
		err := pFunc.UpdateInstance(bgCtx, time.Minute*30, order.ID.Hex(), formData)
		if err != nil {
			_ = models.ResourceOrder.Update(bgCtx, order.ID.Hex(), map[string]interface{}{"status": constant.TaskErr, "end_time": time.Now().Unix(), "over_time": time.Now().Unix()})
			pr.Logger.Errorf("update instance failed, err: %v", err)
			return
		}
		_ = models.ResourceOrder.Update(bgCtx, order.ID.Hex(), map[string]interface{}{"status": constant.TaskSuccess, "end_time": time.Now().Unix(), "over_time": time.Now().Unix()})
	}()

	return order.ID.Hex(), nil
}

// DryRun 参数预校验
func (r ResTemplate) DryRun(ctx context.Context, req *cloudman.CreateResReq) (*cloudman.Result, error) {
	return &cloudman.Result{Message: "参数预校验通过"}, nil
}

// createResourceByTpl 通过资源模板创建订单
func (r ResTemplate) createResourceByTpl(ctx context.Context, req *cloudman.CreateResReq) (*cloudman.CreateResReq, error) {
	// 模板传递格式需要修改,如vSwitchId,VpcId等
	tpl, err := models.ResourceTemplate.Get(ctx, req.TemplateId)
	if err != nil {
		return nil, err
	}

	// 云主机需要替换创建参数, Amount
	if tpl.Type == models.HostResTpl {
		var hostTplCreateFormData schema.HostTplCreateFormData
		err = json.Unmarshal([]byte(req.Data), &hostTplCreateFormData)
		if err != nil {
			return nil, err
		}

		err = validator.Validate.StructCtx(ctx, hostTplCreateFormData)
		if err != nil {
			return nil, err
		}

		var hostRaw types.Host
		err = json.Unmarshal([]byte(tpl.Info), &hostRaw)
		hostRaw.Amount = hostTplCreateFormData.Amount
		hostRaw.HostName = hostTplCreateFormData.HostName
		buf, _ := json.Marshal(hostRaw)
		req.Data = string(buf)
	}

	return req, nil
}

// CreateResource 创建资源
func (r ResTemplate) CreateResource(ctx context.Context, req *cloudman.CreateResReq) (result *cloudman.CommonResourceResp, err error) {
	form := ResourceForm{
		Action:       schema.ResourceCreate,
		IspID:        req.IspId,
		RegionPK:     req.Rid,
		InstanceType: req.Type,
		RegionID:     "",
		RawData:      req.Data,
		Reason:       req.Reason,
		InitOption:   req.InitOption,
	}
	// 获取template校验数据合法性
	// 通过资源模板创建时拼凑提交表单
	if req.TemplateId != "" {
		tpl, err := models.ResourceTemplate.Get(ctx, req.TemplateId)
		if err != nil {
			return nil, err
		}
		form.TemplateId = &req.TemplateId
		form.TemplateName = &tpl.Name
	}
	pr, err := operateResourceBefore(ctx, form)
	if err != nil {
		return nil, err
	}
	provideName := pr.IspType + "_" + req.Type
	f, ok := provider.ProvideMap[provideName]
	if !ok {
		return nil, errors.New("not found provider")
	}
	// 调用检查功能，避免提交到后台才报错的问题
	pFunc, err := f(*pr, true)
	if err != nil {
		return nil, err
	}

	// dry-run create
	_, err = pFunc.CreateInstance(ctx, 5*time.Second, "", []byte(req.Data))
	if err != nil {
		return nil, err
	}

	order, err := createOrder(ctx, form, pr, "")
	if err != nil {
		return nil, err
	}

	// 写入占位数据 m
	if err := createinstance.ModelMap[req.Type].HoldInstancePlace(ctx, []byte(req.Data), order.ID.Hex(), order.IspType); err != nil {
		return nil, err
	}

	go func(ctx context.Context) {
		r.createInstance(ctx, order, req.InitOption)
	}(context.Background())
	return &cloudman.CommonResourceResp{Message: "订单已提交至后台", OrderId: order.ID.Hex()}, nil
	// 订单变化已注册钩子
}

func orderDescribeToMD(rawData string) (string, error) {
	var mp map[string]interface{}
	if err := json.Unmarshal([]byte(rawData), &mp); err != nil {
		return rawData, err
	}
	md := mdhelper.NewMarkdown()
	md.SetLineSplit("\\n")
	var s [][]string
	for key, value := range mp {
		valueStr := fmt.Sprintf("%v", value)
		valueStr = strings.Replace(valueStr, "\\", "\\\\", -1)
		valueStr = strings.Replace(valueStr, "\"", "\\\"", -1)
		valueStr = strings.Replace(valueStr, "\n", "<br />", -1)
		valueStr = strings.Replace(valueStr, "\t", "&nbsp;&nbsp;&nbsp;&nbsp;", -1)
		valueStr = strings.Replace(valueStr, "|", "&#124;", -1)
		s = append(s, []string{key, valueStr})
	}
	md.SetTable([]string{"字段", "值"}, s)
	return md.String(), nil
}

func (r ResTemplate) createInstance(ctx context.Context, order *entity.ResOrder, initOption *cloudman.InitOption) error {
	var err error
	defer func() {
		operateResourceAfter(order, err)
	}()
	err = models.ResourceOrder.Update(ctx, order.ID.Hex(), map[string]int32{"status": constant.TaskRunning})
	if err != nil {
		return err
	}
	logger := BuildLogger(order.ID.Hex())
	provideName := order.IspType + "_" + order.Type
	f, ok := provider.ProvideMap[provideName]
	if !ok {
		return fmt.Errorf("not found provider")
	}
	logger.Infof("初始化provider")
	pr, err := getIspInfo(ctx, order.IspID, "", order.RegionID)
	pr.SetLogger(logger)
	pFunc, err := f(*pr)
	if err != nil {
		pr.Logger.Errorf("初始化provider失败:%v", err.Error())
		return err
	}

	logger.Infof("************ 1. 开始创建实例 ************")
	pr.Logger.Infof("创建实例云调用开始")
	_ = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{
		"status":     constant.TaskRunning,
		"start_time": time.Now().Unix(),
		// 创建之前释放，避免主机名规则匹配到占位的机器
	})
	err = createinstance.ModelMap[order.Type].ReleaseInstancePlace(ctx, []byte(order.RawData), order.ID.Hex())
	if err != nil {
		pr.Logger.Errorf("释放失败:%v", err.Error())
	}
	createdInstances, err := pFunc.CreateInstance(ctx, 60*time.Minute, order.ID.Hex(), []byte(order.RawData))

	if err != nil {
		pr.Logger.Errorf("实例创建失败: %v", err.Error())
		return err
	}
	pr.Logger.Infof("创建实例云调用完成")

	logger.Infof("************ 2. 创建实例后同步 ************")
	syncer := synctask.GetTaskSyncer(order.IspType)
	ctx = context.WithValue(ctx, constant.OrderCtxMeta, map[string]string{
		"order_username": order.CreateUser,
	})

	switch order.Type {
	case "host":
		err = syncer.SyncHost(ctx, pr.Logger, synctask.SyncOption{RegionID: order.RegionID, IspID: order.IspID}, createdInstances...)
	case "mysql":
		err = syncer.SyncMysql(ctx, pr.Logger, synctask.SyncOption{RegionID: order.RegionID, IspID: order.IspID}, createdInstances...)
	case "redis":
		err = syncer.SyncRedis(ctx, pr.Logger, synctask.SyncOption{RegionID: order.RegionID, IspID: order.IspID}, createdInstances...)
	case "lb":
		err = syncer.SyncLoadBalancer(ctx, pr.Logger, synctask.SyncOption{RegionID: order.RegionID, IspID: order.IspID}, createdInstances...)
	default:
		return fmt.Errorf("illegal order type")
	}

	if err != nil {
		pr.Logger.Errorln("创建实例后同步失败")
		return err
	}
	pr.Logger.Infoln("创建实例后同步完成")

	if len(createdInstances) != 0 {
		// 进行创建资源后的初始化
		err = initMultiInstance(ctx, pr, order, createdInstances, initOption)
		if err != nil {
			pr.Logger.Infoln("初始化失败")
			return err
		}
	}

	pr.Logger.Infof("实例创建任务成功")
	logger.Debugf("createdInstances========%v", createdInstances)
	return nil
}

type initRes struct {
	InstanceName string
	InstanceID   string
	Err          error
}

type initInput struct {
	InstanceName string
	InstanceID   string
}

// 初始化实例
func initMultiInstance(ctx context.Context, pr *common.InitProvider, order *entity.ResOrder, instances []string, initOption *cloudman.InitOption) error {
	if initOption == nil {
		pr.Logger.Infoln("不进行初始化，跳过....")
		return nil
	}

	var initErr error
	failedInstances := make([]string, 0)
	adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	if initOption.EnableInit {
		// 获取实例详细信息
		pr.Logger.Infof("************ 开始初始化 ************")
		initInputs := make([]*initInput, 0)
		switch order.Type {
		case "host":
			hostResources, err := models.HostResourceModel.GetByInstanceIDs(adminCtx, instances)
			if err != nil {
				pr.Logger.Errorf("查询失败: %v", err.Error())
				return err
			}
			for _, r := range hostResources {
				initInputs = append(initInputs, &initInput{
					InstanceName: r.InstanceName,
					InstanceID:   r.InstanceID,
				})
			}
		case "mysql":
			mysqlResources, err := models.MysqlClusterResourceModel.GetByInstanceIDs(adminCtx, instances)
			if err != nil {
				pr.Logger.Errorf("查询失败: %v", err.Error())
				return err
			}
			for _, r := range mysqlResources {
				initInputs = append(initInputs, &initInput{
					InstanceName: r.DBClusterDescription,
					InstanceID:   r.DBClusterID,
				})
			}
		case "redis":
			redisResources, err := models.RedisResourceModel.GetByInstanceIDs(adminCtx, instances)
			if err != nil {
				pr.Logger.Errorf("查询失败: %v", err.Error())
				return err
			}
			for _, r := range redisResources {
				initInputs = append(initInputs, &initInput{
					InstanceName: r.InstanceName,
					InstanceID:   r.InstanceID,
				})
			}
		case "lb":
			lbResources, err := models.LoadBalancerModel.GetByInstanceIDs(adminCtx, instances)
			if err != nil {
				pr.Logger.Errorf("查询失败: %v", err.Error())
				return err
			}
			for _, r := range lbResources {
				initInputs = append(initInputs, &initInput{
					InstanceName: r.LoadBalancerName,
					InstanceID:   r.LoadBalancerID,
				})
			}
		default:
			// do nothing
		}

		initCfg, err := models.InitCfgModel.GetByRegionID(ctx, pr.RegionID)
		if err != nil {
			return err
		}

		// 调用标准运维平台执行初始化任务,每个主机对应一个初始化任务
		var successInstances []string
		successInstances, failedInstances, initErr = doMultiInitTask(ctx, pr, order, initInputs, initOption, initCfg)
		if len(successInstances) == 0 {
			return initErr
		}

		// 只重启成功初始化的实例
		instances = successInstances
		// 重启实例
		err = doInstanceReboot(ctx, pr, order, instances)
		if err != nil {
			return err
		}
	}

	if initOption.Lock {
		lockErr := doLockInstance(adminCtx, pr, order, instances)
		if lockErr != nil {
			return lockErr
		}
	}

	// 最后再抛出初始化错误
	if initErr != nil {
		// 发送通知
		go sendInitFailedNotice(ctx, failedInstances, order)
		return initErr
	}

	return nil
}

func sendInitFailedNotice(ctx context.Context, faieldInstances []string, order *entity.ResOrder) error {
	config := cfg.GetNotificationConfig()
	client := notification.New(config.Url, config.Token, config.AppKey)
	text := fmt.Sprintf("**订单ID**: %s\n**失败实例**: %s\n**创建人**: <at user_id=%s> </at>", order.ID.Hex(), strings.Join(faieldInstances, ","), order.CreateUser)
	buttonUrl := fmt.Sprintf("%s/cloudSync/res_order", config.ButtonUrl)
	card := notification.NewColumnCard(
		notification.NewHeader("云管平台初始化失败通知", notification.HeaderTemplateDanger),
		[]notification.Element{
			notification.NewMarkDown(text),
			notification.NewButton("查看详情", notification.NewURLOption(&notification.MultiURL{URL: buttonUrl})),
		},
	)
	for _, userId := range config.UserIds {
		err := client.SendHoyowaveCard(userId, "user_id", card)
		if err != nil {
			return err
		}
	}
	for _, chatId := range config.ChatIds {
		err := client.SendHoyowaveCard(chatId, "chat_id", card)
		if err != nil {
			return err
		}
	}
	return nil

}

func doLockInstance(ctx context.Context, pr *common.InitProvider, order *entity.ResOrder, instances []string) error {
	pr.Logger.Infof("************ 开始锁定资源 ************")
	switch order.Type {
	case "host":
		err := models.HostResourceModel.UpdateLockStatusWithInstanceID(ctx, instances, true)
		if err != nil {
			return err
		}
	case "mysql":
		err := models.MysqlClusterResourceModel.UpdateLockStatusWithInstanceID(ctx, instances, true)
		if err != nil {
			return err
		}
	case "redis":
		err := models.RedisResourceModel.UpdateLockStatusWithInstanceID(ctx, instances, true)
		if err != nil {
			return err
		}
	default:
		// do nothing
	}
	return nil
}

func doInstanceReboot(ctx context.Context, pr *common.InitProvider, order *entity.ResOrder, instances []string) error {
	if order.Type != "host" {
		return nil
	}

	provideName := order.IspType + "_" + order.Type
	f, ok := provider.ProvideMap[provideName]
	if !ok {
		return fmt.Errorf("重启失败, 未找到provider")
	}
	pFunc, err := f(*pr)
	if err != nil {
		return err
	}
	updateForm := common.UpdateInstanceForm{
		Action:    "reboot",
		Instances: instances,
	}
	data, _ := json.Marshal(updateForm)
	return pFunc.UpdateInstance(ctx, time.Minute*60, order.ID.Hex(), data)
}

func doMultiInitTask(
	ctx context.Context,
	pr *common.InitProvider,
	order *entity.ResOrder,
	inputs []*initInput,
	initOption *cloudman.InitOption,
	initCfg *entity.InitCfg,
) ([]string, []string, error) {
	// 调用标准运维平台执行初始化任务,每个主机对应一个初始化任务
	var wg sync.WaitGroup
	var resWg sync.WaitGroup
	resChan := make(chan initRes)
	wg.Add(len(inputs))
	for _, input := range inputs {
		instanceID := input.InstanceID
		instanceName := input.InstanceName
		go func(instanceID, instanceName string) {
			defer func() {
				wg.Done()
				if r := recover(); r != nil {
					logger.Errorf("recover stack: %s", string(debug.Stack()))
					logger.Errorf("Recovered from init panic:%s", r)
				}
			}()
			err := startInitTask(ctx, pr, order, initCfg, initOption, instanceID, instanceName)
			resChan <- initRes{
				InstanceName: instanceName,
				InstanceID:   instanceID,
				Err:          err,
			}
		}(instanceID, instanceName)
	}
	var errExist error
	resWg.Add(1)
	successInstances := make([]string, 0)
	failedInstances := make([]string, 0)
	go func() {
		defer func() {
			resWg.Done()
			if r := recover(); r != nil {
				logger.Errorf("Recovered from res channel panic:%s", r)
			}
		}()
		for res := range resChan {
			if res.Err == nil {
				successInstances = append(successInstances, res.InstanceID)
			} else {
				failedInstances = append(failedInstances, res.InstanceID)
			}
			err := reportInitStatus(ctx, pr, order, res)
			if err != nil {
				errExist = err
			}
		}
	}()
	wg.Wait()
	close(resChan)
	resWg.Wait()
	return successInstances, failedInstances, errExist
}

func startInitTask(ctx context.Context, pr *common.InitProvider, order *entity.ResOrder, initCfg *entity.InitCfg, initOption *cloudman.InitOption, instanceID, instanceName string) error {
	statusDetail, err := common.GetOrderStatusDetail(ctx, order.ID.Hex())
	if err != nil {
		pr.Logger.Errorln("获取订单状态详情失败")
		return err
	}
	common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), true, statusDetail, common.ReportKv{
		common.ReportSprintf("%s.last_time", instanceName):           time.Now().Unix(),
		common.ReportSprintf("%s.status", instanceName):              "init",
		common.ReportSprintf("%s.jobs.Init.last_time", instanceName): time.Now().Unix(),
		common.ReportSprintf("%s.jobs.Init.status", instanceName):    "preparing",
	})
	return initInstance(ctx, pr, initCfg, initOption, order.RawData, instanceName, instanceID)
}

func reportInitStatus(ctx context.Context, pr *common.InitProvider, order *entity.ResOrder, res initRes) error {
	statusDetail, err := common.GetOrderStatusDetail(ctx, order.ID.Hex())
	if err != nil {
		pr.Logger.Errorln("获取订单状态详情失败")
	}
	statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
		common.ReportSprintf("%s.jobs.Init.last_time", res.InstanceName): time.Now().Unix(),
	})
	if res.Err != nil {
		pr.Logger.Errorf("实例(%s)初始化失败: %s", res.InstanceName, res.Err.Error())
		statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
			common.ReportSprintf("%s.status", res.InstanceName):           "failed",
			common.ReportSprintf("%s.is_err", res.InstanceName):           true,
			common.ReportSprintf("%s.jobs.Init.status", res.InstanceName): "failed",
			common.ReportSprintf("%s.jobs.Init.is_err", res.InstanceName): true,
		})
		common.ReportOrderStatusDetail(ctx, order.ID.Hex(), true, statusDetail, "", "")
		return errors.New("存在初始化失败的实例")
	}

	pr.Logger.Infof("实例(%s)初始化成功", res.InstanceName)
	statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
		common.ReportSprintf("%s.status", res.InstanceName):               "success",
		common.ReportSprintf("%s.is_success", res.InstanceName):           true,
		common.ReportSprintf("%s.is_err", res.InstanceName):               false,
		common.ReportSprintf("%s.jobs.Init.status", res.InstanceName):     "success",
		common.ReportSprintf("%s.jobs.Init.is_success", res.InstanceName): true,
		common.ReportSprintf("%s.jobs.Init.is_err", res.InstanceName):     false,
	})
	common.ReportOrderStatusDetail(ctx, order.ID.Hex(), true, statusDetail, "", "")
	return nil
}

func initInstance(ctx context.Context, pr *common.InitProvider, initCfg *entity.InitCfg, initOption *cloudman.InitOption, rawData, instanceName, instanceID string) error {
	// 参数渲染
	params := make([]*hotwheel.PipelineParam, 0)
	formMap := map[string]interface{}{}
	// ignore err
	json.Unmarshal([]byte(rawData), &formMap)
	data := map[string]interface{}{
		"form":          formMap,
		"instance_id":   instanceID,
		"instance_name": instanceName,
	}
	inputJson, err := json.Marshal(data)
	if err != nil {
		return err
	}

	for _, p := range initOption.PipelineParams {
		pd := &hotwheel.PipelineParam{
			Key: p.Key,
		}
		rValue, err := render.GetRenderedParam(string(inputJson), p.Value, p.EnableGzipBase64)
		if err != nil {
			return err
		}
		pd.Value = rValue
		params = append(params, pd)
	}
	for _, p := range initOption.BuiltinParams {
		pd := &hotwheel.PipelineParam{
			Key: p,
		}
		rValue, err := render.GetBuiltinParams(string(inputJson), p)
		if err != nil {
			return err
		}
		pd.Value = rValue
		params = append(params, pd)
	}

	pr.Logger.Infof("创建初始化流水线, 初始化配置:%+v, 初始化参数: %# v", initOption, pretty.Formatter(params))

	// fetch jumpserver data
	var jumpserver *entity.Jumpserver
	for _, j := range initCfg.Jumpservers {
		currentJ := j
		if currentJ.Host == initOption.JumpserverHost {
			jumpserver = &currentJ
		}
	}
	if jumpserver == nil {
		return fmt.Errorf("创建初始化任务失败, 未找到jumpserver host: %s", initOption.JumpserverHost)
	}

	hotwheelClient := hotwheel.NewClient(initCfg.HotwheelOption.URI, initCfg.HotwheelOption.Token)

	// 先启动初始化pipeline
	taskID, err := hotwheelClient.StartPipeline(ctx, initOption.PipelineId, fmt.Sprintf("初始化实例-%s", instanceName), jumpserver, params)
	if err != nil {
		return fmt.Errorf("创建初始化任务失败, 错误日志: %v", err)
	}
	pr.Logger.Infof("实例(%s)初始化任务创建成功, 任务ID: %s", instanceName, taskID)

	waitTimeout := time.Minute * 30
	// 轮询任务结束，直到任务完成或者超时
	ticker := time.NewTicker(time.Second * 5)
	timeout := time.NewTimer(waitTimeout)
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("任务ID: %s, 轮询因为context取消", taskID)
		case <-timeout.C:
			return fmt.Errorf("任务ID: %s, 轮询因为超时取消", taskID)
		case <-ticker.C:
			status, err := hotwheelClient.GetTaskStatus(ctx, taskID)
			if err != nil {
				return err
			}
			if status.IsFinish() {
				if !status.IsSuccess() {
					return fmt.Errorf("任务ID: %s, 任务失败", taskID)
				}
				return nil
			}
		}
	}
}

func agentInstall(ctx context.Context, pr *common.InitProvider, order *entity.ResOrder, instances []string) error {
	if order.Type == "host" && (order.IspType == "aliyun" || order.IspType == "aws") {
		adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
		filter := map[string]interface{}{"InstanceID": map[string]interface{}{"$in": instances}}
		hostReses, err := models.HostResourceModel.FindMany(adminCtx, filter)
		if err != nil {
			pr.Logger.Errorf("查询主机失败: %v", err.Error())
			return err
		}
		return installAgent(ctx, pr, order, hostReses)
	}
	return nil
}

func installAgent(ctx context.Context, pr *common.InitProvider, order *entity.ResOrder, hostReses []*entity.HostResource) error {
	check := struct {
		InstallAgent bool `json:"installAgent"`
	}{}
	err := json.Unmarshal([]byte(order.RawData), &check)
	if err != nil {
		pr.Logger.Infoln("解析order失败，将不安装agent")
		return nil
	}
	// Get host entities
	statusDetail, err := common.GetOrderStatusDetail(ctx, order.ID.Hex())
	if err != nil {
		pr.Logger.Errorln("获取状态详情失败，无法开始安装Agent")
		return err
	}

	// init InstallAgent job, if not, return
	for _, host := range hostReses {
		if !check.InstallAgent {
			statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
				common.ReportSprintf("%s.status", host.InstanceName):     "success",
				common.ReportSprintf("%s.is_success", host.InstanceName): true,
			})
		} else {
			statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
				common.ReportSprintf("%s.last_time", host.InstanceName):                   time.Now().Unix(),
				common.ReportSprintf("%s.instance_name", host.InstanceName):               host.InstanceName,
				common.ReportSprintf("%s.status", host.InstanceName):                      "install_agent",
				common.ReportSprintf("%s.jobs.InstallAgent.last_time", host.InstanceName): time.Now().Unix(),
				common.ReportSprintf("%s.jobs.InstallAgent.status", host.InstanceName):    "preparing",
			})
		}
	}
	if !check.InstallAgent {
		statusDetail = common.ReportOrderStatusDetail(ctx, order.ID.Hex(), true, statusDetail, "", "")
		return nil
	}

	// Start to install agent
	pr.Logger.Infof("************ 3. 同步实例后安装Agent ************")
	// Get account index id
	var accountIndexID uint32 = 22
	var accountEntity entity.Account
	accEnt := entity.GetAccountCollection(models.GetEngine())
	_, err = models.FindPK(ctx, accEnt, order.IspID, &accountEntity)
	if err != nil {
		pr.Logger.Errorln("获取云厂商详情详情失败，无法开始安装Agent")
		return err
	}
	accountIndexID = accountEntity.IndexID

	// SendCommand to install agent
	// 并发执行agent安装
	type hostRes struct {
		instanceName string
		err          error
		commands     string
	}
	resChan := make(chan hostRes)
	var wg sync.WaitGroup
	var lock sync.Mutex
	var errExist error = nil
	var jobmanProdCheckList []jobman.Server
	var jobmanTestCheckList []jobman.Server
	var jobmanProdWinCheckList []jobman.Server
	var jobmanTestWinCheckList []jobman.Server
	wg.Add(len(hostReses))

	for _, host := range hostReses {
		hostReplica := host
		go func() {
			defer func() {
				wg.Done()
				if r := recover(); r != nil {
					logger.Errorf("Recovered from install agent panic:%s", r)
				}
			}()
			if len(hostReplica.InnerIPAddress) == 0 {
				resChan <- hostRes{hostReplica.InstanceName, errors.New("do not have inner ip"), ""}
				return
			}

			commands, err := getInstallCommand(ctx, pr, hostReplica, order, accountIndexID)
			if err != nil {
				resChan <- hostRes{hostReplica.InstanceName, err, ""}
				return
			}

			var aliyunResp *ecs20140526.RunCommandResponse
			var awsResp *ssm.SendCommandOutput
			var jumpserverResp *jumpserversdk.ExecuteCommandRes
			if order.IspType == "aliyun" {
				aliyunResp, err = callAliyunInstallAPI(ctx, hostReplica, order, commands)
			} else if order.IspType == "aws" {
				awsResp, err = callAWSInstallAPI(ctx, hostReplica, order, commands)
			} else if order.IspType == "jumpserver" {
				jumpserverResp, err = callJumpserverInstallAPI(ctx, hostReplica, order, commands)
			} else if order.IspType == "custom" {
				err = callCustomInstallAPI(ctx, pr.Logger, hostReplica, order, commands)
			}
			if err != nil {
				resChan <- hostRes{hostReplica.InstanceName, err, commands}
				return
			}

			// wait for the command to be finished
			sleepTime := time.Millisecond * 100
			maxTime := time.Second * 90
			startTime := time.Now()
			nowTime := startTime
			for {
				time.Sleep(sleepTime)
				sleepTime = sleepTime * 2

				var done bool
				if order.IspType == "aliyun" {
					done, err = callAliyunCheckInstallAPI(ctx, hostReplica, order, aliyunResp)
				} else if order.IspType == "aws" {
					done, err = callAWSCheckInstallAPI(ctx, hostReplica, order, awsResp)
				} else if order.IspType == "jumpserver" {
					done, err = callJumpserverCheckInstallAPI(ctx, hostReplica, order, jumpserverResp)
				} else if order.IspType == "custom" {
					done = true
				}
				if err != nil {
					resChan <- hostRes{hostReplica.InstanceName, err, commands}
					return
				}
				if done {
					break
				}

				nowTime = time.Now()
				if nowTime.Sub(startTime) > maxTime {
					resChan <- hostRes{hostReplica.InstanceName, errors.New("max waiting time exceeded"), commands}
					return
				}
			}
			// 等待agent连接，并反映在数据库相关字段中
			time.Sleep(10 * time.Second)

			// check agent status
			newFilter := map[string]interface{}{"InstanceID": hostReplica.InstanceID}
			adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
			newHost, iErr := models.HostResourceModel.FindOne(adminCtx, newFilter)
			if iErr != nil {
				resChan <- hostRes{hostReplica.InstanceName, iErr, commands}
				return
			}
			if newHost.AgentStatus != 1 && cfg.GetSystemConfig().Env != "local" {
				resChan <- hostRes{hostReplica.InstanceName, errors.New("agent status is not running"), commands}
				return
			}
			server := jobman.Server{
				AgentID:        newHost.AgentID,
				HostName:       newHost.HostName,
				InstanceName:   newHost.InstanceName,
				InstanceID:     newHost.InstanceID,
				PrivateAddress: newHost.InnerIPAddress[0],
			}
			if utils.ResourceIsProd(newHost.InstanceName) {
				if newHost.OSType == "windows" {
					jobmanProdWinCheckList = append(jobmanProdWinCheckList, server)
				} else {
					jobmanProdCheckList = append(jobmanProdCheckList, server)
				}
			} else {
				if newHost.OSType == "windows" {
					jobmanTestWinCheckList = append(jobmanTestWinCheckList, server)
				} else {
					jobmanTestCheckList = append(jobmanTestCheckList, server)
				}
			}
			resChan <- hostRes{hostReplica.InstanceName, nil, ""}
		}()
	}
	go func() {
		for res := range resChan {
			lock.Lock()
			statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
				common.ReportSprintf("%s.last_time", res.instanceName):                   time.Now().Unix(),
				common.ReportSprintf("%s.jobs.InstallAgent.last_time", res.instanceName): time.Now().Unix(),
			})
			if res.err != nil {
				pr.Logger.Errorf("实例(%s)安装Agent失败: %s", res.instanceName, res.err.Error())
				statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
					common.ReportSprintf("%s.status", res.instanceName):                   "failed",
					common.ReportSprintf("%s.is_err", res.instanceName):                   true,
					common.ReportSprintf("%s.jobs.InstallAgent.status", res.instanceName): "failed",
					common.ReportSprintf("%s.jobs.InstallAgent.is_err", res.instanceName): true,
				})
				errExist = errors.New("存在安装Agent失败的实例")
				// print install commands
				if res.commands != "" {
					pr.Logger.Errorf("实例(%s)安装Agent脚本如下:\n%s", res.instanceName, res.commands)
				}
			} else {
				pr.Logger.Infof("实例(%s)安装Agent成功", res.instanceName)
				statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
					common.ReportSprintf("%s.status", res.instanceName):                       "success",
					common.ReportSprintf("%s.is_success", res.instanceName):                   true,
					common.ReportSprintf("%s.jobs.InstallAgent.status", res.instanceName):     "success",
					common.ReportSprintf("%s.jobs.InstallAgent.is_success", res.instanceName): true,
				})
			}
			statusDetail = common.ReportOrderStatusDetail(ctx, order.ID.Hex(), true, statusDetail, "", "")
			lock.Unlock()
		}
	}()
	wg.Wait()
	close(resChan)
	time.Sleep(10 * time.Second)
	pr.Logger.Infof("************ 4. 通过作业平台确认agent安装情况 ************")
	errs := []error{
		jobman.Check(ctx, pr.Logger, order.ID.Hex(), "test", jobmanTestCheckList, 5*time.Minute, "linux"),
		jobman.Check(ctx, pr.Logger, order.ID.Hex(), "prod", jobmanProdCheckList, 5*time.Minute, "linux"),
		jobman.Check(ctx, pr.Logger, order.ID.Hex(), "test", jobmanTestWinCheckList, 5*time.Minute, "windows"),
		jobman.Check(ctx, pr.Logger, order.ID.Hex(), "prod", jobmanProdWinCheckList, 5*time.Minute, "windows"),
	}
	if errExist != nil {
		return errExist
	}
	for _, e := range errs {
		if e != nil {
			return err
		}
	}
	return nil
}

func getInstallCommand(ctx context.Context, pr *common.InitProvider, host *entity.HostResource, order *entity.ResOrder, accountIndexID uint32) (string, error) {
	agentID, err := agent.GenerateID(host.RegionID, fmt.Sprintf("%s:%d", host.InnerIPAddress[0], accountIndexID))
	if err != nil {
		return "", err
	}
	var installConfig *entity.OpsAgentInstallConfig
	opsAgentCfg := cfg.GetOpsAgentConf()
	cmdbEnv := host.CheckCmdbEnvFromTagsAndInstancename()
	installConfig, err = models.OpsAgentInstallConfigModel.FindOne(ctx, map[string]interface{}{"isp_id": order.IspID, "region_id": order.RegionID, "cmdb_env": cmdbEnv})
	if err != nil {
		pr.Logger.Errorln("获取安装配置失败，无法开始安装Agent")
		return "", err
	}

	registerURL := fmt.Sprintf("%s?agentId=%s", installConfig.RegURL, agentID)
	var commands string
	script := opsAgentCfg.Script
	if order.IspType == "jumpserver" && host.OSType == "windows" {
		script = opsAgentCfg.WinScript
	}
	commands, err = utils.RenderTpl(script, map[string]string{
		"binaryURL":   installConfig.BinaryURL,
		"registerURL": registerURL,
	})
	if err != nil {
		return "", err
	}

	if order.IspType == "custom" {
		data := struct {
			Data struct {
				Username string `json:"username"`
				Password string `json:"password"`
				Port     string `json:"port"`
			} `json:"data"`
		}{}

		if err := json.Unmarshal([]byte(order.RawData), &data); err != nil {
			return "", err
		}

		var wrappedCommands []string
		var customCommands []string
		if host.OSType == "windows" {
			executeCommand, err := utils.RenderTpl(opsAgentCfg.WinScript, map[string]string{
				"username": data.Data.Username,
				"password": data.Data.Password,
				"agent_id": agentID,
			})
			if err != nil {
				return "", err
			}
			customCommands = strings.Split(executeCommand, "\n")
		} else if host.OSType == "linux" {
			customCommands = []string{commands}
		}

		sshCommand := `sshpass -p {{.password}} ssh -o StrictHostKeyChecking=no -p {{.port}} {{.ssh_username}}@{{.ip}} "{{.command}}"`
		if len(host.InnerIPAddress) == 0 {
			return "", errors.New("ip doesn't exist")
		}

		for _, command := range customCommands {
			wrappedCommand, err := utils.RenderTpl(sshCommand, map[string]string{
				"ssh_username": data.Data.Username,
				"password":     data.Data.Password,
				"port":         data.Data.Port,
				"ip":           host.InnerIPAddress[0],
				"command":      command,
			})
			if err != nil {
				return "", err
			}
			wrappedCommands = append(wrappedCommands, wrappedCommand)
		}
		finalCommand := strings.Join(wrappedCommands, "\n")
		return finalCommand, nil
	}

	return commands, nil
}

func callAWSInstallAPI(ctx context.Context, host *entity.HostResource, order *entity.ResOrder, commands string) (*ssm.SendCommandOutput, error) {
	awsCfg, err := cfg.GetAwsOptionConf()
	if err != nil {
		return nil, err
	}

	// check if instance has instance profile
	iOut := &ec2.DescribeIamInstanceProfileAssociationsOutput{}
	err = agentsdk.SyncCall(ctx, host.RegionID, "host", "DescribeIamInstanceProfileAssociations", order.IspID, []interface{}{"", &ec2.DescribeIamInstanceProfileAssociationsInput{
		Filters: []ec2Types.Filter{{Name: aws.String("instance-id"), Values: []string{host.InstanceID}}, {Name: aws.String("state"), Values: []string{"associated"}}},
	}}, iOut)
	if err != nil {
		return nil, err
	}
	if len(iOut.IamInstanceProfileAssociations) == 0 {
		// associate instance profile
		associateOut := &ec2.AssociateIamInstanceProfileOutput{}
		err = agentsdk.SyncCall(ctx, host.RegionID, "host", "AssociateIamInstanceProfile", order.IspID, []interface{}{"", &ec2.AssociateIamInstanceProfileInput{
			IamInstanceProfile: &ec2Types.IamInstanceProfileSpecification{Arn: aws.String(awsCfg.IamInstanceProfile)},
			InstanceId:         aws.String(host.InstanceID),
		}}, associateOut)
		if err != nil {
			return nil, err
		}
		time.Sleep(20 * time.Second)
		// check again
		err = agentsdk.SyncCall(ctx, host.RegionID, "host", "DescribeIamInstanceProfileAssociations", order.IspID, []interface{}{"", &ec2.DescribeIamInstanceProfileAssociationsInput{
			Filters: []ec2Types.Filter{{Name: aws.String("instance-id"), Values: []string{host.InstanceID}}, {Name: aws.String("state"), Values: []string{"associated"}}},
		}}, iOut)
		if err != nil {
			return nil, err
		}
		if len(iOut.IamInstanceProfileAssociations) == 0 {
			return nil, errors.New("实例配置文件绑定失败")
		}
	}
	if string(iOut.IamInstanceProfileAssociations[0].State) != "associated" {
		return nil, errors.New("实例配置文件的状态错误")
	}

	// check if ssm agent of instance is valid
	maxCount := 5
	count := 0
	for {
		ssmStatusOut := &ssm.DescribeInstanceInformationOutput{}
		err = agentsdk.SyncCall(ctx, host.RegionID, "ssm", "DescribeInstanceInformation", order.IspID, []interface{}{&ssm.DescribeInstanceInformationInput{
			Filters: []*ssm.InstanceInformationStringFilter{{Key: aws.String("InstanceIds"), Values: []*string{aws.String(host.InstanceID)}}},
		}}, ssmStatusOut)
		if err == nil && len(ssmStatusOut.InstanceInformationList) != 0 {
			ssmPingStatus := aws.StringValue(ssmStatusOut.InstanceInformationList[0].PingStatus)
			if ssmPingStatus == "Online" {
				break
			}
		}
		if count == maxCount {
			if err != nil || len(ssmStatusOut.InstanceInformationList) == 0 {
				return nil, errors.New("等待超时,实例ssm agent状态错误或镜像可能不支持）")
			}
			ssmPingStatus := aws.StringValue(ssmStatusOut.InstanceInformationList[0].PingStatus)
			if ssmPingStatus != "Online" {
				return nil, fmt.Errorf("等待超时,实例ssm agent掉线: %s", ssmPingStatus)
			}
		}
		count++
		time.Sleep(time.Second * 10)
	}

	runReq := &ssm.SendCommandInput{
		DocumentName: aws.String("AWS-RunShellScript"),
		InstanceIds:  aws.StringSlice([]string{host.InstanceID}),
		Parameters: map[string][]*string{"commands": {
			aws.String(commands),
		}},
	}
	runResp := &ssm.SendCommandOutput{}
	err = agentsdk.SyncCall(ctx, host.RegionID, "ssm", "SendCommand", order.IspID, []interface{}{runReq}, runResp)
	if err != nil {
		return nil, err
	}
	return runResp, nil
}

func callAWSCheckInstallAPI(ctx context.Context, host *entity.HostResource, order *entity.ResOrder, runResp *ssm.SendCommandOutput) (bool, error) {
	resultReq := ssm.GetCommandInvocationInput{CommandId: runResp.Command.CommandId, InstanceId: aws.String(host.InstanceID)}
	resultResp := &ssm.GetCommandInvocationOutput{}
	err := agentsdk.SyncCall(ctx, host.RegionID, "ssm", "GetCommandInvocation", order.IspID, []interface{}{resultReq}, resultResp)
	if err != nil {
		return false, err
	}
	if aws.StringValue(resultResp.Status) == "Failed" {
		if aws.StringValue(resultResp.StandardErrorContent) == "" {
			return false, errors.New(aws.StringValue(resultResp.StandardOutputContent))
		}
		return false, errors.New(aws.StringValue(resultResp.StandardErrorContent))
	}
	if aws.StringValue(resultResp.Status) == "Success" {
		return true, nil
	}
	return false, nil
}

func callAliyunInstallAPI(ctx context.Context, host *entity.HostResource, order *entity.ResOrder, command string) (*ecs20140526.RunCommandResponse, error) {
	runReq := &ecs20140526.RunCommandRequest{
		RegionId:       tea.String(host.RegionID),
		Type:           tea.String("RunShellScript"),
		InstanceId:     tea.StringSlice([]string{host.InstanceID}),
		CommandContent: tea.String(command),
	}
	runResp := &ecs20140526.RunCommandResponse{}
	err := agentsdk.SyncCall(ctx, host.RegionID, "host", "RunCommand", order.IspID, []interface{}{runReq}, runResp)
	if err != nil {
		return nil, err
	}
	return runResp, nil
}

func callAliyunCheckInstallAPI(ctx context.Context, host *entity.HostResource, order *entity.ResOrder, runResp *ecs20140526.RunCommandResponse) (bool, error) {
	resultReq := &ecs20140526.DescribeInvocationResultsRequest{RegionId: tea.String(host.RegionID), InvokeId: runResp.Body.InvokeId}
	resultResp := &ecs20140526.DescribeInvocationResultsResponse{}
	err := agentsdk.SyncCall(ctx, host.RegionID, "host", "DescribeInvocationResults", order.IspID, []interface{}{resultReq}, resultResp)
	if err != nil {
		return false, err
	}
	if len(resultResp.Body.Invocation.InvocationResults.InvocationResult) == 0 {
		return false, errors.New("error length of command result")
	}

	invokeResult := resultResp.Body.Invocation.InvocationResults.InvocationResult[0]
	if tea.StringValue(invokeResult.InvokeRecordStatus) != "Finished" {
		// continue to check result
		return false, nil
	}

	if tea.StringValue(invokeResult.InvocationStatus) != "Success" {
		output, err := base64.StdEncoding.DecodeString(tea.StringValue(invokeResult.Output))
		if err != nil {
			return false, errors.New("invocation status is not success and fail to decode base64 output")
		}
		return false, fmt.Errorf("error result of running agentInstall shell: %s", string(output))
	}

	// finished and success
	return true, nil
}

func callJumpserverInstallAPI(ctx context.Context, host *entity.HostResource, order *entity.ResOrder, command string) (*jumpserversdk.ExecuteCommandRes, error) {
	account, err := models.AccountModel.Get(ctx, order.IspID)
	if err != nil {
		return nil, err
	}
	sysUserReq := &jumpserversdk.GetHostSystemUsersReq{
		Host: host.InstanceID,
	}
	sysUserResp := &jumpserversdk.GetHostSystemUsersRes{}
	err = agentsdk.SyncCall(ctx, host.RegionID, "host", "GetHostSystemUsers", order.IspID, []interface{}{account.Host, sysUserReq}, sysUserResp)
	if err != nil {
		return nil, err
	}

	execReq := &jumpserversdk.ExecuteCommandReq{
		Command: command,
		RunAs:   sysUserResp.ID,
		Hosts:   []string{host.InstanceID},
	}
	execResp := &jumpserversdk.ExecuteCommandRes{}
	err = agentsdk.SyncCall(ctx, host.RegionID, "host", "ExecuteCommand", order.IspID, []interface{}{account.Host, execReq}, execResp)
	if err != nil {
		return nil, err
	}
	return execResp, nil
}

func callJumpserverCheckInstallAPI(ctx context.Context, host *entity.HostResource, order *entity.ResOrder, runResp *jumpserversdk.ExecuteCommandRes) (bool, error) {
	account, err := models.AccountModel.Get(ctx, order.IspID)
	if err != nil {
		return false, err
	}

	execReq := &jumpserversdk.GetCommandLogReq{
		ID: runResp.ID,
	}
	execResp := &jumpserversdk.GetCommandLogRes{}
	err = agentsdk.SyncCall(ctx, host.RegionID, "host", "GetCommandLog", order.IspID, []interface{}{account.Host, execReq}, execResp)
	if err != nil {
		return false, err
	}
	if !execResp.IsEnd {
		return false, nil
	}
	if strings.Contains(execResp.Data, "| FAILED!") {
		return false, errors.New("跳板机执行命令出错:\n" + execResp.Data)
	}
	return true, nil
}

func callCustomInstallAPI(ctx context.Context, logger *logrus.Logger, host *entity.HostResource, order *entity.ResOrder, commands string) error {
	// run on jobman to install agent (windows/linux)
	data := struct {
		Data struct {
			Username string `json:"username"`
			Password string `json:"password"`
			Port     string `json:"port"`
		} `json:"data"`
	}{}

	if err := json.Unmarshal([]byte(order.RawData), &data); err != nil {
		return err
	}
	env := "test"
	if utils.ResourceIsProd(host.InstanceName) {
		env = "prod"
	}
	return jobman.InstallAgentOnJobman(ctx, data.Data.Password, commands, logger, order.ID.Hex(), env, 5*time.Minute)
}

// OrderMsg ...
type OrderMsg struct {
	IspType      string `json:"ispType"`
	RegionID     string `json:"regionID"`
	InstanceType string `json:"instanceType"`
	Action       string `json:"action"`
	Status       string `json:"status"`
	Detail       string `json:"detail"`
	FerryURL     string `json:"ferryURL"`
	OrderURL     string `json:"orderURL"`
}

// RunOrder 外部调用订单执行
func (r ResTemplate) RunOrder(ctx context.Context, orderID *cloudman.RunOrderReq) (*cloudman.Result, error) {
	// TODO: 增加权限拦截, 仅审批角色可调用该方法
	order, err := models.ResourceOrder.Get(ctx, orderID.Id)
	if err != nil {
		return nil, err
	}

	// taskBaseCtx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
	// 	"x-rpc-" + "username": order.CreateUser,
	// }))
	taskBaseCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)

	// 回收机器的订单可以在待销毁状态继续触发
	if order.Status >= constant.TaskRunning && order.Action != string(schema.ResourceRecycle) {
		return nil, fmt.Errorf("订单当前状态不可调用")
	}

	// if order.Action == string(schema.ResourceCreate) {
	// }

	if orderID.Status != "accept" && order.Action != "modifyTags" {
		err := closeTask(order, "任务审批未通过")
		ferryURL := fmt.Sprintf("%s/ferry/process/handle-ticket?workOrderId=%d&processId=%s", cfg.GetSystemConfig().WorkOrderWebHost, order.TicketID, cfg.GetSystemConfig().FlowID)
		msg := OrderMsg{IspType: order.IspType, RegionID: order.RegionID, InstanceType: order.GetOrderType(), Action: order.GetAction(), Status: "审批未通过", FerryURL: ferryURL, OrderURL: fmt.Sprintf("%s/?order_id=%s", cfg.GetSystemConfig().OrderURL, order.ID.Hex())}
		msgRaw, _ := json.Marshal(msg)
		go func() {
			if err := cloudmanCore.SendNoticeCtx(ctx, &notifySdk.QNotify{
				Name:        "工单任务通知",
				TemplateMsg: string(msgRaw),
				MikuUser:    []string{order.CreateUser},
			}); err != nil {
				logger.Error(err.Error())
			}
		}()
		return &cloudman.Result{Message: "任务审批结果未通过"}, err
	}

	if order.Action == string(schema.ResourceCreate) {
		go func() {
			ctx, cancel := context.WithTimeout(taskBaseCtx, 60*time.Minute)
			defer cancel()
			// 判断实际操作类型
			err := r.createInstance(ctx, order, &cloudman.InitOption{
				EnableInit: false,
			})
			if err == nil {
				_ = createinstance.ModelMap[order.Type].ReleaseInstancePlace(ctx, []byte(order.RawData), orderID.Id)
			}
		}()

		return &cloudman.Result{Message: "任务开始执行"}, nil
	}

	if order.Action == string(schema.ResourceModify) || order.Action == string(schema.ResourceBackup) || order.Action == string(schema.ResourceCleanup) {
		go func() {
			ctx, cancel := context.WithTimeout(taskBaseCtx, 60*time.Minute)
			defer cancel()
			// 判断实际操作类型
			r.updateResource(ctx, order)
		}()

		return &cloudman.Result{Message: "任务开始执行"}, nil
	}

	if order.Action == string(schema.ResourceRecycle) {
		var form common.UpdateInstanceForm
		err := json.Unmarshal([]byte(order.RawData), &form)
		if err != nil {
			return nil, err
		}
		// 只有待销毁状态才可以在回收机器的订单成功时继续执行
		if order.Status == constant.TaskSuccess && form.Action != "delete" {
			return nil, fmt.Errorf("订单当前状态不可调用")
		}

		go func() {
			ctx, cancel := context.WithTimeout(taskBaseCtx, 60*time.Minute)
			defer cancel()
			// 判断实际操作类型
			r.updateResource(ctx, order)
		}()

		return &cloudman.Result{Message: "任务开始执行"}, nil
	}

	if order.Action == string(schema.ResourceRecover) {
		go func() {
			ctx, cancel := context.WithTimeout(taskBaseCtx, 60*time.Minute)
			defer cancel()
			// 判断实际操作类型
			r.recoverResource(ctx, order)
		}()
		return &cloudman.Result{Message: "任务开始执行"}, nil
	}

	return nil, fmt.Errorf("不支持的操作请求")
}

// RunOrderRetry 失败订单重试
func (r ResTemplate) RunOrderRetry(ctx context.Context, req *cloudman.RunOrderRetryReq) (*cloudman.Result, error) {
	order, err := models.ResourceOrder.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	taskBaseCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)

	if order.Status != constant.TaskErr || !slices.Contains([]string{string(schema.ResourceBackup), string(schema.ResourceCreate)}, order.Action) {
		return nil, fmt.Errorf("只有已失败的备份/创建订单才能重试")
	}

	if order.Action == string(schema.ResourceBackup) {
		// 备份订单重新执行
		order.RawData, err = sjson.Set(order.RawData, "retry_instances", req.RetryList)
		if err != nil {
			return nil, fmt.Errorf("原任务数据无法解析")
		}
		go func() {
			ctx, cancel := context.WithTimeout(taskBaseCtx, 60*time.Minute)
			defer cancel()
			r.updateResource(ctx, order)
		}()
	} else if order.Action == string(schema.ResourceCreate) && order.Type == "host" {
		err = models.ResourceOrder.UpdateStatus(ctx, order.ID.Hex(), constant.TaskRunning)
		if err != nil {
			logger.Errorf("更新订单状态失败: %v", err.Error())
			return nil, err
		}
		go func() {
			ctx, cancel := context.WithTimeout(taskBaseCtx, 60*time.Minute)
			defer cancel()
			err = r.RetryCreateHostOrder(ctx, order)
			if err != nil {
				_ = models.ResourceOrder.UpdateStatus(ctx, order.ID.Hex(), constant.TaskErr)
				return
			}
			_ = models.ResourceOrder.UpdateStatus(ctx, order.ID.Hex(), constant.TaskSuccess)
		}()
	}

	return &cloudman.Result{Message: "任务开始执行"}, nil
}

func (r ResTemplate) RetryCreateHostOrder(ctx context.Context, order *entity.ResOrder) error {
	logger := BuildLogger(order.ID.Hex())
	pr, err := getIspInfo(ctx, order.IspID, "", order.RegionID)
	if err != nil {
		return err
	}
	pr.SetLogger(logger)
	initOption := &cloudman.InitOption{}
	err = json.Unmarshal([]byte(*order.InitOption), initOption)
	if err != nil {
		return err
	}

	if !initOption.EnableInit {
		return fmt.Errorf("无法重试，订单未启用初始化")
	}

	statusDetail := make(map[string]interface{})
	err = json.Unmarshal([]byte(order.StatusDetail), &statusDetail)
	if err != nil {
		return err
	}

	failedInstanceIds := make([]string, 0)
	for _, v := range statusDetail {
		vv, ok := v.(map[string]interface{})
		if !ok {
			return fmt.Errorf("状态详情解析失败")
		}
		instanceId := vv["instance_id"].(string)
		status := vv["status"].(string)
		if status == "failed" {
			failedInstanceIds = append(failedInstanceIds, instanceId)
		}
	}
	// 如果没有失败实例, 直接退出
	if len(failedInstanceIds) == 0 {
		return nil
	}

	adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	// 重试失败实例的初始化任务
	hostResources, err := models.HostResourceModel.GetByInstanceIDs(adminCtx, failedInstanceIds)
	if err != nil {
		pr.Logger.Errorf("查询失败: %v", err.Error())
		return err
	}
	initInputs := make([]*initInput, 0)
	for _, r := range hostResources {
		initInputs = append(initInputs, &initInput{
			InstanceName: r.InstanceName,
			InstanceID:   r.InstanceID,
		})
	}
	initCfg, err := models.InitCfgModel.GetByRegionID(ctx, pr.RegionID)
	if err != nil {
		return err
	}
	pr.Logger.Infof("开始重试初始化实例, %s", strings.Join(failedInstanceIds, ","))
	_, _, err = doMultiInitTask(ctx, pr, order, initInputs, initOption, initCfg)
	if err != nil {
		return err
	}

	// 重试失败的实例
	err = doInstanceReboot(ctx, pr, order, failedInstanceIds)
	if err != nil {
		return err
	}

	if initOption.Lock {
		err = doLockInstance(adminCtx, pr, order, failedInstanceIds)
		if err != nil {
			return err
		}
	}
	return nil
}

// updateCustomResource 更新自定义资源
func (r ResTemplate) updateCustomResource(ctx context.Context, order *entity.ResOrder, data string) (*cloudman.CommonResourceResp, error) {
	var host cloudman.HostResDetail
	err := json.Unmarshal([]byte(data), &host)
	if err != nil {
		return nil, err
	}

	if !models.CheckIspAndTagsPermFromCtx(ctx, "host", "admin", host.IspId, models.PbToCheckTags(host.Tags)) {
		return nil, errors.New("没有更新此资源的权限")
	}

	hostEntity := pbHostToEntity(&host)
	instanceID := host.InstanceId

	accountIndexID, err := agent.GetAccountIndexID(ctx, host.IspId)
	if err != nil {
		return nil, err
	}
	agentID, genErr := agent.GenerateID(host.RegionId, fmt.Sprintf("%s:%d", host.PrivateAddress[0], accountIndexID))
	if genErr == nil {
		hostEntity.AgentID = agentID
	}

	{
		hostEntity.IspID = order.IspID
		hostEntity.IspType = order.IspType
		hostEntity.UpdatedTime = time.Now().Unix()
		hostEntity.ISP = order.IspName
		hostEntity.InstanceID = host.InstanceId
		hostEntity.InstanceType = host.InstanceType
		hostEntity.Status = constant.ResourceRunning
	}

	defer func() {
		operateResourceAfter(order, err)
	}()

	_, err = models.HostResourceModel.CreateOrUpdate(ctx, instanceID, hostEntity, host.Tags, models.HostUpdateOption{
		UpdateDesc: true,
		UpdateTags: true,
		FromSync:   false,
	})
	if err != nil {
		return nil, err
	}

	return &cloudman.CommonResourceResp{Message: "success"}, nil
}

// batchUpdateCustomResource 批量更新自定义资源属性,仅支持主机
func (r ResTemplate) batchUpdateCustomResource(ctx context.Context, order *entity.ResOrder, data string, pr *common.InitProvider) (*cloudman.CommonResourceResp, error) {
	var host *cloudman.BatchModifyReq
	err := json.Unmarshal([]byte(data), &host)
	if err != nil {
		return nil, err
	}

	defer func() {
		operateResourceAfter(order, err)
	}()

	success, failed := HostResEntity.BatchModifyForCustom(ctx, host)
	if len(success) != 0 {
		pr.Logger.Infof("操作成功: %v", success)
	}
	if len(failed) != 0 {
		pr.Logger.Errorf("%v", failed)
		return nil, err
	}

	return &cloudman.CommonResourceResp{Message: "success"}, nil
}

// recycleResource 回收资源
func (r ResTemplate) recycleResource(ctx context.Context, order *entity.ResOrder, pr *common.InitProvider, instanceIDs []string) error {
	var err error

	pr.Logger.Infof("将类型%v实例%v加入回收区", order.Type, instanceIDs)
	go func() {
		// 释放通知
		sendErr := cloudmanCore.SendNotice(&notifySdk.QNotify{
			Name:    fmt.Sprintf("云管平台通知"),
			Message: fmt.Sprintf("将类型%v实例%v加入回收区", order.Type, instanceIDs),
		})
		if sendErr != nil {
			logger.Error(sendErr.Error())
		}
	}()

	defer func() {
		operateResourceAfter(order, err)
	}()

	// 构造实例操作环境
	// 当前仅主机关机用得到
	// 2024/10/18 取消了关机操作，仅关机状态的机器才能回收，所以这里取消了关机操作
	// provideName := order.IspType + "_" + order.Type
	// f, ok := provider.ProvideMap[provideName]
	// if !ok {
	// 	pr.Logger.Errorln("not found provider")
	// 	return err
	// }
	// pr.Logger.Infof("初始化provider")
	// pFunc, err := f(*pr)
	// if err != nil {
	// 	pr.Logger.Errorf("初始化provider失败:%v", err.Error())
	// 	return err
	// }

	// if order.Type == "host" {
	// 	// 所有资源加入回收站必须触发关机动作
	// 	err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{"status": constant.TaskRunning})
	// 	buf, _ := json.Marshal(common.UpdateInstanceForm{Action: "stop", Instances: instanceIDs})
	// 	err = pFunc.UpdateInstance(ctx, 5*time.Minute, order.ID.Hex(), buf)
	// 	if err != nil {
	// 		return err
	// 	}
	// }

	// 开始执行数据库动作
	ctx = context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	// 1、加入回收区表，2、原表标记为已回收
	var tmp []*entity.Recycle
	m, err := cloudmanCore.RecycleCore.GetModel(order.Type)
	if err != nil {
		return err
	}
	if order.Type == "host" {
		for _, instance := range instanceIDs {
			res, err := models.HostResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": instance})
			if err != nil {
				return err
			}
			r := &entity.Recycle{
				InstanceName:   res.InstanceName,
				InstanceType:   order.Type,
				InstanceID:     instance,
				IspID:          pr.IspID,
				IspType:        pr.IspType,
				RegionID:       pr.RegionID,
				Status:         constant.TaskWaitRun,
				InnerIPAddress: res.InnerIPAddress,
			}
			r.CreateUser = order.CreateUser
			r.UpdateUser = order.CreateUser
			tmp = append(tmp, r)

			// 主机类需额外考虑agent删除工作
			_, err = models.OpsAgentModel.FindOne(ctx, map[string]interface{}{"agent_id": res.AgentID})
			if err == nil {
				_ = models.Delete(ctx, entity.GetOpsAgentCollection(models.GetEngine()), map[string]interface{}{"agent_id": res.AgentID})
			}
		}
		err = m.SetRecyclable(ctx, instanceIDs, true)
		if err != nil {
			return err
		}
	} else if order.Type == "mysql" {
		for _, instance := range instanceIDs {
			res, err := models.MysqlClusterResourceModel.FindOne(ctx, map[string]interface{}{"DBClusterId": instance})
			if err != nil {
				return err
			}
			r := &entity.Recycle{
				InstanceName: res.DBClusterDescription,
				InstanceType: order.Type,
				InstanceID:   instance,
				IspID:        pr.IspID,
				IspType:      pr.IspType,
				RegionID:     pr.RegionID,
				Status:       constant.TaskWaitRun,
			}
			r.CreateUser = order.CreateUser
			r.UpdateUser = order.CreateUser
			tmp = append(tmp, r)
		}
		err = m.SetRecyclable(ctx, instanceIDs, true)
		if err != nil {
			return err
		}
	} else if order.Type == "redis" {
		for _, instance := range instanceIDs {
			res, err := models.RedisResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": instance})
			if err != nil {
				return err
			}
			r := &entity.Recycle{
				InstanceName:   res.InstanceName,
				InstanceType:   order.Type,
				InstanceID:     instance,
				IspID:          pr.IspID,
				IspType:        pr.IspType,
				RegionID:       pr.RegionID,
				Status:         constant.TaskWaitRun,
				InnerIPAddress: []string{res.PrivateIP},
			}
			r.CreateUser = order.CreateUser
			r.UpdateUser = order.CreateUser
			tmp = append(tmp, r)
		}
		err = m.SetRecyclable(ctx, instanceIDs, true)
		if err != nil {
			return err
		}
	} else if order.Type == "lb" {
		for _, instance := range instanceIDs {
			res, err := models.LoadBalancerModel.FindOne(ctx, map[string]interface{}{"LoadBalancerID": instance})
			if err != nil {
				return err
			}
			r := &entity.Recycle{
				InstanceName:   res.LoadBalancerName,
				InstanceType:   order.Type,
				InstanceID:     instance,
				IspID:          pr.IspID,
				IspType:        pr.IspType,
				RegionID:       pr.RegionID,
				Status:         constant.TaskWaitRun,
				InnerIPAddress: []string{},
			}
			r.CreateUser = order.CreateUser
			r.UpdateUser = order.CreateUser
			tmp = append(tmp, r)
		}
		err = m.SetRecyclable(ctx, instanceIDs, true)
		if err != nil {
			return err
		}
	}

	return models.RecycleModel.BatchCreate(ctx, tmp)
}

// cleanupResource 清理资源
func (r ResTemplate) cleanupResource(ctx context.Context, order *entity.ResOrder, pr *common.InitProvider, instanceIDs []string) error {
	var err error
	defer func() {
		operateResourceAfter(order, err)
	}()
	pr.Logger.Infof("cleanupResource.Check.start, detail is %v", instanceIDs)

	// 开始执行数据库动作
	ctx = context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	// 判断needCleanup标记是否为真，不通过则整体不执行
	m, err := cloudmanCore.RecycleCore.GetModel(order.Type)
	if err != nil {
		return err
	}
	instanceIDColumn := "InstanceID"
	switch order.Type {
	case "mysql":
		instanceIDColumn = "DBClusterId"
	default:
		// do nothing
	}

	cleanupCount, err := m.Count(ctx, map[string]interface{}{
		instanceIDColumn: bson.M{"$in": instanceIDs},
		"NeedCleanup":    true,
	})
	if err != nil {
		return err
	}
	if int(cleanupCount) != len(instanceIDs) {
		return fmt.Errorf("cleanupResource.Check: not every instance is marked NeedCleanup, please check, (%d/%d)", cleanupCount, len(instanceIDs))
	}
	pr.Logger.Infof("cleanupResource.Check.ok, count is %d", cleanupCount)
	// 资源主表标记为is_delete，删除
	err = m.SetRelease(ctx, instanceIDs)
	if err != nil {
		return err
	}
	pr.Logger.Infof("cleanupResource.SetRelease.ok")
	// 调用cmdb清理资源
	go func() {
		baseCtx := context.WithValue(context.Background(), constant.HookCtxMeta, map[string]string{
			"isp_id":   order.IspID,
			"username": permission.GetUsername(ctx),
		})
		hooks.PubDeleteHostResourceHandler(baseCtx, "cleanup", instanceIDs)
	}()
	pr.Logger.Infof("cleanupResource.NotifyCMDB.sent")
	return nil
}

// UpdateResource 更新资源,仅支持主机类资源
func (r ResTemplate) UpdateResource(ctx context.Context, req *cloudman.ResCommonReq) (*cloudman.CommonResourceResp, error) {
	/* TODO:
	1. 一次仅允许修改一类资源
	2. 如存在多个isp+regionID应按照分类分别创建相关订单,为支持该功能需要修改传入参数的数据
	3. 暂不支持上述规则,由前端判定放行
	*/
	var form common.UpdateInstanceForm
	err := json.Unmarshal([]byte(req.Data), &form)
	if err != nil {
		return nil, err
	}

	type instanceType struct {
		IspID    string
		RegionID string
		Type     string
	}
	instanceMap := map[instanceType][]string{}
	if len(form.Instances) == 0 {
		return nil, fmt.Errorf("传入的变更资源为空")
	}

	for _, v := range form.Instances {
		var ispID, regionID string
		adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
		switch req.Type {
		case "host":
			hostResource, err := models.HostResourceModel.FindOne(adminCtx, map[string]interface{}{"InstanceID": v})
			if err != nil {
				logger.Errorf("find host %s error: %s", v, err.Error())
				return nil, err
			}
			if !models.CheckIspAndTagsPermFromCtx(ctx, "host", "admin", hostResource.IspID, models.EntityToCheckTags(hostResource.Tags.Tag)) {
				return nil, fmt.Errorf("没有更新%v资源的权限", v)
			}
			ispID = hostResource.IspID
			regionID = hostResource.RegionID
		case "mysql":
			mysqlResource, err := models.MysqlClusterResourceModel.FindOne(adminCtx, map[string]interface{}{"DBClusterId": v})
			if err != nil {
				logger.Errorf("find mysql %s error: %s", v, err.Error())
				return nil, err
			}
			if !models.CheckIspAndTagsPermFromCtx(ctx, "mysql", "admin", mysqlResource.IspID, models.EntityToCheckTags(mysqlResource.Tags.Tag)) {
				return nil, fmt.Errorf("没有更新%v资源的权限", v)
			}
			ispID = mysqlResource.IspID
			regionID = mysqlResource.RegionID
		case "redis":
			redisResource, err := models.RedisResourceModel.FindOne(adminCtx, map[string]interface{}{"InstanceID": v})
			if err != nil {
				logger.Errorf("find redis %s error: %s", v, err.Error())
				return nil, err
			}
			if !models.CheckIspAndTagsPermFromCtx(ctx, "redis", "admin", redisResource.IspID, models.RedisEntityToCheckTags(redisResource.Tags.Tag)) {
				return nil, fmt.Errorf("没有更新%v资源的权限", v)
			}
			ispID = redisResource.IspID
			regionID = redisResource.RegionID
		case "lb":
			lbResource, err := models.LoadBalancerModel.FindOne(adminCtx, map[string]interface{}{"LoadBalancerID": v})
			if err != nil {
				logger.Errorf("find lb %s error: %s", v, err.Error())
				return nil, err
			}
			ispID = lbResource.IspID
			regionID = lbResource.RegionID
		default:
			return nil, fmt.Errorf("unknown resource_type")
		}
		thisType := instanceType{IspID: ispID, RegionID: regionID, Type: req.Type}
		if instanceMap[thisType] == nil {
			instanceMap[thisType] = make([]string, 0)
		}
		instanceMap[thisType] = append(instanceMap[thisType], v)
	}

	if req.Type == "host" && form.Action != "delete" {
		unPass, err := models.HostResourceModel.IsLockOrRecycle(ctx, form.Instances)
		if err != nil {
			return nil, err
		}
		if unPass {
			return nil, fmt.Errorf("存在已锁定或已加入回收站的资源")
		}
	}

	if form.Action == "recycle" || form.Action == "cleanup" {
		assocResID, err := r.CheckCmdbAssocExist(ctx, req.Type, form.Instances)
		if err != nil {
			logger.Errorf("CheckCmdbAssocExist.error: %s", err.Error())
		}
		if len(assocResID) > 0 {
			return nil, fmt.Errorf("以下资源在cmdb中存在绑定关系，请按标准流程解除后重试: %v", assocResID)
		}
	}

	action := schema.ResourceModify
	if form.Action == "recycle" {
		action = schema.ResourceRecycle
	} else if form.Action == "backup" || form.Action == "ali_backup" {
		action = schema.ResourceBackup
	} else if form.Action == "cleanup" {
		action = schema.ResourceCleanup
	} else if form.Action == "modifyTags" {
		action = schema.ResourceModifyTags
	} else if form.Action == "stop" {
		action = schema.ResourceStop
	} else if form.Action == "start" {
		action = schema.ResourceStart
	}

	resForm := ResourceForm{
		Action:       action,
		IspID:        req.IspId,
		RegionPK:     req.Rid,
		InstanceType: req.Type,
		RegionID:     req.RegionId,
		RawData:      req.Data,
		Reason:       req.Reason,
	}

	pr, err := operateResourceBefore(ctx, resForm)
	if err != nil {
		return nil, err
	}

	order, err := createOrder(ctx, resForm, pr, "")
	if err != nil {
		return nil, err
	}
	// 根据规则创建修改审批单
	if cfg.GetSystemConfig().EnableWorkOrder && order.IspType != "custom" && order.Action != string(schema.ResourceBackup) && order.Action != string(schema.ResourceModifyTags) {
		if cfg.GetSystemConfig().Env == "local" {
			pr.Logger.Infof("开发环境请手动回调开始运行")
			return &cloudman.CommonResourceResp{Message: "开发环境请手动回调", OrderId: order.ID.Hex()}, err
		}
		data, err := orderDescribeToMD(order.RawData)
		if err != nil {
			logger.Errorf(err.Error())
			return nil, fmt.Errorf("订单转化markdown出错：%s", err.Error())
		}
		token, err := cfg.GetIAMCliToken(cfg.GetSystemConfig().WorkOrderIAMEnv)
		if err != nil {
			logger.Errorf(err.Error())
			return nil, err
		}
		ticketID, err := ferry.NewFerryClient(cfg.GetSystemConfig().WorkOrderHost, token).
			CreateTicket(ctx, ferry.FormValues{
				FormTitle:   fmt.Sprintf("申请%s-%s-%s", order.GetAction(), pr.IspName, order.GetOrderType()),
				FormFlowID:  cfg.GetSystemConfig().FlowID,
				FormCreator: permission.GetUsername(ctx),
				ResType:     order.GetOrderType(),
				Action:      order.GetAction(),
				IspName:     pr.IspName,
				FormReason:  req.Reason,
				OrderID:     order.ID.Hex(),
				Describe:    data, // 表单摘要
				Info:        fmt.Sprintf("%s/?order_id=%s", cfg.GetSystemConfig().OrderURL, order.ID.Hex()),
			})
		if err != nil {
			return nil, err
		}
		pr.Logger.Infof("该任务执行需审批等待审批结束后开始运行")
		_ = models.ResourceOrder.Update(ctx, order.ID.Hex(), map[string]interface{}{"ticket_id": ticketID})
		ferryDetailURL := fmt.Sprintf("%s/ferry/process/handle-ticket?workOrderId=%d&processId=%s",
			cfg.GetSystemConfig().WorkOrderWebHost, ticketID, cfg.GetSystemConfig().FlowID)
		return &cloudman.CommonResourceResp{Message: "订单已提交,等待审批", OrderId: order.ID.Hex(), FerryUrl: ferryDetailURL}, nil
	}

	// 标签修改不区分云厂商并且同步完成
	if form.Action == "modifyTags" {
		_, err := r.batchUpdateCustomResource(ctx, order, req.Data, pr)
		if err != nil {
			return nil, err
		}
		return &cloudman.CommonResourceResp{Message: "修改标签成功", OrderId: order.ID.Hex()}, nil
	}

	if order.IspType == "custom" || order.IspType == "jumpserver" {
		if form.Action == "recycle" {
			err := r.recycleResource(ctx, order, pr, form.Instances)
			if err != nil {
				return nil, err
			}
			return &cloudman.CommonResourceResp{OrderId: order.ID.Hex(), Message: "操作完成"}, nil
		}
		if req.ExtendType == "batchModifyHostAttrs" { // TODO: 破坏了设计规则
			// 批量更新主机属性
			_, err := r.batchUpdateCustomResource(ctx, order, req.Data, pr)
			if err != nil {
				return nil, err
			}
			return &cloudman.CommonResourceResp{OrderId: order.ID.Hex(), Message: "操作完成"}, nil
		} else if form.Action != "cleanup" {
			_, err := r.updateCustomResource(ctx, order, req.Data)
			if err != nil {
				return nil, err
			}
			return &cloudman.CommonResourceResp{OrderId: order.ID.Hex(), Message: "操作完成"}, nil
		}
	}

	// 线上备份+开发环境的工单无审批，会从这里走
	go func() {
		taskCtx, taskCancel := context.WithTimeout(context.Background(), 45*time.Minute)
		defer taskCancel()
		result, err := r.updateResource(taskCtx, order)
		if err != nil {
			logger.Errorf("UpdateResource(%s)执行错误：%s", order.ID.Hex(), err.Error())
			return
		}
		logger.Infof("UpdateResource(%s)执行成功：%s", order.ID.Hex(), result.Message)
	}()
	return &cloudman.CommonResourceResp{OrderId: order.ID.Hex(), Message: "任务已开始执行，无需工单审批"}, err
}

// ProxyRecoverResource -
// 主机的回收站生命周期中，入回收站在template方法组，出回收站在recycle方法组
// 线上均走异步工单，无矛盾；开发模式需同步调用，此处解决私有方法暴露问题
func (r ResTemplate) ProxyRecoverResource(ctx context.Context, order *entity.ResOrder) (*cloudman.Result, error) {
	return r.recoverResource(ctx, order)
}

// recoverResource 恢复主机, 前端实现按云厂商类型创建订单，后端可判断同一个订单中是否包含不同云厂商
func (r ResTemplate) recoverResource(ctx context.Context, order *entity.ResOrder) (*cloudman.Result, error) {
	var form common.UpdateInstanceForm
	var err error
	err = json.Unmarshal([]byte(order.RawData), &form)
	if err != nil {
		return nil, err
	}

	defer func() {
		operateResourceAfter(order, err)
	}()

	m, err := cloudmanCore.RecycleCore.GetModel(order.Type)
	if err != nil {
		return nil, err
	}

	logger := BuildLogger(order.ID.Hex())
	provideName := order.IspType + "_" + order.Type
	f, ok := provider.ProvideMap[provideName]
	if !ok {
		logger.Errorln("not found provider")
		return &cloudman.Result{Message: "not found provider"}, err
	}
	logger.Infof("初始化provider")
	pr, err := getIspInfo(ctx, order.IspID, "", order.RegionID)
	pr.SetLogger(logger)
	pFunc, err := f(*pr)
	if err != nil {
		pr.Logger.Errorf("初始化provider失败:%v", err.Error())
		return &cloudman.Result{Message: "初始化provider失败"}, err
	}

	for _, instanceID := range form.Instances {
		err = m.SetRecyclable(ctx, []string{instanceID}, false)
		if err != nil {
			return nil, err
		}

		c := entity.GetRecycleCollection(models.GetEngine())
		rec := &entity.Recycle{}
		_, err = models.FindOne(ctx, c, models.DefaultFilter(ctx, bson.E{Key: "instance_id", Value: instanceID}), rec)
		if err != nil {
			return nil, err
		}
		err = models.RecycleModel.Delete(ctx, rec.ID.Hex())
		if err != nil {
			return nil, err
		}
	}

	// host资源从回收站移出需要触发开机操作
	if order.Type == "host" {
		err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{
			"start_time": time.Now().Unix(),
			"status":     constant.TaskRunning,
		})
		buf, _ := json.Marshal(common.UpdateInstanceForm{Action: "start", Instances: form.Instances})
		err = pFunc.UpdateInstance(ctx, 5*time.Minute, order.ID.Hex(), buf)
	}

	if err != nil {
		return &cloudman.Result{Message: err.Error()}, err
	}
	return &cloudman.Result{Message: "ok"}, nil
}

func (r ResTemplate) updateResource(ctx context.Context, order *entity.ResOrder) (*cloudman.Result, error) {
	// 登记开始时间
	_ = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{
		"start_time": time.Now().Unix(),
	})
	var form common.UpdateInstanceForm
	var err error
	err = json.Unmarshal([]byte(order.RawData), &form)
	if err != nil {
		return nil, err
	}
	logger := BuildLogger(order.ID.Hex())
	pr, err := getIspInfo(ctx, order.IspID, "", order.RegionID)
	if err != nil {
		return nil, err
	}
	pr.SetLogger(logger)
	// 回收和销毁资源单独走逻辑，这块逻辑只区分host/mysql/redis
	// 备份/清理/开关机/重启走pFunc.UpdateInstance逻辑，这块逻辑会细化到具体厂商实现
	// 回收资源
	if form.Action == "recycle" {
		err := r.recycleResource(ctx, order, pr, form.Instances)
		if err != nil {
			pr.Logger.Errorf(err.Error())
			return nil, err
		}
		// 回收成功，进入下一步待销毁状态
		form.Action = "delete"
		rawData, err := json.Marshal(form)
		if err != nil {
			return nil, err
		}
		m := map[string]interface{}{
			"raw_data": string(rawData),
		}
		err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), m)
		if err != nil {
			return nil, err
		}
		pr.Logger.Infof("回收操作成功")
		return &cloudman.Result{Message: "ok"}, nil
	} else if form.Action == "delete" {
		err = destroyResource(ctx, order.IspID, order.Type, order.RegionID, form.Instances, order.CreateUser, form.IsForce, order)
		if err != nil {
			pr.Logger.Errorf(err.Error())
			return nil, err
		}
		form.Action = "deleted"
		rawData, err := json.Marshal(form)
		if err != nil {
			return nil, err
		}
		m := map[string]interface{}{
			"raw_data": string(rawData),
		}
		err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), m)
		if err != nil {
			return nil, err
		}
		pr.Logger.Infof("释放操作成功")
		return &cloudman.Result{Message: "ok"}, nil
	} else if form.Action == "cleanup" {
		err := r.cleanupResource(ctx, order, pr, form.Instances)
		if err != nil {
			pr.Logger.Errorf(err.Error())
			return nil, err
		}
		pr.Logger.Infof("清理操作成功")
		return &cloudman.Result{Message: "ok"}, nil
	} else {
		err = models.ResourceOrder.Update(context.Background(), order.ID.Hex(), map[string]interface{}{
			"start_time": time.Now().Unix(),
			"status":     constant.TaskRunning,
		})
	}

	provideName := pr.IspType + "_" + order.Type
	f, ok := provider.ProvideMap[provideName]
	if !ok {
		return nil, errors.New("not found provider")
	}

	defer func() {
		operateResourceAfter(order, err)
	}()
	pr.Logger.Infof("初始化provider")
	pFunc, err := f(*pr)
	if err != nil {
		pr.Logger.Errorf("初始化失败:%v", err.Error())
		return &cloudman.Result{Message: "任务初始化失败"}, nil
	}

	err = pFunc.UpdateInstance(ctx, 45*time.Minute, order.ID.Hex(), []byte(order.RawData))
	if err != nil {
		pr.Logger.Errorf("操作失败: %v", err.Error())
		return &cloudman.Result{Message: fmt.Sprintf("操作失败: %v", err.Error())}, nil
	}

	pr.Logger.Infof("操作成功")
	return &cloudman.Result{Message: "ok"}, nil
}

func flushLog(id string, result string) error {
	return models.TaskLogResultModel.Create(context.Background(), &entity.TaskLogResult{
		TaskLogID:  id,
		Stopped:    true,
		TaskResult: result,
	})
}

// List 获取资源组模板列表
func (r ResTemplate) List(ctx context.Context, req *cloudman.ResTemQueryReq) (*cloudman.ResTemListResp, error) {
	list, total, err := models.ResourceTemplate.Query(ctx, &schema.ResTemplateQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		ResTemplateColumnParam: schema.ResTemplateColumnParam{
			Name: req.Name,
			Type: req.Type,
			Isp:  req.Isp,
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	// utils.ArrayToSet(list, func() {
	//
	// })
	var l []string
	for _, v := range list {
		l = append(l, v.ISP)
	}

	if len(l) == 0 {
		return &cloudman.ResTemListResp{
			Total: 0,
			List:  make([]*cloudman.ResTemShort, 0),
		}, nil
	}
	accountMap, err := models.AccountModel.FindManyWithPkToMap(ctx, l)
	if err != nil {
		return nil, err
	}

	li := make([]*cloudman.ResTemShort, 0)
	for _, v := range list {
		li = append(li, &cloudman.ResTemShort{
			Id:          v.ID.Hex(),
			Name:        v.Name,
			IspId:       v.ISP,
			IspName:     accountMap[v.ISP].Name,
			IspType:     accountMap[v.ISP].AType,
			UpdatedTime: v.UpdatedTime,
			Type:        v.Type,
			BindRegion:  v.RId,
			Desc:        v.Desc,
		})
	}

	return &cloudman.ResTemListResp{Total: uint32(total), List: li}, err
}

// Create 创建资源组模板
func (r ResTemplate) Create(ctx context.Context, req *cloudman.ResTemCreateReq) (*cloudman.Result, error) {
	data := models.PbToResourceTemplate(req)

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}
	data.CreateUser = permission.GetUsername(ctx)

	err = models.ResourceTemplate.Create(ctx, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Info 获取资源组模板详情
func (r ResTemplate) Info(ctx context.Context, id *cloudman.ObjectID) (*cloudman.ResTemCreateReq, error) {
	data, err := models.ResourceTemplate.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	return models.ResourceTemplateToPb(data), nil
}

// Update 更新资源组模板
func (r ResTemplate) Update(ctx context.Context, req *cloudman.ResTemCreateReq) (*cloudman.Result, error) {
	data := models.PbToResourceTemplate(req)

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}

	data.UpdateUser = permission.GetUsername(ctx)

	err = models.ResourceTemplate.Update(ctx, req.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Del 删除资源组模板
func (r ResTemplate) Del(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	var data entity.ResTemplate
	data.IsDelete = 1
	data.DeletedTime = time.Now().Unix()

	err := models.ResourceTemplate.Update(ctx, id.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, err
}

// CheckInstanceNameSeq 检查主机名序号
func (r ResTemplate) CheckInstanceNameSeq(ctx context.Context, req *cloudman.CheckInstanceNameSeqReq) (*cloudman.CheckInstanceNameSeqResp, error) {
	var model cloudutils.ModelInterface
	switch req.ResourceType {
	case "host":
		model = models.HostResourceModel
	case "mysql":
		model = models.MysqlClusterResourceModel
	case "redis":
		model = models.RedisResourceModel
	default:
		return nil, fmt.Errorf("unsupport resource_type")
	}

	minUnused, maxUnused, err := cloudutils.GetCloudutils(model).CheckInstanceNameSeq(ctx, req.Prefix, req.Suffix)
	if err != nil {
		return nil, err
	}
	return &cloudman.CheckInstanceNameSeqResp{MinUnused: minUnused, MaxUnused: maxUnused}, nil
}

// GenInstanceNameWithRule -
func (r ResTemplate) GenInstanceNameWithRule(ctx context.Context, req *cloudman.GenInstanceNameWithRuleReq) (*cloudman.GenInstanceNameWithRuleResp, error) {
	sysType := cloudutils.Windows
	if req.GetSysType().Number() == 1 {
		sysType = cloudutils.Linux
	}

	var model cloudutils.ModelInterface
	switch req.ResourceType {
	case "host":
		model = models.HostResourceModel
	case "mysql":
		model = models.MysqlClusterResourceModel
	case "redis":
		model = models.RedisResourceModel
	default:
		return nil, fmt.Errorf("unsupport resource_type")
	}

	instanceNames, err := cloudutils.GetCloudutils(model).GenInstanceName(ctx, req.Rule, int(req.Count), req.IsUnique, sysType)
	sort.StringSlice(instanceNames).Sort()
	return &cloudman.GenInstanceNameWithRuleResp{InstanceNames: instanceNames}, err
}

// CheckCmdbAssocExist -
func (r ResTemplate) CheckCmdbAssocExist(ctx context.Context, resType string, resID []string) ([]string, error) {
	logger := logrus.StandardLogger()
	result := []string{}
	cmdbIDMapTest, err := cmdb.FindBkIDByBkName(logger, false, cmdb.CmdbTypeMap[resType], resID)
	if err != nil {
		return nil, err
	}
	for cloudID, cmdbID := range cmdbIDMapTest {
		resA, _ := cmdb.FindAssocByBkID(logger, false, cmdb.AssocQueryReq{
			BaseObjType: cmdb.CmdbTypeMap[resType],
			InstType:    cmdb.CmdbTypeMap[resType],
			InstID:      cmdbID,
		})
		resB, _ := cmdb.FindAssocByBkID(logger, false, cmdb.AssocQueryReq{
			BaseObjType: cmdb.CmdbTypeMap[resType],
			TargetType:  cmdb.CmdbTypeMap[resType],
			TargetID:    cmdbID,
		})
		if len(resA) > 0 || len(resB) > 0 {
			result = append(result, cloudID+"(test)")
		}
	}
	cmdbIDMapProd, err := cmdb.FindBkIDByBkName(logger, true, cmdb.CmdbTypeMap[resType], resID)
	if err != nil {
		return nil, err
	}
	for cloudID, cmdbID := range cmdbIDMapProd {
		resC, _ := cmdb.FindAssocByBkID(logger, true, cmdb.AssocQueryReq{
			BaseObjType: cmdb.CmdbTypeMap[resType],
			InstType:    cmdb.CmdbTypeMap[resType],
			InstID:      cmdbID,
		})
		resD, _ := cmdb.FindAssocByBkID(logger, true, cmdb.AssocQueryReq{
			BaseObjType: cmdb.CmdbTypeMap[resType],
			TargetType:  cmdb.CmdbTypeMap[resType],
			TargetID:    cmdbID,
		})
		if len(resC) > 0 || len(resD) > 0 {
			result = append(result, cloudID+"(prod)")
		}
	}
	return result, nil
}
