package service

import (
	"fmt"
	"net"

	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// IP 安全组对象
type IP struct {
}

// Describe 获取弹性公网IP列表
func (h IP) Describe(ctx context.Context, req *cloudman.DescribeIPReq) (*cloudman.DescribeIPRes, error) {
	resp, total, err := models.IPModel.Query(ctx, &schema.IPQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		IPColumnParam: schema.IPColumnParam{
			RegionID: req.RegionId,
			IspID:    req.Isp,
			Address:  req.Address,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.IPModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeIPRes{List: list, Total: int32(total)}, err
}

// CreateCustomIP ...
func (h IP) CreateCustomIP(ctx context.Context, req *cloudman.IPEntity) (*cloudman.Result, error) {
	ip := net.ParseIP(req.Address)
	ipType := "ip"
	if ip == nil {
		_, _, err := net.ParseCIDR(req.Address)
		if err != nil {
			return nil, fmt.Errorf("不是合法的ipv4地址或网段")
		}
		ipType = "cidr"
	}

	isp, err := models.AccountModel.Get(ctx, req.IspId)
	if err != nil {
		return nil, err
	}

	_, err = models.IPModel.CreateOrUpdate(ctx, &entity.IP{
		Type:             ipType,
		CreateType:       req.CreateType,
		Address:          req.Address,
		BindInstanceType: req.BindInstanceType,
		InstanceID:       req.InstanceId,
		Desc:             req.Desc,
		RegionID:         req.RegionId,
		IspID:            req.IspId,
		IspType:          isp.AType,
	}, false)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// DescribeByIPs ...
func (h IP) DescribeByIPs(ctx context.Context, req *cloudman.DescribeByIPsReq) (*cloudman.DescribeByIPsRes, error) {
	ips, err := models.IPModel.FindManyWithIPs(ctx, req.Addresses)
	if err != nil {
		return nil, err
	}
	ipPbs, err := models.IPModelToPb(ctx, ips)
	if err != nil {
		return nil, err
	}
	ipMap := map[string][]*cloudman.IPEntity{}
	for _, ipPb := range ipPbs {
		ipMap[ipPb.Address] = append(ipMap[ipPb.Address], ipPb)
	}
	var ipMapSlice []*cloudman.IPMap
	for address, list := range ipMap {
		ipMapSlice = append(ipMapSlice, &cloudman.IPMap{
			Address: address,
			List:    list,
		})
	}
	return &cloudman.DescribeByIPsRes{
		IpMap: ipMapSlice,
	}, nil
}

func (h IP) DescribeIPGroup(ctx context.Context, req *cloudman.DescribeIPGroupReq) (*cloudman.DescribeIPGroupResp, error) {
	resp, total, err := models.IPGroupModel.Query(ctx, &schema.IPGroupQueryParam{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		IPGroupColumnParam: schema.IPGroupColumnParam{
			Name:        req.Name,
			Description: req.Description,
		},
		OrderParams: schema.OrderParams{},
	})

	if err != nil {
		return nil, err
	}

	groups, err := models.IPGroupsToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeIPGroupResp{
		List:  groups,
		Total: int32(total),
	}, nil
}

func (h IP) CreateIPGroup(ctx context.Context, req *cloudman.CreateIPGroupReq) (*cloudman.Result, error) {
	e, err := models.PbToIPGroup(req.Name, req.Description, req.IpIds)
	if err != nil {
		return nil, err
	}
	e.CreateUser = permission.GetUsername(ctx)
	e.UpdateUser = permission.GetUsername(ctx)

	_, err = models.IPGroupModel.CreateOrUpdate(ctx, e)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{
		Message: "创建成功",
	}, nil
}

func (h IP) ModifyIPGroup(ctx context.Context, req *cloudman.ModifyIPGroupReq) (*cloudman.Result, error) {
	e, err := models.IPGroupModel.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	e.Description = req.Description
	e.RelatedIPs = req.IpIds
	e.UpdateUser = permission.GetUsername(ctx)
	_, err = models.IPGroupModel.CreateOrUpdate(ctx, e)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{
		Message: "更新成功",
	}, nil
}

func (h IP) DeleteIPGroup(ctx context.Context, req *cloudman.DeleteIPGroupReq) (*cloudman.Result, error) {
	err := models.IPGroupModel.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{
		Message: "删除成功",
	}, nil
}

func (h IP) DeleteIP(ctx context.Context, req *cloudman.DeleteIPReq) (*cloudman.Result, error) {
	err := models.IPModel.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{
		Message: "删除成功",
	}, nil
}
