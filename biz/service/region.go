package service

import (
	"context"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// Region zone
type Region struct {
}

// Info 获取详情
func (r Region) Info(ctx context.Context, id *cloudman.ObjectID) (*cloudman.RegionDetail, error) {
	res, err := models.RegionModel.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	return &cloudman.RegionDetail{
		Name:     res.Name,
		IspType:  res.IspType,
		RegionId: res.RegionID,
		Id:       res.ID.Hex(),
	}, nil
}

// Create 创建Region
func (r Region) Create(ctx context.Context, request *cloudman.RegionDetail) (*cloudman.Result, error) {
	region := &entity.Region{
		Name:     request.Name,
		IspType:  request.IspType,
		RegionID: request.RegionId,
	}
	err := models.RegionModel.FindOrCreate(ctx, region)
	if err != nil {
		logger.Error(fmt.Sprintf("region model操作失败:%s", err.Error()))
		return nil, fmt.Errorf("创建Region（%s）报错：%v", request.RegionId, err)
	}
	return &cloudman.Result{
		Message: "创建Region成功",
	}, nil
}

// List  获取Region列表
func (r Region) List(ctx context.Context, request *cloudman.RegionQueryRequest) (*cloudman.RegionListResponse, error) {
	res, total, err := models.RegionModel.Query(ctx, &schema.RegionQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: request.Page,
			Size: request.Size,
		},
		RegionColumnParam: schema.RegionColumnParam{
			Name:    request.Name,
			IspType: request.Type,
		},
		RegionSearchParam: schema.RegionSearchParam{
			Keywords: request.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: request.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var list []*cloudman.RegionDetail
	for _, v := range res {
		list = append(list, &cloudman.RegionDetail{
			Id:       v.ID.Hex(),
			Name:     v.Name,
			IspType:  v.IspType,
			RegionId: v.RegionID,
		})
	}

	return &cloudman.RegionListResponse{
		Total: uint32(total),
		List:  list,
	}, nil
}
