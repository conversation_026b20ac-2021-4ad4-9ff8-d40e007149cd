package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/mitchellh/mapstructure"
	"github.com/sirupsen/logrus"
	"github.com/tealeg/xlsx"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// RedisRes 主机对象
type RedisRes struct {
}

// DescribeParamsGroups 获取参数组
func (h RedisRes) DescribeParamsGroups(ctx context.Context, req *cloudman.CacheParamsGroupsReq) (*cloudman.CacheParamsGroupsResp, error) {
	client, err := getCloudCache(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeParamsGroups(ctx, &common.DescribeParameterGroupsInput{
		GroupName:     req.GroupName,
		EngineName:    req.Engine,
		EngineVersion: req.EngineVersion,
	})
}

// GetCacheTypes 获取缓存类型
func (h RedisRes) GetCacheTypes(ctx context.Context, req *cloudman.DBTypeReq) (*cloudman.DBTypeResp, error) {
	client, err := getCloudCache(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.GetDBTypes(ctx)
}

// DescribeAvailableZone 获取可用区
func (h RedisRes) DescribeAvailableZone(ctx context.Context, req *cloudman.DBZoneReq) (*cloudman.DBZoneResp, error) {
	client, err := getCloudCache(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}
	return client.ListAvailableZone(ctx, &common.DescribeZoneInput{})
}

// DescribeCacheClasses 获取实例规格
func (h RedisRes) DescribeCacheClasses(ctx context.Context, req *cloudman.CacheClassReq) (*cloudman.CacheClassesResp, error) {
	client, err := getCloudCache(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}
	return client.GetAvailableClasses(ctx, &common.AvailableClassesInput{
		EngineName:    req.DBType,
		EngineVersion: req.DBVersion,
		// Architecture:  req.,
		ZoneID:      req.ZoneId,
		ProductType: req.ProductType,
	})
}

func manyEntityToPbRedis(ctx context.Context, data []*entity.RedisResource) (resp []*cloudman.RedisDetail, err error) {
	var ids []string
	for _, v := range data {
		isp := v.IspID
		if isp != "" {
			ids = append(ids, isp)
		}
	}

	var ac map[string]entity.Account
	if len(ids) != 0 {
		ac, err = models.AccountModel.FindManyWithPkToMap(ctx, ids)
		if err != nil {
			return nil, err
		}
	}

	for _, v := range data {
		var tags []*cloudman.ResourceTag
		for _, tag := range v.Tags.Tag {
			tags = append(tags, &cloudman.ResourceTag{Key: tag.Key, Value: tag.Value})
		}
		detail := &cloudman.RedisDetail{
			Id:               v.ID.Hex(),
			IsLock:           v.IsLock,
			IspId:            v.IspID,
			IspName:          ac[v.IspID].Name,
			IspType:          v.IspType,
			VpcId:            v.VpcID,
			CreateTime:       v.CreateTime,
			Tags:             tags,
			ResourceGroupId:  v.ResourceGroupID,
			ZoneId:           v.ZoneID,
			Port:             v.Port,
			RegionID:         v.RegionID,
			EngineVersion:    v.EngineVersion,
			InstanceClass:    v.InstanceClass,
			QPS:              v.QPS,
			Connections:      v.Connections,
			Bandwidth:        v.Bandwidth,
			PrivateIp:        v.PrivateIP,
			NetworkType:      v.NetworkType,
			Capacity:         v.Capacity,
			InstanceId:       v.InstanceID,
			InstanceType:     v.InstanceType,
			InstanceName:     v.InstanceName,
			InstanceStatus:   v.TransStatus(),
			ChargeType:       v.ChargeType,
			ConnectionDomain: v.ConnectionDomain,
		}

		resp = append(resp, detail)
	}

	return
}

// GetAccounts 获取redis账户信息
func (h RedisRes) GetAccounts(ctx context.Context, cid *cloudman.ObjectID) (*cloudman.RedisAccounts, error) {
	list, err := models.RedisAccountResourceModel.FindMany(ctx, map[string]interface{}{"InstanceID": cid.Id})
	if err != nil {
		return nil, err
	}

	var account []*cloudman.RedisAccount
	for _, v := range list {
		var privilege []*cloudman.DatabasePrivilege
		for _, p := range v.DatabasePrivileges {
			privilege = append(privilege, &cloudman.DatabasePrivilege{
				AccountPrivilege: p.AccountPrivilege,
			})
		}

		account = append(account, &cloudman.RedisAccount{
			Id:                 v.ID.Hex(),
			IsLock:             v.IsLock,
			IspId:              v.IspID,
			IspType:            v.IspType,
			InstanceId:         v.InstanceID,
			AccountName:        v.AccountName,
			AccountStatus:      v.AccountStatus,
			AccountType:        v.AccountType,
			AccountDescription: v.AccountDescription,
			DatabasePrivileges: privilege,
		})
	}

	return &cloudman.RedisAccounts{List: account}, nil
}

// GetWhitelists 获取白名单列表
func (h RedisRes) GetWhitelists(ctx context.Context, cid *cloudman.ObjectID) (*cloudman.WhitelistInfo, error) {
	instance, err := models.RedisResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": cid.Id})
	if err != nil {
		return nil, err
	}

	var ipGroups []*cloudman.InstanceIPGroup
	for _, ipg := range instance.IPGroups {
		ipGroups = append(ipGroups, &cloudman.InstanceIPGroup{
			SecurityIpGroupAttribute: ipg.SecurityIPGroupAttribute,
			SecurityIpGroupName:      ipg.SecurityIPGroupName,
			SecurityIpList:           ipg.SecurityIPList,
		})
	}

	return &cloudman.WhitelistInfo{
		ClusterId:        instance.InstanceID,
		SecurityGroupIds: &cloudman.InstanceSecurityGroupIds{SecurityGroupID: instance.SecurityGroupIds.SecurityGroupID},
		IpGroups:         ipGroups,
	}, nil
}

// Info 获取详情
func (h RedisRes) Info(ctx context.Context, cid *cloudman.ObjectID) (*cloudman.RedisDetail, error) {
	resp, err := models.RedisResourceModel.FindOne(ctx, map[string]interface{}{
		"InstanceID": cid.Id,
	})
	if err == mongo.ErrNoDocuments {
		return nil, errors.New("没有查看此资源的权限")
	} else if err != nil {
		return nil, err
	}

	var tags []*cloudman.ResourceTag
	for _, v := range resp.Tags.Tag {
		tags = append(tags, &cloudman.ResourceTag{
			Key:   v.Key,
			Value: v.Value,
		})
	}

	ac, err := models.AccountModel.FindPK(ctx, resp.IspID)
	if err != nil {
		logger.Errorf("Find PK Error:", resp.IspID)
		return nil, err
	}

	return &cloudman.RedisDetail{
		Id:                  resp.ID.Hex(),
		IsLock:              resp.IsLock,
		IspId:               resp.IspID,
		IspName:             ac.Name,
		IspType:             resp.IspType,
		ReplacateId:         resp.ReplacateID,
		InstanceId:          resp.InstanceID,
		InstanceName:        resp.InstanceName,
		SearchKey:           resp.SearchKey,
		ConnectionDomain:    resp.ConnectionDomain,
		Port:                resp.Port,
		UserName:            resp.UserName,
		Tags:                tags,
		InstanceStatus:      resp.TransStatus(),
		RegionID:            resp.RegionID,
		Capacity:            resp.Capacity,
		InstanceClass:       resp.InstanceClass,
		QPS:                 resp.QPS,
		Bandwidth:           resp.Bandwidth,
		Connections:         resp.Connections,
		ZoneId:              resp.ZoneID,
		Config:              resp.Config,
		ChargeType:          resp.ChargeType,
		NetworkType:         resp.NetworkType,
		VpcId:               resp.VpcID,
		VSwitchId:           resp.VSwitchID,
		PrivateIp:           resp.PrivateIP,
		CreateTime:          resp.CreateTime,
		EndTime:             resp.EndTime,
		HasRenewChangeOrder: resp.HasRenewChangeOrder,
		IsRds:               resp.IsRds,
		InstanceType:        resp.InstanceType,
		ArchitectureType:    resp.ArchitectureType,
		NodeType:            resp.NodeType,
		PackageType:         resp.PackageType,
		EngineVersion:       resp.EngineVersion,
		DestroyTime:         resp.DestroyTime,
		ConnectionMode:      resp.ConnectionMode,
		ResourceGroupId:     resp.ResourceGroupID,
		ShardCount:          resp.ShardCount,
	}, nil
}

// UnlockResource 解锁资源对象
func (h RedisRes) UnlockResource(ctx context.Context, ids *cloudman.Ids) (*cloudman.Result, error) {
	err := models.RedisResourceModel.UpdateLockStatus(ctx, ids.Ids, false)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// ImportXlsx 只允许导入自定义厂商对象
func (h RedisRes) ImportXlsx(ctx context.Context, req *cloudman.ImportXlsxReq) (*cloudman.BatchResult, error) {
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	isp, err := models.AccountModel.Get(ctx, req.IspId)
	if err != nil {
		return nil, err
	}

	file, err = xlsx.OpenBinary(req.GetFile())
	if err != nil {
		return nil, err
	}

	st := models.RedisResourceModel.FiledOptions(ctx)
	redisMetaMap := make(map[string]*entity.FiledStruct)
	for _, v := range st {
		if v.Extra { // 跳过特别增加的字段
			continue
		}
		redisMetaMap[v.Name] = &entity.FiledStruct{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
		}
	}

	sheet = file.Sheets[0]
	// step1 读取标题
	// step2 标题与Value对应
	row = sheet.Row(0)
	var rowList []*entity.FiledStruct
	for _, cell = range row.Cells {
		if k, ok := redisMetaMap[cell.Value]; ok {
			rowList = append(rowList, k)
		} else {
			rowList = append(rowList, nil)
		}
	}

	var redisList []*entity.RedisResource
	for i := 1; i < len(sheet.Rows); i++ {
		row = sheet.Rows[i]
		m := make(map[string]interface{})
		for index, cellStr := range rowList {
			if cellStr == nil {
				continue
			}
			cell = row.Cells[index]
			value := row.Cells[index].Value
			switch cellStr.Type {
			case "float32":
				if value != "" {
					val, err := cell.Float()
					if err != nil {
						return nil, err
					}
					m[cellStr.Value] = val
				}
				m[cellStr.Value] = 0
				break
			case "int64":
				if value != "" {
					val, err := cell.Int64()
					if err != nil {
						return nil, err
					}
					m[cellStr.Value] = val
				} else {
					m[cellStr.Value] = 0
				}
				break
			case "int32":
				if value != "" {
					val, err := cell.Int()
					if err != nil {
						return nil, err
					}
					m[cellStr.Value] = val
				} else {
					m[cellStr.Value] = 0
				}
				break
			case "bool":
				m[cellStr.Value] = cell.Bool()
				break
			case "struct":
				if value == "" {
					m[cellStr.Value] = nil
					break
				}
				var rawMap map[string]interface{}
				err = json.Unmarshal([]byte(value), &rawMap)
				if err != nil {
					return nil, err
				}

				m[cellStr.Value] = rawMap
				break
			case "slice":
				if value == "" {
					m[cellStr.Value] = nil
					break
				}
				var list []interface{}
				err = json.Unmarshal([]byte(value), &list)
				if err != nil {
					return nil, err
				}

				m[cellStr.Value] = list
				break
			default:
				m[cellStr.Value] = value
			}
		}
		var redis *entity.RedisResource
		err = mapstructure.Decode(m, &redis)
		if err != nil {
			return nil, err
		}

		redis.IspID = isp.ID.Hex() // 前端传入
		redis.IspType = "custom"   // 只允许自定义厂商
		redis.RegionID = "cn-shanghai"
		redis.InstanceStatus = "Running"
		redis.InstanceID = fmt.Sprintf("%s_%s", redis.InstanceID, redis.IspID)

		redisList = append(redisList, redis)
	}

	result, err := models.RedisResourceModel.BatchCreateOrUpdate(ctx, redisList)
	if err != nil {
		return nil, err
	}

	return &cloudman.BatchResult{
		Errors:         result.Errors,
		Success:        result.Success,
		SuccessCreated: result.SuccessCreated,
		SuccessUpdated: result.SuccessUpdated,
		UpdateErrors:   result.UpdateErrors,
	}, nil
}

// Option 获取描述信息
func (h RedisRes) Option(ctx context.Context, empty *cloudman.Empty) (*cloudman.OptionResp, error) {
	st := models.RedisResourceModel.FiledOptions(ctx)
	var li []*cloudman.FieldOption

	for _, v := range st {
		li = append(li, &cloudman.FieldOption{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
			Indexed:  v.Indexed,
			Extra:    v.Extra,
		})
	}

	return &cloudman.OptionResp{List: li}, nil
}

// LockResource 锁定资源
func (h RedisRes) LockResource(ctx context.Context, ids *cloudman.Ids) (*cloudman.Result, error) {
	err := models.RedisResourceModel.UpdateLockStatus(ctx, ids.Ids, true)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// Describe 获取redis资源列表
func (h RedisRes) Describe(ctx context.Context, req *cloudman.RedisReq) (*cloudman.RedisResp, error) {
	resp, total, err := models.RedisResourceModel.Query(ctx, &schema.RedisResourceQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		RedisResourceColumnParam: schema.RedisResourceColumnParam{
			IspID:              req.Isp,
			IspType:            req.Type,
			RegionID:           req.RegionId,
			TagKey:             req.TagKey,
			TagValues:          req.TagValues,
			InstanceID:         req.InstanceId,
			DisplayRecyclable:  req.DisplayRecyclable,
			DisplayNeedCleanup: req.DisplayNeedCleanup,
			InstanceStatus:     req.InstanceStatus,
			UpdateTime: schema.UpdateTimeColumnParam{
				Ref: req.UpdateTimeRef,
				Val: req.UpdateTimeVal,
			},
			SecurityGroupIds:          req.SecurityGroupIds,
			AndSearchSecurityGroupIds: req.AndSearchSecurityGroupIds,
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Keywords,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var list []*cloudman.RedisDetail

	var ids []string
	for _, v := range resp {
		isp := v.IspID
		if isp != "" {
			ids = append(ids, isp)
		}
	}

	var ac map[string]entity.Account
	if len(ids) != 0 {
		ac, err = models.AccountModel.FindManyWithPkToMap(ctx, ids)
		if err != nil {
			return nil, err
		}
	}

	// 查询securityGroup信息
	sgIDMap := map[string]bool{}
	for _, v := range resp {
		for _, sgID := range v.SecurityGroupIds.SecurityGroupID {
			sgIDMap[sgID] = true
		}
	}
	var securityGroupIDs []string
	for sgID := range sgIDMap {
		securityGroupIDs = append(securityGroupIDs, sgID)
	}
	var sgMap map[string]entity.SecurityGroup
	if len(securityGroupIDs) != 0 {
		sgMap, err = models.SecurityGroupModel.FindManyWithIDToMap(ctx, securityGroupIDs)
		if err != nil {
			return nil, err
		}
	}

	for _, v := range resp {
		var tags []*cloudman.ResourceTag
		for _, tag := range v.Tags.Tag {
			tags = append(tags, &cloudman.ResourceTag{Key: tag.Key, Value: tag.Value})
		}
		var sgInfos []*cloudman.SecurityGroupInfo
		for _, sgID := range v.SecurityGroupIds.SecurityGroupID {
			sg := sgMap[sgID]
			sgInfos = append(sgInfos, &cloudman.SecurityGroupInfo{
				SecurityGroupId:   sgID,
				SecurityGroupName: sg.SecurityGroupName,
				Description:       sg.Description,
				VpcId:             sg.VpcID,
			})
		}
		detail := &cloudman.RedisDetail{
			Id:                 v.ID.Hex(),
			IsLock:             v.IsLock,
			IspId:              v.IspID,
			IspName:            ac[v.IspID].Name,
			IspType:            v.IspType,
			VpcId:              v.VpcID,
			CreateTime:         v.CreateTime,
			Tags:               tags,
			ResourceGroupId:    v.ResourceGroupID,
			ZoneId:             v.ZoneID,
			Port:               v.Port,
			RegionID:           v.RegionID,
			EngineVersion:      v.EngineVersion,
			InstanceClass:      v.InstanceClass,
			QPS:                v.QPS,
			Connections:        v.Connections,
			Bandwidth:          v.Bandwidth,
			PrivateIp:          v.PrivateIP,
			NetworkType:        v.NetworkType,
			Capacity:           v.Capacity,
			InstanceId:         v.InstanceID,
			InstanceType:       v.InstanceType,
			InstanceName:       v.InstanceName,
			InstanceStatus:     v.TransStatus(),
			ChargeType:         v.ChargeType,
			ConnectionDomain:   v.ConnectionDomain,
			ArchitectureType:   v.ArchitectureType,
			UpdatedTime:        v.UpdatedTime,
			Recyclable:         v.Recyclable,
			NeedCleanup:        v.NeedCleanup,
			SecurityGroupInfos: sgInfos,
		}
		list = append(list, detail)
	}

	return &cloudman.RedisResp{List: list, Total: int32(total)}, err
}

// ExportXlsx 导出xlsx
func (h RedisRes) ExportXlsx(ctx context.Context, req *cloudman.ExportXlsxReq) (*cloudman.ExportXlsxResp, string, error) {
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	st := models.RedisResourceModel.FiledOptions(ctx)
	file = xlsx.NewFile()
	sheet, err = file.AddSheet("Redis实例列表")
	if err != nil {
		return nil, "", err
	}
	// 加入标题(根据自定义导出列返回)

	selectFieldMap := map[string]struct{}{}
	for _, v := range req.ExportFields {
		selectFieldMap[v] = struct{}{}
	}

	var rowList []*entity.FiledStruct
	row = sheet.AddRow()
	for _, v := range st {
		if v.Extra { // 跳过特别增加的字段
			continue
		}
		if len(req.ExportFields) != 0 {
			if _, ok := selectFieldMap[v.Value]; !ok {
				continue
			}
		}

		cell = row.AddCell()
		cell.Value = v.Name
		if v.Required {
			cell.SetStyle(&xlsx.Style{
				Font: xlsx.Font{
					Bold: true,
				},
			})
		}

		rowList = append(rowList, &entity.FiledStruct{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
		})
	}

	var res []*entity.RedisResource
	if req.ExportType == "selectWithCond" {
		res, _, err = models.RedisResourceModel.Query(ctx, &schema.RedisResourceQueryParams{
			PaginationParam: schema.PaginationParam{
				Size: 9999,
			},
			RedisResourceColumnParam: schema.RedisResourceColumnParam{
				IspID:    req.Isp,
				RegionID: req.RegionId,
			},
		})
	}

	if req.ExportType == "" {
		// 导出全部
		res, err = models.RedisResourceModel.FindMany(ctx, map[string]interface{}{})
	}

	if req.ExportType == "select" && len(req.Selected) != 0 {
		// 导出范围: 导出选中并且选中的内容为空则直接返回
		var selected []string
		for _, v := range req.Selected {
			selected = append(selected, v.Id)
		}
		res, err = models.RedisResourceModel.FindWithManyPK(ctx, selected)
	}

	if err != nil {
		return nil, "", err
	}

	for _, v := range res {
		row = sheet.AddRow()
		strMap, err := mapstruct.Struct2Map(v)
		if err != nil {
			break
		}

		for _, field := range rowList {

			cell = row.AddCell()
			if strMap[field.Value] == nil {
				cell.Value = ""
				continue
			}

			if field.Value == "_id" {
				cell.Value = v.ID.Hex()
				continue
			}

			if field.Type == "struct" {
				str, err1 := json.Marshal(strMap[field.Value])
				if err1 != nil {
					logger.Errorf(err1.Error())
				}

				cell.Value = string(str)
			} else {
				cell.SetValue(strMap[field.Value])
			}
			// if field.Type == "string" {
			// 	cell.Value = (strMap[field.Value]).(string)
			// } else if field.Type == "int64" {
			// 	cell.SetInt64((strMap[field.Value]).(int64))
			// } else if field.Type == "int" {
			// 	cell.SetInt((strMap[field.Value]).(int))
			// } else {
			// 	str, err1 := json.Marshal(strMap[field.Value])
			// 	if err1 != nil {
			// 		logger.Println(err1.Error())
			// 	}
			//
			// 	cell.Value = string(str)
			// }
		}
	}

	var buf bytes.Buffer
	err = file.Write(&buf)
	if err != nil {
		return nil, "", err
	}

	filename := fmt.Sprintf("redis_instance-%s.xlsx", time.Now().Format("20060102150405"))

	return &cloudman.ExportXlsxResp{File: buf.Bytes()}, filename, err
}

// GetSecurityGroup 获取安全组列表
func (h RedisRes) GetSecurityGroup(ctx context.Context, req *cloudman.SecurityGroupReq) (*cloudman.SecurityGroupResp, error) {
	client, err := getCloudCache(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeSecurityGroups(ctx, &common.DescribeSecurityGroupInput{
		VpcID: req.VpcId,
		Size:  req.Size,
	})
}

// GetNetwork 获取VPC网络
func (h RedisRes) GetNetwork(ctx context.Context, req *cloudman.NetworkReq) (*cloudman.NetworkResp, error) {
	client, err := getCloudCache(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeNetworks(ctx, &common.DescribeNetworksInput{})
}

// GetVSwitch 获取交换机子网
func (h RedisRes) GetVSwitch(ctx context.Context, req *cloudman.VSwitchReq) (*cloudman.VSwitchResp, error) {
	client, err := getCloudCache(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeSubnets(ctx, &common.DescribeSubnetsInput{VpcID: req.VpcId, ZoneID: req.ZoneId})
}

// SyncRedisInstances ...
func (h RedisRes) SyncRedisInstances(ctx context.Context, req *cloudman.SyncInstancesReq) (*cloudman.Result, error) {
	if len(req.Ids) == 0 {
		return nil, errors.New("empty host resource ids")
	}
	allRedis, err := models.RedisResourceModel.FindWithManyPK(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	// key: account values: mysqls
	accountRedisMap := map[string][]*entity.RedisResource{}
	for _, redis := range allRedis {
		redises, ok := accountRedisMap[redis.IspID]
		if !ok {
			accountRedisMap[redis.IspID] = []*entity.RedisResource{redis}
		} else {
			redises = append(redises, redis)
			accountRedisMap[redis.IspID] = redises
		}
	}

	for ispID, accountRedises := range accountRedisMap {
		account, err := models.AccountModel.Get(ctx, ispID)
		if err != nil {
			logger.Errorf("handler:SyncResource:FindAccountFailed: accountID=%s err=%s", ispID, err.Error())
			continue
		}

		// key: region values: instanceIDs
		redisRegionMap := map[string][]string{}
		for _, redis := range accountRedises {
			redisRegions, ok := redisRegionMap[redis.RegionID]
			if !ok {
				redisRegionMap[redis.RegionID] = []string{redis.InstanceID}
			} else {
				redisRegions = append(redisRegions, redis.InstanceID)
				redisRegionMap[redis.RegionID] = redisRegions
			}
		}

		for regionID, instances := range redisRegionMap {
			for i := 0; i <= len(instances)/30; i++ {
				var searchInstances []string
				if i == len(instances)/30 {
					searchInstances = instances[i*30:]
				} else {
					searchInstances = instances[i*30 : (i+1)*30]
				}
				// send requests to Aliyun
				if account.AType == "aliyun" {
					aliSyncer := synctask.GetAliyunSyncer()
					if err := aliSyncer.SyncRedis(ctx, nil, synctask.SyncOption{RegionID: regionID, IspID: ispID}, searchInstances...); err != nil {
						return nil, err
					}
				} else if account.AType == "aws" {
					awsSyncer := &synctask.AwsSyncer{}
					if err := awsSyncer.SyncRedis(ctx, nil, synctask.SyncOption{RegionID: regionID, IspID: ispID}, searchInstances...); err != nil {
						return nil, err
					}
				}
			}
		}
	}

	return &cloudman.Result{Message: "success"}, nil
}

func (h RedisRes) GetBackupDetail(ctx context.Context, req *cloudman.OrderID) (*cloudman.GetBackupDetailResp, error) {
	order, err := models.ResourceOrder.Get(ctx, req.OrderId)
	if err != nil {
		return nil, err
	}
	if order.Action != "backup" {
		return nil, fmt.Errorf("wrong order type %s", order.Action)
	}
	if order.Status != 2 {
		return nil, fmt.Errorf("wrong status %d, order is not in success status", order.Status)
	}

	detail, err := getBakcupDetail(ctx, order)
	if err != nil {
		return nil, err
	}
	return &cloudman.GetBackupDetailResp{
		BackupDetailInfos: detail,
	}, nil
}

type instanceBackupOrderInfo struct {
	InstanceID   string                 `json:"instance_id"`
	InstanceName string                 `json:"instance_name"`
	Jobs         map[string]interface{} `json:"jobs"`
}

func getBakcupDetail(ctx context.Context, order *entity.ResOrder) ([]*cloudman.BackupDetailInfo, error) {
	resMap := map[string]instanceBackupOrderInfo{}
	err := json.Unmarshal([]byte(order.StatusDetail), &resMap)
	if err != nil {
		return nil, err
	}
	infos := make([]*cloudman.BackupDetailInfo, 0)
	for _, info := range resMap {
		nodeDownloadUrls := make([]*cloudman.NodeInstanceDownloadUrl, 0)
		for k := range info.Jobs {
			jobId, err := strconv.Atoi(strings.Trim(k, "j")) // remove start j
			if err != nil {
				return nil, err
			}
			nodeurls, err := getJobInfo(ctx, order, info.InstanceID, int64(jobId))
			if err != nil {
				return nil, err
			}
			nodeDownloadUrls = append(nodeDownloadUrls, nodeurls...)
		}
		infos = append(infos, &cloudman.BackupDetailInfo{
			InstanceId:       info.InstanceID,
			InstanceName:     info.InstanceName,
			NodeDownloadUrls: nodeDownloadUrls,
		})
	}
	return infos, nil
}

func getJobInfo(ctx context.Context, order *entity.ResOrder, instanceId string, jobId int64) ([]*cloudman.NodeInstanceDownloadUrl, error) {
	client, err := alicloud.CreateAliKvStoreClient(order.RegionID, order.IspID, false, logrus.New())
	if err != nil {
		return nil, err
	}
	resp, err := client.DescribeBackups(ctx, 1*time.Minute, instanceId, jobId)
	if err != nil {
		return nil, err
	}

	res := make([]*cloudman.NodeInstanceDownloadUrl, 0)
	for _, backup := range resp.Backups.Backup {
		res = append(res, &cloudman.NodeInstanceDownloadUrl{
			NodeId:              tea.StringValue(backup.NodeInstanceId),
			IntranetDownloadUrl: tea.StringValue(backup.BackupIntranetDownloadURL),
		})
	}
	return res, nil
}
