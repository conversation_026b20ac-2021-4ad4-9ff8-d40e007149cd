package service

import (
	"context"
	"time"

	"github.com/alibabacloud-go/tea/tea"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// ResourceGroup 资源组
type ResourceGroup struct {
}

// List 资源组列表查询方法
func (r ResourceGroup) List(ctx context.Context, req *cloudman.GroupQueryReq) (*cloudman.GroupListResp, error) {
	list, total, err := models.ResourceGroup.Query(ctx, &schema.ResourceGroupQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		ResourceGroupColumnParam: schema.ResourceGroupColumnParam{
			Name: req.Name,
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var li []*cloudman.GroupInfo
	for _, v := range list {
		li = append(li, &cloudman.GroupInfo{
			Id:   v.ID.Hex(),
			Name: v.Name,
			Desc: v.Desc,
		})
	}

	return &cloudman.GroupListResp{Total: uint32(total), List: li}, err
}

// Create 新建组
func (r ResourceGroup) Create(ctx context.Context, req *cloudman.GroupCreateReq) (*cloudman.Result, error) {
	data := &entity.ResGroup{
		Name: req.Name,
		Desc: req.Desc,
	}

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}

	data.CreateUser = permission.GetUsername(ctx)

	err = models.ResourceGroup.Create(ctx, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Info 组详情
func (r ResourceGroup) Info(ctx context.Context, id *cloudman.ObjectID) (*cloudman.GroupInfo, error) {
	data, err := models.ResourceGroup.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	return &cloudman.GroupInfo{
		Id:   data.ID.Hex(),
		Name: data.Name,
		Desc: data.Desc,
	}, nil
}

// Update 更新组信息
func (r ResourceGroup) Update(ctx context.Context, req *cloudman.GroupCreateReq) (*cloudman.Result, error) {
	data := &entity.ResGroup{
		Name: req.Name,
		Desc: req.Desc,
	}

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}

	data.UpdateUser = permission.GetUsername(ctx)

	err = models.ResourceGroup.Update(ctx, req.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Del 删除详情组
func (r ResourceGroup) Del(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	var data entity.ResGroup
	data.IsDelete = 1
	data.DeletedTime = time.Now().Unix()

	err := models.ResourceGroup.Update(ctx, id.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, err
}

// List 资源组列表查询方法
func (r ResourceGroup) ListCloud(ctx context.Context, req *cloudman.ListCloudReq) (*cloudman.ListCloudResp, error) {
	client, err := alicloud.NewAliResourceClient(req.RegionId, req.IspId)
	if err != nil {
		return nil, err
	}

	groups, err := client.ListResourceGroups(ctx)
	if err != nil {
		return nil, err
	}

	resList := make([]*cloudman.ListCloudData, 0)
	for _, r := range groups {
		resList = append(resList, &cloudman.ListCloudData{
			DisplayName: tea.StringValue(r.DisplayName),
			Id:          tea.StringValue(r.Id),
			Name:        tea.StringValue(r.Name),
		})
	}

	return &cloudman.ListCloudResp{
		List:  resList,
		Total: int32(len(groups)),
	}, nil
}
