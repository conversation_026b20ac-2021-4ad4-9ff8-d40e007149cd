package service

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// TaskLog 任务日志
type TaskLog struct {
}

// RetrieveTaskLog 获取日志详情
func (t TaskLog) RetrieveTaskLog(ctx context.Context, req *cloudman.OrderLogReq) (*cloudman.TaskLogResultList, error) {
	res, _, err := models.TaskLogModel.GetLogResult(ctx, &schema.TaskLogResultQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Start,
			Size: 5000,
		},
		TaskLogResultColumnParam: schema.TaskLogResultColumnParam{
			TaskLogID: req.Id,
		},
	})

	if err != nil {
		return nil, err
	}

	var list []*cloudman.TaskLogResult
	for _, v := range res {
		list = append(list, &cloudman.TaskLogResult{
			CreatedTime: v.CreatedTime,
			TaskLogId:   v.TaskLogID,
			Stopped:     v.Stopped,
			TaskResult:  v.TaskResult,
		})
	}

	return &cloudman.TaskLogResultList{List: list}, nil
}

// ListTaskLog 任务日志列表
func (t TaskLog) ListTaskLog(ctx context.Context, params *cloudman.TaskLogQueryParams) (*cloudman.TaskLogListResponse, error) {
	list, total, err := models.TaskLogModel.Query(ctx, &schema.TaskLogQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: params.Page,
			Size: params.Size,
		},
		OrderParams: schema.OrderParams{
			Ordering: params.Ordering,
		},
		TaskLogColumnParam: schema.TaskLogColumnParam{
			Name: params.Name,
		},
		TaskLogSearchParam: schema.TaskLogSearchParam{
			Keywords: params.Keywords,
		},
	})

	if err != nil {
		return nil, err
	}

	var ids []string
	for _, v := range list {
		if v.AccountID != constant.AllAccountID {
			ids = append(ids, v.AccountID)
		}
	}

	m, err := models.AccountModel.FindManyWithPkToMap(ctx, ids)
	if err != nil {
		return nil, err
	}
	if m == nil {
		m = map[string]entity.Account{}
	}
	m[constant.AllAccountID] = entity.Account{AType: "custom", Name: constant.AllAccountName}

	var l []*cloudman.TaskLogDetail
	for _, v := range list {
		l = append(l, &cloudman.TaskLogDetail{
			Id:             v.ID.Hex(),
			Name:           v.Name,
			SyncTaskId:     v.SyncTaskID,
			AccountId:      v.AccountID,
			AccountType:    m[v.AccountID].AType,
			AccountName:    m[v.AccountID].Name,
			BindRegionId:   v.BindRegionID,
			BindRegionName: v.BindRegionName,
			TaskType:       v.TaskType,
			Status:         uint32(v.Status),
			EndTime:        v.EndTime,
			CreatedTime:    v.CreatedTime,
			CreateUser:     v.CreateUser,
		})
	}

	return &cloudman.TaskLogListResponse{
		List:  l,
		Total: uint32(total),
	}, nil
}
