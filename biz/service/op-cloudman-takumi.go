package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	httpclient "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/httpClient"
)

// Handler rpc api handler implementation.
type Service struct{}

var maxNumForRequest = 200

// InitStatus handler
func (Service) InitStatus(ctx context.Context) (*cloudman.Result, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return &cloudman.Result{Message: "该操作需要管理员权限"}, errors.New("该操作需要管理员权限")
	}

	if err := RefreshMonitorStatus(ctx); err != nil {
		logger.Errorf(err.Error())
	}
	if err := RefreshAgentStatus(ctx); err != nil {
		logger.Errorf(err.Error())
	}
	return &cloudman.Result{Message: "success"}, nil
}

// RefreshMonitorStatus 全量刷新监控状态
func RefreshMonitorStatus(ctx context.Context) error {
	gaiaCfg, err := cfg.GetGaiaMonitorConf()
	if err != nil {
		return err
	}

	// 获取全部主机信息
	hostResourses, err := models.HostResourceModel.FindMany(ctx, map[string]interface{}{})
	if err != nil {
		return err
	}

	tl := &entity.TaskLog{
		Name:        constant.SyncMonitorAgetnStatusName,
		AccountID:   constant.AllAccountID,
		AccountName: constant.AllAccountName,
		Status:      uint(constant.TaskRunning),
	}

	tl.CreateUser = permission.GetUsername(ctx)

	oid, err := models.TaskLogModel.Create(ctx, tl)
	if err != nil {
		logger.Errorf("创建任务记录失败: error:", err.Error())
		return err
	}

	hook := LogResultHook{
		TaskLogResultModel: func(meta *entity.TaskLogResult) error {
			return models.TaskLogResultModel.Create(context.Background(), meta)
		},
		TaskLogID: oid,
	}
	logging := newResultLogger(hook)
	logging.Infoln("初始化监控刷新任务中........")

	thisAccountHosts := hostResourses
	go func() {
		status := constant.TaskSuccess
		gCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
		var goErr error
		defer func() {
			overResult := "任务执行成功"
			if goErr != nil {
				logging.Error(goErr.Error())
				status = constant.TaskErr
				overResult = "任务执行失败"
			}
			logging.Infoln(overResult)

			_ = models.TaskLogResultModel.Create(gCtx, &entity.TaskLogResult{
				TaskLogID:  oid,
				Stopped:    true,
				TaskResult: overResult,
			})

			_ = models.TaskLogModel.Update(gCtx, oid, map[string]interface{}{
				"status":   status,
				"end_time": time.Now().Unix(),
			})
		}()

		// 同步任务
		for i := 0; i < len(thisAccountHosts)/maxNumForRequest+1; i++ {
			var sendHosts []*entity.HostResource
			if (i+1)*maxNumForRequest >= len(thisAccountHosts) {
				sendHosts = thisAccountHosts[i*maxNumForRequest:]
			} else {
				sendHosts = thisAccountHosts[i*maxNumForRequest : (i+1)*maxNumForRequest]
			}

			endPoints := make([]string, 0)
			for _, host := range sendHosts {
				endPoints = append(endPoints, host.InnerIPAddress...)
			}

			// 向监控平台发送请求
			monitorStatusReq := struct {
				EndPoints []string
			}{endPoints}
			reqRaw, _ := json.Marshal(monitorStatusReq)
			client := httpclient.NewHttpclient()
			header := http.Header{}
			token, err := cfg.GetIAMCliToken(gaiaCfg.IAMEnv)
			if err != nil {
				logger.Errorf(err.Error())
				return
			}
			if cfg.GetGaiaIamToken() != "" {
				token = cfg.GetGaiaIamToken()
			}
			header.Set("Authorization", "Bearer "+token)
			respRaw, goErr := client.POSTCtx(gCtx, gaiaCfg.MonitorAddress, header, reqRaw) // ctx没有给定超时时间，可能导致线程无法回收
			resStr := string(respRaw)
			_ = resStr
			if goErr != nil {
				return
			}

			type resp struct {
				Ident  string `json:"ident"`
				Name   string `json:"name"`
				Status int    `json:"status"`
			}
			monitorStatusResp := struct {
				Dat map[string]resp `json:"dat"`
			}{}
			monitorStatusResp.Dat = make(map[string]resp)
			goErr = json.Unmarshal(respRaw, &monitorStatusResp)
			if goErr != nil {
				return
			}

			// 更新数据库Monitor状态 （将status为0和1的分别进行合并）
			runningIPs := make([]string, 0)
			lostIPs := make([]string, 0)
			noStatusIPs := make([]string, 0)
			hasIPs := map[string]bool{}
			for k, v := range monitorStatusResp.Dat {
				hasIPs[k] = true
				if v.Status == 1 {
					runningIPs = append(runningIPs, k)
				} else {
					lostIPs = append(lostIPs, k)
				}
			}
			for _, ip := range endPoints {
				_, ok := hasIPs[ip]
				if !ok {
					noStatusIPs = append(noStatusIPs, ip)
				}
			}

			if len(runningIPs) != 0 {
				goErr = models.HostResourceModel.UpdateMonitorAgentByIPs(gCtx, runningIPs, 1)
				if goErr != nil {
					return
				}
			}

			if len(lostIPs) != 0 {
				goErr = models.HostResourceModel.UpdateMonitorAgentByIPs(gCtx, lostIPs, 0)
				if goErr != nil {
					return
				}
			}

			if len(noStatusIPs) != 0 {
				goErr = models.HostResourceModel.UpdateMonitorAgentByIPs(gCtx, noStatusIPs, -1)
				if goErr != nil {
					return
				}
			}
		}
	}()
	return nil
}

// RefreshAgentStatus 全量刷新Agent状态
func RefreshAgentStatus(ctx context.Context) error {
	gatewayCfg, ok := cfg.GetOpsGatewayConf()
	if !ok {
		return errors.New("failed to get getGatewayConf")
	}

	// Create Task Log
	tl := &entity.TaskLog{
		Name:        constant.SyncAgentStatusName,
		AccountID:   constant.AllAccountID,
		AccountName: constant.AllAccountName,
		Status:      uint(constant.TaskRunning),
	}
	tl.CreateUser = permission.GetUsername(ctx)
	oid, err := models.TaskLogModel.Create(ctx, tl)
	if err != nil {
		logger.Errorf("创建任务记录失败: error:", err.Error())
		return err
	}
	hook := LogResultHook{
		TaskLogResultModel: func(meta *entity.TaskLogResult) error {
			return models.TaskLogResultModel.Create(context.Background(), meta)
		},
		TaskLogID: oid,
	}
	logging := newResultLogger(hook)
	logging.Infoln("初始化agent状态刷新任务中........")

	// Execute Sync
	go func() {
		var goErr error
		status := constant.TaskSuccess
		adminCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
		defer func() {
			overResult := "任务执行成功"
			if goErr != nil {
				logging.Error(goErr.Error())
				status = constant.TaskErr
				overResult = "任务执行失败"
			}
			logging.Infoln(overResult)

			_ = models.TaskLogResultModel.Create(adminCtx, &entity.TaskLogResult{
				TaskLogID:  oid,
				Stopped:    true,
				TaskResult: overResult,
			})

			_ = models.TaskLogModel.Update(adminCtx, oid, map[string]interface{}{
				"status":   status,
				"end_time": time.Now().Unix(),
			})
		}()

		// 开始获取agent list
		logging.Infoln("开始获取agent list........")
		//token := ""
		cli := http.Client{Timeout: time.Second * 15}
		req, goErr := http.NewRequest("GET", gatewayCfg.AgentListURL, strings.NewReader(""))
		if goErr != nil {
			return
		}
		//req.Header.Add("Authorization", token)
		req.Header.Add("Content-Type", "application/json")
		resp, goErr := cli.Do(req)
		if goErr != nil {
			return
		}
		defer resp.Body.Close()
		if resp.StatusCode >= 400 {
			goErr = fmt.Errorf("bad agent resp status code %d", resp.StatusCode)
			return
		}
		respSchema := &struct {
			Code int    `json:"code"`
			Msg  string `json:"msg"`
			Data struct {
				Count int      `json:"count"`
				Info  []string `json:"info"`
			} `json:"data"`
		}{}
		respData, _ := ioutil.ReadAll(resp.Body)
		goErr = json.Unmarshal(respData, respSchema)
		if goErr != nil {
			return
		}
		if respSchema.Code != 2000 {
			goErr = fmt.Errorf("bad list agents status %d, errMsg: %s", respSchema.Code, respSchema.Msg)
			return
		}

		// update agent status
		for _, agentID := range respSchema.Data.Info {
			host, goErr := models.HostResourceModel.FindOne(adminCtx, map[string]interface{}{"agent_id": agentID, "is_delete": 0})
			if goErr != nil && goErr != mongo.ErrNoDocuments {
				return
			}
			if goErr != mongo.ErrNoDocuments && host.AgentStatus == 0 {
				host.AgentStatus = 1
				if goErr := models.HostResourceModel.UpdateOne(adminCtx, map[string]interface{}{"agent_id": agentID, "is_delete": 0}, host); goErr != nil {
					return
				}
			}
			agent, goErr := models.OpsAgentModel.FindOne(adminCtx, map[string]interface{}{"agent_id": agentID, "is_delete": 0})
			if goErr != nil && goErr != mongo.ErrNoDocuments {
				return
			}
			if goErr != mongo.ErrNoDocuments && agent.Status == 2 {
				agent.Status = 1
				if goErr := models.OpsAgentModel.Update(adminCtx, agentID, agent); goErr != nil {
					return
				}
			}
		}
	}()
	return nil
}
