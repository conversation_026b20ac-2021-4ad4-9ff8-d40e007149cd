package service

import (
	"context"

	"github.com/alibabacloud-go/tea/tea"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
)

type Ddos struct {
}

func (h Ddos) DescribeDdosBgp(ctx context.Context, req *cloudman.DescribeDdosBgpReq) (*cloudman.DescribeDdosBgpRes, error) {
	client, err := alicloud.CreateAliDdosBGPClient(req.AccountId)
	if err != nil {
		return nil, err
	}

	packageList, err := client.GetAliyunBGPPackageList(ctx)
	if err != nil {
		return nil, err
	}
	ddosBgpRes := make([]*cloudman.DdosBgp, 0)
	for _, p := range packageList {
		spec, err := client.GetAliyunBGPPackageSpec(ctx, tea.StringValue(p.InstanceId))
		if err != nil {
			return nil, err
		}
		ddosBgpRes = append(ddosBgpRes, &cloudman.DdosBgp{
			InstanceId: tea.StringValue(p.InstanceId),
			TotalCount: tea.Int32Value(spec.PackConfig.IpSpec),
			UsedCount:  tea.Int32Value(spec.PackConfig.BindIpCount),
		})
	}

	return &cloudman.DescribeDdosBgpRes{
		Total: int32(len(packageList)),
		List:  ddosBgpRes,
	}, nil
}
