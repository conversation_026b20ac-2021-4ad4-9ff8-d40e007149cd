package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"

	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/hashicorp/go-uuid"
	"github.com/mitchellh/mapstructure"
	"github.com/tealeg/xlsx"
	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	httpclient "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/httpClient"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/monitor"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/querybuilder"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/aws"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// HostEntityToPb 主机model转pb
func HostEntityToPb(v entity.HostResource) *cloudman.HostResDetail {
	var tags []*cloudman.ResourceTag
	for _, tag := range v.Tags.Tag {
		tags = append(tags, &cloudman.ResourceTag{Key: tag.TagKey, Value: tag.TagValue})
	}

	var sgInfos []*cloudman.SecurityGroupInfo
	if len(v.SecurityGroupIds.SecurityGroupID) != 0 {
		sgMap, err := models.SecurityGroupModel.FindManyWithIDToMap(context.Background(), v.SecurityGroupIds.SecurityGroupID)
		if err == nil {
			for _, sgID := range v.SecurityGroupIds.SecurityGroupID {
				sg := sgMap[sgID]
				sgInfos = append(sgInfos, &cloudman.SecurityGroupInfo{
					SecurityGroupId:   sgID,
					SecurityGroupName: sg.SecurityGroupName,
					Description:       sg.Description,
					VpcId:             sg.VpcID,
				})
			}
		}
	}

	detail := &cloudman.HostResDetail{
		Id:           v.ID.Hex(),
		InstanceId:   v.InstanceID,
		InstanceType: v.InstanceType,
		HostName:     v.HostName,
		Status:       v.Status,
		Cpu:          v.CPU,
		Memory:       v.Memory,
		CreationTime: int32(v.CreationTime),
		// NetworkInterface: utils.SafeToString(tmp["NetworkInterfaces"]),
		Islock:         v.IsLock,
		Isp:            v.IspID,
		RegionId:       v.RegionID,
		PublicAddress:  v.PublicIPAddress,
		PrivateAddress: v.InnerIPAddress,
		Tags:           tags,
		OsName:         v.OSName,
		InstanceName:   v.InstanceName,
		EipAddress:     v.EipAddress.IPAddress,
		ExpiredTime:    int32(v.ExpiredTime),
		Description:    v.Description,
		ImageId:        v.ImageID,
		ZoneId:         v.ZoneID,
		VpcAttr: &cloudman.InstanceVpcAttribute{
			VpcId:        v.VpcAttributes.VpcID,
			NatIpAddress: v.VpcAttributes.NatIPAddress,
			VSwitchId:    v.VpcAttributes.VSwitchID,
		},
		SecurityGroupInfos: sgInfos,
		OpsAgentId:         v.AgentID,
		OpsAgentEnv:        v.AgentEnv,
		MonitorStatus:      v.MonitorStatusToString(),
		NeedCleanup:        v.NeedCleanup,
	}
	if len(v.PublicIPAddress) == 0 {
		detail.PublicAddress = []string{v.EipAddress.IPAddress}
	}

	return detail
}

// HostRes 主机对象
type HostRes struct {
}

// Dashboard 主机资源概览
func (h HostRes) Dashboard(ctx context.Context, req *cloudman.HostDashBoardReq) (*cloudman.HostDashBoardResp, error) {
	agentRunning, agentStopped, err := models.HostResourceModel.AgentStatusTotal(ctx, req)
	if err != nil {
		return nil, err
	}
	mAgentRunning, mAgentStopped, err := models.HostResourceModel.MonitorAgentStatusTotal(ctx, req)
	if err != nil {
		return nil, err
	}
	running, unRunning, err := models.HostResourceModel.HostStatusTotal(ctx, req)
	if err != nil {
		return nil, err
	}
	expireLast7, autoRelease, needCleanup, err := models.HostResourceModel.ResStatusTotal(ctx, 7*24*time.Hour, req)
	if err != nil {
		return nil, err
	}
	return &cloudman.HostDashBoardResp{
		HostStatus: &cloudman.HostStatus{
			Running:   int32(running),
			UnRunning: int32(unRunning),
		},
		ResStatus: &cloudman.ResourceStatus{
			Expire:      int32(expireLast7),
			Release:     int32(autoRelease),
			NeedCleanup: int32(needCleanup),
		},
		OpsAgentStatus: &cloudman.AgentStatus{
			Running: int32(agentRunning),
			Pending: 0,
			Stopped: int32(agentStopped),
		},
		MonitorAgentStatus: &cloudman.AgentStatus{
			Running: int32(mAgentRunning),
			Pending: 0,
			Stopped: int32(mAgentStopped),
		},
	}, nil
}

// DescribeAutoSnapshotPolicyEX 获取磁盘自动快照规则
func (h HostRes) DescribeAutoSnapshotPolicyEX(ctx context.Context, req *cloudman.AutoSnapshotPolicyEXReq) (*cloudman.AutoSnapshotPolicyEXResp, error) {
	client, err := getCloudHost(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeAutoSnapshotPolicyEx(ctx, &common.PageCommon{Page: req.Page, Size: req.Size})
}

// HostResEntity 主机handler实例
var HostResEntity = new(HostRes)

// Query 自定义查询方法
func (h HostRes) Query(ctx context.Context, query *cloudman.CustomQuery) (*cloudman.HostResResp, error) {
	var rules []map[string]interface{}
	for _, v := range query.Rules {
		value := map[string]interface{}{
			"operator": v.Operator.String(),
			"field":    v.Field,
			"value":    v.Value,
		}
		if len(v.Values) != 0 {
			value["value"] = v.Values
		}
		rules = append(rules, value)
	}

	filter := map[string]interface{}{
		"condition": query.GetCondition().String(),
		"rules":     rules,
	}

	ql, errKey, err := querybuilder.ParseRule(filter)
	if err != nil {
		return nil, err
	}
	if errKey != "" {
		return nil, fmt.Errorf("found error: %s", errKey)
	}

	filterStr, errKey, err := ql.ToMgo()
	if err != nil {
		return nil, err
	}
	if errKey != "" {
		return nil, fmt.Errorf("found error: %s", errKey)
	}
	logger.Infof("filterStr: %s", filterStr)
	return nil, nil
}

// BatchModifyForCustom 批量修改资源信息
func (h HostRes) BatchModifyForCustom(ctx context.Context, req *cloudman.BatchModifyReq) (success []string, failed []string) {
	for _, v := range req.Instances {
		// req.ChangeAttrs
		// req.Tags
		// req.Description
		// req.TagRule
		// req.AllowChangeData
		hostResource, err := models.HostResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": v})
		if err != nil {
			failed = append(failed, fmt.Sprintf("InstanceID: %s, error: %s", v, err.Error()))
			continue
		}
		if !models.CheckIspAndTagsPermFromCtx(ctx, "host", "admin", hostResource.IspID, models.EntityToCheckTags(hostResource.Tags.Tag)) {
			failed = append(failed, fmt.Sprintf("InstanceID: %s, error: %s", v, ""))
			continue
		}

		updateData := hostResource
		updateData.Tags.Tag = []entity.Tag{}
		for _, tag := range req.Tags {
			updateData.Tags.Tag = append(updateData.Tags.Tag, entity.Tag{
				TagValue: tag.Value,
				TagKey:   tag.Key,
			})
		}

		err = models.HostResourceModel.BatchUpdateAttr(ctx, updateData, req.Tags, req.TagRule) // tag需单独更新
		if err != nil {
			failed = append(failed, fmt.Sprintf("InstanceID: %s, error: %s", v, err.Error()))
		} else {
			success = append(success, v)
		}
	}

	return
}

// GetGaiaView 获取gaia-UI访问地址
func (h HostRes) GetGaiaView(ctx context.Context, req *cloudman.GaiaViewReq) (*cloudman.GraphView, error) {
	hCfg, err := cfg.GetGaiaMonitorConf()
	if err != nil {
		return nil, err
	}

	prefix := strings.TrimSuffix(hCfg.Redirect, "/")
	return &cloudman.GraphView{Address: fmt.Sprintf("%s/%s", prefix, req.Endpoint)}, nil
}

// const GaiaMonitUrl = "/api/transfer/data/ui"

// GetGaiaMonitor 获取gaia监控信息
func (h HostRes) GetGaiaMonitor(ctx context.Context, req *cloudman.GaiaMonitReq) (*cloudman.GaiaMonitResp, error) {
	hCfg, err := cfg.GetGaiaMonitorConf()
	if err != nil {
		return nil, err
	}

	reqRaw, _ := json.Marshal(req)
	client := httpclient.NewHttpclient()
	token, err := cfg.GetIAMCliToken(hCfg.IAMEnv)
	if err != nil {
		return nil, err
	}
	if hCfg.IAMToken != "" {
		token = hCfg.IAMToken
	}
	client.SetHeader("Authorization", "Bearer "+token)
	respRaw, err := client.POSTCtx(ctx, hCfg.Address, nil, reqRaw)
	if err != nil {
		return nil, err
	}

	var resp cloudman.GaiaMonitResp
	err = json.Unmarshal(respRaw, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

// GetUPTimeMonitor 获取开机时间
func (h HostRes) GetUPTimeMonitor(ctx context.Context, req *cloudman.MonitorReq) (*cloudman.MonitorResp, error) {
	monitConf, monit, err := h.getMonitor(req)
	if err != nil {
		return nil, err
	}

	var sql string
	switch req.OsType {
	case "windows":
		sql = monitConf.UPTimeSQLTplWin
		break
	default:
		sql = monitConf.UPTimeSQLTpl
	}

	resp, err := monit.GetUPTimeValue(ctx, sql, req.InstanceName)
	if err != nil {
		return nil, err
	}

	var results []*cloudman.MonitorResult
	for _, v := range resp.Result {
		var series []*cloudman.MonitorSeries
		for _, serie := range v.Series {
			var value []*cloudman.MonitorValue
			for _, monitValue := range serie.Values {
				value = append(value, &cloudman.MonitorValue{Value: monitValue})
			}
			series = append(series, &cloudman.MonitorSeries{
				Name:    serie.Name,
				Columns: serie.Columns,
				Values:  value,
			})
		}

		results = append(results, &cloudman.MonitorResult{Series: series})
	}

	return &cloudman.MonitorResp{Results: results}, nil
}

// GetDiskMonitor 获取磁盘监控
func (h HostRes) GetDiskMonitor(ctx context.Context, req *cloudman.MonitorReq) (*cloudman.MonitorResp, error) {
	monitConf, monit, err := h.getMonitor(req)
	if err != nil {
		return nil, err
	}

	var sql string
	switch req.OsType {
	case "windows":
		sql = monitConf.DiskTplForWin
		break
	default:
		sql = monitConf.DiskTpl
	}

	resp, err := monit.GetDiskValue(ctx, sql, req.InstanceName)
	if err != nil {
		return nil, err
	}

	var results []*cloudman.MonitorResult
	for _, v := range resp.Result {
		var series []*cloudman.MonitorSeries
		for _, serie := range v.Series {
			var value []*cloudman.MonitorValue
			for _, monitValue := range serie.Values {
				value = append(value, &cloudman.MonitorValue{Value: monitValue})
			}
			series = append(series, &cloudman.MonitorSeries{
				Name:    serie.Name,
				Columns: serie.Columns,
				Values:  value,
				Tags: &cloudman.MonitorTags{
					Host:     serie.Tags.Host,
					Path:     serie.Tags.Path,
					Instance: serie.Tags.Instance,
				},
			})
		}

		results = append(results, &cloudman.MonitorResult{Series: series})
	}

	return &cloudman.MonitorResp{Results: results}, nil
}

func pbHostToEntity(req *cloudman.HostResDetail) *entity.HostResource {
	var tags []entity.Tag
	for _, v := range req.Tags {
		if v.Key == "" {
			continue
		}
		tags = append(tags, entity.Tag{
			TagValue: v.Value,
			TagKey:   v.Key,
		})
	}

	monitorStatus := -1
	if req.MonitorStatus == "Running" {
		monitorStatus = 1
	} else if req.MonitorStatus == "Lost" {
		monitorStatus = 0
	}

	var sgIDs []string
	for _, sgInfo := range req.SecurityGroupInfos {
		sgIDs = append(sgIDs, sgInfo.SecurityGroupId)
	}
	return &entity.HostResource{
		AllowChangeData: req.AllowChangeData,
		IsLock:          req.Islock,
		Tags: entity.InstanceTags{
			Tag: tags,
		},
		PublicIPAddress:         req.PublicAddress,
		InstanceName:            req.InstanceName,
		OSName:                  req.OsName,
		ImageID:                 req.ImageId,
		GPUSpec:                 req.GPUSpec,
		InnerIPAddress:          req.PrivateAddress,
		HostName:                req.HostName,
		InstanceChargeType:      req.InstanceChargeType,
		ZoneID:                  req.ZoneId,
		OSType:                  req.OSType,
		Description:             req.Description,
		Memory:                  req.Memory,
		InternetMaxBandwidthIn:  req.InternetMaxBandwidthIn,
		InternetMaxBandwidthOut: req.InternetMaxBandwidthOut,
		SecurityGroupIds: entity.InstanceSecurityGroupIds{
			SecurityGroupID: sgIDs,
		},
		RegionID:    req.RegionId,
		CPU:         req.Cpu,
		HostMonitor: entity.HostMonitor{MonitorStatus: int32(monitorStatus)},
	}
}

func manyEntityToPbHost(ctx context.Context, data []*entity.HostResource) (resp []*cloudman.HostResDetail, err error) {
	var ids []string
	for _, v := range data {
		isp := v.IspID
		if isp != "" {
			ids = append(ids, isp)
		}
	}

	var ac map[string]entity.Account
	if len(ids) != 0 {
		ac, err = models.AccountModel.FindManyWithPkToMap(ctx, ids)
		if err != nil {
			return nil, err
		}
	}

	for _, v := range data {
		var tags []*cloudman.ResourceTag
		for _, tag := range v.Tags.Tag {
			tags = append(tags, &cloudman.ResourceTag{Key: tag.TagKey, Value: tag.TagValue})
		}

		data := &cloudman.HostResDetail{
			Id:           v.ID.Hex(),
			InstanceId:   v.InstanceID,
			InstanceType: v.InstanceType,
			HostName:     v.HostName,
			Status:       v.Status,
			Cpu:          v.CPU,
			Memory:       v.Memory,
			CreationTime: int32(v.CreationTime),
			// NetworkInterface: utils.SafeToString(tmp["NetworkInterfaces"]),
			Islock:         v.IsLock,
			Isp:            v.IspID,
			RegionId:       v.RegionID,
			IspType:        ac[v.IspID].AType,
			IspName:        ac[v.IspID].Name,
			PublicAddress:  v.PublicIPAddress,
			PrivateAddress: v.InnerIPAddress,
			Tags:           tags,
			OsName:         v.OSName,
			InstanceName:   v.InstanceName,
			EipAddress:     v.EipAddress.IPAddress,
			ExpiredTime:    int32(v.ExpiredTime),
			Description:    v.Description,
			OpsAgentId:     v.AgentID,
			OpsAgentStatus: entity.TranslateStatus(v.AgentStatus, v.AgentUpdate),
			OpsAgentEnv:    v.AgentEnv,
			MonitorStatus:  v.MonitorStatusToString(),
			NeedCleanup:    v.NeedCleanup,
		}

		resp = append(resp, data)
	}

	return resp, nil
}

// GetGraphView 获取graph地址
func (h HostRes) GetGraphView(ctx context.Context, req *cloudman.MonitorReq) (*cloudman.GraphView, error) {
	if req.InstanceName == "" {
		return nil, fmt.Errorf("hostname is null")
	}
	monitConf, err := cfg.GetMonitorConf()
	if err != nil {
		return nil, err
	}

	viewAddress := monitConf.GrafanaViewAddress
	if req.OsType == "windows" {
		viewAddress = monitConf.GrafanaViewAddressForWin
	}

	env := GetHostEnv(req.InstanceName)

	return &cloudman.GraphView{Address: viewAddress + "?var-server=" + req.InstanceName + "&var-env=" + env}, nil
}

// GetCPUMonitor 获取cpu监控信息
func (h HostRes) GetCPUMonitor(ctx context.Context, req *cloudman.MonitorReq) (*cloudman.MonitorResp, error) {
	monitConf, monit, err := h.getMonitor(req)
	if err != nil {
		return nil, err
	}

	var sql string
	switch req.OsType {
	case "windows":
		sql = monitConf.CPUSqlTplForWin
		break
	default:
		sql = monitConf.CPUSqlTpl
	}

	resp, err := monit.GetCPUValue(ctx, sql, req.InstanceName)
	if err != nil {
		return nil, err
	}

	var results []*cloudman.MonitorResult
	for _, v := range resp.Result {
		var series []*cloudman.MonitorSeries
		for _, serie := range v.Series {
			var value []*cloudman.MonitorValue
			for _, monitValue := range serie.Values {
				value = append(value, &cloudman.MonitorValue{Value: monitValue})
			}
			series = append(series, &cloudman.MonitorSeries{
				Name:    serie.Name,
				Columns: serie.Columns,
				Values:  value,
			})
		}

		results = append(results, &cloudman.MonitorResult{Series: series})
	}

	return &cloudman.MonitorResp{Results: results}, nil
}

// GetMemMonitor 获取内存监控信息
func (h HostRes) GetMemMonitor(ctx context.Context, req *cloudman.MonitorReq) (*cloudman.MonitorResp, error) {
	monitConf, monit, err := h.getMonitor(req)
	if err != nil {
		return nil, err
	}

	var sql string
	switch req.OsType {
	case "windows":
		sql = monitConf.MemSQLTplForWin
		break
	default:
		sql = monitConf.MemSQLTpl
	}

	resp, err := monit.GetMemValue(ctx, sql, req.InstanceName)
	if err != nil {
		return nil, err
	}

	var results []*cloudman.MonitorResult
	for _, v := range resp.Result {
		var series []*cloudman.MonitorSeries
		for _, serie := range v.Series {
			var value []*cloudman.MonitorValue
			for _, monitValue := range serie.Values {
				value = append(value, &cloudman.MonitorValue{Value: monitValue})
			}
			series = append(series, &cloudman.MonitorSeries{
				Name:    serie.Name,
				Columns: serie.Columns,
				Values:  value,
			})
		}

		results = append(results, &cloudman.MonitorResult{Series: series})
	}

	return &cloudman.MonitorResp{Results: results}, nil
}

// GetIOMonitor 获取io监控信息
func (h HostRes) GetIOMonitor(ctx context.Context, req *cloudman.MonitorReq) (*cloudman.MonitorResp, error) {
	monitConf, monit, err := h.getMonitor(req)
	if err != nil {
		return nil, err
	}

	var sql string
	switch req.OsType {
	case "windows":
		sql = monitConf.IOSqlTplForWin
		break
	default:
		sql = monitConf.IOSqlTpl
	}
	resp, err := monit.GetIOValue(ctx, sql, req.InstanceName)
	if err != nil {
		return nil, err
	}

	var results []*cloudman.MonitorResult
	for _, v := range resp.Result {
		var series []*cloudman.MonitorSeries
		for _, serie := range v.Series {
			var value []*cloudman.MonitorValue
			for _, monitValue := range serie.Values {
				value = append(value, &cloudman.MonitorValue{Value: monitValue})
			}
			series = append(series, &cloudman.MonitorSeries{
				Name:    serie.Name,
				Columns: serie.Columns,
				Values:  value,
			})
		}

		results = append(results, &cloudman.MonitorResult{Series: series})
	}

	return &cloudman.MonitorResp{Results: results}, nil
}

// GetNetMonitor 获取网络监控信息
func (h HostRes) GetNetMonitor(ctx context.Context, req *cloudman.MonitorReq) (*cloudman.MonitorResp, error) {
	monitConf, monit, err := h.getMonitor(req)
	if err != nil {
		return nil, err
	}

	var sql string
	switch req.OsType {
	case "windows":
		sql = monitConf.NetSQLTplForWin
		break
	default:
		sql = monitConf.NetSQLTpl
	}
	resp, err := monit.GetNetValue(ctx, sql, req.InstanceName)
	if err != nil {
		return nil, err
	}

	var results []*cloudman.MonitorResult
	for _, v := range resp.Result {
		var series []*cloudman.MonitorSeries
		for _, serie := range v.Series {
			var value []*cloudman.MonitorValue
			for _, monitValue := range serie.Values {
				value = append(value, &cloudman.MonitorValue{Value: monitValue})
			}
			series = append(series, &cloudman.MonitorSeries{
				Name:    serie.Name,
				Columns: serie.Columns,
				Values:  value,
			})
		}

		results = append(results, &cloudman.MonitorResult{Series: series})
	}

	return &cloudman.MonitorResp{Results: results}, nil
}

func (h HostRes) getMonitor(req *cloudman.MonitorReq) (*cfg.MonitorConfig, *monitor.MonitConfig, error) {
	if req.InstanceName == "" {
		return nil, nil, fmt.Errorf("instanceName is null")
	}
	monitConf, err := cfg.GetMonitorConf()
	if err != nil {
		return nil, nil, err
	}

	monit := &monitor.MonitConfig{
		Token:        monitConf.Token,
		QueryAddress: monitConf.Address,
		DBIndex:      monitConf.DBIndex,
	}

	if GetHostEnv(req.InstanceName) == "prod" {
		monit.DBIndex = monitConf.DBIndexProd
	}

	return monitConf, monit, nil
}

func genInstanceID() (uid string, err error) {
	buf, err := uuid.GenerateRandomBytes(8)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", buf), nil
}

// InputResource 手工录入数据
func (h HostRes) InputResource(ctx context.Context, detail *cloudman.HostResDetail) (*cloudman.Result, error) {
	if !models.CheckIspAndTagsPermFromCtx(ctx, "host", "admin", detail.IspId, models.PbToCheckTags(detail.Tags)) {
		return nil, errors.New("没有插入此资源的权限")
	}

	// 查询isp是否为自定义
	account, err := models.AccountModel.FindPK(ctx, detail.Isp)
	if err != nil {
		return nil, err
	}

	if account.AType != "custom" {
		return nil, errors.New("当前账户为非自定义类型")
	}

	uid, err := genInstanceID()
	if err != nil {
		return nil, err
	}
	instanceID := "m-" + uid
	creatTime := time.Now()

	data := pbHostToEntity(detail)
	agentID, genErr := agent.GenerateID(detail.RegionId, fmt.Sprintf("%s:%d", detail.PrivateAddress[0], account.IndexID))
	if genErr == nil {
		data.AgentID = agentID
	}

	{
		data.RegionID = "cn-shanghai"
		data.IspID = account.ID.Hex()
		data.IspType = account.AType
		data.CreationTime = creatTime.Unix()
		data.InstanceID = instanceID
		data.Status = constant.ResourceRunning
		data.ISP = account.Name
		data.CreatedTime = creatTime.Unix()
	}

	_, err = models.HostResourceModel.CreateOrUpdate(ctx, instanceID, data, detail.Tags, models.HostUpdateOption{
		UpdateDesc: true,
		UpdateTags: true,
		FromSync:   false,
	})
	if err != nil {
		return nil, err
	}

	hooks.PubCreateHostResourceHandler(ctx, *data)
	// install agent
	if detail.InstallAgent {
		go func() {
			// taskBaseCtx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
			// 	"x-rpc-" + "username": permission.GetUsername(ctx),
			// }))
			taskBaseCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)

			installData := struct {
				Username string `json:"username"`
				Password string `json:"password"`
				Port     string `json:"port"`
			}{Username: detail.Username, Password: detail.Password, Port: detail.Port}
			if err := createOrderAndInstallAgent(taskBaseCtx, []*entity.HostResource{data}, data.RegionID, &cloudman.DashBoardAccountDetail{
				AccountId:   account.ID.Hex(),
				AccountName: account.Name,
				AccountType: account.AType,
			}, installData); err != nil {
				logger.Errorf("ispID(%s) region(%s) install agent failed: %s", account.ID.Hex(), data.RegionID, err.Error())
			}
		}()
	}

	return &cloudman.Result{Message: "success"}, nil
}

// GetHostDiskInfo 获取主机详情
func (h HostRes) GetHostDiskInfo(ctx context.Context, id *cloudman.ObjectID) (*cloudman.HostDiskInfoResp, error) {
	hostInfo, err := models.HostResourceModel.FindOne(ctx, map[string]interface{}{
		"InstanceID": id.Id,
	})
	if err == mongo.ErrNoDocuments {
		return nil, errors.New("没有查看此资源的权限")
	} else if err != nil {
		return nil, err
	}
	if hostInfo.IspType == "aliyun" {
		client, _ := alicloud.CreateAliEcsClient(hostInfo.RegionID, hostInfo.IspID)
		diskResult, err := client.DescribeDisks(ctx, hostInfo.InstanceID)
		if err != nil {
			return nil, err
		}
		disks := []*cloudman.DiskInfo{}
		for _, diskObj := range diskResult.Disks.Disk {
			rawByte, _ := json.Marshal(diskObj)
			disk := &cloudman.DiskInfo{
				DiskId:           tea.StringValue(diskObj.DiskId),
				Status:           tea.StringValue(diskObj.Status),
				DiskName:         tea.StringValue(diskObj.DiskName),
				CreationTime:     tea.StringValue(diskObj.CreationTime),
				Size:             tea.Int32Value(diskObj.Size),
				ImageId:          tea.StringValue(diskObj.ImageId),
				DiskType:         tea.StringValue(diskObj.Type),
				Device:           tea.StringValue(diskObj.Device),
				Category:         tea.StringValue(diskObj.Category),
				PerformanceLevel: tea.StringValue(diskObj.PerformanceLevel),
				IOPSReadWrite:    tea.Int32Value(diskObj.IOPS),
				RawData:          string(rawByte),
			}
			disks = append(disks, disk)
		}
		return &cloudman.HostDiskInfoResp{Disks: disks}, nil
	}
	return &cloudman.HostDiskInfoResp{Disks: nil}, nil
}

// GetHostInfo 获取主机详情
func (h HostRes) GetHostInfo(ctx context.Context, id *cloudman.ObjectID) (*cloudman.HostResDetail, error) {
	hostInfo, err := models.HostResourceModel.FindOne(ctx, map[string]interface{}{
		"InstanceID": id.Id,
	})
	if err == mongo.ErrNoDocuments {
		return nil, errors.New("没有查看此资源的权限")
	} else if err != nil {
		return nil, err
	}

	var tags []*cloudman.ResourceTag
	for _, tag := range hostInfo.Tags.Tag {
		resTags, err := models.ResourceTags.GetByKeyValue(ctx, tag.TagKey, tag.TagValue)
		if err == nil {
			tags = append(tags, &cloudman.ResourceTag{Key: tag.TagKey, Value: tag.TagValue, Type: resTags.Type})
		} else {
			logger.Errorf(err.Error())
		}
	}

	isp, err := models.AccountModel.FindPK(ctx, hostInfo.IspID)
	if err != nil {
		return nil, fmt.Errorf("查询Account（%s）报错：%v", hostInfo.IspID, err)
	}

	var sgInfos []*cloudman.SecurityGroupInfo
	if len(hostInfo.SecurityGroupIds.SecurityGroupID) != 0 {
		sgMap, err := models.SecurityGroupModel.FindManyWithIDToMap(ctx, hostInfo.SecurityGroupIds.SecurityGroupID)
		if err == nil {
			for _, sgID := range hostInfo.SecurityGroupIds.SecurityGroupID {
				sg := sgMap[sgID]
				sgInfos = append(sgInfos, &cloudman.SecurityGroupInfo{
					SecurityGroupId:   sgID,
					SecurityGroupName: sg.SecurityGroupName,
					Description:       sg.Description,
					VpcId:             sg.VpcID,
				})
			}
		}
	}

	data := &cloudman.HostResDetail{
		AllowChangeData:         hostInfo.AllowChangeData,
		InstanceId:              hostInfo.InstanceID,
		InstanceType:            hostInfo.InstanceType,
		Isp:                     hostInfo.ISP,
		Status:                  hostInfo.Status,
		IspId:                   hostInfo.IspID,
		MonitorStatus:           hostInfo.MonitorStatusToString(),
		FilebeatStatus:          "",
		HostName:                hostInfo.HostName,
		Cpu:                     hostInfo.CPU,
		Memory:                  hostInfo.Memory,
		Islock:                  hostInfo.IsLock,
		BkInstName:              hostInfo.InstanceName,
		RegionId:                hostInfo.RegionID,
		IspName:                 isp.Name,
		IspType:                 hostInfo.IspType,
		Id:                      hostInfo.ID.Hex(),
		CreationTime:            int32(hostInfo.CreationTime),
		PublicAddress:           hostInfo.PublicIPAddress,
		PrivateAddress:          hostInfo.InnerIPAddress,
		Tags:                    tags,
		OsName:                  hostInfo.OSName,
		Description:             hostInfo.Description,
		ExpiredTime:             int32(hostInfo.ExpiredTime),
		InstanceChargeType:      hostInfo.InstanceChargeType,
		SecurityGroupInfos:      sgInfos,
		InternetMaxBandwidthIn:  hostInfo.InternetMaxBandwidthIn,
		InternetMaxBandwidthOut: hostInfo.InternetMaxBandwidthOut,
		ZoneId:                  hostInfo.ZoneID,
		VpcAttr: &cloudman.InstanceVpcAttribute{
			VpcId:        hostInfo.VpcAttributes.VpcID,
			NatIpAddress: hostInfo.VpcAttributes.NatIPAddress,
			VSwitchId:    hostInfo.VpcAttributes.VSwitchID,
		},
		ImageId:          hostInfo.ImageID,
		OSType:           hostInfo.OSType,
		InstanceName:     hostInfo.InstanceName,
		GPUSpec:          hostInfo.GPUSpec,
		EipAddress:       hostInfo.EipAddress.IPAddress,
		OpsAgentId:       hostInfo.AgentID,
		OpsAgentEnv:      hostInfo.AgentEnv,
		NeedCleanup:      hostInfo.NeedCleanup,
		NetworkInterface: models.NetworkInterfaceToPb(hostInfo.NetworkInterfaces),
	}

	if len(hostInfo.PublicIPAddress) == 0 {
		data.PublicAddress = []string{hostInfo.EipAddress.IPAddress}
	}

	agent, err := models.OpsAgentModel.FindOne(ctx, map[string]interface{}{
		"agent_id": hostInfo.AgentID,
	})

	if agent != nil && err == nil {
		data.OpsAgentId = agent.AgentID
		data.OpsAgentEnv = agent.Env
		data.OpsAgentStatus = agent.TranslateStatus()
	}

	return data, nil
}

// UnlockResource 解锁资源对象
func (h HostRes) UnlockResource(ctx context.Context, ids *cloudman.Ids) (*cloudman.Result, error) {
	err := models.HostResourceModel.UpdateLockStatus(ctx, ids.Ids, false)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// ImportXlsx 只允许导入自定义厂商对象
func (h HostRes) ImportXlsx(ctx context.Context, req *cloudman.ImportXlsxReq) (*cloudman.BatchResult, error) {
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	isp, err := models.AccountModel.Get(ctx, req.IspId)
	if err != nil {
		return nil, err
	}

	file, err = xlsx.OpenBinary(req.GetFile())
	if err != nil {
		return nil, err
	}

	st := models.HostResourceModel.FiledOptions(ctx)
	hostMetaMap := make(map[string]*entity.FiledStruct)
	for _, v := range st {
		if v.Extra {
			continue
		}
		hostMetaMap[v.Name] = &entity.FiledStruct{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
		}
	}

	sheet = file.Sheets[0]
	// step1 读取标题
	// step2 标题与Value对应
	row = sheet.Row(0)
	var rowList []*entity.FiledStruct
	for _, cell = range row.Cells {
		if k, ok := hostMetaMap[cell.Value]; ok {
			rowList = append(rowList, k)
		} else {
			rowList = append(rowList, nil)
		}
	}

	var hostList []*entity.HostResource
	for i := 1; i < len(sheet.Rows); i++ {
		m, err := models.ExcelToMap(sheet.Rows[i], rowList)
		if err != nil {
			return nil, err
		}
		var hostResource *entity.HostResource
		err = mapstructure.Decode(m, &hostResource)
		if err != nil {
			return nil, err
		}

		hostResource.IspID = isp.ID.Hex() // 前端传入
		hostResource.IspType = "custom"   // 只允许自定义厂商
		hostResource.RegionID = "cn-shanghai"
		hostResource.Status = "Running"
		hostResource.InstanceID = fmt.Sprintf("%s_%s", hostResource.HostName, hostResource.IspID)
		hostResource.InstanceName = hostResource.HostName
		hostResource.AllowChangeData = true // excel导入一律为允许,用户可通过批量修改属性或者变更实例修改该值

		if len(hostResource.InnerIPAddress) > 0 {
			accountIndexID, err := agent.GetAccountIndexID(ctx, hostResource.IspID)
			if err != nil {
				return nil, fmt.Errorf("get account index id error: %v", err)
			}
			agentID, genErr := agent.GenerateID(hostResource.RegionID, fmt.Sprintf("%s:%d", hostResource.InnerIPAddress[0], accountIndexID))
			if genErr == nil {
				hostResource.AgentID = agentID
			}
		}

		hostList = append(hostList, hostResource)
	}

	result, err := models.HostResourceModel.BatchCreateOrUpdate(ctx, hostList, models.HostUpdateOption{
		UpdateDesc: true,
		UpdateTags: true,
		FromSync:   false,
	})
	if err != nil {
		return nil, err
	}

	return &cloudman.BatchResult{
		Errors:         result.Errors,
		Success:        result.Success,
		SuccessCreated: result.SuccessCreated,
		SuccessUpdated: result.SuccessUpdated,
		UpdateErrors:   result.UpdateErrors,
	}, nil
}

// GetImages 获取镜像列表
func (h HostRes) GetImages(ctx context.Context, req *cloudman.ImagesReq) (*cloudman.ImagesResp, error) {
	if !req.Raw {
		localClient, _ := getLocalHostParams(ctx, req.IspId, req.Rid)
		if localClient != nil {
			result, _ := localClient.DescribeImages(ctx, &common.DescribeImagesInput{OsType: req.OsType, ImageName: req.ImageName, IsPublic: req.IsPublic})
			if result != nil {
				return result, nil
			}
		}
	}
	hostSDK, err := getCloudHost(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}

	return hostSDK.DescribeImages(ctx, &common.DescribeImagesInput{OsType: req.OsType, ImageName: req.ImageName, IsPublic: req.IsPublic})
}

// GetInstanceTypes GetInstanceTypes
func (h HostRes) GetInstanceTypes(ctx context.Context, req *cloudman.InstanceTypeReq) (*cloudman.InstanceTypesResp, error) {
	if !req.Raw {
		localClient, _ := getLocalHostParams(ctx, req.IspId, req.Rid)
		if localClient != nil {
			result, _ := localClient.DescribeInstanceTypes(ctx,
				&common.DescribeInstanceTypesInput{ImageID: req.ImagesId, ZoneID: req.ZoneId},
			)
			if result != nil {
				return result, nil
			}
		}
	}

	hostSDK, err := getCloudHost(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}
	return hostSDK.DescribeInstanceTypes(ctx, &common.DescribeInstanceTypesInput{ImageID: req.ImagesId, ZoneID: req.ZoneId})
}

// Option 获取描述信息
func (h HostRes) Option(ctx context.Context, empty *cloudman.Empty) (*cloudman.OptionResp, error) {
	st := models.HostResourceModel.FiledOptions(ctx)
	var li []*cloudman.FieldOption

	for _, v := range st {
		li = append(li, &cloudman.FieldOption{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
			Indexed:  v.Indexed,
			Extra:    v.Extra,
		})
	}

	return &cloudman.OptionResp{List: li}, nil
}

// LockResource 锁定主机资源
func (h HostRes) LockResource(ctx context.Context, ids *cloudman.Ids) (*cloudman.Result, error) {
	err := models.HostResourceModel.UpdateLockStatus(ctx, ids.Ids, true)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// GetVSwitch 获取交换机
func (h HostRes) GetVSwitch(ctx context.Context, req *cloudman.VSwitchReq) (*cloudman.VSwitchResp, error) {
	if !req.Raw {
		localClient, _ := getLocalHostParams(ctx, req.Isp, req.Rid)
		if localClient != nil {
			result, _ := localClient.DescribeSubnets(ctx, &common.DescribeSubnetsInput{VpcID: req.VpcId, ZoneID: req.ZoneId})
			if result != nil {
				return result, nil
			}
		}
	}
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeSubnets(ctx, &common.DescribeSubnetsInput{VpcID: req.VpcId, ZoneID: req.ZoneId})
}

// GetNetwork 获取网络
func (h HostRes) GetNetwork(ctx context.Context, req *cloudman.NetworkReq) (*cloudman.NetworkResp, error) {
	if !req.Raw {
		localClient, _ := getLocalHostParams(ctx, req.Isp, req.Rid)
		if localClient != nil {
			result, _ := localClient.DescribeNetworks(ctx, &common.DescribeNetworksInput{})
			if result != nil {
				return result, nil
			}
		}
	}
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeNetworks(ctx, &common.DescribeNetworksInput{})
}

func getAwsEc2Client(ctx context.Context, isp string, rid string) (*aws.Ec2Client, error) {
	regionID, err := models.RegionModel.FindManyWithPK(ctx, []string{rid}) // TODO: 由前端传入
	if err != nil {
		return nil, err
	}
	if len(regionID) == 0 {
		return nil, fmt.Errorf("not found region")
	}

	return aws.NewEc2Client(
		regionID[0].RegionID, isp,
	), nil
}

func getAliEcsClient(ctx context.Context, isp string, rid string) (*alicloud.AliEcsClient, error) {
	regionID, err := models.RegionModel.FindManyWithPK(ctx, []string{rid}) // TODO: 由前端传入
	if err != nil {
		return nil, err
	}
	if len(regionID) == 0 {
		return nil, fmt.Errorf("not found region")
	}

	return alicloud.CreateAliEcsClient(regionID[0].RegionID, isp)
}

// GetSecurityGroup 获取安全组
func (h HostRes) GetSecurityGroup(ctx context.Context, req *cloudman.SecurityGroupReq) (*cloudman.SecurityGroupResp, error) {
	if !req.Raw {
		localClient, _ := getLocalHostParams(ctx, req.Isp, req.Rid)
		if localClient != nil {
			result, _ := localClient.DescribeSecurityGroups(ctx, &common.DescribeSecurityGroupInput{
				VpcID: req.VpcId,
				Size:  req.Size,
			})
			if result != nil {
				return result, nil
			}
		}
	}
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeSecurityGroups(ctx, &common.DescribeSecurityGroupInput{
		VpcID: req.VpcId,
		Size:  req.Size,
	})
}

// ListChargeType 获取计费模型列表
func (h HostRes) ListChargeType(ctx context.Context, req *cloudman.ChargeTypeReq) (*cloudman.ChargeTypesResp, error) {
	if !req.Raw {
		localClient, _ := getLocalHostParams(ctx, req.Isp, req.Rid)
		if localClient != nil {
			result, _ := localClient.DescribeChargeType(ctx)
			if result != nil {
				return result, nil
			}
		}
	}
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeChargeType(ctx)
}

// GetKeyPairs 获取密钥对
func (h HostRes) GetKeyPairs(ctx context.Context, req *cloudman.KeyPairReq) (*cloudman.KeyPairResp, error) {
	client, err := getCloudHost(ctx, req.Isp, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeKeyPairs(ctx, &common.DescribeKeyPairsInput{
		KeyName: req.Name,
	})
}

// GetVolumeTypes GetVolumeTypes
func (h HostRes) GetVolumeTypes(ctx context.Context, req *cloudman.VolumeTypeReq) (*cloudman.VolumeTypesResp, error) {
	if !req.Raw {
		localClient, _ := getLocalHostParams(ctx, req.IspId, req.Rid)
		if localClient != nil {
			result, _ := localClient.DescribeVolumeTypes(ctx, &common.DescribeVolumeTypesInput{ImageID: req.ImageId})
			if result != nil {
				return result, nil
			}
		}
	}

	client, err := getCloudHost(ctx, req.IspId, req.Rid)
	if err != nil {
		return nil, err
	}

	return client.DescribeVolumeTypes(ctx, &common.DescribeVolumeTypesInput{ImageID: req.ImageId})
}

// Describe 获取主机资源列表
func (h HostRes) Describe(ctx context.Context, req *cloudman.HostResReq) (*cloudman.HostResResp, error) {
	resp, total, err := models.HostResourceModel.Query(ctx, &schema.HostResourceQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		HostResourceColumnParam: schema.HostResourceColumnParam{
			IspID:              req.Isp,
			IspType:            req.Type,
			OSType:             req.OsType,
			RegionID:           req.RegionId,
			TagKey:             req.TagKey,
			TagValues:          req.TagValues,
			AgentStatus:        req.AgentStatus,
			AgentEnv:           req.AgentEnv,
			MonitorStatus:      req.MonitorStatus,
			Status:             req.Status,
			ExpireBeforeTime:   req.ExpireTimeBefore,
			ReleaseResource:    req.ReleaseResource,
			DisplayRecyclable:  req.DisplayRecyclable,
			DisplayNeedCleanup: req.DisplayNeedCleanup,
			UpdateTime: schema.UpdateTimeColumnParam{
				Ref: req.UpdateTimeRef,
				Val: req.UpdateTimeVal,
			},
			BatchOSName:               req.BatchOsName,
			BatchAgentStatus:          req.BatchAgentStatus,
			BatchMonitorStatus:        req.BatchMonitorStatus,
			SecurityGroupIds:          req.SecurityGroupIds,
			AndSearchSecurityGroupIds: req.AndSearchSecurityGroupIds,
		},
		SearchParams: schema.SearchParams{},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.HostModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.HostResResp{List: list, Total: int32(total)}, err
}

func describeHostByInstanceIDs(ctx context.Context, instanceIDs []string) ([]*entity.HostResource, error) {
	instances, err := models.HostResourceModel.FindMany(ctx, map[string]interface{}{
		"InstanceID": map[string][]string{
			"$in": instanceIDs,
		},
	})
	if err != nil {
		return nil, err
	}
	return instances, nil
}

// DescribeOpsStatus 获取运维服务状态
func (h HostRes) DescribeOpsStatus(ctx context.Context, req *cloudman.Ids) (*cloudman.HostOpsStatusResp, error) {
	if len(req.Ids) == 0 {
		return &cloudman.HostOpsStatusResp{}, nil
	}
	hosts, err := models.HostResourceModel.FindWithManyPK(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	hostMap := map[string]*cloudman.OpsStatus{}
	for _, v := range hosts {
		hostMap[v.ID.Hex()] = &cloudman.OpsStatus{
			AgentStatus:   entity.TranslateStatus(v.AgentStatus, v.AgentUpdate),
			MonitorStatus: v.MonitorStatusToString(),
		}
	}

	return &cloudman.HostOpsStatusResp{HostMap: hostMap}, err
}

// GetExistOSNames ...
func (h HostRes) GetExistOSNames(ctx context.Context, req *cloudman.Empty) (*cloudman.OSNames, error) {
	OSNames, err := models.HostResourceModel.FindAllOSNames(ctx)
	if err != nil {
		return nil, err
	}
	sort.Strings(OSNames)
	return &cloudman.OSNames{List: OSNames}, nil
}

// GetZone 获取可用区
func (h HostRes) GetZone(ctx context.Context, req *cloudman.ZoneReq) (*cloudman.ZoneRespList, error) {
	account, err := models.AccountModel.Get(ctx, req.IspId)
	if err != nil {
		return nil, err
	}
	if account.AType == constant.IspAws {
		client, err := getAwsEc2Client(ctx, req.IspId, req.Rid)
		if err != nil {
			return nil, err
		}
		res, err := client.GetAvailabilityZones(ctx)
		if err != nil {
			return nil, err
		}

		var list []*cloudman.ZoneRespInfo
		for _, v := range res.AvailabilityZones {
			list = append(list, &cloudman.ZoneRespInfo{
				ZoneId:    tea.StringValue(v.ZoneId),
				LocalName: tea.StringValue(v.ZoneName),
			})
		}
		return &cloudman.ZoneRespList{List: list}, nil
	} else if account.AType == constant.IspMihoyo {
		return &cloudman.ZoneRespList{List: []*cloudman.ZoneRespInfo{
			{
				ZoneId:    "default_zone",
				LocalName: "默认区",
			},
		}}, nil
	} else if account.AType == constant.IspAliyun {
		client, err := getAliEcsClient(ctx, req.IspId, req.Rid)
		if err != nil {
			return nil, err
		}
		res, err := client.GetAliZones(ctx)
		if err != nil {
			return nil, err
		}

		var list []*cloudman.ZoneRespInfo
		for _, v := range res.Zones.Zone {
			resourceInfo := v.AvailableResources.ResourcesInfo
			if len(resourceInfo) != 0 {
				list = append(list, &cloudman.ZoneRespInfo{
					ZoneId:             tea.StringValue(v.ZoneId),
					LocalName:          tea.StringValue(v.LocalName),
					DataDiskCategories: tea.StringSliceValue(resourceInfo[0].DataDiskCategories.SupportedDataDiskCategory),
					SystemDiskCategory: tea.StringSliceValue(resourceInfo[0].SystemDiskCategories.SupportedSystemDiskCategory),
					InstanceType:       tea.StringSliceValue(resourceInfo[0].InstanceTypes.SupportedInstanceType),
				})
			}
		}
		return &cloudman.ZoneRespList{List: list}, nil
	} else {
		return &cloudman.ZoneRespList{List: []*cloudman.ZoneRespInfo{}}, nil
	}
}

// ExportXlsx 导出xlsx
func (h HostRes) ExportXlsx(ctx context.Context, req *cloudman.ExportXlsxReq) (*cloudman.ExportXlsxResp, string, error) {
	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row
	var cell *xlsx.Cell
	var err error

	st := models.HostResourceModel.FiledOptions(ctx)
	file = xlsx.NewFile()
	sheet, err = file.AddSheet("主机实例列表")
	if err != nil {
		return nil, "", err
	}
	// 加入标题(根据自定义导出列返回)

	selectFieldMap := map[string]struct{}{}
	for _, v := range req.ExportFields {
		selectFieldMap[v] = struct{}{}
	}

	var rowList []*entity.FiledStruct
	row = sheet.AddRow()
	// 打印标题
	for _, v := range st {
		if v.Extra { // 跳过特别增加的字段
			continue
		}
		if len(req.ExportFields) != 0 {
			if _, ok := selectFieldMap[v.Value]; !ok {
				continue
			}
		}

		cell = row.AddCell()
		cell.Value = v.Name
		if v.Required {
			cell.SetStyle(&xlsx.Style{
				Font: xlsx.Font{
					Bold: true,
				},
			})
		}

		rowList = append(rowList, &entity.FiledStruct{
			Name:     v.Name,
			Type:     v.Type,
			Value:    v.Value,
			Required: v.Required,
		})
	}

	var res []*entity.HostResource
	if req.ExportType == "selectWithCond" {
		res, _, err = models.HostResourceModel.Query(ctx, &schema.HostResourceQueryParams{
			PaginationParam: schema.PaginationParam{
				Size: 9999999,
			},
			HostResourceColumnParam: schema.HostResourceColumnParam{
				IspID:              req.Isp,
				IspType:            req.Type,
				RegionID:           req.RegionId,
				TagKey:             req.TagKey,
				TagValues:          req.TagValues,
				AgentStatus:        req.AgentStatus,
				MonitorStatus:      req.MonitorStatus,
				Status:             req.Status,
				ExpireBeforeTime:   req.ExpireTimeBefore,
				ReleaseResource:    req.ReleaseResource,
				DisplayRecyclable:  req.DisplayRecyclable,
				DisplayNeedCleanup: false,
				UpdateTime: schema.UpdateTimeColumnParam{
					Ref: req.UpdateTimeRef,
					Val: req.UpdateTimeVal,
				},
			},
			SearchColumnField: schema.SearchColumnField{
				SearchKey:   req.SearchKey,
				SearchValue: req.SearchValue,
			},
		})
	}

	if req.ExportType == "" {
		// 导出全部
		res, err = models.HostResourceModel.FindMany(ctx, map[string]interface{}{})
	}

	if req.ExportType == "select" && len(req.Selected) != 0 {
		// 导出范围: 导出选中并且选中的内容为空则直接返回
		var selected []string
		for _, v := range req.Selected {
			selected = append(selected, v.Id)
		}
		res, err = models.HostResourceModel.FindWithManyPK(ctx, selected)
	}

	if err != nil {
		return nil, "", err
	}

	for _, v := range res {
		row = sheet.AddRow()
		strMap, err := mapstruct.Struct2Map(v)
		if err != nil {
			break
		}

		for _, field := range rowList {

			cell = row.AddCell()
			if strMap[field.Value] == nil {
				cell.Value = ""
				continue
			}

			if field.Value == "_id" {
				cell.Value = v.ID.Hex()
				continue
			}
			switch field.Type {
			case "struct", "slice", "SliceString":
				val := strMap[field.Value]
				str, err1 := json.Marshal(val)
				if err1 != nil {
					logger.Error(err1.Error())
				}
				if field.Type == "struct" && string(str) == "{}" {
					cell.Value = ""
				} else {
					cell.Value = string(str)
				}
			default:
				cell.SetValue(strMap[field.Value])
			}
			// if field.Type == "string" {
			// 	cell.Value = (strMap[field.Value]).(string)
			// } else if field.Type == "int64" {
			// 	cell.SetInt64((strMap[field.Value]).(int64))
			// } else if field.Type == "int" {
			// 	cell.SetInt((strMap[field.Value]).(int))
			// } else {
			// 	str, err1 := json.Marshal(strMap[field.Value])
			// 	if err1 != nil {
			// 		logger.Println(err1.Error())
			// 	}
			//
			// 	cell.Value = string(str)
			// }
		}
	}

	var buf bytes.Buffer
	err = file.Write(&buf)
	if err != nil {
		return nil, "", err
	}

	filename := fmt.Sprintf("host_instance-%s.xlsx", time.Now().Format("20060102150405"))
	return &cloudman.ExportXlsxResp{File: buf.Bytes()}, filename, err
}

// UpdateMonitorStatus 批量更新主机Monitor状态
func (h HostRes) UpdateMonitorStatus(ctx context.Context, req *cloudman.UpdateMonitorStatusReq) (*cloudman.Result, error) {
	if err := models.HostResourceModel.UpdateMonitorAgentByIPs(ctx, req.GetIps(), req.GetStatus()); err != nil {
		return &cloudman.Result{Message: err.Error()}, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// GetRemoteToken 获取远程登录token
func (h HostRes) GetRemoteToken(ctx context.Context, req *cloudman.ObjectID) (*cloudman.Result, error) {
	hostInfo, err := models.HostResourceModel.FindOne(ctx, map[string]interface{}{
		"InstanceID": req.Id,
	})
	if err == mongo.ErrNoDocuments {
		return nil, fmt.Errorf("找不到主机资源%s", req.Id)
	} else if err != nil {
		return nil, err
	}

	isp, err := models.AccountModel.FindPK(ctx, hostInfo.IspID)
	if err != nil {
		return nil, fmt.Errorf("查询Account（%s）报错：%v", hostInfo.IspID, err)
	}
	if isp.AType == "mihoyo" {
		token := ""
		err := agentsdk.SyncCall(ctx, "", "host", "GetVncToken", hostInfo.IspID, []interface{}{req.Id}, &token)
		if err != nil {
			return nil, fmt.Errorf("获取vnc_token失败：%s", err.Error())
		}
		return &cloudman.Result{Message: token}, nil
	}
	return &cloudman.Result{}, fmt.Errorf("不支持的服务商类型%s", isp.AType)
}

// SyncHostInstances ...
func (h HostRes) SyncHostInstances(ctx context.Context, req *cloudman.SyncInstancesReq) (*cloudman.Result, error) {
	if len(req.Ids) == 0 {
		return nil, errors.New("empty host resource ids")
	}
	allHosts, err := models.HostResourceModel.FindWithManyPK(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	// key: account values: hosts
	accountHostMap := map[string][]*entity.HostResource{}
	for _, host := range allHosts {
		hosts, ok := accountHostMap[host.IspID]
		if !ok {
			accountHostMap[host.IspID] = []*entity.HostResource{host}
		} else {
			hosts = append(hosts, host)
			accountHostMap[host.IspID] = hosts
		}
	}

	for ispID, accountHosts := range accountHostMap {
		account, err := models.AccountModel.Get(ctx, ispID)
		if err != nil {
			logger.Errorf("handler:SyncResource:FindAccountFailed: accountID=%s err=%s", ispID, err.Error())
			continue
		}

		// key: region values: instanceIDs
		hostRegionMap := map[string][]string{}
		for _, host := range accountHosts {
			hostRegions, ok := hostRegionMap[host.RegionID]
			if !ok {
				hostRegionMap[host.RegionID] = []string{host.InstanceID}
			} else {
				hostRegions = append(hostRegions, host.InstanceID)
				hostRegionMap[host.RegionID] = hostRegions
			}
		}

		for regionID, instances := range hostRegionMap {
			for i := 0; i <= len(instances)/30; i++ {
				var searchInstances []string
				if i == len(instances)/30 {
					searchInstances = instances[i*30:]
				} else {
					searchInstances = instances[i*30 : (i+1)*30]
				}
				// send requests to Aliyun
				if account.AType == "aliyun" {
					aliSyncer := &synctask.AliyunSyncer{}
					if err := aliSyncer.SyncHost(ctx, nil, synctask.SyncOption{RegionID: regionID, IspID: ispID}, searchInstances...); err != nil {
						return nil, err
					}
				} else if account.AType == "aws" {
					awsSyncer := &synctask.AwsSyncer{}
					if err := awsSyncer.SyncHost(ctx, nil, synctask.SyncOption{RegionID: regionID, IspID: ispID}, searchInstances...); err != nil {
						return nil, err
					}
				}
			}
		}
	}

	return &cloudman.Result{Message: "success"}, nil
}

func (h HostRes) AssignIpv6Address(ctx context.Context, req *cloudman.AssignIpv6AddressReq) (*cloudman.Result, error) {
	hostResource, err := models.HostResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": req.InstanceId})
	if err != nil {
		return nil, err
	}
	if hostResource.IspType == "aliyun" {
		client, err := alicloud.CreateAliEcsClient(hostResource.RegionID, hostResource.IspID)
		if err != nil {
			return nil, err
		}
		if len(hostResource.NetworkInterfaces.NetworkInterface) == 0 {
			return nil, fmt.Errorf("cannot get network interface")
		}
		networkInterfaceId := hostResource.NetworkInterfaces.NetworkInterface[0].NetworkInterfaceID
		address, err := client.AssignIpv6Addresses(ctx, networkInterfaceId)
		if err != nil {
			return nil, err
		}

		if !req.EnablePublicAccess {
			return &cloudman.Result{Message: fmt.Sprintf("ipv6 address is %s", address)}, nil
		}
		ipv6Detail, err := client.WaitIpv6AvailAndGetDetail(ctx, address)
		if err != nil {
			return nil, err
		}
		if len(ipv6Detail.Ipv6Addresses.Ipv6Address) == 0 {
			return nil, fmt.Errorf("cannot get ipv6 address")
		}
		gatewayId := tea.StringValue(ipv6Detail.Ipv6Addresses.Ipv6Address[0].Ipv6GatewayId)
		addressId := tea.StringValue(ipv6Detail.Ipv6Addresses.Ipv6Address[0].Ipv6AddressId)
		// 绑定公网带宽
		err = client.AllocateIpv6InternetBandwidth(ctx, gatewayId, addressId, req.PublicPaytype, req.PublicBandwidth)
		if err != nil {
			return nil, err
		}
		return &cloudman.Result{Message: fmt.Sprintf("ipv6 address is %s", address)}, nil
	}

	return nil, fmt.Errorf("isp type %s not support", hostResource.IspType)
}

func (h HostRes) UnassignIpv6Address(ctx context.Context, req *cloudman.UnassignIpv6AddressesReq) (*cloudman.Result, error) {
	hostResource, err := models.HostResourceModel.FindOne(ctx, map[string]interface{}{"InstanceID": req.InstanceId})
	if err != nil {
		return nil, err
	}
	if hostResource.IspType == "aliyun" {
		client, err := alicloud.CreateAliEcsClient(hostResource.RegionID, hostResource.IspID)
		if err != nil {
			return nil, err
		}
		if len(hostResource.NetworkInterfaces.NetworkInterface) == 0 {
			return nil, fmt.Errorf("cannot get network interface")
		}
		networkInterfaceId := hostResource.NetworkInterfaces.NetworkInterface[0].NetworkInterfaceID
		err = client.UnAssignIpv6Address(ctx, networkInterfaceId, req.Ipv6Address)
		if err != nil {
			return nil, err
		}
		return &cloudman.Result{Message: "success"}, nil
	}

	return nil, fmt.Errorf("isp type %s not support", hostResource.IspType)
}
