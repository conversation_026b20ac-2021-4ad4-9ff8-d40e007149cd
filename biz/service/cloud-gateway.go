package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net"
	"strconv"
	"strings"
	"text/template"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jobman"

	"github.com/tidwall/sjson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/consul"
	notifySdk "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/op-notifyman-sdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"

	"github.com/tealeg/xlsx"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/ssh"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// pre-install-cmd = "/home/<USER>/ops/ossutil64 config -c /home/<USER>/ops/.ossutil_checkpoint --endpoint=https://oss-cn-shanghai.aliyuncs.com --access-key-id={{.AccessKeyID}} --access-key-secret={{.AccessKeySecret}}"
const createGatewayCMD = "ossutil cp -f --endpoint={{.endpoint}} --access-key-id={{.AccessKeyID}} --access-key-secret={{.AccessKeySecret}} oss://hk4e-ops-prod/install_gateway.sh . && cat install_gateway.sh|bash -xs install {{.AccessKeyID}} {{.AccessKeySecret}}"
const installAgentCMD = "ossutil cp -f --endpoint={{.endpoint}} --access-key-id={{.AccessKeyID}} --access-key-secret={{.AccessKeySecret}} oss://hk4e-ops-prod/ops-agent/install_agent.sh . && cat install_agent.sh | bash -s update {{.GatewayAddress}}/api/v1/agent/reg?agentId={{.agent_Id}} {{.agent_version}} {{.agent_type}}"

// CloudGateway 云管网关
type CloudGateway struct {
}

// GetHostAgentID 获取主机agentID
func (c CloudGateway) GetHostAgentID(ctx context.Context, req *cloudman.GetAgentReq) (*cloudman.AgentIDResp, error) {
	if req.InnerIpaddress == "" {
		return nil, fmt.Errorf("ip can't null")
	}

	region := "cn-shanghai"
	// 默认region为cn-shanghai
	if req.Region != "" {
		region = req.Region
	}
	accountIndexID := getAccountIndexID(ctx, req.AccountId)
	agentID, err := agent.GenerateID(region, fmt.Sprintf("%s:%d", req.InnerIpaddress, accountIndexID))
	return &cloudman.AgentIDResp{AgentId: agentID}, err
}

func getAccountIndexID(ctx context.Context, accountId string) uint32 {
	const defaultAccountIndexID = 58422
	var accountEntity entity.Account
	accEnt := entity.GetAccountCollection(models.GetEngine())
	_, err := models.FindPK(ctx, accEnt, accountId, &accountEntity)
	if err != nil {
		logger.Errorf("get index by account id(%s) failed: %s", accountId, err.Error())
		return defaultAccountIndexID
	}
	return accountEntity.IndexID
}

// RawAgentInfo 获取gateway内存中的值(包含断线，详情等信息)
func (c CloudGateway) RawAgentInfo(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	return nil, nil
}

// ListAgent agent列表
func (c CloudGateway) ListAgent(ctx context.Context, req *cloudman.ListAgentReq) (*cloudman.ListAgentResp, error) {
	li, currentCount, err := models.OpsAgentModel.Query(ctx, &schema.OpsAgentQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			Status:   req.Status,
			NodeType: req.NodeType,
			Env:      req.Env,
			Version:  req.Version,
			Hostname: req.HostName,
			HostIP:   req.HostIp,
			AgentID:  req.AgentId,
		},
		OpsAgentSearchParam: schema.OpsAgentSearchParam{
			Keywords:    req.Keywords,
			SearchParam: req.SearchParam,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})
	if err != nil {
		return nil, err
	}

	runningCount, err := models.OpsAgentModel.CountCondition(ctx, &schema.OpsAgentQueryParams{
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			Status:   1,
			NodeType: req.NodeType,
			Env:      req.Env,
		},
		OpsAgentSearchParam: schema.OpsAgentSearchParam{
			Keywords: req.Keywords,
		},
	})
	if err != nil {
		return nil, err
	}
	outdatedCount, err := models.OpsAgentModel.CountCondition(ctx, &schema.OpsAgentQueryParams{
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			Status:   3,
			NodeType: req.NodeType,
			Env:      req.Env,
		},
		OpsAgentSearchParam: schema.OpsAgentSearchParam{
			Keywords: req.Keywords,
		},
	})
	if err != nil {
		return nil, err
	}
	allCount, err := models.OpsAgentModel.CountCondition(ctx, &schema.OpsAgentQueryParams{
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			NodeType: req.NodeType,
			Env:      req.Env,
		},
		OpsAgentSearchParam: schema.OpsAgentSearchParam{
			Keywords: req.Keywords,
		},
	})
	if err != nil {
		return nil, err
	}

	var list []*cloudman.AgentCallbackReq
	for _, l := range li {
		status := l.Status
		if status == constant.AgentRunning && time.Now().Unix()-l.UpdatedTime > cfg.GetOpsAgentConf().LostWait {
			status = constant.AgentNoReport
		}
		list = append(list, &cloudman.AgentCallbackReq{
			AgentId:       l.AgentID,
			Status:        status,
			Env:           l.Env,
			RealIps:       l.RealIP,
			Region:        l.Region,
			Version:       l.Version,
			SystemVersion: l.SystemVersion,
			NodeType:      l.NodeType,
			ProxyId:       l.ProxyID,
			Hostname:      l.Hostname,
			HostIp:        l.HostIP,
			CpuCore:       l.Hardware.CPUCore,
			MemTotal:      l.Hardware.MemTotal,
			GpuList:       l.Hardware.GPUList,
			CpuModel:      l.Hardware.CPUModel,
			CreatedTime:   uint32(l.CreatedTime),
			UpdatedTime:   uint32(l.UpdatedTime),
		})
	}

	return &cloudman.ListAgentResp{
		List:     list,
		Total:    uint32(allCount),
		Running:  uint32(runningCount),
		Outdated: uint32(outdatedCount),
		Current:  uint32(currentCount),
	}, nil
}

// ChangeAgent 改变主机绑定的agent状态
func (c CloudGateway) ChangeAgent(ctx context.Context, req *cloudman.AgentCallbackReq) (*cloudman.Result, error) {
	region, addressPort, err := agent.DecodeAgentInfo(req.AgentId)
	if err != nil {
		return nil, err
	}

	reqDump, _ := json.Marshal(req)
	logger.Infof("ChangeAgent.dump: %s", string(reqDump))

	innerIP := strings.Split(addressPort, ":")[0]
	// 判断上报信息是否合法，仅告警不卡流程
	ipMatchFlag := false
	envMatchFlag := true
	for _, realIP := range req.RealIps {
		if strings.Contains(realIP, innerIP) {
			ipMatchFlag = true
			break
		}
	}
	if req.Env != "" {
		if utils.ResourceIsProd(req.Hostname) && req.Env != "prod" {
			envMatchFlag = false
		}
		if utils.ResourceIsTest(req.Hostname) && req.Env != "test" {
			envMatchFlag = false
		}
	}
	// receive status of agent-updated
	if req.Status == constant.AgentUpdated || req.Status == constant.AgentUpdateFailed {
		go func() {
			goCtx := context.Background()
			err = modifyAgentUpdateOrder(goCtx, req.AgentId, req.Status)
			if err != nil {
				logger.Errorf("failed to modify agent-update-order: %v", err)
			}
			if req.Status == constant.AgentUpdateFailed {
				err = models.OpsAgentModel.UpdateStatus(goCtx, req.AgentId, constant.AgentRunning)
				if err != nil {
					logger.Errorf("failed to update agent status: %v", err)
				}
			}
		}()
		return &cloudman.Result{Message: "success"}, nil
	}
	// receive status of agent-updating
	if req.Status == constant.AgentUpdating {
		err = models.OpsAgentModel.UpdateStatus(ctx, req.AgentId, req.Status)
		if err != nil {
			return nil, err
		}
		return &cloudman.Result{Message: "success"}, nil
	}
	if req.Status == constant.AgentRunning && (!ipMatchFlag || !envMatchFlag) {
		config, ok := cfg.GetNotifyCfg().Items["changeAgent"]
		if ok {
			err := notifySdk.SendMessage(context.Background(), &notifySdk.QNotify{
				Name:             "云管平台agent上报信息错误",
				TemplateMsg:      formatChangeAgentNotice(req),
				MikuUser:         config.MikuUser,
				PlateFromID:      config.PlateFromID,
				ToolID:           config.ToolID,
				TemplateID:       config.TemplateID,
				NotifyManAddress: cfg.GetNotifyCfg().NotifyAddress,
			})
			if err != nil {
				logger.Errorf("ChangeAgent.SendNotice.error: %s", err.Error())
			}
		}
	}

	var netInterfaces []models.AgentReportNetworkInterface
	for _, netInterfaceStr := range req.NetSpeed {
		netInterface := strings.Split(netInterfaceStr, ":")
		var bandWidth string
		if len(netInterface) > 1 {
			bandWidth = netInterface[1]
		}
		netInterfaces = append(netInterfaces, models.AgentReportNetworkInterface{
			Name:      netInterface[0],
			Bandwidth: bandWidth,
		})
	}

	for _, aid := range cfg.GetOpsAgentConf().AnyEnvAgent {
		if req.AgentId == aid {
			req.Env = "any"
		}
	}
	// err = models.HostResourceModel.UpdateAgent(ctx, region, innerIP, req.AgentId, req.Status)
	err = models.OpsAgentModel.CreateOrUpdate(ctx, &entity.OpsAgent{
		AgentID:       req.AgentId,
		Hostname:      req.Hostname,
		HostIP:        innerIP,
		RealIP:        req.RealIps,
		Region:        region,
		Status:        req.Status,
		Version:       req.Version,
		SystemVersion: req.SystemVersion,
		NodeType:      req.NodeType,
		ProxyID:       req.ProxyId,
		Env:           req.Env,
		Hardware: entity.OpsAgentHardware{
			GPUList:  req.GpuList,
			CPUModel: req.CpuModel,
			CPUCore:  req.CpuCore,
			MemTotal: req.MemTotal,
		},
	}, &models.AgentReportHostInfo{
		GPUList:          req.GpuList,
		CPUModel:         req.CpuModel,
		CPUCore:          req.CpuCore,
		MemTotal:         req.MemTotal,
		OSName:           req.SystemVersion,
		NetWorkInterface: netInterfaces,
	})
	return &cloudman.Result{Message: "success"}, err
}

// ChangeAgentStatus 改变主机绑定的agent状态
func (c CloudGateway) ChangeAgentStatus(ctx context.Context, req *cloudman.ChangeAgentStatusReq) (*cloudman.Result, error) {
	var failedAgent []string
	for _, agentID := range req.Ids {
		err := models.OpsAgentModel.UpdateStatus(ctx, agentID, req.Status)
		if err != nil {
			logger.Errorf("failed to update agent status: %s, %v", agentID, err)
			failedAgent = append(failedAgent, agentID)
		}
	}
	if len(failedAgent) != 0 {
		return nil, fmt.Errorf("failed agentID: %s", strings.Join(failedAgent, ","))
	}
	return &cloudman.Result{Message: "success"}, nil
}

// DeleteAgent 删除Agent
func (c CloudGateway) DeleteAgent(ctx context.Context, req *cloudman.ChangeAgentStatusReq) (*cloudman.Result, error) {
	var failedAgent []string
	for _, agentID := range req.Ids {
		err := models.OpsAgentModel.DeleteByAgentID(ctx, agentID)
		if err != nil {
			logger.Errorf("failed to delete agent: %s, %v", agentID, err)
			failedAgent = append(failedAgent, agentID)
		}
	}
	if len(failedAgent) != 0 {
		return nil, fmt.Errorf("failed agentID: %s", strings.Join(failedAgent, ","))
	}
	return &cloudman.Result{Message: "success"}, nil
}

// ChangeAgents -
func (c CloudGateway) ChangeAgents(ctx context.Context, req []*cloudman.AgentCallbackReq) (*cloudman.Result, error) {
	go func() {
		ctxGo := context.Background()
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf("%v", err)
			}
		}()

		for _, item := range req {
			region, addressPort, err := agent.DecodeAgentInfo(item.AgentId)
			if err != nil {
				continue
			}
			innerIP := strings.Split(addressPort, ":")[0]
			var netInterfaces []models.AgentReportNetworkInterface
			for _, netInterfaceStr := range item.NetSpeed {
				netInterface := strings.Split(netInterfaceStr, ":")
				var bandWidth string
				if len(netInterface) > 1 {
					bandWidth = netInterface[1]
				}
				netInterfaces = append(netInterfaces, models.AgentReportNetworkInterface{
					Name:      netInterface[0],
					Bandwidth: bandWidth,
				})
			}
			for _, aid := range cfg.GetOpsAgentConf().AnyEnvAgent {
				if item.AgentId == aid {
					item.Env = "any"
				}
			}
			err = models.OpsAgentModel.CreateOrUpdate(ctxGo, &entity.OpsAgent{
				AgentID:       item.AgentId,
				Hostname:      item.Hostname,
				HostIP:        innerIP,
				RealIP:        item.RealIps,
				Region:        region,
				Status:        item.Status,
				Version:       item.Version,
				SystemVersion: item.SystemVersion,
				NodeType:      item.NodeType,
				ProxyID:       item.ProxyId,
				Env:           item.Env,
				Hardware: entity.OpsAgentHardware{
					GPUList:  item.GpuList,
					CPUModel: item.CpuModel,
					CPUCore:  item.CpuCore,
					MemTotal: item.MemTotal,
				},
			}, &models.AgentReportHostInfo{
				GPUList:          item.GpuList,
				CPUModel:         item.CpuModel,
				CPUCore:          item.CpuCore,
				MemTotal:         item.MemTotal,
				OSName:           item.SystemVersion,
				NetWorkInterface: netInterfaces,
			})
			if err != nil {
				logger.Errorf("failed to update agent status: %s, %v", item.AgentId, err)
			}
		}
	}()
	return &cloudman.Result{Message: "success"}, nil
}

// GetGateAddress 获取网络连接地址
func (c CloudGateway) GetGateAddress(ctx context.Context, empty *cloudman.Empty) (*cloudman.GatewayAddressReq, error) {
	data, err := models.GatewayConfModel.Get(ctx)
	if err != nil {
		return nil, err
	}

	return &cloudman.GatewayAddressReq{
		PublicProtoc:   data.PublicProtoc,
		PublicAddress:  data.PublicAddress,
		PrivateProtoc:  data.PrivateProtoc,
		PrivateAddress: data.PrivateAddress,
	}, nil
}

// InsertGateAddress 写入网络连接地址
func (c CloudGateway) InsertGateAddress(ctx context.Context, req *cloudman.GatewayAddressReq) (*cloudman.Result, error) {
	data := &entity.GatewayConf{
		PublicProtoc:   req.PublicProtoc,
		PublicAddress:  req.PublicAddress,
		PrivateProtoc:  req.PrivateProtoc,
		PrivateAddress: req.PrivateAddress,
	}
	err := models.GatewayConfModel.CreateOrUpdate(ctx, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// CreateAgentScript 自动安装agent
func (c CloudGateway) CreateAgentScript(ctx context.Context, req *cloudman.CreateAgentReq) (*cloudman.ObjectID, error) {
	return nil, nil
}

// GetAgentScript 获取agent手动安装脚本
func (c CloudGateway) GetAgentScript(ctx context.Context, req *cloudman.CreateAgentReq) (*cloudman.Script, error) {
	agentInstall, err := c.checkAgent(ctx, req)
	if err != nil {
		return nil, err
	}
	if req.NetworkType != "public" {
		req.NetworkType = "private"
	}

	// if req.AuthType == "password" && req.SSHPassword == "" {
	// 	return nil, errors.New("ssh密码不得为空")
	// }
	hs, err := models.HostResourceModel.FindMany(ctx, map[string]interface{}{
		"isp_id":     agentInstall.Isp,
		"InstanceID": agentInstall.SSHostID,
	})
	if err != nil {
		return nil, err
	}

	logger.Debugf("hs:", len(hs))

	if len(hs) == 0 {
		return nil, errors.New("未发现相关主机")
	}

	host := hs[0]
	ipAddr := host.InnerIPAddress[0]

	agentID, err := agent.GenerateID(req.Region, fmt.Sprintf("%s:%d", ipAddr, agentInstall.SSHPort))
	if err != nil {
		return nil, err
	}

	script, err := c.buildAgentScript(ctx, req, agentID)
	if err != nil {
		return nil, err
	}

	return &cloudman.Script{Script: script}, nil
}

// AgentInstallSt agent安装参数
type AgentInstallSt struct {
	Isp         string `json:"isp,omitempty" validate:"required"`          // 服务提供商id
	Region      string `json:"region,omitempty" validate:"required"`       // 连接地域
	SSHostID    string `json:"ssh_host_id,omitempty" validate:"required"`  // ssh主机id
	SSHPort     uint32 `json:"ssh_port,omitempty" validate:"required"`     // ssh端口
	AuthType    string `json:"auth_type,omitempty" validate:"required"`    // ssh认证类型
	SSHUsername string `json:"ssh_username,omitempty" validate:"required"` // ssh username
	SSHPassword string `json:"ssh_password,omitempty"`                     // ssh密码
	SSHPem      string `json:"ssh_pem,omitempty"`                          // ssh证书内容
	NodeType    string `json:"node_type,omitempty"`                        // 节点类型
	NetworkType string `json:"network_type,omitempty"`                     // 网络类型
}

func (c CloudGateway) checkAgent(ctx context.Context, req *cloudman.CreateAgentReq) (*AgentInstallSt, error) {
	agentInstall := &AgentInstallSt{
		Isp:         req.Isp,
		Region:      req.Region,
		SSHostID:    req.SshHostId,
		SSHPort:     req.SshPort,
		AuthType:    req.AuthType,
		SSHUsername: req.SshUsername,
		SSHPassword: req.SshPassword,
		SSHPem:      req.SshPem,
		NodeType:    req.NodeType,
		NetworkType: req.NetworkType,
	}

	err := validator.Validate.StructCtx(ctx, agentInstall)

	return agentInstall, err
}

// buildAgentScript build agent安装脚本
func (c CloudGateway) buildAgentScript(ctx context.Context, req *cloudman.CreateAgentReq, agentID string) (string, error) {
	gateAddress, err := models.GatewayConfModel.Get(ctx)
	if err != nil {
		return "", err
	}

	var connectAddress string

	if req.NetworkType == "private" && gateAddress.PrivateAddress != "" {
		connectAddress = fmt.Sprintf("%s://%s", gateAddress.PrivateProtoc, gateAddress.PrivateAddress)
	}

	if req.NetworkType == "public" && gateAddress.PublicAddress != "" {
		connectAddress = fmt.Sprintf("%s://%s", gateAddress.PublicProtoc, gateAddress.PublicAddress)
	}

	if connectAddress == "" {
		return "", errors.New("未发现网关连接地址")
	}

	tpl, err := template.New("agent-script").Parse(installAgentCMD)
	if err != nil {
		return "", err
	}

	ossConf, ok := cfg.GetOPSOssConfig()
	if !ok {
		return "", errors.New("未发现oss配置")
	}

	agentConf := cfg.GetOpsAgentConf()

	var buf bytes.Buffer
	err = tpl.Execute(&buf, map[string]string{
		"endpoint":        ossConf.Endpoint,
		"AccessKeyID":     ossConf.AccessKeyID,
		"AccessKeySecret": ossConf.AccessKeySecret,
		"GatewayAddress":  connectAddress,
		"agent_Id":        agentID,
		"agent_version":   agentConf.LatestVersion,
		"agent_type":      req.NodeType,
	})
	return buf.String(), nil
}

// GetGatewayScript 获取gateway安装脚本
func (c CloudGateway) GetGatewayScript(ctx context.Context, req *cloudman.CreateGatewayReq) (*cloudman.Script, error) {
	script, err := c.buildGateScript()
	if err != nil {
		return nil, err
	}

	return &cloudman.Script{Script: script}, nil
}

// buildGateScript 获取gateway安装脚本
func (c CloudGateway) buildGateScript() (string, error) {
	tpl, err := template.New("gateway-script").Parse(createGatewayCMD)
	if err != nil {
		return "", nil
	}

	conf, ok := cfg.GetOPSOssConfig()
	if !ok {
		return "", errors.New("未发现oss配置")
	}

	var buf bytes.Buffer
	err = tpl.Execute(&buf, conf)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

// ListGateway 获取gateway列表
func (c CloudGateway) ListGateway(ctx context.Context, empty *cloudman.Empty) (*cloudman.ListGatewayResp, error) {
	cli, err := consul.NewClient(cfg.GetConsulAddress())
	if err != nil {
		return nil, err
	}

	// service, _, err := cli.Catalog().Service("ops-gateway", "", nil)
	gatewayConf, _ := cfg.GetOpsGatewayConf()
	service, _, err := cli.Catalog().Service(gatewayConf.Name, "", nil)
	if err != nil {
		return nil, err
	}

	var li []*cloudman.GatewayResp
	for _, v := range service {
		li = append(li, &cloudman.GatewayResp{
			Status:   v.Checks.AggregatedStatus(),
			Hostname: v.Node,
			Address:  v.ServiceAddress,
			Name:     v.ServiceID,
		})
	}

	return &cloudman.ListGatewayResp{List: li}, nil
}

// HostQuery 查询主机
type HostQuery struct {
	Hostname string `json:"hostname"`
	Region   string `json:"region"`
}

// Gettopo (deprecated) 获取topo,hostname+地域确定唯一agent
func (c CloudGateway) Gettopo(ctx context.Context, empty *cloudman.Empty) (*cloudman.TopoResult, error) {
	gatewayList, err := c.ListGateway(ctx, empty)
	if err != nil {
		return nil, err
	}

	agentList, _, err := models.OpsAgentModel.Query(ctx, &schema.OpsAgentQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: 9999,
		},
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			NodeType: "proxy",
		},
	})

	if err != nil {
		return nil, err
	}

	var ali []*cloudman.AgentResp
	for _, v := range agentList {
		ali = append(ali, &cloudman.AgentResp{
			AgentId:        v.AgentID,
			Status:         v.Status,
			RegionId:       v.Region,
			NodeType:       v.NodeType,
			ProxyId:        v.ProxyID,
			LatestHostname: v.Hostname,
			LatestIp:       v.HostIP,
			OsName:         v.SystemVersion,
		})
	}

	return &cloudman.TopoResult{
		Gateway: gatewayList.List,
		Agent:   ali,
	}, nil
}

func agentEntityToPb(e entity.OpsAgent) *cloudman.AgentCallbackReq {
	return &cloudman.AgentCallbackReq{
		AgentId:       e.AgentID,
		Status:        e.Status,
		Env:           e.Env,
		RealIps:       e.RealIP,
		Region:        e.Region,
		Version:       e.Version,
		SystemVersion: e.SystemVersion,
		NodeType:      e.NodeType,
		ProxyId:       e.ProxyID,
		Hostname:      e.Hostname,
		HostIp:        e.HostIP,
		CpuCore:       e.Hardware.CPUCore,
		MemTotal:      e.Hardware.MemTotal,
		GpuList:       e.Hardware.GPUList,
		CpuModel:      e.Hardware.CPUModel,
		CreatedTime:   uint32(e.CreatedTime),
		UpdatedTime:   uint32(e.UpdatedTime),
	}
}

// GetGatewayTopo 获取网关拓扑图
func (c CloudGateway) GetGatewayTopo(ctx context.Context, req *cloudman.GatewayTopoReq) (*cloudman.GatewayTopoResult, error) {
	proxyResp, _, err := models.OpsAgentModel.Query(ctx, &schema.OpsAgentQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: math.MaxInt32,
		},
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			NodeType: "proxy",
		},
	})
	if err != nil {
		return nil, err
	}

	normalResp, _, err := models.OpsAgentModel.Query(ctx, &schema.OpsAgentQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: math.MaxInt32,
		},
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			NodeType: "normal",
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
	})
	if err != nil {
		return nil, err
	}

	topos := map[string][]*cloudman.AgentCallbackReq{
		"proxy_": {}, "proxy_prod": {}, "proxy_test": {}, "normal": {}, "normal_prod": {}, "normal_test": {},
	}
	for _, l := range proxyResp {
		topos["proxy_"+l.Env] = append(topos["proxy_"+l.Env], agentEntityToPb(l))
	}
	for _, l := range normalResp {
		topos["normal_"+l.Env] = append(topos["normal_"+l.Env], agentEntityToPb(l))
	}

	return &cloudman.GatewayTopoResult{
		ProdTopo: &cloudman.Topo{
			ProxyList:  topos["proxy_prod"],
			NormalList: topos["normal_prod"],
		},
		TestTopo: &cloudman.Topo{
			ProxyList:  topos["proxy_test"],
			NormalList: topos["normal_test"],
		},
		NoEnvTopo: &cloudman.Topo{
			ProxyList:  topos["proxy_"],
			NormalList: topos["normal_"],
		},
	}, nil
}

// SSHInstall 执行ssh安装时的参数
type SSHInstall struct {
	ISP         string `json:"isp_id" validate:"required"`
	RegionID    string `json:"region_id" validate:"required"`
	SSHHostID   string `json:"ssh_host_id" validate:"required"`
	SSHHost     string `json:"ssh_host"`
	SSHPort     uint32 `json:"ssh_port" validate:"required"`
	AuthType    string `json:"auth_type"`
	SSHPassword string `json:"ssh_password"`
	SSHPem      string `json:"ssh_pem"`
	SSHUsername string `json:"ssh_username" validate:"required"`
}

// Do 执行ssh安装
func (s SSHInstall) Do(oid string, cmd string) error {
	err := models.TaskLogResultModel.Create(context.Background(), &entity.TaskLogResult{
		TaskLogID: oid,
	})
	if err != nil {
		return err
	}

	hook := LogResultHook{
		TaskLogResultModel: func(meta *entity.TaskLogResult) error {
			return models.TaskLogResultModel.Create(context.Background(), meta)
		},
		TaskLogID: oid,
	}

	logging := newResultLogger(hook)
	logging.Infoln("初始化ssh任务中........")

	sshClient, err := ssh.NewSSHClient(ssh.Auth{
		User:     s.SSHUsername,
		Password: s.SSHPassword,
		PemRaw:   []byte(s.SSHPem),
	}, net.JoinHostPort(s.SSHHost, strconv.Itoa(int(s.SSHPort))))

	if err != nil {
		logging.Errorf("初始化ssh失败, err:%s", err.Error())
		return err
	}

	err = sshClient.RunCommand(cmd, logging.Writer())
	if err != nil {
		logging.Errorf("任务执行失败, err:%s", err.Error())
	}
	return err
}

// CreateGateway 创建云网关
func (c CloudGateway) CreateGateway(ctx context.Context, req *cloudman.CreateGatewayReq) (*cloudman.ObjectID, error) {
	sshConfig := &SSHInstall{
		ISP:         req.Isp,
		SSHHostID:   req.SshHostId,
		SSHPort:     req.SshPort,
		AuthType:    req.AuthType,
		SSHPassword: req.SshPassword,
		SSHPem:      req.SshPem,
		SSHUsername: req.SshUsername,
		RegionID:    req.Region,
	}
	err := validator.Validate.StructCtx(ctx, sshConfig)
	if err != nil {
		return nil, err
	}
	region, err := models.RegionModel.Get(ctx, req.Region)
	if err != nil {
		return nil, err
	}

	res, err := models.HostResourceModel.FindMany(ctx, map[string]interface{}{
		"isp_id":     req.Isp,
		"InstanceID": req.SshHostId,
	})
	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		return nil, errors.New("未发现相关主机记录")
	}
	hostRes := res[0]
	sshConfig.SSHHost = hostRes.InnerIPAddress[0]

	username := permission.GetUsername(ctx)
	account, err := models.AccountModel.Get(ctx, sshConfig.ISP)
	if err != nil {
		return nil, err
	}
	taskOrder := &entity.ResOrder{
		IspID:      sshConfig.ISP,
		IspType:    account.AType,
		IspName:    account.Name,
		Action:     "InstallGateway",
		RegionID:   sshConfig.RegionID,
		Type:       "host",
		Status:     constant.TaskRunning,
		RegionName: region.Name,
	}
	taskOrder.CreateUser = username
	_, err = models.ResourceOrder.Create(ctx, taskOrder)
	if err != nil {
		return nil, err
	}

	cmd, err := c.buildGateScript()
	if err != nil {
		return nil, err
	}

	oid := taskOrder.ID.Hex()

	go func() {
		err = sshConfig.Do(oid, cmd)
		result := map[string]interface{}{
			"status":    constant.TaskSuccess,
			"over_time": time.Now().Unix(),
		}
		if err != nil {
			result["status"] = constant.TaskErr
			result["error_msg"] = err.Error()
		}

		models.ResourceOrder.Update(context.Background(), oid, result)
	}()

	return &cloudman.ObjectID{Id: oid}, nil
}

// CreateAgent 创建agent,目前仅支持两层连接,即gateway<-->proxy<-->agent
func (c CloudGateway) CreateAgent(ctx context.Context, req *cloudman.CreateGatewayReq) (*cloudman.ObjectID, error) {
	return nil, nil
}

// ExportXlsx 根据筛选导出所有符合的agent为xlsx
func (c CloudGateway) ExportXlsx(ctx context.Context, req *cloudman.ListAgentReq) (*cloudman.ExportXlsxResp, string, error) {
	allLi, _, err := models.OpsAgentModel.Query(ctx, &schema.OpsAgentQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: math.MaxInt32,
		},
		OpsAgentColumnParam: schema.OpsAgentColumnParam{
			// Status:   req.Status,
			NodeType: req.NodeType,
		},
		OpsAgentSearchParam: schema.OpsAgentSearchParam{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, "", err
	}

	var file *xlsx.File
	var sheet *xlsx.Sheet
	var row *xlsx.Row

	file = xlsx.NewFile()
	sheet, _ = file.AddSheet("运维Agent列表")
	// 打印标题
	titleArray := []string{
		"主机名",
		"内网IP列表",
		"操作系统",
		"节点类型",
		"运行状态",
		"运行版本",
		"最后更新时间",
	}
	row = sheet.AddRow()
	for _, title := range titleArray {
		cell := row.AddCell()
		cell.Value = title
		cell.SetStyle(&xlsx.Style{
			Font: xlsx.Font{
				Bold: true,
			},
		})
	}
	for _, l := range allLi {
		row = sheet.AddRow()
		// 主机名称
		cell := row.AddCell()
		cell.Value = l.Hostname
		// 内网ip列表
		cell = row.AddCell()
		cell.Value = l.HostIP
		// 操作系统
		cell = row.AddCell()
		cell.Value = l.SystemVersion
		// 节点类型
		cell = row.AddCell()
		cell.Value = l.NodeType
		// 运行状态
		cell = row.AddCell()
		if l.Status == 1 {
			cell.Value = "Running"
		} else if l.Status == 2 {
			cell.Value = "Stopped"
		} else {
			cell.Value = "Unknown"
		}
		// 运行版本
		cell = row.AddCell()
		cell.Value = l.Version
		// 最后更新时间
		cell = row.AddCell()
		cell.Value = time.Unix(l.UpdatedTime, 0).Format("2006-01-02 15:04:05")
	}

	var buf bytes.Buffer
	err = file.Write(&buf)
	if err != nil {
		return nil, "", err
	}
	filename := fmt.Sprintf("ops-agent-%s.xlsx", time.Now().Format("20060102150405"))
	return &cloudman.ExportXlsxResp{File: buf.Bytes()}, filename, err
}

// ListAgentInstallConfig -
func (c CloudGateway) ListAgentInstallConfig(ctx context.Context, req *cloudman.Empty) (*cloudman.ListAgentInstallConfigResp, error) {
	fullItem, err := models.OpsAgentInstallConfigModel.FindMany(ctx, map[string]interface{}{})
	if err != nil {
		return nil, err
	}
	result := []*cloudman.AgentInstallConfig{}
	for _, l := range fullItem {
		result = append(result, &cloudman.AgentInstallConfig{
			IspId:     l.IspID,
			RegionId:  l.RegionID,
			CmdbEnv:   l.CmdbEnv,
			BinaryUrl: l.BinaryURL,
			RegUrl:    l.RegURL,
		})
	}
	return &cloudman.ListAgentInstallConfigResp{List: result}, nil
}

// UpdateAgentInstallConfig -
func (c CloudGateway) UpdateAgentInstallConfig(ctx context.Context, req *cloudman.AgentInstallConfig) (*cloudman.Result, error) {
	inputObj := &entity.OpsAgentInstallConfig{
		IspID:     req.IspId,
		RegionID:  req.RegionId,
		CmdbEnv:   req.CmdbEnv,
		BinaryURL: req.BinaryUrl,
		RegURL:    req.RegUrl,
	}
	if req.BinaryUrl == "" && req.RegUrl == "" {
		inputObj.IsDelete = 1
	} else {
		inputObj.IsDelete = 0
	}
	err := models.OpsAgentInstallConfigModel.CreateOrUpdate(ctx, inputObj)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// ReinstallAgent ...
func (c CloudGateway) ReinstallAgent(ctx context.Context, req *cloudman.ReinstallAgentReq) (*cloudman.Result, error) {
	allHosts, err := models.HostResourceModel.FindWithManyPK(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	accounts, err := models.AccountModel.ToDashBoard(ctx)
	if err != nil {
		return nil, err
	}

	accountMap := map[string]*cloudman.DashBoardAccountDetail{}
	for _, account := range accounts {
		_, ok := accountMap[account.AccountId]
		if !ok {
			accountMap[account.AccountId] = account
		}
	}

	ispHosts := map[string]map[string][]*entity.HostResource{
		"aliyun":     {},
		"aws":        {},
		"jumpserver": {},
		"custom":     {},
	}
	for _, host := range allHosts {
		_, ok := ispHosts[host.IspType]
		if !ok {
			continue
		}
		_, ok = ispHosts[host.IspType][host.RegionID]
		if !ok {
			ispHosts[host.IspType][host.RegionID] = []*entity.HostResource{}
		}
		ispHosts[host.IspType][host.RegionID] = append(ispHosts[host.IspType][host.RegionID], host)
	}

	installData := struct {
		Username string `json:"username"`
		Password string `json:"password"`
		Port     string `json:"port"`
	}{Username: req.Username, Password: req.Password, Port: req.Port}

	for isp := range ispHosts {
		if isp == "custom" {
			installAgentsForIsp(ctx, accountMap, ispHosts[isp], installData)
		} else {
			installAgentsForIsp(ctx, accountMap, ispHosts[isp], "")
		}
	}
	return &cloudman.Result{Message: "start to install"}, nil
}

// UpdateAgent ...
func (c CloudGateway) UpdateAgent(ctx context.Context, req *cloudman.UpdateAgentReq) (*cloudman.Result, error) {
	allHosts, err := models.HostResourceModel.FindWithManyPK(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	accounts, err := models.AccountModel.ToDashBoard(ctx)
	if err != nil {
		return nil, err
	}

	accountMap := map[string]*cloudman.DashBoardAccountDetail{}
	for _, account := range accounts {
		_, ok := accountMap[account.AccountId]
		if !ok {
			accountMap[account.AccountId] = account
		}
	}

	accountRegionMap := map[string]map[string][]*entity.HostResource{}

	for _, host := range allHosts {
		_, ok := accountRegionMap[host.IspID]
		if !ok {
			accountRegionMap[host.IspID] = map[string][]*entity.HostResource{}
		}
		_, ok = accountRegionMap[host.IspID][host.RegionID]
		if !ok {
			accountRegionMap[host.IspID][host.RegionID] = []*entity.HostResource{}
		}
		accountRegionMap[host.IspID][host.RegionID] = append(accountRegionMap[host.IspID][host.RegionID], host)
	}

	for _, regions := range accountRegionMap {
		for regionID, hosts := range regions {
			var prodHosts []*entity.HostResource
			var testHosts []*entity.HostResource
			for _, host := range hosts {
				if host.CheckCmdbEnvFromTagsAndInstancename() == "prod" {
					prodHosts = append(prodHosts, host)
				} else {
					testHosts = append(testHosts, host)
				}
			}

			if len(prodHosts) > 0 {
				err = updateAgents(ctx, regionID, "prod", accountMap, prodHosts)
				if err != nil {
					logger.Errorf("failed to start to update agents: %v", err)
				}
			}
			if len(testHosts) > 0 {
				err = updateAgents(ctx, regionID, "test", accountMap, testHosts)
				if err != nil {
					logger.Errorf("failed to start to update agents: %v", err)
				}
			}
		}
	}
	return &cloudman.Result{Message: "start to install"}, nil
}

func updateAgents(ctx context.Context, regionID string, cmdbEnv string, accountMap map[string]*cloudman.DashBoardAccountDetail, hosts []*entity.HostResource) error {
	ispID := hosts[0].IspID
	detail := accountMap[ispID]
	order := &entity.ResOrder{
		Status:     constant.TaskRunning,
		IspID:      ispID,
		IspName:    detail.AccountName,
		IspType:    detail.AccountType,
		RegionID:   regionID,
		RegionName: "",
		Type:       "host",
		Action:     "UpdateAgent",
		RawData:    "",
		Reason:     "",
	}

	order.CreateUser = permission.GetUsername(ctx)
	order.CreatedTime = time.Now().Unix()
	order.StartTime = time.Now().Unix()

	orderID, err := models.ResourceOrder.Create(ctx, order)
	if err != nil {
		return err
	}
	order.ID = orderID

	defer func() {
		if err != nil {
			operateResourceAfter(order, err)
			return
		}

		go func() {
			// wait for the signal for 5m, then close the order if it hasn't been closed
			count := 0
			deferCtx := context.Background()
			for {
				time.Sleep(30 * time.Second)

				latestOrder, deferErr := models.ResourceOrder.Get(deferCtx, order.ID.Hex())
				if deferErr != nil {
					logger.Errorf("cannot get latest status of order: %s, %v", order.ID.Hex(), deferErr)
					continue
				}
				if latestOrder.Status == constant.TaskSuccess || latestOrder.Status == constant.TaskErr {
					return
				}

				// if timeout, force close the order
				if count == 10 {
					operateResourceAfter(order, errors.New("等待更新任务完成超时"))
					return
				}
				count++

				// if all tasks of the order have ended, close the order
				latestStatusDetail, deferErr := common.GetOrderStatusDetail(deferCtx, orderID.Hex())
				if deferErr != nil {
					logger.Errorf("cannot get latest status detail of order: %s", order.ID.Hex())
				}
				s, f, total := common.StatusDetailCount(latestStatusDetail)
				if len(s)+len(f) == total {
					var orderErr error = nil
					if len(f) != 0 {
						orderErr = errors.New("存在失败的任务")
					}
					operateResourceAfter(order, orderErr)
					return
				}
			}
		}()
	}()

	logger := BuildLogger(order.ID.Hex())
	logger.Infof("初始化provider")
	pr, err := getIspInfo(ctx, order.IspID, "", order.RegionID)
	if err != nil {
		pr.Logger.Errorf("初始化provider失败:%v", err.Error())
		return err
	}
	pr.SetLogger(logger)

	statusDetail := ""
	var sendHosts []*entity.HostResource
	for _, host := range hosts {
		statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
			common.ReportSprintf("%s.start_time", host.AgentID):    time.Now().Unix(),
			common.ReportSprintf("%s.last_time", host.AgentID):     time.Now().Unix(),
			common.ReportSprintf("%s.instance_id", host.AgentID):   host.InstanceID,
			common.ReportSprintf("%s.instance_name", host.AgentID): host.InstanceName,
			common.ReportSprintf("%s.status", host.AgentID):        "update_agent",
		})

		// if agent is not found or lost, fail fast
		opAgent, err := models.OpsAgentModel.FindOne(ctx, map[string]interface{}{
			"agent_id": host.AgentID,
		})
		if err != nil || opAgent.Status == constant.AgentLost || opAgent.Status == constant.AgentUpdating {
			statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
				common.ReportSprintf("%s.last_time", host.AgentID): time.Now().Unix(),
				common.ReportSprintf("%s.status", host.AgentID):    "agent_not_available",
				common.ReportSprintf("%s.is_err", host.AgentID):    true,
			})
		} else {
			sendHosts = append(sendHosts, host)
		}
	}
	common.ReportOrderStatusDetail(ctx, order.ID.Hex(), true, statusDetail, "", "")

	installConfig, err := models.OpsAgentInstallConfigModel.FindOne(ctx, map[string]interface{}{"isp_id": ispID, "region_id": regionID, "cmdb_env": cmdbEnv})
	if err != nil {
		pr.Logger.Errorln("获取安装配置失败，无法开始更新Agent")
		return err
	}

	err = jobman.UpdateAgentOnJobman(ctx, logger, cmdbEnv, sendHosts, installConfig.BinaryURL)
	if err != nil {
		logger.Errorf("更新agent失败: %v", err)
		return err
	}
	return nil
}

func modifyAgentUpdateOrder(ctx context.Context, agentID string, status int32) error {
	agentRunning := false
	count := 0
	if status == constant.AgentUpdated {
		// Wait to check agent status
		for {
			time.Sleep(10 * time.Second)
			count++
			if count == 15 {
				break
			}
			agentEntity, err := models.OpsAgentModel.FindOne(ctx, map[string]interface{}{
				"agent_id": agentID,
			})
			if err != nil {
				continue
			}
			if agentEntity.Status == constant.AgentRunning {
				agentRunning = true
				break
			}
		}
	}

	var orders []*entity.ResOrder
	var err error
	orders, err = models.ResourceOrder.FindMany(ctx, map[string]interface{}{
		"action": "UpdateAgent",
		"status": 1,
	})
	if err == mongo.ErrNoDocuments {
		return nil
	} else if err != nil {
		return err
	}

	for _, order := range orders {
		statusDetail, err := common.GetOrderStatusDetail(ctx, order.ID.Hex())
		if err != nil {
			logger.Errorf("cannot get order status detail: %v", err)
			continue
		}
		logger := BuildLogger(order.ID.Hex())
		if common.StatusDetailKeyExists(statusDetail, agentID) {
			if status == constant.AgentUpdated {
				if agentRunning {
					statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.last_time", agentID):  time.Now().Unix(),
						common.ReportSprintf("%s.status", agentID):     "success",
						common.ReportSprintf("%s.is_success", agentID): true,
					})
					logger.Infof("收到agent更新成功状态: %s", agentID)
				} else {
					statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), true, statusDetail, common.ReportKv{
						common.ReportSprintf("%s.last_time", agentID): time.Now().Unix(),
						common.ReportSprintf("%s.status", agentID):    "agent_not_running",
						common.ReportSprintf("%s.is_err", agentID):    true,
					})
					logger.Warnf("收到agent更新成功状态但是agent未上报: %s", agentID)
				}
			} else {
				statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), true, statusDetail, common.ReportKv{
					common.ReportSprintf("%s.last_time", agentID): time.Now().Unix(),
					common.ReportSprintf("%s.status", agentID):    "failed",
					common.ReportSprintf("%s.is_err", agentID):    true,
				})
				logger.Errorf("收到agent更新失败状态: %s", agentID)
			}

			// if all tasks of this order have ended, close the order
			s, f, total := common.StatusDetailCount(statusDetail)
			if len(s)+len(f) == total {
				var orderErr error = nil
				if len(f) != 0 {
					orderErr = errors.New("存在失败的任务")
				}
				operateResourceAfter(order, orderErr)
			}
		}
	}
	return nil
}

func installAgentsForIsp(ctx context.Context, accountMap map[string]*cloudman.DashBoardAccountDetail, ispHosts map[string][]*entity.HostResource, data interface{}) {
	for regionID, hosts := range ispHosts {
		if len(hosts) == 0 {
			continue
		}
		ispID := hosts[0].IspID
		_, ok := accountMap[ispID]
		if !ok {
			logger.Errorf("isp(%s) not exists", ispID)
			break
		}
		curRegionHosts := hosts
		curRegionID := regionID
		go func() {
			// taskBaseCtx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
			// 	"x-rpc-" + "username": permission.GetUsername(ctx),
			// }))
			taskBaseCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
			if err := createOrderAndInstallAgent(taskBaseCtx, curRegionHosts, curRegionID, accountMap[ispID], data); err != nil {
				logger.Errorf("ispID(%s) region(%s) install agent failed: %s", ispID, curRegionID, err.Error())
			}
		}()
	}
}

func createOrderAndInstallAgent(ctx context.Context, hosts []*entity.HostResource, regionID string, detail *cloudman.DashBoardAccountDetail, data interface{}) error {
	ispID := hosts[0].IspID
	order := &entity.ResOrder{
		Status:     constant.TaskRunning,
		IspID:      ispID,
		IspName:    detail.AccountName,
		IspType:    detail.AccountType,
		RegionID:   regionID,
		RegionName: "",
		Type:       "host",
		Action:     "ReinstallAgent",
		RawData:    "",
		Reason:     "",
	}

	order.CreateUser = permission.GetUsername(ctx)
	order.CreatedTime = time.Now().Unix()
	order.StartTime = time.Now().Unix()
	check := struct {
		InstallAgent bool        `json:"installAgent"`
		Data         interface{} `json:"data"`
	}{InstallAgent: true, Data: data}
	rawData, _ := json.Marshal(check)
	order.RawData = string(rawData)

	orderID, err := models.ResourceOrder.Create(ctx, order)
	if err != nil {
		return err
	}
	order.ID = orderID

	defer func() {
		operateResourceAfter(order, err)
	}()

	logger := BuildLogger(order.ID.Hex())
	logger.Infof("初始化provider")
	pr, err := getIspInfo(ctx, order.IspID, "", order.RegionID)
	if err != nil {
		pr.Logger.Errorf("初始化provider失败:%v", err.Error())
		return err
	}
	pr.SetLogger(logger)

	statusDetail := ""
	for _, host := range hosts {
		statusDetail = common.ReportOrderStatusDetailKv(ctx, order.ID.Hex(), false, statusDetail, common.ReportKv{
			common.ReportSprintf("%s.start_time", host.InstanceName):    time.Now().Unix(),
			common.ReportSprintf("%s.last_time", host.InstanceName):     time.Now().Unix(),
			common.ReportSprintf("%s.instance_id", host.InstanceName):   host.InstanceID,
			common.ReportSprintf("%s.instance_name", host.InstanceName): host.InstanceName,
			common.ReportSprintf("%s.status", host.InstanceName):        "install_agent",
		})
	}
	common.ReportOrderStatusDetail(ctx, order.ID.Hex(), true, statusDetail, "", "")

	err = installAgent(ctx, pr, order, hosts)
	if err != nil {
		logger.Errorf("install agent failed: %s", err.Error())
		return err
	}
	return nil
}

func formatChangeAgentNotice(agentObj *cloudman.AgentCallbackReq) string {
	result := ""
	if cfg.GetSystemConfig().Env == "dev" || cfg.GetSystemConfig().Env == "local" {
		result += fmt.Sprintf("(env:%s)\n\n\n", cfg.GetSystemConfig().Env)
	}
	item := []string{}
	_, addressPort, _ := agent.DecodeAgentInfo(agentObj.AgentId)
	innerIP := strings.Split(addressPort, ":")[0]
	item = append(item, fmt.Sprintf("解析IP：%v", innerIP))
	item = append(item, fmt.Sprintf("上报IP：%v", agentObj.RealIps))
	item = append(item, fmt.Sprintf("上报HostName：%v", agentObj.Hostname))
	item = append(item, fmt.Sprintf("上报env：%v", agentObj.Env))
	item = append(item, fmt.Sprintf("agentID：%v", agentObj.AgentId))

	result += strings.Join(item, "\n")
	jsonResult, _ := sjson.Set("", "msg", result)
	return jsonResult
}

// ListAgentVersion -
func (c CloudGateway) ListAgentVersion(ctx context.Context, req *cloudman.Empty) (*cloudman.ListAgentVersionResp, error) {
	res, err := models.OpsAgentModel.DistinctVersion(ctx)
	if err != nil {
		return nil, err
	}
	return &cloudman.ListAgentVersionResp{Versions: res}, nil
}
