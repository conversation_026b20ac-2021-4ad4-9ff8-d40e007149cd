package service

import (
	"errors"
	"math"
	"math/rand"

	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// Domain 安全组对象
type Domain struct {
}

// Describe 获取弹性公网IP列表
func (h Domain) Describe(ctx context.Context, req *cloudman.DescribeDomainReq) (*cloudman.DescribeDomainRes, error) {
	resp, total, err := models.DomainModel.Query(ctx, &schema.DomainQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		DomainColumnParam: schema.DomainColumnParam{
			RegionID: req.RegionId,
			IspID:    req.Isp,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{
			Ordering: []string{"+DomainId"},
		},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.DomainModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeDomainRes{List: list, Total: int32(total)}, err
}

// DescribeRecords ...
func (h Domain) DescribeRecords(ctx context.Context, req *cloudman.DescribeDomainRecordsReq) (*cloudman.DescribeDomainRes, error) {
	domains, err := h.Describe(ctx, &cloudman.DescribeDomainReq{
		Page: 1,
		Size: math.MaxInt32,
	})
	if err != nil {
		return nil, err
	}

	total := 0
	var resDomains []*cloudman.DomainEntity
	for _, domain := range domains.List {
		var resRecords []*cloudman.Record
		for _, record := range domain.RecordList {
			if req.RecordValue != "" && record.Value != req.RecordValue {
				continue
			}
			resRecords = append(resRecords, record)
		}
		if len(resRecords) != 0 {
			total += len(resRecords)
			domain.RecordList = resRecords
			resDomains = append(resDomains, domain)
		}
	}

	return &cloudman.DescribeDomainRes{List: resDomains, Total: int32(total)}, err
}

// SyncDnspod ...
func (h Domain) SyncDnspod(ctx context.Context, req *cloudman.Empty) (*cloudman.Result, error) {
	if synctask.IsSyncingDnspod.Load() {
		return nil, errors.New("域名正在同步中，请稍后重试")
	}
	go func() {
		goCtx := context.Background()
		if err := synctask.SyncDNSPod(goCtx); err != nil {
			logger.Warnf("failed to sync dnspod: %v", err)
		}
	}()
	return &cloudman.Result{Message: "success"}, nil
}

// CreateDomain ...
func (h Domain) CreateDomain(ctx context.Context, req *cloudman.CreateDomainReq) (*cloudman.Result, error) {
	var newID int
	for {
		// 生成一个9开头，9位的随机数
		newID = 9*int(math.Pow(10, 8)) + rand.Int()%int(math.Pow(10, 8))
		_, err := models.DomainModel.GetByDomainID(ctx, newID)
		if err != nil {
			if err == mongo.ErrNoDocuments {
				break
			} else {
				return nil, err
			}
		}
	}
	_, err := models.DomainModel.CreateOrUpdate(ctx, &entity.Domain{
		DomainID: newID,
		Name:     req.Name,
		Status:   "ENABLE",
		RegionID: "cn-shanghai",
		IspType:  "custom",
	})
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}
