package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	provider "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

// ErrorAccessKey ...
const ErrorAccessKey = "can't decode"

// StarAccessSecret ...
const StarAccessSecret = "666666"

// Account 账户rpc
type Account struct {
}

// UpdateParam 更新参数
func (a Account) UpdateParam(ctx context.Context, info *cloudman.AccountParamInfo) (*cloudman.Result, error) {
	data := a.paramPBToData(info)
	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}
	// 对传入参数进行验证
	if data.ParamType == "host" {
		var hostTplParam schema.APHostTplParams
		err = json.Unmarshal([]byte(data.Params), &hostTplParam)
		if err != nil {
			return nil, err
		}
		err = validator.Validate.StructCtx(ctx, hostTplParam)
		if err != nil {
			return nil, err
		}
	}
	err = models.AccountParamModel.Update(ctx, info.Id, utils.ToMap(data))
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

func (a Account) paramToPB(data entity.AccountParam) *cloudman.AccountParamInfo {
	return &cloudman.AccountParamInfo{
		Id:            data.ID.Hex(),
		ParamType:     cloudman.AccountParamsType(cloudman.AccountParamsType_value[data.ParamType]),
		BindRegionId:  data.BindRegionID,
		Params:        data.Params,
		BindAccountId: data.BindAccountID,
		UpdateTime:    int32(data.UpdatedTime),
		CreateTime:    int32(data.CreatedTime),
		UpdateUser:    data.UpdateUser,
		CreateUser:    data.CreateUser,
		Status:        cloudman.Status(cloudman.Status_value[data.Status]),
	}
}

func (a Account) paramPBToData(data *cloudman.AccountParamInfo) *entity.AccountParam {
	return &entity.AccountParam{
		BindAccountID: data.BindAccountId,
		ParamType:     data.ParamType.String(),
		BindRegionID:  data.BindRegionId,
		Params:        data.Params,
		Status:        data.Status.String(),
	}
}

// CreateParam 创建参数
func (a Account) CreateParam(ctx context.Context, info *cloudman.AccountParamInfo) (*cloudman.Result, error) {
	data := a.paramPBToData(info)
	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}
	// 对传入参数进行验证
	if data.ParamType == "host" {
		var hostTplParam schema.APHostTplParams
		err = json.Unmarshal([]byte(data.Params), &hostTplParam)
		if err != nil {
			return nil, err
		}
		err = validator.Validate.StructCtx(ctx, hostTplParam)
		if err != nil {
			return nil, err
		}
	}
	data.CreateUser = permission.GetUsername(ctx)
	result, err := models.AccountParamModel.Create(ctx, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: result}, nil
}

// ParamInfo 参数详情
func (a Account) ParamInfo(ctx context.Context, id *cloudman.ObjectID) (*cloudman.AccountParamInfo, error) {
	data, err := models.AccountParamModel.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	return a.paramToPB(*data), nil
}

// ListParam 获取参数列表
func (a Account) ListParam(ctx context.Context, req *cloudman.ListAccountParamReq) (*cloudman.ListAccountParamResp, error) {
	accountParams, total, err := models.AccountParamModel.Query(ctx, &schema.AccountParamQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		AccountParamColumnParam: schema.AccountParamColumnParam{
			ParamType:     req.ParamType,
			BindRegion:    req.BindRegionId,
			BindAccountID: req.BindAccountId,
		},
		OrderParams: schema.OrderParams{},
	})
	if err != nil {
		return nil, err
	}

	var data []*cloudman.AccountParamInfo
	for _, v := range accountParams {
		data = append(data, a.paramToPB(v))
	}
	return &cloudman.ListAccountParamResp{
		Total: uint32(total),
		List:  data,
	}, nil
}

// DelParam 删除参数
func (a Account) DelParam(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	err := models.AccountParamModel.Delete(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

func getIspInfo(ctx context.Context, isp string, regionPK string, regionID string) (*common.InitProvider, error) {
	account, err := models.AccountModel.Get(ctx, isp)
	if err != nil {
		return nil, err
	}
	pr := &common.InitProvider{
		RegionID: regionID,
		IspType:  account.AType,
		IspID:    account.ID.Hex(),
		IspName:  account.Name,
	}
	if regionID == "" {
		r, err := models.RegionModel.Get(ctx, regionPK) // TODO: 由前端传入
		if err != nil {
			return nil, err
		}
		pr.RegionID = r.RegionID
		pr.RegionName = r.Name
	}

	return pr, nil
}

func (a Account) testPing(ctx context.Context, ispType, kmsName, key, secret, host string) ([]*common.Region, error) {
	provideName := ispType + "_host"
	f, ok := provider.ProvideMap[provideName]
	if !ok {
		return nil, errors.New("not found provider")
	}

	hook := LogResultHook{
		TaskLogResultModel: func(meta *entity.TaskLogResult) error {
			return nil
		},
	}

	logging := newResultLogger(hook)
	fun, err := f(common.InitProvider{
		Logger: logging,
		Host:   host,
	})

	if err != nil {
		return nil, err
	}

	return fun.TestPing(ctx, kmsName, key, secret, 2*time.Second)
}

// GetAccountRegion 获取地域信息
func (a Account) GetAccountRegion(ctx context.Context, request *cloudman.UpdateAccountRequest) (*cloudman.RegionResult, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	_, accessSecret, err := correctAKSK(ctx, request.Id, request.AccessKey, request.AccessSecret)
	if err != nil {
		return nil, err
	}

	regions, err := a.testPing(ctx, request.AType, request.KmsName, request.AccessKey, accessSecret, request.Host)
	if err != nil {
		return nil, err
	}

	region := &entity.Region{
		IspType: request.AType,
	}

	var PbRegions []*cloudman.Region
	for _, v := range regions {
		region.RegionID = v.RegionID
		region.Name = v.Name
		region.Endpoint = v.Endpoint
		err = models.RegionModel.FindOrCreate(ctx, region)
		if err != nil {
			logger.Error(fmt.Sprintf("region model操作失败:%s", err.Error()))
			return nil, errors.New("服务器错误")
		}

		PbRegions = append(PbRegions, &cloudman.Region{
			LocalName: v.Name,
			RegionId:  v.RegionID,
			IspType:   v.IspType,
		})
	}

	return &cloudman.RegionResult{Regions: PbRegions}, nil
}

// TestRawPing 使用账号、密码测试连通性
func (a Account) TestRawPing(ctx context.Context, request *cloudman.UpdateAccountRequest) (*cloudman.PingResult, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	fromDB, accessSecret, err := correctAKSK(ctx, request.Id, request.AccessKey, request.AccessSecret)
	if err != nil {
		return nil, err
	}
	pingResult := cloudman.PingResult_PongSKFromReq
	if fromDB {
		pingResult = cloudman.PingResult_PongSKFromDB
	}

	_, err = a.testPing(ctx, request.AType, request.KmsName, request.AccessKey, accessSecret, request.Host)
	if err != nil {
		logger.Errorf("TestRawPing failed: %s", err.Error())
		return &cloudman.PingResult{Result: cloudman.PingResult_NoReply}, nil
	}

	return &cloudman.PingResult{Result: pingResult}, nil
}

func correctAKSK(ctx context.Context, accountID, inputAK, inputSK string) (bool, string, error) {
	accessSecret := inputSK
	fromDB := false
	if accountID != "" {
		account, err := models.AccountModel.Get(ctx, accountID)
		if err != nil {
			return false, "", err
		}

		accessKey, err := utils.DecryptStrByAes(account.AccessKey, constant.AccountSecret)
		if err != nil {
			return false, "", fmt.Errorf("Account.Ping.DecryptAccessKey: %s", err.Error())
		}

		if inputAK == accessKey && inputSK == StarAccessSecret {
			fromDB = true
			accessSecret, err = utils.DecryptStrByAes(account.AccessSecret, constant.AccountSecret)
			if err != nil {
				return false, "", fmt.Errorf("Account.Ping.DecryptAccessSecret: %s", err.Error())
			}
		}
	}
	return fromDB, accessSecret, nil
}

// GetBindRegion 获取账号纳管地域列表
func (a Account) GetBindRegion(ctx context.Context, id *cloudman.ObjectID) (*cloudman.RegionListResponse, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	account, err := models.AccountModel.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	var list []entity.Region
	if len(account.RegionIDs) != 0 {
		list, err = models.RegionModel.FindManyWithPK(ctx, account.RegionIDs)
		if err != nil {
			return nil, err
		}
	}

	var li []*cloudman.RegionDetail
	for _, v := range list {
		li = append(li, &cloudman.RegionDetail{
			Name:     v.Name,
			IspType:  v.IspType,
			RegionId: v.RegionID,
			Id:       v.ID.Hex(),
		})
	}

	return &cloudman.RegionListResponse{
		List: li,
	}, nil
}

// UpdateStatus 更新账户状态
func (a Account) UpdateStatus(ctx context.Context, req *cloudman.UpdateAccountStatusReq) (*cloudman.Result, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	data := map[string]interface{}{
		"update_user": permission.GetUsername(ctx),
		"status":      req.Status,
	}

	err := models.AccountModel.Update(ctx, req.Id, data)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{Message: "success"}, nil
}

// List 获取账户列表
func (a Account) List(ctx context.Context, req *cloudman.AccountQueryReq) (*cloudman.AccountListRes, error) {
	// meta := rpc.Metadata(ctx) // 获取head中包含的信息
	list, total, err := models.AccountModel.Query(ctx, &schema.AccountQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		AccountColumnParam: schema.AccountColumnParam{
			Name: req.Name,
			Type: req.Type,
		},
		AccountSearchParam: schema.AccountSearchParam{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var res []*cloudman.AccountDetail
	for _, v := range list {
		item := &cloudman.AccountDetail{
			Id:                  v.ID.Hex(),
			Status:              uint32(v.Status),
			Name:                v.Name,
			AType:               v.AType,
			AccessKey:           v.AccessKey,
			AccessSecret:        StarAccessSecret, // 隐藏秘钥
			PingStatus:          v.PingStatus,
			IndexId:             v.IndexID,
			HighLevelPermission: v.HighLevelPermission,
			UseAgent:            v.UseAgent,
			UpdateUser:          v.UpdateUser,
			UpdatedTime:         v.UpdatedTime,
			Host:                v.Host,
			ProjectId:           v.ProjectID,
		}
		if item.UseAgent {
			//exist, running := models.CloudAgentModel.CheckExistAndRunning(ctx, item.Id)
			exist, running := false, false
			if exist {
				item.AgentStatus |= 1 << 0
			}
			if running {
				item.AgentStatus |= 1 << 1
			}
		}
		res = append(res, item)
	}
	return &cloudman.AccountListRes{
		List:  res,
		Total: uint32(total),
	}, nil
}

// ListAll 获取所有账户列表
func (a Account) ListAll(ctx context.Context, req *cloudman.AccountQueryReq) (*cloudman.AccountListRes, error) {
	ctx = context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	list, total, err := models.AccountModel.Query(ctx, &schema.AccountQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		AccountColumnParam: schema.AccountColumnParam{
			Name: req.Name,
			Type: req.Type,
		},
		AccountSearchParam: schema.AccountSearchParam{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var res []*cloudman.AccountDetail
	for _, v := range list {
		ak, _ := utils.DecryptStrByAes(v.AccessKey, constant.AccountSecret)

		res = append(res, &cloudman.AccountDetail{
			Id:                  v.ID.Hex(),
			Status:              uint32(v.Status),
			Name:                v.Name,
			AType:               v.AType,
			AccessKey:           ak,
			AccessSecret:        StarAccessSecret, // 隐藏秘钥
			PingStatus:          v.PingStatus,
			HighLevelPermission: v.HighLevelPermission,
			UseAgent:            v.UseAgent,
			Host:                v.Host,
			ProjectId:           v.ProjectID,
		})
	}
	return &cloudman.AccountListRes{
		List:  res,
		Total: uint32(total),
	}, nil
}

// Create 创建账户
func (a Account) Create(ctx context.Context, request *cloudman.CreateAccountRequest) (*cloudman.Result, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	if request.AType != "custom" && request.AType != "jumpserver" {
		IspExist := models.AccountModel.IspExist(ctx, request.AType)
		if IspExist {
			return nil, fmt.Errorf("相同类型云厂商已存在")
		}
	}
	userIDs := make([]string, 0, len(request.UserIds))
	for _, v := range request.UserIds {
		userIDs = append(userIDs, v.GetId())
	}
	encryptedAccessKey, _ := utils.EncryptStrByAes(request.AccessKey, constant.AccountSecret)
	encryptedAccessSecret, _ := utils.EncryptStrByAes(request.AccessSecret, constant.AccountSecret)
	data := &entity.Account{
		Name:                request.Name,
		AType:               request.AType,
		Status:              request.Status,
		AccessKey:           encryptedAccessKey,
		AccessSecret:        encryptedAccessSecret,
		RegionIDs:           request.RegionIds,
		UserIDs:             userIDs,
		IndexID:             request.IndexId,
		HighLevelPermission: request.HighLevelPermission,
		Arn:                 request.Arn,
		UseAgent:            request.UseAgent,
		Host:                request.Host,
		KMSName:             request.KmsName,
		ProjectID:           request.ProjectId,
	}
	data.CreateUser = permission.GetUsername(ctx)

	_, err := models.AccountModel.Create(ctx, data)
	if err != nil {
		return nil, err
	}

	//err = a.createSyncTask(ctx, aID, data)
	//if err != nil {
	//	return nil, err
	//}

	return &cloudman.Result{Message: "success"}, nil
}

// createSyncTask
func (a Account) createSyncTask(ctx context.Context, accountID string, data *entity.Account) error {
	t := &entity.SyncTask{
		AccountID: accountID,
		Policy:    1, // 1周期同步
		Rate:      60,
		Retry:     3,
		Timeout:   600,
		Status:    0,
	}
	t.CreateUser = permission.GetUsername(ctx)

	synctasks, err := models.SyncTaskModel.FindByAccountID(ctx, accountID)
	if err != nil && err != mongo.ErrNoDocuments {
		return err
	}

	taskSyncer := synctask.GetTaskSyncer(data.AType)
	for _, regionID := range data.RegionIDs {
		reg, err := models.RegionModel.Get(ctx, regionID)
		if err != nil {
			return err
		}
		for taskName, taskType := range taskSyncer.TaskSet() {
			t.BindRegionID = regionID
			t.BindRegionName = reg.Name
			t.BindRegionSourceID = reg.RegionID
			t.Name = taskName
			t.TaskType = taskType

			alreadyHas := false
			for _, v := range synctasks {
				if v.BindRegionID == regionID && v.TaskType == taskType {
					alreadyHas = true
					break
				}
			}
			if alreadyHas {
				continue
			}

			_, err := models.SyncTaskModel.Create(ctx, t)
			if err != nil {
				return err
			}
		}
	}

	// for _, regionID := range data.RegionIDs {
	//	reg, err := models.RegionModel.Get(ctx, regionID)
	//	if err != nil {
	//		return err
	//	}
	//	for taskname := range synctask.TaskSet {
	//		t.BindRegionID = regionID
	//		t.BindRegionName = reg.Name
	//		t.Name = taskname
	//		t.TaskType = taskname
	//		_, err := models.SyncTaskModel.Create(ctx, t)
	//		if err != nil {
	//			return err
	//		}
	//	}
	// }

	return nil
}

// Ping 测试账户连通性
func (a Account) Ping(ctx context.Context, request *cloudman.ObjectID) (*cloudman.PingResult, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	account, err := models.AccountModel.Get(ctx, request.Id)
	if err != nil {
		return nil, err
	}

	defer func() {
		status := map[string]interface{}{"ping_status": 0}
		if err != nil {
			status["ping_status"] = 1
		}
		models.AccountModel.Update(ctx, account.ID.Hex(), status)
	}()
	accessKey, err := utils.DecryptStrByAes(account.AccessKey, constant.AccountSecret)
	if err != nil {
		return nil, fmt.Errorf("Account.Ping.DecryptAccessKey: %s", err.Error())
	}
	accessSecret, err := utils.DecryptStrByAes(account.AccessSecret, constant.AccountSecret)
	if err != nil {
		return nil, fmt.Errorf("Account.Ping.DecryptAccessSecret: %s", err.Error())
	}

	_, err = a.testPing(ctx, account.AType, account.KMSName, accessKey, accessSecret, account.Host)
	if err != nil {
		return &cloudman.PingResult{Result: cloudman.PingResult_NoReply}, nil
	}

	return &cloudman.PingResult{Result: cloudman.PingResult_PongSKFromDB}, nil
}

// Retrieve 获取账户详情
func (a Account) Retrieve(ctx context.Context, id *cloudman.ObjectID) (*cloudman.AccountDetail, error) {
	hasPermission, err := models.AccountModel.HasAccountPermission(ctx, id.GetId())
	if err != nil {
		return nil, err
	}
	if !hasPermission {
		return nil, errors.New("没有查看此资源的权限")
	}

	data, err := models.AccountModel.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}
	var r []*cloudman.Region

	if data.AType != "custom" {
		rList, err := models.RegionModel.FindManyWithPK(ctx, data.RegionIDs)
		if err != nil {
			return nil, err
		}
		for _, v := range rList {
			r = append(r, &cloudman.Region{
				LocalName: v.Name,
				RegionId:  v.RegionID,
				IspType:   v.IspType,
				Id:        v.ID.Hex(),
			})
		}
	}

	u := make([]*cloudman.UserId, 0)
	for _, v := range data.UserIDs {
		u = append(u, &cloudman.UserId{Id: v})
	}

	accessKey, err := utils.DecryptStrByAes(data.AccessKey, constant.AccountSecret)
	if err != nil {
		accessKey = ErrorAccessKey
	}

	return &cloudman.AccountDetail{
		Id:                  data.ID.Hex(),
		Status:              uint32(data.Status),
		Name:                data.Name,
		AType:               data.AType,
		AccessKey:           accessKey,
		AccessSecret:        "666666",
		Region:              r,
		UserIds:             u,
		IndexId:             data.IndexID,
		HighLevelPermission: data.HighLevelPermission,
		UseAgent:            data.UseAgent,
		Host:                data.Host,
		KmsName:             data.KMSName,
		ProjectId:           data.ProjectID,
	}, nil
}

// Update 更新账户
func (a Account) Update(ctx context.Context, request *cloudman.UpdateAccountRequest) (*cloudman.Result, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	accessKey := request.AccessKey
	if accessKey != ErrorAccessKey {
		accessKey, _ = utils.EncryptStrByAes(request.AccessKey, constant.AccountSecret)
	}
	accessSecret := request.AccessSecret
	if accessSecret == StarAccessSecret {
		zResp, err := models.AccountModel.Get(ctx, request.Id, true)
		if err != nil {
			return nil, err
		}
		accessSecret = zResp.AccessSecret
	} else if accessSecret != "" {
		accessSecret, _ = utils.EncryptStrByAes(accessSecret, constant.AccountSecret)
	}

	userIDs := make([]string, 0, len(request.UserIds))
	for _, v := range request.UserIds {
		userIDs = append(userIDs, v.GetId())
	}
	account := &entity.Account{
		Name:                request.Name,
		AType:               request.AType,
		Status:              request.Status,
		AccessKey:           accessKey,
		AccessSecret:        accessSecret,
		RegionIDs:           request.RegionIds,
		UserIDs:             userIDs,
		IndexID:             request.IndexId,
		HighLevelPermission: request.HighLevelPermission,
		UseAgent:            request.UseAgent,
		Host:                request.Host,
		KMSName:             request.KmsName,
		ProjectID:           request.ProjectId,
	}
	account.UpdateUser = permission.GetUsername(ctx)

	d, err := mapstruct.Struct2Map(account)
	if err != nil {
		return nil, err
	}

	err = models.AccountModel.Update(ctx, request.Id, d)
	if err != nil {
		return nil, err
	}

	//err = a.deleteBindTask(ctx, request.Id)
	//if err != nil {
	//	return nil, err
	//}
	//
	err = a.createSyncTask(ctx, request.Id, account)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// deleteBindTask 账户更新删除所有同步任务,更新完成后再创建
func (a Account) deleteBindTask(ctx context.Context, aid string) error {
	_, err := models.SyncTaskModel.UpdateMany(ctx, map[string]interface{}{"account_id": aid}, map[string]interface{}{"is_delete": 1})
	return err
}

// Destroy 删除账户
func (a Account) Destroy(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	if !permission.IsAdminFromCtx(ctx) {
		return nil, errors.New("该操作需要管理员权限")
	}

	_, err := models.AccountModel.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}
	/*
		删除云账户动作
		1. 检查云账户的主机已全部被释放
		2. 同步删除所有绑定的任务
		3. 通知定时任务平台停止定时任务
	*/

	hosts, err := models.HostResourceModel.FindMany(ctx, map[string]interface{}{"isp_id": id.Id})
	if len(hosts) != 0 {
		return nil, errors.New("存在未释放的该云厂商主机")
	}

	username := permission.GetUsername(ctx)

	data := map[string]interface{}{
		"update_user":  username,
		"is_delete":    1,
		"deleted_time": time.Now().Unix(),
	}

	defer func() {
		_, err := models.SyncTaskModel.UpdateMany(ctx, map[string]interface{}{
			"account_id": id,
		}, map[string]interface{}{
			"is_delete":   1,
			"update_user": username,
		})
		if err != nil {
			logger.Errorf("DeleteByAccountID Error:", err.Error())
		}
		// TODO: 通知定时任务平台
	}()

	err = models.AccountModel.Update(ctx, id.Id, data)
	if err != nil {
		return nil, err
	}

	err = a.deleteBindTask(ctx, id.Id)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{}, nil
}

// Sync 同步账户
func (a Account) Sync(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	// 检查权限;检查任务状态;
	hasPerm, err := permission.CheckPermissionFromCtx(ctx, "order_admin", "")
	if err != nil || !hasPerm {
		return nil, fmt.Errorf("没有同步资源权限")
	}

	taskList, err := models.SyncTaskModel.FindByAccountID(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	go func() {
		// 便利syncTask，同步串行
		for _, task := range taskList {
			if err := runSyncTask(context.Background(), task.ID.Hex(), false); err != nil {
				logger.Errorf(err.Error())
				continue
			}
		}
	}()

	return &cloudman.Result{Message: "success"}, nil
}
