package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/core"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mapstruct"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/querybuilder"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// var ResGroupRelated

// ResGroupPolicy 资源组分组策略
type ResGroupPolicy struct {
}

// CountResource 资源计数
type CountResource struct {
	Host  int64 `json:"host"`
	Mysql int64 `json:"mysql"`
	Redis int64 `json:"redis"`
}

// ResourceGroupFilter 资源分组过滤
type ResourceGroupFilter struct {
	Filter map[string]interface{} `json:"filter"`
	Model  core.ResourceModel     `json:"model"`
}

// CountResource 获取分组统计
func (r ResGroupPolicy) CountResource(ctx context.Context, req *cloudman.CountResReq) (*cloudman.CountResResp, error) {
	var wg sync.WaitGroup
	var lock sync.Mutex
	totalObj := make(map[string]interface{})
	filters := map[string]*ResourceGroupFilter{
		"host": {
			Model: models.HostResourceModel,
		},
		"mysql": {
			Model: models.MysqlClusterResourceModel,
		},
		"redis": {
			Model: models.RedisResourceModel,
		},
	}

	if req.BindResGroupId == "" {
		for _, v := range filters {
			v.Filter = models.HostDefaultFilterMap(ctx, map[string]interface{}{})
		}
	} else {
		p, err := models.ResourcePolicy.FindPolicy(ctx, map[string]interface{}{"bind_res_group_id": req.BindResGroupId})
		if err != nil {
			return nil, err
		}
		for _, v := range p {
			filter, _, err := v.ToFilter()
			if err != nil {
				return nil, err
			}
			filter = models.HostDefaultFilterMap(ctx, filter)
			f := filters[v.ResourceType]

			if len(filter) == 0 {
				f.Filter = nil
			} else {
				f.Filter = filter
			}
		}
	}

	for rType, rFilter := range filters {
		wg.Add(1)
		rFilter := rFilter
		go func(rt string) {
			total, err := rFilter.Model.Count(ctx, rFilter.Filter)
			if err != nil {
				logger.Errorf(err.Error())
			}
			lock.Lock()
			totalObj[rt] = total
			lock.Unlock()
			wg.Done()
		}(rType)
	}

	wg.Wait()
	var resp CountResource
	err := mapstruct.DecodeFromMapStr(&resp, totalObj)

	return &cloudman.CountResResp{
		HostTotal:  uint32(resp.Host),
		MysqlTotal: uint32(resp.Mysql),
		RedisTotal: uint32(resp.Redis),
	}, err
}

// FindResource 获取分组数据
func (r ResGroupPolicy) FindResource(ctx context.Context, req *cloudman.FindResReq) (*cloudman.FindResResp, error) {
	mergedFilter := make(map[string]interface{})
	filter := make(map[string]interface{})
	if req.BindResGroupId != "" {
		p, err := models.ResourcePolicy.GetPolicy(ctx, req.BindResGroupId, req.ResourceType)
		if err != nil {
			return nil, err
		}
		if p != nil {
			filter, _, err = p.ToFilter()
			if err != nil {
				return nil, err
			}
		} else {
			filter = map[string]interface{}{"no_query": 1}
		}
		mergedFilter = filter
	}
	mFilter := make(map[string]interface{})
	if req.Condition != "" {
		condition, errKey, err := querybuilder.ParseRuleFromBytes([]byte(req.Condition))
		if err != nil {
			logger.Error(errKey)
			return nil, err
		}

		mFilter, _, err = condition.ToMgo()
		if err != nil {
			return nil, err
		}
		//mapstruct.MapAssign(filter, mFilter)
		mergedFilter = mFilter
	}
	if req.Condition != "" && req.BindResGroupId != "" {
		mergedFilter = map[string]interface{}{"$and": []interface{}{filter, mFilter}}
	}

	resp := &cloudman.FindResResp{}

	switch req.ResourceType {
	case "host":
		res, total, err := models.HostResourceModel.Query(ctx, &schema.HostResourceQueryParams{
			PaginationParam: schema.PaginationParam{Page: req.Page, Size: req.Size},
			OrderParams:     schema.OrderParams{Ordering: req.Ordering},
		}, mergedFilter)
		if err != nil {
			return nil, err
		}
		resp.Total = uint32(total)
		host, err := manyEntityToPbHost(ctx, res)
		if err != nil {
			return nil, err
		}
		resp.HostList = host
	case "mysql":
		res, total, err := models.MysqlClusterResourceModel.Query(ctx, &schema.MysqlClusterResourceQueryParams{
			PaginationParam: schema.PaginationParam{Page: req.Page, Size: req.Size},
			OrderParams:     schema.OrderParams{Ordering: req.Ordering},
		}, mergedFilter)
		if err != nil {
			return nil, err
		}
		resp.Total = uint32(total)
		host, err := manyEntityToPbMysql(ctx, res)
		if err != nil {
			return nil, err
		}
		resp.MysqlList = host
	case "redis":
		res, total, err := models.RedisResourceModel.Query(ctx, &schema.RedisResourceQueryParams{
			PaginationParam: schema.PaginationParam{Page: req.Page, Size: req.Size},
			OrderParams:     schema.OrderParams{Ordering: req.Ordering},
		}, mergedFilter)
		if err != nil {
			return nil, err
		}
		resp.Total = uint32(total)
		host, err := manyEntityToPbRedis(ctx, res)
		if err != nil {
			return nil, err
		}
		resp.RedisList = host
	}

	return resp, nil

}

// Info 获取分组策略详情
func (r ResGroupPolicy) Info(ctx context.Context, req *cloudman.GroupPolicyInfoReq) (*cloudman.ResGroupPolicyInfo, error) {
	if req.Id == "" || req.ResourceType == "" {
		return nil, fmt.Errorf("'group id' and 'resource type' not valid")
	}

	var resp cloudman.ResGroupPolicyInfo
	info, err := models.ResourcePolicy.GetInfo(ctx, req.Id, req.ResourceType)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return &resp, nil
		}
		return nil, err
	}

	return &cloudman.ResGroupPolicyInfo{
		BindGroupName: info.ResGroupInfo.Name,
		BindGroupDesc: info.ResGroupInfo.Desc,
		BindGroupId:   req.Id,
		ResourceType:  req.ResourceType,
		Condition:     info.Condition,
	}, nil
}

// // Create 创建分组策略
// func (r ResGroupPolicy) Create(ctx context.Context, req *cloudman.ResGroupPolicyCreateReq) (*cloudman.Result, error) {
// 	data := &entity.ResGroupPolicy{
// 		Name:             req.Name,
// 		Desc:             req.Desc,
// 		BindResGroupIDS:  req.BindGroupIds,
// 		ConditionRelated: req.GroupConditions.Related,
// 	}
//
// 	var li []entity.ResGroupCond
// 	for _, v := range req.GroupConditions.List {
// 		li = append(li, entity.ResGroupCond{
// 			Key:     v.Key,
// 			Related: v.Related,
// 			Value:   v.Value,
// 		})
// 	}
// 	data.Condition = li
// 	err := validator.Validate.StructCtx(ctx, data)
// 	if err != nil {
// 		return nil, err
// 	}
//
// 	data.CreateUser = GetUsername(ctx)
//
// 	err = models.ResourcePolicy.Create(ctx, data)
// 	if err != nil {
// 		return nil, err
// 	}
//
// 	return &cloudman.Result{Message: "success"}, nil
// }

// Info 分组策略详情
// func (r ResGroupPolicy) Info(ctx context.Context, id *cloudman.ObjectID) (*cloudman.ResGroupPolicyInfo, error) {
// 	data, err := models.ResourcePolicy.Get(ctx, id.Id)
// 	if err != nil {
// 		return nil, err
// 	}
//
// 	res := &cloudman.ResGroupPolicyInfo{
// 		Id:           data.ID.Hex(),
// 		Name:         data.Name,
// 		Desc:         data.Desc,
// 		BindGroupIds: data.BindResGroupIDS,
// 		GroupConditions: &cloudman.GroupPolicy{
// 			Related: data.ConditionRelated,
// 		},
// 	}
//
// 	var list []*cloudman.GroupPolicyInfo
// 	for _, v := range data.Condition {
// 		list = append(list, &cloudman.GroupPolicyInfo{
// 			Key:     v.Key,
// 			Value:   v.Value,
// 			Related: v.Related,
// 		})
// 	}
// 	res.GroupConditions.List = list
//
// 	var infoList []*cloudman.GroupInfo
// 	for _, v := range data.ResGroupInfo {
// 		infoList = append(infoList, &cloudman.GroupInfo{
// 			Id:   v.ID.Hex(),
// 			Name: v.Name,
// 			Desc: v.Desc,
// 		})
// 	}
// 	res.GroupInfo = infoList
// 	return res, nil
// }

// Update 更新分组策略，不存在则创建
func (r ResGroupPolicy) Update(ctx context.Context, req *cloudman.ResGroupPolicyCreateReq) (*cloudman.Result, error) {
	data := &entity.ResGroupPolicy{
		BindResGroupID: req.BindGroupId,
		ResourceType:   req.ResourceType,
		Condition:      req.Condition,
	}

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}

	if data.Condition != "" {
		// 检查查询条件是否合法
		_, _, err = data.ToFilter()
		if err != nil {
			return nil, err
		}
	}

	err = models.ResourcePolicy.CreateOrUpdate(ctx, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Del 删除分组策略
func (r ResGroupPolicy) Del(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	var data entity.ResGroupPolicy
	data.IsDelete = 1
	data.DeletedTime = time.Now().Unix()

	err := models.ResourcePolicy.Update(ctx, id.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, err
}

// ListPolicy 分组策略列表
// func (r ResGroupPolicy) ListPolicy(ctx context.Context, req *cloudman.ResGroupPolicyQueryReq) (*cloudman.ResGroupPolicyListResp, error) {
// 	list, total, err := models.ResourcePolicy.Query(ctx, &schema.ResGroupPolicyQueryParams{
// 		PaginationParam: schema.PaginationParam{
// 			Page: req.Page,
// 			Size: req.Size,
// 		},
// 		ResGroupPolicyColumnParam: schema.ResGroupPolicyColumnParam{
// 			Name: req.Name,
// 		},
// 		SearchParams: schema.SearchParams{
// 			Keywords: req.Keywords,
// 		},
// 		OrderParams: schema.OrderParams{
// 			Ordering: req.Ordering,
// 		},
// 	})
//
// 	if err != nil {
// 		return nil, err
// 	}
//
// 	var li []*cloudman.ResGroupPolicyShort
// 	for _, v := range list {
// 		li = append(li, &cloudman.ResGroupPolicyShort{
// 			Id:   v.ID.Hex(),
// 			Name: v.Name,
// 			Desc: v.Desc,
// 		})
// 	}
//
// 	return &cloudman.ResGroupPolicyListResp{Total: uint32(total), List: li}, err
// }
