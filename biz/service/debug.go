package service

import (
	"context"

	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
)

// Debug Debug-handler
type Debug struct {
}

// FixResTagBind 修复标签绑定关系
func (s Debug) FixResTagBind(ctx context.Context, empty *cloudman.Empty) (*cloudman.Result, error) {
	ctx = context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	res, err := models.HostResourceModel.FindMany(ctx, map[string]interface{}{})
	if err != nil {
		return nil, err
	}

	for _, host := range res {
		if len(host.Tags.Tag) != 0 {
			for _, tag := range host.Tags.Tag {
				resTags, err := models.ResourceTags.GetByKeyValue(ctx, tag.TagKey, tag.TagValue)
				if err == mongo.ErrNoDocuments {
					// add tag
					err = models.ResourceTags.Create(ctx, &entity.ResTags{Key: tag.TagKey, Value: tag.TagValue, Type: "user"})
					if err != nil {
						return nil, err
					}
					resTags, err = models.ResourceTags.GetByKeyValue(ctx, tag.TagKey, tag.TagValue)
					if err != nil {
						return nil, err
					}
				} else if err != nil {
					return nil, err
				}
				_, err = models.ResourceBindTags.FindByResAndTagID(ctx, host.ID, "host", resTags.ID)
				if err == mongo.ErrNoDocuments {
					logger.Infof("fix missing tag bind: %s %s %s\n", host.InstanceID, tag.TagKey, tag.TagValue)
					err = models.ResourceBindTags.BindByID(ctx, host.ID, "host", resTags.ID, "custom")
					if err != nil {
						return nil, err
					}
				} else if err != nil {
					return nil, err
				}
			}
		}
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Test -
func (s Debug) Test(ctx context.Context, empty *cloudman.Empty) (*cloudman.Result, error) {
	return &cloudman.Result{}, nil
}
