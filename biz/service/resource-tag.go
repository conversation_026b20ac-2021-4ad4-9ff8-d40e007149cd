package service

import (
	"context"
	"errors"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// Tags 标签管理
type Tags struct {
}

// List 获取资源标签
func (t Tags) List(ctx context.Context, req *cloudman.TagQueryReq) (*cloudman.TagListResp, error) {
	list, total, err := models.ResourceTags.Query(ctx, &schema.TagsQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		TagsColumnParam: schema.TagsColumnParam{
			Name: req.Name,
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var li []*cloudman.TagInfo
	for _, v := range list {
		li = append(li, &cloudman.TagInfo{
			Id:    v.ID.Hex(),
			Key:   v.Key,
			Desc:  v.Desc,
			Value: v.Value,
			Type:  v.Type,
			Kv:    strings.Join([]string{v.Key, v.Value}, "-"),
		})
	}

	return &cloudman.TagListResp{Total: uint32(total), List: li}, err
}

// ListSystem 获取所有系统标签
func (t Tags) ListSystem(ctx context.Context, req *cloudman.TagQueryReq) (*cloudman.TagListResp, error) {
	list, total, err := models.ResourceTags.Query(ctx, &schema.TagsQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		TagsColumnParam: schema.TagsColumnParam{
			Name: req.Name,
			Type: "system",
		},
		SearchParams: schema.SearchParams{
			Keywords: req.Keywords,
		},
		OrderParams: schema.OrderParams{
			Ordering: req.Ordering,
		},
	})

	if err != nil {
		return nil, err
	}

	var li []*cloudman.TagInfo
	for _, v := range list {
		li = append(li, &cloudman.TagInfo{
			Id:    v.ID.Hex(),
			Key:   v.Key,
			Desc:  v.Desc,
			Value: v.Value,
			Type:  v.Type,
			Kv:    strings.Join([]string{v.Key, v.Value}, "-"),
		})
	}

	return &cloudman.TagListResp{Total: uint32(total), List: li}, err
}

// Create 创建资源标签
func (t Tags) Create(ctx context.Context, req *cloudman.TagCreateReq) (*cloudman.Result, error) {
	data := &entity.ResTags{
		Key:   req.Key,
		Value: req.Value,
		Desc:  req.Desc,
		Type:  req.Type,
	}

	if !permission.IsAdminFromCtx(ctx) && data.Type == "system" {
		return nil, errors.New("管理员才能修改系统标签")
	}

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}

	data.CreateUser = permission.GetUsername(ctx)

	err = models.ResourceTags.Create(ctx, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Info 获取资源标签详情
func (t Tags) Info(ctx context.Context, id *cloudman.ObjectID) (*cloudman.TagInfo, error) {
	data, err := models.ResourceTags.Get(ctx, id.Id)
	if err != nil {
		return nil, err
	}

	return &cloudman.TagInfo{
		Id:    data.ID.Hex(),
		Key:   data.Key,
		Value: data.Value,
		Desc:  data.Desc,
		Type:  data.Type,
		Kv:    strings.Join([]string{data.Key, data.Value}, "-"),
	}, nil
}

// Update 更新资源标签详情
func (t Tags) Update(ctx context.Context, req *cloudman.TagCreateReq) (*cloudman.Result, error) {
	data := &entity.ResTags{
		Key:   req.Key,
		Value: req.Value,
		Desc:  req.Desc,
		Type:  req.Type,
	}

	err := validator.Validate.StructCtx(ctx, data)
	if err != nil {
		return nil, err
	}

	data.UpdateUser = permission.GetUsername(ctx)

	//Get Tag's origin value
	resTagsEntity, err := models.ResourceTags.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if !permission.IsAdminFromCtx(ctx) {
		if resTagsEntity.Type == "system" {
			return nil, errors.New("管理员才能修改系统标签")
		} else if req.Type != resTagsEntity.Type {
			return nil, errors.New("管理员才能修改Tag状态")
		}
	}

	//Update host tags
	if err := models.HostResourceModel.UpdateWithTagOption(ctx, resTagsEntity.Key, resTagsEntity.Value, data.Value); err != nil {
		return nil, err
	}

	err = models.ResourceTags.Update(ctx, req.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, nil
}

// Del 删除资源标签
func (t Tags) Del(ctx context.Context, id *cloudman.ObjectID) (*cloudman.Result, error) {
	var data entity.ResTags
	data.IsDelete = 1
	data.DeletedTime = time.Now().Unix()

	//check if this tag is used by host
	var resTagsEntity *entity.ResTags
	resTagsEntity, err := models.ResourceTags.Get(ctx, id.Id)
	if err == mongo.ErrNoDocuments {
		return nil, err
	}

	ctx = context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	_, err = models.HostResourceModel.FindOne(ctx, map[string]interface{}{
		"Tags.Tag": map[string]interface{}{
			"$elemMatch": map[string]string{
				"TagKey":   resTagsEntity.Key,
				"TagValue": resTagsEntity.Value,
			},
		},
	})
	if err == nil {
		return nil, errors.New("删除失败，有正在使用或未释放的该标签实例")
	} else if err != mongo.ErrNoDocuments {
		return nil, err
	}

	err = models.ResourceTags.Update(ctx, id.Id, data)
	if err != nil {
		return nil, err
	}

	return &cloudman.Result{Message: "success"}, err
}
