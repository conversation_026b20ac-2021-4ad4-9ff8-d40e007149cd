package service

import (
	"context"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
)

// Permission 首页
type Permission struct{}

// CheckInstancePermission 检查用户的实例权限
func (h Permission) CheckInstancePermission(ctx context.Context, req *cloudman.CheckInstancePermissionReq) (*cloudman.CheckInstancePermissionResp, error) {
	if len(req.AgentIDs) == 0 {
		return &cloudman.CheckInstancePermissionResp{AgentIDs: []string{}}, nil
	}

	adminCtx := context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	hosts, err := models.HostResourceModel.FindMany(adminCtx, map[string]interface{}{"agent_id": map[string]interface{}{"$in": req.AgentIDs}})
	if err != nil {
		return nil, err
	}
	var availableAgentID []string
	for _, host := range hosts {
		if models.CheckIspAndTagsPerm(ctx, req.Username, req.InstanceType, "admin", host.IspID, models.EntityToCheckTags(host.Tags.Tag)) {
			availableAgentID = append(availableAgentID, host.AgentID)
		}
	}
	return &cloudman.CheckInstancePermissionResp{AgentIDs: availableAgentID}, nil
}
