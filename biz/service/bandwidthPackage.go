package service

import (
	"context"
	"github.com/alibabacloud-go/tea/tea"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
)

type BandwidthPackage struct {
}

func (h BandwidthPackage) DescribeBandwidthPackageCbwp(ctx context.Context, req *cloudman.DescribeBandwidthPackageCbwpReq) (*cloudman.DescribeBandwidthPackageCbwpRes, error) {
	r, err := models.RegionModel.Get(ctx, req.RegionId) // TODO: 由前端传入
	if err != nil {
		return nil, err
	}

	client, err := alicloud.CreateAliEcsClient(r.RegionID, req.AccountId)
	if err != nil {
		return nil, err
	}

	packageList, err := client.GetAliyunBandwidthPackageList(ctx)
	if err != nil {
		return nil, err
	}
	bandwidthPackageCbwpRes := make([]*cloudman.BandwidthPackageCbwp, 0)
	for _, p := range packageList {
		c := int32(len(p.PublicIpAddresses.PublicIpAddresse))
		bandwidthPackageCbwpRes = append(bandwidthPackageCbwpRes, &cloudman.BandwidthPackageCbwp{
			InstanceId: tea.StringValue(p.BandwidthPackageId),
			Bandwidth:  tea.StringValue(p.Bandwidth),
			UsedCount:  tea.Int32Value(&c),
			Name:       tea.StringValue(p.Name),
		})
	}

	return &cloudman.DescribeBandwidthPackageCbwpRes{
		Total: int32(len(packageList)),
		List:  bandwidthPackageCbwpRes,
	}, nil
}
