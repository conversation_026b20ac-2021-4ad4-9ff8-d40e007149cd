package service

import (
	"context"
	"encoding/json"
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/hotwheel"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/render"
)

type InitResource struct {
}

// GetJumpserver 获取堡垒机
func (r InitResource) GetJumpserver(ctx context.Context, request *cloudman.GetJumpserverReq) (*cloudman.GetJumpserverResp, error) {
	region, err := models.RegionModel.Get(ctx, request.RegionId)
	if err != nil {
		return nil, fmt.Errorf("获取region失败, regionID: %s, err: %v", request.RegionId, err)
	}

	initCfg, err := models.InitCfgModel.GetByRegionID(ctx, region.RegionID)
	if err != nil {
		return nil, fmt.Errorf("获取InitCfg失败, region.RegionID: %s, err: %v", region.RegionID, err)
	}

	hosts := make([]string, 0)
	for _, jumpserver := range initCfg.Jumpservers {
		hosts = append(hosts, jumpserver.Host)
	}

	return &cloudman.GetJumpserverResp{
		Hosts: hosts,
	}, nil
}

func (r InitResource) GetPipeline(ctx context.Context, request *cloudman.GetPipelineReq) (*cloudman.GetPipelineResp, error) {
	region, err := models.RegionModel.Get(ctx, request.RegionId)
	if err != nil {
		return nil, err
	}

	initCfg, err := models.InitCfgModel.GetByRegionID(ctx, region.RegionID)
	if err != nil {
		return nil, err
	}

	hotwheelClient := hotwheel.NewClient(initCfg.HotwheelOption.URI, initCfg.HotwheelOption.Token)

	data, err := hotwheelClient.GetPipelines(ctx, initCfg.HotwheelOption.CateID)
	if err != nil {
		return nil, err
	}

	pipelines := make([]*cloudman.PipelineData, 0)
	for _, p := range data {
		pipelines = append(pipelines, &cloudman.PipelineData{
			Id:   p.ID,
			Name: p.Name,
		})
	}

	return &cloudman.GetPipelineResp{
		Pipelines: pipelines,
	}, nil
}

func (r InitResource) PreviewParam(ctx context.Context, request *cloudman.PreviewParamReq) (*cloudman.PreviewParamResp, error) {
	form := make(map[string]interface{})
	err := json.Unmarshal([]byte(request.FormJson), &form)
	if err != nil {
		return nil, err
	}

	data := map[string]interface{}{
		"form":          form,
		"instance_id":   "虚拟实例ID",
		"instance_name": "虚拟实例名称",
	}
	inputJson, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	res := make([]*cloudman.PipelineParamPreviewData, 0)
	for _, p := range request.Params {
		pd := &cloudman.PipelineParamPreviewData{
			Key:   p.Key,
			Value: p.Value,
		}

		rValue, err := render.GetRenderedParam(string(inputJson), p.Value, p.EnableGzipBase64)
		if err != nil {
			return nil, err
		}
		pd.RenderedValue = rValue
		res = append(res, pd)
	}

	for _, p := range request.BuiltinParams {
		pd := &cloudman.PipelineParamPreviewData{
			Key:   p,
			Value: p,
		}
		pd.RenderedValue, err = render.GetBuiltinParams(string(inputJson), p)
		if err != nil {
			return nil, err
		}
		res = append(res, pd)
	}

	return &cloudman.PreviewParamResp{
		PreviewParams: res,
	}, nil
}
