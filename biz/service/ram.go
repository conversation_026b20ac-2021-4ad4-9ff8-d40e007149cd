package service

import (
	"context"
	"fmt"
	"time"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	"platgit.mihoyo.com/jql-ops/op-cloudman/synctask"
)

type RAM struct {
}

func (r RAM) DescribeRAMPolicy(ctx context.Context, req *cloudman.DescribeRAMPolicyReq) (*cloudman.DescribeRAMPolicyResp, error) {
	resp, total, err := models.PolicyModel.Query(ctx, &schema.PolicyQueryParam{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		PolicyColumnParam: schema.PolicyColumnParam{
			IspID:       req.Isp,
			Name:        req.Name,
			Desc:        req.Desc,
			NeedCleanup: req.NeedCleanup,
		},
		OrderParams: schema.OrderParams{
			Ordering: []string{
				"-UpdateDate",
				"-CreateDate",
			},
		},
	})

	if err != nil {
		return nil, err
	}

	policies, err := models.PolicyToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeRAMPolicyResp{
		List:  policies,
		Total: int32(total),
	}, nil
}

func (r RAM) ModifyRAMPolicySourceIPGroup(ctx context.Context, req *cloudman.ModifyRAMPolicySourceIPGroupReq) (*cloudman.Result, error) {
	e, err := models.PolicyModel.Get(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	e.RelatedIpGroups = req.IpGroupIds
	e.UpdateUser = permission.GetUsername(ctx)
	_, err = models.PolicyModel.CreateOrUpdate(ctx, e, false)
	if err != nil {
		return nil, err
	}
	return &cloudman.Result{
		Message: "更新成功",
	}, nil
}

func (r RAM) PreviewRAMPolicyDeploy(ctx context.Context, req *cloudman.PreviewRAMPolicyDeployReq) (*cloudman.PreviewRAMPolicyDeployResp, error) {
	if len(req.Ids) == 0 && !req.IsAll {
		return nil, fmt.Errorf("发布id不能为空")
	}

	diffs, err := models.PolicyModel.GetPolicyDiff(ctx, req.Ids, req.IsAll)
	if err != nil {
		return nil, err
	}

	resp := &cloudman.PreviewRAMPolicyDeployResp{
		Results: []*cloudman.PreviewRamPolicyDeployRes{},
	}

	for _, diff := range diffs {
		deployRes := &cloudman.PreviewRamPolicyDeployRes{
			Id:                diff.OrignalPolicy.ID.Hex(),
			Name:              diff.OrignalPolicy.PolicyName,
			Description:       diff.OrignalPolicy.Description,
			AttachUsers:       []*cloudman.AttachUser{},
			AddIps:            diff.AddIps,
			RemoveIps:         diff.RemoveIps,
			OldPolicyDocument: diff.OrignalPolicy.DefaultPolicyDocument,
			NewPolicyDocument: diff.NewDocument,
		}
		for _, u := range diff.OrignalPolicy.AttachUsers {
			deployRes.AttachUsers = append(deployRes.AttachUsers, &cloudman.AttachUser{
				DisplayName: u.DisplayName,
				UserId:      u.UserId,
				UserName:    u.UserName,
				AttachDate:  u.AttachDate,
			})
		}
		resp.Results = append(resp.Results, deployRes)
	}
	return resp, nil
}

func (r RAM) RAMPolicyDeploy(ctx context.Context, req *cloudman.RAMPolicyDeployReq) (*cloudman.Result, error) {
	diffs, err := models.PolicyModel.GetPolicyDiff(ctx, req.Ids, req.IsAll)
	if err != nil {
		return nil, err
	}
	if len(diffs) == 0 {
		return nil, fmt.Errorf("没有需要发布的权限策略组")
	}

	// 先创建发布订单
	pr := &common.InitProvider{
		RegionID: diffs[0].OrignalPolicy.RegionID,
		IspType:  diffs[0].OrignalPolicy.IspType,
		IspID:    diffs[0].OrignalPolicy.IspID,
		Logger:   logrus.New(),
	}
	order, err := createOrder(ctx, ResourceForm{
		Action:       "deploy",
		IspID:        diffs[0].OrignalPolicy.IspID,
		InstanceType: "policy",
		RegionID:     diffs[0].OrignalPolicy.RegionID,
	}, pr, "")
	if err != nil {
		return nil, err
	}

	pr.Logger.Infof("发布任务创建成功...")
	// 需要用协程来异步完成发布动作
	go doPolicyDeploy(permission.GetUsername(ctx), diffs, order, pr)

	return &cloudman.Result{
		Message: "发布任务创建成功",
	}, nil
}

func doPolicyDeploy(username string, diffs []*models.PolicyDiff, order *entity.ResOrder, pr *common.InitProvider) (err error) {
	pr.Logger.Infof("开始发布任务...")
	// gCtx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
	// 	"x-rpc-" + "username": username,
	// }))
	gCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
	err = models.ResourceOrder.Update(gCtx, order.ID.Hex(), map[string]interface{}{"start_time": time.Now().Unix(), "status": constant.TaskRunning})
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			_ = models.ResourceOrder.Update(gCtx, order.ID.Hex(), map[string]interface{}{"over_time": time.Now().Unix(), "status": constant.TaskErr})
			return
		}
		_ = models.ResourceOrder.Update(gCtx, order.ID.Hex(), map[string]interface{}{"over_time": time.Now().Unix(), "status": constant.TaskSuccess})
	}()
	if err != nil {
		return err
	}
	for _, p := range diffs {
		client, err := alicloud.NewAliRamClient(p.OrignalPolicy.RegionID, p.OrignalPolicy.IspID)
		if err != nil {
			return err
		}
		pr.Logger.Infof("开始发布权限策略组%s", p.OrignalPolicy.PolicyName)
		err = client.CreatePolicyVersion(gCtx, p.OrignalPolicy.PolicyName, p.NewDocument, true)
		if err != nil {
			pr.Logger.Errorf("发布权限策略组%s失败, 失败信息: %s", p.OrignalPolicy.PolicyName, err.Error())
			return err
		}
		pr.Logger.Infof("发布权限策略组%s成功", p.OrignalPolicy.PolicyName)
	}

	// 同步最新的权限策略
	aliSyncer := &synctask.AliyunSyncer{}
	regionID := diffs[0].OrignalPolicy.RegionID
	ispID := diffs[0].OrignalPolicy.IspID
	err = aliSyncer.SyncPolicy(gCtx, logrus.StandardLogger(), synctask.SyncOption{RegionID: regionID, IspID: ispID})
	if err != nil {
		pr.Logger.Errorf("同步权限策略组失败")
		return err
	}
	pr.Logger.Infof("同步权限策略组成功")
	return nil
}

// SyncRAMPolicy 同步指定账号的权限策略组
func (r RAM) SyncRAMPolicy(ctx context.Context, req *cloudman.SyncRamPolicyReq) (*cloudman.Result, error) {
	tasks, err := models.SyncTaskModel.FindResourceTask(ctx, synctask.SyncPolicy, req.IspId, req.RegionId)
	if err != nil {
		return nil, err
	}
	if len(tasks) == 0 {
		return nil, fmt.Errorf("请先创建同步权限策略组任务")
	}

	for _, task := range tasks {
		_, err = Inner{}.RunTaskAPI(ctx, &cloudman.RunTaskReq{
			Oid: task.ID.Hex(),
		})
		if err != nil {
			return nil, err
		}
	}

	return &cloudman.Result{
		Message: "同步任务创建成功",
	}, nil
}

func (r RAM) SyncNewRAMPolicy(ctx context.Context, req *cloudman.SyncNewRAMPolicyReq) (*cloudman.Result, error) {
	aliSyncer := &synctask.AliyunSyncer{}
	if err := aliSyncer.SyncPolicyByNames(ctx, logrus.StandardLogger(), synctask.SyncOption{RegionID: req.RegionId, IspID: req.IspId}, req.Names...); err != nil {
		return nil, err
	}

	return &cloudman.Result{
		Message: "同步中,请稍后刷新页面查看",
	}, nil
}

func (r RAM) GetCloudRamPolicy(ctx context.Context, req *cloudman.GetCloudRamPolicyReq) (*cloudman.GetCloudRamPolicyResp, error) {
	client, err := alicloud.NewAliRamClient(req.RegionId, req.IspId)
	if err != nil {
		return nil, err
	}
	policies, err := client.ListPolicies(ctx, "Custom")
	if err != nil {
		return nil, err
	}

	list := make([]*cloudman.GetCloudRamPolicyRes, 0)
	for _, p := range policies {
		list = append(list, &cloudman.GetCloudRamPolicyRes{
			Name:        tea.StringValue(p.PolicyName),
			Description: tea.StringValue(p.Description),
		})
	}

	return &cloudman.GetCloudRamPolicyResp{
		List: list,
	}, nil
}
