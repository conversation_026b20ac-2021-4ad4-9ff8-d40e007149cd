package service

import (
	"fmt"

	"github.com/alibabacloud-go/tea/tea"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/ec2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// Eip 安全组对象
type Eip struct {
}

// Describe 获取弹性公网IP列表
func (h Eip) Describe(ctx context.Context, req *cloudman.DescribeEipReq) (*cloudman.DescribeEipRes, error) {
	resp, total, err := models.EipModel.Query(ctx, &schema.EipQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: req.Page,
			Size: req.Size,
		},
		EipColumnParam: schema.EipColumnParam{
			RegionID:  req.RegionId,
			IspID:     req.Isp,
			IPAddress: req.IpAddress,
		},
		SearchColumnField: schema.SearchColumnField{
			SearchKey:   req.SearchKey,
			SearchValue: req.SearchValue,
		},
		OrderParams: schema.OrderParams{},
	})
	if err != nil {
		return nil, err
	}

	list, err := models.EipModelToPb(ctx, resp)
	if err != nil {
		return nil, err
	}

	return &cloudman.DescribeEipRes{List: list, Total: int32(total)}, err
}

func (h Eip) DescribeSegment(ctx context.Context, req *cloudman.DescribeEipSegmentReq) (*cloudman.DescribeEipSegmentRes, error) {
	region, err := models.RegionModel.Get(ctx, req.RegionId)
	if err != nil {
		return nil, err
	}

	aliVpcClient, err := alicloud.NewAliVpcClient(region.RegionID, req.Isp)
	if err != nil {
		return nil, err
	}

	resp, err := aliVpcClient.DescribeEipSegment(ctx)
	if err != nil {
		return nil, err
	}
	segments := make([]*cloudman.EipSegment, 0)
	for _, res := range resp.EipSegments.EipSegment {
		availableCount, err := aliVpcClient.GetAvailEipCountBySegID(ctx, tea.StringValue(res.InstanceId))
		if err != nil {
			return nil, err
		}

		segments = append(segments, &cloudman.EipSegment{
			Status:     tea.StringValue(res.Status),
			Segment:    tea.StringValue(res.Segment),
			IpCount:    fmt.Sprintf("%d", availableCount),
			InstanceId: tea.StringValue(res.InstanceId),
		})
	}

	return &cloudman.DescribeEipSegmentRes{List: segments, Total: tea.Int32Value(resp.TotalCount)}, err
}

func (h Eip) DescribeIpams(ctx context.Context, req *cloudman.DescribeIpamReq) (*cloudman.DescribeIpamRes, error) {
	region, err := models.RegionModel.Get(ctx, req.RegionId)
	if err != nil {
		return nil, err
	}

	ipams, err := ec2.ListIpamPools(ctx, region.RegionID, req.Isp)
	if err != nil {
		return nil, err
	}

	data := make([]*cloudman.Ipam, 0)
	for _, ipam := range ipams {
		data = append(data, &cloudman.Ipam{
			Id:              ipam.IpamPoolId,
			State:           ipam.State,
			Cidrs:           ipam.Cidrs,
			TotalCount:      int32(ipam.TotalCount),
			AssignedPercent: float32(ipam.AssignedPercent),
		})
	}

	return &cloudman.DescribeIpamRes{List: data, Total: int32(len(data))}, nil
}
