package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
)

// ServiceTree ...
type ServiceTree struct {
}

// GetTree ...
func (s ServiceTree) GetTree(ctx context.Context, req *cloudman.Raw) (*cloudman.GetTreeResponse, error) {
	svConfig := cfg.GetServiceTreeConfig()
	if svConfig.Disable {
		return &cloudman.GetTreeResponse{}, nil
	}
	resBody, err := serviceTreeProxy(ctx, "POST", "service-tree/api/v1/get_tree", req.RawBody)
	if err != nil {
		return nil, err
	}
	res := struct {
		Data *cloudman.GetTreeResponse `json:"data"`
	}{}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		return nil, err
	}
	return res.Data, nil
}

// GetEntity ...
func (s ServiceTree) GetEntity(ctx context.Context, req *cloudman.Raw) (*cloudman.GetEntityResponse, error) {
	svConfig := cfg.GetServiceTreeConfig()
	if svConfig.Disable {
		return &cloudman.GetEntityResponse{}, nil
	}
	resBody, err := serviceTreeProxy(ctx, "POST", "service-tree/api/v1/get_entity", req.RawBody)
	if err != nil {
		return nil, err
	}
	res := struct {
		Data *cloudman.GetEntityResponse `json:"data"`
	}{}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		return nil, err
	}
	return res.Data, nil
}

// NodeEntityCount ...
func (s ServiceTree) NodeEntityCount(ctx context.Context, req *cloudman.Raw) (*cloudman.NodeEntityCountResponse, error) {
	svConfig := cfg.GetServiceTreeConfig()
	if svConfig.Disable {
		return &cloudman.NodeEntityCountResponse{}, nil
	}
	resBody, err := serviceTreeProxy(ctx, "POST", "service-tree/web/node/entity_count", req.RawBody)
	if err != nil {
		return nil, err
	}
	res := struct {
		Data *cloudman.NodeEntityCountResponse `json:"data"`
	}{}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		return nil, err
	}
	return res.Data, nil
}

// AddNode ...
func (s ServiceTree) AddNode(ctx context.Context, req *cloudman.AddNodeRequest) (*cloudman.AddNodeResponse, error) {
	conf := cfg.GetServiceTreeConfig()
	if conf.Disable {
		return &cloudman.AddNodeResponse{}, nil
	}
	cli, err := cfg.GetIAMCli(conf.IAMEnv)
	if err != nil {
		return nil, err
	}
	token, err := cli.CreateToken(ctx, "privilege", permission.GetUsername(ctx))
	if err != nil {
		return nil, err
	}
	reqByte, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	resBody, code, err := utils.HTTPPost(ctx, conf.ServiceTreePrefix+"service-tree/web/node/add", http.Header{"Authorization": []string{"Bearer " + token.Token}}, reqByte, time.Second*10)
	if err != nil {
		return nil, err
	}
	if code != 200 {
		return nil, errors.New(string(resBody))
	}
	res := struct {
		RetCode int32  `json:"retcode"`
		Message string `json:"message"`
	}{}
	err = json.Unmarshal(resBody, &res)
	if err != nil {
		return nil, err
	}
	if res.RetCode != 0 {
		return nil, fmt.Errorf("addnode error: %s", res.Message)
	}
	return &cloudman.AddNodeResponse{}, nil
}

// Verify ...
func (s ServiceTree) Verify(ctx context.Context, req *cloudman.VerifyRequest) (*cloudman.VerifyResponse, error) {
	conf := cfg.GetServiceTreeConfig()
	if conf.Disable {
		return &cloudman.VerifyResponse{}, nil
	}
	return models.Verify(ctx, req)
}

func serviceTreeProxy(ctx context.Context, method string, url string, rawBody []byte) ([]byte, error) {
	conf := cfg.GetServiceTreeConfig()
	cli, err := cfg.GetIAMCli(conf.IAMEnv)
	if err != nil {
		return nil, err
	}
	token, err := cli.CreateToken(ctx, "privilege", permission.GetUsername(ctx))
	if err != nil {
		return nil, err
	}
	var resBody []byte
	code := 0
	if method == "POST" {
		resBody, code, err = utils.HTTPPost(ctx, conf.ServiceTreePrefix+url, http.Header{"Authorization": []string{"Bearer " + token.Token}}, rawBody, time.Second*10)
	} else if method == "GET" {
		resBody, code, err = utils.HTTPGet(ctx, conf.ServiceTreePrefix+url, http.Header{"Authorization": []string{"Bearer " + token.Token}}, map[string]string{}, time.Second*10)
	}
	if err != nil {
		return nil, err
	}
	if code != 200 {
		return nil, errors.New(string(resBody))
	}
	return resBody, nil
}
