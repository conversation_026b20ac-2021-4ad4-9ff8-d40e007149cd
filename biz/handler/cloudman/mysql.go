// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// MysqlOption .
// @router /api/v2/resource/mysql/option [GET]
func MysqlOption(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.Option(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlDescribeCluster .
// @router /api/v2/resource/mysql [POST]
func MysqlDescribeCluster(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MysqlClusterReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.DescribeCluster(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlLockResource .
// @router /api/v2/resource/mysql-lock [POST]
func MysqlLockResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Ids
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.LockResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlUnlockResource .
// @router /api/v2/resource/mysql-lock [DELETE]
func MysqlUnlockResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Ids
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.UnlockResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlExportXlsx .
// @router /api/v2/resource/mysql-export [POST]
func MysqlExportXlsx(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ExportXlsxReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, filename, err := service.MysqlRes{}.ExportXlsx(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(consts.StatusOK, "application/octet-stream", resp.File)
}

// MysqlImportXlsx .
// @router /api/v2/resource/mysql-import [POST]
func MysqlImportXlsx(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ImportXlsxReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.ImportXlsx(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlClusterInfo .
// @router /api/v2/resource/mysql-info/:id [GET]
func MysqlClusterInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.ClusterInfo(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetDatabases .
// @router /api/v2/resource/mysql-info-database/:id [GET]
func MysqlGetDatabases(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetDatabases(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetAccounts .
// @router /api/v2/resource/mysql-info-account/:id [GET]
func MysqlGetAccounts(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetAccounts(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetWhitelists .
// @router /api/v2/resource/mysql-info-whitelist/:id [GET]
func MysqlGetWhitelists(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetWhitelists(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetEndpoints .
// @router /api/v2/resource/mysql-info-endpoint/:id [GET]
func MysqlGetEndpoints(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetEndpoints(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetDBTypes .
// @router /api/v2/resource/mysql-ref/db_type/:rid [GET]
func MysqlGetDBTypes(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DBTypeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetDBTypes(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlDescribeAvailableZone .
// @router /api/v2/resource/mysql-ref/available_zone/:rid [GET]
func MysqlDescribeAvailableZone(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DBZoneReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.DescribeAvailableZone(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlDescribeDBClasses .
// @router /api/v2/resource/mysql-ref/classes/:rid [GET]
func MysqlDescribeDBClasses(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DBClassesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.DescribeDBClasses(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetSecurityGroup .
// @router /api/v2/resource/mysql-network/security-group/:rid [GET]
func MysqlGetSecurityGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SecurityGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetSecurityGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetNetwork .
// @router /api/v2/resource/mysql-network/network/:rid [GET]
func MysqlGetNetwork(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.NetworkReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetNetwork(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlGetVSwitch .
// @router /api/v2/resource/mysql-network/vswitch/:rid [GET]
func MysqlGetVSwitch(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.VSwitchReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.GetVSwitch(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlSyncMysqlInstances .
// @router /api/v2/resource/mysql/sync [POST]
func MysqlSyncMysqlInstances(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SyncInstancesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.SyncMysqlInstances(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlDescribeIPWhiteList .
// @router /api/v2/resource/mysql-network/ip-whitelist/:rid [POST]
func MysqlDescribeIPWhiteList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.IPWhiteListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.DescribeIPWhiteList(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlUploadBackupSetToOSS .
// @router /api/v2/resource/mysql/backup/upload [POST]
func MysqlUploadBackupSetToOSS(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UploadBackupSetToOSSReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.UploadBackupSetToOSS(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// MysqlDescribeParamsGroups .
// @router /api/v2/resource/mysql/parameter-groups/:isp_id/:rid [POST]
func MysqlDescribeParamsGroups(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DBParamsGroupsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.MysqlRes{}.DescribeParamsGroups(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
