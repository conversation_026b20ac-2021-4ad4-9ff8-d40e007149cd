// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// CmdbQuery .
// @router /api/v2/cmdb/query [POST]
func CmdbQuery(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbQueryRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.Query(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbQueryAssociate .
// @router /api/v2/cmdb/query_associate [POST]
func CmdbQueryAssociate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbQueryAssociateRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.QueryAssociate(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbManualUpdate .
// @router /api/v2/cmdb/manual_update [POST]
func CmdbManualUpdate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbUpdateRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.ManualUpdate(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbDelete .
// @router /api/v2/cmdb/delete [POST]
func CmdbDelete(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbDeleteRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.Delete(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbPullInfo .
// @router /api/v2/cmdb/pull_info [POST]
func CmdbPullInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbPullInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.PullInfo(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbDiff .
// @router /api/v2/cmdb/diff [POST]
func CmdbDiff(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbDiffRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.Diff(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbDiffProcess .
// @router /api/v2/cmdb/process [POST]
func CmdbDiffProcess(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbDiffProcessRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.DiffProcess(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbDiffDelete .
// @router /api/v2/cmdb/diff/delete [POST]
func CmdbDiffDelete(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbDiffDeleteReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.DiffDelete(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CmdbDiffUpdate .
// @router /api/v2/cmdb/diff/update [POST]
func CmdbDiffUpdate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CmdbDiffDeleteReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Cmdb{}.DiffUpdate(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
