// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// ResTemplateList .
// @router /api/v2/resource/template [GET]
func ResTemplateList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ResTemQueryReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.List(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateCreate .
// @router /api/v2/resource/template [POST]
func ResTemplateCreate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ResTemCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.Create(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateInfo .
// @router /api/v2/resource/template/:id [GET]
func ResTemplateInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.Info(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateUpdate .
// @router /api/v2/resource/template/:id [PUT]
func ResTemplateUpdate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ResTemCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.Update(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateDel .
// @router /api/v2/resource/template/:id [DELETE]
func ResTemplateDel(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.Del(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateCreateResource .
// @router /api/v2/resource/create [POST]
func ResTemplateCreateResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateResReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.CreateResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateUpdateResource .
// @router /api/v2/resource/update [POST]
func ResTemplateUpdateResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ResCommonReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.UpdateResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateUpdateResources .
// @router /api/v2/resource/bulk-update [POST]
func ResTemplateUpdateResources(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateResReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.UpdateResources(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateRunOrder .
// @router /api/v2/resource/run/:id [POST]
func ResTemplateRunOrder(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RunOrderReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.RunOrder(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateRunOrderRetry .
// @router /api/v2/resource/run/:id/retry [POST]
func ResTemplateRunOrderRetry(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RunOrderRetryReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.RunOrderRetry(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateGetSalePrice .
// @router /api/v2/resource/order-price [POST]
func ResTemplateGetSalePrice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ResTemCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.GetSalePrice(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateCheckInstanceNameSeq .
// @router /api/v2/resource/check_instancename_seq [POST]
func ResTemplateCheckInstanceNameSeq(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CheckInstanceNameSeqReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.CheckInstanceNameSeq(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResTemplateGenInstanceNameWithRule .
// @router /api/v2/resource/gen_instancename_with_rule [POST]
func ResTemplateGenInstanceNameWithRule(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GenInstanceNameWithRuleReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResTemplate{}.GenInstanceNameWithRule(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
