// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// RedisOption .
// @router /api/v2/resource/redis/option [GET]
func RedisOption(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.Option(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisDescribe .
// @router /api/v2/resource/redis [POST]
func RedisDescribe(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RedisReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.Describe(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisLockResource .
// @router /api/v2/resource/redis-lock [POST]
func RedisLockResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Ids
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.LockResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisUnlockResource .
// @router /api/v2/resource/redis-lock [DELETE]
func RedisUnlockResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Ids
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.UnlockResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisExportXlsx .
// @router /api/v2/resource/redis-export [POST]
func RedisExportXlsx(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ExportXlsxReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, filename, err := service.RedisRes{}.ExportXlsx(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(consts.StatusOK, "application/octet-stream", resp.File)
}

// RedisImportXlsx .
// @router /api/v2/resource/redis-import [POST]
func RedisImportXlsx(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ImportXlsxReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.ImportXlsx(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisInfo .
// @router /api/v2/resource/redis-info/:id [GET]
func RedisInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.Info(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisGetAccounts .
// @router /api/v2/resource/redis-info-account/:id [GET]
func RedisGetAccounts(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.GetAccounts(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisGetWhitelists .
// @router /api/v2/resource/redis-info-whitelist/:id [GET]
func RedisGetWhitelists(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.GetWhitelists(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisGetCacheTypes .
// @router /api/v2/resource/redis-ref/type/:rid [GET]
func RedisGetCacheTypes(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DBTypeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.GetCacheTypes(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisDescribeAvailableZone .
// @router /api/v2/resource/redis-ref/available_zone/:rid [GET]
func RedisDescribeAvailableZone(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DBZoneReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.DescribeAvailableZone(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisDescribeCacheClasses .
// @router /api/v2/resource/redis-ref/classes/:rid [GET]
func RedisDescribeCacheClasses(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CacheClassReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.DescribeCacheClasses(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisGetSecurityGroup .
// @router /api/v2/resource/redis-network/security-group/:rid [GET]
func RedisGetSecurityGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SecurityGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.GetSecurityGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisGetNetwork .
// @router /api/v2/resource/redis-network/network/:rid [GET]
func RedisGetNetwork(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.NetworkReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.GetNetwork(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisGetVSwitch .
// @router /api/v2/resource/redis-network/vswitch/:rid [GET]
func RedisGetVSwitch(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.VSwitchReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.GetVSwitch(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisSyncRedisInstances .
// @router /api/v2/resource/redis/sync [POST]
func RedisSyncRedisInstances(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SyncInstancesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.SyncRedisInstances(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisGetBackupDetail .
// @router /api/v2/resource/redis/backup/:order_id [GET]
func RedisGetBackupDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.OrderID

	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.GetBackupDetail(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RedisDescribeParamsGroups .
// @router /api/v2/resource/redis-ref/parameter-groups/:isp_id/:rid [GET]
func RedisDescribeParamsGroups(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CacheParamsGroupsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RedisRes{}.DescribeParamsGroups(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
