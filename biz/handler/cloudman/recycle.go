// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// RecycleList .
// @router /api/v2/resource/recycle [GET]
func RecycleList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RecycleQueryReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.List(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleInfo .
// @router /api/v2/resource/recycle/:id [GET]
func RecycleInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.Info(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleRecover .
// @router /api/v2/resource/recycle/:id/recover [POST]
func RecycleRecover(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RecoverReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.Recover(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleDestroy .
// @router /api/v2/resource/recycle/:id/destroy [PUT]
func RecycleDestroy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RecycleReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.Destroy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleCreatePolicy .
// @router /api/v2/resource/recycle-policy [POST]
func RecycleCreatePolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RecyclePolicy
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.CreatePolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleGetPolicy .
// @router /api/v2/resource/recycle-policy/:id [GET]
func RecycleGetPolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.GetPolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleGetPolicyList .
// @router /api/v2/resource/recycle-policy [GET]
func RecycleGetPolicyList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RecyclePolicyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.GetPolicyList(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleChangePolicy .
// @router /api/v2/resource/recycle-policy/:id [PUT]
func RecycleChangePolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RecyclePolicy
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.ChangePolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleDelPolicy .
// @router /api/v2/resource/recycle-policy/:id [DELETE]
func RecycleDelPolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.DelPolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RecycleRunPolicy .
// @router /api/v2/resource/run-policy [POST]
func RecycleRunPolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Recycle{}.RunPolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
