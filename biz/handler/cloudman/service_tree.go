// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// ServiceTreeGetTree .
// @router /api/v1/servicetree/get_tree [POST]
func ServiceTreeGetTree(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Raw
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ServiceTree{}.GetTree(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ServiceTreeGetEntity .
// @router /api/v1/servicetree/get_entity [POST]
func ServiceTreeGetEntity(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Raw
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ServiceTree{}.GetEntity(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ServiceTreeNodeEntityCount .
// @router /api/v1/servicetree/node/entity_count [POST]
func ServiceTreeNodeEntityCount(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Raw
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ServiceTree{}.NodeEntityCount(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ServiceTreeAddNode .
// @router /api/v1/servicetree/add_node [POST]
func ServiceTreeAddNode(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.AddNodeRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ServiceTree{}.AddNode(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ServiceTreeVerify .
// @router /api/v1/servicetree/verify [POST]
func ServiceTreeVerify(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.VerifyRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ServiceTree{}.Verify(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
