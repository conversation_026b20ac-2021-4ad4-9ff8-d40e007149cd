// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// EipDescribe .
// @router /api/v2/resource/eip [POST]
func EipDescribe(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeEipReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Eip{}.Describe(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// EipDescribeSegment .
// @router /api/v2/resource/eip/segment [POST]
func EipDescribeSegment(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeEipSegmentReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Eip{}.DescribeSegment(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// EipDescribeIpam .
// @router /api/v2/resource/eip/ipam [POST]
func EipDescribeIpam(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeIpamReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := service.Eip{}.DescribeIpams(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
