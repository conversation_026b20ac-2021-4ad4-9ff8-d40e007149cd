// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// CloudAgentTaskList .
// @router /api/v1/cloud_agent/task/list [POST]
func CloudAgentTaskList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CloudAgentTaskListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudAgent{}.TaskList(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudAgentTaskDetail .
// @router /api/v1/cloud_agent/task/:id/detail [GET]
func CloudAgentTaskDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.IDReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudAgent{}.TaskDetail(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudAgentResChangelist .
// @router /api/v1/res_changelist/query [POST]
func CloudAgentResChangelist(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ResChangelistReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudAgent{}.ResChangelist(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
