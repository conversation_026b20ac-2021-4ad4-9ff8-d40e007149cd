// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// InitResourceGetJumpserver .
// @router /api/v2/resource_init/jumpserver [GET]
func InitResourceGetJumpserver(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GetJumpserverReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.InitResource{}.GetJumpserver(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// InitResourceGetPipeline .
// @router /api/v2/resource_init/pipeline [GET]
func InitResourceGetPipeline(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GetPipelineReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.InitResource{}.GetPipeline(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// InitResourcePreviewParam .
// @router /api/v2/resource_init/param/preview [POST]
func InitResourcePreviewParam(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.PreviewParamReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.InitResource{}.PreviewParam(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
