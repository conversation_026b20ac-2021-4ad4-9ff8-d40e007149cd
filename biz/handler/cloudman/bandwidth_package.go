// Code generated by hertz generator.

package cloudman

import (
	"context"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// BandwidthPackageDescribeCbwp .
// @router /api/v2/resource/bandwidth-package/cbwp [GET]
func BandwidthPackageDescribeCbwp(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeBandwidthPackageCbwpReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.BandwidthPackage{}.DescribeBandwidthPackageCbwp(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
