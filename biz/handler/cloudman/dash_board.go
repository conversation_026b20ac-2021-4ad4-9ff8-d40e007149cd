// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// DashBoardGetAccount .
// @router /api/v1/dashboard/account [GET]
func DashBoardGetAccount(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DashBoardAccountRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetAccount(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardGetUserAuth .
// @router /api/v1/dashboard/userauth [GET]
func DashBoardGetUserAuth(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DashBoardUserAuthRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetUserAuth(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardGetIfWorkOrderUsed .
// @router /api/v1/dashboard/ifworkorderused [GET]
func DashBoardGetIfWorkOrderUsed(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetIfWorkOrderUsed(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardGetBackendVersion .
// @router /api/v1/dashboard/backendVersion [GET]
func DashBoardGetBackendVersion(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetBackendVersion(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardGetHostBrief .
// @router /api/v1/dashboard/host_brief [POST]
func DashBoardGetHostBrief(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DashboardHostBriefReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetHostBrief(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardGetResBrief .
// @router /api/v1/dashboard/res_brief [POST]
func DashBoardGetResBrief(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DashboardResBriefReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetResBrief(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardGetHostDailyChart .
// @router /api/v1/dashboard/host_daily_chart [POST]
func DashBoardGetHostDailyChart(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DashboardHostDailyCountReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetHostDailyChart(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardGetResDailyChart .
// @router /api/v1/dashboard/res_daily_chart [POST]
func DashBoardGetResDailyChart(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DashboardResDailyCountReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.GetResDailyChart(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardAutoFillSnapshot .
// @router /api/v1/dashboard/auto_fill_snapshot [POST]
func DashBoardAutoFillSnapshot(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.AutoFillSnapshotReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.AutoFillSnapshot(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DashBoardFillSnapshot .
// @router /api/v1/dashboard/fill_snapshot [POST]
func DashBoardFillSnapshot(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.FillSnapshotReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.DashBoard{}.FillSnapshot(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
