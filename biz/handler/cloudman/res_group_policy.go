// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// ResGroupPolicyInfo .
// @router /api/v2/resource/group-policy/:id [GET]
func ResGroupPolicyInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GroupPolicyInfoReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResGroupPolicy{}.Info(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResGroupPolicyUpdate .
// @router /api/v2/resource/group-policy/:id [PUT]
func ResGroupPolicyUpdate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ResGroupPolicyCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResGroupPolicy{}.Update(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResGroupPolicyFindResource .
// @router /api/v2/resource/group-policy-preview/query [POST]
func ResGroupPolicyFindResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.FindResReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResGroupPolicy{}.FindResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResGroupPolicyCountResource .
// @router /api/v2/resource/group-policy-preview/count [GET]
func ResGroupPolicyCountResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CountResReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResGroupPolicy{}.CountResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
