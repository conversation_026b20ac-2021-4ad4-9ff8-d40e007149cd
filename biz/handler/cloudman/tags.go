// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// TagsList .
// @router /api/v2/resource/tags [GET]
func TagsList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.TagQueryReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Tags{}.List(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TagsListSystem .
// @router /api/v2/resource/system/tags [GET]
func TagsListSystem(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.TagQueryReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Tags{}.ListSystem(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TagsCreate .
// @router /api/v2/resource/tags [POST]
func TagsCreate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.TagCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Tags{}.Create(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TagsInfo .
// @router /api/v2/resource/tags/:id [GET]
func TagsInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Tags{}.Info(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TagsUpdate .
// @router /api/v2/resource/tags/:id [PUT]
func TagsUpdate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.TagCreateReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Tags{}.Update(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TagsDel .
// @router /api/v2/resource/tags/:id [DELETE]
func TagsDel(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Tags{}.Del(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
