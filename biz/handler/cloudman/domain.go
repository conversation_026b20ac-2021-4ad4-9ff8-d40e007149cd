// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// DomainDescribe .
// @router /api/v2/resource/domain [POST]
func DomainDescribe(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeDomainReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Domain{}.Describe(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DomainDescribeRecords .
// @router /api/v2/resource/domain/records [POST]
func DomainDescribeRecords(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeDomainRecordsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Domain{}.DescribeRecords(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DomainSyncDnspod .
// @router /api/v2/resource/domain/sync-dnspod [POST]
func DomainSyncDnspod(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Domain{}.SyncDnspod(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// DomainCreateDomain .
// @router /api/v2/resource/domain/create [POST]
func DomainCreateDomain(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateDomainReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Domain{}.CreateDomain(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
