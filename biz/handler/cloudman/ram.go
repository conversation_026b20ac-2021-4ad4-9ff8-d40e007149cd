// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// RAMDescribeRAMPolicy .
// @router /api/v2/resource/rams [GET]
func RAMDescribeRAMPolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeRAMPolicyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RAM{}.DescribeRAMPolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RAMModifyRAMPolicySourceIPGroup .
// @router /api/v2/resource/ram/:id/ip_group [PUT]
func RAMModifyRAMPolicySourceIPGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ModifyRAMPolicySourceIPGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RAM{}.ModifyRAMPolicySourceIPGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RAMSyncRAMPolicy .
// @router /api/v2/resource/ram/sync [POST]
func RAMSyncRAMPolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SyncRamPolicyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RAM{}.SyncRAMPolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RAMPreviewRAMPolicyDeploy .
// @router /api/v2/resource/ram/deploy/preview [POST]
func RAMPreviewRAMPolicyDeploy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.PreviewRAMPolicyDeployReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RAM{}.PreviewRAMPolicyDeploy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RAMRAMPolicyDeploy .
// @router /api/v2/resource/ram/deploy [POST]
func RAMRAMPolicyDeploy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RAMPolicyDeployReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RAM{}.RAMPolicyDeploy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RAMSyncNewRAMPolicy .
// @router /api/v2/resource/ram/new/sync [POST]
func RAMSyncNewRAMPolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SyncNewRAMPolicyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RAM{}.SyncNewRAMPolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// RAMGetCloudRamPolicy .
// @router /api/v2/resource/ram/cloud [GET]
func RAMGetCloudRamPolicy(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GetCloudRamPolicyReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.RAM{}.GetCloudRamPolicy(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
