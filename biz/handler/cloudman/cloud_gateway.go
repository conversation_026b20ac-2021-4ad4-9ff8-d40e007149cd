// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// CloudGatewayCreateGateway .
// @router /api/v2/gateway [POST]
func CloudGatewayCreateGateway(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateGatewayReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.CreateGateway(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayGetGatewayScript .
// @router /api/v2/gateway/script [POST]
func CloudGatewayGetGatewayScript(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateGatewayReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.GetGatewayScript(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayGettopo .
// @router /api/v2/gateway-topo [GET]
func CloudGatewayGettopo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.Gettopo(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayGetGatewayTopo .
// @router /api/v2/gateway/topo [POST]
func CloudGatewayGetGatewayTopo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GatewayTopoReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.GetGatewayTopo(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayListGateway .
// @router /api/v2/gateway [GET]
func CloudGatewayListGateway(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ListGateway(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayGetAgentScript .
// @router /api/v2/gateway/agent/script [POST]
func CloudGatewayGetAgentScript(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateAgentReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.GetAgentScript(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayInsertGateAddress .
// @router /api/v2/gateway/config [POST]
func CloudGatewayInsertGateAddress(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GatewayAddressReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.InsertGateAddress(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayGetGateAddress .
// @router /api/v2/gateway/config [GET]
func CloudGatewayGetGateAddress(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.GetGateAddress(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayChangeAgent .
// @router /api/v2/gateway/agent [POST]
func CloudGatewayChangeAgent(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.AgentCallbackReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ChangeAgent(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayChangeAgents .
// @router /api/v2/gateway/agents [POST]
func CloudGatewayChangeAgents(ctx context.Context, c *app.RequestContext) {
	var err error
	var req []*cloudman.AgentCallbackReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ChangeAgents(ctx, req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayChangeAgentStatus .
// @router /api/v2/gateway/agent/status [POST]
func CloudGatewayChangeAgentStatus(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ChangeAgentStatusReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ChangeAgentStatus(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayDeleteAgent .
// @router /api/v2/gateway/agent/delete [POST]
func CloudGatewayDeleteAgent(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ChangeAgentStatusReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.DeleteAgent(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayListAgent .
// @router /api/v2/gateway/agent [GET]
func CloudGatewayListAgent(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ListAgentReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ListAgent(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayRawAgentInfo .
// @router /api/v2/gateway/agent/info [GET]
func CloudGatewayRawAgentInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.RawAgentInfo(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayExportXlsx .
// @router /api/v2/gateway/agent/export [POST]
func CloudGatewayExportXlsx(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ListAgentReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, filename, err := service.CloudGateway{}.ExportXlsx(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(consts.StatusOK, "application/octet-stream", resp.File)
}

// CloudGatewayListAgentInstallConfig .
// @router /api/v2/gateway/install_config [GET]
func CloudGatewayListAgentInstallConfig(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ListAgentInstallConfig(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayUpdateAgentInstallConfig .
// @router /api/v2/gateway/install_config [POST]
func CloudGatewayUpdateAgentInstallConfig(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.AgentInstallConfig
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.UpdateAgentInstallConfig(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayReinstallAgent .
// @router /api/v2/gateway/reinstall_agent [POST]
func CloudGatewayReinstallAgent(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ReinstallAgentReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ReinstallAgent(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayUpdateAgent .
// @router /api/v2/gateway/update_agent [POST]
func CloudGatewayUpdateAgent(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateAgentReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.UpdateAgent(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayGetHostAgentID .
// @router /api/v2/common/get_agent_id [POST]
func CloudGatewayGetHostAgentID(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GetAgentReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.GetHostAgentID(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// CloudGatewayListAgentVersion .
// @router /api/v2/gateway/list_version [GET]
func CloudGatewayListAgentVersion(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.CloudGateway{}.ListAgentVersion(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
