// Code generated by hertz generator.

package cloudman

import (
	"context"
	"io"
	"mime/multipart"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// HostResTemplateOption .
// @router /api/v2/resource/host/option [GET]
func HostResTemplateOption(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.Option(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateDescribe .
// @router /api/v2/resource/host [POST]
func HostResTemplateDescribe(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.HostResReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.Describe(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateDescribeOpsStatus .
// @router /api/v2/resource/host-ops-status [POST]
func HostResTemplateDescribeOpsStatus(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Ids
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.DescribeOpsStatus(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateLockResource .
// @router /api/v2/resource/host-lock [POST]
func HostResTemplateLockResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Ids
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.LockResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateUnlockResource .
// @router /api/v2/resource/host-lock [DELETE]
func HostResTemplateUnlockResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Ids
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.UnlockResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateExportXlsx .
// @router /api/v2/resource/host-export [POST]
func HostResTemplateExportXlsx(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ExportXlsxReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, filename, err := service.HostRes{}.ExportXlsx(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(consts.StatusOK, "application/octet-stream", resp.File)
}

// HostResTemplateImportXlsx .
// @router /api/v2/resource/host-import [POST]
func HostResTemplateImportXlsx(ctx context.Context, c *app.RequestContext) {
	var err error
	type ImportXlsxReq struct {
		File     *multipart.FileHeader `protobuf:"bytes,1,opt,name=file,proto3" form:"file" json:"file" query:"file"`
		FileName string                `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" form:"file_name" json:"file_name" query:"file_name"`
		IspId    string                `protobuf:"bytes,3,opt,name=isp_id,json=ispId,proto3" form:"isp_id" json:"isp_id" query:"isp_id"`
	}
	var req ImportXlsxReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}
	file, err := req.File.Open()
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}
	defer file.Close()
	binaryData, err := io.ReadAll(file)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	importReq := &cloudman.ImportXlsxReq{
		File:     binaryData,
		FileName: req.FileName,
		IspId:    req.IspId,
	}

	resp, err := service.HostRes{}.ImportXlsx(ctx, importReq)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateInputResource .
// @router /api/v2/resource/host-create [POST]
func HostResTemplateInputResource(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.HostResDetail
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.InputResource(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetHostInfo .
// @router /api/v2/resource/host-info/:id [GET]
func HostResTemplateGetHostInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetHostInfo(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetHostDiskInfo .
// @router /api/v2/resource/host-info/:id/disk [GET]
func HostResTemplateGetHostDiskInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetHostDiskInfo(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetCPUMonitor .
// @router /api/v2/resource/host-monitor/cpu [POST]
func HostResTemplateGetCPUMonitor(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MonitorReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetCPUMonitor(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetMemMonitor .
// @router /api/v2/resource/host-monitor/mem [POST]
func HostResTemplateGetMemMonitor(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MonitorReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetMemMonitor(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetIOMonitor .
// @router /api/v2/resource/host-monitor/io [POST]
func HostResTemplateGetIOMonitor(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MonitorReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetIOMonitor(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetNetMonitor .
// @router /api/v2/resource/host-monitor/net [POST]
func HostResTemplateGetNetMonitor(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MonitorReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetNetMonitor(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetDiskMonitor .
// @router /api/v2/resource/host-monitor/disk [POST]
func HostResTemplateGetDiskMonitor(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MonitorReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetDiskMonitor(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetUPTimeMonitor .
// @router /api/v2/resource/host-monitor/uptime [POST]
func HostResTemplateGetUPTimeMonitor(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MonitorReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetUPTimeMonitor(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetGraphView .
// @router /api/v2/resource/host-monitor/view [POST]
func HostResTemplateGetGraphView(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.MonitorReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetGraphView(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetGaiaMonitor .
// @router /api/v2/resource/host-monitor/gaia [POST]
func HostResTemplateGetGaiaMonitor(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GaiaMonitReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetGaiaMonitor(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetGaiaView .
// @router /api/v2/resource/host-monitor/gaia-view [POST]
func HostResTemplateGetGaiaView(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GaiaViewReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetGaiaView(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetZone .
// @router /api/v2/resource/template-hostinfo/zone/:rid [GET]
func HostResTemplateGetZone(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ZoneReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetZone(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetKeyPairs .
// @router /api/v2/resource/template-hostinfo/keypairs/:rid [GET]
func HostResTemplateGetKeyPairs(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.KeyPairReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetKeyPairs(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetSecurityGroup .
// @router /api/v2/resource/template-hostinfo/security-group/:rid [GET]
func HostResTemplateGetSecurityGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SecurityGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetSecurityGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetNetwork .
// @router /api/v2/resource/template-hostinfo/network/:rid [GET]
func HostResTemplateGetNetwork(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.NetworkReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetNetwork(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetVSwitch .
// @router /api/v2/resource/template-hostinfo/vswitch/:rid [GET]
func HostResTemplateGetVSwitch(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.VSwitchReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetVSwitch(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetImages .
// @router /api/v2/resource/template-hostinfo/images [GET]
func HostResTemplateGetImages(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ImagesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetImages(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetInstanceTypes .
// @router /api/v2/resource/template-hostinfo/instance-types [GET]
func HostResTemplateGetInstanceTypes(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.InstanceTypeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetInstanceTypes(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetVolumeTypes .
// @router /api/v2/resource/template-hostinfo/volume-types/:rid [GET]
func HostResTemplateGetVolumeTypes(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.VolumeTypeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetVolumeTypes(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateListChargeType .
// @router /api/v2/resource/template-hostinfo/charge-types/:isp [GET]
func HostResTemplateListChargeType(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ChargeTypeReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.ListChargeType(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateDescribeAutoSnapshotPolicyEX .
// @router /api/v2/resource/template-hostinfo/auto-snapshot-policyex/:isp_id [GET]
func HostResTemplateDescribeAutoSnapshotPolicyEX(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.AutoSnapshotPolicyEXReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.DescribeAutoSnapshotPolicyEX(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetExistOSNames .
// @router /api/v2/resource/host/exist_os_names [GET]
func HostResTemplateGetExistOSNames(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetExistOSNames(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateUpdateMonitorStatus .
// @router /api/v2/resource/host-update/monitor-status [POST]
func HostResTemplateUpdateMonitorStatus(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateMonitorStatusReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.UpdateMonitorStatus(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateQuery .
// @router /api/v2/resource/host-query [POST]
func HostResTemplateQuery(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CustomQuery
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.Query(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateDashboard .
// @router /api/v2/resource/host/dashboard [GET]
func HostResTemplateDashboard(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.HostDashBoardReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := service.HostRes{}.Dashboard(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateGetRemoteToken .
// @router /api/v2/resource/host/remote_token [GET]
func HostResTemplateGetRemoteToken(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.GetRemoteToken(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateSyncHostInstances .
// @router /api/v2/resource/host/sync [POST]
func HostResTemplateSyncHostInstances(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.SyncInstancesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.SyncHostInstances(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateAssignIpv6Address .
// @router /api/v2/resource/host/ipv6/assign [POST]
func HostResTemplateAssignIpv6Address(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.AssignIpv6AddressReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.AssignIpv6Address(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// HostResTemplateUnassignIpv6Address .
// @router /api/v2/resource/host/ipv6/unassign [POST]
func HostResTemplateUnassignIpv6Address(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UnassignIpv6AddressesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.HostRes{}.UnassignIpv6Address(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
