// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// LoadBalancerDescribe .
// @router /api/v2/resource/loadbalancer [POST]
func LoadBalancerDescribe(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeLoadBalancerReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.LoadBalancer{}.Describe(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// LoadBalancerDescribeDetail .
// @router /api/v2/resource/loadbalancer/:id [GET]
func LoadBalancerDescribeDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjIDReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.LoadBalancer{}.DescribeDetail(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// LoadBalancerDescribeServerGroup .
// @router /api/v2/resource/loadbalancer-server-groups/:id [GET]
func LoadBalancerDescribeServerGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.IDReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.LoadBalancer{}.DescribeServerGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// LoadBalancerDescribeZones .
// @router /api/v2/resource/loadbalancer-network/zones [POST]
func LoadBalancerDescribeZones(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeZonesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.LoadBalancer{}.DescribeZones(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// LoadBalancerListACLs .
// @router /api/v2/resource/loadbalancer-network/acls [POST]
func LoadBalancerListACLs(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ListACLsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	// resp, err := service.LoadBalancer{}.ListACLs(ctx, &req)
	// if err != nil {
	// 	response.Error(c, err, consts.StatusInternalServerError)
	// 	return
	// }

	response.NoData(c)
}

// LoadBalancerListCertificates .
// @router /api/v2/resource/loadbalancer-network/certs [POST]
func LoadBalancerListCertificates(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ListCertificatesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.LoadBalancer{}.ListCertificates(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// LoadBalancerCleanupLoadBalancer .
// @router /api/v2/resource/loadbalancer/cleanup [POST]
func LoadBalancerCleanupLoadBalancer(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.LoadBalancerIDsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.LoadBalancer{}.CleanupLoadBalancer(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
