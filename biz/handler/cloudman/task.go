// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// TaskList .
// @router /api/v1/task [GET]
func TaskList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.TaskListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Task{}.List(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TaskRetrieve .
// @router /api/v1/task/:id [GET]
func TaskRetrieve(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RunTaskReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Task{}.Retrieve(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TaskCreate .
// @router /api/v1/task [POST]
func TaskCreate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateTaskReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Task{}.Create(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TaskRunTaskAPI .
// @router /api/v1/task/:id/run [POST]
func TaskRunTaskAPI(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RunTaskReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Task{}.RunTaskAPI(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TaskUpdate .
// @router /api/v1/task/:id [PUT]
func TaskUpdate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateTaskReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Task{}.Update(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// TaskDelete .
// @router /api/v1/task/:id [DELETE]
func TaskDelete(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ObjectID
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.Task{}.Delete(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
