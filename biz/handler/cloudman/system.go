// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// SystemGetConfig .
// @router /api/v2/config [GET]
func SystemGetConfig(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.System{}.GetConfig(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SystemWecomDailyNotify .
// @router /api/v2/wecom_daily_notify [GET]
func SystemWecomDailyNotify(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.System{}.WecomDailyNotify(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SystemSecurityGroupRuleDailyNotify .
// @router /api/v2/securitygroup_rule_daily_notify [GET]
func SystemSecurityGroupRuleDailyNotify(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.Empty
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.System{}.SecurityGroupRuleDailyNotify(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
