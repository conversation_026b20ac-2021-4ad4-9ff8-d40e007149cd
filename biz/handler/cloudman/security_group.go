// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// SecurityGroupDescribe .
// @router /api/v2/resource/securitygroup [POST]
func SecurityGroupDescribe(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeSecurityGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.Describe(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupDescribeByInstances .
// @router /api/v2/resource/securitygroup/instances [POST]
func SecurityGroupDescribeByInstances(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeSecurityGroupByInstancesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.DescribeByInstances(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupDescribeRules .
// @router /api/v2/resource/securitygroup/rules [POST]
func SecurityGroupDescribeRules(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeRulesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.DescribeRules(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupUpdateSecurityGroupRule .
// @router /api/v2/resource/securitygroup/rule [PUT]
func SecurityGroupUpdateSecurityGroupRule(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateSecurityGroupRuleReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.UpdateSecurityGroupRule(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupGetAccountRegionTags .
// @router /api/v2/resource/securitygroup/custom-tags [GET]
func SecurityGroupGetAccountRegionTags(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GetAccountRegionTagsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.GetAccountRegionTags(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupGetCustomTagSecurityGroups .
// @router /api/v2/resource/securitygroup/custom-tags-sg [GET]
func SecurityGroupGetCustomTagSecurityGroups(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.GetCustomTagSecurityGroupsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.GetCustomTagSecurityGroups(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupUpdateSecurityGroupCustomTag .
// @router /api/v2/resource/securitygroup/custom-tags/update [POST]
func SecurityGroupUpdateSecurityGroupCustomTag(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateSecurityGroupCustomTagReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.UpdateSecurityGroupCustomTag(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupUpdateSecurityGroups .
// @router /api/v2/resource/securitygroup/update [POST]
func SecurityGroupUpdateSecurityGroups(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateSecurityGroupsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.UpdateSecurityGroups(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupCleanupSecurityGroups .
// @router /api/v2/resource/securitygroup/cleanup [POST]
func SecurityGroupCleanupSecurityGroups(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.UpdateSecurityGroupCustomTagReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.CleanupSecurityGroups(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupExportSecurityGroup .
// @router /api/v2/resource/securitygroup/export [POST]
func SecurityGroupExportSecurityGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ExportSecurityGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, filename, err := service.SecurityGroup{}.ExportSecurityGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(consts.StatusOK, "application/octet-stream", resp.File)
}

// SecurityGroupExportSecurityGroupRule .
// @router /api/v2/resource/securitygroup/rule/export [POST]
func SecurityGroupExportSecurityGroupRule(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ExportSecurityGroupRuleReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, filename, err := service.SecurityGroup{}.ExportSecurityGroupRule(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(consts.StatusOK, "application/octet-stream", resp.File)
}

// SecurityGroupExportRelateInstances .
// @router /api/v2/resource/securitygroup/relate-instances/export [POST]
func SecurityGroupExportRelateInstances(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ExportRelateInstancesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, filename, err := service.SecurityGroup{}.ExportRelateInstances(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(consts.StatusOK, "application/octet-stream", resp.File)
}

// SecurityGroupBatchDeleteSecurityGroupRule .
// @router /api/v2/resource/securitygroup/rule/batch-delete [POST]
func SecurityGroupBatchDeleteSecurityGroupRule(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.BatchDeleteSecurityGroupRuleReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.BatchDeleteSecurityGroupRule(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupCreateSecurityGroupRule .
// @router /api/v2/resource/securitygroup/rule [POST]
func SecurityGroupCreateSecurityGroupRule(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateSecurityGroupRuleReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.CreateSecurityGroupRule(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupDescribeIPWhitelists .
// @router /api/v2/resource/securitygroup/whitelists [GET]
func SecurityGroupDescribeIPWhitelists(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeIPWhitelistsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.DescribeIPWhitelists(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupModifyIPWhitelists .
// @router /api/v2/resource/securitygroup/whitelist/:security_group_id [PUT]
func SecurityGroupModifyIPWhitelists(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ModifyIPWhitelistsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.ModifyIPWhitelists(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupDescribeALBAcls .
// @router /api/v2/resource/securitygroup/alb_acls [GET]
func SecurityGroupDescribeALBAcls(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeALBAclsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.DescribeALBAcls(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupAddALBAclsEntries .
// @router /api/v2/resource/securitygroup/alb_acl/:acl_id/entry [POST]
func SecurityGroupAddALBAclsEntries(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.AddALBAclsEntriesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.AddALBAclsEntries(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupRemoveALBAclsEntries .
// @router /api/v2/resource/securitygroup/alb_acl/:acl_id/entry/batch-delete [POST]
func SecurityGroupRemoveALBAclsEntries(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.RemoveALBAclsEntriesReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.RemoveALBAclsEntries(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupJoinSecurityGroup .
// @router /api/v2/resource/securitygroup/join [POST]
func SecurityGroupJoinSecurityGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.JoinOrLeaveSecurityGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.JoinSecurityGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// SecurityGroupLeaveSecurityGroup .
// @router /api/v2/resource/securitygroup/leave [POST]
func SecurityGroupLeaveSecurityGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.JoinOrLeaveSecurityGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.SecurityGroup{}.LeaveSecurityGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
