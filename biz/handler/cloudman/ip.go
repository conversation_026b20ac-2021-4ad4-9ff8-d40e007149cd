// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// IPDescribe .
// @router /api/v2/resource/ip [POST]
func IPDescribe(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeIPReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.Describe(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// IPCreateCustomIP .
// @router /api/v2/resource/ip/create [POST]
func IPCreateCustomIP(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.IPEntity
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.CreateCustomIP(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// IPDeleteIP .
// @router /api/v2/resource/ip/:id [DELETE]
func IPDeleteIP(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DeleteIPReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.DeleteIP(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// IPDescribeByIPs .
// @router /api/v2/resource/ip/ips [POST]
func IPDescribeByIPs(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeByIPsReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.DescribeByIPs(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// IPDescribeIPGroup .
// @router /api/v2/resource/ip_groups [GET]
func IPDescribeIPGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DescribeIPGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.DescribeIPGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// IPCreateIPGroup .
// @router /api/v2/resource/ip_group [POST]
func IPCreateIPGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.CreateIPGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.CreateIPGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// IPModifyIPGroup .
// @router /api/v2/resource/ip_group/:id [PUT]
func IPModifyIPGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.ModifyIPGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.ModifyIPGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// IPDeleteIPGroup .
// @router /api/v2/resource/ip_group/:id [DELETE]
func IPDeleteIPGroup(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.DeleteIPGroupReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.IP{}.DeleteIPGroup(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
