// Code generated by hertz generator.

package cloudman

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
	cloudman "platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/response"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
)

// ResAuditGetOrderList .
// @router /api/v2/audit/order [POST]
func ResAuditGetOrderList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.OrderTaskReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResourceAudit{}.GetOrderList(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResAuditGetOrderLog .
// @router /api/v2/audit/order-log/:id [GET]
func ResAuditGetOrderLog(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.OrderLogReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResourceAudit{}.GetOrderLog(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}

// ResAuditGetOrder .
// @router /api/v2/audit/order/:id [GET]
func ResAuditGetOrder(ctx context.Context, c *app.RequestContext) {
	var err error
	var req cloudman.IDReq
	err = c.BindAndValidate(&req)
	if err != nil {
		response.Error(c, err, consts.StatusBadRequest)
		return
	}

	resp, err := service.ResourceAudit{}.GetOrder(ctx, &req)
	if err != nil {
		response.Error(c, err, consts.StatusInternalServerError)
		return
	}

	response.Data(c, resp)
}
