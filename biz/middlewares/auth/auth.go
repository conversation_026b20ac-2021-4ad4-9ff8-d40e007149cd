package auth

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
)

const (
	Username      = "username"
	Authorization = "Authorization"
	Token         = "token"
)

func GetUsername(ctx context.Context, c *app.RequestContext) (username string) {
	token := GetAuthToken(c)
	if token == "" {
		return
	}
	cli, err := cfg.GetIamDefaultCli()
	if err != nil {
		return
	}
	username = cli.GetUsername(ctx, token)
	return
}

func GetAuthToken(c *app.RequestContext) string {
	return c.Request.Header.Get(Authorization)
}

func Auth() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		username := GetUsername(ctx, c)
		ctx = context.WithValue(ctx, Username, username)
		ctx = context.WithValue(ctx, Token, GetAuthToken(c))
		c.Next(ctx)
	}
}
