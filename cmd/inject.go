package cmd

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"google.golang.org/grpc/metadata"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/model/cloudman"
	"platgit.mihoyo.com/jql-ops/op-cloudman/biz/service"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	bkcmdb "platgit.mihoyo.com/jql-ops/op-cloudman/hooks/bk_cmdb"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks/innerhook"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks/notice"
	"platgit.mihoyo.com/jql-ops/op-cloudman/hooks/webhook"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
	iamsdk "platgit.mihoyo.com/op-plat/iam-sdk-go-http"
)

func initMongo() {
	err := models.CreateIndexes(context.Background(), models.GetEngine())
	if err != nil {
		panic(err.Error())
	}
}

func initMonitorStatus() {
	ctx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
	_, err := service.Service{}.InitStatus(ctx)
	if err != nil {
		panic(err.Error())
	}
}

func initHook() {
	innerhook.RegHook()
	notice.RegHook()
	webhook.RegHook()
	bkcmdb.RegHook()
}

func init() {
	// core.AddHook("logger.OnEntryEncoding", func(ctx context.Context, ent logger.Entry, fields []logger.Field) ([]logger.Field, error) {
	// 	ip := ctxhelper.GetRPCMeta(ctx, "client_ip")
	// 	username := ctxhelper.GetRPCMeta(ctx, "username")
	// 	fields = append(fields, logger.String("client_ip", ip))
	// 	fields = append(fields, logger.String("username", username))
	// 	return fields, nil
	// })
}

func initIAM() {
	iam, err := cfg.GetIAMCfg()
	if err != nil {
		panic(err)
	}
	for env, iamCfg := range iam.EnvCK {
		cli, err := iamsdk.InitIAMClient(iamCfg.ClientKeyID, iamCfg.ClientKeySecret, iamCfg.DebugAddress)
		if err != nil {
			panic(err)
		}
		cfg.IAMCliMap[env] = cli
	}
	_, ok := cfg.IAMCliMap["cur"]
	if !ok {
		panic(errors.New("missing current iam cfg"))
	}
}

// func initKMS() {
// 	if err := kms.InitCloudmanKMSCli(); err != nil {
// 		logger.Errorf(context.Background(), "init kms error: %s", err.Error())
// 	}
// 	if err := kms.InitDnspodKMSCli(); err != nil {
// 		logger.Errorf(context.Background(), "init kms error: %s", err.Error())
// 	}
// }

// func initCron() {
// 	jobmanCfg := cfg.GetJobmanConfig()
// 	if !jobmanCfg.Enable {
// 		return
// 	}
// 	ctx := context.Background()
// 	res, _, err := models.SyncTaskModel.Query(ctx, &schema.SyncTaskQueryParams{
// 		PaginationParam: schema.PaginationParam{
// 			Page: 1,
// 			Size: math.MaxInt32,
// 		},
// 	})
// 	if err != nil {
// 		logger.Errorf("定时任务初始化失败！ error:%s", err.Error())
// 		return
// 	}
// 	for _, v := range res {
// 		if v.Status == 1 && v.BindJobmanTaskID == "" {
// 			templateID, cronTaskID, err := jobman.CreateSyncCron(ctx, v.ID.Hex(), v.Name+"-"+v.BindRegionID, uint32(v.Rate))
// 			if err != nil {
// 				logger.Errorf("定时任务初始化失败！ error:%s", err.Error())
// 				return
// 			}
// 			err = models.SyncTaskModel.Update(ctx, v.ID.Hex(), map[string]interface{}{
// 				"bind_jobman_task_id":     cronTaskID,
// 				"bind_jobman_template_id": templateID,
// 			})
// 			if err != nil {
// 				logger.Errorf("定时任务初始化失败！ error:%s", err.Error())
// 				return
// 			}
// 		}
// 	}
// }

// StartDailySnapshotCron ...
func StartDailySnapshotCron() {
	c := cron.New()
	_, err := c.AddFunc("2 0 * * *", DailySnapshot)
	if err != nil {
		panic(err)
	}
	c.Start()
}

// DailySnapshot ...
func DailySnapshot() {
	ctx := context.Background()
	sysConf := cfg.GetSystemConfig()
	if !sysConf.EnableSnapshot {
		return
	}

	count := 0
	for {
		err := doSnapshot(ctx)
		if err == nil {
			break
		}
		logger.Errorf("daily snapshot failed: %s", err.Error())

		count++
		if count >= 3 {
			logger.Errorf("daily snapshot failed 3 times, will stop")
			break
		}
		logger.Infof("daily snapshot failed, will restart after 15m")
		time.Sleep(time.Minute * 15)
	}
}

func doSnapshot(ctx context.Context) error {
	snapshot, err := models.ResSnapshotModel.FindOne(ctx, &entity.ResSnapshot{
		AccountID: "daily_snapshot_status",
		DayTime:   time.Now().Truncate(24 * time.Hour).Unix(),
	})
	if err == nil {
		if snapshot.ResType == "success" {
			return nil
		}
		if time.Unix(snapshot.UpdatedTime, 0).Add(15 * time.Minute).After(time.Now()) {
			return errors.New("save snapshot still running, prepare next try")
		}
	} else if err != mongo.ErrNoDocuments {
		return err
	}

	dayTime := time.Now().Truncate(24 * time.Hour).Unix()
	err = models.ResSnapshotModel.CreateOrUpdateDailyStatus(ctx, models.DefaultFilter(ctx,
		bson.E{Key: "account_id", Value: "daily_snapshot_status"},
		bson.E{Key: "day_time", Value: dayTime}),
		&entity.ResSnapshot{
			AccountID:  "daily_snapshot_status",
			ResType:    "running",
			Region:     "running",
			DayTime:    dayTime,
			TotalCount: 0,
		})
	if err != nil {
		return errors.New("save snapshot running status error")
	}
	for _, ispType := range constant.AllIspType {
		for _, resType := range constant.AllResType {
			providerName := fmt.Sprintf("%s_%s", ispType, resType)
			f, ok := resprovider.ProvideMap[providerName]
			if !ok {
				continue
			}
			accounts, err := models.AccountModel.FindByAType(ctx, ispType)
			if err != nil {
				return err
			}
			for _, account := range accounts {
				var regions []entity.Region
				if ispType != "custom" {
					regions, err = models.RegionModel.FindManyWithPK(ctx, account.RegionIDs)
					if err != nil {
						logger.Errorf("get account region error: %s, %v", strings.Join(account.RegionIDs, ","), err)
						continue
					}
				} else {
					regions = append(regions, entity.Region{RegionID: "cn-shanghai"})
				}
				for _, region := range regions {
					pr, err := f(common.InitProvider{RegionID: region.RegionID, IspID: account.ID.Hex(), IspType: ispType, Host: account.Host, Logger: logrus.New()})
					if err != nil {
						return err
					}
					count, err := pr.GetSnapshotCount(ctx)
					if err != nil {
						logger.Errorf("save snapshot error: %s, %s, %v", account.ID.Hex(), region.RegionID, err)
						continue
					}
					err = models.ResSnapshotModel.FindOrCreate(ctx, &entity.ResSnapshot{
						AccountID:  account.ID.Hex(),
						ResType:    resType,
						Region:     region.RegionID,
						DayTime:    time.Now().Truncate(24 * time.Hour).Unix(),
						TotalCount: int(count),
					})
					if err != nil {
						logger.Errorf("save snapshot error: %s, %s, %v", account.ID.Hex(), region.RegionID, err)
						continue
					}
				}
			}
		}
	}
	err = models.ResSnapshotModel.CreateOrUpdateDailyStatus(ctx, models.DefaultFilter(ctx,
		bson.E{Key: "account_id", Value: "daily_snapshot_status"},
		bson.E{Key: "day_time", Value: dayTime}),
		&entity.ResSnapshot{
			AccountID:  "daily_snapshot_status",
			ResType:    "success",
			Region:     "success",
			DayTime:    dayTime,
			TotalCount: 1,
		})
	if err != nil {
		return errors.New("save snapshot success status error")
	}
	return nil
}

// initLocalCron 启动一个cron进程，每隔一段时间从SyncTask里面取任务执行
func initLocalCron() {
	c := cron.New()
	// 把username塞到里面去
	ctx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
		"x-rpc-" + "username": "system",
	}))

	// 每隔多少秒拉取一次定时任务表
	interval := 20

	_, err := c.AddFunc(fmt.Sprintf("@every %ds", interval), func() {
		fetchAndRunSyncTask(ctx)
	})
	if err != nil {
		panic(err)
	}
	c.Start()
	logger.Info("start local cron process success")
}

// fetchAndRunSyncTask 取出SyncTask中满足条件的任务执行
// 执行条件: 1. 全局的enable_local_cron的开关是打开的。2. 任务为启用状态。3. 服务器时间 - 任务上次执行时间 > 任务的执行间隔
func fetchAndRunSyncTask(ctx context.Context) {
	ctx = context.WithValue(ctx, permission.Inner, permission.InnerTrue)
	if !cfg.GetSystemConfig().EnableLocalCron {
		logger.Warn("fetchAndRunSyncTask.disable_local_cron, local cron is turned off, skip the current loop ...")
		return
	}

	tasks, _, err := models.SyncTaskModel.Query(ctx, &schema.SyncTaskQueryParams{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: math.MaxInt32,
		},
	})

	if err != nil {
		logger.Errorf("fetchAndRunSyncTask.query, query sync task failed, err: %v", err)
		return
	}

	for _, t := range tasks {
		if t.Status != 1 {
			continue
		}
		if time.Now().Unix()-t.LatestRunStartTime < int64(t.Rate*60) {
			logger.Warnf("fetchAndRunSyncTask.tasks.rate, the task %s in account %s has already ran at %s, and the current rate is %d minutes", t.Name, t.AccountID, time.Unix(t.LatestRunStartTime, 0).Format(time.RFC3339), t.Rate)
			continue
		}

		_, err := service.Task{}.RunTaskAPI(ctx, &cloudman.RunTaskReq{
			Oid: t.ID.Hex(),
		})
		if err != nil {
			logger.Errorf("fetchAndRunSyncTask.tasks.run, run task %s failed, error: %v", t.Name, err)
		}
	}
}
