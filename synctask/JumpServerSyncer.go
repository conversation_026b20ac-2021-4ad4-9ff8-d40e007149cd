package synctask

import (
	"encoding/json"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jumpserversdk"
)

// JumpserverSyncer 阿里云同步器
type JumpserverSyncer struct {
}

var jumpserverSyncer *JumpserverSyncer

// GetJumpserverSyncer 获取单例
func GetJumpserverSyncer() *JumpserverSyncer {
	if jumpserverSyncer == nil {
		jumpserverSyncer = &JumpserverSyncer{}
	}
	return jumpserverSyncer
}

// TaskSet 获取GetJumpserverSyncer支持的同步方法组
func (m *JumpserverSyncer) TaskSet() map[string]string {
	return map[string]string{
		"同步堡垒机Host信息": SyncHost,
	}
}

// SyncHost 同步云主机
// Comment 字段作为
func (m *JumpserverSyncer) SyncHost(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}

	account, err := models.AccountModel.Get(ctx, option.IspID)
	if err != nil {
		return err
	}
	accountIndexID := account.IndexID

	logging.Infof("jumpserver.SyncHost.start")
	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("**************")

	// 循环变量初始化
	var pageSize, offset int64 = 100, 0
	var totalCount = 0
	for {
		var updateData []*entity.HostResource
		var hosts []*jumpserversdk.Host
		err = agentsdk.SyncCall(ctx, option.RegionID, "host", "ListHosts", option.IspID, []interface{}{account.Host, offset, pageSize}, &hosts)
		if err != nil {
			logging.Warnf("jumpserver.SyncHost.ListHosts.failed: %s", err.Error())
			return err
		}

		if len(hosts) == 0 {
			break
		}
		totalCount += len(hosts)
		offset += pageSize
		for _, h := range hosts {
			inHost := ConvertJumpserverHost2InHost(h)
			inHost.IspID = option.IspID
			// 标记版本号，版本号不一致的最后统一标记为待清理
			inHost.UpdateVersion = syncUpdateVersion
			// 注入agentID值
			if len(inHost.InnerIPAddress) > 0 && inHost.AgentID == "" {
				// 多内网IP,仅使用第一个生成
				agentID, genErr := agent.GenerateID(inHost.RegionID, fmt.Sprintf("%s:%d", inHost.InnerIPAddress[0], accountIndexID))
				if genErr == nil {
					inHost.AgentID = agentID
				}
			}
			updateData = append(updateData, inHost)
		}
		dataDump, _ := json.Marshal(updateData)
		logging.Infof("jumpserver.SyncHost.save.dump: %s", string(dataDump))
		_, err = models.HostResourceModel.BatchCreateOrUpdate(ctx, updateData, models.HostUpdateOption{
			UpdateTags: false,
			UpdateDesc: false,
			FromSync:   true,
		})
		if err != nil {
			logging.Warnf("jumpserver.SyncHost.save.failed: %s", err.Error())
			return err
		}
	}

	// 只有全量更新才打清理标签清理
	if len(instanceIDs) == 0 {
		cleanupAmount, err := models.HostResourceModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("HostResourceModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("HostResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}

	// syncHost自身逻辑跑完，跟一个cmdb绑定信息的同步
	err = GetBkCmdbSyncer().SyncHost(ctx, logging, option, instanceIDs...)
	if err != nil {
		return err
	}

	return nil
}

// ConvertJumpserverHost2InHost sdk结构转云管结构
func ConvertJumpserverHost2InHost(h *jumpserversdk.Host) *entity.HostResource {
	inHost := &entity.HostResource{}
	inHost.InstanceID = h.ID
	inHost.RegionID = "cn-shanghai"

	inHost.HostName = h.HostName
	inHost.InstanceName = "office-test-" + h.IP
	inHost.Description = h.Comment
	kvs := strings.Split(h.Comment, ",")
	for _, v := range kvs {
		val := strings.Split(v, ":")
		if len(val) != 2 {
			continue
		}
		if val[0] == "name" {
			inHost.InstanceName = val[1]
			inHost.HostName = val[1]
		} else if val[0] == "desc" {
			inHost.Description = val[1]
		}
	}
	inHost.IspType = constant.IspJumpserver
	ip := net.ParseIP(h.IP)
	if ip.IsPrivate() {
		inHost.InnerIPAddress = []string{h.IP}
	} else {
		inHost.PublicIPAddress = []string{h.IP}
	}
	inHost.OSType = strings.ToLower(h.Platform)
	inHost.OSName = inHost.OSType
	//inHost.ImageID = h.Image.ImageID
	inHost.Status = "Stopped"
	if h.IsActive {
		inHost.Status = "Running"
	}
	return inHost
}

// SyncRedis jumpserver无redis类型
func (m *JumpserverSyncer) SyncRedis(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	return nil
}

// SyncMysql jumpserver无mysql类型
func (m *JumpserverSyncer) SyncMysql(ctx context.Context, logging *logrus.Logger, option SyncOption, clusterIDs ...string) error {
	return nil
}

// SyncSecurityGroup jumpserver无SecurityGroup类型
func (m *JumpserverSyncer) SyncSecurityGroup(ctx context.Context, logging *logrus.Logger, option SyncOption, securityGroupIDs ...string) error {
	return nil
}

// SyncLoadBalancer ...
func (m *JumpserverSyncer) SyncLoadBalancer(ctx context.Context, logging *logrus.Logger, option SyncOption, loadBalancerIDs ...string) error {
	return nil
}

// SyncEIP ...
func (m *JumpserverSyncer) SyncEIP(ctx context.Context, logging *logrus.Logger, option SyncOption, eipIDs ...string) error {
	return nil
}

func (m *JumpserverSyncer) SyncPolicy(ctx context.Context, logging *logrus.Logger, option SyncOption, eipIDs ...string) error {
	return nil
}
