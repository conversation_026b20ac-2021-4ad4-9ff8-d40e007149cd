package synctask

import (
	"encoding/json"
	"errors"
	"sync/atomic"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	dnspod "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/dnspod/v20210323"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/kms"
	"platgit.mihoyo.com/jql-ops/op-cloudman/logger"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
)

// IsSyncingDnspod ...
var IsSyncingDnspod atomic.Bool

// SyncDNSPod ...
func SyncDNSPod(ctx context.Context) error {
	IsSyncingDnspod.Store(true)
	defer IsSyncingDnspod.Store(false)
	kmsClient, ok := kms.Clients["dnspod"]
	if !ok {
		return errors.New("cannot get kms client: 'dnspod'")
	}
	ak, sk, err := kmsClient.GetRealAK("qingxing_get_dns_domain", "tencent_ak", "tencent_sk")
	if err != nil {
		return err
	}
	credential := common.NewCredential(ak, sk)
	client, _ := dnspod.NewClient(credential, regions.Shanghai, profile.NewClientProfile())
	resp, err := client.DescribeDomainList(dnspod.NewDescribeDomainListRequest())
	if err != nil {
		return err
	}
	resRaw, err := json.Marshal(resp.Response.DomainList)
	if err != nil {
		return err
	}
	var domainLists []*entity.Domain
	err = json.Unmarshal(resRaw, &domainLists)
	if err != nil {
		return err
	}

	for _, domain := range domainLists {
		recordReq := dnspod.NewDescribeRecordListRequest()
		recordReq.Domain = common.StringPtr(domain.Name)
		recordRes, err := client.DescribeRecordList(recordReq)
		if err != nil {
			return err
		}
		recordResRaw, err := json.Marshal(recordRes.Response.RecordList)
		if err != nil {
			return err
		}
		var recordLists []*entity.Record
		err = json.Unmarshal(recordResRaw, &recordLists)
		if err != nil {
			return err
		}
		domain.RecordList = recordLists
		domain.RegionID = regions.Shanghai
		domain.IspType = "dnspod"
	}

	_, err = models.DomainModel.BatchCreateOrUpdate(ctx, domainLists)
	if err != nil {
		return err
	}
	logger.Infof("dnspod synced successfully")
	return nil
}
