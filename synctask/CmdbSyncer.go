package synctask

import (
	"encoding/json"

	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/net/context"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cmdb"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/utils"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// BkCmdbSyncer 阿里云同步器
type BkCmdbSyncer struct {
	gameRegionCache map[int]string
}

var bkCmdbSyncer *BkCmdbSyncer

// GetBkCmdbSyncer 获取单例
func GetBkCmdbSyncer() *BkCmdbSyncer {
	if bkCmdbSyncer == nil {
		bkCmdbSyncer = &BkCmdbSyncer{}
	}
	return bkCmdbSyncer
}

// TaskSet 获取GetCmdbSyncer支持的同步方法组
func (m *BkCmdbSyncer) TaskSet() map[string]string {
	return map[string]string{
		"同步蓝鲸CMDB-Host信息":  SyncHost,
		"同步蓝鲸CMDB-Mysql信息": SyncMysql,
		"同步蓝鲸CMDB-Redis信息": SyncRedis,
	}
}

var dbPageSize = uint64(100)
var dbSyncSize = uint64(50)

// SyncHost -
func (m *BkCmdbSyncer) SyncHost(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	gameRegionCache = map[int]string{}
	if cfg.GetCmdbConfig().BkCmdbTestURL != "" {
		err := m.syncHostWithEnv(ctx, logging, false, option, instanceIDs...)
		if err != nil {
			return err
		}
	}
	if cfg.GetCmdbConfig().BkCmdbURL != "" {
		err := m.syncHostWithEnv(ctx, logging, true, option, instanceIDs...)
		if err != nil {
			return err
		}
	}
	return nil
}

func (m *BkCmdbSyncer) syncHostWithEnv(ctx context.Context, logging *logrus.Logger, isProd bool, option SyncOption, instanceIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging = utils.CmdbLogger(logging)
	dbPage := uint64(0)
	dbTotal := dbSyncSize * 2
	customFilter := map[string]any{}
	if len(instanceIDs) != 0 {
		iInstanceID := []any{}
		for _, i := range instanceIDs {
			iInstanceID = append(iInstanceID, i)
		}
		customFilter["InstanceID"] = bson.M{"$in": iInstanceID}
	}
	targetEnv := "test"
	if isProd {
		targetEnv = "prod"
	}
	for dbPage*dbSyncSize < dbTotal {
		// 搜索云管数据
		resp, total, err := models.HostResourceModel.Query(ctx, &schema.HostResourceQueryParams{
			HostResourceColumnParam: schema.HostResourceColumnParam{
				IspID:    option.IspID,
				AgentEnv: targetEnv,
				RegionID: option.RegionID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbSyncSize,
			},
			OrderParams: schema.OrderParams{},
		}, customFilter)
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage++
		if len(resp) == 0 {
			continue
		}
		ids := []string{}
		for _, item := range resp {
			ids = append(ids, item.InstanceID)
		}
		// 搜索cmdb的id
		bkIDMap, err := cmdb.FindBkIDByBkName(logging, isProd, "ali_ecs", ids)
		if err != nil {
			continue
		}
		if len(bkIDMap) == 0 {
			continue
		}
		cmdbMap := map[string]entity.BkCmdbInfo{}
		for instanceID, bkID := range bkIDMap {
			cmdbInfo := entity.BkCmdbInfo{}
			cmdbInfo.InstID = int32(bkID)
			// 分别根据id查找两个方向的绑定关系
			assoc1RawArray, assoc1Err := cmdb.FindAssocByBkID(logging, isProd, cmdb.AssocQueryReq{
				TargetType: "ali_ecs",
				TargetID:   bkID,
			})
			if assoc1Err != nil {
				logging.Warnf("cmdb.FindAssocByBkID1.%s.error: %s", instanceID, assoc1Err.Error())
			} else {
				// 查到绑定就用区服/进程id换名字
				regionErr := m.readGameRegion(ctx, logging, isProd, assoc1RawArray, &cmdbInfo)
				if regionErr != nil {
					logging.Warnf("cmdb.readGameRegion.%s.error: %s", instanceID, regionErr.Error())
				}
			}
			assoc2RawArray, assoc2Err := cmdb.FindAssocByBkID(logging, isProd, cmdb.AssocQueryReq{
				InstType: "ali_ecs",
				InstID:   bkID,
			})
			if assoc2Err != nil {
				logging.Warnf("cmdb.FindAssocByBkID2.%s.error: %s", instanceID, assoc2Err.Error())
			} else {
				processErr := m.readYuanshenProcess(ctx, logging, isProd, assoc2RawArray, &cmdbInfo)
				if processErr != nil {
					logging.Warnf("cmdb.readYuanshenProcess.%s.error: %s", instanceID, processErr.Error())
				}
			}
			cmdbMap[instanceID] = cmdbInfo
		}
		cmdbUpdateBin, _ := json.Marshal(cmdbMap)
		logging2 := utils.CmdbLogger(logging)
		logging2.Infof("SyncHost.UpdateCmdbInfo.dump: %s", string(cmdbUpdateBin))
		updateErr := models.HostResourceModel.UpdateCmdbInfo(ctx, cmdbMap)
		if updateErr != nil {
			logging.Warnf("SyncHost.UpdateCmdbInfo.error: %s", updateErr.Error())
		}
	}
	return nil
}

// SyncRedis -
func (m *BkCmdbSyncer) SyncRedis(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	return nil
}

// SyncMysql -
func (m *BkCmdbSyncer) SyncMysql(ctx context.Context, logging *logrus.Logger, option SyncOption, clusterIDs ...string) error {
	if !cfg.GetCmdbConfig().Enable {
		logging.Warn("current sync cmdb is disabled, skip...")
		return nil
	}

	gameRegionCache = map[int]string{}
	if cfg.GetCmdbConfig().BkCmdbTestURL != "" {
		err := m.syncMysqlDatabase(ctx, logging, false, option, clusterIDs...)
		if err != nil {
			return err
		}
	}
	if cfg.GetCmdbConfig().BkCmdbURL != "" {
		err := m.syncMysqlDatabase(ctx, logging, true, option, clusterIDs...)
		if err != nil {
			return err
		}
	}
	return nil
}

func (m *BkCmdbSyncer) syncMysqlDatabase(ctx context.Context, logging *logrus.Logger, isProd bool, option SyncOption, clusterIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging = utils.CmdbLogger(logging)
	dbPage := uint64(0)
	dbTotal := dbPageSize * 2
	customFilter := map[string]any{}
	if len(clusterIDs) != 0 {
		iInstanceID := []any{}
		for _, i := range clusterIDs {
			iInstanceID = append(iInstanceID, i)
		}
		customFilter["cluster_id"] = bson.M{"$in": clusterIDs}
	}
	for dbPage*dbPageSize < dbTotal {
		// 搜索云管数据
		resp, total, err := models.MysqlDatabaseResourceModel.Query(ctx, &schema.MysqlDatabaseResourceQueryParams{
			MysqlDatabaseResourceColumnParam: schema.MysqlDatabaseResourceColumnParam{
				IspID:    option.IspID,
				RegionID: option.RegionID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbPageSize,
			},
			OrderParams: schema.OrderParams{},
		}, customFilter)
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage++
		if len(resp) == 0 {
			continue
		}
		ids := []string{}
		for _, item := range resp {
			ids = append(ids, item.ClusterID+"_"+item.DBName)
		}
		// 搜索cmdb的id
		bkIDMap, err := cmdb.FindBkIDByBkName(logging, isProd, "polardb_database", ids)
		if err != nil {
			continue
		}
		if len(bkIDMap) == 0 {
			continue
		}
		cmdbMap := map[string]entity.BkCmdbInfo{}
		for instanceID, bkID := range bkIDMap {
			cmdbInfo := entity.BkCmdbInfo{}
			cmdbInfo.InstID = int32(bkID)
			// 数据库只查区服
			assoc1RawArray, assoc1Err := cmdb.FindAssocByBkID(logging, isProd, cmdb.AssocQueryReq{
				TargetType: "polardb_database",
				TargetID:   bkID,
			})
			if assoc1Err != nil {
				logging.Warnf("cmdb.FindAssocByBkID1.%s.error: %s", instanceID, assoc1Err.Error())
			} else {
				assoc1RawCutArray := []json.RawMessage{}
				for _, r := range assoc1RawArray {
					if gjson.GetBytes(r, "bk_obj_asst_id").String() == "game_region_belong_polardb_database" {
						assoc1RawCutArray = append(assoc1RawCutArray, r)
					}
				}
				// 查到绑定就用区服/进程id换名字
				regionErr := m.readGameRegion(ctx, logging, isProd, assoc1RawCutArray, &cmdbInfo)
				if regionErr != nil {
					logging.Warnf("cmdb.readGameRegion.%s.error: %s", instanceID, regionErr.Error())
				}
			}
			if len(cmdbInfo.GameRegionName) > 0 || len(cmdbInfo.ProcessName) > 0 {
				cmdbMap[instanceID] = cmdbInfo
			}
		}
		cmdbUpdateBin, _ := json.Marshal(cmdbMap)
		logging2 := utils.CmdbLogger(logging)
		logging2.Infof("syncMysqlDatabase.UpdateCmdbInfo.dump: %s", string(cmdbUpdateBin))
		updateErr := models.MysqlDatabaseResourceModel.UpdateCmdbInfo(ctx, cmdbMap)
		if updateErr != nil {
			logging.Warnf("syncMysqlDatabase.UpdateCmdbInfo.error: %s", updateErr.Error())
		}
	}
	return nil
}

var gameRegionCache = map[int]string{}

func (m *BkCmdbSyncer) readGameRegion(ctx context.Context, logging *logrus.Logger, isProd bool, rawArray []json.RawMessage, cmdbInfo *entity.BkCmdbInfo) error {
	targetIDs := []int{}
	prodMinus := 1
	if !isProd {
		prodMinus = -1
	}
	for _, jr := range rawArray {
		targetIDs = append(targetIDs, int(gjson.ParseBytes(jr).Get("bk_inst_id").Int()))
	}
	uncacheTargetIDs := []int{}
	for _, targetID := range targetIDs {
		if gameRegionCache[prodMinus*targetID] != "" {
			cmdbInfo.GameRegionName = append(cmdbInfo.GameRegionName, gameRegionCache[prodMinus*targetID])
		} else {
			uncacheTargetIDs = append(uncacheTargetIDs, targetID)
		}
	}
	targetNames, err := cmdb.FindBkNameByBkID(logging, isProd, "game_region", uncacheTargetIDs)
	if err != nil {
		return err
	}
	for targetID, name := range targetNames {
		cmdbInfo.GameRegionName = append(cmdbInfo.GameRegionName, name)
		gameRegionCache[prodMinus*targetID] = name
	}
	return nil
}

func (m *BkCmdbSyncer) readYuanshenProcess(ctx context.Context, logging *logrus.Logger, isProd bool, rawArray []json.RawMessage, cmdbInfo *entity.BkCmdbInfo) error {
	targetIDs := []int{}
	for _, jr := range rawArray {
		targetIDs = append(targetIDs, int(gjson.ParseBytes(jr).Get("bk_asst_inst_id").Int()))
	}
	targetNames, err := cmdb.FindBkNameByBkID(logging, isProd, "yuanshen_process", targetIDs)
	if err != nil {
		return err
	}
	for _, n := range targetNames {
		cmdbInfo.ProcessName = append(cmdbInfo.ProcessName, n)
	}
	return nil
}
