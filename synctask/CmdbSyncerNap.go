package synctask

import (
	"context"
	"fmt"
	"strings"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cmdb"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/common"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"
)

// BkCmdbNapSyncer 阿里云绝区零同步器
type BkCmdbNapSyncer struct {
}

var bkCmdbNapSyncer *BkCmdbNapSyncer

// GetBkCmdbNapSyncer 获取单例
func GetBkCmdbNapSyncer() *BkCmdbNapSyncer {
	if bkCmdbNapSyncer == nil {
		bkCmdbNapSyncer = &BkCmdbNapSyncer{}
	}
	return bkCmdbNapSyncer
}

var serverShortNameMap = map[string]string{
	"dg":       "dbgate",
	"dp":       "dispatch",
	"fightmgr": "fightmgrserver",
	"gs":       "gameserver",
	"gate":     "gateserver",
	"mail":     "mailserver",
	"match":    "matchserver",
	"muip":     "muipserver",
	"ns":       "nodeserver",
	"oa":       "oaserver",
	"rank":     "rankserver",
	"room":     "roomserver",
	"sns":      "snsserver",
	"ufight":   "ufightserver",
	"fight":    "fightserver",
}

// SyncCMDBHost 用区服列表数据库的数据来同步CMDB的数据
func (m *BkCmdbNapSyncer) SyncCMDBHost(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	if !cfg.GetCmdbConfig().Enable {
		logging.Warn("current sync cmdb is disabled, skip...")
		return nil
	}

	// 先做一次批量更新，确保云管平台所有机器进入CMDB
	// 增量同步，只添加CMDB中没有的机器数据
	logging.Info("开始做CMDB增量同步")
	err := m.SyncAddHost(ctx, logging, option)
	if err != nil {
		logging.Errorf("add cmdb hosts failed, err: %v", err)
		return nil
	}

	// 先同步测试的数据，再同步正式的数据
	// 将主机绑定服务和集群
	logging.Info("开始更新CMDB测试项目数据")
	err = m.SyncCMDBHostWithEnv(ctx, logging, option, false)
	if err != nil {
		logging.Errorf("sync cmdb test data failed, err: %v", err)
	} else {
		logging.Info("更新CMDB测试项目数据完成")
	}

	logging.Info("开始更新CMDB正式项目数据")
	err = m.SyncCMDBHostWithEnv(ctx, logging, option, true)
	if err != nil {
		logging.Errorf("sync cmdb prod data failed, err: %v", err)
	} else {
		logging.Info("更新CMDB正式项目数据完成")
	}
	return nil
}

func getSetNameModulesMap(logging *logrus.Logger, regionCfg cfg.RegionConfig) (map[string]map[string]*cmdb.TopoModule, error) {
	topoData, err := cmdb.FindCMDBTopoInstByBizID(logging, regionCfg.BizID)
	if err != nil {
		return nil, err
	}
	setNameModulesMap := map[string]map[string]*cmdb.TopoModule{}
	for _, set := range topoData.TopSets {
		moduleMap := map[string]*cmdb.TopoModule{}
		for _, module := range set.Modules {
			moduleMap[module.BkInstName] = module
		}
		setNameModulesMap[set.BkInstName] = moduleMap
	}
	return setNameModulesMap, nil
}

// SyncCMDBWithEnv 根据环境来同步CMDB数据
func (m *BkCmdbNapSyncer) SyncCMDBHostWithEnv(ctx context.Context, logging *logrus.Logger, option SyncOption, isProd bool) error {
	cmdbCfg := cfg.GetCmdbConfig()
	regionCfg := cmdbCfg.OtherRegion
	if isProd {
		regionCfg = cmdbCfg.ProdRegion
	}

	if !regionCfg.Enable {
		logging.Warnf("current region sync is disabled, isProd: %v", isProd)
		return nil
	}

	// 获取集群和模块的映射关系
	// e.g. {"region_name_1": {"module_name_1": {...}, "module_name_2": {...}}}
	setNameModulesMap, err := getSetNameModulesMap(logging, regionCfg)
	if err != nil {
		return err
	}

	// 获取全部CMDB信息
	hosts, err := cmdb.FindBkHostByInstanceIDs(logging, nil, []string{"biz", "set", "module"})
	if err != nil {
		return err
	}
	hostnameMap := map[string]map[string]*cmdb.ModuleInfo{}
	hostnameIDMap := map[string]int32{}
	hostnameStateMap := map[string]string{}
	idleHostIDs := make([]int32, 0)
	for _, info := range hosts.Info {
		moduleMap := map[string]*cmdb.ModuleInfo{}
		for _, m := range info.ModuleInfo {
			moduleMap[m.BkModuleName] = m
		}
		hostnameMap[info.HostInfo.BkHostname] = moduleMap
		hostnameIDMap[info.HostInfo.BkHostname] = *info.HostInfo.BKHostID
		hostnameStateMap[info.HostInfo.BkHostname] = tea.StringValue(info.HostInfo.BkState)

		if info.GetBizID() == cmdb.IdleBizID && isHostnameMatchRegion(regionCfg, info.HostInfo.BkHostname) {
			idleHostIDs = append(idleHostIDs, *info.HostInfo.BKHostID)
		}
	}

	// 把空闲机器移动到业务，再将机器的当前状态字段改为备用机
	batchSize := 100
	for i := 0; i < len(idleHostIDs); i += batchSize {
		moves := idleHostIDs[i:min(len(idleHostIDs), i+batchSize)]
		err = cmdb.MoveMultipleHostToBizIDIdle(logging, moves, regionCfg.BizID)
		if err != nil {
			logging.Warnf("移动机器(bizID: %v)到业务失败", moves)
			continue
		}
		logging.Infof("把下面机器(%v)(共%d台),移动到业务(%d)成功", moves, len(moves), regionCfg.BizID)

		err = cmdb.ChangeMulHostsStates(logging, moves, "备用机")
		if err != nil {
			logging.Warnf("修改机器状态(bizID: %v)失败", moves)
			continue
		}
		logging.Infof("修改机器状态(%v)(共%d台)为备用机成功", moves, len(moves))
	}

	// 全量获取区服列表信息
	serverList, err := cmdb.GetServerListData(logging, isProd)
	if err != nil {
		return err
	}

	// 需要改成运营中需告警状态的机器id
	needChangeStateList := []int32{}
	// 移动主机到对应的模块中
	for _, s := range serverList {
		hostModules, ok := hostnameMap[s.Name]
		if !ok {
			// 忽略主机不存在的情况
			continue
		}

		hostState := hostnameStateMap[s.Name]
		hostID := hostnameIDMap[s.Name]
		if s.IsEnable == 1 && hostState != "运营中[需告警]" {
			needChangeStateList = append(needChangeStateList, hostID)
		}

		moduleName, ok := serverShortNameMap[s.Des]
		if !ok {
			logging.Warnf("没有办法将这个短名[%s]转化为模块名称", s.Des)
			continue
		}

		// 如果已经在对应的模块下了，跳过
		_, ok = hostModules[moduleName]
		if ok {
			continue
		}

		set, ok := setNameModulesMap[s.RegionName]
		if !ok {
			logging.Warnf("CMDB中没有找到当前集群[%s], 尝试创建集群...", s.RegionName)
			_, err := cmdb.CreateSet(logging, s.RegionName, isProd)
			if err != nil {
				logging.Warnf("创建CMDB集群[%s]失败", s.RegionName)
			}
			// 重新获取一遍集群
			setNameModulesMap, err = getSetNameModulesMap(logging, regionCfg)
			if err != nil {
				return err
			}
			set = setNameModulesMap[s.RegionName]
		}
		module, ok := set[moduleName]
		if !ok {
			logging.Warnf("集群[%s]中没有找到模块[%s]", s.RegionName, moduleName)
			continue
		}

		err := cmdb.TransferHost(logging, hostID, module.BkInstID, isProd)
		if err != nil {
			logging.Warnf("移动主机[%s]到模块中[%s]失败,错误日志: %v", s.Name, moduleName, err)
			continue
		}
		logging.Infof("移动主机[%s]到模块[%s]中成功,", s.Name, moduleName)
	}

	for i := 0; i < len(needChangeStateList); i += batchSize {
		moves := needChangeStateList[i:min(len(needChangeStateList), i+batchSize)]
		err = cmdb.ChangeMulHostsStates(logging, moves, "运营中[需告警]")
		if err != nil {
			logging.Warnf("修改机器状态(bizID: %v)失败", moves)
			continue
		}
		logging.Infof("修改机器状态(%v)(共%d台)为运营中[需告警]成功", moves, len(moves))
	}

	return nil
}

func isHostnameMatchRegion(regionCfg cfg.RegionConfig, hostname string) bool {
	envs := strings.Split(regionCfg.Envs, ",")

	if len(envs) == 0 {
		return false
	}

	hostSplit := strings.Split(hostname, "-")
	if len(hostSplit) < 2 {
		return false
	}
	hostEnv := hostSplit[1]
	for _, e := range envs {
		if e == hostEnv {
			return true
		}
	}
	return false
}

func moveToBizAndTransferAgain(logging *logrus.Logger, hostID int32, hostName string, bkModuleID int32, isProd bool) {
	err := cmdb.MoveIdleToBizIdle(logging, hostID, isProd)
	if err != nil {
		logging.Warnf("移动主机[%s]到业务空闲池失败,错误日志: %v", hostName, err)
		return
	}

	err = cmdb.TransferHost(logging, hostID, bkModuleID, isProd)
	if err != nil {
		logging.Warnf("移动主机[%s]到模块[%d]中失败,错误日志: %v", hostName, bkModuleID, err)
	}
}

func (m *BkCmdbNapSyncer) SyncAddHost(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	dbPage := uint64(1)
	dbTotal := dbSyncSize * 2
	queryCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)

	for (dbPage-1)*dbSyncSize < dbTotal {
		// 1. 搜索云管数据
		mHosts, total, err := models.HostResourceModel.Query(queryCtx, &schema.HostResourceQueryParams{
			HostResourceColumnParam: schema.HostResourceColumnParam{
				IspID:    option.IspID,
				RegionID: option.RegionID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbSyncSize,
			},
			OrderParams: schema.OrderParams{},
		})
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage += 1
		if len(mHosts) == 0 {
			continue
		}

		instanceIDs := make([]string, 0)
		for _, mHost := range mHosts {
			instanceIDs = append(instanceIDs, mHost.InstanceID)
		}

		// 2. 按照instanceID来查找CMDB主机
		queryRes, err := cmdb.FindBkHostByInstanceIDs(logging, instanceIDs, nil)
		if err != nil {
			return err
		}

		// 3. 检查是否有不存在的实例
		instanceIDMap := map[string]*cmdb.BkHost{}
		for _, info := range queryRes.Info {
			instanceIDMap[info.HostInfo.BkCloudInstID] = info.HostInfo
		}
		notExistHosts := make([]entity.HostResource, 0)
		needUpdateHosts := make([]entity.HostResource, 0)
		for _, host := range mHosts {
			if bkHost, ok := instanceIDMap[host.InstanceID]; ok {
				// 检查host是否需要更新
				if ok := cmdb.IsDiffHostEntityWithBkHost(host, bkHost); ok {
					needUpdateHosts = append(needUpdateHosts, *host)
				}
				continue
			}
			notExistHosts = append(notExistHosts, *host)
		}

		// 4. 更新实例字段, 避免存量机器字段不匹配, hook也会更新cmdb的主机数据
		if len(needUpdateHosts) != 0 {
			logging.Infof("start update diff hosts in cmdb, num of hosts is %d", len(needUpdateHosts))
			_, err = cmdb.UpdateCmdbHosts(logging, needUpdateHosts)
			if err != nil {
				logging.Errorf("update cmdb hosts failed, err: %v", err)
			}
		}

		// 5. 增加不存在实例
		if len(notExistHosts) != 0 {
			logging.Infof("start add hosts to cmdb, num of hosts is %d, total is %d", len(notExistHosts), total)
			_, err = cmdb.CreateCmdbHosts(logging, notExistHosts)
			if err != nil {
				logging.Errorf("create cmdb hosts failed, err: %v", err)
			}
		}
	}
	return nil
}

func (m *BkCmdbNapSyncer) SyncAddPolardb(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	if !cfg.GetCmdbConfig().Enable {
		logging.Warn("current sync cmdb is disabled, skip...")
		return nil
	}

	task := cmdb.CompareTask{
		OrderID:      "",
		Env:          "prod",
		RegionID:     option.RegionID,
		ResourceType: []string{"mysql"},
		InstanceID:   []string{},
		IspID:        option.IspID,
	}
	err := task.DoCompare(ctx, &common.InitProvider{
		RegionID: option.RegionID,
		Logger:   logrus.StandardLogger(),
	})
	if err != nil {
		return err
	}

	instanceIDs := []string{}
	for k := range task.Result.Mysql.Add {
		instanceIDs = append(instanceIDs, k)
	}
	customFilter := map[string]any{}
	customFilter["DBClusterId"] = bson.M{"$in": instanceIDs}
	instances, _, err := models.MysqlClusterResourceModel.Query(ctx, &schema.MysqlClusterResourceQueryParams{
		MysqlClusterResourceColumnParam: schema.MysqlClusterResourceColumnParam{
			RegionID: option.RegionID,
			IspID:    option.IspID,
		},
		PaginationParam: schema.PaginationParam{
			Page: 0,
			Size: 9999,
		},
		OrderParams: schema.OrderParams{},
	}, customFilter)
	if err != nil {
		return err
	}
	for _, instance := range instances {
		_, err := cmdb.AddBkCmdbObj(ctx, cmdb.ALIPolarDB, &cmdb.RdsCreation{
			BkInstName:      instance.DBClusterID,
			CreateTime:      instance.CreateTime,
			DBEngine:        instance.Engine,
			DBEngineVersion: instance.DBVersion,
			DBType:          instance.DBType,
			InstanceClass:   instance.DBNodeClass,
			InstanceID:      instance.DBClusterID,
			InstanceName:    instance.DBClusterDescription,
			InstanceStatus:  strings.ToLower(instance.DBClusterStatus),
			ProviderName:    option.IspType,
			RegionID:        option.RegionID,
			ZoneID:          instance.ZoneID,
			VpcID:           instance.VpcID,
		})
		if err != nil {
			logging.Errorf("add cmdb rds failed, err: %v", err)
			return err
		}
	}

	return nil
}

func (m *BkCmdbNapSyncer) SyncAddRedis(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	if !cfg.GetCmdbConfig().Enable {
		logging.Warn("current sync cmdb is disabled, skip...")
		return nil
	}

	task := cmdb.CompareTask{
		OrderID:      "",
		Env:          "prod",
		RegionID:     option.RegionID,
		ResourceType: []string{"redis"},
		InstanceID:   []string{},
		IspID:        option.IspID,
	}
	err := task.DoCompare(ctx, &common.InitProvider{
		RegionID: option.RegionID,
		Logger:   logrus.StandardLogger(),
	})
	if err != nil {
		return err
	}

	instanceIDs := []string{}
	for k := range task.Result.Redis.Add {
		instanceIDs = append(instanceIDs, k)
	}
	customFilter := map[string]any{}
	customFilter["InstanceID"] = bson.M{"$in": instanceIDs}
	instances, _, err := models.RedisResourceModel.Query(ctx, &schema.RedisResourceQueryParams{
		RedisResourceColumnParam: schema.RedisResourceColumnParam{
			RegionID: option.RegionID,
			IspID:    option.IspID,
		},
		PaginationParam: schema.PaginationParam{
			Page: 0,
			Size: 9999,
		},
		OrderParams: schema.OrderParams{},
	}, customFilter)
	if err != nil {
		return err
	}
	for _, instance := range instances {
		_, err := cmdb.AddBkCmdbObj(ctx, cmdb.ALIKVStore, &cmdb.RedisCreation{
			BkInstName:      instance.InstanceID,
			InstanceID:      instance.InstanceID,
			InstanceName:    instance.InstanceName,
			ProviderName:    option.IspType,
			VpcID:           instance.VpcID,
			VSwitchID:       instance.VSwitchID,
			RegionID:        option.RegionID,
			ZoneID:          instance.ZoneID,
			CreateTime:      instance.CreateTime,
			InstanceClass:   instance.InstanceClass,
			InstanceStatus:  strings.ToLower(instance.InstanceStatus),
			DBEngine:        instance.InstanceType,
			DBEngineVersion: instance.EngineVersion,
		})
		if err != nil {
			logging.Errorf("add cmdb redis failed, err: %v", err)
			return err
		}
	}

	return nil
}

func (m *BkCmdbNapSyncer) SyncLB(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	if !cfg.GetCmdbConfig().Enable {
		logging.Warn("current sync cmdb is disabled, skip...")
		return nil
	}

	task := cmdb.CompareTask{
		OrderID:      "",
		Env:          "prod",
		RegionID:     option.RegionID,
		ResourceType: []string{"lb"},
		InstanceID:   []string{},
		IspID:        option.IspID,
	}
	err := task.DoCompare(ctx, &common.InitProvider{
		RegionID: option.RegionID,
		Logger:   logrus.StandardLogger(),
	})
	if err != nil {
		return err
	}

	instanceIDs := []string{}
	for k := range task.Result.LB.Add {
		instanceIDs = append(instanceIDs, k)
	}
	customFilter := map[string]any{}
	customFilter["InstanceID"] = bson.M{"$in": instanceIDs}
	instances, _, err := models.LoadBalancerModel.Query(ctx, &schema.LoadBalancerQueryParams{
		LoadBalancerColumnParam: schema.LoadBalancerColumnParam{
			RegionID: option.RegionID,
			IspID:    option.IspID,
		},
		PaginationParam: schema.PaginationParam{
			Page: 0,
			Size: 9999,
		},
		OrderParams: schema.OrderParams{},
	}, customFilter)
	if err != nil {
		return err
	}
	for _, instance := range instances {
		_, err := cmdb.AddBkCmdbObj(ctx, cmdb.ALILB, &cmdb.AliLB{
			BkAssetID:  instance.LoadBalancerID,
			BkInstName: instance.LoadBalancerName,
			BKSN:       instance.LoadBalancerID,
			BkModel:    fmt.Sprintf("%s-%s", instance.LoadBalancerType, instance.LoadBalancerSpec),
			BkVendor:   instance.IspType,
			RegionID:   instance.RegionID,
		})
		if err != nil {
			logging.Errorf("add cmdb lb failed, err: %v", err)
			return err
		}
	}

	return nil
}

// SyncHostBkCMDB 更新主机的BkCMDB字段信息
func (m *BkCmdbNapSyncer) SyncHostBkCMDB(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	if !cfg.GetCmdbConfig().Enable {
		logging.Warn("current sync cmdb is disabled, skip...")
		return nil
	}
	logging.Info("开始更新主机的CMDB字段信息")

	dbPage := uint64(1)
	dbTotal := dbPageSize * 2

	queryCtx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)

	for (dbPage-1)*dbPageSize < dbTotal {
		// 1. 搜索云管数据
		mHosts, total, err := models.HostResourceModel.Query(queryCtx, &schema.HostResourceQueryParams{
			HostResourceColumnParam: schema.HostResourceColumnParam{
				IspID:    option.IspID,
				RegionID: option.RegionID,
			},
			PaginationParam: schema.PaginationParam{
				Page: dbPage,
				Size: dbPageSize,
			},
			OrderParams: schema.OrderParams{},
		})
		if err != nil {
			return err
		}
		dbTotal = total
		dbPage++
		if len(mHosts) == 0 {
			continue
		}

		instanceIDs := make([]string, 0)
		for _, mHost := range mHosts {
			instanceIDs = append(instanceIDs, mHost.InstanceID)
		}

		// 2. 按照instanceID来查找CMDB主机
		queryRes, err := cmdb.FindBkHostByInstanceIDs(logging, instanceIDs, nil)
		if err != nil {
			return err
		}

		// 3. 更新hostresource的bkcmdb字段
		cmdbMap := map[string]entity.BkCmdbInfo{}
		for _, q := range queryRes.Info {
			if q.HostInfo == nil {
				continue
			}
			cmdbMap[q.HostInfo.BkCloudInstID] = entity.BkCmdbInfo{
				HostID: *q.HostInfo.BKHostID,
			}
		}
		updateErr := models.HostResourceModel.UpdateCmdbInfo(ctx, cmdbMap)
		if updateErr != nil {
			logging.Warnf("SyncHost.UpdateCmdbInfo.error: %s", updateErr.Error())
		}
	}
	logging.Info("更新主机的CMDB字段信息完成")

	return nil
}

func (m *BkCmdbNapSyncer) DiffUpdate(ctx context.Context, logging *logrus.Logger, isDryRun bool) ([]string, error) {
	resCount := 0
	maxloop := 200
	updateInstanceNames := make([]string, 0)
	for i := 1; i <= maxloop; i++ {
		mHosts, total, err := models.HostResourceModel.Query(ctx, &schema.HostResourceQueryParams{
			HostResourceColumnParam: schema.HostResourceColumnParam{},
			PaginationParam: schema.PaginationParam{
				Page: uint64(i),
				Size: dbSyncSize,
			},
			OrderParams: schema.OrderParams{},
		})
		if err != nil {
			return nil, err
		}
		if len(mHosts) == 0 {
			break
		}
		resCount += len(mHosts)
		instanceIDs := make([]string, 0)
		for _, mHost := range mHosts {
			instanceIDs = append(instanceIDs, mHost.InstanceID)
		}

		queryRes, err := cmdb.FindBkHostByInstanceIDs(logging, instanceIDs, nil)
		if err != nil {
			return nil, err
		}
		instanceIDMap := map[string]*cmdb.BkHost{}
		for _, info := range queryRes.Info {
			instanceIDMap[info.HostInfo.BkCloudInstID] = info.HostInfo
		}

		needUpdateHosts := make([]entity.HostResource, 0)
		for _, host := range mHosts {
			bkHost, ok := instanceIDMap[host.InstanceID]
			if !ok {
				continue
			}
			// 检查host是否需要更新
			if ok := cmdb.IsDiffHostEntityWithBkHost(host, bkHost); ok {
				needUpdateHosts = append(needUpdateHosts, *host)
				updateInstanceNames = append(updateInstanceNames, host.InstanceName)
			}
		}

		if len(needUpdateHosts) != 0 && !isDryRun {
			cmdbMap := map[string]entity.BkCmdbInfo{}
			for idx, host := range needUpdateHosts {
				bkHost, ok := instanceIDMap[host.InstanceID]
				if !ok {
					continue
				}
				cmdbMap[host.InstanceID] = entity.BkCmdbInfo{
					HostID: tea.Int32Value(bkHost.BKHostID),
				}
				host.BkCmdb = &entity.BkCmdbInfo{
					HostID: tea.Int32Value(bkHost.BKHostID),
				}
				needUpdateHosts[idx] = host
			}
			updateErr := models.HostResourceModel.UpdateCmdbInfo(ctx, cmdbMap)
			if updateErr != nil {
				logging.Warnf("SyncHost.UpdateCmdbInfo.error: %s", updateErr.Error())
			}

			logging.Infof("start update diff hosts in cmdb, num of hosts is %d", len(needUpdateHosts))
			_, err = cmdb.UpdateCmdbHosts(logging, needUpdateHosts)
			if err != nil {
				logging.Errorf("update cmdb hosts failed, err: %v", err)
			}
		}

		if uint64(resCount) >= total {
			break
		}
	}
	return updateInstanceNames, nil
}
