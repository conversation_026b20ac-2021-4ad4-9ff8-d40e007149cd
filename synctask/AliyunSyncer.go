package synctask

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/rds"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cmdb"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
	"platgit.mihoyo.com/jql-ops/op-cloudman/schema"

	alb20200616 "github.com/alibabacloud-go/alb-20200616/v2/client"
	"github.com/alibabacloud-go/ecs-20140526/v2/client"
	ecs20140526V3 "github.com/alibabacloud-go/ecs-20140526/v3/client"
	ram20150501 "github.com/alibabacloud-go/ram-20150501/v2/client"
	slb20140515 "github.com/alibabacloud-go/slb-20140515/v4/client"
	"github.com/alibabacloud-go/tea/tea"
	vpc20160428 "github.com/alibabacloud-go/vpc-20160428/v5/client"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent"
)

// AliyunSyncer 阿里云同步器
type AliyunSyncer struct {
}

var aliyunSyncer *AliyunSyncer

// GetAliyunSyncer 获取单例AliyunSyncer
func GetAliyunSyncer() *AliyunSyncer {
	if aliyunSyncer == nil {
		aliyunSyncer = &AliyunSyncer{}
	}
	return aliyunSyncer
}

// TaskSet 获取AliyunSyncer支持的同步方法组
func (a *AliyunSyncer) TaskSet() map[string]string {
	return map[string]string{
		"同步阿里云ECS信息":     SyncHost,
		"同步阿里云KvStore信息": SyncRedis,
		"同步阿里云PolarDB信息": SyncMysql,
		"同步阿里云安全组信息":     SyncSecurityGroup,
		"同步阿里云负载均衡信息":    SyncLoadBalancer,
		"同步阿里云弹性公网IP信息":  SyncEIP,
		"同步阿里云权限策略组信息":   SyncPolicy,
	}
}

// SyncHost 同步云主机
func (a *AliyunSyncer) SyncHost(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging.Infof("准备从阿里云获取ecs信息....，InstanceIDs: %v", instanceIDs)

	aliEcsClient, err := NewAliEcsClient(option)
	if err != nil {
		logging.Errorf("init client fail:%v", err)
		return err
	}
	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("20060102150405")

	// 只有全量更新才打清理标签清理
	if len(instanceIDs) == 0 {
		err = syncAllHostFromCloud(ctx, aliEcsClient, logging, syncUpdateVersion, option)
		if err != nil {
			return err
		}
	} else {
		err = syncPartialHostFromCloud(ctx, aliEcsClient, logging, syncUpdateVersion, option, instanceIDs)
		if err != nil {
			return err
		}
	}

	// 用区服列表的信息来更新CMDB的数据
	err = GetBkCmdbNapSyncer().SyncCMDBHost(ctx, logging, option)
	if err != nil {
		return err
	}

	// syncHost自身逻辑跑完，跟一个cmdb绑定信息的同步
	err = GetBkCmdbNapSyncer().SyncHostBkCMDB(ctx, logging, option)
	if err != nil {
		return err
	}
	return nil
}

func checkECSTag(tags *client.DescribeInstancesResponseBodyInstancesInstanceTags) bool {
	systemConfig := cfg.GetSystemConfig()
	if systemConfig.SyncEnv == "" {
		return true
	}
	if tags == nil || len(tags.Tag) == 0 {
		return false
	}

	envTag := ""
	platformEnvTag := []string{}
	for _, tag := range tags.Tag {
		if tea.StringValue(tag.TagKey) == "env" {
			envTag = tea.StringValue(tag.TagValue)
		} else if tea.StringValue(tag.TagKey) == "platform_env" {
			platformEnvTag = strings.Split(tea.StringValue(tag.TagValue), ",")
		}
	}

	switch systemConfig.SyncEnv {
	case cfg.SyncEnvProd:
		return slices.Contains(constant.EnvProdTags, envTag)
	case cfg.SyncEnvTest:
		if len(platformEnvTag) > 0 {
			return slices.Contains(platformEnvTag, cfg.SyncEnvTest)
		}
		return slices.Contains(constant.EnvTestTags, envTag)
	default:
		return true
	}
}

func syncAllHostFromCloud(ctx context.Context, aliEcsClient *alicloud.AliEcsClient, logging *logrus.Logger, syncUpdateVersion string, option SyncOption) error {
	// 获取ispid的管理编号
	var accountIndexID uint32
	var accountEntity entity.Account
	accEnt := entity.GetAccountCollection(models.GetEngine())
	_, err := models.FindPK(ctx, accEnt, option.IspID, &accountEntity)
	if err == nil {
		accountIndexID = accountEntity.IndexID
	} else {
		logging.Errorf("cannot get accountIndexID, err: %s", err.Error())
		return err
	}

	// 循环变量初始化
	nextToken := ""
	maxSize := 100
	maxLoop := 1000
	var totalSave int
	var result *models.BatchResult

	// 循环遍历所有实例
	for i := 0; i <= maxLoop; i++ {
		response, err := aliEcsClient.DescribeAllInstances(ctx, int32(maxSize), nextToken)
		if err != nil {
			logging.Errorf("aliEcsClient.DescribeInstances.fail:%+v", err)
			return err
		}
		if response.NextToken != nil {
			nextToken = tea.StringValue(response.NextToken)
		}
		Instances := response.Instances
		logging.Infof("aliEcsClient.DescribeInstances: response.Length %d, response.TotalCount:%v, region:%v\n", len(Instances.Instance), tea.Int32Value(response.TotalCount), option.RegionID)

		var updateData []*entity.HostResource
		for _, instance := range Instances.Instance {
			if !checkECSTag(instance.Tags) {
				logging.Warnf("instance %s不属于该环境,跳过", *instance.InstanceName)
				continue
			}
			inInterface := AliDescribeInstanceBodyToHost(instance, option.IspID)
			// 标记版本号，版本号不一致的最后统一标记为待清理
			inInterface.UpdateVersion = syncUpdateVersion
			// 注入agentID值
			if len(inInterface.InnerIPAddress) > 0 && inInterface.AgentID == "" {
				// 多内网IP,仅使用第一个生成
				agentID, genErr := agent.GenerateID(inInterface.RegionID, fmt.Sprintf("%s:%d", inInterface.InnerIPAddress[0], accountIndexID))
				if genErr == nil {
					inInterface.AgentID = agentID
				}
			}
			// insert initTreeNode
			inInterface.InitTreeNode = getTreeNode("host", inInterface.InstanceName)
			updateData = append(updateData, &inInterface)
		}

		totalSave += len(updateData)
		logging.Infof("totalSave.num: %d\n", totalSave)

		result, err = models.HostResourceModel.BatchCreateOrUpdate(ctx, updateData, models.HostUpdateOption{
			UpdateTags: true,
			UpdateDesc: true,
			FromSync:   true,
		})
		if err != nil {
			logging.Errorf("HostResourceModel.BatchCreateOrUpdate.fail: %+v***%+v", result, err.Error())
			break
		} else {
			logging.Infof("HostResourceModel.BatchCreateOrUpdate.ok: %+v", result)
		}

		if nextToken == "" {
			break
		}
	}
	logging.Infof("aliEcs同步共写入数据%v条", totalSave)

	cleanupAmount, err := models.HostResourceModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
	if err != nil {
		logging.Errorf("HostResourceModel.MarkCleanup.failed: %s", err.Error())
		return err
	}
	logging.Infof("HostResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	return nil
}

func syncPartialHostFromCloud(ctx context.Context, aliEcsClient *alicloud.AliEcsClient, logging *logrus.Logger, syncUpdateVersion string, option SyncOption, instanceIDs []string) error {
	// 获取ispid的管理编号
	var accountIndexID uint32
	var accountEntity entity.Account
	accEnt := entity.GetAccountCollection(models.GetEngine())
	_, err := models.FindPK(ctx, accEnt, option.IspID, &accountEntity)
	if err == nil {
		accountIndexID = accountEntity.IndexID
	} else {
		logging.Errorf("cannot get accountIndexID, err: %s", err.Error())
		return err
	}

	// 循环变量初始化
	var totalSave int
	var result *models.BatchResult

	for i := 0; i < len(instanceIDs); i += 100 {
		response, err := aliEcsClient.DescribeInstances(ctx, 1, 100, "", instanceIDs[i:min(i+100, len(instanceIDs))]...)
		if err != nil {
			logging.Errorf("aliEcsClient.DescribeInstances.fail:%+v", err)
			return err
		}
		Instances := response.Instances
		logging.Infof("aliEcsClient.DescribeInstances: response.Length %d, response.TotalCount:%v, region:%v\n", len(Instances.Instance), tea.Int32Value(response.TotalCount), option.RegionID)

		var updateData []*entity.HostResource
		for _, instance := range Instances.Instance {
			if !checkECSTag(instance.Tags) {
				logging.Warnf("instance %s不属于该环境,跳过", *instance.InstanceName)
				continue
			}
			inInterface := AliDescribeInstanceBodyToHost(instance, option.IspID)
			// 标记版本号，版本号不一致的最后统一标记为待清理
			inInterface.UpdateVersion = syncUpdateVersion
			// 注入agentID值
			if len(inInterface.InnerIPAddress) > 0 && inInterface.AgentID == "" {
				// 多内网IP,仅使用第一个生成
				agentID, genErr := agent.GenerateID(inInterface.RegionID, fmt.Sprintf("%s:%d", inInterface.InnerIPAddress[0], accountIndexID))
				if genErr == nil {
					inInterface.AgentID = agentID
				}
			}
			// insert initTreeNode
			inInterface.InitTreeNode = getTreeNode("host", inInterface.InstanceName)
			updateData = append(updateData, &inInterface)
		}

		totalSave += len(updateData)
		result, err = models.HostResourceModel.BatchCreateOrUpdate(ctx, updateData, models.HostUpdateOption{
			UpdateTags: true,
			UpdateDesc: true,
			FromSync:   true,
		})
		if err != nil {
			logging.Errorf("HostResourceModel.BatchCreateOrUpdate.fail: %+v***%+v", result, err.Error())
			break
		} else {
			logging.Infof("HostResourceModel.BatchCreateOrUpdate.ok: %+v", result)
		}
	}
	logging.Infof("aliEcs同步共写入数据%v条", totalSave)
	return nil
}

// AliDescribeInstanceBodyToHost ...
func AliDescribeInstanceBodyToHost(instance *client.DescribeInstancesResponseBodyInstancesInstance, accountID string) entity.HostResource {
	var inInterface entity.HostResource
	inrec, _ := json.Marshal(&instance)
	json.Unmarshal(inrec, &inInterface)
	if tea.StringValue(instance.InstanceNetworkType) == "vpc" {
		inInterface.InnerIPAddress = tea.StringSliceValue(instance.VpcAttributes.PrivateIpAddress.IpAddress)
	}
	if instance.PublicIpAddress != nil && len(instance.PublicIpAddress.IpAddress) > 0 {
		inInterface.PublicIPAddress = tea.StringSliceValue(instance.PublicIpAddress.IpAddress)
	}
	if instance.RdmaIpAddress != nil {
		inInterface.RdmaIPAddress = tea.StringSliceValue(instance.RdmaIpAddress.IpAddress)
	}
	// if len(inInterface.PublicIPAddress) == 0 && inInterface.EipAddress.IPAddress == "" {
	// 	inInterface.PublicIPAddress = append(inInterface.PublicIPAddress, "127.0.0.1")
	// }

	creationTime, _ := time.Parse("2006-01-02T15:04Z", tea.StringValue(instance.CreationTime))
	inInterface.CreationTime = creationTime.Unix()
	expireTime, _ := time.Parse("2006-01-02T15:04Z", tea.StringValue(instance.ExpiredTime))
	inInterface.ExpiredTime = expireTime.Unix()
	inInterface.IspID = accountID
	inInterface.IspType = "aliyun"
	return inInterface
}

// ignoreEc2Inst 是否忽略实例
func (a *AliyunSyncer) ignoreEcsInst(skipRules []string, instanceName string) bool {
	for _, skipRule := range skipRules {
		filterPattern := fmt.Sprintf("-%s-", skipRule)
		if strings.Contains(instanceName, filterPattern) {
			return true
		}
	}
	return false
}

// SyncRedis 同步Redis信息
func (a *AliyunSyncer) SyncRedis(ctx context.Context, logging *logrus.Logger, option SyncOption, inputInstanceIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging.Infoln("准备从阿里云获取kvstore(redis)信息....")

	client, err := NewAliKvStoreClient(option)
	if err != nil {
		return err
	}

	kvStoreClient := syncAliKvStore{
		client:  client,
		logging: logging,
		option:  option,
	}
	instanceIDs, err := kvStoreClient.syncAliKvStoreInstances(ctx, inputInstanceIDs...)
	if err != nil {
		return err
	}

	// 用区服列表的信息来更新CMDB的数据
	err = GetBkCmdbNapSyncer().SyncAddRedis(ctx, logging, option)
	if err != nil {
		return err
	}

	err = kvStoreClient.syncAliKvStoreAccounts(ctx, instanceIDs)

	return err
}

// SyncMysql xxx
func (a *AliyunSyncer) SyncMysql(ctx context.Context, logging *logrus.Logger, option SyncOption, clusterIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging.Infoln("同步阿里云PolarDB信息")

	polarDBClient := NewAliPolarDBClient(option)

	PolarDBClient := SyncPolarDB{
		client:  polarDBClient,
		logging: logging,
		option:  option,
	}

	clusterIDs, err := PolarDBClient.SyncCluster(ctx, clusterIDs...)
	if err != nil {
		return err
	}

	// // TODO: Database同步可分离???或增加同步重试
	err = PolarDBClient.SyncDatabase(ctx, clusterIDs)
	if err != nil {
		return err
	}

	err = PolarDBClient.SyncEndpoint(ctx, clusterIDs)
	if err != nil {
		return err
	}

	err = PolarDBClient.syncAccount(ctx, clusterIDs)
	if err != nil {
		return err
	}

	// 用区服列表的信息来更新CMDB的数据
	err = GetBkCmdbNapSyncer().SyncAddPolardb(ctx, logging, option)
	if err != nil {
		return err
	}

	err = GetBkCmdbSyncer().SyncMysql(ctx, logging, option, clusterIDs...)
	if err != nil {
		return err
	}

	return err
}

// AliSecurityGroupToEntity ...
func AliSecurityGroupToEntity(instance *client.DescribeSecurityGroupsResponseBodySecurityGroupsSecurityGroup) entity.SecurityGroup {
	var inInterface entity.SecurityGroup
	inInterface.VpcID = tea.StringValue(instance.VpcId)
	inInterface.SecurityGroupID = tea.StringValue(instance.SecurityGroupId)
	inInterface.SecurityGroupName = tea.StringValue(instance.SecurityGroupName)
	inInterface.SecurityGroupType = tea.StringValue(instance.SecurityGroupType)
	inInterface.ResourceGroupID = tea.StringValue(instance.ResourceGroupId)
	inInterface.Description = tea.StringValue(instance.Description)
	inInterface.EcsCount = int(tea.Int32Value(instance.EcsCount))
	inInterface.ServiceID = tea.Int64Value(instance.ServiceID)
	inInterface.AvailableInstanceAmount = int(tea.Int32Value(instance.AvailableInstanceAmount))
	inInterface.CreationTime = tea.StringValue(instance.CreationTime)
	var tags []entity.Tag
	for _, tag := range instance.Tags.Tag {
		tags = append(tags, entity.Tag{TagKey: tea.StringValue(tag.TagKey), TagValue: tea.StringValue(tag.TagValue)})
	}
	inInterface.Tags = tags
	return inInterface
}

// SyncSecurityGroup xxx
func (a *AliyunSyncer) SyncSecurityGroup(ctx context.Context, logging *logrus.Logger, option SyncOption, securityGroupIDs ...string) error {
	// 同时同步下面三个安全组资源, ecs安全组, polarDB白名单和负载均衡ACL

	var wg sync.WaitGroup
	errList := []error{nil, nil, nil}
	wg.Add(1)
	go func() {
		defer wg.Done()
		err := a.SyncECSSecurityGroup(ctx, logging, option, securityGroupIDs...)
		if err != nil {
			errList[0] = err
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		err := a.SyncPolarDBWhitelist(ctx, logging, option)
		if err != nil {
			errList[1] = err
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		err := a.SyncAlbACL(ctx, logging, option)
		if err != nil {
			errList[2] = err
		}
	}()
	wg.Wait()

	errorsMessage := ""
	for _, err := range errList {
		if err != nil {
			errorsMessage += err.Error()
		}
	}

	if errorsMessage != "" {
		return errors.New(errorsMessage)
	}
	return nil
}

func (a *AliyunSyncer) SyncPolarDBWhitelist(ctx context.Context, logging *logrus.Logger, option SyncOption, whitelistIDs ...string) error {
	polardbClient, err := rds.GetAliPolarDB(ctx, option.RegionID, option.IspID)
	if err != nil {
		return err
	}

	resp, err := polardbClient.ListAllGlobalSecurityIPGroup(ctx)
	if err != nil {
		return err
	}

	whitelistIDMap := make(map[string]interface{})
	if len(whitelistIDs) != 0 {
		for _, id := range whitelistIDs {
			whitelistIDMap[id] = true
		}
	}

	entities := make([]*entity.IPWhitelist, 0)
	for _, whitelist := range resp.Body.GlobalSecurityIPGroup {
		if len(whitelistIDs) != 0 {
			if _, ok := whitelistIDMap[tea.StringValue(whitelist.GlobalSecurityGroupId)]; !ok {
				continue
			}
		}
		e := &entity.IPWhitelist{
			GlobalIgName:          tea.StringValue(whitelist.GlobalIgName),
			GIPList:               tea.StringValue(whitelist.GIpList),
			GlobalSecurityGroupID: tea.StringValue(whitelist.GlobalSecurityGroupId),
			RegionID:              tea.StringValue(whitelist.RegionId),
			DBInstances:           tea.StringSliceValue(whitelist.DBInstances),
			IspID:                 option.IspID,
			IspType:               option.IspType,
		}
		entities = append(entities, e)
	}

	batchRes, err := models.IPWhitelistModel.BatchCreateOrUpdate(ctx, entities)
	if err != nil {
		logging.Errorf("IPWhitelistModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}
	logging.Infof("IPWhitelistModel.BatchCreateOrUpdate.ok: %+v", batchRes)
	return nil
}

func (a *AliyunSyncer) SyncAlbACL(ctx context.Context, logging *logrus.Logger, option SyncOption, aclIDs ...string) error {
	albClient, err := alicloud.NewAliLoadBalancerClient(option.RegionID, option.IspID)
	if err != nil {
		return err
	}

	acls, err := albClient.ListALBAcls(ctx, aclIDs...)
	if err != nil {
		return err
	}

	aclIDMap := make(map[string]interface{})
	if len(aclIDs) != 0 {
		for _, id := range aclIDs {
			aclIDMap[id] = true
		}
	}

	// entities := make([]*entity.AlbACL, 0)
	for _, acl := range acls {
		if len(aclIDs) != 0 {
			if _, ok := aclIDMap[tea.StringValue(acl.AclId)]; !ok {
				continue
			}
		}

		e := &entity.AlbACL{
			ACLStatus:        tea.StringValue(acl.AclStatus),
			ResourceGroupID:  tea.StringValue(acl.ResourceGroupId),
			ACLID:            tea.StringValue(acl.AclId),
			AddressIPVersion: tea.StringValue(acl.AddressIPVersion),
			CreateTime:       tea.StringValue(acl.CreateTime),
			ACLName:          tea.StringValue(acl.AclName),
			AclEntries:       []*entity.ACLEntry{},
			RelatedListeners: []*entity.RelatedListener{},
		}

		entries, err := albClient.ListAclEntries(ctx, tea.StringValue(acl.AclId))
		if err != nil {
			return err
		}
		for _, entry := range entries {
			e.AclEntries = append(e.AclEntries, &entity.ACLEntry{
				Status:      tea.StringValue(entry.Status),
				Entry:       tea.StringValue(entry.Entry),
				Description: tea.StringValue(entry.Description),
			})
		}
		relations, err := albClient.ListAclRelatedListeners(ctx, []string{tea.StringValue(acl.AclId)})
		if err != nil {
			return err
		}
		for _, relation := range relations {
			e.RelatedListeners = append(e.RelatedListeners, &entity.RelatedListener{
				Status:           tea.StringValue(relation.Status),
				ListenerPort:     tea.Int32Value(relation.ListenerPort),
				LoadBalancerID:   tea.StringValue(relation.LoadBalancerId),
				ListenerProtocol: tea.StringValue(relation.ListenerProtocol),
				ListenerID:       tea.StringValue(relation.ListenerId),
			})
		}
		e.IspID = option.IspID
		e.RegionID = option.RegionID
		e.IspType = "aliyun"

		// 由于变更acl entry也会进行同步，为了两边数据一致，这里选择一个一个更新
		cuRes, err := models.AlbACLModel.CreateOrUpdate(ctx, e)
		if err != nil {
			logging.Errorf("AlbACLModel.CreateOrUpdate.fail: %s***%+v", cuRes, err.Error())
		}
		logging.Infof("AlbACLModel.CreateOrUpdate.ok: aclID: %s, action: %s", tea.StringValue(acl.AclId), cuRes)
	}
	logging.Info("Sync Alb ACL finished")
	return nil
}

func (a *AliyunSyncer) syncVpcSecurityGroup(ctx context.Context, logging *logrus.Logger, option SyncOption, aliEcsClient *alicloud.AliEcsClient, vpcID, syncUpdateVersion string, securityGroupIDs ...string) error {
	sgResp, err := aliEcsClient.GetSecurityGroup(ctx, vpcID, securityGroupIDs...)
	if err != nil {
		return err
	}

	var sgEntities []*entity.SecurityGroup
	for _, sg := range sgResp {
		sgAttr, err := aliEcsClient.GetSecurityGroupAttribute(ctx, tea.StringValue(sg.SecurityGroupId))
		if err != nil {
			return err
		}

		sgEntity := AliSecurityGroupToEntity(sg)
		sgEntity.IspType = "aliyun"
		sgEntity.IspID = option.IspID
		sgEntity.RegionID = option.RegionID
		// 标记版本号，版本号不一致的最后统一标记为待清理
		sgEntity.UpdateVersion = syncUpdateVersion
		sgEntity.Permissions, err = AliPermissionToEntity(sgAttr.Permissions)
		if err != nil {
			return err
		}
		sgEntities = append(sgEntities, &sgEntity)
	}

	batchRes, err := models.SecurityGroupModel.BatchCreateOrUpdate(ctx, sgEntities)
	if err != nil {
		logging.Errorf("SecurityGroupModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}
	logging.Infof("SecurityGroupModel.BatchCreateOrUpdate.ok: %+v", batchRes)
	return nil
}

func AliPermissionToEntity(p *ecs20140526V3.DescribeSecurityGroupAttributeResponseBodyPermissions) ([]entity.SecurityGroupPermission, error) {
	sgPermEntities := make([]entity.SecurityGroupPermission, 0)
	for _, perm := range p.Permission {
		sgPermRaw, _ := json.Marshal(perm)
		sgPermEntity := entity.SecurityGroupPermission{}
		err := json.Unmarshal(sgPermRaw, &sgPermEntity)
		if err != nil {
			return nil, err
		}
		sgPermEntities = append(sgPermEntities, sgPermEntity)
	}
	return sgPermEntities, nil
}

func (a *AliyunSyncer) SyncECSSecurityGroup(ctx context.Context, logging *logrus.Logger, option SyncOption, securityGroupIDs ...string) error {
	aliEcsClient, err := NewAliEcsClient(option)
	if err != nil {
		logging.Errorf("init client fail:%v", err)
		return err
	}
	vpcResp, err := aliEcsClient.GetVPCs(ctx, 1, 50)
	if err != nil {
		return err
	}

	var vpcIDs []string
	for _, vpc := range vpcResp.Vpcs.Vpc {
		vpcIDs = append(vpcIDs, tea.StringValue(vpc.VpcId))
	}

	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("20060102150405")
	for _, vpcID := range vpcIDs {
		err := a.syncVpcSecurityGroup(ctx, logging, option, aliEcsClient, vpcID, syncUpdateVersion, securityGroupIDs...)
		if err != nil {
			return err
		}
	}

	// 只有全量更新才打清理标签清理
	if len(securityGroupIDs) == 0 {
		cleanupAmount, err := models.SecurityGroupModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("SecurityGroupModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("SecurityGroupModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}

	return nil
}

// SyncLoadBalancer 同步负载均衡
func (a *AliyunSyncer) SyncLoadBalancer(ctx context.Context, logging *logrus.Logger, option SyncOption, loadBalancerIDs ...string) error {
	syncUpdateVersion := time.Now().Format("20060102150405")
	err := a.SyncApplicationLoadBalancer(ctx, logging, option, syncUpdateVersion, loadBalancerIDs...)
	if err != nil {
		return err
	}

	err = a.syncALBServerGroup(ctx, logging, option)
	if err != nil {
		return err
	}

	err = a.syncClassicLoadBalancer(ctx, logging, option, syncUpdateVersion, loadBalancerIDs...)
	if err != nil {
		return err
	}

	// 只有全量更新才打清理标签清理
	if len(loadBalancerIDs) == 0 {
		cleanupAmount, err := models.LoadBalancerModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("LoadBalancerModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("LoadBalancerModel.MarkCleanup, 标记需要清理的数量%d个", cleanupAmount)
	}

	// 同步数据到CMDB中
	err = GetBkCmdbNapSyncer().SyncLB(ctx, logging, option)
	if err != nil {
		logging.Errorf("同步CMDB失败, err: %v", err)
	}
	return nil
}

func checkLbTag(tags []*alb20200616.ListLoadBalancersResponseBodyLoadBalancersTags) bool {
	systemConfig := cfg.GetSystemConfig()
	if systemConfig.SyncEnv == "" {
		return true
	}

	envTag := ""
	for _, tag := range tags {
		if tea.StringValue(tag.Key) == "env" {
			envTag = tea.StringValue(tag.Value)
			break
		}
	}
	switch systemConfig.SyncEnv {
	case cfg.SyncEnvProd:
		return slices.Contains(constant.EnvProdTags, envTag)
	case cfg.SyncEnvTest:
		return slices.Contains(constant.EnvTestTags, envTag)
	default:
		return true
	}
}

func (a *AliyunSyncer) SyncApplicationLoadBalancer(ctx context.Context, logging *logrus.Logger, option SyncOption, syncUpdateVersion string, loadBalancerIDs ...string) error {
	aliLoadBalancerClient, err := alicloud.NewAliLoadBalancerClient(option.RegionID, option.IspID)
	if err != nil {
		logging.Errorf("init client fail:%v", err)
		return err
	}

	albs, err := aliLoadBalancerClient.ListAllALBs(ctx, loadBalancerIDs...)
	if err != nil {
		return err
	}

	lbEntities := make([]*entity.LoadBalancer, 0)
	lbIDs := make([]string, 0)
	lbMap := make(map[string]*entity.LoadBalancer)
	for _, alb := range albs {
		if !checkLbTag(alb.Tags) {
			logging.Warnf("instance %s不属于该环境,跳过", *alb.LoadBalancerName)
			continue
		}
		albAttr, err := aliLoadBalancerClient.GetALBAttribute(ctx, tea.StringValue(alb.LoadBalancerId))
		if err != nil {
			return err
		}
		lbEntity := AliALBToEntity(albAttr)
		lbEntity.IspID = option.IspID
		lbEntity.IspType = "aliyun"
		lbEntity.UpdateVersion = syncUpdateVersion
		lbEntities = append(lbEntities, &lbEntity)
		lbIDs = append(lbIDs, tea.StringValue(alb.LoadBalancerId))
	}
	for _, lbEntity := range lbEntities {
		lbMap[lbEntity.LoadBalancerID] = lbEntity
	}

	listeners, err := aliLoadBalancerClient.ListALBListeners(ctx, lbIDs)
	if err != nil {
		return err
	}
	for _, listener := range listeners {
		lb, ok := lbMap[tea.StringValue(listener.LoadBalancerId)]
		if ok {
			albListener := &entity.ALBListener{
				ListenerID:          tea.StringValue(listener.ListenerId),
				ListenerProtocol:    tea.StringValue(listener.ListenerProtocol),
				ListenerPort:        tea.Int32Value(listener.ListenerPort),
				IdleTimeout:         tea.Int32Value(listener.IdleTimeout),
				RequestTimeout:      tea.Int32Value(listener.RequestTimeout),
				GzipEnabled:         tea.BoolValue(listener.GzipEnabled),
				ListenerStatus:      tea.StringValue(listener.ListenerStatus),
				SecurityPolicyID:    tea.StringValue(listener.SecurityPolicyId),
				ListenerDescription: tea.StringValue(listener.ListenerDescription),
			}

			if len(listener.DefaultActions) > 0 && listener.DefaultActions[0] != nil &&
				listener.DefaultActions[0].ForwardGroupConfig != nil &&
				len(listener.DefaultActions[0].ForwardGroupConfig.ServerGroupTuples) > 0 {
				albListener.DefaultServerGroupID = tea.StringValue(listener.DefaultActions[0].ForwardGroupConfig.ServerGroupTuples[0].ServerGroupId)
			}

			lb.ALBListeners = append(lb.ALBListeners, albListener)
		}
	}

	// mongo insert
	batchRes, err := models.LoadBalancerModel.BatchCreateOrUpdate(ctx, lbEntities)
	if err != nil {
		logging.Errorf("ALB.LoadBalancerModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}
	logging.Infof("ALB.LoadBalancerModel.BatchCreateOrUpdate.ok: %+v", batchRes)

	return nil
}

func (a *AliyunSyncer) syncALBServerGroup(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	aliLoadBalancerClient, err := alicloud.NewAliLoadBalancerClient(option.RegionID, option.IspID)
	if err != nil {
		logging.Errorf("init client fail:%v", err)
		return err
	}

	serverGroups, err := aliLoadBalancerClient.ListALBServerGroups(ctx)
	if err != nil {
		return err
	}
	eServerGroups := make([]*entity.LBServerGroup, 0)
	for _, sg := range serverGroups {
		esg := &entity.LBServerGroup{
			ServerGroupID:     tea.StringValue(sg.ServerGroupId),
			ServerGroupName:   tea.StringValue(sg.ServerGroupName),
			ResourceGroupID:   tea.StringValue(sg.ResourceGroupId),
			Scheduler:         tea.StringValue(sg.Scheduler),
			CreateTime:        tea.StringValue(sg.CreateTime),
			Servers:           []*entity.LBServerGroupServer{},
			ServerGroupType:   tea.StringValue(sg.ServerGroupType),
			ServerGroupStatus: tea.StringValue(sg.ServerGroupStatus),
			VpcID:             tea.StringValue(sg.VpcId),
			Protocol:          tea.StringValue(sg.Protocol),
			IspID:             option.IspID,
			IspType:           option.IspType,
		}
		servers, err := aliLoadBalancerClient.ListALBServerGroupsServers(ctx, esg.ServerGroupID)
		if err != nil {
			return err
		}
		for _, server := range servers {
			esg.Servers = append(esg.Servers, &entity.LBServerGroupServer{
				ServerID:   tea.StringValue(server.ServerId),
				ServerType: tea.StringValue(server.ServerType),
				Status:     tea.StringValue(server.Status),
				ServerIP:   tea.StringValue(server.ServerIp),
				Port:       tea.Int32Value(server.Port),
				Weight:     tea.Int32Value(server.Weight),
			})
		}
		eServerGroups = append(eServerGroups, esg)
	}

	// mongo insert
	batchRes, err := models.LBServerGroupModel.BatchCreateOrUpdate(ctx, eServerGroups)
	if err != nil {
		logging.Errorf("LBServerGroupModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}
	logging.Infof("LBServerGroupModel.BatchCreateOrUpdate.ok: %+v", batchRes)

	return nil
}

func (a *AliyunSyncer) syncClassicLoadBalancer(ctx context.Context, logging *logrus.Logger, option SyncOption, syncUpdateVersion string, loadBalancerIDs ...string) error {
	aliLoadBalancerClient, err := alicloud.NewAliLoadBalancerClient(option.RegionID, option.IspID)
	if err != nil {
		logging.Errorf("init client fail:%v", err)
		return err
	}

	slbBody, err := aliLoadBalancerClient.DescribeSLBs(ctx, loadBalancerIDs...)
	if err != nil {
		return err
	}

	var lbEntities []*entity.LoadBalancer
	var lbIDs []string
	lbMap := map[string]*entity.LoadBalancer{}
	for _, slb := range slbBody.LoadBalancers.LoadBalancer {
		slbAttr, err := aliLoadBalancerClient.DescribeSLBAttribute(ctx, tea.StringValue(slb.LoadBalancerId))
		if err != nil {
			return err
		}
		lbEntity := AliSLBToEntity(slbAttr)
		lbEntity.IspID = option.IspID
		lbEntity.IspType = "aliyun"
		lbEntity.UpdateVersion = syncUpdateVersion
		lbEntities = append(lbEntities, &lbEntity)
		lbIDs = append(lbIDs, tea.StringValue(slb.LoadBalancerId))
	}

	for _, lbEntity := range lbEntities {
		lbMap[lbEntity.LoadBalancerID] = lbEntity
	}

	listeners, err := aliLoadBalancerClient.DescribeSLBListeners(ctx, lbIDs)
	if err != nil {
		return err
	}
	for _, listener := range listeners {
		lb, ok := lbMap[tea.StringValue(listener.LoadBalancerId)]
		if ok {
			lb.Listeners = append(lb.Listeners, entity.LoadBalancerListener{
				ACLID:             tea.StringValue(listener.AclId),
				ACLStatus:         tea.StringValue(listener.AclStatus),
				ACLType:           tea.StringValue(listener.AclType),
				BackendServerPort: tea.Int32Value(listener.BackendServerPort),
				Bandwidth:         tea.Int32Value(listener.Bandwidth),
				Description:       tea.StringValue(listener.Description),
				ListenerPort:      tea.Int32Value(listener.ListenerPort),
				ListenerProtocol:  tea.StringValue(listener.ListenerProtocol),
				LoadBalancerID:    tea.StringValue(listener.LoadBalancerId),
				Scheduler:         tea.StringValue(listener.Scheduler),
				Status:            tea.StringValue(listener.Status),
				VServerGroupID:    tea.StringValue(listener.VServerGroupId),
			})
		}
	}

	// mongo insert
	batchRes, err := models.LoadBalancerModel.BatchCreateOrUpdate(ctx, lbEntities)
	if err != nil {
		logging.Errorf("SLB.LoadBalancerModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}
	logging.Infof("SLB.LoadBalancerModel.BatchCreateOrUpdate.ok: %+v", batchRes)

	return nil
}

func AliALBToEntity(alb *alicloud.AlbResponseBody) entity.LoadBalancer {
	lb := entity.LoadBalancer{
		AddressIPVersion:   tea.StringValue(alb.AddressIpVersion),
		AddressType:        tea.StringValue(alb.AddressType),
		CreateTime:         tea.StringValue(alb.CreateTime),
		LoadBalancerID:     tea.StringValue(alb.LoadBalancerId),
		LoadBalancerName:   tea.StringValue(alb.LoadBalancerName),
		LoadBalancerSpec:   tea.StringValue(alb.LoadBalancerEdition),
		LoadBalancerStatus: tea.StringValue(alb.LoadBalancerStatus),
		Tags:               []entity.Tag{},
		VpcID:              tea.StringValue(alb.VpcId),
		Address:            tea.StringValue(alb.DNSName),
		RegionID:           tea.StringValue(alb.RegionId),
		LoadBalancerType:   "alb",
		Ipv6AddressType:    tea.StringValue(alb.Ipv6AddressType),
		ZoneMappings:       []entity.ZoneMappings{},
	}

	for _, z := range alb.ZoneMappings {
		if z == nil {
			continue
		}
		zoneMappings := entity.ZoneMappings{
			LoadBalancerAddresses: []entity.ZoneMappingsLoadBalancerAddresses{},
			VSwitchId:             tea.StringValue(z.VSwitchId),
			ZoneId:                tea.StringValue(z.ZoneId),
		}
		for _, address := range z.LoadBalancerAddresses {
			if address == nil {
				continue
			}
			zoneMappings.LoadBalancerAddresses = append(zoneMappings.LoadBalancerAddresses, entity.ZoneMappingsLoadBalancerAddresses{
				Address:         tea.StringValue(address.Address),
				AllocationId:    tea.StringValue(address.AllocationId),
				EipType:         tea.StringValue(address.EipType),
				IntranetAddress: tea.StringValue(address.IntranetAddress),
				Ipv6Address:     tea.StringValue(address.Ipv6Address),
			})
		}

		lb.ZoneMappings = append(lb.ZoneMappings, zoneMappings)
	}

	if alb.ModificationProtectionConfig != nil {
		lb.ModificationProtectionReason = tea.StringValue(alb.ModificationProtectionConfig.Reason)
		lb.ModificationProtectionStatus = tea.StringValue(alb.ModificationProtectionConfig.Status)
	}
	if alb.LoadBalancerBillingConfig != nil {
		lb.PayType = tea.StringValue(alb.LoadBalancerBillingConfig.PayType)
	}
	if alb.SecurityGroupIds != nil {
		lb.SecurityGroupIds = alb.SecurityGroupIds
	}

	for _, t := range alb.Tags {
		lb.Tags = append(lb.Tags, entity.Tag{
			TagValue: tea.StringValue(t.Value),
			TagKey:   tea.StringValue(t.Key),
		})

	}
	return lb
}

// AliSLBToEntity ...
func AliSLBToEntity(lbAttr *slb20140515.DescribeLoadBalancerAttributeResponseBody) entity.LoadBalancer {
	lb := entity.LoadBalancer{
		Address:                      tea.StringValue(lbAttr.Address),
		AddressIPVersion:             tea.StringValue(lbAttr.AddressIPVersion),
		AddressType:                  tea.StringValue(lbAttr.AddressType),
		AutoReleaseTime:              tea.Int64Value(lbAttr.AutoReleaseTime),
		Bandwidth:                    tea.Int32Value(lbAttr.Bandwidth),
		CreateTime:                   tea.StringValue(lbAttr.CreateTime),
		CreateTimeStamp:              tea.Int64Value(lbAttr.CreateTimeStamp),
		DeleteProtection:             tea.StringValue(lbAttr.DeleteProtection),
		EndTime:                      tea.StringValue(lbAttr.EndTime),
		EndTimeStamp:                 tea.Int64Value(lbAttr.EndTimeStamp),
		InstanceChargeType:           tea.StringValue(lbAttr.InstanceChargeType),
		InternetChargeType:           tea.StringValue(lbAttr.InternetChargeType),
		LoadBalancerID:               tea.StringValue(lbAttr.LoadBalancerId),
		LoadBalancerName:             tea.StringValue(lbAttr.LoadBalancerName),
		LoadBalancerSpec:             tea.StringValue(lbAttr.LoadBalancerSpec),
		LoadBalancerStatus:           tea.StringValue(lbAttr.LoadBalancerStatus),
		MasterZoneID:                 tea.StringValue(lbAttr.MasterZoneId),
		ModificationProtectionReason: tea.StringValue(lbAttr.ModificationProtectionReason),
		ModificationProtectionStatus: tea.StringValue(lbAttr.ModificationProtectionStatus),
		NetworkType:                  tea.StringValue(lbAttr.NetworkType),
		PayType:                      tea.StringValue(lbAttr.PayType),
		RegionID:                     tea.StringValue(lbAttr.RegionId),
		RegionIDAlias:                tea.StringValue(lbAttr.RegionIdAlias),
		RenewalCycUnit:               tea.StringValue(lbAttr.RenewalCycUnit),
		RenewalDuration:              tea.Int32Value(lbAttr.RenewalDuration),
		RenewalStatus:                tea.StringValue(lbAttr.RenewalStatus),
		ResourceGroupID:              tea.StringValue(lbAttr.ResourceGroupId),
		SlaveZoneID:                  tea.StringValue(lbAttr.SlaveZoneId),
		VSwitchID:                    tea.StringValue(lbAttr.VSwitchId),
		VpcID:                        tea.StringValue(lbAttr.VpcId),
		LoadBalancerType:             "slb",
	}
	var tags []entity.Tag
	for _, tag := range lbAttr.Tags.Tag {
		tags = append(tags, entity.Tag{TagKey: tea.StringValue(tag.TagKey), TagValue: tea.StringValue(tag.TagValue)})
	}
	lb.Tags = tags

	// 复制切片数据
	if lbAttr.BackendServers != nil {
		lb.BackendServers = make([]entity.LoadBalancerBackendServer, len(lbAttr.BackendServers.BackendServer))
		for _, backendServer := range lbAttr.BackendServers.BackendServer {
			lb.BackendServers = append(lb.BackendServers, entity.LoadBalancerBackendServer{
				ServerID: tea.StringValue(backendServer.ServerId),
				ServerIP: tea.StringValue(backendServer.ServerIp),
				Type:     tea.StringValue(backendServer.Type),
				Weight:   tea.Int32Value(backendServer.Weight),
			})
		}
	}

	return lb
}

// SyncEIP xxx
func (a *AliyunSyncer) SyncEIP(ctx context.Context, logging *logrus.Logger, option SyncOption, eipIDs ...string) error {
	aliVpcClient, err := alicloud.NewAliVpcClient(option.RegionID, option.IspID)
	if err != nil {
		logging.Errorf("init client fail:%v", err)
		return err
	}

	eips, err := aliVpcClient.DescribeEipAddresses(ctx)
	if err != nil {
		return err
	}

	var eipEntities []*entity.Eip

	for _, eip := range eips {
		if len(eipIDs) == 0 || slices.Contains(eipIDs, *eip.AllocationId) {
			eipEntity := AliEipToEntity(eip)
			eipEntity.IspID = option.IspID
			eipEntity.IspType = "aliyun"
			eipEntities = append(eipEntities, &eipEntity)
		}
	}

	// mongo insert
	batchRes, err := models.EipModel.BatchCreateOrUpdate(ctx, eipEntities)
	if err != nil {
		logging.Errorf("EipModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}
	logging.Infof("EipModel.BatchCreateOrUpdate.ok: %+v", batchRes)

	return nil
}

// AliEipToEntity ...
func AliEipToEntity(eip *vpc20160428.DescribeEipAddressesResponseBodyEipAddressesEipAddress) entity.Eip {
	eipEntity := entity.Eip{
		AllocationID:                  tea.StringValue(eip.AllocationId),
		AllocationTime:                tea.StringValue(eip.AllocationTime),
		Bandwidth:                     tea.StringValue(eip.Bandwidth),
		BandwidthPackageBandwidth:     tea.StringValue(eip.BandwidthPackageBandwidth),
		BandwidthPackageID:            tea.StringValue(eip.BandwidthPackageId),
		BandwidthPackageType:          tea.StringValue(eip.BandwidthPackageType),
		BizType:                       tea.StringValue(eip.BizType),
		BusinessStatus:                tea.StringValue(eip.BusinessStatus),
		ChargeType:                    tea.StringValue(eip.ChargeType),
		DeletionProtection:            tea.BoolValue(eip.DeletionProtection),
		Description:                   tea.StringValue(eip.Description),
		EipBandwidth:                  tea.StringValue(eip.EipBandwidth),
		ExpiredTime:                   tea.StringValue(eip.ExpiredTime),
		HDMonitorStatus:               tea.StringValue(eip.HDMonitorStatus),
		HasReservationData:            tea.StringValue(eip.HasReservationData),
		ISP:                           tea.StringValue(eip.ISP),
		InstanceID:                    tea.StringValue(eip.InstanceId),
		InstanceRegionID:              tea.StringValue(eip.InstanceRegionId),
		InstanceType:                  tea.StringValue(eip.InstanceType),
		InternetChargeType:            tea.StringValue(eip.InternetChargeType),
		IPAddress:                     tea.StringValue(eip.IpAddress),
		Name:                          tea.StringValue(eip.Name),
		Netmode:                       tea.StringValue(eip.Netmode),
		PublicIPAddressPoolID:         tea.StringValue(eip.PublicIpAddressPoolId),
		RegionID:                      tea.StringValue(eip.RegionId),
		ReservationActiveTime:         tea.StringValue(eip.ReservationActiveTime),
		ReservationBandwidth:          tea.StringValue(eip.ReservationBandwidth),
		ReservationInternetChargeType: tea.StringValue(eip.ReservationInternetChargeType),
		ReservationOrderType:          tea.StringValue(eip.ReservationOrderType),
		ResourceGroupID:               tea.StringValue(eip.ResourceGroupId),
		SecondLimited:                 tea.BoolValue(eip.SecondLimited),
		SecurityProtectionTypes:       tea.StringSliceValue(eip.SecurityProtectionTypes.SecurityProtectionType),
		SegmentInstanceID:             tea.StringValue(eip.SegmentInstanceId),
		ServiceManaged:                tea.Int32Value(eip.ServiceManaged),
		Status:                        tea.StringValue(eip.Status),
		VpcID:                         tea.StringValue(eip.VpcId),
		Zone:                          tea.StringValue(eip.Zone),
	}

	var operationLocks []entity.EipAddressOperationLocks
	for _, lock := range eip.OperationLocks.LockReason {
		operationLocks = append(operationLocks, entity.EipAddressOperationLocks{
			LockReason: tea.StringValue(lock.LockReason),
		})
	}
	eipEntity.OperationLocks = operationLocks

	var tags []entity.Tag
	if eip.Tags != nil {
		for _, tag := range eip.Tags.Tag {
			tags = append(tags, entity.Tag{TagKey: tea.StringValue(tag.Key), TagValue: tea.StringValue(tag.Value)})
		}
	}
	eipEntity.Tags = tags

	return eipEntity
}

func (a *AliyunSyncer) SyncPolicyByNames(ctx context.Context, logging *logrus.Logger, option SyncOption, policyNames ...string) error {
	if len(policyNames) == 0 {
		return fmt.Errorf("必须指定权限策略名称")
	}
	client, err := alicloud.NewAliRamClient(option.RegionID, option.IspID)
	if err != nil {
		return err
	}

	policies, err := client.ListPolicies(ctx, "Custom", policyNames...)
	if err != nil {
		return err
	}

	var entities []*entity.Policy

	for _, p := range policies {
		detail, err := client.GetPolicy(ctx, "Custom", tea.StringValue(p.PolicyName))
		if err != nil {
			return err
		}
		ep, err := client.ListEntitiesForPolicy(ctx, "Custom", tea.StringValue(p.PolicyName))
		if err != nil {
			return err
		}
		e := AliPolicyToEntity(detail)
		e.IspID = option.IspID
		e.IspType = "aliyun"
		e.RegionID = option.RegionID
		if ep.Users != nil {
			for _, u := range ep.Users.User {
				e.AttachUsers = append(e.AttachUsers, &entity.AttachUser{
					DisplayName: tea.StringValue(u.DisplayName),
					UserId:      tea.StringValue(u.UserId),
					UserName:    tea.StringValue(u.UserName),
					AttachDate:  tea.StringValue(u.AttachDate),
				})
			}
		}

		entities = append(entities, &e)
	}

	// mongo insert
	batchRes, err := models.PolicyModel.BatchCreateOrUpdate(ctx, entities, true)
	if err != nil {
		logging.Errorf("PolicyModel.BatchCreateOrUpdate.fail: %v", err.Error())
		return err
	}
	logging.Infof("PolicyModel.BatchCreateOrUpdate.ok: %+v", batchRes)
	return nil
}

func (a *AliyunSyncer) SyncPolicy(ctx context.Context, logging *logrus.Logger, option SyncOption, policyIDs ...string) error {
	customFilter := map[string]any{}
	if len(policyIDs) != 0 {
		ids := make([]primitive.ObjectID, 0)
		for _, i := range policyIDs {
			id, err := primitive.ObjectIDFromHex(i)
			if err != nil {
				return err
			}
			ids = append(ids, id)
		}
		customFilter["_id"] = bson.M{"$in": ids}
	}

	eps, _, err := models.PolicyModel.Query(ctx, &schema.PolicyQueryParam{
		PaginationParam: schema.PaginationParam{
			Page: 1,
			Size: 9999,
		},
		PolicyColumnParam: schema.PolicyColumnParam{
			IspID: option.IspID,
		},
		OrderParams: schema.OrderParams{},
	}, customFilter)
	if err != nil {
		return err
	}

	policyNames := make([]string, 0)
	for _, p := range eps {
		policyNames = append(policyNames, p.PolicyName)
	}

	client, err := alicloud.NewAliRamClient(option.RegionID, option.IspID)
	if err != nil {
		return err
	}

	syncUpdateVersion := time.Now().Format("20060102150405")
	policies, err := client.ListPolicies(ctx, "Custom", policyNames...)
	if err != nil {
		return err
	}

	var entities []*entity.Policy

	for _, p := range policies {
		detail, err := client.GetPolicy(ctx, "Custom", tea.StringValue(p.PolicyName))
		if err != nil {
			return err
		}
		ep, err := client.ListEntitiesForPolicy(ctx, "Custom", tea.StringValue(p.PolicyName))
		if err != nil {
			return err
		}
		e := AliPolicyToEntity(detail)
		e.IspID = option.IspID
		e.IspType = "aliyun"
		e.RegionID = option.RegionID
		e.UpdateVersion = syncUpdateVersion
		if ep.Users != nil {
			for _, u := range ep.Users.User {
				e.AttachUsers = append(e.AttachUsers, &entity.AttachUser{
					DisplayName: tea.StringValue(u.DisplayName),
					UserId:      tea.StringValue(u.UserId),
					UserName:    tea.StringValue(u.UserName),
					AttachDate:  tea.StringValue(u.AttachDate),
				})
			}
		}

		entities = append(entities, &e)
	}

	// mongo insert
	batchRes, err := models.PolicyModel.BatchCreateOrUpdate(ctx, entities, true)
	if err != nil {
		logging.Errorf("PolicyModel.BatchCreateOrUpdate.fail: %v", err.Error())
		return err
	}
	logging.Infof("PolicyModel.BatchCreateOrUpdate.ok: %+v", batchRes)

	// 只有全量更新才打清理标签清理
	if len(policyIDs) == 0 {
		cleanupAmount, err := models.PolicyModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("PolicyModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("PolicyModel.MarkCleanup, 标记需要清理的数量%d个", cleanupAmount)
	}

	return nil
}

func AliPolicyToEntity(p *ram20150501.GetPolicyResponseBody) (e entity.Policy) {
	pp := p.Policy
	return entity.Policy{
		UpdateDate:            tea.StringValue(pp.UpdateDate),
		PolicyType:            tea.StringValue(pp.PolicyType),
		Description:           tea.StringValue(pp.Description),
		AttachUsers:           []*entity.AttachUser{},
		DefaultVersion:        tea.StringValue(pp.DefaultVersion),
		PolicyName:            tea.StringValue(pp.PolicyName),
		CreateDate:            tea.StringValue(pp.CreateDate),
		DefaultPolicyVersion:  tea.StringValue(p.DefaultPolicyVersion.VersionId),
		DefaultPolicyDocument: tea.StringValue(p.DefaultPolicyVersion.PolicyDocument),
	}
}

func (m *AliyunSyncer) DeleteNonExistHost(ctx context.Context, logging *logrus.Logger, dryRun bool) (string, error) {
	// 1. 查找全部未删除的云管主机
	res, err := models.HostResourceModel.FindMany(ctx, map[string]interface{}{"is_delete": 0})
	if err != nil {
		return "", err
	}

	instanceIDMap := make(map[string]string)
	for _, r := range res {
		instanceIDMap[r.InstanceID] = r.HostName
	}
	hosts, err := cmdb.FindBkHostByInstanceIDs(logging, nil, nil)
	if err != nil {
		return "", err
	}
	// 2. 对比找到在云管平台不存在的instanceIDs
	deleteInstanceIDs := make([]string, 0)
	deleteHostnames := make([]string, 0)
	for _, h := range hosts.Info {
		if _, ok := instanceIDMap[h.HostInfo.BkCloudInstID]; !ok {
			deleteInstanceIDs = append(deleteInstanceIDs, h.HostInfo.BkCloudInstID)
			deleteHostnames = append(deleteHostnames, h.HostInfo.BkHostname)
		}
	}

	if len(deleteInstanceIDs) == 0 {
		return "跳过,未发现需要删除的主机", nil
	}

	if dryRun {
		return fmt.Sprintf("dryRun模式开启, 删除的实例名称为: %s", strings.Join(deleteHostnames, ",")), nil
	}

	// 3. 删除cmdb中机器
	batchSize := 100
	for i := 0; i < len(deleteInstanceIDs); i += batchSize {
		ids := deleteInstanceIDs[i:min(len(deleteInstanceIDs), i+batchSize)]
		_, err = cmdb.DeleteBkCmdbHost(logging, ids)
		if err != nil {
			return "", fmt.Errorf("删除失败, 错误: %v", err)
		}
	}
	return fmt.Sprintf("删除成功, 实例名称: %s", strings.Join(deleteHostnames, ",")), nil
}
