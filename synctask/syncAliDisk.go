package synctask

import (
	"context"
	"github.com/sirupsen/logrus"
)

//
// func init() {
// 	RegTasK("同步阿里云disk信息", SyncAliDisk)
// }

// FetchDisk xxx
func FetchDisk(ctx context.Context, logging *logrus.Logger, option SyncOption) error {

	logging.Infoln("准备从阿里云获取disk信息....")

	// sdk, err := NewAliEcsClient(option)
	// if err != nil {
	// 	logging.Errorf("init client fail:%v", err)
	// 	return err
	// }
	//
	// count := 0
	// var update []map[string]interface{}
	//
	// r, err := bkinstance.NewClientWithAliEcs(option.BKCMDBAddress)
	// if err != nil {
	// 	logging.Errorf("init client fail:%v", err)
	// 	return err
	// }

	// regionID := option.RegionID

	// instanceIds := InstanceIds[regionID]

	// for k := range instanceIds {
	// 	// instanceId := instanceIds[k]
	// 	response, err := sdk.DescribeDisks(k)
	// 	if err != nil {
	// 		logging.Errorf("fetch disk fail:%+v", err)
	// 		continue
	// 	}
	//
	// 	Instances := response.Disks
	// 	var diskList []map[string]interface{}
	// 	for k := range Instances.Disk {
	// 		disk := Instances.Disk[k]
	// 		diskList = append(diskList, map[string]interface{}{"Size": disk.Size, "Device": disk.Device})
	// 	}
	//
	// 	ret, _ := json.Marshal(diskList)
	//
	// 	update = append(update, map[string]interface{}{"bk_inst_name": fmt.Sprintf("%s_%s", k, regionID), "disk": string(ret)})
	// 	count = count + 1
	// 	time.Sleep(1 * time.Second)
	//
	// 	if count%5 == 0 {
	// 		logging.Infof("send to cmdb: data len:%v region:%v", len(update), regionID)
	// 		result, err := r.BatchCreateOrUpdate(context.TODO(), update)
	// 		if err != nil {
	// 			logging.Errorf("send to cmdb fail:%+v***%+v", result, err)
	// 		}
	// 		update = []map[string]interface{}{}
	// 	}
	// }
	return nil
}

// SyncAliDisk xxx
func SyncAliDisk(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	return FetchDisk(ctx, logging, option)
}
