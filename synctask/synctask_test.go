package synctask

import (
	"context"
	"testing"

	"github.com/sirupsen/logrus"
)

func TestSyncCMDBHost(t *testing.T) {
	err := GetBkCmdbNapSyncer().SyncCMDBHost(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "6540eb306e463ff70c0ba44a",
		IspType:    "",
		RegionID:   "6541b72dca2014b11baf6af3",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.<PERSON><PERSON><PERSON>("error is %v", err)
}

func TestSyncHost(t *testing.T) {
	err := GetAliyunSyncer().SyncHost(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "65572aed9bb2a1793df282a6",
		IspType:    "aliyun",
		RegionID:   "cn-shanghai",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.<PERSON>rrorf("error is %v", err)
}

func TestSyncSomeHost(t *testing.T) {
	err := GetAliyunSyncer().SyncHost(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "65572aed9bb2a1793df282a6",
		IspType:    "aliyun",
		RegionID:   "cn-shanghai",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	}, "i-uf61cpz1tgvdqe5la1zs", "i-uf6fsel7rzocjw7h6mds", "i-uf6dcgxwzib0v4i8ip22", "i-uf619mklc4b7grw5lgd2", "i-uf619mklc4b7dlgd24ol", "i-uf68v4e682125hmyx2s1", "i-uf67w409rh1xrq4xxnod", "i-uf656af190rpycu5i3d5", "i-uf6f8tu30nw2oukbibhi", "i-uf6fke7r7irzj2n7jfyh", "i-uf61crif1wgea5pi938d", "i-uf61crif1wgea5pi938c", "i-uf60690d2xqbv3ehe52f", "i-uf64fl660plklfzs5fvg", "i-uf64fl660plklfzs5fvf", "i-uf6fizgm9swwrtrlfwh6", "i-uf6fizgm9swwrtrlfwh5", "i-uf6fizgm9swwrtrlfwh4", "i-uf62tr71wom9dnez8hgu", "i-uf659o9rxxeukxw5l60b", "i-uf6033hyjrse01x49115", "i-uf627a7lh28wve3h8x0z", "i-uf6eju3xei6y195rapyp", "i-uf6g8a2j9pfru3wrz27y", "i-uf63ttwyq5nwah8eqd9s", "i-uf63637h0zi0k9hu6bqq", "i-uf6iw63ja0orbtcr5ynh", "i-uf63mcvsc1az2ytb7wyt", "i-uf66nl2clxz3emco58e4", "i-uf65fla7pu9vd4q4u97t", "i-uf6flygv5q6qspd61sid", "i-uf69d8mau5ltiwn8rte8", "i-uf6e9gufa7eihrj6e94k", "i-uf61tzw7pw3ct86kiua1", "i-uf6i3mpuoxtprynu3yxc", "i-uf6bw31ptihzvtalxcas", "i-uf6cfl9amu8zlaoj5sfe", "i-uf6h6mm8izkisdifzctx", "i-uf6g7bcknyvre57s1yv5", "i-uf64fphghj48as99pjlk", "i-uf6hyd2qs3dwruw4cmoj", "i-uf61tzw7pw3b93qx3d3v", "i-uf6cfl9amu8x4gbffrki", "i-uf6dbyae9rid749s3sdj", "i-uf64wfly2ljqulz5wpl8", "i-uf647v5a79mofdhydffg", "i-uf6bof3du2zz10t7qrbe", "i-uf63o9bnjt2oaexuob2j", "i-uf6921nxsagnml5uwqk1", "i-uf6hkqxae4f2vhhszf94", "i-uf6db9oid9dt89hoqc9p", "i-uf68b32ohqw0lb7h4qbu", "i-uf666ysa3tlf44mohxfg", "i-uf6g19dchf32p8q8bztl", "i-uf620pnrqaglkw00v86m", "i-uf64iszmevwu4ij9nyyf", "i-uf66ocbyzf4kghcui0yx", "i-uf6bj5syle0m8pp0sq0k", "i-uf61yzu8ubvuq0s6j9nx", "i-uf62jngkk6id73980n9s", "i-uf6htxw6eh432o1uxvdy", "i-uf6iam2atdtv5dgw11zz", "i-uf6hom42n97uhvgxltzs", "i-uf6gpk2mbm78b2qvwzx9", "i-uf6ckh8q20nk9986m0i6", "i-uf61uq1gszh12o7c0rj9", "i-uf6g8jljysgplc0omul3", "i-uf61vb3qtncvfcrd79dk", "i-uf6df3zfdoo19s4vdvk7", "i-uf60v56wumoozdyq73wu", "i-uf6b38wgk1glfahkziiv", "i-uf69914kl3cx6qgakuq1", "i-uf69914kl3cx49nwicst", "i-uf68hhrukfs3dwe38i6x", "i-uf6fq1zzk0hj00e8hksq", "i-uf69bxleb6yq0x40geb8", "i-uf64f3u4fblmw9u16yc3", "i-uf64f3u4fblmvg8ki4lx", "i-uf68jtle5ipmla510k4p", "i-uf644yk9uur8b011dvwd", "i-uf644yk9uur8b011dvwe", "i-uf644yk9uur8b011dvwf", "i-uf6ah19e5ym5n27um8xh", "i-uf6f3nfrlqltsg8vpipy", "i-uf637le13v3c7hqfzryc", "i-uf66ajk6cgggsyymexpq", "i-uf6hh2nnpxue1fysal7t", "i-uf62wfz1dcxtckrwctxy", "i-uf611co41uygy0thzlkn", "i-uf611co41uygy0thzlkp", "i-uf611co41uygy0thzlko", "i-uf65e5re60l64v25fetr", "i-uf68002x8jhkl0fos164", "i-uf6ha9chxtf5apbd2llz", "i-uf64hsuh6s6th6gmm8hb", "i-uf652b30kcqfkc74msax", "i-uf66ypeugbf5c041v04z", "i-uf60f4sodeuydf37fl9w", "i-uf6gcdlnn91sbfnzqnkq", "i-uf6a17bg0takicfsywxg", "i-uf643c4ap6y5fc3jkyqm", "i-uf6d7tvekpypc13xmj0o", "i-uf6ae3wp782sqcnxinhj", "i-uf68s9gpsr99bgeqdb6u", "i-uf6g8eomwy980zb8h7ep", "i-uf66ua8a0mvale1rf8dk", "i-uf6eg72up5wbb451opo7", "i-uf69o9f6rzssx0rczipb", "i-uf64k2cyzh4ytkgmunsh", "i-uf6g8eomwy95ybz51p93", "i-uf6992jr7lxlf27ktze9", "i-uf6g8eomwy94hxplcntq", "i-uf60kyukb9k469zrzdao", "i-uf6cj0apv3qo2cc7p21i", "i-uf6g38l4vz4axvttjjc0", "i-uf65da8cpwyjn7phf67a", "i-uf682tl73nk6c9ujrs4z", "i-uf6e13a5ju58mrvyj8ja", "i-uf64fa3s2azd3d7rj62c", "i-uf63wjp05u6xcd61x3rt", "i-uf64fa3s2azd3d7rj621", "i-uf6dkmdveonza6g02xlf", "i-uf64fa3s2azd2llbye4o", "i-uf69d3w0frwrmily4zhy", "i-uf6gsocgu8u69xatdlhx", "i-uf69d3w0frwrmily4zhl", "i-uf682tl73nk681x63i1m", "i-uf6deny9199hsy0rhvx9", "i-uf69fppg41nv2jrzxsi6", "i-uf68h0usx91e0ivv67i2", "i-uf6hwn97w4q5720yipmr", "i-uf682tl73nk67zy4zg28", "i-uf6899tqebd5fznc0gs0", "i-uf617l7t58lpts4407n3", "i-uf6dkmdveonz98wh5zr0", "i-uf6e57tbv6wkhifhwms1", "i-uf6hwn97w4q56y2walne", "i-uf65da8cpwyj4uq4ojig", "i-uf632cbz7zslz58jjecl", "i-uf6bjqolzwjfr4sqkrcq", "i-uf6899tqebd5fxoawesf", "i-uf6fjou6qw524ujgve6c", "i-uf6gsocgu8u69nfntbjp", "i-uf6aq431qs6ox59saf89", "i-uf6bjqolzwjfqwwm4je6", "i-uf6e13a5ju58lam4hqr7", "i-uf6bjqolzwjfnaokovjo", "i-uf6e13a5ju551pa672nm", "i-uf68jvwg0rke4tr1u8a1", "i-uf6c40gmjvnlm4h7j5m1", "i-uf66hfaknzdl170c4te1", "i-uf6fvf0n87jghhxb53aa", "i-uf6ixgw2fkf2xragkd71", "i-uf69ywdroqaxb1svqfsh", "i-uf66bobf7fwxil3tk9yd", "i-uf6g72w1uc9itswu9pyo", "i-uf60ayubfzwwbcyuysh9", "i-uf6d0tiwh49ecpsusadu", "i-uf6d0tiwh49ecpsusadt", "i-uf6h5wcsrzzz284s1lak", "i-uf6dm825wiar486l4hf7", "i-uf6a7ld41laz7togafx7", "i-uf6a7ld41laz7togafx6", "i-uf63c1ggq7lfie986gpu", "i-uf6aa8pwy332jf7b24zn", "i-uf667emtssoiljx2g4xp", "i-uf6bb3w4s8y5vb0dbkzy", "i-uf6dt0ptqod6kbfoerqd", "i-uf66sfbs27efwrptu8yi", "i-uf64h48lcbywn5soaeph", "i-uf60q1y56cx3i72t92iy", "i-uf60q1y56cx3i72t92iz", "i-uf60q1y56cx3i72t92j0", "i-uf69ql6wc4u0pn9q42o4", "i-uf6i8y9ggbzcal4ilon0", "i-uf6bgzfjw9fn8bkgrawi")
	t.Errorf("error is %v", err)
}

func TestSyncLB(t *testing.T) {
	err := GetAliyunSyncer().SyncLoadBalancer(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "65572aed9bb2a1793df282a6",
		IspType:    "aliyun",
		RegionID:   "cn-shanghai",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.Errorf("error is %v", err)
}

func TestSyncCMDBPolardb(t *testing.T) {
	ctx := context.Background()
	// settings.Config.AppGroup = "ys"
	// settings.Config.AppName = "op-cloudman-takumi"
	// s := zestmock.Start()
	// s.Config(
	// 	settings.Config.AppGroup,
	// 	settings.Config.AppName,
	// 	map[string]string{
	// 		"cmdb-config":   "bk_cmdb_url: \"http://cmdb.nap.mihoyo.com\"\nbk_cmdb_test_url: \"http://cmdb.nap.mihoyo.com\"\nenable: true\nother_region:\n  enable: true\n  biz_id: 3\n  server_list_url: \"https://sre-easycode.juequling.com/easycode/dbapi/query/cn_other_server_list\"\n  server_list_token: \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2OTkzODY2ODgsInVzZXJuYW1lIjoiYWRtaW4ifQ.caQ7slshQ5ECym8IsCHBmS5qofvk0EYiL_tVhBPzE_0\"\n  template_id: 1\nprod_region:\n  enable: false",
	// 		"system-config": "env: local\nenable_local_cron: true"})

	err := GetBkCmdbNapSyncer().SyncAddPolardb(ctx, logrus.StandardLogger(), SyncOption{
		IspID:    "65572aed9bb2a1793df282a6",
		IspType:  "aliyun",
		RegionID: "cn-shanghai",
	})
	t.Errorf("error is %v", err)
}

func TestSyncCMDBRedis(t *testing.T) {
	ctx := context.Background()
	// settings.Config.AppGroup = "ys"
	// settings.Config.AppName = "op-cloudman-takumi"
	// s := zestmock.Start()
	// s.Config(
	// 	settings.Config.AppGroup,
	// 	settings.Config.AppName,
	// 	map[string]string{
	// 		"cmdb-config":   "bk_cmdb_url: \"http://cmdb.nap.mihoyo.com\"\nbk_cmdb_test_url: \"http://cmdb.nap.mihoyo.com\"\nenable: true\nother_region:\n  enable: true\n  biz_id: 3\n  server_list_url: \"https://sre-easycode.juequling.com/easycode/dbapi/query/cn_other_server_list\"\n  server_list_token: \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2OTkzODY2ODgsInVzZXJuYW1lIjoiYWRtaW4ifQ.caQ7slshQ5ECym8IsCHBmS5qofvk0EYiL_tVhBPzE_0\"\n  template_id: 1\nprod_region:\n  enable: false",
	// 		"system-config": "env: local\nenable_local_cron: true"})

	err := GetBkCmdbNapSyncer().SyncAddRedis(ctx, logrus.StandardLogger(), SyncOption{
		IspID:    "65572aed9bb2a1793df282a6",
		IspType:  "aliyun",
		RegionID: "cn-shanghai",
	})
	t.Errorf("error is %v", err)
}

func TestSyncPolicy(t *testing.T) {
	err := GetAliyunSyncer().SyncPolicy(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "65572aed9bb2a1793df282a6",
		IspType:    "aliyun",
		RegionID:   "cn-shanghai",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.Errorf("error is %v", err)
}

func TestSyncEcsSecurityGroup(t *testing.T) {
	err := GetAliyunSyncer().SyncECSSecurityGroup(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "65572aed9bb2a1793df282a6",
		IspType:    "aliyun",
		RegionID:   "cn-shanghai",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.Errorf("error is %v", err)
}

func TestSyncPolardbWhitelist(t *testing.T) {
	err := GetAliyunSyncer().SyncPolarDBWhitelist(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "65572aed9bb2a1793df282a6",
		IspType:    "aliyun",
		RegionID:   "cn-shanghai",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.Errorf("error is %v", err)
}

func TestSyncAlbAcl(t *testing.T) {
	err := GetAliyunSyncer().SyncAlbACL(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "65572aed9bb2a1793df282a6",
		IspType:    "aliyun",
		RegionID:   "cn-shanghai",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.Errorf("error is %v", err)
}

func TestDeleteSyncCMDB(t *testing.T) {
	ctx := context.Background()
	// settings.Config.AppGroup = "ys"
	// settings.Config.AppName = "op-cloudman-takumi"
	// s := zestmock.Start()
	// s.Config(
	// 	settings.Config.AppGroup,
	// 	settings.Config.AppName,
	// 	map[string]string{
	// 		"cmdb-config":   "bk_cmdb_url: \"http://ops.nap.mihoyo.com/backend/cmdb\"\nbk_cmdb_token: \"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoibG9naW4iLCJjcmVhdGVfdXNlciI6InlpLnhpZSIsImV4cCI6MTcwNzIxNDg3MSwiaWF0IjoxNzA2NjEwMDcxLCJuYmYiOjE3MDY2MTAwNzEsInN1YiI6InlpLnhpZSJ9.iLBUndNpWQes_i8_eP5Ee4SU_KRiH7RE_z-GpzW2MmY\"\nenable: true\nother_region:\n  enable: true\n  biz_id: 3\n  server_list_url: \"https://sre-easycode.juequling.com/easycode/dbapi/query/cn_other_server_list\"\n  server_list_token: \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2OTkzODY2ODgsInVzZXJuYW1lIjoiYWRtaW4ifQ.caQ7slshQ5ECym8IsCHBmS5qofvk0EYiL_tVhBPzE_0\"\n  template_id: 1\nprod_region:\n  enable: false",
	// 		"system-config": "env: local\nenable_local_cron: true"})

	res, err := GetAliyunSyncer().DeleteNonExistHost(ctx, logrus.StandardLogger(), true)
	t.Errorf("error is %v, res: %s", err, res)
}

func TestSyncAwsEcs(t *testing.T) {
	err := GetAwsSyncer().SyncHost(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "680214522c78794fe897c51d",
		IspType:    "aws",
		RegionID:   "ap-southeast-1",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.Errorf("error is %v", err)
}

func TestSyncAwsLb(t *testing.T) {
	err := GetAwsSyncer().SyncLoadBalancer(context.Background(), logrus.StandardLogger(), SyncOption{
		IspID:      "680214522c78794fe897c51d",
		IspType:    "aws",
		RegionID:   "ap-southeast-1",
		RateLimit:  0,
		RetryLimit: 0,
		Timeout:    0,
	})
	t.Errorf("error is %v", err)
}
