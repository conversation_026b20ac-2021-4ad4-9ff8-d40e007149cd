package synctask

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/ec2"
	ec2types "github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go-v2/service/elasticache"
	estypes "github.com/aws/aws-sdk-go-v2/service/elasticache/types"
	"github.com/aws/aws-sdk-go-v2/service/rds"
	rdstypes "github.com/aws/aws-sdk-go-v2/service/rds/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/elbv2"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent"
	ec2util "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/ec2"
	rdsutil "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/rds"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/awsutil/redis"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/jobman"
	awsprovider "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/aws"
)

// AwsSyncer AWS同步器
type AwsSyncer struct {
}

var awsSyncer *AwsSyncer

// GetAwsSyncer 获取单例AwsSyncer
func GetAwsSyncer() *AwsSyncer {
	if awsSyncer == nil {
		awsSyncer = &AwsSyncer{}
	}
	return awsSyncer
}

// TaskSet AWS同步方法组
func (a *AwsSyncer) TaskSet() map[string]string {
	return map[string]string{
		"同步AWS EC2信息":    SyncHost,
		"同步AWS Redis信息":  SyncRedis,
		"同步AWS Mysql信息":  SyncMysql,
		"同步AWS 安全组信息":    SyncSecurityGroup,
		"同步AWS 弹性公网IP信息": SyncEIP,
	}
}

// SyncRedis -
func (a *AwsSyncer) SyncRedis(ctx context.Context, logging *logrus.Logger, option SyncOption, inputInstanceID ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging.Infoln("准备从AWS获取Redis信息")
	times := 1
	var marker *string
	var totalSave int

	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("**************")

	for times == 1 || marker != nil {
		var updateData []*entity.RedisResource
		if logging != nil {
			logging.Infof("第%d次同步CacheCluster信息\n", times)
		}
		// client.Descr()
		ccOut := &elasticache.DescribeReplicationGroupsOutput{}
		if len(inputInstanceID) != 0 {
			for _, instanceID := range inputInstanceID {
				ccIn := &elasticache.DescribeReplicationGroupsInput{ReplicationGroupId: aws.String(instanceID)}
				out := &elasticache.DescribeReplicationGroupsOutput{}
				err := agentsdk.SyncCall(ctx, option.RegionID, "redis", "DescribeReplicationGroups", option.IspID, []interface{}{nil, ccIn}, out)
				if err != nil {
					if logging != nil {
						logging.Errorf("第%d次同步CacheClusterGroup报错：%v", times, err)
					}
					return err
				}
				ccOut.ReplicationGroups = append(ccOut.ReplicationGroups, out.ReplicationGroups...)
			}
		} else {
			err := agentsdk.SyncCall(ctx, option.RegionID, "redis", "DescribeReplicationGroups", option.IspID, []interface{}{nil, &elasticache.DescribeReplicationGroupsInput{}}, ccOut)
			if err != nil {
				if logging != nil {
					logging.Errorf("第%d次同步CacheClusterGroup报错：%v", times, err)
				}
				return err
			}
		}

		for _, rg := range ccOut.ReplicationGroups {
			// todo: 判断cc是否忽略
			if rg.ConfigurationEndpoint == nil && rg.NodeGroups == nil {
				if logging != nil {
					logging.Errorf("ElastiCache Replication Group (%s) doesn't have node groups", aws.StringValue(rg.ReplicationGroupId))
				}
				continue
			}
			redisSlice, err := redis.GetRedisResourceSlice(ctx, rg, a.ispID(option), a.ispType(), a.regionID(option), logging)
			if err != nil {
				if logging != nil {
					logging.Errorf("第%d次提取CacheCluster报错：%v", times, err)
				}
				return err
			}
			updateData = append(updateData, redisSlice...)
		}

		flag := true
		var resClusters []estypes.CacheCluster
		clusterIn := &elasticache.DescribeCacheClustersInput{ShowCacheNodeInfo: &flag}
		clusterOut := &elasticache.DescribeCacheClustersOutput{}
		err := agentsdk.SyncCall(ctx, option.RegionID, "redis", "DescribeCacheClusters", option.IspID, []interface{}{nil, clusterIn}, clusterOut)
		if err != nil {
			if logging != nil {
				logging.Errorf("第%d次同步CacheCluster报错：%v", times, err)
			}
			return err
		}
		resClusters = append(resClusters, clusterOut.CacheClusters...)

		for _, cc := range resClusters {
			if cc.ReplicationGroupId != nil {
				for _, rg := range updateData {
					var sgs []string
					for _, sg := range cc.SecurityGroups {
						sgs = append(sgs, aws.StringValue(sg.SecurityGroupId))
					}
					rg.SecurityGroupIds.SecurityGroupID = sgs
				}
				continue
			}
			d, _ := redis.GetRedisResource(ctx, cc, a.ispID(option), a.ispType(), a.regionID(option))
			updateData = append(updateData, d)
		}

		for _, d := range updateData {
			d.UpdateVersion = syncUpdateVersion
			d.InitTreeNode = getTreeNode("redis", d.InstanceName)
		}

		if logging != nil {
			logging.Infof("pre-save result: len is:%d\n", len(updateData))
		}
		totalSave += len(updateData)

		result, err := models.RedisResourceModel.BatchCreateOrUpdate(context.TODO(), updateData)
		if err != nil {
			if logging != nil {
				logging.Errorf("save data fail:%+v***%+v", result, err.Error())
			}
			return err
		}

		times++
		marker = ccOut.Marker
	}

	// 只有全量更新才打清理标签清理
	if len(inputInstanceID) == 0 {
		cleanupAmount, err := models.RedisResourceModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("RedisResourceModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("RedisResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}
	logging.Infof("aws.SyncRedis.end")

	fmt.Println("*************************")
	if logging != nil {
		logging.Infof("AWS ElasticCache(redis)同步结束,共写入数据%v条", totalSave)
	}
	return nil
}

func (a *AwsSyncer) syncDatabase(dsn, clusterID string, logging *logrus.Logger, option SyncOption) error {
	// todo enable option
	dbSlice, err := jobman.FetchDatabase(context.Background(), logrus.New(), "prod", dsn, clusterID)
	if err != nil {
		if logging != nil {
			logging.Errorf("Show Databases Error:%s\n", err)
		}
		return err
	}
	var updateData []*entity.MysqlDatabaseResource
	for _, db := range dbSlice {
		if db == "information_schema" {
			continue
		}
		database := &entity.MysqlDatabaseResource{
			IspID:     a.ispID(option),
			IspType:   a.ispType(),
			RegionID:  option.RegionID,
			ClusterID: clusterID,
			DBName:    db,
		}
		updateData = append(updateData, database)
	}
	if logging != nil {
		logging.Infoln("Updatedata:", len(updateData))
	}
	result, err := models.MysqlDatabaseResourceModel.BatchCreateOrUpdate(context.Background(), updateData)
	if err != nil {
		if logging != nil {
			logging.Errorf("save database fail:%+v***%+v", result, err.Error())
		}
		return err
	}
	if logging != nil {
		logging.Infoln("Update Database Success")
	}
	return nil
}

func (a *AwsSyncer) syncAccount(dsn, clusterID string, logging *logrus.Logger, option SyncOption) error {
	return nil
}

// SyncMysql 同步Mysql
func (a *AwsSyncer) SyncMysql(ctx context.Context, logging *logrus.Logger, option SyncOption, clusterIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging.Infoln("准备从AWS获取Mysql信息")
	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("**************")

	times := 0
	var marker *string
	var totalSave int
	for times == 0 || marker != nil {
		times++

		var updateData []*entity.MysqlClusterResource
		var updateEndpoints []*entity.MysqlClusterEndpointResource

		logging.Infof("第%d次同步DBCluster信息\n", times)
		clusterOut := &rds.DescribeDBClustersOutput{}
		clusterInput := &rds.DescribeDBClustersInput{Marker: marker}
		if len(clusterIDs) > 0 {
			clusterInput.Filters = []rdstypes.Filter{{Name: aws.String("db-cluster-id"), Values: clusterIDs}}
		}
		err := agentsdk.SyncCall(ctx, option.RegionID, "mysql", "DescribeDBClusters", option.IspID, []interface{}{"",
			clusterInput,
		}, clusterOut)

		if err != nil {
			logging.Errorf("第%d次同步DBCluster报错：%v", times, err)
			return err
		}
		for _, cluster := range clusterOut.DBClusters {
			// 跳过aurora实例
			if !strings.Contains(rdsutil.Engine(cluster), "aurora") {
				continue
			}
			// 根据env标签判断是否要同步

			logging.Infof("--------------------------------------------\n")
			logging.Infoln("Cluster:", *cluster.DBClusterIdentifier)
			logging.Infoln("Arn:", *cluster.DBClusterArn)
			logging.Infoln("Engine:", *cluster.Engine)
			logging.Infoln("Endpoint:", *cluster.Endpoint)
			logging.Infoln("ReaderEndpoint:", *cluster.ReaderEndpoint)
			logging.Infoln("Port:", *cluster.Port)
			logging.Infoln("=============================================")

			localClusterID := rdsutil.DBClusterID(cluster)
			localClusterEndpoint := rdsutil.DBEndpoint(cluster)
			localClusterReaderEndpoint := rdsutil.DBReaderEndpoint(cluster)
			localClusterPort := rdsutil.DBPort(cluster)

			if cluster.ReaderEndpoint != nil && cluster.Port != nil {
				err = a.syncDatabase(*cluster.ReaderEndpoint, localClusterID, logging, option)
				if err != nil {
					logging.Errorf("syncDatabase(%s)(%s).error: %s\n", *cluster.DBClusterIdentifier, *cluster.ReaderEndpoint, err.Error())
				}
				err = a.syncAccount(*cluster.ReaderEndpoint, localClusterID, logging, option)
				if err != nil {
					logging.Errorf("syncAccount(%s)(%s).error: %s\n", *cluster.DBClusterIdentifier, *cluster.ReaderEndpoint, err.Error())
				}
			} else {
				logging.Warnln("未发现endpoint,不同步database")
			}
			updateEndpoints = append(updateEndpoints, &entity.MysqlClusterEndpointResource{
				IspID:         a.ispID(option),
				IspType:       a.ispType(),
				DBClusterID:   localClusterID,
				DBEndpointID:  localClusterEndpoint + "/primary", // 主地址
				ReadWriteMode: "",
				AddressItems: []entity.MysqlClusterAddressItems{
					{
						PrivateZoneConnectionString: localClusterEndpoint,
						ConnectionString:            localClusterEndpoint,
						NetType:                     "Private",
						Port:                        fmt.Sprint(localClusterPort),
					},
					{
						PrivateZoneConnectionString: localClusterEndpoint,
						ConnectionString:            localClusterEndpoint,
						NetType:                     "Public",
						Port:                        fmt.Sprint(localClusterPort),
					},
				},
				EndpointType: "Cluster",
			})
			updateEndpoints = append(updateEndpoints, &entity.MysqlClusterEndpointResource{
				IspID:         a.ispID(option),
				IspType:       a.ispType(),
				DBClusterID:   localClusterID,
				DBEndpointID:  localClusterEndpoint + "/cluster", // 集群地址
				ReadWriteMode: "ReadWrite",
				AddressItems: []entity.MysqlClusterAddressItems{
					{
						PrivateZoneConnectionString: localClusterEndpoint,
						ConnectionString:            localClusterEndpoint,
						NetType:                     "Private",
						Port:                        fmt.Sprint(localClusterPort),
					},
					{
						PrivateZoneConnectionString: localClusterEndpoint,
						ConnectionString:            localClusterEndpoint,
						NetType:                     "Public",
						Port:                        fmt.Sprint(localClusterPort),
					},
				},
				EndpointType: "Cluster",
			})
			updateEndpoints = append(updateEndpoints, &entity.MysqlClusterEndpointResource{
				IspID:         a.ispID(option),
				IspType:       a.ispType(),
				DBClusterID:   localClusterID,
				DBEndpointID:  localClusterReaderEndpoint + "/readonly", // 只读地址
				ReadWriteMode: "ReadOnly",
				AddressItems: []entity.MysqlClusterAddressItems{
					{
						PrivateZoneConnectionString: localClusterReaderEndpoint,
						ConnectionString:            localClusterReaderEndpoint,
						NetType:                     "Private",
						Port:                        fmt.Sprint(localClusterPort),
					},
					{
						PrivateZoneConnectionString: localClusterReaderEndpoint,
						ConnectionString:            localClusterReaderEndpoint,
						NetType:                     "Public",
						Port:                        fmt.Sprint(localClusterPort),
					},
				},
				EndpointType: "Primary",
			})

			// a.syncEndpoint(ctx, client, cluster.DBClusterIdentifier, logging, option)
			var nodeSlice []entity.MysqlClusterDBNode
			var inst rdstypes.DBInstance
			for _, member := range cluster.DBClusterMembers {
				fmt.Printf("DescribeDBInstances: Args: %s", *member.DBInstanceIdentifier)

				instIn := &rds.DescribeDBInstancesInput{DBInstanceIdentifier: member.DBInstanceIdentifier}
				instOut := &rds.DescribeDBInstancesOutput{}
				err := agentsdk.SyncCall(ctx, option.RegionID, "mysql", "DescribeDBInstances", option.IspID, []interface{}{"", instIn}, instOut)
				if err != nil {
					logging.Errorf("第%d次同步，查询Cluster（%s）Instance（%s）报错：%v\n", times, *cluster.DBClusterIdentifier, *member.DBInstanceIdentifier, err)
					continue
				}

				inst = instOut.DBInstances[0]
				address := ""
				if inst.Endpoint != nil {
					address = *inst.Endpoint.Address
				}

				logging.Info("Node:", *inst.DBInstanceIdentifier, ", Endpoint:", address)

				nodeSlice = append(nodeSlice, rdsutil.DBNode(inst, member, a.regionID(option)))
			}
			logging.Info("*********************************************")

			mysql := &entity.MysqlClusterResource{
				IspID:        a.ispID(option),
				IspType:      a.ispType(),
				VpcID:        rdsutil.VpcID(cluster),
				Expired:      rdsutil.Expired(),
				DBNodeNumber: rdsutil.DBNodeNumber(cluster),
				CreateTime:   (*cluster.ClusterCreateTime).Format("2006-01-02 15:04:05"),
				PayType:      rdsutil.PayType(),
				DBNodeClass:  rdsutil.DBNodeClass(inst),
				Tags:         rdsutil.Tags(cluster),
				DBType:       rdsutil.DBType(),
				DBNodes: entity.MysqlClusterDBNodes{
					DBNode: nodeSlice,
				},
				RegionID:             option.RegionID,
				DBVersion:            rdsutil.DBVersion(cluster),
				DBClusterID:          rdsutil.DBClusterID(cluster),
				DBClusterStatus:      rdsutil.DBClusterStatus(cluster),
				StorageUsed:          rdsutil.StorageUsed(cluster),
				DBClusterNetworkType: rdsutil.DBClusterNetworkType(),
				DBClusterDescription: rdsutil.DBClusterDescription(cluster),
				ZoneID:               "",
				Engine:               rdsutil.Engine(cluster),
				UpdateVersion:        syncUpdateVersion,
				InitTreeNode:         getTreeNode("mysql", rdsutil.DBClusterDescription(cluster)),
				SecurityGroupIds:     entity.InstanceSecurityGroupIds{SecurityGroupID: rdsutil.DBVpcSecurityGroups(cluster)},
			}

			updateData = append(updateData, mysql)
		}

		logging.Infof("MysqlClusterResource pre-save, len: %d\n", len(updateData))
		totalSave += len(updateData)

		result, err := models.MysqlClusterResourceModel.BatchCreateOrUpdate(context.TODO(), updateData)
		if err != nil {
			logging.Errorf("MysqlClusterResource save fail, result: %+v, error: %+v", result, err.Error())
			return err
		}

		logging.Infof("MysqlClusterEndpoint pre-save, len: %d\n", len(updateEndpoints))
		_, err = models.MysqlClusterEndpointResourceModel.BatchCreateOrUpdate(context.TODO(), updateEndpoints)
		if err != nil {
			logging.Errorf("MysqlClusterEndpoint save fail, result: %+v, error: %+v", result, err.Error())
			return err
		}
		logging.Info("MysqlClusterEndpointResourceModel Success")
		marker = clusterOut.Marker
	}

	// 只有全量更新才打清理标签清理
	if len(clusterIDs) == 0 {
		cleanupAmount, err := models.MysqlClusterResourceModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("MysqlClusterResourceModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("MysqlClusterResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}
	logging.Infof("AWS MySQL同步结束,共写入数据%v条", totalSave)
	return nil
}

// SyncHost 同步云主机
func (a *AwsSyncer) SyncHost(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging.Infof("准备从aws获取ecs信息....，InstanceIDs: %v", instanceIDs)
	logging.Infoln("初始化缓存参数：InstanceType Map")
	instTypeMap := map[ec2types.InstanceType]ec2types.InstanceTypeInfo{}

	logging.Infoln("初始化缓存参数：Image Map")
	imageMap := map[string]ec2types.Image{}
	logging.Infoln("初始化缓存参数： InstDeviceSize Map")
	instDeviceSizeMap, err := a.allVolumes(ctx, option)
	if err != nil {
		logging.Error(err.Error())
		return err
	}

	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("**************")
	// 获取ispid的管理编号
	var accountIndexID uint32
	var accountEntity entity.Account
	accEnt := entity.GetAccountCollection(models.GetEngine())
	_, err = models.FindPK(ctx, accEnt, option.IspID, &accountEntity)
	if err == nil {
		accountIndexID = accountEntity.IndexID
	} else {
		logging.Errorf("cannot get accountIndexID, err: %s", err.Error())
		return err
	}
	// 循环变量初始化
	var totalSave int
	var result *models.BatchResult
	times := 1
	var nextToken *string

	for times == 1 || nextToken != nil {
		var updateData []*entity.HostResource

		logging.Infoln("第", times, "次同步EC2数据")

		instanceIn := &ec2.DescribeInstancesInput{NextToken: nextToken}
		if len(instanceIDs) > 0 {
			instanceIn.InstanceIds = instanceIDs
		}
		instanceOut := &ec2.DescribeInstancesOutput{}
		err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeInstances", option.IspID, []interface{}{"", instanceIn}, instanceOut)
		if err != nil {
			logging.Errorf("第%d次同步EC2数据报错：%v", times, err)
			return err
		}

		for _, r := range instanceOut.Reservations {
			for _, inst := range r.Instances {
				// 主机删除后，会以“terminated”状态残留1小时
				if inst.State.Name == ec2types.InstanceStateNameTerminated {
					continue
				}
				if !checkEc2Tags(inst.Tags) {
					logging.Warnf("instance %s不属于该环境,跳过", getInstName(inst))
					continue
				}

				host := ec2util.HostResource(inst)
				host.SetRegionID(option.RegionID)
				err = a.freshEc2InstanceType(ctx, inst.InstanceType, instTypeMap, option)
				if err != nil {
					return err
				}
				host.SetMemory(ec2util.Memory(instTypeMap[inst.InstanceType]))
				// host.SetZoneID(awssync.ZoneID(zon))
				host.ImageID = aws.StringValue(inst.ImageId)
				err := a.freshImage(ctx, *inst.ImageId, imageMap, option)
				if err != nil {
					logging.Warnf("%s, 获取Image失败:%s, 使用实例默认值", *inst.InstanceId, err.Error())
					host.OSType, host.OSNameEn, host.OSName = aws.StringValue(inst.PlatformDetails), "", ""
				} else {
					if _, ok := imageMap[*inst.ImageId]; ok {
						host.OSType, host.OSNameEn, host.OSName = ec2util.OsInfo(imageMap[*inst.ImageId])
					}
				}

				// set local capacity
				host.LocalStorageCapacity = int64(a.getLocalCapacity(instDeviceSizeMap, inst))
				a.SetIsp(host, option)
				// 标记版本号，版本号不一致的最后统一标记为待清理
				host.UpdateVersion = syncUpdateVersion
				// 注入agentID值
				if len(host.InnerIPAddress) > 0 && host.AgentID == "" {
					// 多内网IP,仅使用第一个生成
					agentID, genErr := agent.GenerateID(host.RegionID, fmt.Sprintf("%s:%d", host.InnerIPAddress[0], accountIndexID))
					if genErr == nil {
						host.AgentID = agentID
					}
				}
				// insert initTreeNode
				host.InitTreeNode = getTreeNode("host", host.InstanceName)
				updateData = append(updateData, host)
			}
		}

		logging.Infof("pre-save result: len is:%d\n", len(updateData))
		totalSave += len(updateData)

		result, err = models.HostResourceModel.BatchCreateOrUpdate(ctx, updateData, models.HostUpdateOption{
			UpdateTags: false,
			UpdateDesc: true,
			FromSync:   true,
		})
		if err != nil {
			logging.Errorf("HostResourceModel.BatchCreateOrUpdate.error:%+v***%+v", result, err.Error())
			return err
		}
		logging.Infof("HostResourceModel.BatchCreateOrUpdate.ok: %+v", result)
		times++
		nextToken = instanceOut.NextToken
	}

	// 更新实例的EIP
	a.freshEIPs(ctx, logging, option)

	logging.Infof("AWS EC2同步结束,共写入数据%v条", totalSave)

	// 只有全量更新才打清理标签清理
	if len(instanceIDs) == 0 {
		cleanupAmount, err := models.HostResourceModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("HostResourceModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("HostResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}

	// 用区服列表的信息来更新CMDB的数据
	err = GetBkCmdbNapSyncer().SyncCMDBHost(ctx, logging, option)
	if err != nil {
		return err
	}

	// syncHost自身逻辑跑完，跟一个cmdb绑定信息的同步
	err = GetBkCmdbNapSyncer().SyncHostBkCMDB(ctx, logging, option)
	if err != nil {
		return err
	}
	return nil
}

func getInstName(inst ec2types.Instance) string {
	for _, tag := range inst.Tags {
		if aws.StringValue(tag.Key) == "Name" {
			return aws.StringValue(tag.Value)
		}
	}
	return ""
}

func checkEc2Tags(tags []ec2types.Tag) bool {
	systemConfig := cfg.GetSystemConfig()
	if systemConfig.SyncEnv == "" {
		return true
	}

	envTag := ""
	platformEnvTag := []string{}
	for _, tag := range tags {
		if aws.StringValue(tag.Key) == "env" {
			envTag = aws.StringValue(tag.Value)
			break
		} else if aws.StringValue(tag.Key) == "platform_env" {
			platformEnvTag = strings.Split(aws.StringValue(tag.Value), ",")
		}
	}
	switch systemConfig.SyncEnv {
	case cfg.SyncEnvProd:
		return slices.Contains(constant.EnvProdTags, envTag)
	case cfg.SyncEnvTest:
		if len(platformEnvTag) > 0 {
			return slices.Contains(platformEnvTag, cfg.SyncEnvTest)
		}
		return slices.Contains(constant.EnvTestTags, envTag)
	default:
		return true
	}
}

type instanceTag interface {
	ec2types.Tag | rdstypes.Tag | elasticache.Tag | elbv2.Tag
}

// AwsDescribeInstanceBodyToHost ...
func (a *AwsSyncer) AwsDescribeInstanceBodyToHost(ctx context.Context, instances []ec2types.Instance, regionID string, accountID string) ([]*entity.HostResource, error) {
	option := SyncOption{
		IspID:    accountID,
		RegionID: regionID,
	}
	instTypeMap := map[ec2types.InstanceType]ec2types.InstanceTypeInfo{}
	imageMap := map[string]ec2types.Image{}
	instDeviceSizeMap, err := a.allVolumes(ctx, option)
	var updateData []*entity.HostResource
	for _, inst := range instances {
		host := ec2util.HostResource(inst)
		host.SetRegionID(option.RegionID)
		err = a.freshEc2InstanceType(ctx, inst.InstanceType, instTypeMap, option)
		if err != nil {
			return updateData, err
		}
		host.SetMemory(ec2util.Memory(instTypeMap[inst.InstanceType]))

		// host.SetZoneID(awssync.ZoneID(zon))

		err := a.freshImage(ctx, *inst.ImageId, imageMap, option)
		if err != nil {
			break
		}
		host.ImageID = *inst.ImageId
		if _, ok := imageMap[*inst.ImageId]; ok {
			host.OSType, host.OSNameEn, host.OSName = ec2util.OsInfo(imageMap[*inst.ImageId])
		}

		// set local capacity
		host.LocalStorageCapacity = int64(a.getLocalCapacity(instDeviceSizeMap, inst))

		a.SetIsp(host, option)

		updateData = append(updateData, host)
	}
	return updateData, nil
}

func (a *AwsSyncer) getLocalCapacity(instDeviceSizeMap map[string]map[string]int32, inst ec2types.Instance) int32 {
	// if instDeviceSizeMap==nil {
	//	return 0
	// }
	deviceSizeMap, ok := instDeviceSizeMap[*inst.InstanceId]
	if ok == false {
		return 0
	}

	size, ok := deviceSizeMap[*inst.RootDeviceName]
	if ok {
		return size
	}
	return 0
}

// func LocalStorage(inst types.Instance) {
//	inst.RootDeviceType
//	inst.RootDeviceName
//	inst.RamdiskId
//	for _, device := range inst.BlockDeviceMappings {
//		if *inst.RootDeviceName == *device.DeviceName {
//			device.Ebs.VolumeId
//		}
//	}
// }

type device struct {
	InstanceID string
	VolumeID   string
	DeviceName string
	Size       int32
}

func (a *AwsSyncer) allVolumes(ctx context.Context, option SyncOption) (map[string]map[string]int32, error) {
	var nextToken *string
	times := 1

	instDeviceSizeMap := map[string]map[string]int32{}

	for times == 1 || nextToken != nil {
		out := &ec2.DescribeVolumesOutput{}
		err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeVolumes", option.IspID, []interface{}{"", &ec2.DescribeVolumesInput{}}, out)
		if err != nil {
			msg := fmt.Sprintf("第%d次查询Volume报错：%v", times, err)
			return nil, errors.New(msg)
		}
		for _, vol := range out.Volumes {
			for _, att := range vol.Attachments {
				if att.InstanceId == nil {
					continue
				}
				if _, ok := instDeviceSizeMap[*att.InstanceId]; ok {
					instDeviceSizeMap[*att.InstanceId][*att.Device] = *vol.Size
				} else {
					instDeviceSizeMap[*att.InstanceId] = map[string]int32{
						*att.Device: *vol.Size,
					}
				}
			}
		}

		times++
		nextToken = out.NextToken
	}
	return instDeviceSizeMap, nil
}

func (a *AwsSyncer) freshEIPs(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	in := &ec2.DescribeAddressesInput{}
	out := &ec2.DescribeAddressesOutput{}
	err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeAddresses", option.IspID, []interface{}{"", in}, out)
	if err != nil {
		msg := fmt.Sprintf("获取AWS EC2 EIP信息报错：%v", err)
		logging.Error(msg)
		return errors.New(msg)
	}

	for _, addr := range out.Addresses {
		if addr.InstanceId == nil {
			continue
		}

		err = models.HostResourceModel.SetEipAddress(ctx, *addr.InstanceId, entity.InstanceEipAddress{
			IPAddress: *addr.PublicIp,
			// Bandwidth 弹性公网带宽限速(M/s)
			// Bandwidth int32 `bson:"Bandwidth" json:"Bandwidth,omitempty" xml:"Bandwidth,omitempty" field:"弹性公网带宽限速(M/s)"`
			// AllocationID 弹性公网IP的ID
			AllocationID: *addr.AllocationId,
			// IsSupportUnassociate 是否可以解绑弹性公网IP
			IsSupportUnassociate: true,
			// InternetChargeType 弹性公网IP的计费方式
			InternetChargeType: constant.PayByTraffic,
		})
		if err != nil {
			msg := fmt.Sprintf("刷新AWS EC2（%s） EIP信息报错：%v", *addr.InstanceId, err)
			logging.Error(msg)
		}
	}

	return nil
}

// SetIsp 设置云厂商信息
func (a *AwsSyncer) SetIsp(h *entity.HostResource, option SyncOption) {
	h.IspID = option.IspID
	h.IspType = constant.IspAws
}

func (a *AwsSyncer) ispID(option SyncOption) string {
	return option.IspID
}
func (a *AwsSyncer) ispType() string {
	return constant.IspAws
}

func (a *AwsSyncer) regionID(option SyncOption) string {
	return option.RegionID
}

// freshImage 更新Image信息
func (a *AwsSyncer) freshImage(ctx context.Context, imageID string, imageMap map[string]ec2types.Image, option SyncOption) error {
	if _, ok := imageMap[imageID]; ok {
		return nil
	}

	output := &ec2.DescribeImagesOutput{}
	err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeImages", option.IspID, []interface{}{"", &ec2.DescribeImagesInput{ImageIds: []string{imageID}}}, output)
	if err != nil {
		msg := fmt.Sprintf("获取AWS EC2 Image（%s）信息报错：%v", imageID, err)
		return errors.New(msg)
	}

	if len(output.Images) == 0 {
		msg := fmt.Sprintf("AWS EC2 Image（%s）不存在", imageID)
		return errors.New(msg)
	}

	imageMap[imageID] = output.Images[0]

	return nil
}

// func (a *AwsSyncer) getAvailabilityZone (ctx context.Context, client *ec2.Client, zoneName string) {
//	zoneOutput, err := client.DescribeAvailabilityZones(ctx,&ec2.DescribeAvailabilityZonesInput{ZoneNames: []string{zoneName}})
// }

// freshEc2InstanceType 更新InstanceType
func (a *AwsSyncer) freshEc2InstanceType(ctx context.Context, instType ec2types.InstanceType, instTypeMap map[ec2types.InstanceType]ec2types.InstanceTypeInfo, option SyncOption) error {
	if _, ok := instTypeMap[instType]; ok {
		return nil
	}

	output := &ec2.DescribeInstanceTypesOutput{}
	err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeInstanceTypes", option.IspID, []interface{}{"", &ec2.DescribeInstanceTypesInput{InstanceTypes: []ec2types.InstanceType{instType}}}, output)

	if err != nil {
		return err
	}
	if len(output.InstanceTypes) == 0 {
		return fmt.Errorf("InstanceType:%s不存在", instType)
	}

	instTypeMap[instType] = output.InstanceTypes[0]

	return nil
}

// allEc2InstanceType 获取全部InstanceType
func (a *AwsSyncer) allEc2InstanceType(ctx context.Context, client *ec2.Client, logging *logrus.Logger) (map[ec2types.InstanceType]ec2types.InstanceTypeInfo, error) {
	times := 1
	var nextToken *string
	instTypeMap := map[ec2types.InstanceType]ec2types.InstanceTypeInfo{}

	for times == 1 || nextToken != nil {
		logging.Infoln("第", times, "次同步EC2 InstanceType数据")

		output, err := client.DescribeInstanceTypes(ctx, &ec2.DescribeInstanceTypesInput{InstanceTypes: []ec2types.InstanceType{}})
		if err != nil {
			logging.Errorf("第%d次同步EC2 InstanceType报错：%v", times, err)
			return nil, err
		}

		for _, instType := range output.InstanceTypes {
			instTypeMap[instType.InstanceType] = instType
		}

		times++
		nextToken = output.NextToken
	}

	return instTypeMap, nil
}

// SyncSecurityGroup xxx
func (a *AwsSyncer) SyncSecurityGroup(ctx context.Context, logging *logrus.Logger, option SyncOption, securityGroupIDs ...string) error {
	instanceOut := &ec2.DescribeSecurityGroupsOutput{}
	err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeSecurityGroups", option.IspID, []interface{}{"", &ec2.DescribeSecurityGroupsInput{}}, instanceOut)
	if err != nil {
		return err
	}

	var sgEntities []*entity.SecurityGroup
	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("**************")
	for _, sg := range instanceOut.SecurityGroups {
		rulesOut := &ec2.DescribeSecurityGroupRulesOutput{}
		err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeSecurityGroupRules", option.IspID,
			[]interface{}{"", &ec2.DescribeSecurityGroupRulesInput{Filters: []ec2types.Filter{{Name: aws.String("group-id"), Values: []string{aws.StringValue(sg.GroupId)}}}}}, rulesOut)
		if err != nil {
			return err
		}

		sgEntity := AwsSecurityGroupToEntity(sg, rulesOut.SecurityGroupRules)
		sgEntity.IspType = "aws"
		sgEntity.IspID = option.IspID
		sgEntity.RegionID = option.RegionID
		sgEntity.UpdateVersion = syncUpdateVersion
		sgEntities = append(sgEntities, &sgEntity)
	}

	batchRes, err := models.SecurityGroupModel.BatchCreateOrUpdate(ctx, sgEntities)
	if err != nil {
		logging.Errorf("HostResourceModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}

	// 只有全量更新才打清理标签清理
	if len(securityGroupIDs) == 0 {
		cleanupAmount, err := models.SecurityGroupModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("SecurityGroupModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("SecurityGroupModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}
	return nil
}

// AwsSecurityGroupToEntity ...
func AwsSecurityGroupToEntity(instance ec2types.SecurityGroup, rules []ec2types.SecurityGroupRule) entity.SecurityGroup {
	var inInterface entity.SecurityGroup
	inInterface.VpcID = aws.StringValue(instance.VpcId)
	inInterface.SecurityGroupID = aws.StringValue(instance.GroupId)
	inInterface.SecurityGroupName = aws.StringValue(instance.GroupName)
	inInterface.Description = aws.StringValue(instance.Description)
	var tags []entity.Tag
	for _, tag := range instance.Tags {
		tags = append(tags, entity.Tag{TagKey: aws.StringValue(tag.Key), TagValue: aws.StringValue(tag.Value)})
	}
	inInterface.Tags = tags
	var sgPermEntities []entity.SecurityGroupPermission
	sgPermEntities = convertAwsRules(rules)
	inInterface.Permissions = sgPermEntities
	return inInterface
}

func convertAwsRules(rules []ec2types.SecurityGroupRule) []entity.SecurityGroupPermission {
	var sgPermEntities []entity.SecurityGroupPermission
	for _, rule := range rules {
		portRange := strconv.Itoa(int(aws.Int32Value(rule.ToPort)))
		if aws.Int32Value(rule.ToPort) != aws.Int32Value(rule.FromPort) {
			portRange = fmt.Sprintf("%d/%d", aws.Int32Value(rule.FromPort), aws.Int32Value(rule.ToPort))
		}
		ipProtocol := strings.ToUpper(aws.StringValue(rule.IpProtocol))
		if aws.StringValue(rule.IpProtocol) == "-1" {
			ipProtocol = "ALL"
		}
		direction := "ingress"
		if aws.BoolValue(rule.IsEgress) {
			direction = "egress"
		}
		sgPermEntities = append(sgPermEntities, entity.SecurityGroupPermission{
			Direction:           direction,
			IPProtocol:          ipProtocol,
			SourceCidrIP:        aws.StringValue(rule.CidrIpv4),
			PortRange:           portRange,
			Description:         aws.StringValue(rule.Description),
			Priority:            "1",
			Policy:              "Accept",
			SecurityGroupRuleID: aws.StringValue(rule.SecurityGroupRuleId),
		})
		// TODO: 安全组关联安全组
	}
	return sgPermEntities
}

func convertAwsPerm(perms []ec2types.IpPermission, direction string) []entity.SecurityGroupPermission {
	var sgPermEntities []entity.SecurityGroupPermission
	for _, perm := range perms {
		for _, ip := range perm.IpRanges {
			portRange := strconv.Itoa(int(aws.Int32Value(perm.ToPort)))
			if aws.Int32Value(perm.ToPort) != aws.Int32Value(perm.FromPort) {
				portRange = fmt.Sprintf("%d/%d", aws.Int32Value(perm.FromPort), aws.Int32Value(perm.ToPort))
			}
			ipProtocol := strings.ToUpper(aws.StringValue(perm.IpProtocol))
			if aws.StringValue(perm.IpProtocol) == "-1" {
				ipProtocol = "ALL"
			}
			sgPermEntities = append(sgPermEntities, entity.SecurityGroupPermission{
				Direction:    direction,
				IPProtocol:   ipProtocol,
				SourceCidrIP: aws.StringValue(ip.CidrIp),
				PortRange:    portRange,
				Description:  aws.StringValue(ip.Description),
				Priority:     "1",
				Policy:       "Accept",
			})
			// TODO: 安全组关联安全组
		}
	}
	return sgPermEntities
}

// SyncLoadBalancer ...
func (a *AwsSyncer) SyncLoadBalancer(ctx context.Context, logging *logrus.Logger, option SyncOption, loadBalancerIDs ...string) error {
	syncUpdateVersion := time.Now().Format("**************")
	lbClient := awsprovider.NewLoadBalancerClient(option.RegionID, option.IspID)
	lbs, err := lbClient.DescribeLoadbalancers(ctx, loadBalancerIDs...)
	if err != nil {
		return err
	}
	lbEntities := make([]*entity.LoadBalancer, 0)
	for _, lb := range lbs {
		lbEntity := &entity.LoadBalancer{
			LoadBalancerID:     aws.StringValue(lb.LoadBalancerArn),
			LoadBalancerName:   aws.StringValue(lb.LoadBalancerName),
			LoadBalancerType:   aws.StringValue(lb.Type),
			LoadBalancerStatus: aws.StringValue(lb.State.Code),
			VpcID:              aws.StringValue(lb.VpcId),
			AddressType:        aws.StringValue(lb.Type),
			RegionID:           option.RegionID,
			IspID:              option.IspID,
			IspType:            "aws",
			UpdateVersion:      syncUpdateVersion,
		}

		listeners, err := lbClient.DescribeListeners(ctx, aws.StringValue(lb.LoadBalancerArn))
		if err != nil {
			return err
		}
		for _, listener := range listeners {
			lbEntity.Listeners = append(lbEntity.Listeners, entity.LoadBalancerListener{
				LoadBalancerID:   aws.StringValue(lb.LoadBalancerArn),
				ListenerPort:     int32(aws.Int64Value(listener.Port)),
				ListenerProtocol: aws.StringValue(listener.Protocol),
			})
		}
		lbEntities = append(lbEntities, lbEntity)
	}

	// mongo insert
	batchRes, err := models.LoadBalancerModel.BatchCreateOrUpdate(ctx, lbEntities)
	if err != nil {
		logging.Errorf("ALB.LoadBalancerModel.BatchCreateOrUpdate.fail: %+v***%+v", batchRes, err.Error())
		return err
	}
	logging.Infof("ALB.LoadBalancerModel.BatchCreateOrUpdate.ok: %+v", batchRes)

	// 只有全量更新才打清理标签清理
	if len(loadBalancerIDs) == 0 {
		cleanupAmount, err := models.LoadBalancerModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("LoadBalancerModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("LoadBalancerModel.MarkCleanup, 标记需要清理的数量%d个", cleanupAmount)
	}
	return nil
}

// SyncEIP ...
func (a *AwsSyncer) SyncEIP(ctx context.Context, logging *logrus.Logger, option SyncOption, eipIDs ...string) error {
	in := &ec2.DescribeAddressesInput{}
	out := &ec2.DescribeAddressesOutput{}
	err := agentsdk.SyncCall(ctx, option.RegionID, "host", "DescribeAddresses", option.IspID, []interface{}{"", in}, out)
	if err != nil {
		msg := fmt.Sprintf("获取AWS EC2 EIP信息报错：%v", err)
		logging.Error(msg)
		return errors.New(msg)
	}

	var eips []*entity.Eip
	for _, addr := range out.Addresses {
		eips = append(eips, &entity.Eip{
			AllocationID: aws.StringValue(addr.AllocationId),
			Name:         aws.StringValue(addr.AllocationId),
			InstanceID:   aws.StringValue(addr.InstanceId),

			IPAddress: aws.StringValue(addr.PublicIp),
			IspID:     option.IspID,
			IspType:   "aws",
			RegionID:  option.RegionID,
		})
	}

	_, err = models.EipModel.BatchCreateOrUpdate(ctx, eips)
	if err != nil {
		return err
	}

	return nil
}

func (a *AwsSyncer) SyncPolicy(ctx context.Context, logging *logrus.Logger, option SyncOption, eipIDs ...string) error {
	return nil
}
