package synctask

import (
	"context"
	"fmt"
	"time"

	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/permission"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// TaskSyncer 任务同步器
type TaskSyncer interface {
	SyncHost(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error
	SyncRedis(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error
	SyncMysql(ctx context.Context, logging *logrus.Logger, option SyncOption, clusterIDs ...string) error
	SyncSecurityGroup(ctx context.Context, logging *logrus.Logger, option SyncOption, securityGroupIDs ...string) error
	SyncLoadBalancer(ctx context.Context, logging *logrus.Logger, option SyncOption, loadBalancerIDs ...string) error
	SyncEIP(ctx context.Context, logging *logrus.Logger, option SyncOption, loadBalancerIDs ...string) error
	SyncPolicy(ctx context.Context, logging *logrus.Logger, option SyncOption, policyIDs ...string) error
	TaskSet() map[string]string
}

// 同步任务
const (
	SyncHost          = "SyncHost"
	SyncRedis         = "SyncRedis"
	SyncMysql         = "SyncMysql"
	SyncSecurityGroup = "SyncSecurityGroup"
	SyncLoadBalancer  = "SyncLoadBalancer"
	SyncEIP           = "SyncEIP"
	SyncPolicy        = "SyncPolicy"
)

// GetTaskSyncer 根据云厂商类型获取同步器
func GetTaskSyncer(ispType string) TaskSyncer {
	switch ispType {
	case constant.IspAliyun:
		return GetAliyunSyncer()
	case constant.IspAws:
		return GetAwsSyncer()
	case constant.IspMihoyo:
		return GetMihoyoSyncer()
	case constant.IspJumpserver:
		return GetJumpserverSyncer()
	default:
		return GetAliyunSyncer()
	}
}

// SyncTask 执行同步任务
func SyncTask(ctx context.Context, syncer TaskSyncer, methodName string, logging *logrus.Logger, option SyncOption) error {
	switch methodName {
	case SyncHost:
		return syncer.SyncHost(ctx, logging, option)
	case SyncRedis:
		return syncer.SyncRedis(ctx, logging, option)
	case SyncMysql:
		return syncer.SyncMysql(ctx, logging, option)
	case SyncSecurityGroup:
		return syncer.SyncSecurityGroup(ctx, logging, option)
	case SyncLoadBalancer:
		return syncer.SyncLoadBalancer(ctx, logging, option)
	case SyncEIP:
		return syncer.SyncEIP(ctx, logging, option)
	case SyncPolicy:
		return syncer.SyncPolicy(ctx, logging, option)
	default:
		return fmt.Errorf("方法（%s）不存在", methodName)
	}
}

// TaskInput Task任务相关的入参
type TaskInput struct {
	TaskName   string
	MethodName string
}

// RunSyncTask 执行任务
func RunSyncTask(ctx context.Context, logging *logrus.Logger, input TaskInput, option SyncOption) error {
	option.print()

	syncer := GetTaskSyncer(option.IspType)

	methodName := input.MethodName
	if methodName == "" {
		value, ok := syncer.TaskSet()[input.TaskName]
		if !ok {
			return fmt.Errorf("任务:%s,方法未实现,请联系管理员", input.TaskName)
		}
		methodName = value
	}

	var retryNum uint
	for retryNum = 0; retryNum < option.RetryLimit; retryNum++ {
		if retryNum != 0 {
			logging.Error("任务执行失败,10秒后重试")
			time.Sleep(10 * time.Second) // 任务执行失败,1分钟重试
		}

		ctx, cancel := context.WithTimeout(ctx, time.Duration(option.Timeout)*time.Second)
		err := SyncTask(ctx, syncer, methodName, logging, option)
		cancel()
		if err != nil {
			logging.Errorf("任务执行失败: %v", err.Error())
		} else {
			return nil
		}
	}
	return fmt.Errorf("超出重试限制,任务执行失败")
}

// SyncOption xxx
type SyncOption struct {
	IspID      string // 云厂商账户ID
	IspType    string // 云厂商类别
	RegionID   string // 地域ID
	RateLimit  uint   // 限速
	RetryLimit uint   // 最大尝试次数
	Timeout    uint   // 超时时间
}

func (s SyncOption) print() {
	fmt.Println("IspID:", s.IspID, len(s.IspID))
	fmt.Println("IspType:", s.IspType, len(s.IspType))
	fmt.Println("RegionID:", s.RegionID, len(s.RegionID))
	fmt.Println("RateLimit:", s.RateLimit)
	fmt.Println("RetryLimit:", s.RetryLimit)
	fmt.Println("Timeout:", s.Timeout)
}

func getTreeNode(resourceType, instanceName string) string {
	ctx := context.WithValue(context.Background(), permission.Inner, permission.InnerTrue)
	treeNodeHost, _ := models.ResCreatingModel.FindOne(ctx, map[string]interface{}{
		"resource_type": resourceType,
		"instance_name": instanceName,
	})
	if treeNodeHost != nil {
		return treeNodeHost.TreeNode
	}
	return "pool"
}
