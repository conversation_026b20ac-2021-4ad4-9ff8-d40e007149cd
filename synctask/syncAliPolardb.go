package synctask

import (
	"context"
	"encoding/json"
	"slices"
	"strings"
	"time"

	polardb20170801 "github.com/alibabacloud-go/polardb-20170801/v6/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"

	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
)

// SyncPolarDB ...
type SyncPolarDB struct {
	client  *alicloud.AliPolarDBClient
	logging *logrus.Logger
	option  SyncOption
}

func checkPolartag(tags *polardb20170801.DescribeDBClustersResponseBodyItemsDBClusterTags) bool {
	systemConfig := cfg.GetSystemConfig()
	if systemConfig.SyncEnv == "" {
		return true
	}
	if tags == nil || len(tags.Tag) == 0 {
		return false
	}
	envTag := ""
	for _, tag := range tags.Tag {
		if tea.StringValue(tag.Key) == "env" {
			envTag = tea.StringValue(tag.Value)
			break
		}
	}
	switch systemConfig.SyncEnv {
	case cfg.SyncEnvProd:
		return slices.Contains(constant.EnvProdTags, envTag)
	case cfg.SyncEnvTest:
		return slices.Contains(constant.EnvTestTags, envTag)
	default:
		return true
	}
}

// SyncCluster ...
func (s SyncPolarDB) SyncCluster(ctx context.Context, syncClusterIDs ...string) ([]string, error) {
	if s.logging != nil {
		s.logging.Infoln("同步阿里云PolarDB cluster信息")
	}

	var pageSize, totalCount, currentNum int32
	pageSize = 50
	totalCount = 0
	var clusterIDs []string

	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("20060102150405")

	var lockClusters []string // 锁定的集群列表
	maxloop := 1000
	for i := 1; i < maxloop; i++ {
		response, err := s.client.DescribeDBClusters(ctx, int32(i), pageSize, syncClusterIDs...)
		if err != nil {
			if s.logging != nil {
				s.logging.Errorf("fetch polarDB cluster fail:%+v", err)
			}
			return nil, err
		}

		Clusters := response.Items
		var updateData []*entity.MysqlClusterResource

		for _, cluster := range Clusters.DBCluster {
			if !checkPolartag(cluster.Tags) {
				s.logging.Warnf("instance %s不属于该环境,跳过", *cluster.DBClusterDescription)
				continue
			}
			var inCluster entity.MysqlClusterResource
			inrec, _ := json.Marshal(cluster)
			json.Unmarshal(inrec, &inCluster)

			// sync ip whitelist and security group
			whitelistBody, err := s.client.DescribeDBClusterAccessWhitelist(ctx, tea.StringValue(cluster.DBClusterId))
			if err != nil {
				return nil, err
			}
			// get bind ip whitelist
			bindWhitelist, err := s.client.DescribeGlobalSecurityIPGroupRelation(ctx, tea.StringValue(cluster.DBClusterId))
			if err != nil {
				return nil, err
			}
			iWhitelists := make([]entity.InstanceIPWhitelist, 0)
			for _, w := range bindWhitelist.GlobalSecurityIPGroupRel {
				iWhitelists = append(iWhitelists, entity.InstanceIPWhitelist{
					GIpList:               tea.StringValue(w.GIpList),
					GlobalIgName:          tea.StringValue(w.GlobalIgName),
					GlobalSecurityGroupId: tea.StringValue(w.GlobalSecurityGroupId),
				})
			}
			inCluster.IPWhitelists = iWhitelists
			sgs := make([]string, 0)
			for _, sg := range whitelistBody.DBClusterSecurityGroups.DBClusterSecurityGroup {
				sgs = append(sgs, tea.StringValue(sg.SecurityGroupId))
			}
			inCluster.SecurityGroupIds = entity.InstanceSecurityGroupIds{
				SecurityGroupID: sgs,
			}
			var ipgs []entity.InstanceIPGroup
			for _, ipg := range whitelistBody.Items.DBClusterIPArray {
				ipgs = append(ipgs, entity.InstanceIPGroup{
					SecurityIPGroupAttribute: tea.StringValue(ipg.DBClusterIPArrayAttribute),
					SecurityIPGroupName:      tea.StringValue(ipg.DBClusterIPArrayName),
					SecurityIPList:           strings.Split(tea.StringValue(ipg.SecurityIps), ","),
				})
			}
			inCluster.IPGroups = ipgs

			var tags []entity.Tag
			if cluster.Tags != nil {
				for _, v := range cluster.Tags.Tag {
					tags = append(tags, entity.Tag{
						TagValue: tea.StringValue(v.Value),
						TagKey:   tea.StringValue(v.Key),
					})
				}
			}
			inCluster.Tags.Tag = tags
			inCluster.IspID = s.option.IspID
			inCluster.IspType = "aliyun"
			inCluster.UpdateVersion = syncUpdateVersion
			inCluster.InitTreeNode = getTreeNode("mysql", tea.StringValue(cluster.DBClusterDescription))
			updateData = append(updateData, &inCluster)
			clusterID := tea.StringValue(cluster.DBClusterId)
			if tea.StringValue(cluster.LockMode) == "ManualLock" {
				// 集群已锁定
				lockClusters = append(lockClusters, clusterID)
				if s.logging != nil {
					s.logging.Errorf("cluster-ID: %s 已被锁定....", clusterID)
				}
			} else {
				clusterIDs = append(clusterIDs, clusterID)
			}
		}
		if s.logging != nil {
			s.logging.Infof("pre-save: len is:%d", len(updateData))
		}
		if len(updateData) == 0 {
			if s.logging != nil {
				s.logging.Infoln("未获取到polarDB cluster信息")
			}
			return nil, err
		}

		result, err := models.MysqlClusterResourceModel.BatchCreateOrUpdate(context.TODO(), updateData)
		if err != nil {
			if s.logging != nil {
				s.logging.Errorf("save data fail:%+v***%+v", result, err.Error())
			}
			return nil, err
		}

		if totalCount == 0 {
			totalCount = tea.Int32Value(response.TotalRecordCount)
		}
		currentNum += pageSize
		if currentNum >= totalCount {
			break
		}
	}

	// 只有全量更新才打清理标签清理
	if len(syncClusterIDs) == 0 {
		cleanupAmount, err := models.MysqlClusterResourceModel.MarkCleanup(ctx, s.option.IspID, s.option.RegionID, syncUpdateVersion)
		if err != nil {
			s.logging.Errorf("MysqlClusterResourceModel.MarkCleanup.failed: %s", err.Error())
			return nil, err
		}
		s.logging.Infof("MysqlClusterResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}
	s.logging.Infof("aliyun.SyncMysqlCluster.end")

	return clusterIDs, nil
}

// SyncDatabase ...
func (s SyncPolarDB) SyncDatabase(ctx context.Context, clusterIDs []string) error {
	// database会限流,需要手动延长同步时间
	if s.logging != nil {
		s.logging.Infoln("同步阿里云PolarDB database信息,同步时间较长,请耐心等候...")
	}

	var pageSize, totalCount, currentNum int32
	pageSize = 100
	totalCount = 0

	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("20060102150405")

	for _, clusterID := range clusterIDs {
		totalCount = 0
		for i := 1; i < 10000; i++ {
			response, err := s.client.DescribeDatabase(ctx, int32(i), pageSize, clusterID)
			if err != nil {
				if s.logging != nil {
					s.logging.Errorf("fetch polarDB database fail:%+v", err)
				}
				return err
			}

			cluster := response.Databases
			var updateData []*entity.MysqlDatabaseResource

			for _, dbRawInfo := range cluster.Database {
				var dbObj entity.MysqlDatabaseResource
				dbRawJSON, _ := json.Marshal(dbRawInfo)
				_ = json.Unmarshal(dbRawJSON, &dbObj)
				dbObj.IspID = s.option.IspID
				dbObj.IspType = "aliyun"
				dbObj.RegionID = s.option.RegionID
				dbObj.ClusterID = clusterID
				dbObj.UpdateVersion = syncUpdateVersion
				updateData = append(updateData, &dbObj)
			}
			if s.logging != nil {
				s.logging.Infof("pre-save(%s/%d): len is:%d", clusterID, i, len(updateData))
			}

			if len(updateData) == 0 {
				if s.logging != nil {
					s.logging.Infoln("未获取到polarDB database信息, cluster_id:", clusterID)
				}
				break
			}

			result, err := models.MysqlDatabaseResourceModel.BatchCreateOrUpdate(context.TODO(), updateData)
			if err != nil {
				if s.logging != nil {
					s.logging.Errorf("save data fail:%+v***%+v", result, err.Error())
				}
				break
			}

			// 阿里云返回Database接口不会返回total,需要自行判断
			currentNum = tea.Int32Value(response.PageRecordCount)
			totalCount += currentNum
			if tea.Int32Value(response.PageRecordCount) != pageSize {
				break
			}
		}

		if s.logging != nil {
			s.logging.Infof("cluster:%s, database count:%d\n", clusterID, totalCount)
		}

		// 对cluster下的db做清理
		cleanupAmount, err := models.MysqlDatabaseResourceModel.DoCleanup(ctx, s.option.IspID, clusterID, syncUpdateVersion)
		if err != nil {
			s.logging.Errorf("MysqlDatabaseResourceModel.DoCleanup.failed: %s", err.Error())
		} else {
			s.logging.Infof("MysqlDatabaseResourceModel.DoCleanup, 清理db数量%d个", cleanupAmount)
		}
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

func (s SyncPolarDB) syncAccount(ctx context.Context, clusterIDs []string) error {
	if s.logging != nil {
		s.logging.Infoln("同步阿里云PolarDB account信息,同步时间较长,请耐心等候...")
	}
	var pageSize, totalCount, currentNum int32
	pageSize = 100
	totalCount = 0

	for _, clusterID := range clusterIDs {
		for i := 1; i < 10000; i++ {
			response, err := s.client.DescribeAccount(ctx, int32(i), pageSize, clusterID)
			if err != nil {
				if s.logging != nil {
					s.logging.Errorf("fetch polarDB database fail:%+v", err)
				}
				return err
			}

			accounts := response.Accounts
			var updateData []*entity.MysqlDatabaseAccountResource

			for _, account := range accounts {
				var inAccount entity.MysqlDatabaseAccountResource
				inrec, _ := json.Marshal(account)
				json.Unmarshal(inrec, &inAccount)
				inAccount.IspID = s.option.IspID
				inAccount.IspType = "aliyun"
				inAccount.ClusterID = clusterID
				updateData = append(updateData, &inAccount)
			}
			if s.logging != nil {
				s.logging.Infof("pre-save: len is:%d", len(updateData))
			}
			if len(updateData) == 0 {
				if s.logging != nil {
					s.logging.Infoln("未获取到polarDB account信息, cluster_id:", clusterID)
				}
				break
			}

			result, err := models.MysqlDatabaseAccountResourceModel.BatchCreateOrUpdate(context.TODO(), updateData)
			if err != nil {
				if s.logging != nil {
					s.logging.Errorf("save data fail:%+v***%+v", result, err.Error())
				}
				break
			}

			// if totalCount == 0 {
			// 	totalCount = tea.Int32Value(response.PageRecordCount)
			// }
			currentNum = tea.Int32Value(response.PageRecordCount)
			totalCount += currentNum
			if tea.Int32Value(response.PageRecordCount) != pageSize {
				break
			}

			// currentNum += pageSize
			// if currentNum >= totalCount {
			// 	break
			// }
		}
		if s.logging != nil {
			s.logging.Infof("阿里云PolarDB database: %s account已同步数量:%d\n", clusterID, totalCount)
		}
		time.Sleep(300 * time.Millisecond)
	}

	return nil
}

// SyncEndpoint ...
func (s SyncPolarDB) SyncEndpoint(ctx context.Context, clusterIDs []string) error {
	if s.logging != nil {
		s.logging.Infoln("同步阿里云PolarDB endpoint信息,同步时间较长,请耐心等候...")
	}

	for _, clusterID := range clusterIDs {
		response, err := s.client.DescribeDBClusterEndpoints(ctx, clusterID)
		if err != nil {
			if s.logging != nil {
				s.logging.Errorf("fetch polarDB database fail:%+v", err)
			}
			return err
		}
		var updateData []*entity.MysqlClusterEndpointResource

		for _, account := range response.Items {
			var inEndpoint entity.MysqlClusterEndpointResource
			inrec, _ := json.Marshal(account)
			json.Unmarshal(inrec, &inEndpoint)
			inEndpoint.IspID = s.option.IspID
			inEndpoint.IspType = "aliyun"
			inEndpoint.DBClusterID = clusterID
			updateData = append(updateData, &inEndpoint)
		}
		if s.logging != nil {
			s.logging.Infof("pre-save: len is:%d", len(updateData))
		}

		if len(updateData) == 0 {
			if s.logging != nil {
				s.logging.Infoln("未获取到polarDB endpoint信息, cluster_id:", clusterID)
			}
			break
		}

		result, err := models.MysqlClusterEndpointResourceModel.BatchCreateOrUpdate(context.TODO(), updateData)
		if err != nil {
			if s.logging != nil {
				s.logging.Errorf("save data fail:%+v***%+v", result, err.Error())
			}
			break
		}

		time.Sleep(300 * time.Millisecond)
	}

	return nil
}

// NewAliPolarDBClient 初始化polarDB-client
func NewAliPolarDBClient(option SyncOption) *alicloud.AliPolarDBClient {
	return alicloud.CreateAliPolarDBClient(option.RegionID, option.IspID)
}
