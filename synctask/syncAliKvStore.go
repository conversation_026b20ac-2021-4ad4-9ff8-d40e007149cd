package synctask

import (
	"context"
	"encoding/json"
	"slices"
	"strings"
	"time"

	rclient "github.com/alibabacloud-go/r-kvstore-20150101/v7/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
)

type syncAliKvStore struct {
	client  *alicloud.AliKvStoreClient
	logging *logrus.Logger
	option  SyncOption
}

func checkRedistag(tags *rclient.DescribeInstancesResponseBodyInstancesKVStoreInstanceTags) bool {
	systemConfig := cfg.GetSystemConfig()
	if systemConfig.SyncEnv == "" {
		return true
	}

	if tags == nil || len(tags.Tag) == 0 {
		return false
	}
	envTag := ""
	for _, tag := range tags.Tag {
		if tea.StringValue(tag.Key) == "env" {
			envTag = tea.StringValue(tag.Value)
			break
		}
	}
	switch systemConfig.SyncEnv {
	case cfg.SyncEnvProd:
		return slices.Contains(constant.EnvProdTags, envTag)
	case cfg.SyncEnvTest:
		return slices.Contains(constant.EnvTestTags, envTag)
	default:
		return true
	}
}

func (s syncAliKvStore) syncAliKvStoreInstances(ctx context.Context, inputInstanceIDs ...string) ([]string, error) {
	if s.logging != nil {
		s.logging.Infoln("准备从阿里云获取kvstore(redis)集群信息....")
	}
	var pageSize int32 = 30
	var pageNum int32 = 1

	var updateData []*entity.RedisResource
	var instanceIDs []string

	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("20060102150405")

	pulling := true
	for pulling {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			resp, err := s.client.DescribeInstances(ctx, pageNum, pageSize, inputInstanceIDs...)
			if err != nil {
				return nil, err
			}
			for _, instance := range resp.Instances.KVStoreInstance {
				if !checkRedistag(instance.Tags) {
					s.logging.Warnf("instance %s不属于该环境,跳过", *instance.InstanceName)
					continue
				}

				var inInstance entity.RedisResource
				// 以下字段需转换为时间格式
				inrec, _ := json.Marshal(&instance)
				json.Unmarshal(inrec, &inInstance)

				// sync security-group
				sgBody, err := s.client.DescribeSecurityGroupConfiguration(ctx, tea.StringValue(instance.InstanceId))
				if err != nil {
					return nil, err
				}
				var sgs entity.InstanceSecurityGroupIds
				for _, sg := range sgBody.Items.EcsSecurityGroupRelation {
					sgs = entity.InstanceSecurityGroupIds{SecurityGroupID: []string{tea.StringValue(sg.SecurityGroupId)}}
				}
				inInstance.SecurityGroupIds = sgs

				ipBody, err := s.client.DescribeSecurityIps(ctx, tea.StringValue(instance.InstanceId))
				if err != nil {
					return nil, err
				}
				// sync ip whitelist
				var ipgs []entity.InstanceIPGroup
				for _, ipg := range ipBody.SecurityIpGroups.SecurityIpGroup {
					ipgs = append(ipgs, entity.InstanceIPGroup{
						SecurityIPGroupAttribute: tea.StringValue(ipg.SecurityIpGroupAttribute),
						SecurityIPGroupName:      tea.StringValue(ipg.SecurityIpGroupName),
						SecurityIPList:           strings.Split(tea.StringValue(ipg.SecurityIpList), ","),
					})
				}
				inInstance.IPGroups = ipgs

				inInstance.IspID = s.option.IspID
				inInstance.IspType = "aliyun"
				inInstance.UpdateVersion = syncUpdateVersion
				inInstance.InitTreeNode = getTreeNode("redis", tea.StringValue(instance.InstanceName))
				updateData = append(updateData, &inInstance)
				instanceIDs = append(instanceIDs, inInstance.InstanceID)
			}

			if tea.Int32Value(resp.TotalCount) <= (pageSize*pageNum) || tea.Int32Value(resp.TotalCount) == 0 || len(inputInstanceIDs) > 0 {
				pulling = false
			}
			pageNum++
		}
	}

	if s.logging != nil {
		s.logging.Infof("pre-save result: len is:%d", len(updateData))
	}

	if len(updateData) == 0 {
		if s.logging != nil {
			s.logging.Infoln("未获取到kvStore信息")
		}
		return nil, nil
	}

	result, err := models.RedisResourceModel.BatchCreateOrUpdate(ctx, updateData)
	if err != nil {
		if s.logging != nil {
			s.logging.Errorf("save data fail:%+v***%+v", result, err.Error())
		}
		return nil, err
	}

	// 只有全量更新才打清理标签清理
	if len(inputInstanceIDs) == 0 {
		cleanupAmount, err := models.RedisResourceModel.MarkCleanup(ctx, s.option.IspID, s.option.RegionID, syncUpdateVersion)
		if err != nil {
			s.logging.Errorf("RedisResourceModel.MarkCleanup.failed: %s", err.Error())
			return nil, err
		}
		s.logging.Infof("RedisResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}
	s.logging.Infof("aliyun.SyncRedis.end")

	return instanceIDs, nil
}

func (s syncAliKvStore) syncAliKvStoreAccounts(ctx context.Context, instanceIDs []string) error {
	if s.logging != nil {
		s.logging.Infoln("准备从阿里云获取kvstore(redis)账户信息....")
	}
	var updateData []*entity.RedisAccountResource
	for _, instance := range instanceIDs {
		resp, err := s.client.DescribeAccounts(ctx, instance)
		if err != nil {
			return err
		}
		for _, account := range resp.Account {
			var inInstance entity.RedisAccountResource
			// 以下字段需转换为时间格式
			inrec, _ := json.Marshal(&account)
			json.Unmarshal(inrec, &inInstance)
			inInstance.IspID = s.option.IspID
			inInstance.IspType = "aliyun"
			updateData = append(updateData, &inInstance)
		}
	}

	if len(updateData) != 0 {
		result, err := models.RedisAccountResourceModel.BatchCreateOrUpdate(ctx, updateData)
		if err != nil {
			if s.logging != nil {
				s.logging.Errorf("save data fail:%+v***%+v", result, err.Error())
			}
			return err
		}
	}

	return nil
}

func strTimeTranslateBkTime(strTime string) string {
	if strTime == "" {
		return ""
	}
	t, err := time.Parse("2006-01-02T15:04:05Z", strTime)
	if err != nil {
		return ""
	}

	return t.Format("2006-01-02 15:04:05")
}

// NewAliKvStoreClient kv-store client
func NewAliKvStoreClient(option SyncOption) (*alicloud.AliKvStoreClient, error) {
	return alicloud.CreateAliKvStoreClient(option.RegionID, option.IspID, false, new(logrus.Logger))
}
