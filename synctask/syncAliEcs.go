package synctask

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/resprovider/alicloud"
)

// ignoreEc2Inst 是否忽略实例
func ignoreEcsInst(skipRules []string, instanceName string) bool {
	for _, skipRule := range skipRules {
		filterPattern := fmt.Sprintf("-%s-", skipRule)
		if strings.Contains(instanceName, filterPattern) {
			return true
		}
	}
	return false
}

// func strTimeTranslateBkTimeECS(strTime string) string {
// 	if strTime == "" {
// 		return ""
// 	}
// 	t, err := time.Parse("2006-01-02T15:04Z", strTime)
// 	if err != nil {
// 		return ""
// 	}
//
// 	return t.Format("2006-01-02 15:04:05")
// }

// NewAliEcsClient ecs.NewClientWithAccessKey
func NewAliEcsClient(option SyncOption) (*alicloud.AliEcsClient, error) {
	return alicloud.CreateAliEcsClient(option.RegionID, option.IspID)
}

// SyncAliEcs 同步阿里云 ecs
func SyncAliEcs(ctx context.Context, logging *logrus.Logger, option SyncOption) error {
	return GetAliyunSyncer().SyncHost(ctx, logging, option)
}
