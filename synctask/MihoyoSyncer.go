package synctask

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models"
	"platgit.mihoyo.com/jql-ops/op-cloudman/models/entity"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/agent"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/cloudsdk/agentsdk"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/mihoyosdk"
)

// MihoyoSyncer 阿里云同步器
type MihoyoSyncer struct {
}

var mihoyoSyncer *MihoyoSyncer

// GetMihoyoSyncer 获取单例
func GetMihoyoSyncer() *MihoyoSyncer {
	if mihoyoSyncer == nil {
		mihoyoSyncer = &MihoyoSyncer{}
	}
	return mihoyoSyncer
}

// TaskSet 获取GetMihoyoSyncer支持的同步方法组
func (m *MihoyoSyncer) TaskSet() map[string]string {
	return map[string]string{
		"同步米哈游Host信息": SyncHost,
	}
}

// SyncHost 同步云主机
func (m *MihoyoSyncer) SyncHost(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	if logging == nil {
		logging = logrus.StandardLogger()
	}
	logging.Infof("mihoyo.SyncHost.start")
	var err error
	// 设置本次同步的版本号
	syncUpdateVersion := time.Now().Format("**************")
	// 获取ispid的管理编号
	var accountIndexID uint32
	var accountEntity entity.Account
	accEnt := entity.GetAccountCollection(models.GetEngine())
	_, err = models.FindPK(ctx, accEnt, option.IspID, &accountEntity)
	if err != nil {
		logging.Errorf("cannot get accountIndexID, err: %s", err.Error())
		return err
	}
	accountIndexID = accountEntity.IndexID
	// 循环变量初始化
	var pageSize, currentPage int64 = 100, 1
	var totalCount = 0
	for {
		var updateData []*entity.HostResource
		hosts := []*mihoyosdk.Host{}
		err := agentsdk.SyncCall(ctx, option.RegionID, "host", "ListVms", option.IspID, []interface{}{pageSize, currentPage}, &hosts)
		if err != nil {
			logging.Warnf("mihoyo.SyncHost.ListVms.failed: %s", err.Error())
			return err
		}
		if len(hosts) == 0 {
			break
		}
		totalCount += len(hosts)
		currentPage++
		for _, h := range hosts {
			inHost := ConvertSdkHost2InHost(h)
			inHost.IspID = option.IspID
			inHost.RegionID = option.RegionID
			// 标记版本号，版本号不一致的最后统一标记为待清理
			inHost.UpdateVersion = syncUpdateVersion
			// 注入agentID值
			if len(inHost.InnerIPAddress) > 0 && inHost.AgentID == "" {
				generateRegionID := inHost.RegionID
				if inHost.RegionID == "shanghai" {
					generateRegionID = "cn-shanghai"
				}
				// 多内网IP,仅使用第一个生成
				agentID, genErr := agent.GenerateID(generateRegionID, fmt.Sprintf("%s:%d", inHost.InnerIPAddress[0], accountIndexID))
				if genErr == nil {
					inHost.AgentID = agentID
				}
			}
			updateData = append(updateData, inHost)
		}
		dataDump, _ := json.Marshal(updateData)
		logging.Infof("mihoyo.SyncHost.save.dump: %s", string(dataDump))
		_, err = models.HostResourceModel.BatchCreateOrUpdate(ctx, updateData, models.HostUpdateOption{
			UpdateTags: false,
			UpdateDesc: true,
			FromSync:   true,
		})
		if err != nil {
			logging.Warnf("mihoyo.SyncHost.save.failed: %s", err.Error())
			return err
		}
	}

	// 只有全量更新才打清理标签清理
	if len(instanceIDs) == 0 {
		cleanupAmount, err := models.HostResourceModel.MarkCleanup(ctx, option.IspID, option.RegionID, syncUpdateVersion)
		if err != nil {
			logging.Errorf("HostResourceModel.MarkCleanup.failed: %s", err.Error())
			return err
		}
		logging.Infof("HostResourceModel.MarkCleanup, 标记未获得同步的数量%d个", cleanupAmount)
	}
	// 做一次CMDB增量同步
	err = GetBkCmdbNapSyncer().SyncAddHost(ctx, logging, option)
	if err != nil {
		return err
	}

	// syncHost自身逻辑跑完，跟一个cmdb绑定信息的同步
	err = GetBkCmdbNapSyncer().SyncHostBkCMDB(ctx, logging, option)
	if err != nil {
		return err
	}
	logging.Infof("mihoyo.SyncHost.end")

	return nil
}

// ignoreEc2Inst 是否忽略实例
func (m *MihoyoSyncer) ignoreEcsInst(skipRules []string, instanceName string) bool {
	for _, skipRule := range skipRules {
		filterPattern := fmt.Sprintf("-%s-", skipRule)
		if strings.Contains(instanceName, filterPattern) {
			return true
		}
	}
	return false
}

// SyncRedis mihoyo无redis类型
func (m *MihoyoSyncer) SyncRedis(ctx context.Context, logging *logrus.Logger, option SyncOption, instanceIDs ...string) error {
	return nil
}

// SyncMysql mihoyo无mysql类型
func (m *MihoyoSyncer) SyncMysql(ctx context.Context, logging *logrus.Logger, option SyncOption, clusterIDs ...string) error {
	return nil
}

// SyncSecurityGroup xxx
func (m *MihoyoSyncer) SyncSecurityGroup(ctx context.Context, logging *logrus.Logger, option SyncOption, securityGroupIDs ...string) error {
	return nil
}

// SyncLoadBalancer ...
func (m *MihoyoSyncer) SyncLoadBalancer(ctx context.Context, logging *logrus.Logger, option SyncOption, loadBalancerIDs ...string) error {
	return nil
}

// SyncEIP ...
func (m *MihoyoSyncer) SyncEIP(ctx context.Context, logging *logrus.Logger, option SyncOption, eipIDs ...string) error {
	return nil
}

// ConvertSdkHost2InHost sdk结构转云管结构
func ConvertSdkHost2InHost(h *mihoyosdk.Host) *entity.HostResource {
	inHost := &entity.HostResource{}
	inHost.InstanceID = h.ID
	inHost.HostName = h.Name
	inHost.InstanceName = h.Name
	inHost.InstanceTypeFamily = h.VMType
	inHost.InstanceType = fmt.Sprintf("%s - %dC%dG", h.VMType, h.Resource.CPU, h.Resource.Memory)
	inHost.CPU = int32(h.Resource.CPU)
	inHost.Memory = int32(h.Resource.Memory)
	inHost.GPUAmount = int32(h.Resource.Gpu)
	inHost.IspType = constant.IspMihoyo
	cTime, _ := time.Parse("2006-01-02 15:04:05", h.CreateTime)
	inHost.CreatedTime = cTime.Unix()
	inHost.CreationTime = cTime.Unix() // 这个才是显示的创建时间
	inHost.OSType = strings.ToLower(h.Image.OsType)
	inHost.OSName = fmt.Sprintf("%s %s", h.Image.OsName, h.Image.OsVersion)
	inHost.ImageID = h.Image.ImageID
	switch h.Status {
	case "running":
		inHost.Status = "Running"
	case "stopped":
		inHost.Status = "Stopped"
	case "starting", "stopping":
		inHost.Status = "Pending"
	default:
		inHost.Status = "Unknown"
	}
	for _, n := range h.Network {
		if n.Type == "private" {
			inHost.InnerIPAddress = append(inHost.InnerIPAddress, n.IP)
		} else {
			inHost.PublicIPAddress = append(inHost.InnerIPAddress, n.IP)
		}
	}
	for _, v := range h.Volumes {
		inHost.LocalStorageCapacity += v.Size
		inHost.LocalStorageAmount++
	}
	return inHost
}

func (m *MihoyoSyncer) SyncPolicy(ctx context.Context, logging *logrus.Logger, option SyncOption, eipIDs ...string) error {
	return nil
}
