package schema

// RedisAccountResourceQueryParams 接口查询参数
type RedisAccountResourceQueryParams struct {
	PaginationParam
	RedisAccountResourceColumnParam
	SearchParams
	OrderParams
}

// RedisAccountResourceColumnParam xxx
type RedisAccountResourceColumnParam struct {
	InstanceID string `json:"instance_id"`
	IspID      string `json:"isp_id"`
	IspType    string `json:"isp_type"`
}

// BuildToMap xxx
func (a RedisAccountResourceColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"instance_id": a.InstanceID,
		"isp_id":      a.IspID,
		"isp_type":    a.IspType,
	}
}

// RedisAccountResourceParams xxx
var RedisAccountResourceParams = []string{"ClusterID"}

// GetSearch xxx
func (a RedisAccountResourceQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: RedisAccountResourceParams,
	}
}
