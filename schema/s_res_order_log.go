package schema

// ResourceOrderLogQueryParams 接口查询参数
type ResourceOrderLogQueryParams struct {
	PaginationParam
	ResourceOrderColumnParam
	SearchParams
	OrderParams
}

// ResourceOrderLogColumnParam xxx
type ResourceOrderLogColumnParam struct {
	BelongOrderID string `bson:"belong_order_id" json:"belong_order_id"`
}

// BuildToMap xxx
func (a ResourceOrderLogColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"belong_order_id": a.BelongOrderID,
	}
}
