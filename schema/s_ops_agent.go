package schema

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg"
)

// OpsAgentQueryParams 接口查询参数
type OpsAgentQueryParams struct {
	PaginationParam
	OpsAgentColumnParam
	OpsAgentSearchParam
	SearchColumnField
	OrderParams
}

// OpsAgentColumnParam 查询字段过滤
type OpsAgentColumnParam struct {
	Status   int32  `json:"status"`
	Env      string `json:"env"`
	NodeType string `json:"node_type"`
	Hostname string `json:"hostname"`
	Version  string `json:"version"`
	HostIP   string `json:"host_ip"`
	AgentID  string `json:"agent_id"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a OpsAgentColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"node_type": a.NodeType,
		"env":       a.Env,
		"version":   a.Version,
		"hostname":  a.Hostname,
		"host_ip":   a.HostIP,
		"agent_id":  a.AgentID,
	}
	lostWait := cfg.GetOpsAgentConf().LostWait
	if a.Status == 1 {
		m["status"] = 1
		m["updated_time"] = bson.M{"$gte": time.Now().Unix() - lostWait}
	}
	if a.Status == 2 {
		m["status"] = 2
	}
	if a.Status == 3 {
		m["status"] = 1
		m["updated_time"] = bson.M{"$lt": time.Now().Unix() - lostWait}
	}
	if a.NodeType == "proxy" || a.NodeType == "master" {
		m["node_type"] = map[string][]string{"$in": {"proxy", "master"}}
	}
	return m
}

// OpsAgentSearchParam 字段搜索参数
type OpsAgentSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a OpsAgentQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"hostname", "host_ip", "agent_id"},
	}
}

// GetCustomSearch 构建模糊查询
func (a OpsAgentQueryParams) GetCustomSearch() SearchParams {
	searchField := []string{"hostname", "host_ip", "agent_id"}
	if len(a.SearchParam) != 0 {
		searchField = a.SearchParam
	}
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: searchField,
	}
}
