package schema

import "go.mongodb.org/mongo-driver/bson"

// PolicyQueryParam 接口查询参数
type PolicyQueryParam struct {
	PaginationParam
	PolicyColumnParam
	PolicySearchParam
	SearchColumnField
	OrderParams
}

// PolicyColumnParam 查询字段过滤
type PolicyColumnParam struct {
	IspID       string `json:"isp_id"`
	Name        string `json:"name"`
	Desc        string `json:"desc"`
	NeedCleanup bool   `json:"NeedCleanup"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a PolicyColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{
		"IspID":       a.IspID,
		"PolicyName":  bson.M{"$regex": a.Name},
		"Description": bson.M{"$regex": a.Desc},
		"NeedCleanup": a.NeedCleanup,
	}
	return filter
}

// PolicySearchParam 字段搜索参数
type PolicySearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a PolicySearchParam) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{""},
	}
}
