package schema

import (
	"go.mongodb.org/mongo-driver/bson"
)

// AgentTaskQueryParams 接口查询参数
type AgentTaskQueryParams struct {
	PaginationParam
	AgentTaskColumnParam
	SearchColumnField
	OrderParams
}

// AgentTaskColumnParam xxx
type AgentTaskColumnParam struct {
	AccountID    string
	Risky        string
	Result       string
	ResourceType string
	Start        int32
	End          int32
}

// BuildToMap ...
// 防止循环调用，此处枚举不从task包里取常量
func (a AgentTaskColumnParam) BuildToMap() map[string]interface{} {
	res := map[string]interface{}{}
	if a.AccountID != "" {
		res["account_id"] = a.AccountID
	}
	if a.ResourceType != "" {
		res["resource_type"] = a.ResourceType
	}
	if a.Start*a.End != 0 {
		res["extra.submit_time"] = bson.D{{"$gte", int64(a.Start) * 1e9}, {"$lte", int64(a.End) * 1e9}}
	}
	if a.Risky == "1" {
		res["extra.risky"] = 1
	} else if a.Risky == "2" {
		res["extra.risky"] = 0
	}
	if a.Result == "full_success" {
		res["status"] = 3
		res["extra.business_err_msg"] = ""
	} else if a.Result == "call_success" {
		res["status"] = 3
		res["extra.business_err_msg"] = bson.D{{"$ne", ""}}
	} else if a.Result == "call_failed" {
		res["status"] = 4
	} else if a.Result == "call_unfinished" {
		res["$nor"] = bson.A{
			bson.D{{"status", 3}},
			bson.D{{"status", 4}},
		}
	}
	return res
}

// AgentTaskSearchParam xxx
type AgentTaskSearchParam struct {
	Keywords    string
	SearchParam []string
}

// GetSearch xxx
func (a AgentTaskQueryParams) GetSearch() SearchParams {
	return SearchParams{}
}
