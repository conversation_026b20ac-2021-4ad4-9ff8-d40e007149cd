package schema

// CloudAgentQueryParams 接口查询参数
type CloudAgentQueryParams struct {
	PaginationParam
	CloudAgentColumnParam
	CloudAgentSearchParam
	OrderParams
}

// CloudAgentColumnParam xxx
type CloudAgentColumnParam struct {
	Status    string
	AccountID string
	InnerIP   string
}

// BuildToMap xxx
func (a CloudAgentColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"status":     a.Status,
		"account_id": a.AccountID,
		"inner_ip":   a.InnerIP,
	}
}

// CloudAgentSearchParam xxx
type CloudAgentSearchParam struct {
	Keywords    string
	SearchParam []string
}

// CloudAgentParams xxx
var CloudAgentParams = []string{"name"}

// GetSearch xxx
func (a CloudAgentQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: CloudAgentParams,
	}
}
