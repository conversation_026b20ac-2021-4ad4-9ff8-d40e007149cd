package schema

// AccountParamQueryParams 接口查询参数
type AccountParamQueryParams struct {
	PaginationParam
	AccountParamColumnParam
	// AccountParamSearchParam
	OrderParams
}

// // GetSearch xxx
// func (a AccountParamQueryParams) GetSearch() SearchParams {
// 	return SearchParams{
// 		Keywords:    a.Keywords,
// 		SearchField: AccountParamsSearchField,
// 	}
// }

// AccountParamColumnParam xxx
type AccountParamColumnParam struct {
	// Enabled       int
	ParamType     string
	BindRegion    string
	BindAccountID string
}

// BuildToMap xxx
func (a AccountParamColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"bind_account_id": a.BindAccountID,
		"param_type":      a.ParamType,
		"bind_region":     a.BindRegion,
	}
}

// APHostTplParams 主机参数模板支持传入的参数
type APHostTplParams struct {
	InstanceChargeType []string                  `json:"InstanceChargeType" validate:"dive,oneof=PrePaid PostPaid"`
	Images             []APHostTplImgParam       `json:"Images"`                         // 镜像列表
	NetWorks           []APHostTplAPNetWorkParam `json:"NetWorks"`                       // 网络配置列表
	Instances          []APHostTplInstanceParam  `json:"Instances"`                      // 实例规格列表
	SystemDisk         APHostTplSystemDisk       `json:"SystemDisk" validate:"required"` // 系统盘
	DataDisk           APHostTplDataDisk         `json:"DataDisk" validate:"required"`   // 数据盘
}

// APHostTplImgParam 镜像参数列表
type APHostTplImgParam struct {
	ID         string `json:"image_id" validate:"required"`          // 镜像id
	Name       string `json:"image_name" validate:"required"`        // 镜像名称
	OwnerAlias string `json:"image_owner_alias" validate:"required"` // 镜像来源
	CreateTime string `json:"CreateTime" validate:"required"`
	OSType     string `json:"os_type" validate:"required"`    //  镜像的操作系统类型
	OSName     string `json:"os_name" validate:"required"`    // 显示的中文名称
	IsPublic   bool   `json:"is_public" validate:"is_public"` // 是否公共镜像
}

// APHostTplAPNetWorkParam 网络配置列表
type APHostTplAPNetWorkParam struct {
	APHostVPC
	VSwitches      []APHostVSwitch       `json:"VSwitches" validate:"required"` // 交换机列表,未绑定交换机的vpc不能用
	SecurityGroups []APHostSecurityGroup `json:"SecurityGroups"`                // 安全组不是必须的
}

// APHostVPC vpc网络配置
type APHostVPC struct {
	Name        string `json:"name" validate:"required"`   // vpc名称
	ID          string `json:"vpc_id" validate:"required"` // vpc-id
	Status      string `json:"status" validate:"required"` // 状态
	IsDefault   bool   `json:"isDefault"`                  // 是否为默认vpc
	Description string `json:"description"`                // vpc描述
}

// APHostVSwitch 交换机
type APHostVSwitch struct {
	Status string `json:"status" validate:"required"`
	ID     string `json:"vSwitch_id" validate:"required"`
	Name   string `json:"vSwitch_name"`
}

// APHostSecurityGroup 安全组
type APHostSecurityGroup struct {
	Description string `json:"Description"` // 描述
	ID          string `json:"SecurityGroupId" validate:"required"`
	Name        string `json:"SecurityGroupName"`
	VpcID       string `json:"VpcId"`
}

// APHostTplInstanceParam 实例规格列表
type APHostTplInstanceParam struct {
	MemorySize         float32 `json:"MemorySize"`         // 内存大小
	CPUCoreCount       int32   `json:"CpuCoreCount"`       // vpc内核数目
	InstanceTypeFamily string  `json:"InstanceTypeFamily"` // 实例规格组
	InstanceTypeID     string  `json:"InstanceTypeID"`     // 实例规格ID
}

// APHostTplSystemDisk 主机系统盘配置
type APHostTplSystemDisk struct {
	CateGoryOptions []CommonLabelVal `validate:"required"` // 云盘类型
	MinSize         uint32           `validate:"gte=20"`   // 最小容量
	MaxSize         uint32           `validate:"gte=40"`   // 最大容量
}

// CommonLabelVal 通用标签
type CommonLabelVal struct {
	Label string `json:"label" validate:"required"`
	Value string `json:"value" validate:"required"`
}

// APHostTplDataDisk 数据盘配置
type APHostTplDataDisk struct {
	CateGoryOptions []CommonLabelVal `validate:"required"` // 云盘类型
	MinSize         uint32           `validate:"gte=20"`   // 最小容量
	MaxSize         uint32           `validate:"gte=40"`   // 最大容量
	MinCount        uint32
	MaxCount        uint32 `validate:"lte=16"` // 允许挂载的最大数量
}

// // AccountParamSearchParam xxx
// type AccountParamSearchParam struct {
// 	Keywords    string
// 	SearchParam []string
// }

// AccountParamsSearchField xxx
// var AccountParamsSearchField = []string{"name"}
