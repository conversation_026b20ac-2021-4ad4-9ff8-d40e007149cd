package schema

// RecyclePolicyQueryParams 接口查询参数
type RecyclePolicyQueryParams struct {
	PaginationParam
	RecyclePolicyColumnParam
	SearchParams
	OrderParams
}

// RecyclePolicyColumnParam xxx
type RecyclePolicyColumnParam struct {
	Name         string
	InstanceType string
}

// BuildToMap xxx
func (a RecyclePolicyColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"instance_type": a.InstanceType,
	}
}

// // RecyclePolicySearchParam xxx
// type RecyclePolicySearchParam struct {
// 	Keywords    string
// 	SearchParams []string
// }

// RecyclePolicyParams xxx
var RecyclePolicyParams = []string{"name"}

// GetSearch xxx
func (a RecyclePolicyQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: RecyclePolicyParams,
	}
}
