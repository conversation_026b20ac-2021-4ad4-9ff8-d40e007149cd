package schema

// DomainQueryParams 接口查询参数
type DomainQueryParams struct {
	PaginationParam
	DomainColumnParam
	DomainSearchParam
	SearchColumnField
	OrderParams
}

// DomainColumnParam 查询字段过滤
type DomainColumnParam struct {
	RegionID string `json:"RegionID"`
	IspID    string `json:"isp_id"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a DomainColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{
		"isp_id":   a.IspID,
		"RegionID": a.RegionID,
	}
	return filter
}

// DomainSearchParam 字段搜索参数
type DomainSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a DomainQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"DomainName"},
	}
}
