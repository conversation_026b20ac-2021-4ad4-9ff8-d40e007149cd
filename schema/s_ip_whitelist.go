package schema

import "go.mongodb.org/mongo-driver/bson"

// IPWhitelistQueryParam 接口查询参数
type IPWhitelistQueryParam struct {
	PaginationParam
	IPWhitelistColumnParam
	IPWhitelistSearchParam
	SearchColumnField
	OrderParams
}

// IPWhitelistColumnParam 查询字段过滤
type IPWhitelistColumnParam struct {
	WhitelistId string
	Name        string
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a IPWhitelistColumnParam) BuildToMap() map[string]interface{} {
	filter := make(map[string]interface{})
	if a.WhitelistId != "" {
		filter["GlobalSecurityGroupId"] = a.WhitelistId
	}

	if a.Name != "" {
		filter["GlobalIgName"] = bson.M{
			"$regex": a.Name,
		}
	}

	return filter
}

// IPWhitelistSearchParam 字段搜索参数
type IPWhitelistSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a IPWhitelistSearchParam) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{""},
	}
}
