package schema

import (
	"fmt"
	"time"
)

// HostResourceQueryParams 接口查询参数
type HostResourceQueryParams struct {
	PaginationParam
	HostResourceColumnParam
	SearchParams
	SearchColumnField
	OrderParams
}

// HostResourceColumnParam xxx
type HostResourceColumnParam struct {
	InstanceID                string                `json:"InstanceID"`
	Status                    string                `json:"Status"`
	IspID                     string                `json:"isp_id"`
	IspType                   string                `json:"isp_type"`
	OSType                    string                `json:"OSType"`
	RegionID                  string                `json:"RegionID"`
	TagKey                    string                `json:"tag_key"`
	TagValues                 []string              `json:"tag_values"`
	AgentStatus               string                `json:"agent_status"`
	AgentEnv                  string                `json:"agent_env"`
	MonitorStatus             string                `json:"monitor_status"`
	UpdateTime                UpdateTimeColumnParam `json:"update_time"`
	ExpireBeforeTime          int32                 `json:"expire_before_time"`
	ReleaseResource           bool                  `json:"release_resource"`
	DisplayRecyclable         bool                  `json:"display_recyclable"` // 是否显示已加入回收站资源
	DisplayNeedCleanup        bool                  `json:"display_need_cleanup"`
	BatchOSName               []string              `json:"batch_os_name"`
	BatchAgentStatus          []string              `json:"batch_agent_status"`
	SecurityGroupIds          []string              `json:"security_group_ids"`
	AndSearchSecurityGroupIds bool                  `json:"and_search_security_group_ids"`
	BatchMonitorStatus        []string              `json:"batch_monitor_status"`
}

// UpdateTimeColumnParam 更新时间字段筛选
type UpdateTimeColumnParam struct {
	Ref string `json:"ref"` // 大于/小于
	Val int32  `json:"val"` // 值
}

// BuildToMap xxx
func (a HostResourceColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{
		"Status":        a.Status,
		"InstanceID":    a.InstanceID,
		"isp_id":        a.IspID,
		"isp_type":      a.IspType,
		"OSType":        a.OSType,
		"RegionID":      a.RegionID,
		"agent_status":  a.AgentStatus,
		"agent_env":     a.AgentEnv,
		"MonitorStatus": a.MonitorStatus,
		"Recyclable":    false,
	}
	if a.Status != "" {
		filter["NeedCleanup"] = map[string]interface{}{"$ne": true}
	}
	if a.UpdateTime.Val != 0 {
		filter["updated_time"] = map[string]int32{
			fmt.Sprintf("$%s", a.UpdateTime.Ref): a.UpdateTime.Val,
			"$ne":                                0,
		}
	}
	if len(a.BatchAgentStatus) > 0 {
		var batchStatus []int32
		for _, statusText := range a.BatchAgentStatus {
			status := AgentStatusTextToCode(statusText)
			if status != -1 {
				batchStatus = append(batchStatus, status)
			}
			filter["agent_status"] = map[string][]int32{"$in": batchStatus}
		}
	} else {
		if a.AgentStatus != "" {
			status := AgentStatusTextToCode(a.AgentStatus)
			if status != -1 {
				filter["agent_status"] = map[string]int32{"$eq": status}
			}
		}
	}

	if len(a.BatchMonitorStatus) > 0 {
		var batchStatus []int32
		for _, statusText := range a.BatchMonitorStatus {
			status := MonitorStatusTextToCode(statusText)
			if status != -2 {
				batchStatus = append(batchStatus, status)
			}
		}
		filter["MonitorStatus"] = map[string][]int32{"$in": batchStatus}
	} else if a.MonitorStatus != "" {
		status := MonitorStatusTextToCode(a.MonitorStatus)
		if status != -2 {
			filter["MonitorStatus"] = map[string]int32{
				"$eq": status,
			}
		}
	}

	if len(a.BatchOSName) > 0 {
		filter["OSName"] = map[string][]string{"$in": a.BatchOSName}
	}

	// bson.E{Key: "ExpiredTime", Value: bson.M{"$lte": time.Now().Add(day).Unix(), "$ne": 0}})
	if a.ExpireBeforeTime != 0 {
		filter["ExpiredTime"] = map[string]int64{
			"$lte": time.Now().Add(time.Duration(a.ExpireBeforeTime) * 24 * time.Hour).Unix(),
			"$ne":  0,
		}
	}
	// bson.E{Key: "AutoReleaseTime", Value: bson.M{"$lte": time.Now().Add(day).Unix(), "$ne": 0}})
	if a.ReleaseResource {
		filter["AutoReleaseTime"] = map[string]int64{
			"$lte": time.Now().Add(7 * 24 * time.Hour).Unix(),
			"$ne":  0,
		}
	}
	if a.DisplayRecyclable {
		delete(filter, "Recyclable")
	}
	if a.DisplayNeedCleanup {
		filter["NeedCleanup"] = true
	}
	if len(a.SecurityGroupIds) > 0 {
		if a.AndSearchSecurityGroupIds {
			filter["SecurityGroupIds.SecurityGroupId"] = map[string]interface{}{"$all": a.SecurityGroupIds}
		} else {
			filter["SecurityGroupIds.SecurityGroupId"] = map[string]interface{}{"$in": a.SecurityGroupIds}
		}
	}
	return filter
}

// AgentStatusTextToCode ...
func AgentStatusTextToCode(statusText string) int32 {
	switch statusText {
	case "Running":
		return 1
	case "Lost":
		return 2
	case "NotFound":
		return 0
	case "Updating":
		return 6
	default:
		return -1
	}
}

// MonitorStatusTextToCode ...
func MonitorStatusTextToCode(statusText string) int32 {
	switch statusText {
	case "Running":
		return 1
	case "UnRun":
		return 0
	case "NotFound":
		return -1
	default:
		return -2
	}
}

// HostResourceParams xxx
var HostResourceParams = []string{"InstanceID"}

// GetSearch xxx
func (a HostResourceQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: HostResourceParams,
	}
}
