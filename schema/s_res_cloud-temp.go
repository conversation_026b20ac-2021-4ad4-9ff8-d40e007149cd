package schema

import "platgit.mihoyo.com/jql-ops/op-cloudman/pkg/validator"

// Tags 通用标签
type Tags struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// AliEcsTemplate 阿里云ecs模板
type AliEcsTemplate struct {
	ZoneID                  string                   `json:"ZoneID"`
	InstanceChargeType      string                   `json:"InstanceChargeType" validate:"required"`
	OsType                  string                   `json:"os_type" validate:"required"`
	ImageID                 interface{}              `json:"ImageID" validate:"required"`
	InstanceType            string                   `json:"InstanceType" validate:"required"`
	InstanceTypeMap         interface{}              `json:"InstanceTypeMap" validate:"required"`
	LoginType               string                   `json:"LoginType" validate:"required"`
	Password                string                   `json:"password"`
	PasswordInherit         bool                     `json:"PasswordInherit,omitempty"`
	KeyPairName             string                   `json:"KeyPairName"`
	SystemDisk              AliEcsTemplateSystemDisk `json:"SystemDisk" validate:"required"`
	DataDisk                []AliEcsTemplateDataDisk `json:"DataDisk"`
	InternetChargeType      string                   `json:"InternetChargeType"`      // 流量计费类型
	InternetMaxBandwidthOut uint32                   `json:"InternetMaxBandwidthOut"` // 出口带宽
	VpcID                   interface{}              `json:"VpcId" validate:"required"`
	VSwitchID               interface{}              `json:"VSwitchID" validate:"required"`
	SecurityGroupID         interface{}              `json:"SecurityGroupID" validate:"required"`
	InstanceName            string                   `json:"InstanceName"`
	HostName                string                   `json:"HostName"`
	DefaultTags             []Tags                   `json:"default_tags"`
	IoOptimized             string                   `json:"IoOptimized"`
}

// AliEcsTemplateSystemDisk 阿里云系统盘
type AliEcsTemplateSystemDisk struct {
	PerformanceLevel string `json:"PerformanceLevel" validate:"required"`
	Category         string `json:"Category" validate:"required"`
	Size             int32  `json:"Size" validate:"required"`
	// Min              int32    `json:"min" validate:"required"`
	// Max              int32    `json:"max" validate:"required"`
}

// AliEcsTemplateDataDisk 阿里云数据盘
type AliEcsTemplateDataDisk struct {
	AliEcsTemplateSystemDisk
	Limit int32 `json:"limit"`
}

// Validate validate
func (a AliEcsTemplate) Validate() error {
	return validator.Validate.Struct(a)
}

// BuyAliEcsInstance xx
type BuyAliEcsInstance struct {
	ImageID string `json:"ImageID"`
}
