package schema

// OpsAgentInstallConfigQueryParams 接口查询参数
type OpsAgentInstallConfigQueryParams struct {
	PaginationParam
	OpsAgentInstallConfigColumnParam
	OpsAgentInstallConfigSearchParam
	OrderParams
}

// OpsAgentInstallConfigColumnParam 查询字段过滤
type OpsAgentInstallConfigColumnParam struct {
	Status   int32  `json:"status"`
	NodeType string `json:"node_type"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a OpsAgentInstallConfigColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"status":    a.Status,
		"node_type": a.NodeType,
	}
	return m
}

// OpsAgentInstallConfigSearchParam 字段搜索参数
type OpsAgentInstallConfigSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a OpsAgentInstallConfigQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"hostname", "host_ip", "version", "agent_id"},
	}
}
