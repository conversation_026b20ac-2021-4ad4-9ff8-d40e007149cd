package schema

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
)

// MysqlClusterResourceQueryParams 接口查询参数
type MysqlClusterResourceQueryParams struct {
	PaginationParam
	MysqlClusterResourceColumnParam
	SearchParams
	SearchColumnField
	OrderParams
}

// MysqlClusterResourceColumnParam xxx
type MysqlClusterResourceColumnParam struct {
	ClusterID                 string                `json:"ClusterID"`
	Status                    string                `json:"Status"`
	IspID                     string                `json:"isp_id"`
	IspType                   string                `json:"isp_type"`
	RegionID                  string                `json:"RegionID"`
	TagKey                    string                `json:"tag_key"`
	TagValues                 []string              `json:"tag_values"`
	DisplayRecyclable         bool                  `json:"display_recyclable"`
	DisplayNeedCleanup        bool                  `json:"display_need_cleanup"`
	DBClusterStatus           string                `json:"DBClusterStatus"`
	UpdateTime                UpdateTimeColumnParam `json:"update_time"`
	SecurityGroupIds          []string              `json:"security_group_ids"`
	AndSearchSecurityGroupIds bool                  `json:"and_search_security_group_ids"`
}

// BuildToMap xxx
func (a MysqlClusterResourceColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"status":          a.Status,
		"ClusterID":       a.ClusterID,
		"isp_id":          a.IspID,
		"isp_type":        a.IspType,
		"RegionID":        a.RegionID,
		"Recyclable":      bson.D{{Key: "$ne", Value: true}},
		"DBClusterStatus": a.DBClusterStatus,
	}
	if a.DBClusterStatus != "" {
		m["NeedCleanup"] = map[string]interface{}{"$ne": true}
	}
	if a.DBClusterStatus == "Running" {
		m["DBClusterStatus"] = bson.D{{"$in", bson.A{"Running", "available"}}}
	}
	if a.UpdateTime.Val != 0 {
		m["updated_time"] = map[string]int32{
			fmt.Sprintf("$%s", a.UpdateTime.Ref): a.UpdateTime.Val,
			"$ne":                                0,
		}
	}
	if a.DisplayRecyclable {
		delete(m, "Recyclable")
	}
	if a.DisplayNeedCleanup {
		m["NeedCleanup"] = true
	}
	if len(a.SecurityGroupIds) > 0 {
		if a.AndSearchSecurityGroupIds {
			m["SecurityGroupIds.SecurityGroupId"] = map[string]interface{}{"$all": a.SecurityGroupIds}
		} else {
			m["SecurityGroupIds.SecurityGroupId"] = map[string]interface{}{"$in": a.SecurityGroupIds}
		}
	}
	return m
}

// MysqlClusterResourceParams xxx
var MysqlClusterResourceParams = []string{"ClusterID"}

// GetSearch xxx
func (a MysqlClusterResourceQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: MysqlClusterResourceParams,
	}
}
