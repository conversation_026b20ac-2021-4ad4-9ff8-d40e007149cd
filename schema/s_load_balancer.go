package schema

// LoadBalancerQueryParams 接口查询参数
type LoadBalancerQueryParams struct {
	PaginationParam
	LoadBalancerColumnParam
	LoadBalancerSearchParam
	SearchColumnField
	OrderParams
}

// LoadBalancerColumnParam 查询字段过滤
type LoadBalancerColumnParam struct {
	RegionID    string `json:"RegionID"`
	IspID       string `json:"isp_id"`
	Address     string `json:"address"`
	LbType      string `json:"LoadBalancerType"` // alb 应用型, slb 传统型
	NeedCleanup bool   `json:"NeedCleanup"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a LoadBalancerColumnParam) BuildToMap() map[string]interface{} {
	lbType := make([]string, 0)
	switch a.LbType {
	case "alb":
		lbType = append(lbType, "alb", "application")
	case "slb":
		lbType = append(lbType, "slb", "network")
	}

	filter := map[string]interface{}{
		"isp_id":           a.IspID,
		"RegionID":         a.RegionID,
		"Address":          a.Address,
		"LoadBalancerType": map[string]interface{}{"$in": lbType},
		"NeedCleanup":      a.NeedCleanup,
	}
	return filter
}

// LoadBalancerSearchParam 字段搜索参数
type LoadBalancerSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a LoadBalancerQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"LoadBalancerName"},
	}
}
