package schema

// RegionQueryParams 接口查询参数
type RegionQueryParams struct {
	PaginationParam
	RegionColumnParam
	RegionSearchParam
	OrderParams
}

// RegionColumnParam 查询字段过滤
type RegionColumnParam struct {
	Name  string `json:"name" form:"name"`
	IspType string `json:"isp_type" form:"isp_type"`
	ZType string `json:"z_type" form:"z_type"` // 过滤区别零值
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a RegionColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"name":   a.Name,
		"isp_type": a.IspType,
		"z_type": a.ZType,
	}
	return m
}

// RegionSearchParam 字段搜索参数
type RegionSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a RegionQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"name"},
	}
}
