package schema

import "strings"

// ListResult 通用列表返回格式
type ListResult struct {
	PaginationResult
	List interface{} `json:"list,omitempty"`
}

// PaginationResult 分页查询结果
type PaginationResult struct {
	Total uint64 `json:"total"`
}

// PaginationParam 通用分页查询条件
type PaginationParam struct {
	Page uint64 `form:"page,default=1"`
	Size uint64 `form:"size,default=10"`
}

// GetCurrent 分页page
func (p PaginationParam) GetCurrent() uint64 {
	if p.Page == 0 {
		return 1
	}
	return p.Page
}

// GetPageSize 分页size
func (p PaginationParam) GetPageSize() uint64 {
	if p.Size == 0 {
		return 10
	}
	return p.Size
}

// OrderParams 排序字段
type OrderParams struct {
	Ordering []string `form:"ordering"`
}

// BuildToOrderFields 构建多字段排序
func (o OrderParams) BuildToOrderFields() (orderFields []*OrderField) {
	for _, str := range o.Ordering {
		orderField := &OrderField{
			Key:       str[1:],
			Direction: 1,
		}
		if strings.HasPrefix(str, "-") {
			orderField.Direction = 2
		}
		orderFields = append(orderFields, orderField)
	}
	return
}

// OrderDirection 排序方向
type OrderDirection int

const (
	// OrderByASC 升序排序
	OrderByASC OrderDirection = 1
	// OrderByDESC 降序排序
	OrderByDESC OrderDirection = 2
)

// NewOrderField 创建排序字段
func NewOrderField(key string, d OrderDirection) *OrderField {
	return &OrderField{
		Key:       key,
		Direction: d,
	}
}

// OrderField 排序字段
type OrderField struct {
	Key       string         // 字段名(字段名约束为小写蛇形)
	Direction OrderDirection // 排序方向
}

// SearchParams 搜索字段
type SearchParams struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchField []string `form:"-"`
}

// SearchColumnField 指定搜索字段
type SearchColumnField struct {
	SearchKey   string
	SearchValue string
}

// GetSearchColumnField 获取搜索字段
func (s SearchColumnField) GetSearchColumnField() (key, value string) {
	return s.SearchKey, s.SearchValue
}

// ColumnField 精确匹配字段
type ColumnField struct {
	Key   string
	Value string
}

// BindQueryParams 绑定查询参数
type BindQueryParams interface {
	BuildToMap() map[string]interface{}
}

// FindOption 查询选项
type FindOption struct {
	Depth  uint     // 查询深度
	Fields []string // 查询字段
}
