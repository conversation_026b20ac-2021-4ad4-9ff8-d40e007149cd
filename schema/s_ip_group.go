package schema

import "go.mongodb.org/mongo-driver/bson"

// IPGroupQueryParam 接口查询参数
type IPGroupQueryParam struct {
	PaginationParam
	IPGroupColumnParam
	IPGroupSearchParam
	SearchColumnField
	OrderParams
}

// IPGroupColumnParam 查询字段过滤
type IPGroupColumnParam struct {
	Name        string `json:"Name"`
	Description string `json:"Description"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a IPGroupColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{}
	if a.Name != "" {
		filter["Name"] = bson.M{"$regex": a.Name}
	}
	if a.Description != "" {
		filter["Description"] = bson.M{"$regex": a.Description}
	}
	return filter
}

// IPGroupSearchParam 字段搜索参数
type IPGroupSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a IPGroupSearchParam) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{""},
	}
}
