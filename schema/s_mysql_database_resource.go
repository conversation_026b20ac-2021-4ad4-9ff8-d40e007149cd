package schema

// MysqlDatabaseResourceQueryParams 接口查询参数
type MysqlDatabaseResourceQueryParams struct {
	PaginationParam
	MysqlDatabaseResourceColumnParam
	SearchParams
	OrderParams
}

// MysqlDatabaseResourceColumnParam xxx
type MysqlDatabaseResourceColumnParam struct {
	ClusterID string   `json:"cluster_id"`
	Status    string   `json:"Status"`
	IspID     string   `json:"isp_id"`
	IspType   string   `json:"isp_type"`
	RegionID  string   `json:"region_id"`
	TagKey    string   `json:"tag_key"`
	TagValues []string `json:"tag_values"`
}

// BuildToMap xxx
func (a MysqlDatabaseResourceColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"DBStatus":   a.Status,
		"cluster_id": a.ClusterID,
		"isp_id":     a.IspID,
		"isp_type":   a.IspType,
		"region_id":  a.RegionID,
	}
}

// MysqlDatabaseResourceParams xxx
var MysqlDatabaseResourceParams = []string{"ClusterID"}

// GetSearch xxx
func (a MysqlDatabaseResourceQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: MysqlDatabaseResourceParams,
	}
}
