package schema

// IPQueryParams 接口查询参数
type IPQueryParams struct {
	PaginationParam
	IPColumnParam
	IPSearchParam
	SearchColumnField
	OrderParams
}

// IPColumnParam 查询字段过滤
type IPColumnParam struct {
	RegionID string `json:"RegionID"`
	IspID    string `json:"isp_id"`
	Address  string `json:"address"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a IPColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{
		"isp_id":   a.IspID,
		"RegionID": a.RegionID,
		"address":  a.Address,
	}
	return filter
}

// IPSearchParam 字段搜索参数
type IPSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a IPQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"IPName"},
	}
}
