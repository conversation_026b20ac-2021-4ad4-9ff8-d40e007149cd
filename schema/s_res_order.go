package schema

import (
	"fmt"

	"platgit.mihoyo.com/jql-ops/op-cloudman/pkg/constant"
)

// ResourceAction 资源操作类型
type ResourceAction string

const (
	// ResourceCreate 创建资源
	ResourceCreate ResourceAction = "create"
	// ResourceModify 变更资源
	ResourceModify ResourceAction = "modify"
	// ResourceModifyTags 变更标签
	ResourceModifyTags ResourceAction = "modifyTags"
	// ResourceRecycle 回收资源
	ResourceRecycle ResourceAction = "recycle"
	// ResourceDelete 删除资源
	ResourceDelete ResourceAction = "delete"
	// ResourceRecover 移出回收站
	ResourceRecover ResourceAction = "recover"
	// ResourceCleanup 清理资源
	ResourceCleanup ResourceAction = "cleanup"
	// ResourceBackup 备份资源
	ResourceBackup ResourceAction = "backup"
	// ResourceReboot 重启资源
	ResourceReboot ResourceAction = "reboot"
	// ResourceStop 停止资源
	ResourceStop ResourceAction = "stop"
	// ResourceStart 启动实例
	ResourceStart ResourceAction = "start"
)

// ResourceOrderQueryParams 接口查询参数
type ResourceOrderQueryParams struct {
	PaginationParam
	ResourceOrderColumnParam
	SearchParams
	SearchColumnField
	OrderParams
}

// ResourceOrderColumnParam xxx
type ResourceOrderColumnParam struct {
	TaskLogID  string     `json:"task_log_id"`
	ISPID      string     `json:"isp_id"`
	Status     string     `json:"status"`
	Type       string     `json:"type"`
	RegionID   string     `json:"region_id"`
	Action     string     `json:"action"`
	CreateTime TimeFilter `json:"create_time"`
}

// BuildToMap xxx
func (a ResourceOrderColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"isp_id":      a.ISPID,
		"task_log_id": a.TaskLogID,
		"type":        a.Type,
		"region_id":   a.RegionID,
		"action":      a.Action,
	}
	if a.Status != "" {
		m["status"] = ForceNumberFilter(constant.TaskStatusMap[a.Status])
	}
	if a.CreateTime.Value != 0 {
		m["created_time"] = map[string]int32{fmt.Sprintf("$%s", a.CreateTime.Ref): a.CreateTime.Value}
	}

	return m
}

// ForceNumberFilter 强制搜索
type ForceNumberFilter int32

// // ResourceGroupSearchParam xxx
// type ResourceGroupSearchParam struct {
// 	Keywords    string
// 	SearchParams []string
// }

// ResourceOrderParams xxx
var ResourceOrderParams = []string{"create_user"}

// GetSearch xxx
func (a ResourceOrderQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: ResourceOrderParams,
	}
}
