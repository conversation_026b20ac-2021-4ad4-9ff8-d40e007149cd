package schema

// ResourceGroupQueryParams 接口查询参数
type ResourceGroupQueryParams struct {
	PaginationParam
	ResourceGroupColumnParam
	SearchParams
	OrderParams
}

// ResourceGroupColumnParam xxx
type ResourceGroupColumnParam struct {
	Name string
}

// BuildToMap xxx
func (a ResourceGroupColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"name": a.Name,
	}
}

// // ResourceGroupSearchParam xxx
// type ResourceGroupSearchParam struct {
// 	Keywords    string
// 	SearchParams []string
// }

// ResourceGroupParams xxx
var ResourceGroupParams = []string{"name"}

// GetSearch xxx
func (a ResourceGroupQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: ResourceGroupParams,
	}
}
