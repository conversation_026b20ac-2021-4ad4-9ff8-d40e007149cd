package schema

import (
	"strings"

	"go.mongodb.org/mongo-driver/bson"
)

// ResChangelistQueryParams 接口查询参数
type ResChangelistQueryParams struct {
	PaginationParam
	ResChangelistColumnParam
	SearchColumnField
	OrderParams
}

// ResChangelistColumnParam xxx
type ResChangelistColumnParam struct {
	AccountID    string
	ResourceType string
	InstanceID   string
	InstanceName string
	Field        string
	Start        int32
	End          int32
}

// BuildToMap ...
// 防止循环调用，此处枚举不从task包里取常量
func (a ResChangelistColumnParam) BuildToMap() map[string]interface{} {
	res := map[string]interface{}{}
	if a.AccountID != "" {
		res["account_id"] = a.AccountID
	}
	if a.ResourceType != "" {
		rtSlice := strings.Split(a.ResourceType, ",")
		res["resource_type"] = bson.M{"$in": rtSlice}
	}
	if a.InstanceID != "" {
		res["instance_id"] = bson.M{"$regex": a.InstanceID}
	}
	if a.InstanceName != "" {
		res["instance_name"] = bson.M{"$regex": a.InstanceName}
	}
	if a.Field != "" {
		fieldArray := strings.Split(a.Field, ",")
		res["field"] = bson.M{"$in": fieldArray}
	}
	if a.Start*a.End != 0 {
		res["created_time"] = bson.D{{"$gte", a.Start}, {"$lte", a.End}}
	}
	return res
}

// ResChangelistSearchParam xxx
type ResChangelistSearchParam struct {
	Keywords    string
	SearchParam []string
}

// GetSearch xxx
func (a ResChangelistQueryParams) GetSearch() SearchParams {
	return SearchParams{}
}
