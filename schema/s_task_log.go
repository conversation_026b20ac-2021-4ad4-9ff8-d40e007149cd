package schema

// TaskLogResultQueryParams 接口查询参数
type TaskLogResultQueryParams struct {
	PaginationParam
	OrderParams

	TaskLogResultColumnParam
}

// TaskLogResultColumnParam 查询字段过滤
type TaskLogResultColumnParam struct {
	TaskLogID string `json:"task_log_id"`
}

// BuildToMap xxx
func (a TaskLogResultColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"task_log_id": a.TaskLogID,
	}
	return m
}

// TaskLogQueryParams 接口查询参数
type TaskLogQueryParams struct {
	PaginationParam
	OrderParams

	TaskLogColumnParam
	TaskLogSearchParam
}

// TaskLogColumnParam 查询字段过滤
type TaskLogColumnParam struct {
	Name string `json:"name" form:"name"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a TaskLogColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"name": a.Name,
	}
	return m
}

// TaskLogSearchParam 字段搜索参数
type TaskLogSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// TaskLogParams xxx
var TaskLogParams = []string{"name"}

// GetSearch 构建模糊查询
func (a TaskLogQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: TaskLogParams,
	}
}
