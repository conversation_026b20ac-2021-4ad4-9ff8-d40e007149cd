package schema

// SyncTaskQueryParams 接口查询参数
type SyncTaskQueryParams struct {
	PaginationParam
	OrderParams

	SyncTaskColumnParam
	SyncTaskSearchParam
}

// SyncTaskColumnParam 查询字段过滤
type SyncTaskColumnParam struct {
	Status string `json:"status" form:"status"` // 过滤区别零值
	Name   string `json:"name" form:"name"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a SyncTaskColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"status": a.Status,
		"name":   a.Name,
	}
	return m
}

// SyncTaskSearchParam 字段搜索参数
type SyncTaskSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// SyncTaskParams xxx
var SyncTaskParams = []string{"name"}

// GetSearch 构建模糊查询
func (a SyncTaskQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: SyncTaskParams,
	}
}
