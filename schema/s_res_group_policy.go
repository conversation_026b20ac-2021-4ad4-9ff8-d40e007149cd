package schema

// ResGroupPolicyQueryParams 接口查询参数
type ResGroupPolicyQueryParams struct {
	PaginationParam
	ResGroupPolicyColumnParam
	SearchParams
	OrderParams
}

// ResGroupPolicyColumnParam xxx
type ResGroupPolicyColumnParam struct {
	Name string
}

// BuildToMap xxx
func (a ResGroupPolicyColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"name": a.Name,
	}
}

// // ResGroupPolicySearchParam xxx
// type ResGroupPolicySearchParam struct {
// 	Keywords    string
// 	SearchParams []string
// }

// ResGroupPolicyParams xxx
var ResGroupPolicyParams = []string{"name"}

// GetSearch xxx
func (a ResGroupPolicyQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: ResGroupPolicyParams,
	}
}
