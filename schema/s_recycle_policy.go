package schema

// RecycleQueryParams 接口查询参数
type RecycleQueryParams struct {
	PaginationParam
	RecycleColumnParam
	SearchParams
	OrderParams
}

// RecycleColumnParam xxx
type RecycleColumnParam struct {
	InstanceName string
	InstanceID   string
	InstanceType string
	IspID        string
	RegionID     string
	CreateUser   string
	Status       string
}

// BuildToMap xxx
func (a RecycleColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{
		"instance_name": a.InstanceName,
		"instance_id":   a.InstanceID,
		"instance_type": a.InstanceType,
		"create_user":   a.CreateUser,
		"isp_id":        a.IspID,
		"region_id":     a.RegionID,
	}
	if a.Status == "Released" {
		filter["status"] = 2
	} else if a.Status == "Waiting" {
		filter["status"] = map[string]int32{"$ne": 2}
	}

	return filter
}

// // RecycleSearchParam xxx
// type RecycleSearchParam struct {
// 	Keywords    string
// 	SearchParams []string
// }

// RecycleParams xxx
var RecycleParams = []string{"name"}

// GetSearch xxx
func (a RecycleQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: RecycleParams,
	}
}
