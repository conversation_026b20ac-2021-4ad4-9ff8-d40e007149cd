package schema

import (
	"strings"

	"go.mongodb.org/mongo-driver/bson"
)

// ResCreatingQueryParams 接口查询参数
type ResCreatingQueryParams struct {
	PaginationParam
	ResCreatingColumnParam
	SearchColumnField
	OrderParams
}

// ResCreatingColumnParam xxx
type ResCreatingColumnParam struct {
	AccountID    string
	ResourceType string
	InstanceID   string
	InstanceName string
	Field        string
	Start        int32
	End          int32
}

// BuildToMap ...
// 防止循环调用，此处枚举不从task包里取常量
func (a ResCreatingColumnParam) BuildToMap() map[string]interface{} {
	res := map[string]interface{}{}
	if a.AccountID != "" {
		res["account_id"] = a.AccountID
	}
	if a.ResourceType != "" {
		res["resource_type"] = a.ResourceType
	}
	if a.InstanceID != "" {
		res["instance_id"] = bson.M{"$regex": a.InstanceID}
	}
	if a.InstanceName != "" {
		res["instance_name"] = bson.M{"$regex": a.InstanceName}
	}
	if a.Field != "" {
		fieldArray := strings.Split(a.Field, ",")
		res["field"] = bson.M{"$in": fieldArray}
	}
	if a.Start*a.End != 0 {
		res["created_time"] = bson.D{{"$gte", a.Start}, {"$lte", a.End}}
	}
	return res
}

// ResCreatingSearchParam xxx
type ResCreatingSearchParam struct {
	Keywords    string
	SearchParam []string
}

// GetSearch xxx
func (a ResCreatingQueryParams) GetSearch() SearchParams {
	return SearchParams{}
}
