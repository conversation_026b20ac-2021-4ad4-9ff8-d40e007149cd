package schema

// SecurityGroupQueryParams 接口查询参数
type SecurityGroupQueryParams struct {
	PaginationParam
	SecurityGroupColumnParam
	SecurityGroupSearchParam
	SearchColumnField
	OrderParams
}

// SecurityGroupColumnParam 查询字段过滤
type SecurityGroupColumnParam struct {
	RegionID           string `json:"RegionID"`
	IspID              string `json:"isp_id"`
	CustomTag          string `json:"custom_tag"`
	Level              string `json:"level"`
	DisplayNeedCleanup bool   `json:"display_need_cleanup"`
	Type               string `json:"type"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a SecurityGroupColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{
		"isp_id":            a.IspID,
		"RegionID":          a.RegionID,
		"custom_tag":        a.CustomTag,
		"level_tag.level":   a.Level,
		"SecurityGroupType": a.Type,
	}
	if a.CustomTag == "默认节点" {
		delete(filter, "custom_tag")
		filter["$or"] = []map[string]interface{}{
			{"custom_tag": ""},
			{"custom_tag": map[string]interface{}{"$exists": false}},
		}
	}
	if a.DisplayNeedCleanup {
		filter["NeedCleanup"] = true
	}
	return filter
}

// SecurityGroupSearchParam 字段搜索参数
type SecurityGroupSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a SecurityGroupQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"SecurityGroupName"},
	}
}
