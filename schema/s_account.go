package schema

// AccountQueryParams 接口查询参数
type AccountQueryParams struct {
	PaginationParam
	AccountColumnParam
	AccountSearchParam
	OrderParams
}

// AccountColumnParam xxx
type AccountColumnParam struct {
	Status string
	Name   string
	Type   string
}

// BuildToMap xxx
func (a AccountColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"status": a.Status,
		"name":   a.Name,
		"a_type": a.Type,
	}
}

// AccountSearchParam xxx
type AccountSearchParam struct {
	Keywords    string
	SearchParam []string
}

// AccountParams xxx
var AccountParams = []string{"name"}

// GetSearch xxx
func (a AccountQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: AccountParams,
	}
}
