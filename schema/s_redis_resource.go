package schema

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
)

// RedisResourceQueryParams 接口查询参数
type RedisResourceQueryParams struct {
	PaginationParam
	RedisResourceColumnParam
	SearchParams
	SearchColumnField
	OrderParams
}

// RedisResourceColumnParam xxx
type RedisResourceColumnParam struct {
	InstanceID                string                `json:"InstanceID"`
	Status                    string                `json:"Status"`
	IspID                     string                `json:"isp_id"`
	IspType                   string                `json:"isp_type"`
	TagKey                    string                `json:"tag_key"`
	TagValues                 []string              `json:"tag_values"`
	RegionID                  string                `json:"RegionID"`
	DisplayRecyclable         bool                  `json:"display_recyclable"`
	DisplayNeedCleanup        bool                  `json:"display_need_cleanup"`
	InstanceStatus            string                `json:"InstanceStatus"`
	UpdateTime                UpdateTimeColumnParam `json:"update_time"`
	SecurityGroupIds          []string              `json:"security_group_ids"`
	AndSearchSecurityGroupIds bool                  `json:"and_search_security_group_ids"`
}

// BuildToMap xxx
func (a RedisResourceColumnParam) BuildToMap() map[string]interface{} {
	m := map[string]interface{}{
		"DBStatus":       a.Status,
		"InstanceID":     a.InstanceID,
		"isp_id":         a.IspID,
		"isp_type":       a.IspType,
		"RegionID":       a.RegionID,
		"Recyclable":     bson.D{{Key: "$ne", Value: true}},
		"InstanceStatus": a.InstanceStatus,
	}
	if a.InstanceStatus != "" {
		m["NeedCleanup"] = map[string]interface{}{"$ne": true}
	}
	if a.InstanceStatus == "Running" {
		m["InstanceStatus"] = bson.D{{"$in", bson.A{"Normal", "available"}}}
	}
	if a.UpdateTime.Val != 0 {
		m["updated_time"] = map[string]int32{
			fmt.Sprintf("$%s", a.UpdateTime.Ref): a.UpdateTime.Val,
			"$ne":                                0,
		}
	}
	if a.DisplayRecyclable {
		delete(m, "Recyclable")
	}
	if a.DisplayNeedCleanup {
		m["NeedCleanup"] = true
	}
	if len(a.SecurityGroupIds) > 0 {
		if a.AndSearchSecurityGroupIds {
			m["SecurityGroupIds.SecurityGroupId"] = map[string]interface{}{"$all": a.SecurityGroupIds}
		} else {
			m["SecurityGroupIds.SecurityGroupId"] = map[string]interface{}{"$in": a.SecurityGroupIds}
		}
	}
	return m
}

// RedisResourceParams xxx
var RedisResourceParams = []string{"InstanceID"}

// GetSearch xxx
func (a RedisResourceQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: RedisResourceParams,
	}
}
