package schema

// ResTemplateQueryParams 接口查询参数
type ResTemplateQueryParams struct {
	PaginationParam
	ResTemplateColumnParam
	SearchParams
	OrderParams
}

// ResTemplateColumnParam xxx
type ResTemplateColumnParam struct {
	Name string
	Type string
	Isp  string
}

// BuildToMap xxx
func (a ResTemplateColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"name": a.Name,
		"type": a.Type,
		"isp":  a.Isp,
	}
}

// // ResTemplateSearchParam xxx
// type ResTemplateSearchParam struct {
// 	Keywords    string
// 	SearchParams []string
// }

// ResTemplateParams xxx
var ResTemplateParams = []string{"name"}

// GetSearch xxx
func (a ResTemplateQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: ResTemplateParams,
	}
}

// ResTemplatePattern 通用验证规则,通常一个表单由map[string]ResTemplatePattern构成
type ResTemplatePattern struct {
	Label     string                      `json:"label"`                    // 字段名称
	Type      string                      `json:"type" validate:"required"` // 值类型
	Meta      string                      `json:"meta"`                     // 用户选择原始数据
	MetaFunc  string                      `json:"meta_func"`                // 用户选择原始数据获取方法,保留字段
	Validator ResTemplatePatternValidator `json:"validator"`                // 校验字段
	Required  string                      `json:"required"`                 // 是否必填
	AllowEdit bool                        `json:"allow_edit"`               // 允许编辑, 保留字段
}

// ResTemplatePatternValidator 校验用户填入的值是否为模板中限定的值
type ResTemplatePatternValidator struct {
	OneOf  []interface{} `json:"one_of"` // 允许用户输入的值
	Regexp string        `json:"regexp"` // 正则表达式校验规则, 仅当Type为string时有效
	Min    float32       `json:"min"`    // 最小值
	Max    float32       `json:"max"`    // 最大值
}

// HostTplCreateFormData 主机创建form
type HostTplCreateFormData struct {
	Amount   *int32  `json:"Amount" validate:"required"` // 购买数量
	HostName *string `json:"HostName"`                   // 主机名
}
