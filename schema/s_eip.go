package schema

// EipQueryParams 接口查询参数
type EipQueryParams struct {
	PaginationParam
	EipColumnParam
	EipSearchParam
	SearchColumnField
	OrderParams
}

// EipColumnParam 查询字段过滤
type EipColumnParam struct {
	RegionID     string `json:"RegionID"`
	IspID        string `json:"isp_id"`
	IPAddress    string `json:"ip_address"`
	AllocationID string `json:"AllocationId"`
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a EipColumnParam) BuildToMap() map[string]interface{} {
	filter := map[string]interface{}{
		"isp_id":       a.IspID,
		"RegionID":     a.RegionID,
		"IpAddress":    a.IPAddress,
		"AllocationId": a.AllocationID,
	}
	return filter
}

// EipSearchParam 字段搜索参数
type EipSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a EipQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{"EipName"},
	}
}
