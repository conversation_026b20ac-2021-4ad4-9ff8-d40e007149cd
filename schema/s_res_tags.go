package schema

// TagsQueryParams 接口查询参数
type TagsQueryParams struct {
	PaginationParam
	TagsColumnParam
	SearchParams
	OrderParams
}

// TagsColumnParam xxx
type TagsColumnParam struct {
	Name string
	Type string
}

// BuildToMap xxx
func (a TagsColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"name": a.Name,
		"type": a.Type,
	}
}

// // TagsSearchParam xxx
// type TagsSearchParam struct {
// 	Keywords    string
// 	SearchParams []string
// }

// TagsParams xxx
var TagsParams = []string{"name"}

// GetSearch xxx
func (a TagsQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: TagsParams,
	}
}
