package schema

import "time"

// ModelMeta xxx
type ModelMeta struct {
	ID        string `json:"id,omitempty"`
	CreatedAt int64  `json:"created_time"`
	UpdatedAt int64  `json:"updated_time"`
	DeletedAt int64  `gorm:"index" json:"-"` // 修复原model无法关闭序列化
}

// AddUserMeta xxx
type AddUserMeta struct {
	CreateUser string `gorm:"create_user" json:"create_user"`
	UpdateUser string `gorm:"update_user" json:"update_user"`
}

// UserModelMeta xxx
type UserModelMeta struct {
	ModelMeta
	AddUserMeta
}

// UserModelResponse xxx
type UserModelResponse struct {
	CreateAt   *time.Time `json:"created_time"`
	CreateUser string     `json:"create_user"`
	UpdateAt   *time.Time `json:"updated_time"`
	UpdateUser string     `json:"update_user"`
}

// UID xxx
type UID struct {
	ID string `uri:"id" binding:"required"`
}

// TimeFilter 通用时间类过滤方法
type TimeFilter struct {
	Ref   string `json:"ref"`
	Value int32  `json:"value"`
}
