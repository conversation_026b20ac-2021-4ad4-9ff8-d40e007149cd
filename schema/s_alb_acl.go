package schema

import "go.mongodb.org/mongo-driver/bson"

// ALBAclQueryParam 接口查询参数
type ALBAclQueryParam struct {
	PaginationParam
	ALBAclColumnParam
	ALBAclSearchParam
	SearchColumnField
	OrderParams
}

// ALBAclColumnParam 查询字段过滤
type ALBAclColumnParam struct {
	AclId string
	Name  string
}

// BuildToMap 为提高性能需手动绑定映射关系
func (a ALBAclColumnParam) BuildToMap() map[string]interface{} {
	filter := make(map[string]interface{})
	if a.AclId != "" {
		filter["AclId"] = a.AclId
	}
	if a.Name != "" {
		filter["AclName"] = bson.M{
			"$regex": a.Name,
		}
	}
	return filter
}

// ALBAclSearchParam 字段搜索参数
type ALBAclSearchParam struct {
	Keywords    string   `form:"keywords" json:"keywords"`
	SearchParam []string `form:"-"`
}

// GetSearch 构建模糊查询
func (a ALBAclSearchParam) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: []string{""},
	}
}
