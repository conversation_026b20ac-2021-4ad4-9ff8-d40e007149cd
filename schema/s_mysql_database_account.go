package schema

// MysqlDatabaseAccountResourceQueryParams 接口查询参数
type MysqlDatabaseAccountResourceQueryParams struct {
	PaginationParam
	MysqlDatabaseAccountResourceColumnParam
	SearchParams
	OrderParams
}

// MysqlDatabaseAccountResourceColumnParam xxx
type MysqlDatabaseAccountResourceColumnParam struct {
	ClusterID string   `json:"cluster_id"`
	Status    string   `json:"Status"`
	IspID     string   `json:"isp_id"`
	IspType   string   `json:"isp_type"`
	TagKey    string   `json:"tag_key"`
	TagValues []string `json:"tag_values"`
}

// BuildToMap xxx
func (a MysqlDatabaseAccountResourceColumnParam) BuildToMap() map[string]interface{} {
	return map[string]interface{}{
		"DBStatus":   a.Status,
		"cluster_id": a.ClusterID,
		"isp_id":     a.IspID,
		"isp_type":   a.IspType,
	}
}

// MysqlDatabaseAccountResourceParams xxx
var MysqlDatabaseAccountResourceParams = []string{"ClusterID"}

// GetSearch xxx
func (a MysqlDatabaseAccountResourceQueryParams) GetSearch() SearchParams {
	return SearchParams{
		Keywords:    a.Keywords,
		SearchField: MysqlDatabaseAccountResourceParams,
	}
}
