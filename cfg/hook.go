package cfg

import (
	"fmt"

	"gopkg.in/yaml.v2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
)

// HookOption webhook选项
type HookOption struct {
	GlobalToken           string   `yaml:"global_token"`            // 全局hook-token
	OrderStart            []string `yaml:"order_start"`             // 订单开始
	OrderEnd              []string `yaml:"order_end"`               // 订单结束
	ChangeHostResource    []string `yaml:"change_host_resource"`    // 主机类资源变化时触发(create/update)
	ChangeMysqlResource   []string `yaml:"change_mysql_resource"`   // mysql资源变化时触发(create/update)
	ChangeMysqlEPResource []string `yaml:"change_mysqlEP_resource"` // mysql-ep资源变化时触发(create/update)
	ChangeMysqlDBResource []string `yaml:"change_mysqlDB_resource"` // mysql-db资源变化时触发(create/update)
	ChangeRedisResource   []string `yaml:"change_redis_resource"`   // redis资源变化时触发(create/update)
	// CreateResource []string `yaml:"create_resource"` // 新增资源
	// UpdateResource []string `yaml:"update_resource"` // 资源更新
	DeleteResource []string       `yaml:"delete_resource"` // 删除资源时触发，统一返回ID
	SyncTask       []string       `yaml:"sync_task"`       // 同步任务执行钩子
	HotWheel       HotWheelOption `yaml:"hotWheel"`        // 标准运维定制需求
}

// HotWheelOption 标准运维相关配置
type HotWheelOption struct {
	ReleaseHostResource   []HotWheelCommonOption `yaml:"releaseResource"`       // 加入回收站
	UnReleaseHostResource []HotWheelCommonOption `yaml:"unReleaseHostResource"` // 从回收站移出
	UpdateResource        []HotWheelCommonOption `yaml:"addResource"`
	CreateResource        []HotWheelCommonOption `yaml:"createResource"`
}

// HotWheelCommonOption 标准运维通用接口
type HotWheelCommonOption struct {
	URI        string `yaml:"uri"`
	Token      string `yaml:"token"`
	PipelineID int    `yaml:"pipelineID"`
}

// GetHookOption 获取hook选项
func GetHookOption() (*HookOption, error) {
	raw, ok := zest.GetRaw("webhook")
	if !ok {
		return nil, fmt.Errorf("not found hook_options")
	}

	var option HookOption
	err := yaml.Unmarshal([]byte(raw), &option)
	if err != nil {
		return nil, err
	}

	return &option, nil
}
