package cfg

import (
	"fmt"

	"gopkg.in/yaml.v2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
)

// AwsOption AWS相关配置元数据
type AwsOption struct {
	DefaultRegionID    string   `yaml:"defaultRegionID"`
	ImageOwners        []string `yaml:"imageOwners"`
	IamInstanceProfile string   `yaml:"iamInstanceProfile"`
	SkipDBSync         bool     `yaml:"skip_db_sync"`

	//ImageVolumes []AwsImageVolume `yaml:"imageVolumes"`
	//InstanceTypes   []awsInstanceType `yaml:"instanceTypes"`
	//VolumeTypes   []awsVolumeType   `yaml:"volumeTypes"`
	//ChargeTypes []awsChargeType `yaml:"chargeTypes"`
	//DBconf      AwsDbConfig     `yaml:"dbconf"`
}

// GetAwsOptionConf 获取AWS相关的元数据
func GetAwsOptionConf() (*AwsOption, error) {
	if awsOption == nil {
		awsOption = new(AwsOption)
	}
	raw, ok := zest.GetRaw("awsOption")
	if !ok {
		return nil, fmt.Errorf("not found aws conf")
	}

	err := yaml.Unmarshal([]byte(raw), awsOption)

	return awsOption, err
}

var awsOption *AwsOption
