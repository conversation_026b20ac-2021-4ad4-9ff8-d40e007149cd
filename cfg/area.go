package cfg

import (
	"fmt"

	"gopkg.in/yaml.v2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
)

// Area 区服地域-->云厂商地域
type Area struct {
	Area   string `yaml:"area"`
	Isp    string `yaml:"isp"`
	Region string `yaml:"region"`
}

// GetAreas 获取AWS相关的元数据
func GetAreas() ([]Area, error) {
	raw, ok := zest.GetRaw("areas")
	if !ok {
		return nil, fmt.Errorf("not found aws conf")
	}

	var areas []Area
	err := yaml.Unmarshal([]byte(raw), &areas)

	return areas, err
}
