package cfg

import (
	"fmt"

	"golang.org/x/net/context"

	"gopkg.in/yaml.v2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
	iamsdk "platgit.mihoyo.com/op-plat/iam-sdk-go-http"
)

// CKCfg ...
type CKCfg struct {
	ClientKeyID     string `yaml:"client_key_id"`
	ClientKeySecret string `yaml:"client_key_secret"`
	DebugAddress    string `yaml:"debug_address"`
}

// IAMConfig 权限中心初始化配置
type IAMConfig struct {
	EnvCK map[string]CKCfg `yaml:"env_ck"`
}

// IAMCliMap xxx
var IAMCliMap = map[string]*iamsdk.Client{}

// IAMServiceID xxx
var IAMServiceID = "op-cloudman-takumi"

// GetIAMCfg 获取权限中心配置
func GetIAMCfg() (*IAMConfig, error) {
	raw, ok := zest.GetRaw("iam")
	if !ok {
		return nil, fmt.Errorf("not found iam option")
	}

	var conf IAMConfig
	err := yaml.Unmarshal([]byte(raw), &conf)
	return &conf, err
}

// GetIAMCli ...
func GetIAMCli(env string) (*iamsdk.Client, error) {
	cli, ok := IAMCliMap[env]
	if !ok {
		return nil, fmt.Errorf("missing iam client env: %s", env)
	}
	return cli, nil
}

// GetIAMCliToken ...
func GetIAMCliToken(env string) (string, error) {
	cli, err := GetIAMCli(env)
	if err != nil {
		return "", err
	}
	return cli.GetSelfToken(context.Background())
}

func GetIamDefaultCli() (*iamsdk.Client, error) {
	for _, v := range IAMCliMap {
		return v, nil
	}
	return nil, fmt.Errorf("cannot find client")
}
