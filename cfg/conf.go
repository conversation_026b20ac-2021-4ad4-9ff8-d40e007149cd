/*
	zest配置中心文档:
*/

package cfg

import (
	"encoding/json"
	"fmt"
	"os"

	"gopkg.in/yaml.v2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
)

// GetCollectionPrefix 获取mongo collection prefix
func GetCollectionPrefix() string {
	return "cloudman_takumi"
}

// GetDBName 获取db name
func GetDBName() string {
	return "op-cloudman-takumi"
}

// GetQCronAddress 获取定时器地址
func GetQCronAddress() string {
	return ""
}

// OssConfig oss配置信息
type OssConfig struct {
	AccessKeyID        string `json:"access-key-id"`
	AccessKeySecret    string `json:"access-key-secret"`
	InstallGatewayPath string `json:"install_gateway_path"`
	OssBucket          string `json:"oss_bucket"`
	OssPublicEndpoint  string `json:"oss_public_endpoint"`
	Endpoint           string `json:"oss_private_endpoint"` // 默认endpoint为内网地址
}

// GetOPSOssConfig 获取oss配置
func GetOPSOssConfig() (*OssConfig, bool) {
	raw, ok := zest.GetRaw("ops_oss")
	if !ok {
		return nil, false
	}
	var conf OssConfig
	err := json.Unmarshal([]byte(raw), &conf)
	return &conf, err == nil
}

// GetConsulAddress 获取consul地址
func GetConsulAddress() string {
	raw, ok := zest.GetRaw("ops_consul")
	if ok {
		return raw
	}
	return ""
}

// OpsAgentConfig ops-agent-config
type OpsAgentConfig struct {
	LatestVersion     string   `yaml:"latest_version"`
	LatestVersionDesc string   `yaml:"latest_version_desc"`
	Script            string   `yaml:"script"`
	WinScript         string   `yaml:"win_script"`
	JumpserverReqType string   `yaml:"jumpserver_req_type"` // 'straight' or 'jobman'
	LostWait          int64    `yaml:"lost_wait"`           // second, 'lost' if no ops-agent report
	AnyEnvAgent       []string `yaml:"any_env_agent"`
}

// GetOpsAgentConf 获取ops-agent配置文件
func GetOpsAgentConf() *OpsAgentConfig {
	var config OpsAgentConfig
	zest.SafeGet("ops_agent", &config)
	if config.LostWait == 0 {
		config.LostWait = 86400
	}
	return &config
}

// OpsGatewayConfig ops-agent-config
type OpsGatewayConfig struct {
	Name         string `yaml:"name"`
	AgentListURL string `yaml:"agentListURL"`
}

// GetOpsGatewayConf 获取ops-agent配置文件
func GetOpsGatewayConf() (*OpsGatewayConfig, bool) {
	raw, ok := zest.GetRaw("ops_gateway")
	if !ok {
		return nil, false
	}

	var conf OpsGatewayConfig
	conf.Name = "ops-gateway"

	err := yaml.Unmarshal([]byte(raw), &conf)

	return &conf, err == nil
}

// MonitorConfig 监控相关配置
type MonitorConfig struct {
	Token                    string `yaml:"token"`                    // grafana-Token
	Address                  string `yaml:"address"`                  // grafana查询地址
	GrafanaViewAddress       string `yaml:"grafana_view_address"`     // grafana图地址
	GrafanaViewAddressForWin string `yaml:"grafana_view_address_win"` // grafana图地址
	CPUSqlTpl                string `yaml:"CPUSqlTpl"`                // 获取CPU用量
	CPUSqlTplForWin          string `yaml:"CPUSqlTpl_win"`            // 获取CPU用量for win
	MemSQLTpl                string `yaml:"MenSqlTpl"`                // 获取内存用量
	MemSQLTplForWin          string `yaml:"MenSqlTpl_win"`            // 获取内存用量for win
	IOSqlTpl                 string `yaml:"IOSqlTpl"`                 // 获取IO
	IOSqlTplForWin           string `yaml:"IOSqlTpl_win"`             // 获取IOfor win
	NetSQLTpl                string `yaml:"NetSqlTpl"`                // 获取网络速度
	NetSQLTplForWin          string `yaml:"NetSqlTpl_win"`            // 获取网络速度for win
	DBIndex                  string `yaml:"db_index"`                 // 获取influxdb环境Index
	DBIndexProd              string `yaml:"db_index_prod"`            // 获取influxdb生产环境Index
	DiskTpl                  string `yaml:"DiskSqlTpl"`               // 获取磁盘用量
	DiskTplForWin            string `yaml:"DiskSqlTpl_win"`           // 获取磁盘用量for win
	UPTimeSQLTpl             string `yaml:"UPTimeSQLTpl"`             // 获取开机时间
	UPTimeSQLTplWin          string `yaml:"UPTimeSqlTpl_win"`         // 获取开机时间for win
}

// GetMonitorConf 获取监控相关配置
func GetMonitorConf() (*MonitorConfig, error) {
	raw, ok := zest.GetRaw("monitor")
	if !ok {
		return nil, fmt.Errorf("not found monitor conf")
	}

	var conf MonitorConfig

	err := yaml.Unmarshal([]byte(raw), &conf)

	return &conf, err
}

// SyncOption 同步选项
type SyncOption struct {
	DbFetchAgent  string `yaml:"db_fetch_agent"`
	DbFetchScript string `yaml:"db_fetch_script"`
}

// GetSyncOptionConf 获取同步选项
func GetSyncOptionConf() SyncOption {
	var config SyncOption
	zest.SafeGet("syncOption", &config)
	return config
}

// GaiaMonitorConfig Gaia监控配置
type GaiaMonitorConfig struct {
	//AuthToken      string `yaml:"auth_token"`      // gaia监控数据查询token
	IAMEnv         string `yaml:"iam_env"`
	Address        string `yaml:"address"`         // gaia监控数据查询地址
	Redirect       string `yaml:"redirect"`        // 用于跳转至gaia主机监控地址
	MonitorAddress string `yaml:"monitor_address"` // gaia监控状态查询地址
	IAMToken       string `yaml:"iam_token"`       // gaia token
}

// GetGaiaMonitorConf 获取Gaia监控平台配置项
func GetGaiaMonitorConf() (*GaiaMonitorConfig, error) {
	raw, ok := zest.GetRaw("Gaia")
	if !ok {
		return nil, fmt.Errorf("not found gaia conf")
	}

	var conf GaiaMonitorConfig

	err := yaml.Unmarshal([]byte(raw), &conf)

	return &conf, err
}

func GetGaiaIamToken() string {
	cfg, err := GetGaiaMonitorConf()
	if err != nil {
		return ""
	}
	return cfg.IAMToken
}

const (
	SyncEnvProd = "prod"
	SyncEnvTest = "test"
)

// SystemConfig 系统配置
type SystemConfig struct {
	EnableWorkOrder      bool        `yaml:"enable_work_order"`
	WorkOrderHost        string      `yaml:"work_order_host"`
	WorkOrderWebHost     string      `yaml:"work_order_web_host"`
	WorkOrderIAMEnv      string      `yaml:"work_order_iam_env"`
	FlowID               string      `yaml:"flow_id"`
	OrderURL             string      `yaml:"order_url"`
	Env                  string      `yaml:"env"`
	Cluster              string      `yaml:"cluster"`
	EnableSnapshot       bool        `yaml:"enable_snapshot"`
	ShowTagValue         bool        `yaml:"show_tag_value"`
	AutoRefreshOpsStatus bool        `yaml:"auto_refresh_ops_status"`
	EnableLocalCron      bool        `yaml:"enable_local_cron"`
	Redis                RedisConfig `yaml:"redis"`
	SyncEnv              string      `yaml:"sync_env"` // 同步云资源的环境，目前有两种1.测试:test 2.正式: prod。如果为空则同步所有的资源
}

type RedisConfig struct {
	Addr     string `yaml:"addr"`
	Password string `yaml:"password"`
}

// GetSystemConfig 获取系统配置
func GetSystemConfig() SystemConfig {
	var config SystemConfig
	zest.SafeGet("system-config", &config)

	return config
}

// JobmanConfig jobman执行配置
type JobmanConfig struct {
	Enable             bool   `yaml:"enable"`
	ProdIAMEnv         string `yaml:"prod_iam_env"`
	ProdURLBase        string `yaml:"prod_url_base"`        // url拼接前缀，schema和port
	ProdRootAccountID  string `yaml:"prod_root_account_id"` // root账户id
	TestIAMEnv         string `yaml:"test_iam_env"`
	TestURLBase        string `yaml:"test_url_base"`         // url拼接前缀，schema和port
	TestRootAccountID  string `yaml:"test_root_account_id"`  // root账户id
	OfficeProxyAgentID string `yaml:"office_proxy_agent_id"` // 办公网proxy的agentID
	CurEnvPrefix       string `yaml:"cur_env_prefix"`        // 当前环境云管请求地址前缀
	TokenForJobmanCron string `yaml:"token_for_jobman_cron"` // 能调用云管同步任务的token
}

// GetJobmanConfig 获取jobman执行配置
func GetJobmanConfig() JobmanConfig {
	var config JobmanConfig
	zest.SafeGet("jobman-config", &config)
	return config
}

// CmdbConfig jobman执行配置
type CmdbConfig struct {
	Enable          bool   `yaml:"enable"`
	BkCmdbURL       string `yaml:"bk_cmdb_url"`
	BkCmdbToken     string `yaml:"bk_cmdb_token"`
	BkCmdbTestURL   string `yaml:"bk_cmdb_test_url"`
	BkCmdbTestToken string `yaml:"bk_cmdb_test_token"`
	LogTo           string `yaml:"log_to"`

	OtherRegion RegionConfig `yaml:"other_region"`
	ProdRegion  RegionConfig `yaml:"prod_region"`
}

type RegionConfig struct {
	Enable          bool   `yaml:"enable"`
	BizID           int32  `yaml:"biz_id"`            // 业务ID
	ServerListURL   string `yaml:"server_list_url"`   // 数据库查询链接
	ServerListToken string `yaml:"server_list_token"` // 数据库查询token
	TemplateID      int32  `yaml:"template_id"`       // 集群模版ID
	Envs            string `yaml:"envs"`              // 表示哪些环境需要归属到当前业务ID下，以逗号分隔 e.g. prod,live
}

// GetCmdbConfig 获取jobman执行配置
func GetCmdbConfig() CmdbConfig {
	var config CmdbConfig
	zest.SafeGet("cmdb-config", &config)
	return config
}

// ServiceTreeConfig 服务树配置
type ServiceTreeConfig struct {
	Disable           bool   `yaml:"disable"`
	ServiceTreePrefix string `yaml:"service_tree_prefix"`
	IAMEnv            string `yaml:"iam_env"`
}

// GetServiceTreeConfig 获取服务树配置
func GetServiceTreeConfig() ServiceTreeConfig {
	var config ServiceTreeConfig
	zest.SafeGet("service-tree-config", &config)
	return config
}

func IsDev() bool {
	return os.Getenv("ENV") == "dev"
}

type NotificationConfig struct {
	AppKey    string `yaml:"app_key"`
	Url       string `yaml:"url"`
	Token     string `yaml:"token"`
	ButtonUrl string `yaml:"button_url"`

	ChatIds []string `yaml:"chat_ids"`
	UserIds []string `yaml:"user_ids"`
}

func GetNotificationConfig() NotificationConfig {
	var config NotificationConfig
	zest.SafeGet("notification", &config)
	return config
}
