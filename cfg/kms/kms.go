package kms

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"gopkg.in/yaml.v2"
	"gopkg.mihoyo.com/infosys/kms"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
)

// Config ...
type Config struct {
	AccessKey              string `yaml:"access_key"`
	AccessCredential       string `yaml:"access_credential"`
	EndPoint               string `yaml:"end_point"`
	DnspodAccessKey        string `yaml:"dnspod_access_key"`
	DnspodAccessCredential string `yaml:"dnspod_access_credential"`
}

// Clients ...
var Clients = map[string]*Client{}

type cacheBody struct {
	Cache         map[string]string
	LastCacheTime time.Time
}

// Client ...
type Client struct {
	Cli   *kms.Client
	Cache sync.Map
}

// GetKMSCfg 获取权限中心配置
func GetKMSCfg() (*Config, error) {
	raw, ok := zest.GetRaw("kms")
	if !ok {
		return nil, fmt.Errorf("not found kms option")
	}

	var conf Config
	err := yaml.Unmarshal([]byte(raw), &conf)
	return &conf, err
}

// InitCloudmanKMSCli ...
func InitCloudmanKMSCli() error {
	kmsCfg, err := GetKMSCfg()
	if err != nil {
		return err
	}
	return InitKMSCli("cloudman", kmsCfg.AccessKey, kmsCfg.AccessCredential, kmsCfg.EndPoint)
}

// InitDnspodKMSCli ...
func InitDnspodKMSCli() error {
	kmsCfg, err := GetKMSCfg()
	if err != nil {
		return err
	}
	return InitKMSCli("dnspod", kmsCfg.DnspodAccessKey, kmsCfg.DnspodAccessCredential, kmsCfg.EndPoint)
}

// InitKMSCli ...
func InitKMSCli(name, accessKey, accessCredential, endPoint string) error {
	cli, err := kms.Init(accessKey, accessCredential, endPoint, false)
	if err != nil {
		return err
	}
	Clients[name] = &Client{
		Cli:   cli,
		Cache: sync.Map{},
	}
	return nil
}

// GetRealAK ...
func (c *Client) GetRealAK(kmsName, ak, sk string) (realAK string, realSK string, err error) {
	cacheKeysRaw, hasCache := c.Cache.Load(kmsName)
	if hasCache {
		body, canConvert := cacheKeysRaw.(cacheBody)
		if canConvert && body.LastCacheTime.Add(time.Minute*10).After(time.Now()) {
			var ok, ok2 bool
			realAK, ok = body.Cache[ak]
			realSK, ok2 = body.Cache[sk]
			if ok && ok2 {
				return realAK, realSK, nil
			}
		}
	}
	return c.getAKFromKMS(kmsName, ak, sk)
}

func (c *Client) getAKFromKMS(kmsName, ak, sk string) (realAK, realSK string, err error) {
	keys, err := c.requestKMS(kmsName)
	if err != nil {
		return "", "", err
	}
	var ok bool
	c.Cache.Store(kmsName, cacheBody{
		Cache:         keys,
		LastCacheTime: time.Now(),
	})
	realAK, ok = keys[ak]
	if !ok {
		return "", "", fmt.Errorf("wrong ak: %s", ak)
	}
	realSK, ok = keys[sk]
	if !ok {
		return "", "", fmt.Errorf("wrong sk: %s", sk)
	}
	return realAK, realSK, nil
}

func (c *Client) requestKMS(kmsName string) (map[string]string, error) {
	if c.Cli == nil {
		return nil, errors.New("kms cli is nil")
	}
	if err := c.Cli.LoadSecrets([]string{kmsName}); err != nil {
		return nil, err
	}
	keys := c.Cli.GetSecret(kmsName)
	if keys == nil {
		return nil, fmt.Errorf("empty key, maybe wrong kmsname: %s", kmsName)
	}
	return keys, nil
}
