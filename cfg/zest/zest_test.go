package zest

import (
	"context"
	"fmt"
	"os"
	"testing"
)

func TestZest(t *testing.T) {
	_ = os.Setenv("ZEST_ADDRESS", "http://127.0.0.1:6000")
	_ = os.Setenv("APP_GROUP", "ops")
	_ = os.Setenv("APP_NAME", "op-cloudman-takumi")
	_ = os.Setenv("ZEST_TOKEN", "dcebe8fa-f8dc-11ef-b92d-00155da0d06c")
	cli := NewClient()
	adminConf, err := cli.GetRaw(context.Background(), "admin")
	if err != nil {
		t.<PERSON>al(err)
	}
	fmt.Println(adminConf)
}
