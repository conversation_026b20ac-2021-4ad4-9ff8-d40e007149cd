package zest

import (
	"context"
	"fmt"

	"gopkg.in/yaml.v2"
	"platgit.mihoyo.com/op-plat/zest-helper/client"
	"platgit.mihoyo.com/op-plat/zest-helper/pkg/cfg"
)

var cli *client.Client

func init() {
	cli = NewClient()
}

func NewClient() *client.Client {
	config, err := cfg.NewConfig()
	if err != nil {
		panic(fmt.Errorf("err when new zest-helper config, errMsg: %s", err.Error()))
	}
	cli := client.NewClient(config)
	return cli
}

func GetRaw(key string) (string, bool) {
	res, err := cli.GetRaw(context.Background(), key)
	if err != nil {
		return "", false
	}
	return res, true
}

func GetRawWithoutCache(key string) (string, bool) {
	res, err := cli.GetRawWithoutCache(context.Background(), key)
	if err != nil {
		return "", false
	}
	return res, true
}

func SafeGet(key string, target interface{}) bool {
	val, ok := GetRaw(key)
	if !ok {
		return false
	}
	return yaml.Unmarshal([]byte(val), target) == nil
}
