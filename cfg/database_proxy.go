package cfg

import (
	"fmt"

	"gopkg.in/yaml.v2"
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
)

/*
jp:
  address: local://
hk:
  address: local://
default: jp
*/

// type AWSPullOption struct {
// 	Region  map[string]AWSPullDBConf `yaml:"region,flow"`
// 	Default string                   `yaml:"default"`
// }

// AWSPullDBConf pull-service配置项
type AWSPullDBConf struct {
	Address  string `yaml:"address"`
	DBUser   string `yaml:"db_user"`
	DBPasswd string `yaml:"db_password"`
}

// GetAWSPullOption 获取AWSPullDBConf
func GetAWSPullOption() (*AWSPullDBConf, error) {
	raw, ok := zest.GetRaw("aws-pull-db-conf")
	if !ok {
		return nil, fmt.Errorf("aws-pull-db-conf")
	}

	option := &AWSPullDBConf{}
	err := yaml.Unmarshal([]byte(raw), option)

	return option, err
}
