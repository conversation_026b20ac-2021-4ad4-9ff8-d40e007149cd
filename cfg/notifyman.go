package cfg

import (
	"platgit.mihoyo.com/jql-ops/op-cloudman/cfg/zest"
)

// NotifyManCfg 通知平台初始化配置
type NotifyManCfg struct {
	NotifyAddress string                `yaml:"notify_address"`
	Items         map[string]NotifyItem `yaml:"items"`
}

// NotifyItem -
type NotifyItem struct {
	PlateFromID string   `yaml:"platFormId"` // 平台id
	ToolID      string   `yaml:"toolId"`     // 工具id
	TemplateID  string   `yaml:"templateId"` // 模版id
	MikuUser    []string `yaml:"mikuUser"`
}

// GetNotifyCfg 获取通知平台配置
func GetNotifyCfg() NotifyManCfg {
	var config NotifyManCfg
	zest.SafeGet("notifyOption", &config)
	return config
}
